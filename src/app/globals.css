@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Custom variables for HireFlow */
    --sidebar: 0 0% 98%;
    --sidebar-border: 220 13% 91%;
    --sidebar-foreground: 224 71.4% 4.1%;
    --sidebar-muted: 220 14.3% 95.9%;
    --sidebar-muted-foreground: 220 8.9% 46.1%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --success: 142 76% 36%;
    --success-foreground: 138 76% 97%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Custom dark mode variables */
    --sidebar: 224 71.4% 4.1%;
    --sidebar-border: 215 27.9% 16.9%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-muted: 215 27.9% 16.9%;
    --sidebar-muted-foreground: 217.9 10.6% 64.9%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --success: 142 76% 36%;
    --success-foreground: 138 76% 97%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for HireFlow */
@layer components {
  .ai-button {
    @apply bg-gradient-to-r from-primary to-emerald-600 hover:from-primary/90 hover:to-emerald-600/90;
    @apply shadow-lg hover:shadow-xl transition-all duration-200;
    @apply border-0 text-primary-foreground font-medium;
  }

  .ai-glow {
    @apply relative overflow-hidden;
  }

  .ai-glow::before {
    @apply absolute inset-0 bg-gradient-to-r from-primary/20 to-emerald-500/20;
    @apply rounded-lg opacity-0 transition-opacity duration-300;
    content: "";
  }

  .ai-glow:hover::before {
    @apply opacity-100;
  }

  .animate-pulse-green {
    animation: pulse-green 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse-green {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .sidebar-nav-item {
    @apply flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium;
    @apply transition-all duration-200 hover:bg-sidebar-muted;
    @apply text-sidebar-foreground/70 hover:text-sidebar-foreground;
  }

  .sidebar-nav-item.active {
    @apply bg-primary text-primary-foreground;
    @apply shadow-sm;
  }

  .sidebar-nav-item.active:hover {
    @apply bg-primary/90;
  }
}

/* Scrollbar styling */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--foreground));
  }
}
