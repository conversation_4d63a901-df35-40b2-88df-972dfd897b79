import type { Metadata } from "next";
import "./globals.css";
import { Inter } from "next/font/google";
import { Providers } from "./providers";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "HireFlow - AI Recruiting System",
    template: "%s | HireFlow",
  },
  description:
    "Hệ thống tuyển dụng thông minh với AI - Smart recruiting system with AI assistance",
  keywords: ["recruiting", "AI", "hiring", "HR", "recruitment", "tuyển dụng"],
  authors: [{ name: "HireFlow Team" }],
  creator: "Hire<PERSON><PERSON>",
  publisher: "HireFlow",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: "website",
    locale: "vi_VN",
    url: "https://hireflow.ai",
    title: "HireFlow - AI Recruiting System",
    description: "<PERSON><PERSON> thống tuyển dụng thông minh với AI",
    siteName: "HireFlow",
  },
  twitter: {
    card: "summary_large_image",
    title: "HireFlow - AI Recruiting System",
    description: "Hệ thống tuyển dụng thông minh với AI",
    creator: "@hireflow",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
