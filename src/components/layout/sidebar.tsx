"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useTranslation } from "@/lib/i18n";
import {
  LayoutDashboard,
  Users,
  Building2,
  GitBranch,
  Calendar,
  MessageSquare,
  BarChart3,
  Settings,
  Zap,
  UserCheck,
  User,
  UsersIcon,
} from "lucide-react";

export const Sidebar = () => {
  const pathname = usePathname();
  const { t } = useTranslation();

  const navigation = [
    { name: t.nav.dashboard, href: "/", icon: LayoutDashboard },
    { name: t.nav.candidates, href: "/candidates", icon: Users },
    { name: t.nav.jobs, href: "/jobs", icon: Building2 },
    { name: t.nav.pipeline, href: "/pipeline", icon: GitBranch },
    { name: t.nav.calendar, href: "/calendar", icon: Calendar },
    { name: t.nav.interviewers, href: "/interviewers", icon: User<PERSON><PERSON><PERSON> },
    { name: t.nav.messages, href: "/messages", icon: MessageSquare },
    { name: t.nav.analytics, href: "/analytics", icon: BarChart3 },
  ];

  const secondaryNavigation = [
    { name: t.nav.profile, href: "/profile", icon: User },
    { name: t.nav.team, href: "/team", icon: UsersIcon },
    { name: t.nav.settings, href: "/settings", icon: Settings },
  ];

  return (
    <div className="w-64 bg-sidebar border-r border-sidebar-border flex flex-col relative">
      {/* AI Glow Effect */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-primary/10 to-transparent" />

      {/* Logo */}
      <div className="flex items-center gap-3 p-6 border-b border-sidebar-border relative z-10">
        <div className="w-10 h-10 bg-primary rounded-xl flex items-center justify-center shadow-lg animate-pulse-green">
          <Zap className="w-5 h-5 text-primary-foreground" />
        </div>
        <div>
          <h1 className="text-xl font-bold text-sidebar-foreground">
            HireFlow
          </h1>
          <p className="text-xs text-sidebar-foreground/60 font-medium">
            AI Recruiting
          </p>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2 relative z-10">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn("sidebar-nav-item group", {
                active: isActive,
              })}
            >
              <div className="relative">
                <item.icon className="w-5 h-5 transition-transform group-hover:scale-110" />
                {isActive && (
                  <div className="absolute -inset-1 bg-primary/20 rounded-lg blur-sm -z-10" />
                )}
              </div>
              <span className="font-medium">{item.name}</span>
              {isActive && (
                <div className="ml-auto w-2 h-2 bg-primary-foreground rounded-full opacity-80" />
              )}
            </Link>
          );
        })}
      </nav>

      {/* AI Assistant Banner */}
      <div className="mx-4 mb-4 p-4 bg-gradient-to-br from-primary/20 to-emerald-500/20 rounded-xl border border-primary/30 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 animate-shimmer" />
        <div className="relative">
          <div className="flex items-center gap-2 mb-2">
            <Zap className="w-4 h-4 text-primary" />
            <span className="text-sm font-semibold text-sidebar-foreground">
              AI Assistant
            </span>
          </div>
          <p className="text-xs text-sidebar-foreground/80 mb-3">
            Let AI help you find the perfect candidates faster
          </p>
          <button className="w-full py-2 px-3 bg-primary text-primary-foreground rounded-lg text-xs font-medium hover:bg-primary/90 transition-colors">
            Try AI Search
          </button>
        </div>
      </div>

      {/* Secondary Navigation */}
      <nav className="px-4 py-4 border-t border-sidebar-border space-y-1 relative z-10">
        {secondaryNavigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn("sidebar-nav-item", {
                active: isActive,
              })}
            >
              <item.icon className="w-5 h-5" />
              <span className="font-medium">{item.name}</span>
            </Link>
          );
        })}
      </nav>
    </div>
  );
};
