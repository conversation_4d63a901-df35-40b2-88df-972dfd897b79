/**
 * Validation Helpers
 * Utility functions for form validation and error handling
 */

import { z } from 'zod';
import { FieldErrors, FieldValues, Path } from 'react-hook-form';

// Type for validation result
export interface ValidationResult<T = any> {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
  fieldErrors?: FieldErrors<T>;
}

// Validate data against a Zod schema
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): ValidationResult<T> {
  try {
    const result = schema.parse(data);
    return {
      success: true,
      data: result,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      const fieldErrors: FieldErrors<T> = {};

      error.errors.forEach((err) => {
        const path = err.path.join('.');
        errors[path] = err.message;
        
        // Create field errors for react-hook-form
        if (err.path.length > 0) {
          const fieldPath = err.path[0] as Path<T>;
          fieldErrors[fieldPath] = {
            type: err.code,
            message: err.message,
          };
        }
      });

      return {
        success: false,
        errors,
        fieldErrors,
      };
    }

    return {
      success: false,
      errors: { general: 'Validation failed' },
    };
  }
}

// Safe validation that doesn't throw
export function safeValidate<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): z.SafeParseReturnType<unknown, T> {
  return schema.safeParse(data);
}

// Get first error message from validation result
export function getFirstError(errors?: Record<string, string>): string | undefined {
  if (!errors) return undefined;
  const firstKey = Object.keys(errors)[0];
  return firstKey ? errors[firstKey] : undefined;
}

// Format validation errors for display
export function formatValidationErrors(
  errors: z.ZodError['errors']
): Record<string, string> {
  const formatted: Record<string, string> = {};
  
  errors.forEach((error) => {
    const path = error.path.join('.');
    formatted[path] = error.message;
  });
  
  return formatted;
}

// Custom validation functions
export const validators = {
  // Password strength validator
  passwordStrength: (password: string): { score: number; feedback: string[] } => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) score += 1;
    else feedback.push('Use at least 8 characters');

    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('Include lowercase letters');

    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('Include uppercase letters');

    if (/\d/.test(password)) score += 1;
    else feedback.push('Include numbers');

    if (/[^a-zA-Z\d]/.test(password)) score += 1;
    else feedback.push('Include special characters');

    return { score, feedback };
  },

  // Email domain validator
  emailDomain: (email: string, allowedDomains: string[]): boolean => {
    const domain = email.split('@')[1]?.toLowerCase();
    return domain ? allowedDomains.includes(domain) : false;
  },

  // File size validator
  fileSize: (file: File, maxSizeMB: number): boolean => {
    return file.size <= maxSizeMB * 1024 * 1024;
  },

  // File type validator
  fileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type);
  },

  // URL validator with specific protocols
  urlWithProtocol: (url: string, protocols: string[] = ['http', 'https']): boolean => {
    try {
      const urlObj = new URL(url);
      return protocols.includes(urlObj.protocol.slice(0, -1));
    } catch {
      return false;
    }
  },

  // Phone number validator (international format)
  internationalPhone: (phone: string): boolean => {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  },

  // Credit card number validator (Luhn algorithm)
  creditCard: (number: string): boolean => {
    const digits = number.replace(/\D/g, '');
    let sum = 0;
    let isEven = false;

    for (let i = digits.length - 1; i >= 0; i--) {
      let digit = parseInt(digits[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  },

  // Date range validator
  dateRange: (startDate: Date, endDate: Date): boolean => {
    return startDate <= endDate;
  },

  // Age validator
  age: (birthDate: Date, minAge: number, maxAge?: number): boolean => {
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())
      ? age - 1
      : age;

    return actualAge >= minAge && (maxAge ? actualAge <= maxAge : true);
  },
};

// Validation error messages
export const errorMessages = {
  required: 'This field is required',
  email: 'Please enter a valid email address',
  phone: 'Please enter a valid phone number',
  url: 'Please enter a valid URL',
  minLength: (min: number) => `Must be at least ${min} characters`,
  maxLength: (max: number) => `Must be no more than ${max} characters`,
  min: (min: number) => `Must be at least ${min}`,
  max: (max: number) => `Must be no more than ${max}`,
  pattern: 'Invalid format',
  fileSize: (maxMB: number) => `File size must be less than ${maxMB}MB`,
  fileType: (types: string[]) => `File type must be one of: ${types.join(', ')}`,
  passwordMismatch: "Passwords don't match",
  passwordWeak: 'Password is too weak',
  dateInvalid: 'Please enter a valid date',
  datePast: 'Date cannot be in the past',
  dateFuture: 'Date cannot be in the future',
  numberInvalid: 'Please enter a valid number',
  arrayEmpty: 'At least one item is required',
  custom: (message: string) => message,
};

// Schema composition helpers
export const schemaHelpers = {
  // Make all fields optional
  partial: <T>(schema: z.ZodSchema<T>) => schema.partial(),

  // Pick specific fields
  pick: <T, K extends keyof T>(schema: z.ZodObject<any>, keys: K[]) =>
    schema.pick(Object.fromEntries(keys.map(key => [key, true])) as any),

  // Omit specific fields
  omit: <T, K extends keyof T>(schema: z.ZodObject<any>, keys: K[]) =>
    schema.omit(Object.fromEntries(keys.map(key => [key, true])) as any),

  // Extend schema with additional fields
  extend: <T, U>(schema: z.ZodObject<any>, extension: z.ZodRawShape) =>
    schema.extend(extension),

  // Merge two schemas
  merge: <T, U>(schema1: z.ZodObject<any>, schema2: z.ZodObject<any>) =>
    schema1.merge(schema2),

  // Create conditional schema
  conditional: <T>(
    condition: (data: any) => boolean,
    trueSchema: z.ZodSchema<T>,
    falseSchema: z.ZodSchema<T>
  ) => z.any().superRefine((data, ctx) => {
    const schema = condition(data) ? trueSchema : falseSchema;
    const result = schema.safeParse(data);
    
    if (!result.success) {
      result.error.errors.forEach(error => {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: error.message,
          path: error.path,
        });
      });
    }
  }),
};

// Form validation hook helper
export function createFormValidator<T>(schema: z.ZodSchema<T>) {
  return {
    validate: (data: unknown) => validateData(schema, data),
    safeValidate: (data: unknown) => safeValidate(schema, data),
    schema,
  };
}

// Async validation helper
export async function validateAsync<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  asyncValidators?: Array<(data: T) => Promise<string | null>>
): Promise<ValidationResult<T>> {
  // First, run synchronous validation
  const syncResult = validateData(schema, data);
  
  if (!syncResult.success || !syncResult.data) {
    return syncResult;
  }

  // Then run async validators if provided
  if (asyncValidators && asyncValidators.length > 0) {
    const asyncErrors: Record<string, string> = {};
    
    await Promise.all(
      asyncValidators.map(async (validator, index) => {
        try {
          const error = await validator(syncResult.data!);
          if (error) {
            asyncErrors[`async_${index}`] = error;
          }
        } catch (err) {
          asyncErrors[`async_${index}`] = 'Validation failed';
        }
      })
    );

    if (Object.keys(asyncErrors).length > 0) {
      return {
        success: false,
        errors: { ...syncResult.errors, ...asyncErrors },
      };
    }
  }

  return syncResult;
}
