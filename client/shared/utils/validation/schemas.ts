/**
 * Validation Schemas
 * Reusable Zod schemas for common validation patterns
 */

import { z } from 'zod';

// Common field validations
export const commonSchemas = {
  // Basic fields
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number'),
  url: z.string().url('Please enter a valid URL'),
  
  // Text fields with length constraints
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  
  // Numbers
  positiveNumber: z.number().positive('Must be a positive number'),
  nonNegativeNumber: z.number().min(0, 'Must be 0 or greater'),
  percentage: z.number().min(0, 'Must be at least 0%').max(100, 'Must be at most 100%'),
  
  // Dates
  pastDate: z.date().max(new Date(), 'Date cannot be in the future'),
  futureDate: z.date().min(new Date(), 'Date cannot be in the past'),
  
  // Files
  fileSize: (maxSizeMB: number) => 
    z.instanceof(File).refine(
      (file) => file.size <= maxSizeMB * 1024 * 1024,
      `File size must be less than ${maxSizeMB}MB`
    ),
  
  fileType: (allowedTypes: string[]) =>
    z.instanceof(File).refine(
      (file) => allowedTypes.includes(file.type),
      `File type must be one of: ${allowedTypes.join(', ')}`
    ),
  
  // Arrays
  nonEmptyArray: <T>(schema: z.ZodSchema<T>) =>
    z.array(schema).min(1, 'At least one item is required'),
  
  // Optional with fallback
  optionalString: z.string().optional().or(z.literal('')),
  optionalNumber: z.number().optional().or(z.nan()),
};

// User-related schemas
export const userSchemas = {
  profile: z.object({
    name: commonSchemas.name,
    email: commonSchemas.email,
    phone: commonSchemas.phone.optional(),
    avatar: z.string().url().optional(),
    bio: commonSchemas.description,
  }),
  
  changePassword: z.object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z.string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    confirmPassword: z.string(),
  }).refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  }),
  
  login: z.object({
    email: commonSchemas.email,
    password: z.string().min(1, 'Password is required'),
    rememberMe: z.boolean().optional(),
  }),
  
  register: z.object({
    name: commonSchemas.name,
    email: commonSchemas.email,
    password: z.string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    confirmPassword: z.string(),
    acceptTerms: z.boolean().refine((val) => val === true, 'You must accept the terms and conditions'),
  }).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  }),
};

// Candidate-related schemas
export const candidateSchemas = {
  basic: z.object({
    name: commonSchemas.name,
    email: commonSchemas.email,
    phone: commonSchemas.phone.optional(),
    location: z.string().optional(),
    avatar: z.string().url().optional(),
  }),
  
  professional: z.object({
    position: z.string().min(1, 'Position is required'),
    experience: z.number().min(0, 'Experience must be 0 or greater').max(50, 'Experience must be less than 50 years'),
    currentSalary: commonSchemas.nonNegativeNumber.optional(),
    expectedSalary: commonSchemas.nonNegativeNumber.optional(),
    skills: z.array(z.string()).optional(),
    education: z.string().optional(),
    summary: commonSchemas.description,
  }),
  
  documents: z.object({
    resume: z.instanceof(File).optional(),
    coverLetter: z.string().optional(),
    portfolio: z.string().url().optional(),
    linkedin: z.string().url().optional(),
    github: z.string().url().optional(),
  }),
  
  full: z.object({
    // Basic info
    name: commonSchemas.name,
    email: commonSchemas.email,
    phone: commonSchemas.phone.optional(),
    location: z.string().optional(),
    
    // Professional info
    position: z.string().min(1, 'Position is required'),
    experience: z.number().min(0).max(50),
    currentSalary: commonSchemas.nonNegativeNumber.optional(),
    expectedSalary: commonSchemas.nonNegativeNumber.optional(),
    skills: z.array(z.string()).optional(),
    
    // Additional info
    notes: commonSchemas.description,
    tags: z.array(z.string()).optional(),
    status: z.enum(['new', 'screening', 'interview', 'offer', 'hired', 'rejected']),
  }),
};

// Job-related schemas
export const jobSchemas = {
  basic: z.object({
    title: commonSchemas.title,
    department: z.string().min(1, 'Department is required'),
    location: z.string().min(1, 'Location is required'),
    type: z.enum(['full-time', 'part-time', 'contract', 'internship']),
    remote: z.boolean().optional(),
  }),
  
  details: z.object({
    description: z.string().min(1, 'Job description is required'),
    requirements: z.string().min(1, 'Requirements are required'),
    responsibilities: z.string().optional(),
    benefits: z.string().optional(),
    salaryMin: commonSchemas.nonNegativeNumber.optional(),
    salaryMax: commonSchemas.nonNegativeNumber.optional(),
    currency: z.string().default('USD'),
  }),
  
  full: z.object({
    title: commonSchemas.title,
    department: z.string().min(1, 'Department is required'),
    location: z.string().min(1, 'Location is required'),
    type: z.enum(['full-time', 'part-time', 'contract', 'internship']),
    remote: z.boolean().optional(),
    description: z.string().min(1, 'Job description is required'),
    requirements: z.string().min(1, 'Requirements are required'),
    responsibilities: z.string().optional(),
    benefits: z.string().optional(),
    salaryMin: commonSchemas.nonNegativeNumber.optional(),
    salaryMax: commonSchemas.nonNegativeNumber.optional(),
    currency: z.string().default('USD'),
    status: z.enum(['draft', 'published', 'closed', 'archived']),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
    tags: z.array(z.string()).optional(),
  }).refine((data) => {
    if (data.salaryMin && data.salaryMax) {
      return data.salaryMin <= data.salaryMax;
    }
    return true;
  }, {
    message: 'Minimum salary cannot be greater than maximum salary',
    path: ['salaryMax'],
  }),
};

// Interview-related schemas
export const interviewSchemas = {
  schedule: z.object({
    candidateId: z.string().min(1, 'Candidate is required'),
    jobId: z.string().min(1, 'Job is required'),
    interviewerId: z.string().min(1, 'Interviewer is required'),
    type: z.enum(['phone', 'video', 'in-person', 'technical']),
    scheduledAt: z.date().min(new Date(), 'Interview date must be in the future'),
    duration: z.number().min(15, 'Duration must be at least 15 minutes').max(480, 'Duration cannot exceed 8 hours'),
    location: z.string().optional(),
    notes: commonSchemas.description,
  }),
  
  feedback: z.object({
    interviewId: z.string().min(1, 'Interview ID is required'),
    rating: z.number().min(1, 'Rating is required').max(5, 'Rating cannot exceed 5'),
    technicalSkills: z.number().min(1).max(5),
    communication: z.number().min(1).max(5),
    culturalFit: z.number().min(1).max(5),
    experience: z.number().min(1).max(5),
    comments: z.string().min(1, 'Comments are required'),
    recommendation: z.enum(['hire', 'no-hire', 'maybe']),
    nextSteps: z.string().optional(),
  }),
};

// Settings schemas
export const settingsSchemas = {
  company: z.object({
    name: z.string().min(1, 'Company name is required'),
    website: commonSchemas.url.optional(),
    industry: z.string().optional(),
    size: z.enum(['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']).optional(),
    description: commonSchemas.description,
    logo: z.string().url().optional(),
  }),
  
  notifications: z.object({
    emailNotifications: z.boolean(),
    pushNotifications: z.boolean(),
    newApplications: z.boolean(),
    interviewReminders: z.boolean(),
    weeklyReports: z.boolean(),
  }),
  
  integrations: z.object({
    slackWebhook: commonSchemas.url.optional(),
    emailProvider: z.enum(['smtp', 'sendgrid', 'mailgun']).optional(),
    calendarSync: z.boolean().optional(),
  }),
};

// Search and filter schemas
export const searchSchemas = {
  candidateSearch: z.object({
    query: z.string().optional(),
    skills: z.array(z.string()).optional(),
    experience: z.object({
      min: z.number().min(0).optional(),
      max: z.number().max(50).optional(),
    }).optional(),
    location: z.string().optional(),
    status: z.array(z.enum(['new', 'screening', 'interview', 'offer', 'hired', 'rejected'])).optional(),
    salaryRange: z.object({
      min: commonSchemas.nonNegativeNumber.optional(),
      max: commonSchemas.nonNegativeNumber.optional(),
    }).optional(),
  }),
  
  jobSearch: z.object({
    query: z.string().optional(),
    department: z.string().optional(),
    type: z.array(z.enum(['full-time', 'part-time', 'contract', 'internship'])).optional(),
    location: z.string().optional(),
    remote: z.boolean().optional(),
    status: z.array(z.enum(['draft', 'published', 'closed', 'archived'])).optional(),
  }),
};

// Export all schemas
export const validationSchemas = {
  common: commonSchemas,
  user: userSchemas,
  candidate: candidateSchemas,
  job: jobSchemas,
  interview: interviewSchemas,
  settings: settingsSchemas,
  search: searchSchemas,
};
