/**
 * Enhanced Notification System
 * Consistent notification patterns with toast integration
 */

import React from 'react';
import { toast, ToastOptions } from 'sonner';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Info, 
  X,
  ExternalLink,
  Copy,
  Download,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface NotificationAction {
  label: string;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'ghost';
  icon?: React.ReactNode;
}

export interface NotificationOptions extends Omit<ToastOptions, 'description'> {
  type?: NotificationType;
  title: string;
  description?: string;
  actions?: NotificationAction[];
  persistent?: boolean;
  showCloseButton?: boolean;
  duration?: number;
}

// Notification icons mapping
const notificationIcons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
};

// Notification colors mapping
const notificationColors = {
  success: 'text-green-600',
  error: 'text-red-600',
  warning: 'text-yellow-600',
  info: 'text-blue-600',
};

// Custom notification component
function NotificationContent({
  type = 'info',
  title,
  description,
  actions,
  showCloseButton = true,
  onClose,
}: NotificationOptions & { onClose?: () => void }) {
  const Icon = notificationIcons[type];
  const iconColor = notificationColors[type];

  return (
    <div className="flex items-start space-x-3 p-4 bg-background border rounded-lg shadow-lg min-w-[300px] max-w-[500px]">
      <Icon className={cn('h-5 w-5 mt-0.5 flex-shrink-0', iconColor)} />
      
      <div className="flex-1 space-y-2">
        <div className="flex items-start justify-between">
          <div>
            <h4 className="font-medium text-foreground">{title}</h4>
            {description && (
              <p className="text-sm text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          
          {showCloseButton && onClose && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-muted"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {actions && actions.length > 0 && (
          <div className="flex items-center space-x-2 pt-2">
            {actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || 'outline'}
                size="sm"
                onClick={() => {
                  action.onClick();
                  if (onClose) onClose();
                }}
                className="h-8"
              >
                {action.icon && <span className="mr-1">{action.icon}</span>}
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Main notification system class
export class NotificationSystem {
  private static defaultDuration = 5000;

  static show(options: NotificationOptions) {
    const {
      type = 'info',
      title,
      description,
      actions,
      persistent = false,
      showCloseButton = true,
      duration = this.defaultDuration,
      ...toastOptions
    } = options;

    const toastId = toast.custom(
      (t) => (
        <NotificationContent
          type={type}
          title={title}
          description={description}
          actions={actions}
          showCloseButton={showCloseButton}
          onClose={() => toast.dismiss(t)}
        />
      ),
      {
        duration: persistent ? Infinity : duration,
        ...toastOptions,
      }
    );

    return toastId;
  }

  static success(title: string, options?: Partial<NotificationOptions>) {
    return this.show({
      type: 'success',
      title,
      ...options,
    });
  }

  static error(title: string, options?: Partial<NotificationOptions>) {
    return this.show({
      type: 'error',
      title,
      persistent: true, // Errors should be persistent by default
      ...options,
    });
  }

  static warning(title: string, options?: Partial<NotificationOptions>) {
    return this.show({
      type: 'warning',
      title,
      duration: 7000, // Warnings should stay longer
      ...options,
    });
  }

  static info(title: string, options?: Partial<NotificationOptions>) {
    return this.show({
      type: 'info',
      title,
      ...options,
    });
  }

  static dismiss(toastId?: string | number) {
    if (toastId) {
      toast.dismiss(toastId);
    } else {
      toast.dismiss();
    }
  }

  static dismissAll() {
    toast.dismiss();
  }
}

// Predefined notification patterns
export const notifications = {
  // Success patterns
  success: {
    saved: (itemName?: string) =>
      NotificationSystem.success(
        `${itemName || 'Item'} saved successfully`,
        {
          description: 'Your changes have been saved.',
        }
      ),

    created: (itemName?: string) =>
      NotificationSystem.success(
        `${itemName || 'Item'} created successfully`,
        {
          description: 'The new item has been added.',
        }
      ),

    updated: (itemName?: string) =>
      NotificationSystem.success(
        `${itemName || 'Item'} updated successfully`,
        {
          description: 'Your changes have been applied.',
        }
      ),

    deleted: (itemName?: string) =>
      NotificationSystem.success(
        `${itemName || 'Item'} deleted successfully`,
        {
          description: 'The item has been removed.',
        }
      ),

    copied: (content?: string) =>
      NotificationSystem.success(
        'Copied to clipboard',
        {
          description: content ? `"${content}" has been copied.` : 'Content copied successfully.',
        }
      ),

    uploaded: (fileName?: string) =>
      NotificationSystem.success(
        'File uploaded successfully',
        {
          description: fileName ? `"${fileName}" has been uploaded.` : 'Your file is ready.',
        }
      ),
  },

  // Error patterns
  error: {
    generic: (message?: string) =>
      NotificationSystem.error(
        'Something went wrong',
        {
          description: message || 'Please try again or contact support if the problem persists.',
          actions: [
            {
              label: 'Retry',
              onClick: () => window.location.reload(),
              variant: 'outline',
            },
          ],
        }
      ),

    network: () =>
      NotificationSystem.error(
        'Network error',
        {
          description: 'Please check your internet connection and try again.',
          actions: [
            {
              label: 'Retry',
              onClick: () => window.location.reload(),
              variant: 'outline',
            },
          ],
        }
      ),

    validation: (message: string) =>
      NotificationSystem.error(
        'Validation error',
        {
          description: message,
        }
      ),

    unauthorized: () =>
      NotificationSystem.error(
        'Access denied',
        {
          description: 'You do not have permission to perform this action.',
          actions: [
            {
              label: 'Sign in',
              onClick: () => (window.location.href = '/login'),
              variant: 'outline',
            },
          ],
        }
      ),

    notFound: (itemName?: string) =>
      NotificationSystem.error(
        `${itemName || 'Item'} not found`,
        {
          description: 'The requested item could not be found.',
        }
      ),
  },

  // Warning patterns
  warning: {
    unsavedChanges: (onSave?: () => void, onDiscard?: () => void) =>
      NotificationSystem.warning(
        'You have unsaved changes',
        {
          description: 'Your changes will be lost if you leave without saving.',
          actions: [
            ...(onSave ? [{
              label: 'Save',
              onClick: onSave,
              variant: 'default' as const,
            }] : []),
            ...(onDiscard ? [{
              label: 'Discard',
              onClick: onDiscard,
              variant: 'outline' as const,
            }] : []),
          ],
        }
      ),

    deleteConfirmation: (itemName: string, onConfirm: () => void) =>
      NotificationSystem.warning(
        `Delete ${itemName}?`,
        {
          description: 'This action cannot be undone.',
          actions: [
            {
              label: 'Delete',
              onClick: onConfirm,
              variant: 'outline',
            },
          ],
        }
      ),

    quota: (percentage: number) =>
      NotificationSystem.warning(
        'Storage quota warning',
        {
          description: `You have used ${percentage}% of your storage quota.`,
          actions: [
            {
              label: 'Manage storage',
              onClick: () => (window.location.href = '/settings/storage'),
              variant: 'outline',
            },
          ],
        }
      ),
  },

  // Info patterns
  info: {
    processing: (itemName?: string) =>
      NotificationSystem.info(
        `Processing ${itemName || 'request'}...`,
        {
          description: 'This may take a few moments.',
          persistent: true,
        }
      ),

    newFeature: (featureName: string, learnMoreUrl?: string) =>
      NotificationSystem.info(
        `New feature: ${featureName}`,
        {
          description: 'Check out what\'s new in this update.',
          actions: [
            ...(learnMoreUrl ? [{
              label: 'Learn more',
              onClick: () => window.open(learnMoreUrl, '_blank'),
              variant: 'outline' as const,
              icon: <ExternalLink className="h-3 w-3" />,
            }] : []),
          ],
        }
      ),

    maintenance: (startTime?: string) =>
      NotificationSystem.info(
        'Scheduled maintenance',
        {
          description: startTime 
            ? `System maintenance is scheduled for ${startTime}.`
            : 'System maintenance is scheduled. Some features may be temporarily unavailable.',
          persistent: true,
        }
      ),
  },
};

// Hook for using notifications
export function useNotifications() {
  return {
    show: NotificationSystem.show,
    success: NotificationSystem.success,
    error: NotificationSystem.error,
    warning: NotificationSystem.warning,
    info: NotificationSystem.info,
    dismiss: NotificationSystem.dismiss,
    dismissAll: NotificationSystem.dismissAll,
    patterns: notifications,
  };
}
