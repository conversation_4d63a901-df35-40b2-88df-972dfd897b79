/**
 * Error Boundary Wrapper Components
 * Pre-configured error boundaries for different use cases
 */

import React, { ReactNode } from "react";
import { ErrorBoundary } from "./ErrorBoundary";

// Global Error Boundary for the entire application
interface GlobalErrorBoundaryProps {
  children: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo, errorId: string) => void;
}

export const GlobalErrorBoundary: React.FC<GlobalErrorBoundaryProps> = ({
  children,
  onError,
}) => {
  return (
    <ErrorBoundary level="global" onError={onError} resetOnPropsChange={false}>
      {children}
    </ErrorBoundary>
  );
};

// Domain Error Boundary for domain-specific sections
interface DomainErrorBoundaryProps {
  children: ReactNode;
  domain: string;
  onError?: (error: Error, errorInfo: React.ErrorInfo, errorId: string) => void;
  resetKeys?: Array<string | number>;
}

export const DomainErrorBoundary: React.FC<DomainErrorBoundaryProps> = ({
  children,
  domain,
  onError,
  resetKeys,
}) => {
  const handleError = (
    error: Error,
    errorInfo: React.ErrorInfo,
    errorId: string,
  ) => {
    // Add domain context to error reporting
    console.error(`Domain Error [${domain}]:`, { error, errorInfo, errorId });

    if (onError) {
      onError(error, errorInfo, errorId);
    }
  };

  return (
    <ErrorBoundary
      level="domain"
      onError={handleError}
      resetOnPropsChange={true}
      resetKeys={resetKeys}
    >
      {children}
    </ErrorBoundary>
  );
};

// Component Error Boundary for individual components
interface ComponentErrorBoundaryProps {
  children: ReactNode;
  componentName?: string;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo, errorId: string) => void;
  resetKeys?: Array<string | number>;
}

export const ComponentErrorBoundary: React.FC<ComponentErrorBoundaryProps> = ({
  children,
  componentName,
  fallback,
  onError,
  resetKeys,
}) => {
  const handleError = (
    error: Error,
    errorInfo: React.ErrorInfo,
    errorId: string,
  ) => {
    // Add component context to error reporting
    console.error(`Component Error [${componentName || "Unknown"}]:`, {
      error,
      errorInfo,
      errorId,
    });

    if (onError) {
      onError(error, errorInfo, errorId);
    }
  };

  return (
    <ErrorBoundary
      level="component"
      fallback={fallback}
      onError={handleError}
      resetOnPropsChange={true}
      resetKeys={resetKeys}
    >
      {children}
    </ErrorBoundary>
  );
};

// Async Error Boundary for handling async operations
interface AsyncErrorBoundaryProps {
  children: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo, errorId: string) => void;
  resetKeys?: Array<string | number>;
}

export const AsyncErrorBoundary: React.FC<AsyncErrorBoundaryProps> = ({
  children,
  onError,
  resetKeys,
}) => {
  return (
    <ErrorBoundary
      level="component"
      onError={onError}
      resetOnPropsChange={true}
      resetKeys={resetKeys}
    >
      {children}
    </ErrorBoundary>
  );
};

// HOC for wrapping components with error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ComponentErrorBoundaryProps, "children">,
) {
  const WrappedComponent = (props: P) => {
    return (
      <ComponentErrorBoundary
        componentName={Component.displayName || Component.name}
        {...errorBoundaryProps}
      >
        <Component {...props} />
      </ComponentErrorBoundary>
    );
  };

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

// Hook for error reporting
export const useErrorHandler = () => {
  const reportError = (error: Error, context?: Record<string, any>) => {
    const errorId = `manual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.error(`Manual Error Report [${errorId}]:`, {
      error,
      context,
      timestamp: new Date().toISOString(),
    });

    // In a real application, send to error reporting service
    // errorReportingService.captureException(error, {
    //   tags: { manual: true },
    //   extra: { context, errorId },
    // });

    return errorId;
  };

  return { reportError };
};
