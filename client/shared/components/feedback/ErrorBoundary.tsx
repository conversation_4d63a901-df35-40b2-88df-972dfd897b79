/**
 * Error Boundary Components
 * Provides error handling and user-friendly error states
 */

import React, { Component, ErrorInfo, ReactNode } from "react";
import { AlertTriangle, RefreshCw, Home, Bug } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  level?: "global" | "domain" | "component";
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

export class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: "",
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError } = this.props;
    const { errorId } = this.state;

    this.setState({
      error,
      errorInfo,
    });

    // Report error to external service or logging
    if (onError) {
      onError(error, errorInfo, errorId);
    } else {
      // Default error reporting
      this.reportError(error, errorInfo, errorId);
    }
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    if (hasError && prevProps.resetOnPropsChange && resetOnPropsChange) {
      if (resetKeys) {
        const hasResetKeyChanged = resetKeys.some(
          (key, index) => prevProps.resetKeys?.[index] !== key,
        );
        if (hasResetKeyChanged) {
          this.resetErrorBoundary();
        }
      }
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  private reportError = (
    error: Error,
    errorInfo: ErrorInfo,
    errorId: string,
  ) => {
    // In a real application, you would send this to your error reporting service
    console.group(`🚨 Error Boundary Caught Error [${errorId}]`);
    console.error("Error:", error);
    console.error("Error Info:", errorInfo);
    console.error("Component Stack:", errorInfo.componentStack);
    console.groupEnd();

    // Example: Send to error reporting service
    // errorReportingService.captureException(error, {
    //   tags: { errorBoundary: true, level: this.props.level },
    //   extra: { errorInfo, errorId },
    // });
  };

  private resetErrorBoundary = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: "",
    });
  };

  private handleRetry = () => {
    this.resetErrorBoundary();
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = "/";
  };

  render() {
    const { hasError, error, errorId } = this.state;
    const { children, fallback, level = "component" } = this.props;

    if (hasError) {
      if (fallback) {
        return fallback;
      }

      return this.renderErrorUI(error, errorId, level);
    }

    return children;
  }

  private renderErrorUI(error: Error | null, errorId: string, level: string) {
    const isGlobalError = level === "global";
    const isDomainError = level === "domain";

    if (isGlobalError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <CardTitle className="text-xl">Something went wrong</CardTitle>
              <CardDescription>
                We're sorry, but something unexpected happened. Please try
                refreshing the page.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-2">
                <Button onClick={this.handleReload} className="w-full">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reload Page
                </Button>
                <Button
                  variant="outline"
                  onClick={this.handleGoHome}
                  className="w-full"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Go Home
                </Button>
              </div>

              {process.env.NODE_ENV === "development" && (
                <Alert>
                  <Bug className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    <strong>Error ID:</strong> {errorId}
                    <br />
                    <strong>Error:</strong> {error?.message}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    if (isDomainError) {
      return (
        <div className="flex items-center justify-center p-8">
          <Card className="w-full max-w-lg">
            <CardHeader className="text-center">
              <div className="mx-auto w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mb-3">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              <CardTitle className="text-lg">Section Unavailable</CardTitle>
              <CardDescription>
                This section encountered an error and couldn't load properly.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center gap-2">
                <Button onClick={this.handleRetry} size="sm">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                <Button variant="outline" onClick={this.handleReload} size="sm">
                  Reload Page
                </Button>
              </div>

              {process.env.NODE_ENV === "development" && (
                <Alert className="mt-4">
                  <Bug className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    <strong>Error ID:</strong> {errorId}
                    <br />
                    <strong>Error:</strong> {error?.message}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    // Component level error
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <div className="flex items-center gap-2 text-red-800 mb-2">
          <AlertTriangle className="w-4 h-4" />
          <span className="font-medium text-sm">Component Error</span>
        </div>
        <p className="text-red-700 text-sm mb-3">
          This component couldn't render properly.
        </p>
        <Button onClick={this.handleRetry} size="sm" variant="outline">
          <RefreshCw className="w-3 h-3 mr-1" />
          Retry
        </Button>

        {process.env.NODE_ENV === "development" && (
          <div className="mt-3 p-2 bg-red-100 rounded text-xs text-red-800">
            <strong>Error ID:</strong> {errorId}
            <br />
            <strong>Error:</strong> {error?.message}
          </div>
        )}
      </div>
    );
  }
}
