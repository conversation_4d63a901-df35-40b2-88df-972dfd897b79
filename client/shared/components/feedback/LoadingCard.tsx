/**
 * Loading Card Components
 * Card-based loading states for different content types
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSkeleton, LoadingSpinner } from './LoadingSpinner';
import { cn } from '@/lib/utils';

// Generic loading card
export interface LoadingCardProps {
  title?: string;
  description?: string;
  className?: string;
  children?: React.ReactNode;
}

export const LoadingCard: React.FC<LoadingCardProps> = ({
  title,
  description,
  className,
  children,
}) => {
  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        {title ? (
          <CardTitle>{title}</CardTitle>
        ) : (
          <LoadingSkeleton variant="text" width="60%" height="24px" />
        )}
        {description ? (
          <CardDescription>{description}</CardDescription>
        ) : (
          <LoadingSkeleton variant="text" width="80%" height="16px" />
        )}
      </CardHeader>
      <CardContent>
        {children || (
          <div className="space-y-3">
            <LoadingSkeleton variant="text" width="100%" />
            <LoadingSkeleton variant="text" width="75%" />
            <LoadingSkeleton variant="text" width="90%" />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Profile card loading state
export const LoadingProfileCard: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center space-x-4">
          <LoadingSkeleton variant="circular" width={64} height={64} />
          <div className="space-y-2 flex-1">
            <LoadingSkeleton variant="text" width="60%" height="20px" />
            <LoadingSkeleton variant="text" width="40%" height="16px" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <LoadingSkeleton variant="text" width="50%" height="14px" />
              <LoadingSkeleton variant="text" width="80%" height="16px" />
            </div>
            <div className="space-y-2">
              <LoadingSkeleton variant="text" width="50%" height="14px" />
              <LoadingSkeleton variant="text" width="70%" height="16px" />
            </div>
          </div>
          <div className="space-y-2">
            <LoadingSkeleton variant="text" width="30%" height="14px" />
            <LoadingSkeleton variant="text" width="100%" height="60px" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// List item loading state
export const LoadingListItem: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('flex items-center space-x-4 p-4 border rounded-lg', className)}>
      <LoadingSkeleton variant="circular" width={40} height={40} />
      <div className="space-y-2 flex-1">
        <LoadingSkeleton variant="text" width="60%" height="16px" />
        <LoadingSkeleton variant="text" width="40%" height="14px" />
      </div>
      <LoadingSkeleton variant="rectangular" width={80} height={32} />
    </div>
  );
};

// Table loading state
export interface LoadingTableProps {
  rows?: number;
  columns?: number;
  className?: string;
}

export const LoadingTable: React.FC<LoadingTableProps> = ({
  rows = 5,
  columns = 4,
  className,
}) => {
  return (
    <div className={cn('w-full', className)}>
      {/* Table header */}
      <div className="flex space-x-4 p-4 border-b">
        {Array.from({ length: columns }).map((_, i) => (
          <div key={i} className="flex-1">
            <LoadingSkeleton variant="text" width="70%" height="16px" />
          </div>
        ))}
      </div>
      
      {/* Table rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4 p-4 border-b">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="flex-1">
              <LoadingSkeleton 
                variant="text" 
                width={colIndex === 0 ? "90%" : `${60 + Math.random() * 30}%`}
                height="16px" 
              />
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

// Chart loading state
export const LoadingChart: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <LoadingSkeleton variant="text" width="40%" height="20px" />
        <LoadingSkeleton variant="text" width="60%" height="14px" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Chart area */}
          <LoadingSkeleton variant="rectangular" width="100%" height="200px" />
          
          {/* Legend */}
          <div className="flex justify-center space-x-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-2">
                <LoadingSkeleton variant="circular" width={12} height={12} />
                <LoadingSkeleton variant="text" width="60px" height="14px" />
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Form loading state
export const LoadingForm: React.FC<{ fields?: number; className?: string }> = ({
  fields = 4,
  className,
}) => {
  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <LoadingSkeleton variant="text" width="50%" height="24px" />
        <LoadingSkeleton variant="text" width="70%" height="16px" />
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Array.from({ length: fields }).map((_, i) => (
            <div key={i} className="space-y-2">
              <LoadingSkeleton variant="text" width="30%" height="16px" />
              <LoadingSkeleton variant="rectangular" width="100%" height="40px" />
            </div>
          ))}
          
          {/* Form buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <LoadingSkeleton variant="rectangular" width={80} height={36} />
            <LoadingSkeleton variant="rectangular" width={100} height={36} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Dashboard widget loading state
export const LoadingWidget: React.FC<{ 
  type?: 'metric' | 'chart' | 'list';
  className?: string;
}> = ({ type = 'metric', className }) => {
  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <LoadingSkeleton variant="text" width="60%" height="16px" />
          <LoadingSkeleton variant="circular" width={20} height={20} />
        </div>
      </CardHeader>
      <CardContent>
        {type === 'metric' && (
          <div className="space-y-2">
            <LoadingSkeleton variant="text" width="40%" height="32px" />
            <LoadingSkeleton variant="text" width="60%" height="14px" />
          </div>
        )}
        
        {type === 'chart' && (
          <LoadingSkeleton variant="rectangular" width="100%" height="120px" />
        )}
        
        {type === 'list' && (
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <LoadingSkeleton variant="circular" width={24} height={24} />
                  <LoadingSkeleton variant="text" width="120px" height="14px" />
                </div>
                <LoadingSkeleton variant="text" width="40px" height="14px" />
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Loading state with spinner and message
export interface LoadingStateProps {
  message?: string;
  submessage?: string;
  showSpinner?: boolean;
  className?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  submessage,
  showSpinner = true,
  className,
}) => {
  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      {showSpinner && <LoadingSpinner size="lg" className="mb-4" />}
      <h3 className="text-lg font-medium text-foreground">{message}</h3>
      {submessage && (
        <p className="text-sm text-muted-foreground mt-2">{submessage}</p>
      )}
    </div>
  );
};
