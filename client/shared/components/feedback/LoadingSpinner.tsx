/**
 * Loading Spinner Components
 * Various loading indicators for different use cases
 */

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { Loader2, RefreshCw } from 'lucide-react';

const spinnerVariants = cva(
  'animate-spin',
  {
    variants: {
      size: {
        xs: 'h-3 w-3',
        sm: 'h-4 w-4',
        md: 'h-6 w-6',
        lg: 'h-8 w-8',
        xl: 'h-12 w-12',
      },
      variant: {
        default: 'text-primary',
        muted: 'text-muted-foreground',
        white: 'text-white',
        destructive: 'text-destructive',
      },
    },
    defaultVariants: {
      size: 'md',
      variant: 'default',
    },
  }
);

export interface LoadingSpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  text?: string;
  icon?: 'loader' | 'refresh';
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size,
  variant,
  text,
  icon = 'loader',
  className,
  ...props
}) => {
  const IconComponent = icon === 'refresh' ? RefreshCw : Loader2;

  if (text) {
    return (
      <div
        className={cn('flex items-center justify-center space-x-2', className)}
        {...props}
      >
        <IconComponent className={spinnerVariants({ size, variant })} />
        <span className="text-sm text-muted-foreground">{text}</span>
      </div>
    );
  }

  return (
    <div className={cn('flex justify-center', className)} {...props}>
      <IconComponent className={spinnerVariants({ size, variant })} />
    </div>
  );
};

// Inline spinner for buttons and small spaces
export interface InlineSpinnerProps extends VariantProps<typeof spinnerVariants> {
  className?: string;
}

export const InlineSpinner: React.FC<InlineSpinnerProps> = ({
  size = 'sm',
  variant = 'default',
  className,
}) => {
  return <Loader2 className={cn(spinnerVariants({ size, variant }), className)} />;
};

// Full page loading spinner
export interface PageLoadingProps {
  text?: string;
  size?: VariantProps<typeof spinnerVariants>['size'];
}

export const PageLoading: React.FC<PageLoadingProps> = ({
  text = 'Loading...',
  size = 'lg',
}) => {
  return (
    <div className="flex min-h-[400px] items-center justify-center">
      <div className="text-center space-y-4">
        <LoadingSpinner size={size} />
        <p className="text-muted-foreground">{text}</p>
      </div>
    </div>
  );
};

// Overlay loading spinner
export interface LoadingOverlayProps {
  isVisible: boolean;
  text?: string;
  size?: VariantProps<typeof spinnerVariants>['size'];
  className?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  text = 'Loading...',
  size = 'lg',
  className,
}) => {
  if (!isVisible) return null;

  return (
    <div
      className={cn(
        'absolute inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm',
        className
      )}
    >
      <div className="text-center space-y-4">
        <LoadingSpinner size={size} variant="default" />
        <p className="text-sm text-muted-foreground">{text}</p>
      </div>
    </div>
  );
};

// Dots loading animation
export interface LoadingDotsProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingDots: React.FC<LoadingDotsProps> = ({
  size = 'md',
  className,
}) => {
  const sizeClasses = {
    sm: 'h-1 w-1',
    md: 'h-2 w-2',
    lg: 'h-3 w-3',
  };

  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'rounded-full bg-current animate-pulse',
            sizeClasses[size]
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  );
};

// Pulse loading animation
export interface LoadingPulseProps {
  className?: string;
  children?: React.ReactNode;
}

export const LoadingPulse: React.FC<LoadingPulseProps> = ({
  className,
  children,
}) => {
  return (
    <div className={cn('animate-pulse', className)}>
      {children || (
        <div className="h-4 bg-muted rounded w-full" />
      )}
    </div>
  );
};

// Progress bar loading
export interface LoadingProgressProps {
  progress?: number;
  text?: string;
  className?: string;
}

export const LoadingProgress: React.FC<LoadingProgressProps> = ({
  progress,
  text,
  className,
}) => {
  return (
    <div className={cn('space-y-2', className)}>
      {text && (
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">{text}</span>
          {progress !== undefined && (
            <span className="text-muted-foreground">{Math.round(progress)}%</span>
          )}
        </div>
      )}
      <div className="w-full bg-muted rounded-full h-2">
        <div
          className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
          style={{
            width: progress !== undefined ? `${progress}%` : '30%',
            animation: progress === undefined ? 'pulse 2s infinite' : undefined,
          }}
        />
      </div>
    </div>
  );
};

// Skeleton loading for specific shapes
export interface LoadingSkeletonProps {
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  className?: string;
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  variant = 'text',
  width,
  height,
  className,
}) => {
  const baseClasses = 'animate-pulse bg-muted';
  
  const variantClasses = {
    text: 'rounded h-4',
    circular: 'rounded-full',
    rectangular: 'rounded',
  };

  const style: React.CSSProperties = {};
  if (width) style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) style.height = typeof height === 'number' ? `${height}px` : height;

  return (
    <div
      className={cn(baseClasses, variantClasses[variant], className)}
      style={style}
    />
  );
};

// Loading state wrapper
export interface LoadingWrapperProps {
  loading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  overlay?: boolean;
  text?: string;
}

export const LoadingWrapper: React.FC<LoadingWrapperProps> = ({
  loading,
  children,
  fallback,
  overlay = false,
  text,
}) => {
  if (loading && !overlay) {
    return (
      <div>
        {fallback || <PageLoading text={text} />}
      </div>
    );
  }

  return (
    <div className="relative">
      {children}
      {loading && overlay && (
        <LoadingOverlay isVisible={true} text={text} />
      )}
    </div>
  );
};
