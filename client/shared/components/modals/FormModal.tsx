/**
 * Form Modal Component
 * Reusable modal wrapper for forms with consistent styling and behavior
 */

import React from "react";
import { Button } from "@/components/ui/button";
import {
  Modal,
  ModalHeader,
  ModalTitle,
  ModalDescription,
  ModalBody,
  ModalFooter,
  ModalProps,
} from "./Modal";

export interface FormModalProps extends Omit<ModalProps, "children"> {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;

  // Form actions
  onSubmit?: () => void | Promise<void>;
  onCancel?: () => void;

  // Button configuration
  submitText?: string;
  cancelText?: string;
  submitVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";

  // State
  loading?: boolean;
  disabled?: boolean;

  // Footer customization
  customFooter?: React.ReactNode;
  hideFooter?: boolean;
}

export const FormModal: React.FC<FormModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  icon,
  children,
  onSubmit,
  onCancel,
  submitText = "Save",
  cancelText = "Cancel",
  submitVariant = "default",
  loading = false,
  disabled = false,
  customFooter,
  hideFooter = false,
  size = "lg",
  scrollable = true,
  className,
}) => {
  const handleSubmit = async () => {
    if (onSubmit) {
      try {
        await onSubmit();
      } catch (error) {
        // Error handling should be done by the parent component
        console.error("Form submission failed:", error);
      }
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size={size}
      scrollable={scrollable}
      className={className}
    >
      <ModalHeader>
        <ModalTitle icon={icon}>{title}</ModalTitle>
        {description && <ModalDescription>{description}</ModalDescription>}
      </ModalHeader>

      <ModalBody>{children}</ModalBody>

      {!hideFooter && (
        <ModalFooter justify="end">
          {customFooter || (
            <>
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={loading}
              >
                {cancelText}
              </Button>
              {onSubmit && (
                <Button
                  variant={submitVariant}
                  onClick={handleSubmit}
                  disabled={disabled || loading}
                  loading={loading}
                >
                  {submitText}
                </Button>
              )}
            </>
          )}
        </ModalFooter>
      )}
    </Modal>
  );
};
