/**
 * Generic Modal System
 * Compound component pattern for consistent modal behavior across the application
 */

import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";

// Modal size variants
const modalVariants = cva("", {
  variants: {
    size: {
      sm: "max-w-md",
      md: "max-w-lg",
      lg: "max-w-2xl",
      xl: "max-w-4xl",
      "2xl": "max-w-6xl",
      full: "max-w-[95vw] max-h-[95vh]",
    },
    scrollable: {
      true: "max-h-[90vh] overflow-y-auto",
      false: "",
    },
  },
  defaultVariants: {
    size: "md",
    scrollable: false,
  },
});

// Base Modal Props
export interface ModalProps extends VariantProps<typeof modalVariants> {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
}

// Modal Context for compound components
interface ModalContextValue {
  isOpen: boolean;
  onClose: () => void;
  size?: VariantProps<typeof modalVariants>["size"];
}

const ModalContext = React.createContext<ModalContextValue | null>(null);

const useModalContext = () => {
  const context = React.useContext(ModalContext);
  if (!context) {
    throw new Error("Modal compound components must be used within Modal");
  }
  return context;
};

// Main Modal Component
export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  children,
  size,
  scrollable,
  className,
}) => {
  const contextValue: ModalContextValue = {
    isOpen,
    onClose,
    size,
  };

  return (
    <ModalContext.Provider value={contextValue}>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent
          className={cn(modalVariants({ size, scrollable }), className)}
        >
          {children}
        </DialogContent>
      </Dialog>
    </ModalContext.Provider>
  );
};

// Modal Header Component
export interface ModalHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({
  children,
  className,
}) => {
  return (
    <DialogHeader className={cn("space-y-3", className)}>
      {children}
    </DialogHeader>
  );
};

// Modal Title Component
export interface ModalTitleProps {
  children: React.ReactNode;
  className?: string;
  icon?: React.ReactNode;
}

export const ModalTitle: React.FC<ModalTitleProps> = ({
  children,
  className,
  icon,
}) => {
  return (
    <DialogTitle className={cn("flex items-center gap-2", className)}>
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span>{children}</span>
    </DialogTitle>
  );
};

// Modal Description Component
export interface ModalDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

export const ModalDescription: React.FC<ModalDescriptionProps> = ({
  children,
  className,
}) => {
  return (
    <DialogDescription className={className}>{children}</DialogDescription>
  );
};

// Modal Body Component
export interface ModalBodyProps {
  children: React.ReactNode;
  className?: string;
  padding?: boolean;
}

export const ModalBody: React.FC<ModalBodyProps> = ({
  children,
  className,
  padding = true,
}) => {
  return (
    <div className={cn("flex-1", padding && "py-4", className)}>{children}</div>
  );
};

// Modal Footer Component
export interface ModalFooterProps {
  children: React.ReactNode;
  className?: string;
  justify?: "start" | "center" | "end" | "between";
}

export const ModalFooter: React.FC<ModalFooterProps> = ({
  children,
  className,
  justify = "end",
}) => {
  const justifyClasses = {
    start: "justify-start",
    center: "justify-center",
    end: "justify-end",
    between: "justify-between",
  };

  return (
    <DialogFooter className={cn(justifyClasses[justify], className)}>
      {children}
    </DialogFooter>
  );
};
