/**
 * FormBuilder Component System
 * Generic form builder with dynamic field configuration and validation
 */

import React from 'react';
import { useForm, UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { DynamicInput } from './DynamicInput';
import { Form } from '@/components/ui/form';

export type FieldType = 
  | 'text' 
  | 'email' 
  | 'password' 
  | 'number' 
  | 'tel' 
  | 'url'
  | 'textarea' 
  | 'select' 
  | 'multiselect'
  | 'checkbox' 
  | 'radio' 
  | 'switch'
  | 'date' 
  | 'datetime' 
  | 'time'
  | 'file' 
  | 'image'
  | 'tags'
  | 'rich-text'
  | 'slider'
  | 'rating';

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  group?: string;
}

export interface FieldConfig<T extends FieldValues = FieldValues> {
  name: Path<T>;
  type: FieldType;
  label: string;
  placeholder?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  hidden?: boolean;
  
  // Field-specific props
  options?: SelectOption[];
  multiple?: boolean;
  accept?: string; // for file inputs
  min?: number;
  max?: number;
  step?: number;
  rows?: number; // for textarea
  
  // Validation
  validation?: z.ZodSchema<any>;
  
  // Layout
  className?: string;
  containerClassName?: string;
  fullWidth?: boolean;
  
  // Conditional rendering
  dependsOn?: Path<T>;
  showWhen?: (value: any) => boolean;
  
  // Custom render
  render?: (field: any, form: UseFormReturn<T>) => React.ReactNode;
}

export interface FormSection<T extends FieldValues = FieldValues> {
  title?: string;
  description?: string;
  fields: FieldConfig<T>[];
  className?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

export interface FormBuilderProps<T extends FieldValues = FieldValues> {
  fields?: FieldConfig<T>[];
  sections?: FormSection<T>[];
  schema?: z.ZodSchema<T>;
  defaultValues?: Partial<T>;
  onSubmit: (data: T) => void | Promise<void>;
  onCancel?: () => void;
  
  // Form state
  loading?: boolean;
  disabled?: boolean;
  
  // Layout
  layout?: 'vertical' | 'horizontal' | 'grid';
  columns?: number;
  spacing?: 'sm' | 'md' | 'lg';
  
  // Buttons
  submitText?: string;
  cancelText?: string;
  showCancel?: boolean;
  customActions?: React.ReactNode;
  
  // Styling
  className?: string;
  formClassName?: string;
}

export function FormBuilder<T extends FieldValues = FieldValues>({
  fields = [],
  sections = [],
  schema,
  defaultValues,
  onSubmit,
  onCancel,
  loading = false,
  disabled = false,
  layout = 'vertical',
  columns = 1,
  spacing = 'md',
  submitText = 'Submit',
  cancelText = 'Cancel',
  showCancel = false,
  customActions,
  className,
  formClassName,
}: FormBuilderProps<T>) {
  
  const form = useForm<T>({
    resolver: schema ? zodResolver(schema) : undefined,
    defaultValues: defaultValues as any,
  });

  const { handleSubmit, watch } = form;
  const watchedValues = watch();

  // Combine fields and sections
  const allSections: FormSection<T>[] = [
    ...(fields.length > 0 ? [{ fields }] : []),
    ...sections,
  ];

  const handleFormSubmit = async (data: T) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const shouldShowField = (field: FieldConfig<T>): boolean => {
    if (field.hidden) return false;
    
    if (field.dependsOn && field.showWhen) {
      const dependentValue = watchedValues[field.dependsOn];
      return field.showWhen(dependentValue);
    }
    
    return true;
  };

  const getLayoutClasses = () => {
    const spacingClasses = {
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
    };

    const layoutClasses = {
      vertical: 'flex flex-col',
      horizontal: 'flex flex-row flex-wrap',
      grid: `grid grid-cols-1 md:grid-cols-${columns}`,
    };

    return `${layoutClasses[layout]} ${spacingClasses[spacing]}`;
  };

  return (
    <div className={className}>
      <Form {...form}>
        <form 
          onSubmit={handleSubmit(handleFormSubmit)}
          className={`space-y-6 ${formClassName || ''}`}
        >
          {allSections.map((section, sectionIndex) => (
            <FormSection
              key={sectionIndex}
              section={section}
              form={form}
              shouldShowField={shouldShowField}
              layoutClasses={getLayoutClasses()}
              disabled={disabled || loading}
            />
          ))}
          
          {(customActions || onSubmit || showCancel) && (
            <div className="flex justify-end gap-3 pt-4 border-t">
              {customActions}
              {showCancel && onCancel && (
                <button
                  type="button"
                  onClick={onCancel}
                  disabled={loading}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  {cancelText}
                </button>
              )}
              <button
                type="submit"
                disabled={disabled || loading}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {loading ? 'Submitting...' : submitText}
              </button>
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}

interface FormSectionProps<T extends FieldValues> {
  section: FormSection<T>;
  form: UseFormReturn<T>;
  shouldShowField: (field: FieldConfig<T>) => boolean;
  layoutClasses: string;
  disabled: boolean;
}

function FormSection<T extends FieldValues>({
  section,
  form,
  shouldShowField,
  layoutClasses,
  disabled,
}: FormSectionProps<T>) {
  const [collapsed, setCollapsed] = React.useState(section.defaultCollapsed || false);
  
  const visibleFields = section.fields.filter(shouldShowField);
  
  if (visibleFields.length === 0) return null;

  return (
    <div className={`space-y-4 ${section.className || ''}`}>
      {section.title && (
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              {section.title}
            </h3>
            {section.description && (
              <p className="text-sm text-gray-500 mt-1">
                {section.description}
              </p>
            )}
          </div>
          {section.collapsible && (
            <button
              type="button"
              onClick={() => setCollapsed(!collapsed)}
              className="text-sm text-indigo-600 hover:text-indigo-500"
            >
              {collapsed ? 'Expand' : 'Collapse'}
            </button>
          )}
        </div>
      )}
      
      {(!section.collapsible || !collapsed) && (
        <div className={layoutClasses}>
          {visibleFields.map((field) => (
            <div
              key={field.name}
              className={`${field.containerClassName || ''} ${
                field.fullWidth ? 'col-span-full' : ''
              }`}
            >
              {field.render ? (
                field.render(field, form)
              ) : (
                <DynamicInput
                  field={field}
                  form={form}
                  disabled={disabled || field.disabled}
                />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
