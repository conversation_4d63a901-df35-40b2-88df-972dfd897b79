/**
 * DataTable Column Helpers
 * Utility functions and components for creating table columns
 */

import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown, 
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Copy,
} from 'lucide-react';
import { format } from 'date-fns';

// Column helper types
export interface ActionMenuItem {
  label: string;
  icon?: React.ReactNode;
  onClick: (row: any) => void;
  variant?: 'default' | 'destructive';
  disabled?: (row: any) => boolean;
}

export interface ColumnHelperOptions {
  enableSorting?: boolean;
  className?: string;
}

// Sortable header component
export function SortableHeader({ 
  column, 
  children, 
  className 
}: { 
  column: any; 
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <Button
      variant="ghost"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      className={`h-auto p-0 font-medium hover:bg-transparent ${className || ''}`}
    >
      {children}
      {column.getIsSorted() === "asc" ? (
        <ArrowUp className="ml-2 h-4 w-4" />
      ) : column.getIsSorted() === "desc" ? (
        <ArrowDown className="ml-2 h-4 w-4" />
      ) : (
        <ArrowUpDown className="ml-2 h-4 w-4" />
      )}
    </Button>
  );
}

// Actions dropdown component
export function ActionsDropdown({ 
  row, 
  actions 
}: { 
  row: any; 
  actions: ActionMenuItem[];
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {actions.map((action, index) => (
          <DropdownMenuItem
            key={index}
            onClick={() => action.onClick(row.original)}
            disabled={action.disabled?.(row.original)}
            className={action.variant === 'destructive' ? 'text-red-600' : ''}
          >
            {action.icon && <span className="mr-2">{action.icon}</span>}
            {action.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Column helper functions
export const columnHelpers = {
  // Text column with optional sorting
  text: <T,>(
    accessorKey: keyof T,
    header: string,
    options: ColumnHelperOptions = {}
  ): ColumnDef<T> => ({
    accessorKey: accessorKey as string,
    header: options.enableSorting !== false ? ({ column }) => (
      <SortableHeader column={column} className={options.className}>
        {header}
      </SortableHeader>
    ) : header,
    cell: ({ getValue }) => (
      <div className={options.className}>
        {getValue() as string}
      </div>
    ),
  }),

  // Badge column for status/category display
  badge: <T,>(
    accessorKey: keyof T,
    header: string,
    variantMap?: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'>,
    options: ColumnHelperOptions = {}
  ): ColumnDef<T> => ({
    accessorKey: accessorKey as string,
    header: options.enableSorting !== false ? ({ column }) => (
      <SortableHeader column={column} className={options.className}>
        {header}
      </SortableHeader>
    ) : header,
    cell: ({ getValue }) => {
      const value = getValue() as string;
      const variant = variantMap?.[value] || 'default';
      return (
        <Badge variant={variant} className={options.className}>
          {value}
        </Badge>
      );
    },
  }),

  // Date column with formatting
  date: <T,>(
    accessorKey: keyof T,
    header: string,
    dateFormat: string = 'PPP',
    options: ColumnHelperOptions = {}
  ): ColumnDef<T> => ({
    accessorKey: accessorKey as string,
    header: options.enableSorting !== false ? ({ column }) => (
      <SortableHeader column={column} className={options.className}>
        {header}
      </SortableHeader>
    ) : header,
    cell: ({ getValue }) => {
      const date = getValue() as string | Date;
      if (!date) return <span className="text-muted-foreground">-</span>;
      return (
        <div className={options.className}>
          {format(new Date(date), dateFormat)}
        </div>
      );
    },
  }),

  // Avatar column for user display
  avatar: <T,>(
    nameKey: keyof T,
    avatarKey?: keyof T,
    emailKey?: keyof T,
    options: ColumnHelperOptions = {}
  ): ColumnDef<T> => ({
    id: 'user',
    header: 'User',
    cell: ({ row }) => {
      const name = row.getValue(nameKey as string) as string;
      const avatar = avatarKey ? row.getValue(avatarKey as string) as string : undefined;
      const email = emailKey ? row.getValue(emailKey as string) as string : undefined;
      
      return (
        <div className={`flex items-center space-x-2 ${options.className || ''}`}>
          <Avatar className="h-8 w-8">
            <AvatarImage src={avatar} />
            <AvatarFallback>
              {name?.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{name}</div>
            {email && (
              <div className="text-sm text-muted-foreground">{email}</div>
            )}
          </div>
        </div>
      );
    },
  }),

  // Actions column
  actions: <T,>(
    actions: ActionMenuItem[],
    options: ColumnHelperOptions = {}
  ): ColumnDef<T> => ({
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => (
      <ActionsDropdown row={row} actions={actions} />
    ),
  }),

  // Custom column with render function
  custom: <T,>(
    id: string,
    header: string | ((props: any) => React.ReactNode),
    cell: (props: any) => React.ReactNode,
    options: ColumnHelperOptions = {}
  ): ColumnDef<T> => ({
    id,
    header: typeof header === 'string' && options.enableSorting !== false ? ({ column }) => (
      <SortableHeader column={column} className={options.className}>
        {header}
      </SortableHeader>
    ) : header,
    cell,
  }),
};

// Common action presets
export const commonActions = {
  view: (onView: (row: any) => void): ActionMenuItem => ({
    label: 'View',
    icon: <Eye className="h-4 w-4" />,
    onClick: onView,
  }),

  edit: (onEdit: (row: any) => void): ActionMenuItem => ({
    label: 'Edit',
    icon: <Edit className="h-4 w-4" />,
    onClick: onEdit,
  }),

  delete: (onDelete: (row: any) => void): ActionMenuItem => ({
    label: 'Delete',
    icon: <Trash2 className="h-4 w-4" />,
    onClick: onDelete,
    variant: 'destructive',
  }),

  copy: (onCopy: (row: any) => void): ActionMenuItem => ({
    label: 'Copy',
    icon: <Copy className="h-4 w-4" />,
    onClick: onCopy,
  }),
};

// Status badge variants
export const statusVariants = {
  active: 'default' as const,
  inactive: 'secondary' as const,
  pending: 'outline' as const,
  rejected: 'destructive' as const,
  approved: 'default' as const,
  draft: 'secondary' as const,
  published: 'default' as const,
  archived: 'outline' as const,
};

// Priority badge variants
export const priorityVariants = {
  low: 'secondary' as const,
  medium: 'outline' as const,
  high: 'default' as const,
  urgent: 'destructive' as const,
};

// Example usage helper
export function createCandidateColumns() {
  return [
    columnHelpers.avatar('name', 'avatar', 'email'),
    columnHelpers.text('position', 'Position'),
    columnHelpers.badge('status', 'Status', statusVariants),
    columnHelpers.date('appliedAt', 'Applied', 'PPP'),
    columnHelpers.actions([
      commonActions.view((row) => console.log('View', row)),
      commonActions.edit((row) => console.log('Edit', row)),
      commonActions.delete((row) => console.log('Delete', row)),
    ]),
  ];
}
