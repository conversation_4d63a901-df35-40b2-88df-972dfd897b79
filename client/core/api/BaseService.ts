/**
 * Base Service Class
 * Provides common functionality for all domain services
 */

import { ApiClient, ApiResponse, PaginatedResponse } from "./ApiClient";

export interface BaseEntity {
  id: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  filters?: Record<string, any>;
}

export interface CreateData {
  [key: string]: any;
}

export interface UpdateData {
  [key: string]: any;
}

/**
 * Generic CRUD operations interface
 */
export interface CrudService<
  T extends BaseEntity,
  C extends CreateData,
  U extends UpdateData,
> {
  getAll(params?: QueryParams): Promise<PaginatedResponse<T>>;
  getById(id: string): Promise<ApiResponse<T>>;
  create(data: C): Promise<ApiResponse<T>>;
  update(id: string, data: U): Promise<ApiResponse<T>>;
  delete(id: string): Promise<ApiResponse<void>>;
}

/**
 * Base service implementation
 */
export abstract class BaseService<
  T extends BaseEntity,
  C extends CreateData,
  U extends UpdateData,
> implements CrudService<T, C, U>
{
  protected apiClient: ApiClient;
  protected baseEndpoint: string;

  constructor(apiClient: ApiClient, baseEndpoint: string) {
    this.apiClient = apiClient;
    this.baseEndpoint = baseEndpoint.startsWith("/")
      ? baseEndpoint
      : `/${baseEndpoint}`;
  }

  /**
   * Get all entities with pagination and filtering
   */
  async getAll(params?: QueryParams): Promise<PaginatedResponse<T>> {
    const queryParams = this.buildQueryParams(params);
    const response = await this.apiClient.get<T[]>(
      this.baseEndpoint,
      queryParams,
    );

    // Ensure response has pagination metadata
    if (!response.meta) {
      response.meta = {
        total: response.data?.length || 0,
        page: params?.page || 1,
        limit: params?.limit || 10,
        hasNext: false,
        hasPrev: false,
      };
    }

    return response as PaginatedResponse<T>;
  }

  /**
   * Get entity by ID
   */
  async getById(id: string): Promise<ApiResponse<T>> {
    this.validateId(id);
    return this.apiClient.get<T>(`${this.baseEndpoint}/${id}`);
  }

  /**
   * Create new entity
   */
  async create(data: C): Promise<ApiResponse<T>> {
    this.validateCreateData(data);
    const processedData = this.preprocessCreateData(data);
    const response = await this.apiClient.post<T>(
      this.baseEndpoint,
      processedData,
    );
    return this.postprocessResponse(response);
  }

  /**
   * Update existing entity
   */
  async update(id: string, data: U): Promise<ApiResponse<T>> {
    this.validateId(id);
    this.validateUpdateData(data);
    const processedData = this.preprocessUpdateData(data);
    const response = await this.apiClient.put<T>(
      `${this.baseEndpoint}/${id}`,
      processedData,
    );
    return this.postprocessResponse(response);
  }

  /**
   * Delete entity
   */
  async delete(id: string): Promise<ApiResponse<void>> {
    this.validateId(id);
    return this.apiClient.delete<void>(`${this.baseEndpoint}/${id}`);
  }

  /**
   * Bulk operations
   */
  async bulkCreate(data: C[]): Promise<ApiResponse<T[]>> {
    data.forEach((item) => this.validateCreateData(item));
    const processedData = data.map((item) => this.preprocessCreateData(item));
    return this.apiClient.post<T[]>(`${this.baseEndpoint}/bulk`, processedData);
  }

  async bulkUpdate(
    updates: Array<{ id: string; data: U }>,
  ): Promise<ApiResponse<T[]>> {
    updates.forEach(({ id, data }) => {
      this.validateId(id);
      this.validateUpdateData(data);
    });

    const processedUpdates = updates.map(({ id, data }) => ({
      id,
      data: this.preprocessUpdateData(data),
    }));

    return this.apiClient.put<T[]>(
      `${this.baseEndpoint}/bulk`,
      processedUpdates,
    );
  }

  async bulkDelete(ids: string[]): Promise<ApiResponse<void>> {
    ids.forEach((id) => this.validateId(id));
    return this.apiClient.delete<void>(`${this.baseEndpoint}/bulk`, { ids });
  }

  /**
   * Search functionality
   */
  async search(
    query: string,
    params?: Omit<QueryParams, "search">,
  ): Promise<PaginatedResponse<T>> {
    const searchParams = { ...params, search: query };
    return this.getAll(searchParams);
  }

  /**
   * Count entities
   */
  async count(
    filters?: Record<string, any>,
  ): Promise<ApiResponse<{ count: number }>> {
    const params = filters ? { filters } : undefined;
    return this.apiClient.get<{ count: number }>(
      `${this.baseEndpoint}/count`,
      params,
    );
  }

  /**
   * Check if entity exists
   */
  async exists(id: string): Promise<ApiResponse<{ exists: boolean }>> {
    this.validateId(id);
    return this.apiClient.get<{ exists: boolean }>(
      `${this.baseEndpoint}/${id}/exists`,
    );
  }

  // Protected methods for customization by subclasses

  /**
   * Build query parameters for API requests
   */
  protected buildQueryParams(params?: QueryParams): Record<string, any> {
    if (!params) return {};

    const queryParams: Record<string, any> = {};

    if (params.page !== undefined) queryParams.page = params.page;
    if (params.limit !== undefined) queryParams.limit = params.limit;
    if (params.search) queryParams.search = params.search;
    if (params.sortBy) queryParams.sortBy = params.sortBy;
    if (params.sortOrder) queryParams.sortOrder = params.sortOrder;
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams[key] = value;
        }
      });
    }

    return queryParams;
  }

  /**
   * Validate entity ID
   */
  protected validateId(id: string): void {
    if (!id || typeof id !== "string" || id.trim().length === 0) {
      throw new Error("Invalid ID provided");
    }
  }

  /**
   * Validate create data - override in subclasses for specific validation
   */
  protected validateCreateData(data: C): void {
    if (!data || typeof data !== "object") {
      throw new Error("Invalid create data provided");
    }
  }

  /**
   * Validate update data - override in subclasses for specific validation
   */
  protected validateUpdateData(data: U): void {
    if (!data || typeof data !== "object") {
      throw new Error("Invalid update data provided");
    }
  }

  /**
   * Preprocess create data before sending to API
   * Override in subclasses for data transformation
   */
  protected preprocessCreateData(data: C): C {
    return data;
  }

  /**
   * Preprocess update data before sending to API
   * Override in subclasses for data transformation
   */
  protected preprocessUpdateData(data: U): U {
    return data;
  }

  /**
   * Postprocess API response
   * Override in subclasses for response transformation
   */
  protected postprocessResponse(response: ApiResponse<T>): ApiResponse<T> {
    return response;
  }

  /**
   * Get endpoint for specific operations
   */
  protected getEndpoint(path?: string): string {
    return path ? `${this.baseEndpoint}/${path}` : this.baseEndpoint;
  }

  /**
   * Handle service-specific errors
   * Override in subclasses for custom error handling
   */
  protected handleError(error: any): never {
    throw error;
  }
}

/**
 * Service factory for creating service instances
 */
export class ServiceFactory {
  private apiClient: ApiClient;
  private services: Map<string, any> = new Map();

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  /**
   * Register a service
   */
  register<T>(
    name: string,
    serviceClass: new (apiClient: ApiClient) => T,
  ): void {
    this.services.set(name, serviceClass);
  }

  /**
   * Get a service instance
   */
  get<T>(name: string): T {
    const ServiceClass = this.services.get(name);
    if (!ServiceClass) {
      throw new Error(`Service '${name}' not found`);
    }
    return new ServiceClass(this.apiClient);
  }

  /**
   * Get API client
   */
  getApiClient(): ApiClient {
    return this.apiClient;
  }
}
