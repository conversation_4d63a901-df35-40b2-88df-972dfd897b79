/**
 * Core API Client
 * Provides centralized HTTP client with error handling, retries, and response transformation
 */

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasNext?: boolean;
    hasPrev?: boolean;
  };
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: {
    total: number;
    page: number;
    limit: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface RequestOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  params?: Record<string, any>;
  body?: any;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

export interface ApiError extends Error {
  status?: number;
  code?: string;
  details?: any;
}

export class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private defaultTimeout: number;
  private defaultRetries: number;

  constructor(
    baseURL: string,
    options: {
      defaultHeaders?: Record<string, string>;
      timeout?: number;
      retries?: number;
    } = {},
  ) {
    this.baseURL = baseURL.replace(/\/$/, ""); // Remove trailing slash
    this.defaultHeaders = {
      "Content-Type": "application/json",
      ...options.defaultHeaders,
    };
    this.defaultTimeout = options.timeout || 10000; // 10 seconds
    this.defaultRetries = options.retries || 3;
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.defaultHeaders["Authorization"] = `Bearer ${token}`;
  }

  /**
   * Remove authentication token
   */
  removeAuthToken(): void {
    delete this.defaultHeaders["Authorization"];
  }

  /**
   * Main request method
   */
  async request<T = any>(
    endpoint: string,
    options: RequestOptions = {},
  ): Promise<ApiResponse<T>> {
    const {
      method = "GET",
      headers = {},
      params,
      body,
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      retryDelay = 1000,
    } = options;

    const url = this.buildURL(endpoint, params);
    const requestHeaders = { ...this.defaultHeaders, ...headers };

    let lastError: ApiError;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          method,
          headers: requestHeaders,
          body: body ? JSON.stringify(body) : undefined,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw await this.createApiError(response);
        }

        const data = await response.json();
        return this.transformResponse<T>(data);
      } catch (error) {
        lastError = this.handleError(error);

        // Don't retry on client errors (4xx) except 429 (rate limit)
        if (
          lastError.status &&
          lastError.status >= 400 &&
          lastError.status < 500 &&
          lastError.status !== 429
        ) {
          throw lastError;
        }

        // If this is the last attempt, throw the error
        if (attempt === retries) {
          throw lastError;
        }

        // Wait before retrying
        await this.delay(retryDelay * Math.pow(2, attempt)); // Exponential backoff
      }
    }

    throw lastError!;
  }

  /**
   * GET request
   */
  async get<T = any>(
    endpoint: string,
    params?: Record<string, any>,
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "GET", params });
  }

  /**
   * POST request
   */
  async post<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "POST", body });
  }

  /**
   * PUT request
   */
  async put<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "PUT", body });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "PATCH", body });
  }

  /**
   * Build full URL with query parameters
   */
  private buildURL(endpoint: string, params?: Record<string, any>): string {
    const url = new URL(
      endpoint.startsWith("/") ? endpoint.slice(1) : endpoint,
      this.baseURL,
    );

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach((v) => url.searchParams.append(key, String(v)));
          } else {
            url.searchParams.append(key, String(value));
          }
        }
      });
    }

    return url.toString();
  }

  /**
   * Transform API response to standard format
   */
  private transformResponse<T>(data: any): ApiResponse<T> {
    // If the response is already in our standard format, return as-is
    if (data && typeof data === "object" && "success" in data) {
      return data as ApiResponse<T>;
    }

    // Transform to standard format
    return {
      data: data as T,
      success: true,
      message: "Request successful",
    };
  }

  /**
   * Create API error from response
   */
  private async createApiError(response: Response): Promise<ApiError> {
    let errorData: any;

    try {
      errorData = await response.json();
    } catch {
      errorData = { message: response.statusText };
    }

    const error = new Error(
      errorData.message || `HTTP ${response.status}`,
    ) as ApiError;
    error.status = response.status;
    error.code = errorData.code;
    error.details = errorData.details || errorData;

    return error;
  }

  /**
   * Handle and normalize errors
   */
  private handleError(error: any): ApiError {
    if (error.name === "AbortError") {
      const timeoutError = new Error("Request timeout") as ApiError;
      timeoutError.code = "TIMEOUT";
      return timeoutError;
    }

    if (error instanceof TypeError && error.message.includes("fetch")) {
      const networkError = new Error("Network error") as ApiError;
      networkError.code = "NETWORK_ERROR";
      return networkError;
    }

    // If it's already an ApiError, return as-is
    if (error.status !== undefined) {
      return error as ApiError;
    }

    // Convert generic error to ApiError
    const apiError = new Error(error.message || "Unknown error") as ApiError;
    apiError.code = "UNKNOWN_ERROR";
    return apiError;
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
