/**
 * Service Configuration and Instances
 * Central configuration for all API services
 */

import { ApiClient, ServiceFactory } from "./index";

// API Configuration
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api",
  timeout: 10000,
  retries: 3,
};

// Create API client instance
export const apiClient = new ApiClient(API_CONFIG.baseURL, {
  timeout: API_CONFIG.timeout,
  retries: API_CONFIG.retries,
  defaultHeaders: {
    "Content-Type": "application/json",
  },
});

// Create service factory
export const serviceFactory = new ServiceFactory(apiClient);

// Service registry - will be populated as domain services are created
export const services = {
  // Domain services will be added here during migration
  // candidates: new CandidateService(apiClient),
  // jobs: new JobService(apiClient),
  // interviews: new InterviewService(apiClient),
  // calendar: new CalendarService(apiClient),
  // analytics: new AnalyticsService(apiClient),
};

/**
 * Initialize services with authentication token
 */
export function initializeServices(token?: string): void {
  if (token) {
    apiClient.setAuthToken(token);
  }
}

/**
 * Clear authentication from services
 */
export function clearAuthentication(): void {
  apiClient.removeAuthToken();
}

/**
 * Get API client instance
 */
export function getApiClient(): ApiClient {
  return apiClient;
}

/**
 * Get service factory instance
 */
export function getServiceFactory(): ServiceFactory {
  return serviceFactory;
}
