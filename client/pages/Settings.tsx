import { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Settings as SettingsIcon,
  Building,
  Mail,
  Phone,
  MapPin,
  Globe,
  Palette,
  Plug,
  Save,
  Upload,
  Shield,
  Key,
  Bell,
  Users,
  FileText,
  Link,
  Zap,
  Camera,
  Server,
  Database,
  Code,
  Webhook,
  Archive,
  Download,
  Trash2,
  Refresh<PERSON><PERSON>,
  AlertTriangle,
} from "lucide-react";
import { mockOrganization } from "@/data/profilesData";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";

export default function Settings() {
  const { t } = useTranslation();
  const [organization, setOrganization] = useState(mockOrganization);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(organization);

  const handleSave = () => {
    setOrganization(formData);
    setIsEditing(false);
    toast.success("Settings saved successfully!");
  };

  const handleCancel = () => {
    setFormData(organization);
    setIsEditing(false);
  };

  const updateSetting = (section: string, key: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  const updateIntegration = (integration: string, enabled: boolean) => {
    setFormData((prev) => ({
      ...prev,
      settings: {
        ...prev.settings,
        integrations: {
          ...prev.settings.integrations,
          [integration]: enabled,
        },
      },
    }));
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              {t.settings.title}
            </h1>
            <p className="text-muted-foreground">{t.settings.subtitle}</p>
          </div>
          <div className="flex gap-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  className="rounded-xl"
                >
                  {t.common.cancel}
                </Button>
                <Button onClick={handleSave} className="ai-button gap-2">
                  <Save className="w-4 h-4" />
                  {t.common.save} Thay đổi
                </Button>
              </>
            ) : (
              <Button
                onClick={() => setIsEditing(true)}
                className="ai-button gap-2"
              >
                <SettingsIcon className="w-4 h-4" />
                {t.common.edit} {t.settings.title}
              </Button>
            )}
          </div>
        </div>

        {/* Settings Tabs */}
        <Tabs defaultValue="organization" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="organization" className="gap-2">
              <Building className="w-4 h-4" />
              {t.settings.organization}
            </TabsTrigger>
            <TabsTrigger value="email" className="gap-2">
              <Mail className="w-4 h-4" />
              Email
            </TabsTrigger>
            <TabsTrigger value="integrations" className="gap-2">
              <Plug className="w-4 h-4" />
              {t.settings.integrations}
            </TabsTrigger>
            <TabsTrigger value="security" className="gap-2">
              <Shield className="w-4 h-4" />
              {t.settings.security}
            </TabsTrigger>
            <TabsTrigger value="billing" className="gap-2">
              <FileText className="w-4 h-4" />
              Billing
            </TabsTrigger>
            <TabsTrigger value="advanced" className="gap-2">
              <Server className="w-4 h-4" />
              Advanced
            </TabsTrigger>
          </TabsList>

          {/* Organization Settings */}
          <TabsContent value="organization" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="w-5 h-5" />
                    Company Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">Company Name</Label>
                    <Input
                      id="companyName"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={formData.website}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          website: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="industry">Industry</Label>
                      <Select
                        value={formData.industry}
                        onValueChange={(value) =>
                          setFormData((prev) => ({ ...prev, industry: value }))
                        }
                        disabled={!isEditing}
                      >
                        <SelectTrigger className="rounded-xl">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="Healthcare">Healthcare</SelectItem>
                          <SelectItem value="Finance">Finance</SelectItem>
                          <SelectItem value="Education">Education</SelectItem>
                          <SelectItem value="Retail">Retail</SelectItem>
                          <SelectItem value="Manufacturing">
                            Manufacturing
                          </SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="size">Company Size</Label>
                      <Select
                        value={formData.size}
                        onValueChange={(value) =>
                          setFormData((prev) => ({ ...prev, size: value }))
                        }
                        disabled={!isEditing}
                      >
                        <SelectTrigger className="rounded-xl">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1-10 employees">
                            1-10 employees
                          </SelectItem>
                          <SelectItem value="11-50 employees">
                            11-50 employees
                          </SelectItem>
                          <SelectItem value="51-200 employees">
                            51-200 employees
                          </SelectItem>
                          <SelectItem value="201-500 employees">
                            201-500 employees
                          </SelectItem>
                          <SelectItem value="501-1000 employees">
                            501-1000 employees
                          </SelectItem>
                          <SelectItem value="1000+ employees">
                            1000+ employees
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          description: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl min-h-[100px]"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">Contact Email</Label>
                    <Input
                      id="contactEmail"
                      type="email"
                      value={formData.contactInfo.email}
                      onChange={(e) =>
                        updateSetting("contactInfo", "email", e.target.value)
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">Phone</Label>
                    <Input
                      id="contactPhone"
                      value={formData.contactInfo.phone}
                      onChange={(e) =>
                        updateSetting("contactInfo", "phone", e.target.value)
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="street">Street Address</Label>
                    <Input
                      id="street"
                      value={formData.contactInfo.address.street}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          contactInfo: {
                            ...prev.contactInfo,
                            address: {
                              ...prev.contactInfo.address,
                              street: e.target.value,
                            },
                          },
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        value={formData.contactInfo.address.city}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            contactInfo: {
                              ...prev.contactInfo,
                              address: {
                                ...prev.contactInfo.address,
                                city: e.target.value,
                              },
                            },
                          }))
                        }
                        disabled={!isEditing}
                        className="rounded-xl"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        value={formData.contactInfo.address.state}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            contactInfo: {
                              ...prev.contactInfo,
                              address: {
                                ...prev.contactInfo.address,
                                state: e.target.value,
                              },
                            },
                          }))
                        }
                        disabled={!isEditing}
                        className="rounded-xl"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="zipCode">ZIP Code</Label>
                      <Input
                        id="zipCode"
                        value={formData.contactInfo.address.zipCode}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            contactInfo: {
                              ...prev.contactInfo,
                              address: {
                                ...prev.contactInfo.address,
                                zipCode: e.target.value,
                              },
                            },
                          }))
                        }
                        disabled={!isEditing}
                        className="rounded-xl"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Input
                        id="country"
                        value={formData.contactInfo.address.country}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            contactInfo: {
                              ...prev.contactInfo,
                              address: {
                                ...prev.contactInfo.address,
                                country: e.target.value,
                              },
                            },
                          }))
                        }
                        disabled={!isEditing}
                        className="rounded-xl"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="w-5 h-5" />
                  Branding & Appearance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="primaryColor"
                        value={formData.branding.primaryColor}
                        onChange={(e) =>
                          updateSetting(
                            "branding",
                            "primaryColor",
                            e.target.value,
                          )
                        }
                        disabled={!isEditing}
                        className="rounded-xl"
                      />
                      <div
                        className="w-10 h-10 rounded border-2 border-border"
                        style={{
                          backgroundColor: formData.branding.primaryColor,
                        }}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="secondaryColor"
                        value={formData.branding.secondaryColor}
                        onChange={(e) =>
                          updateSetting(
                            "branding",
                            "secondaryColor",
                            e.target.value,
                          )
                        }
                        disabled={!isEditing}
                        className="rounded-xl"
                      />
                      <div
                        className="w-10 h-10 rounded border-2 border-border"
                        style={{
                          backgroundColor: formData.branding.secondaryColor,
                        }}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Company Logo</Label>
                    <Button
                      variant="outline"
                      className="w-full rounded-xl"
                      disabled={!isEditing}
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Upload Logo
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Email Settings */}
          <TabsContent value="email" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="w-5 h-5" />
                  Email Templates
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  {
                    key: "welcome",
                    label: "Welcome Email",
                    desc: "Sent to new candidates when they apply",
                  },
                  {
                    key: "rejection",
                    label: "Rejection Email",
                    desc: "Sent when a candidate is not selected",
                  },
                  {
                    key: "interview",
                    label: "Interview Invitation",
                    desc: "Sent to schedule interviews",
                  },
                  {
                    key: "offer",
                    label: "Job Offer",
                    desc: "Sent when extending an offer",
                  },
                ].map((template) => (
                  <div key={template.key} className="space-y-2">
                    <div>
                      <Label className="font-medium">{template.label}</Label>
                      <p className="text-sm text-muted-foreground">
                        {template.desc}
                      </p>
                    </div>
                    <Textarea
                      value={
                        formData.settings.emailTemplates[
                          template.key as keyof typeof formData.settings.emailTemplates
                        ]
                      }
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          settings: {
                            ...prev.settings,
                            emailTemplates: {
                              ...prev.settings.emailTemplates,
                              [template.key]: e.target.value,
                            },
                          },
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl min-h-[100px]"
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Integrations */}
          <TabsContent value="integrations" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[
                {
                  key: "slack",
                  label: "Slack",
                  desc: "Send notifications to Slack channels",
                  icon: "💬",
                },
                {
                  key: "teams",
                  label: "Microsoft Teams",
                  desc: "Integration with Microsoft Teams",
                  icon: "🗣️",
                },
                {
                  key: "zoom",
                  label: "Zoom",
                  desc: "Automatically create Zoom meetings",
                  icon: "📹",
                },
                {
                  key: "calendar",
                  label: "Google Calendar",
                  desc: "Sync with Google Calendar",
                  icon: "📅",
                },
              ].map((integration) => (
                <Card key={integration.key}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{integration.icon}</div>
                        <div>
                          <h3 className="font-semibold">{integration.label}</h3>
                          <p className="text-sm text-muted-foreground">
                            {integration.desc}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            formData.settings.integrations[
                              integration.key as keyof typeof formData.settings.integrations
                            ]
                              ? "default"
                              : "secondary"
                          }
                        >
                          {formData.settings.integrations[
                            integration.key as keyof typeof formData.settings.integrations
                          ]
                            ? "Enabled"
                            : "Disabled"}
                        </Badge>
                        <Switch
                          checked={
                            formData.settings.integrations[
                              integration.key as keyof typeof formData.settings.integrations
                            ]
                          }
                          onCheckedChange={(checked) =>
                            updateIntegration(integration.key, checked)
                          }
                          disabled={!isEditing}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Security */}
          <TabsContent value="security" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="w-5 h-5" />
                    Access Control
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">
                        Two-Factor Authentication
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Require 2FA for all users
                      </p>
                    </div>
                    <Switch />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">SSO Integration</Label>
                      <p className="text-sm text-muted-foreground">
                        Enable single sign-on
                      </p>
                    </div>
                    <Switch />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">Session Timeout</Label>
                      <p className="text-sm text-muted-foreground">
                        Auto-logout after inactivity
                      </p>
                    </div>
                    <Select defaultValue="24">
                      <SelectTrigger className="w-32 rounded-xl">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 hour</SelectItem>
                        <SelectItem value="8">8 hours</SelectItem>
                        <SelectItem value="24">24 hours</SelectItem>
                        <SelectItem value="168">1 week</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="w-5 h-5" />
                    API & Webhooks
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full rounded-xl">
                    <Key className="w-4 h-4 mr-2" />
                    Manage API Keys
                  </Button>
                  <Button variant="outline" className="w-full rounded-xl">
                    <Webhook className="w-4 h-4 mr-2" />
                    Configure Webhooks
                  </Button>
                  <Button variant="outline" className="w-full rounded-xl">
                    <Code className="w-4 h-4 mr-2" />
                    View API Documentation
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Billing */}
          <TabsContent value="billing" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Current Plan
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Plan:</span>
                    <Badge className="bg-primary text-primary-foreground">
                      Professional
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Monthly Cost:</span>
                    <span className="font-semibold">$99/month</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Next Billing:</span>
                    <span>February 22, 2024</span>
                  </div>
                  <Separator />
                  <Button variant="outline" className="w-full rounded-xl">
                    Change Plan
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="w-5 h-5" />
                    Billing History
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    {[
                      {
                        date: "Jan 22, 2024",
                        amount: "$99.00",
                        status: "Paid",
                      },
                      {
                        date: "Dec 22, 2023",
                        amount: "$99.00",
                        status: "Paid",
                      },
                      {
                        date: "Nov 22, 2023",
                        amount: "$99.00",
                        status: "Paid",
                      },
                    ].map((invoice, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 border rounded-lg"
                      >
                        <div>
                          <div className="font-medium">{invoice.amount}</div>
                          <div className="text-sm text-muted-foreground">
                            {invoice.date}
                          </div>
                        </div>
                        <Badge variant="outline">{invoice.status}</Badge>
                      </div>
                    ))}
                  </div>
                  <Button variant="outline" className="w-full rounded-xl">
                    <Download className="w-4 h-4 mr-2" />
                    Download All Invoices
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Advanced */}
          <TabsContent value="advanced" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="w-5 h-5" />
                    Data Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full rounded-xl">
                    <Download className="w-4 h-4 mr-2" />
                    Export All Data
                  </Button>
                  <Button variant="outline" className="w-full rounded-xl">
                    <Archive className="w-4 h-4 mr-2" />
                    Archive Old Records
                  </Button>
                  <Button variant="outline" className="w-full rounded-xl">
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Backup Database
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-destructive">
                    <AlertTriangle className="w-5 h-5" />
                    Danger Zone
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 border border-destructive/30 rounded-lg bg-destructive/5">
                    <h4 className="font-semibold text-destructive mb-2">
                      Delete Organization
                    </h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Permanently delete your organization and all associated
                      data. This action cannot be undone.
                    </p>
                    <Button variant="destructive" className="rounded-xl">
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Organization
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
}
