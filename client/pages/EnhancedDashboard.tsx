import React, { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  Calendar,
  Building2,
  TrendingUp,
  CheckCircle,
  Clock,
  Eye,
  ArrowRight,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Star,
  UserPlus,
  Briefcase,
  MessageSquare,
  Activity,
  Target,
  Award,
  ChevronRight,
  BarChart3,
  PieChart,
  AlertCircle,
  CheckCheck,
  Calendar as CalendarIcon,
  Zap,
} from "lucide-react";
import { useTranslation } from "@/lib/i18n";
import { useDashboardOverview, useUpcomingInterviews } from "@/hooks/useApi";
import { dashboardAdapters } from "@/lib/adapters";
import { ProtectedRoute } from "@/lib/auth";
import { Link } from "react-router-dom";
import { formatDistanceToNow } from "date-fns";

// Enhanced Metric Card with animations and interactive features
const EnhancedMetricCard = ({
  title,
  value,
  change,
  changeType,
  icon: Icon,
  trend,
  onClick,
  actionLabel,
}: {
  title: string;
  value: string;
  change?: string;
  changeType?: "increase" | "decrease" | "neutral";
  icon: any;
  trend?: number[];
  onClick?: () => void;
  actionLabel?: string;
}) => {
  return (
    <Card className="group relative overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <CardContent className="p-6 relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors duration-200">
            <Icon className="w-6 h-6 text-primary" />
          </div>
          {change && (
            <div
              className={`px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1 ${
                changeType === "increase"
                  ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                  : changeType === "decrease"
                    ? "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
                    : "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
              }`}
            >
              {changeType === "increase" && "↗"}
              {changeType === "decrease" && "↘"}
              {change}
            </div>
          )}
        </div>

        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-3xl font-bold">{value}</p>
        </div>

        {trend && (
          <div className="mt-4">
            <div className="flex items-center gap-1 h-8">
              {trend.map((val, i) => (
                <div
                  key={i}
                  className="flex-1 bg-muted rounded-sm"
                  style={{ height: `${(val / Math.max(...trend)) * 100}%` }}
                />
              ))}
            </div>
          </div>
        )}

        {onClick && actionLabel && (
          <Button
            onClick={onClick}
            variant="ghost"
            size="sm"
            className="mt-4 w-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          >
            {actionLabel}
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

// Quick Action Card
const QuickActionCard = ({
  title,
  description,
  icon: Icon,
  onClick,
  color = "primary",
}: {
  title: string;
  description: string;
  icon: any;
  onClick: () => void;
  color?: string;
}) => {
  return (
    <Card className="group hover:shadow-md transition-all duration-200 cursor-pointer border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
      <CardContent className="p-4" onClick={onClick}>
        <div className="flex items-center gap-3">
          <div
            className={`p-2 rounded-lg bg-${color}/10 group-hover:bg-${color}/20 transition-colors`}
          >
            <Icon className={`w-5 h-5 text-${color}`} />
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-sm">{title}</h4>
            <p className="text-xs text-muted-foreground">{description}</p>
          </div>
          <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors" />
        </div>
      </CardContent>
    </Card>
  );
};

// Pipeline Stage Component
const PipelineStage = ({
  stage,
  count,
  percentage,
  color,
  total,
}: {
  stage: string;
  count: number;
  percentage: number;
  color: string;
  total: number;
}) => {
  return (
    <div className="group p-4 rounded-lg border bg-card hover:shadow-md transition-all duration-200">
      <div className="flex items-center justify-between mb-3">
        <span className="text-sm font-medium">{stage}</span>
        <span className="text-lg font-bold">{count}</span>
      </div>

      <Progress value={percentage} className="h-2 mb-2" />

      <div className="flex justify-between text-xs text-muted-foreground">
        <span>{percentage.toFixed(1)}%</span>
        <span>
          {count} / {total}
        </span>
      </div>
    </div>
  );
};

// Recent Activity Item
const ActivityItem = ({
  activity,
  index,
}: {
  activity: any;
  index: number;
}) => {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "candidate_applied":
        return <UserPlus className="w-4 h-4 text-blue-600" />;
      case "interview_scheduled":
        return <Calendar className="w-4 h-4 text-green-600" />;
      case "job_posted":
        return <Briefcase className="w-4 h-4 text-purple-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors group">
      <div className="p-2 rounded-full bg-muted group-hover:bg-background transition-colors">
        {getActivityIcon(activity.type)}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium line-clamp-1">{activity.message}</p>
        <p className="text-xs text-muted-foreground">
          {formatDistanceToNow(new Date(activity.timestamp), {
            addSuffix: true,
          })}
        </p>
      </div>
      <Button
        variant="ghost"
        size="sm"
        className="opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <Eye className="w-4 h-4" />
      </Button>
    </div>
  );
};

export default function EnhancedDashboard() {
  const { t } = useTranslation();
  const [selectedPeriod, setSelectedPeriod] = useState("week");

  // API hooks
  const {
    data: overviewData,
    isLoading: overviewLoading,
    error: overviewError,
    refetch: refetchOverview,
  } = useDashboardOverview();

  const {
    data: interviewsData,
    isLoading: interviewsLoading,
    refetch: refetchInterviews,
  } = useUpcomingInterviews({ limit: 10 });

  // Transform API data
  const dashboard = overviewData?.data
    ? dashboardAdapters.fromApi(overviewData.data)
    : null;
  const upcomingInterviews = interviewsData?.data || [];

  // Extract metrics from API
  const summaryCards = overviewData?.data?.summary_cards || {};
  const recruitmentPipeline = overviewData?.data?.recruitment_pipeline || {};
  const recentActivities = overviewData?.data?.recent_activities || [];
  const topPerformingJobs = overviewData?.data?.top_performing_jobs || [];
  const teamPerformance = overviewData?.data?.team_performance || {};

  const handleRefresh = () => {
    refetchOverview();
    refetchInterviews();
  };

  if (overviewLoading) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="p-6 space-y-6">
            <div className="h-8 bg-muted rounded animate-pulse" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className="h-32 bg-muted rounded-lg animate-pulse"
                />
              ))}
            </div>
            <div className="h-64 bg-muted rounded-lg animate-pulse" />
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout>
        <div className="p-6 space-y-6 max-w-7xl mx-auto">
          {/* Enhanced Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                Bảng Điều Khiển Tuyển Dụng
              </h1>
              <p className="text-muted-foreground mt-1">
                Tổng quan hiệu suất tuyển dụng và quản lý ứng viên
              </p>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <EnhancedMetricCard
              title="Tổng Ứng Viên"
              value={summaryCards.total_candidates?.toString() || "0"}
              change="+12.5%"
              changeType="increase"
              icon={Users}
              trend={[20, 35, 45, 30, 55, 40, 60]}
              onClick={() => (window.location.href = "/candidates")}
              actionLabel="Xem tất cả ứng viên"
            />

            <EnhancedMetricCard
              title="Việc Làm Đang Tuyển"
              value={summaryCards.active_jobs?.toString() || "0"}
              change="+8.2%"
              changeType="increase"
              icon={Briefcase}
              trend={[15, 25, 20, 40, 35, 30, 45]}
              onClick={() => (window.location.href = "/jobs")}
              actionLabel="Quản lý công việc"
            />

            <EnhancedMetricCard
              title="Phỏng Vấn Tuần Này"
              value={summaryCards.interviews_this_week?.toString() || "0"}
              change={
                summaryCards.interviews_this_week > 0
                  ? `+${summaryCards.interviews_this_week}`
                  : "0"
              }
              changeType={
                summaryCards.interviews_this_week > 0 ? "increase" : "neutral"
              }
              icon={Calendar}
              trend={[5, 10, 8, 15, 12, 18, 20]}
              onClick={() => (window.location.href = "/calendar")}
              actionLabel="Xem lịch phỏng vấn"
            />

            <EnhancedMetricCard
              title="Ứng Viên Mới Tháng Này"
              value={summaryCards.new_candidates_this_month?.toString() || "0"}
              change="+15.7%"
              changeType="increase"
              icon={TrendingUp}
              trend={[25, 40, 35, 50, 45, 60, 55]}
              onClick={() => (window.location.href = "/analytics")}
              actionLabel="Xem phân tích"
            />
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Recruitment Pipeline */}
            <div className="lg:col-span-2 space-y-6">
              {/* Pipeline Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Quy Trình Tuyển Dụng
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {Object.entries(recruitmentPipeline.by_status || {}).map(
                      ([stage, count]) => {
                        const total = recruitmentPipeline.total_candidates || 1;
                        const percentage = ((count as number) / total) * 100;

                        return (
                          <PipelineStage
                            key={stage}
                            stage={stage}
                            count={count as number}
                            percentage={percentage}
                            color="primary"
                            total={total}
                          />
                        );
                      },
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Top Performing Jobs */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Award className="w-5 h-5" />
                      Công Việc Hiệu Quả Nhất
                    </div>
                    <Button variant="ghost" size="sm" asChild>
                      <Link to="/jobs">
                        Xem tất cả <ArrowRight className="w-4 h-4 ml-1" />
                      </Link>
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {topPerformingJobs
                      .slice(0, 5)
                      .map((job: any, index: number) => (
                        <div
                          key={job.id}
                          className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-semibold text-primary">
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium text-sm">{job.title}</p>
                              <p className="text-xs text-muted-foreground">
                                {job.department}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary">
                              {job.candidates_count} ứng viên
                            </Badge>
                            <Button variant="ghost" size="sm" asChild>
                              <Link to={`/jobs/detail/${job.id}`}>
                                <Eye className="w-4 h-4" />
                              </Link>
                            </Button>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="w-5 h-5" />
                    Hành Động Nhanh
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <QuickActionCard
                    title="Thêm Ứng Viên"
                    description="Tạo hồ sơ ứng viên mới"
                    icon={UserPlus}
                    onClick={() =>
                      (window.location.href = "/candidates?action=add")
                    }
                  />
                  <QuickActionCard
                    title="Đăng Tuyển Dụng"
                    description="Tạo công việc mới"
                    icon={Plus}
                    onClick={() => (window.location.href = "/jobs?action=add")}
                  />
                  <QuickActionCard
                    title="Lên Lịch Phỏng Vấn"
                    description="Sắp xếp cuộc phỏng vấn"
                    icon={CalendarIcon}
                    onClick={() =>
                      (window.location.href = "/interviews?action=schedule")
                    }
                  />
                  <QuickActionCard
                    title="Tìm Kiếm AI"
                    description="Tìm ứng viên với AI"
                    icon={Search}
                    onClick={() =>
                      (window.location.href = "/candidates?ai-search=true")
                    }
                  />
                </CardContent>
              </Card>

              {/* Upcoming Interviews */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      Phỏng Vấn Sắp Tới
                    </div>
                    <Button variant="ghost" size="sm" asChild>
                      <Link to="/calendar">
                        Xem lịch <ArrowRight className="w-4 h-4 ml-1" />
                      </Link>
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {overviewData?.data?.upcoming_interviews?.map(
                      (interview: any) => (
                        <div
                          key={interview.id}
                          className="p-3 rounded-lg border"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <p className="font-medium text-sm">
                              {interview.candidate_name}
                            </p>
                            <Badge variant="outline">{interview.type}</Badge>
                          </div>
                          <p className="text-xs text-muted-foreground mb-1">
                            {interview.job_title}
                          </p>
                          <div className="flex items-center justify-between">
                            <p className="text-xs text-muted-foreground">
                              {new Date(interview.date).toLocaleDateString()}{" "}
                              {interview.time}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {interview.interviewer_name}
                            </p>
                          </div>
                        </div>
                      ),
                    ) || (
                      <div className="text-center py-4 text-muted-foreground">
                        <Calendar className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">
                          Không có phỏng vấn nào sắp tới
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Team Performance */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Hiệu Suất Đội Ngũ
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        Tỷ lệ hoàn thành phỏng vấn
                      </span>
                      <span className="text-sm font-bold">
                        {teamPerformance.interview_completion_rate || 0}%
                      </span>
                    </div>
                    <Progress
                      value={teamPerformance.interview_completion_rate || 0}
                      className="h-2"
                    />

                    <div className="space-y-2">
                      <p className="text-sm font-medium">Top Recruiters</p>
                      {teamPerformance.top_recruiters
                        ?.slice(0, 3)
                        .map((recruiter: any) => (
                          <div
                            key={recruiter.id}
                            className="flex items-center gap-2"
                          >
                            <Avatar className="w-6 h-6">
                              <AvatarImage src={recruiter.avatar} />
                              <AvatarFallback className="text-xs">
                                {recruiter.name
                                  .split(" ")
                                  .map((n: string) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <p className="text-xs font-medium">
                                {recruiter.name}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {recruiter.created_candidates_count} ứng viên
                              </p>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Hoạt Động Gần Đây
                </div>
                <Button variant="ghost" size="sm">
                  Xem tất cả <ArrowRight className="w-4 h-4 ml-1" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {recentActivities.map((activity: any, index: number) => (
                  <ActivityItem key={index} activity={activity} index={index} />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
