import { useState, useMemo, useRef } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  MessageSquare,
  Plus,
  Search,
  Send,
  Mail,
  FileText,
  CheckCircle,
  MoreHorizontal,
  Reply,
  Forward,
  Archive,
  Trash2,
  Edit,
  Eye,
  Paperclip,
  Image,
  File,
  X,
  Save,
  Check,
  ChevronsUpDown,
  RefreshCw,
  Copy,
  Star,
} from "lucide-react";
import { mockMessages, mockCandidates, Message } from "@/data/mockData";
import { formatDistanceToNow } from "date-fns";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useTranslation } from "@/lib/i18n";

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  category: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface MessageFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
}

const mockTemplates: EmailTemplate[] = [
  {
    id: "template-1",
    name: "Interview Invitation",
    subject: "Interview Invitation - {{jobTitle}} Position at {{companyName}}",
    content: `Dear {{candidateName}},

Thank you for your interest in the {{jobTitle}} position at {{companyName}}. We were impressed with your background and would like to invite you for an interview.

Interview Details:
- Position: {{jobTitle}}
- Date: {{interviewDate}}
- Time: {{interviewTime}}
- Type: {{interviewType}}
- Duration: {{duration}}
- Location: {{location}}

Please confirm your availability by replying to this email.

Best regards,
{{interviewerName}}
{{interviewerTitle}}
{{companyName}}`,
    category: "Interview",
    variables: [
      "candidateName",
      "jobTitle",
      "companyName",
      "interviewDate",
      "interviewTime",
      "interviewType",
      "duration",
      "location",
      "interviewerName",
      "interviewerTitle",
    ],
    isActive: true,
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T14:30:00Z",
  },
  {
    id: "template-2",
    name: "Application Confirmation",
    subject: "Application Received - {{jobTitle}} Position",
    content: `Dear {{candidateName}},

Thank you for applying to the {{jobTitle}} position at {{companyName}}. We have received your application and will review it carefully.

We will be in touch within the next 5-7 business days to update you on the status of your application.

Best regards,
{{recruiterName}}
HR Team
{{companyName}}`,
    category: "Application",
    variables: ["candidateName", "jobTitle", "companyName", "recruiterName"],
    isActive: true,
    createdAt: "2024-01-10T09:15:00Z",
    updatedAt: "2024-01-18T11:45:00Z",
  },
  {
    id: "template-3",
    name: "Job Offer",
    subject: "Job Offer - {{jobTitle}} Position at {{companyName}}",
    content: `Dear {{candidateName}},

We are delighted to extend an offer for the {{jobTitle}} position at {{companyName}}!

Offer Details:
- Position: {{jobTitle}}
- Department: {{department}}
- Start Date: {{startDate}}
- Annual Salary: {{salary}}

Please respond by {{offerDeadline}}.

Congratulations!

{{hiringManagerName}}
{{hiringManagerTitle}}
{{companyName}}`,
    category: "Offer",
    variables: [
      "candidateName",
      "jobTitle",
      "companyName",
      "department",
      "startDate",
      "salary",
      "offerDeadline",
      "hiringManagerName",
      "hiringManagerTitle",
    ],
    isActive: true,
    createdAt: "2024-01-12T16:20:00Z",
    updatedAt: "2024-01-22T09:10:00Z",
  },
];

const signatures = [
  {
    id: "sig-1",
    name: "Professional",
    content: `Best regards,
{{senderName}}
{{senderTitle}}
{{companyName}}

Email: {{email}}
Phone: {{phone}}
Website: {{website}}`,
  },
  {
    id: "sig-2",
    name: "Simple",
    content: `Best regards,
{{senderName}}
{{senderTitle}}
{{email}}`,
  },
  {
    id: "sig-3",
    name: "Detailed",
    content: `Sincerely,

{{senderName}}
{{senderTitle}} | {{companyName}}
{{email}} | {{phone}}
{{address}}`,
  },
];

export default function Messages() {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState<
    "inbox" | "sent" | "drafts" | "templates"
  >("inbox");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
  const [isComposeModalOpen, setIsComposeModalOpen] = useState(false);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<EmailTemplate | null>(null);
  const [templateMode, setTemplateMode] = useState<"create" | "edit">("create");

  // Compose form state
  const [composeForm, setComposeForm] = useState({
    to: "",
    cc: "",
    bcc: "",
    subject: "",
    content: "",
    template: "none",
    signature: "sig-1",
    priority: "normal",
    attachments: [] as MessageFile[],
  });

  // Template form state
  const [templateForm, setTemplateForm] = useState({
    name: "",
    subject: "",
    content: "",
    category: "",
    variables: [] as string[],
    isActive: true,
  });

  const [recipients, setRecipients] = useState(
    mockCandidates && mockCandidates.length > 0
      ? mockCandidates
      : [
          {
            id: "1",
            name: "Nguyễn Văn A",
            email: "<EMAIL>",
            phone: "0912345678",
            initials: "NVA",
            position: "Software Developer",
            experience: "2 years",
            skills: ["React", "Node.js"],
            status: "applied" as const,
            appliedDate: "2024-01-15",
            source: "Website",
            location: "Hà Nội",
          },
          {
            id: "2",
            name: "Trần Thị B",
            email: "<EMAIL>",
            phone: "0923456789",
            initials: "TTB",
            position: "Product Manager",
            experience: "3 years",
            skills: ["Product Management", "Analytics"],
            status: "interview" as const,
            appliedDate: "2024-01-16",
            source: "LinkedIn",
            location: "TP.HCM",
          },
          {
            id: "3",
            name: "Lê Hoàng C",
            email: "<EMAIL>",
            phone: "0934567890",
            initials: "LHC",
            position: "UI/UX Designer",
            experience: "1 year",
            skills: ["Figma", "Adobe XD"],
            status: "screening" as const,
            appliedDate: "2024-01-17",
            source: "Referral",
            location: "Đà Nẵng",
          },
        ],
  );
  const [templates, setTemplates] = useState(mockTemplates);
  const [candidateOpen, setCandidateOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Generate more sample messages
  const generateSampleMessages = () => {
    const messages = [...mockMessages];

    // If no mock candidates available, create some sample ones
    const sampleCandidates =
      mockCandidates && mockCandidates.length > 0
        ? mockCandidates
        : [
            { id: "1", name: "Nguyễn Văn A", email: "<EMAIL>" },
            { id: "2", name: "Trần Thị B", email: "<EMAIL>" },
            { id: "3", name: "Lê Hoàng C", email: "<EMAIL>" },
            { id: "4", name: "Phạm Minh D", email: "<EMAIL>" },
            { id: "5", name: "Võ Thị E", email: "<EMAIL>" },
          ];

    const messageTypes = ["sent", "received", "draft"];
    const subjects = [
      "Interview scheduled for next week",
      "Application status update",
      "Follow-up on previous discussion",
      "Offer details and next steps",
      "Reference check request",
      "Welcome to the team!",
      "Technical assessment results",
      "Schedule availability confirmation",
    ];

    for (let i = 0; i < 15; i++) {
      const candidate =
        sampleCandidates[Math.floor(Math.random() * sampleCandidates.length)];
      const subject = subjects[Math.floor(Math.random() * subjects.length)];
      const type =
        messageTypes[Math.floor(Math.random() * messageTypes.length)];

      if (candidate && candidate.name) {
        messages.push({
          id: `generated-${i}`,
          type: "email" as const,
          subject: subject,
          content: `This is a sample message content for ${subject.toLowerCase()}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,
          sender: type === "sent" ? "John Smith" : candidate.name,
          recipient: type === "sent" ? candidate.name : "John Smith",
          timestamp: new Date(
            Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000,
          ).toISOString(),
          status: ["sent", "delivered", "read"][
            Math.floor(Math.random() * 3)
          ] as Message["status"],
          candidateId: candidate.id,
        });
      }
    }

    return messages.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
    );
  };

  const allMessages = generateSampleMessages();

  // Filter messages by tab
  const getMessagesByTab = (tab: string) => {
    switch (tab) {
      case "sent":
        return allMessages.filter((m) => m.sender === "John Smith");
      case "drafts":
        return allMessages.filter((m) => m.status === "draft");
      case "inbox":
      default:
        return allMessages.filter((m) => m.sender !== "John Smith");
    }
  };

  // Filter messages
  const filteredMessages = useMemo(() => {
    const tabMessages = getMessagesByTab(selectedTab);
    return tabMessages.filter((message) => {
      const matchesSearch =
        message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.recipient.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.sender.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesType = filterType === "all" || message.type === filterType;
      const matchesStatus =
        filterStatus === "all" || message.status === filterStatus;

      return matchesSearch && matchesType && matchesStatus;
    });
  }, [selectedTab, searchTerm, filterType, filterStatus, allMessages]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles: MessageFile[] = Array.from(files).map((file) => ({
        id: `file-${Date.now()}-${Math.random()}`,
        name: file.name,
        size: file.size,
        type: file.type,
        url: URL.createObjectURL(file),
      }));
      setComposeForm((prev) => ({
        ...prev,
        attachments: [...prev.attachments, ...newFiles],
      }));
    }
  };

  const removeAttachment = (fileId: string) => {
    setComposeForm((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((f) => f.id !== fileId),
    }));
  };

  const applyTemplate = (template: EmailTemplate) => {
    setComposeForm((prev) => ({
      ...prev,
      subject: template.subject,
      content: template.content,
      template: template.id,
    }));
    toast.success(`Template "${template.name}" applied`);
  };

  const openComposeModal = (templateId?: string) => {
    if (templateId) {
      const template = templates.find((t) => t.id === templateId);
      if (template) {
        applyTemplate(template);
      }
    }
    setIsComposeModalOpen(true);
  };

  const handleSendMessage = () => {
    if (!composeForm.to || !composeForm.subject || !composeForm.content) {
      toast.error("Please fill in all required fields");
      return;
    }

    toast.success("Message sent successfully!");
    setIsComposeModalOpen(false);
    setComposeForm({
      to: "",
      cc: "",
      bcc: "",
      subject: "",
      content: "",
      template: "none",
      signature: "sig-1",
      priority: "normal",
      attachments: [],
    });
  };

  const handleSaveDraft = () => {
    toast.success("Draft saved");
  };

  const handleSaveTemplate = () => {
    if (!templateForm.name || !templateForm.subject || !templateForm.content) {
      toast.error("Please fill in all required fields");
      return;
    }

    const newTemplate: EmailTemplate = {
      id: `template-${Date.now()}`,
      name: templateForm.name,
      subject: templateForm.subject,
      content: templateForm.content,
      category: templateForm.category || "General",
      variables: templateForm.variables,
      isActive: templateForm.isActive,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (templateMode === "edit" && selectedTemplate) {
      setTemplates(
        templates.map((t) =>
          t.id === selectedTemplate.id ? { ...newTemplate, id: t.id } : t,
        ),
      );
      toast.success("Template updated successfully!");
    } else {
      setTemplates([...templates, newTemplate]);
      toast.success("Template created successfully!");
    }

    setIsTemplateModalOpen(false);
    setTemplateForm({
      name: "",
      subject: "",
      content: "",
      category: "",
      variables: [],
      isActive: true,
    });
  };

  const openTemplateModal = (template?: EmailTemplate) => {
    if (template) {
      setSelectedTemplate(template);
      setTemplateForm({
        name: template.name,
        subject: template.subject,
        content: template.content,
        category: template.category,
        variables: template.variables,
        isActive: template.isActive,
      });
      setTemplateMode("edit");
    } else {
      setSelectedTemplate(null);
      setTemplateForm({
        name: "",
        subject: "",
        content: "",
        category: "",
        variables: [],
        isActive: true,
      });
      setTemplateMode("create");
    }
    setIsTemplateModalOpen(true);
  };

  const deleteTemplate = (templateId: string) => {
    setTemplates(templates.filter((t) => t.id !== templateId));
    toast.success("Template deleted");
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getStatusIcon = (status: Message["status"]) => {
    switch (status) {
      case "sent":
        return <Send className="h-4 w-4 text-blue-600" />;
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "read":
        return <CheckCircle className="h-4 w-4 text-emerald-600" />;
      case "draft":
        return <FileText className="h-4 w-4 text-gray-600" />;
      default:
        return <Mail className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: Message["status"]) => {
    switch (status) {
      case "sent":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "delivered":
        return "bg-green-100 text-green-800 border-green-200";
      case "read":
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      case "draft":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Calculate stats
  const stats = {
    total: allMessages.length,
    sent: allMessages.filter((m) => m.sender === "John Smith").length,
    inbox: allMessages.filter((m) => m.sender !== "John Smith").length,
    drafts: allMessages.filter((m) => m.status === "draft").length,
    read: allMessages.filter((m) => m.status === "read").length,
    templates: templates.filter((t) => t.isActive).length,
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              {t.messages.title}
            </h1>
            <p className="text-muted-foreground">{t.messages.subtitle}</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="gap-2 rounded-xl"
              onClick={() => openTemplateModal()}
            >
              <Plus className="w-4 h-4" />
              {t.messages.newTemplate}
            </Button>
            <Button
              className="ai-button gap-2"
              onClick={() => openComposeModal()}
            >
              <Plus className="w-4 h-4" />
              {t.messages.compose}
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/20 rounded-xl">
                  <MessageSquare className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="text-lg font-bold">{stats.total}</p>
                  <p className="text-xs text-muted-foreground">Total</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-indigo-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/20 rounded-xl">
                  <Send className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">{stats.sent}</p>
                  <p className="text-xs text-muted-foreground">Sent</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-500/20 bg-gradient-to-br from-green-500/5 to-emerald-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/20 rounded-xl">
                  <Mail className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">{stats.inbox}</p>
                  <p className="text-xs text-muted-foreground">Inbox</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-gray-500/20 bg-gradient-to-br from-gray-500/5 to-slate-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gray-500/20 rounded-xl">
                  <FileText className="h-4 w-4 text-gray-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">{stats.drafts}</p>
                  <p className="text-xs text-muted-foreground">Drafts</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-emerald-500/20 bg-gradient-to-br from-emerald-500/5 to-green-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-emerald-500/20 rounded-xl">
                  <CheckCircle className="h-4 w-4 text-emerald-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">{stats.read}</p>
                  <p className="text-xs text-muted-foreground">Read</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500/20 rounded-xl">
                  <FileText className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">{stats.templates}</p>
                  <p className="text-xs text-muted-foreground">Templates</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="inbox" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger
              value="inbox"
              className="gap-2"
              onClick={() => setSelectedTab("inbox")}
            >
              <Mail className="w-4 h-4" />
              Inbox ({stats.inbox})
            </TabsTrigger>
            <TabsTrigger
              value="sent"
              className="gap-2"
              onClick={() => setSelectedTab("sent")}
            >
              <Send className="w-4 h-4" />
              Sent ({stats.sent})
            </TabsTrigger>
            <TabsTrigger
              value="drafts"
              className="gap-2"
              onClick={() => setSelectedTab("drafts")}
            >
              <FileText className="w-4 h-4" />
              Drafts ({stats.drafts})
            </TabsTrigger>
            <TabsTrigger
              value="templates"
              className="gap-2"
              onClick={() => setSelectedTab("templates")}
            >
              <FileText className="w-4 h-4" />
              Templates ({stats.templates})
            </TabsTrigger>
          </TabsList>

          {/* Message Lists */}
          <TabsContent value="inbox" className="space-y-6">
            <MessageList />
          </TabsContent>
          <TabsContent value="sent" className="space-y-6">
            <MessageList />
          </TabsContent>
          <TabsContent value="drafts" className="space-y-6">
            <MessageList />
          </TabsContent>

          {/* Templates Content */}
          <TabsContent value="templates" className="space-y-6">
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Manage your email templates for efficient communication.
              </p>
              <Button
                className="ai-button gap-2"
                onClick={() => openTemplateModal()}
              >
                <Plus className="w-4 h-4" />
                New Template
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {templates.map((template) => (
                <Card
                  key={template.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-sm">
                          {template.name}
                        </CardTitle>
                        <CardDescription className="text-xs">
                          {template.category} • Updated{" "}
                          {formatDistanceToNow(new Date(template.updatedAt), {
                            addSuffix: true,
                          })}
                        </CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => openComposeModal(template.id)}
                          >
                            <Send className="mr-2 h-4 w-4" />
                            Use Template
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => openTemplateModal(template)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Template
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              navigator.clipboard.writeText(template.content);
                              toast.success("Template copied to clipboard");
                            }}
                          >
                            <Copy className="mr-2 h-4 w-4" />
                            Copy Content
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => deleteTemplate(template.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-sm font-medium mb-1">
                      {template.subject}
                    </p>
                    <p className="text-sm text-muted-foreground line-clamp-3 mb-3">
                      {template.content.substring(0, 150)}...
                    </p>
                    <div className="flex items-center justify-between">
                      <Badge
                        variant={template.isActive ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {template.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {template.variables.length} variables
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Message Detail Modal */}
      <Dialog open={isMessageModalOpen} onOpenChange={setIsMessageModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Mail className="w-5 h-5" />
              Message Details
            </DialogTitle>
          </DialogHeader>
          {selectedMessage && (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback>
                      {selectedMessage.sender
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold">{selectedMessage.subject}</p>
                    <p className="text-sm text-muted-foreground">
                      From: {selectedMessage.sender} • To:{" "}
                      {selectedMessage.recipient}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(selectedMessage.status)}>
                    {selectedMessage.status}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {formatDistanceToNow(new Date(selectedMessage.timestamp), {
                      addSuffix: true,
                    })}
                  </span>
                </div>
              </div>
              <div className="p-4 border rounded-lg bg-muted/50">
                <pre className="whitespace-pre-wrap font-sans">
                  {selectedMessage.content}
                </pre>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" className="gap-2">
                  <Reply className="w-4 h-4" />
                  Reply
                </Button>
                <Button variant="outline" className="gap-2">
                  <Forward className="w-4 h-4" />
                  Forward
                </Button>
                <Button variant="outline" className="gap-2">
                  <Archive className="w-4 h-4" />
                  Archive
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Compose Message Modal */}
      <Dialog open={isComposeModalOpen} onOpenChange={setIsComposeModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Send className="w-5 h-5" />
              Compose Message
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {/* Recipients */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="to">To *</Label>
                <Popover open={candidateOpen} onOpenChange={setCandidateOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      className="w-full justify-between rounded-xl"
                    >
                      {composeForm.to || "Select recipient..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[400px] p-0">
                    <Command>
                      <CommandInput placeholder="Search recipients..." />
                      <CommandList>
                        <CommandEmpty>No recipient found.</CommandEmpty>
                        <CommandGroup>
                          {recipients.map((candidate) => (
                            <CommandItem
                              key={candidate.id}
                              value={candidate.email.toLowerCase()}
                              onSelect={() => {
                                setComposeForm((prev) => ({
                                  ...prev,
                                  to: candidate.email,
                                }));
                                setCandidateOpen(false);
                              }}
                            >
                              <div className="flex items-center gap-3 w-full">
                                <Avatar className="w-8 h-8">
                                  <AvatarFallback className="text-xs bg-primary/10">
                                    {candidate.initials}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex-1">
                                  <div className="font-medium">
                                    {candidate.name}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {candidate.email}
                                  </div>
                                </div>
                                <Check
                                  className={cn(
                                    "ml-2 h-4 w-4",
                                    composeForm.to === candidate.email
                                      ? "opacity-100"
                                      : "opacity-0",
                                  )}
                                />
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="cc">CC</Label>
                <Input
                  id="cc"
                  placeholder="CC recipients..."
                  value={composeForm.cc}
                  onChange={(e) =>
                    setComposeForm((prev) => ({ ...prev, cc: e.target.value }))
                  }
                  className="rounded-xl"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="bcc">BCC</Label>
                <Input
                  id="bcc"
                  placeholder="BCC recipients..."
                  value={composeForm.bcc}
                  onChange={(e) =>
                    setComposeForm((prev) => ({ ...prev, bcc: e.target.value }))
                  }
                  className="rounded-xl"
                />
              </div>
            </div>

            {/* Template and Signature */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="template">Template</Label>
                <Select
                  value={composeForm.template}
                  onValueChange={(value) => {
                    if (value === "none") {
                      setComposeForm((prev) => ({
                        ...prev,
                        template: "none",
                        subject: "",
                        content: "",
                      }));
                    } else {
                      const template = templates.find((t) => t.id === value);
                      if (template) {
                        applyTemplate(template);
                      }
                    }
                  }}
                >
                  <SelectTrigger className="rounded-xl">
                    <SelectValue placeholder="Choose template..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No template</SelectItem>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="signature">Signature</Label>
                <Select
                  value={composeForm.signature}
                  onValueChange={(value) =>
                    setComposeForm((prev) => ({ ...prev, signature: value }))
                  }
                >
                  <SelectTrigger className="rounded-xl">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {signatures.map((sig) => (
                      <SelectItem key={sig.id} value={sig.id}>
                        {sig.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Subject */}
            <div className="space-y-2">
              <Label htmlFor="subject">Subject *</Label>
              <Input
                id="subject"
                placeholder="Enter subject..."
                value={composeForm.subject}
                onChange={(e) =>
                  setComposeForm((prev) => ({
                    ...prev,
                    subject: e.target.value,
                  }))
                }
                className="rounded-xl"
              />
            </div>

            {/* Content */}
            <div className="space-y-2">
              <Label htmlFor="content">Message *</Label>
              <Textarea
                id="content"
                placeholder="Type your message..."
                value={composeForm.content}
                onChange={(e) =>
                  setComposeForm((prev) => ({
                    ...prev,
                    content: e.target.value,
                  }))
                }
                rows={10}
                className="rounded-xl resize-none"
              />
            </div>

            {/* Attachments */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Attachments</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="gap-2 rounded-xl"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Paperclip className="w-4 h-4" />
                  Add Files
                </Button>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={handleFileUpload}
              />
              {composeForm.attachments.length > 0 && (
                <div className="space-y-2">
                  {composeForm.attachments.map((file) => (
                    <div
                      key={file.id}
                      className="flex items-center justify-between p-2 border rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        {file.type.startsWith("image/") ? (
                          <Image className="w-4 h-4 text-blue-500" />
                        ) : (
                          <File className="w-4 h-4 text-gray-500" />
                        )}
                        <span className="text-sm font-medium">{file.name}</span>
                        <span className="text-xs text-muted-foreground">
                          ({formatFileSize(file.size)})
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAttachment(file.id)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-between pt-4">
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  className="gap-2 rounded-xl"
                  onClick={handleSaveDraft}
                >
                  <Save className="w-4 h-4" />
                  Save Draft
                </Button>
              </div>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsComposeModalOpen(false)}
                  className="rounded-xl"
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  className="ai-button gap-2"
                  onClick={handleSendMessage}
                >
                  <Send className="w-4 h-4" />
                  Send Message
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Template Modal */}
      <Dialog open={isTemplateModalOpen} onOpenChange={setIsTemplateModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              {templateMode === "edit" ? "Edit Template" : "Create Template"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="templateName">Template Name *</Label>
                <Input
                  id="templateName"
                  placeholder="Enter template name..."
                  value={templateForm.name}
                  onChange={(e) =>
                    setTemplateForm((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  className="rounded-xl"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="templateCategory">Category</Label>
                <Select
                  value={templateForm.category}
                  onValueChange={(value) =>
                    setTemplateForm((prev) => ({ ...prev, category: value }))
                  }
                >
                  <SelectTrigger className="rounded-xl">
                    <SelectValue placeholder="Select category..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Interview">Interview</SelectItem>
                    <SelectItem value="Application">Application</SelectItem>
                    <SelectItem value="Offer">Offer</SelectItem>
                    <SelectItem value="Follow-up">Follow-up</SelectItem>
                    <SelectItem value="Rejection">Rejection</SelectItem>
                    <SelectItem value="General">General</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="templateSubject">Subject *</Label>
              <Input
                id="templateSubject"
                placeholder="Enter subject line..."
                value={templateForm.subject}
                onChange={(e) =>
                  setTemplateForm((prev) => ({
                    ...prev,
                    subject: e.target.value,
                  }))
                }
                className="rounded-xl"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="templateContent">Content *</Label>
              <Textarea
                id="templateContent"
                placeholder="Enter template content... Use {{variableName}} for dynamic content."
                value={templateForm.content}
                onChange={(e) =>
                  setTemplateForm((prev) => ({
                    ...prev,
                    content: e.target.value,
                  }))
                }
                rows={12}
                className="rounded-xl resize-none font-mono"
              />
              <p className="text-xs text-muted-foreground">
                Use variables like {"{"}
                {"{"} candidateName {"}"}
                {"}"}, {"{"}
                {"{"} jobTitle {"}"}
                {"}"}, etc. for dynamic content
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                checked={templateForm.isActive}
                onCheckedChange={(checked) =>
                  setTemplateForm((prev) => ({ ...prev, isActive: checked }))
                }
              />
              <Label className="text-sm">Active template</Label>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsTemplateModalOpen(false)}
                className="rounded-xl"
              >
                Cancel
              </Button>
              <Button
                type="button"
                className="ai-button gap-2"
                onClick={handleSaveTemplate}
              >
                <Save className="w-4 h-4" />
                {templateMode === "edit" ? "Update" : "Create"} Template
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Layout>
  );

  // Message List Component
  function MessageList() {
    return (
      <>
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search messages..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 rounded-xl"
            />
          </div>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-[150px] rounded-xl">
              <SelectValue placeholder="All Types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="sms">SMS</SelectItem>
              <SelectItem value="note">Note</SelectItem>
            </SelectContent>
          </Select>
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-[150px] rounded-xl">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="sent">Sent</SelectItem>
              <SelectItem value="delivered">Delivered</SelectItem>
              <SelectItem value="read">Read</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" className="gap-2 rounded-xl">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
        </div>

        {/* Messages */}
        <div className="space-y-4">
          {filteredMessages.map((message) => (
            <Card
              key={message.id}
              className="hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => {
                setSelectedMessage(message);
                setIsMessageModalOpen(true);
              }}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>
                        {(selectedTab === "sent"
                          ? message.recipient
                          : message.sender
                        )
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <CardTitle className="text-sm truncate">
                          {message.subject}
                        </CardTitle>
                        <Badge className={getStatusColor(message.status)}>
                          {message.status}
                        </Badge>
                      </div>
                      <CardDescription className="text-xs">
                        {selectedTab === "sent" ? (
                          <>To: {message.recipient}</>
                        ) : (
                          <>From: {message.sender}</>
                        )}{" "}
                        •{" "}
                        {formatDistanceToNow(new Date(message.timestamp), {
                          addSuffix: true,
                        })}
                      </CardDescription>
                    </div>
                  </div>
                  <DropdownMenu onOpenChange={(e) => e && e.stopPropagation()}>
                    <DropdownMenuTrigger
                      asChild
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem>
                        <Reply className="mr-2 h-4 w-4" />
                        Reply
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Forward className="mr-2 h-4 w-4" />
                        Forward
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Star className="mr-2 h-4 w-4" />
                        Star
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Archive className="mr-2 h-4 w-4" />
                        Archive
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                  {message.content}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(message.status)}
                    <Badge variant="outline" className="text-xs">
                      {message.type}
                    </Badge>
                  </div>
                  {message.candidateId && (
                    <Badge variant="secondary" className="text-xs">
                      Candidate
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredMessages.length === 0 && (
            <Card className="p-8 text-center">
              <div className="flex flex-col items-center gap-4">
                <Mail className="w-12 h-12 text-muted-foreground" />
                <div>
                  <h3 className="text-lg font-semibold">No messages found</h3>
                  <p className="text-muted-foreground">
                    {selectedTab === "drafts"
                      ? "You don't have any draft messages."
                      : searchTerm
                        ? "Try adjusting your search terms."
                        : "Your inbox is empty."}
                  </p>
                </div>
                <Button className="gap-2" onClick={() => openComposeModal()}>
                  <Plus className="w-4 h-4" />
                  Compose Message
                </Button>
              </div>
            </Card>
          )}
        </div>
      </>
    );
  }
}
