import { useState, useMemo } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Users,
  Plus,
  Search,
  Edit,
  Trash2,
  MoreVertical,
  Crown,
  Shield,
  UserCheck,
  UserX,
  Eye,
  TrendingUp,
  Target,
  Activity,
  Download,
  Loader2,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Mail,
  Phone,
  Building,
} from "lucide-react";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";
import { useAuth } from "@/lib/auth";
import { cn, safeFormatDistanceToNow } from "@/lib/utils";

// Import user management hooks and types
import {
  useUsers,
  useUserStatistics,
  useCreateUser,
  useUpdateUser,
  useDeactivateUser,
} from "@/hooks/useUserManagement";
import {
  User,
  UserListParams,
  CreateUserData,
  UpdateUserData,
  ROLE_DISPLAY_NAMES,
  SORT_OPTIONS,
  DEPARTMENTS,
} from "@/lib/types/userManagement";

// Import components
import { UserForm, UserDetailModal } from "@/domains/users/components/team";

export default function TeamManagement() {
  const { t } = useTranslation();
  const { user: authUser } = useAuth();

  // State management
  const [userListParams, setUserListParams] = useState<UserListParams>({
    page: 1,
    per_page: 15,
    include: "roles,permissions",
  });

  const [searchTerm, setSearchTerm] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState<string>("all");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("name");
  const [view, setView] = useState<"grid" | "table">("table");

  // Modal states
  const [isUserFormOpen, setIsUserFormOpen] = useState(false);
  const [isUserDetailOpen, setIsUserDetailOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userFormMode, setUserFormMode] = useState<"create" | "edit">("create");
  const [userToDeactivate, setUserToDeactivate] = useState<User | null>(null);

  // API calls
  const {
    data: usersResponse,
    isLoading: usersLoading,
    error: usersError,
    refetch: refetchUsers,
  } = useUsers(userListParams);

  const { data: statisticsResponse, isLoading: statsLoading } =
    useUserStatistics();

  // Mutations
  const createUserMutation = useCreateUser();
  const updateUserMutation = useUpdateUser();
  const deactivateUserMutation = useDeactivateUser();

  // Check if user is admin
  const isAdmin = authUser?.role === "admin";

  // Prepare filters for API call
  const updateFilters = () => {
    const filters: any = {};

    if (searchTerm.trim()) {
      if (searchTerm.includes("@")) {
        filters.email = searchTerm.trim();
      } else {
        filters.name = searchTerm.trim();
      }
    }

    if (departmentFilter !== "all") filters.department = departmentFilter;
    if (roleFilter !== "all") filters.role = roleFilter;
    if (statusFilter !== "all") filters.is_active = statusFilter === "active";

    setUserListParams((prev) => ({
      ...prev,
      page: 1,
      filter: Object.keys(filters).length > 0 ? filters : undefined,
      sort: sortBy,
    }));
  };

  // Update filters when dependencies change
  useMemo(() => {
    const timeoutId = setTimeout(updateFilters, 300);
    return () => clearTimeout(timeoutId);
  }, [searchTerm, departmentFilter, roleFilter, statusFilter, sortBy]);

  // Extract data from API responses
  const users = usersResponse?.data || [];
  const usersMeta = usersResponse?.meta;
  const statistics = statisticsResponse?.data;

  // Get unique departments from users
  const availableDepartments = [
    ...new Set(users.map((u) => u.department).filter(Boolean)),
  ];

  // Handlers
  const handleCreateUser = () => {
    setSelectedUser(null);
    setUserFormMode("create");
    setIsUserFormOpen(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setUserFormMode("edit");
    setIsUserFormOpen(true);
  };

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsUserDetailOpen(true);
  };

  const handleUserFormSubmit = async (
    data: CreateUserData | UpdateUserData,
  ) => {
    try {
      if (userFormMode === "create") {
        await createUserMutation.mutateAsync(data as CreateUserData);
      } else if (selectedUser) {
        await updateUserMutation.mutateAsync({
          id: selectedUser.id,
          data: data as UpdateUserData,
        });
      }
      setIsUserFormOpen(false);
      setSelectedUser(null);
    } catch (error) {
      // Error handling is done in the hooks
    }
  };

  const handleToggleUserStatus = (user: User) => {
    setUserToDeactivate(user);
  };

  const confirmDeactivateUser = async () => {
    if (!userToDeactivate) return;

    try {
      await deactivateUserMutation.mutateAsync(userToDeactivate.id);
      setUserToDeactivate(null);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handlePageChange = (newPage: number) => {
    setUserListParams((prev) => ({ ...prev, page: newPage }));
  };

  const exportUserData = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      totalUsers: users.length,
      statistics,
      users,
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `users_export_${new Date().toISOString().split("T")[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Dữ liệu người dùng đã được xuất!");
  };

  // Role icon and color helpers
  const getRoleIcon = (role: string) => {
    switch (role) {
      case "admin":
        return Crown;
      case "hiring_manager":
        return Shield;
      case "recruiter":
        return Users;
      case "interviewer":
        return UserCheck;
      default:
        return Eye;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "admin":
        return "text-amber-600 bg-amber-100 border-amber-200";
      case "hiring_manager":
        return "text-blue-600 bg-blue-100 border-blue-200";
      case "recruiter":
        return "text-green-600 bg-green-100 border-green-200";
      case "interviewer":
        return "text-purple-600 bg-purple-100 border-purple-200";
      default:
        return "text-gray-600 bg-gray-100 border-gray-200";
    }
  };

  // Show loading state
  if (usersLoading && !users.length) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <p>Đang tải danh sách người dùng...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Show error state for non-admin users
  if (usersError && !isAdmin) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Shield className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h2 className="text-xl font-semibold mb-2">
              Không có quyền truy cập
            </h2>
            <p className="text-muted-foreground">
              Bạn không có quyền xem danh sách người dùng.
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              Quản lý nhóm
            </h1>
            <p className="text-muted-foreground">
              Quản lý người dùng và phân quyền trong hệ thống
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="gap-2 rounded-xl"
              onClick={() => refetchUsers()}
              disabled={usersLoading}
            >
              <RefreshCw
                className={cn("w-4 h-4", usersLoading && "animate-spin")}
              />
              Làm mới
            </Button>
            <Button
              variant="outline"
              className="gap-2 rounded-xl"
              onClick={exportUserData}
            >
              <Download className="w-4 h-4" />
              Xuất dữ liệu
            </Button>
            {isAdmin && (
              <Button className="ai-button gap-2" onClick={handleCreateUser}>
                <Plus className="w-4 h-4" />
                Thêm người dùng
              </Button>
            )}
          </div>
        </div>

        {/* Statistics */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/20 rounded-xl">
                    <Users className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {statistics.total_users}
                    </p>
                    <p className="text-xs text-muted-foreground">Tổng số</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-green-500/20 bg-gradient-to-br from-green-500/5 to-emerald-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-500/20 rounded-xl">
                    <UserCheck className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {statistics.active_users}
                    </p>
                    <p className="text-xs text-muted-foreground">Hoạt động</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-amber-500/20 bg-gradient-to-br from-amber-500/5 to-orange-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-amber-500/20 rounded-xl">
                    <Crown className="h-4 w-4 text-amber-600" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {statistics.by_role.admin}
                    </p>
                    <p className="text-xs text-muted-foreground">Quản trị</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-indigo-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-xl">
                    <Shield className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {statistics.by_role.hiring_manager}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      QL Tuyển dụng
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-green-600/20 bg-gradient-to-br from-green-600/5 to-emerald-600/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-600/20 rounded-xl">
                    <Users className="h-4 w-4 text-green-700" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {statistics.by_role.recruiter}
                    </p>
                    <p className="text-xs text-muted-foreground">Nhà TD</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500/20 rounded-xl">
                    <UserCheck className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {statistics.by_role.interviewer}
                    </p>
                    <p className="text-xs text-muted-foreground">Phỏng vấn</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <div className="flex items-center gap-4 flex-wrap">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Tìm kiếm theo tên hoặc email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 rounded-xl"
            />
          </div>

          <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
            <SelectTrigger className="w-[180px] rounded-xl">
              <SelectValue placeholder="Tất cả phòng ban" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả phòng ban</SelectItem>
              {availableDepartments.map((dept) => (
                <SelectItem key={dept} value={dept}>
                  {dept}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[150px] rounded-xl">
              <SelectValue placeholder="Tất cả vai trò" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả vai trò</SelectItem>
              {Object.entries(ROLE_DISPLAY_NAMES).map(([key, value]) => (
                <SelectItem key={key} value={key}>
                  {value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[120px] rounded-xl">
              <SelectValue placeholder="Trạng thái" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả</SelectItem>
              <SelectItem value="active">Hoạt động</SelectItem>
              <SelectItem value="inactive">Không hoạt động</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[150px] rounded-xl">
              <SelectValue placeholder="Sắp xếp" />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="text-sm text-muted-foreground">
            {usersMeta
              ? `${usersMeta.from}-${usersMeta.to} của ${usersMeta.total} người dùng`
              : `${users.length} người dùng`}
          </div>
        </div>

        {/* Users Table */}
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Người dùng</TableHead>
                <TableHead>Vai trò</TableHead>
                <TableHead>Phòng ban</TableHead>
                <TableHead>Liên hệ</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Đăng nhập cuối</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => {
                const RoleIcon = getRoleIcon(user.role);
                const canEdit = isAdmin || user.id === authUser?.id;
                const canDeactivate = isAdmin && user.id !== authUser?.id;

                return (
                  <TableRow key={user.id} className="hover:bg-muted/50">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={user.avatar} alt={user.name} />
                          <AvatarFallback className="bg-primary/10 text-primary text-sm">
                            {user.initials}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {user.title || "Không có chức vụ"}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        className={cn("text-xs", getRoleColor(user.role))}
                        variant="outline"
                      >
                        <RoleIcon className="w-3 h-3 mr-1" />
                        {user.role_display_name ||
                          ROLE_DISPLAY_NAMES[
                            user.role as keyof typeof ROLE_DISPLAY_NAMES
                          ]}
                      </Badge>
                    </TableCell>
                    <TableCell>{user.department || "Không có"}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="w-3 h-3 text-muted-foreground" />
                          <span className="truncate max-w-[200px]">
                            {user.email}
                          </span>
                        </div>
                        {user.phone && (
                          <div className="flex items-center gap-2 text-sm">
                            <Phone className="w-3 h-3 text-muted-foreground" />
                            <span>{user.phone}</span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={user.is_active ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {user.is_active ? "Hoạt động" : "Không hoạt động"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {user.last_login_at
                          ? safeFormatDistanceToNow(user.last_login_at, {
                              addSuffix: true,
                            })
                          : "Chưa đăng nhập"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewUser(user)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            Xem chi tiết
                          </DropdownMenuItem>
                          {canEdit && (
                            <DropdownMenuItem
                              onClick={() => handleEditUser(user)}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Chỉnh sửa
                            </DropdownMenuItem>
                          )}
                          {canDeactivate && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleToggleUserStatus(user)}
                                className={
                                  user.is_active
                                    ? "text-orange-600"
                                    : "text-green-600"
                                }
                              >
                                {user.is_active ? (
                                  <>
                                    <UserX className="mr-2 h-4 w-4" />
                                    Vô hiệu hóa
                                  </>
                                ) : (
                                  <>
                                    <UserCheck className="mr-2 h-4 w-4" />
                                    Kích hoạt
                                  </>
                                )}
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>

          {/* Pagination */}
          {usersMeta && usersMeta.last_page > 1 && (
            <div className="flex items-center justify-between px-4 py-3 border-t">
              <div className="text-sm text-muted-foreground">
                Trang {usersMeta.current_page} của {usersMeta.last_page}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(usersMeta.current_page - 1)}
                  disabled={usersMeta.current_page <= 1 || usersLoading}
                >
                  <ChevronLeft className="w-4 h-4" />
                  Trước
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(usersMeta.current_page + 1)}
                  disabled={
                    usersMeta.current_page >= usersMeta.last_page ||
                    usersLoading
                  }
                >
                  Sau
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </Card>

        {/* User Form Modal */}
        <UserForm
          isOpen={isUserFormOpen}
          onClose={() => setIsUserFormOpen(false)}
          onSubmit={handleUserFormSubmit}
          user={selectedUser}
          mode={userFormMode}
          isLoading={
            createUserMutation.isPending || updateUserMutation.isPending
          }
          currentUser={authUser}
        />

        {/* User Detail Modal */}
        <UserDetailModal
          isOpen={isUserDetailOpen}
          onClose={() => setIsUserDetailOpen(false)}
          user={selectedUser}
          onEdit={handleEditUser}
          onToggleStatus={handleToggleUserStatus}
        />

        {/* Deactivate Confirmation Dialog */}
        <AlertDialog
          open={!!userToDeactivate}
          onOpenChange={() => setUserToDeactivate(null)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {userToDeactivate?.is_active ? "Vô hiệu hóa" : "Kích hoạt"}{" "}
                người dùng
              </AlertDialogTitle>
              <AlertDialogDescription>
                {userToDeactivate?.is_active
                  ? `Bạn có chắc chắn muốn vô hiệu hóa tài khoản của ${userToDeactivate?.name}? Họ sẽ không thể đăng nhập vào hệ thống.`
                  : `Bạn có chắc chắn muốn kích hoạt tài khoản của ${userToDeactivate?.name}? Họ sẽ có thể đăng nhập vào hệ thống.`}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Hủy</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDeactivateUser}
                disabled={deactivateUserMutation.isPending}
                className={
                  userToDeactivate?.is_active
                    ? "bg-destructive hover:bg-destructive/90"
                    : ""
                }
              >
                {deactivateUserMutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                {userToDeactivate?.is_active ? "Vô hiệu hóa" : "Kích hoạt"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </Layout>
  );
}
