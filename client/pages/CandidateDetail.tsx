import React, { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import {
  CandidateDetailContent,
  CandidateFormModal,
} from "@/domains/candidates/components";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AlertTriangle,
  ArrowLeft,
  RefreshCw,
  Loader2,
  User,
  Edit,
} from "lucide-react";
import { toast } from "sonner";
import { useCandidate, useCandidateActions } from "@/domains/candidates/hooks";
import { Candidate, UpdateCandidateData } from "@/domains/candidates/types";
import { LoadingSpinner } from "@/shared/components/feedback";

export default function CandidateDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Use the single candidate hook
  const { candidate, isLoading, error, refetch } = useCandidate(id || "", {
    enabled: !!id,
  });

  const candidateActions = useCandidateActions();

  const handleRetry = () => {
    refetch();
  };

  const handleStatusChange = async (candidateId: string, newStatus: string) => {
    try {
      await candidateActions.updateStatus.mutateAsync({
        candidateId,
        status: newStatus as any,
      });
      // Success message is handled by the mutation
    } catch (error) {
      // Error message is handled by the mutation
    }
  };

  const handleEdit = (candidateToEdit: Candidate) => {
    setIsEditModalOpen(true);
  };

  const handleEditSubmit = async (data: UpdateCandidateData) => {
    if (!candidate) return;

    try {
      // This would use a candidate update mutation
      toast.success("Candidate updated successfully");
      setIsEditModalOpen(false);
      refetch();
    } catch (error) {
      toast.error("Failed to update candidate");
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <Layout>
        <div className="p-6 max-w-6xl mx-auto space-y-6">
          {/* Header skeleton */}
          <div className="flex items-center gap-4">
            <Skeleton className="w-10 h-10" />
            <div className="flex items-center gap-4">
              <Skeleton className="h-16 w-16 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-48" />
                <div className="flex gap-2">
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-16" />
                </div>
              </div>
            </div>
          </div>

          {/* Content skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          </div>
        </div>
      </Layout>
    );
  }

  // Error state
  if (error) {
    return (
      <Layout>
        <div className="p-6 max-w-4xl mx-auto">
          <div className="flex items-center gap-2 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </Button>
          </div>

          <Alert variant="destructive" className="max-w-md mx-auto">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>
                {error instanceof Error
                  ? error.message
                  : "Failed to load candidate details"}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetry}
                className="ml-4"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>

          <div className="text-center mt-8">
            <User className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-2xl font-semibold mb-2">Candidate Not Found</h2>
            <p className="text-muted-foreground mb-6">
              The candidate you're looking for doesn't exist or may have been
              removed.
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="outline" onClick={() => navigate("/candidates")}>
                View All Candidates
              </Button>
              <Button onClick={handleRetry}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Success state
  if (!candidate) {
    return null;
  }

  return (
    <Layout>
      <CandidateDetailContent
        candidate={candidate}
        onStatusChange={handleStatusChange}
        onEdit={handleEdit}
        showBackButton={true}
        isFullPage={true}
      />

      {/* Edit Modal */}
      <CandidateFormModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        candidate={candidate}
        onSubmit={handleEditSubmit}
        loading={candidateActions.isLoading}
      />
    </Layout>
  );
}
