import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/lib/auth";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Zap,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Building,
  Users,
  Globe,
  Shield,
  ArrowRight,
  Check,
  Star,
  Sparkles,
  Brain,
  Target,
  TrendingUp,
  Rocket,
  Award,
  ChevronRight,
  Facebook,
} from "lucide-react";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";

interface Organization {
  id: string;
  name: string;
  domain: string;
  logo?: string;
  plan: "free" | "pro" | "enterprise";
  size: string;
  industry: string;
}

const mockOrganizations: Organization[] = [
  {
    id: "org-1",
    name: "HireFlow Technologies",
    domain: "hireflow.com",
    plan: "enterprise",
    size: "51-200 employees",
    industry: "Technology",
  },
  {
    id: "org-2",
    name: "Acme Corporation",
    domain: "acme.com",
    plan: "pro",
    size: "11-50 employees",
    industry: "Manufacturing",
  },
  {
    id: "org-3",
    name: "StartupCo",
    domain: "startup.co",
    plan: "free",
    size: "1-10 employees",
    industry: "Technology",
  },
];

export default function Login() {
  const { t, language } = useTranslation();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [loginMethod, setLoginMethod] = useState<
    "email" | "google" | "facebook" | null
  >(null);

  const { login } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setLoginMethod("email");

    try {
      // Use real API authentication
      await login(email, password);

      toast({
        title:
          language === "vi" ? "Đăng nhập thành công!" : "Login successful!",
        description:
          language === "vi"
            ? "Chào mừng bạn trở lại HireFlow"
            : "Welcome back to HireFlow",
      });

      // Redirect to dashboard
      navigate("/dashboard");
    } catch (error: any) {
      toast({
        title: language === "vi" ? "Đăng nhập thất bại" : "Login failed",
        description:
          error.message ||
          (language === "vi"
            ? "Email hoặc mật khẩu không đúng"
            : "Invalid email or password"),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setLoginMethod(null);
    }
  };

  const handleSocialLogin = async (provider: "google" | "facebook") => {
    setIsLoading(true);
    setLoginMethod(provider);

    try {
      // Simulate OAuth flow
      toast.info(
        language === "vi"
          ? `Đang chuyển hướng đến ${provider === "google" ? "Google" : "Facebook"}...`
          : `Redirecting to ${provider}...`,
      );

      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mock successful social login
      const mockUser = {
        email: `user@${provider}.com`,
        name: provider === "google" ? "Google User" : "Facebook User",
        loginMethod: provider,
        loginTime: new Date().toISOString(),
        avatar: `https://ui-avatars.com/api/?name=${provider}&background=22C55E&color=fff`,
      };

      localStorage.setItem("hireflow_user", JSON.stringify(mockUser));

      toast.success(
        language === "vi"
          ? `Đăng nhập ${provider === "google" ? "Google" : "Facebook"} thành công!`
          : `${provider} login successful!`,
      );

      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 1000);
    } catch (error) {
      toast.error(
        language === "vi"
          ? "Đăng nhập thất bại. Vui lòng thử lại."
          : "Login failed. Please try again.",
      );
    } finally {
      setIsLoading(false);
      setLoginMethod(null);
    }
  };

  const getPlanBadge = (plan: string) => {
    switch (plan) {
      case "enterprise":
        return (
          <Badge className="bg-purple-100 text-purple-800 border-purple-200">
            Enterprise
          </Badge>
        );
      case "pro":
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            Professional
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200">
            Free
          </Badge>
        );
    }
  };

  const features = [
    {
      icon: Brain,
      title: language === "vi" ? "Sàng lọc AI" : "AI-Powered Screening",
      desc:
        language === "vi"
          ? "Ghép nối ứng viên thông minh và sàng lọc tự động"
          : "Intelligent candidate matching and automated screening",
    },
    {
      icon: Target,
      title: language === "vi" ? "Phân tích Thông minh" : "Smart Analytics",
      desc:
        language === "vi"
          ? "Thông tin dựa trên dữ liệu để tối ưu hóa quy trình tuyển dụng"
          : "Data-driven insights to optimize your hiring process",
    },
    {
      icon: TrendingUp,
      title: language === "vi" ? "Theo dõi Hiệu suất" : "Performance Tracking",
      desc:
        language === "vi"
          ? "Giám sát số liệu tuyển dụng và hiệu suất đội ngũ"
          : "Monitor hiring metrics and team performance",
    },
    {
      icon: Rocket,
      title: language === "vi" ? "Triển khai Nhanh" : "Fast Deployment",
      desc:
        language === "vi"
          ? "Bắt đầu trong vài phút với thiết lập trực quan"
          : "Get started in minutes with our intuitive setup",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-emerald-500/5 flex items-center justify-center p-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none" />

      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* Left Side - Branding & Features */}
        <div className="space-y-8">
          {/* Logo & Branding */}
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center shadow-lg animate-pulse-green">
                <Zap className="w-6 h-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-foreground">HireFlow</h1>
                <p className="text-muted-foreground font-medium">
                  AI-Powered Recruiting Platform
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <h2 className="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                {language === "vi" ? "Cách mạng hóa" : "Transform Your"}
                <span className="block bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent">
                  {language === "vi"
                    ? "Quy trình Tuyển dụng"
                    : "Hiring Process"}
                </span>
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {language === "vi"
                  ? "Tham gia cùng hàng nghìn công ty sử dụng HireFlow để tìm kiếm, đánh giá và tuyển dụng nhân tài tốt nhất một cách nhanh chóng với các công cụ tuyển dụng được hỗ trợ bởi AI."
                  : "Join thousands of companies using HireFlow to find, evaluate, and hire the best talent faster with AI-powered recruiting tools."}
              </p>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div
                  key={index}
                  className="p-4 rounded-xl border bg-card/50 backdrop-blur-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold mb-1">{feature.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {feature.desc}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Social Proof */}
          <div className="flex items-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="flex -space-x-2">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="w-8 h-8 rounded-full bg-primary/10 border-2 border-background flex items-center justify-center"
                  >
                    <Users className="w-4 h-4 text-primary" />
                  </div>
                ))}
              </div>
              <span>Trusted by 10,000+ companies</span>
            </div>
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
              <span>4.9/5 rating</span>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="w-full max-w-md mx-auto">
          <Card className="border-primary/20 shadow-xl backdrop-blur-sm">
            <CardHeader className="space-y-1 pb-6">
              <CardTitle className="text-2xl font-bold text-center">
                {language === "vi" ? "Chào mừng trở lại" : "Welcome back"}
              </CardTitle>
              <p className="text-muted-foreground text-center">
                {language === "vi"
                  ? "Đăng nhập vào tài khoản của bạn để tiếp tục"
                  : "Sign in to your account to continue"}
              </p>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="login" className="space-y-6">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="login">
                    {language === "vi" ? "Đăng nhập" : "Sign In"}
                  </TabsTrigger>
                  <TabsTrigger value="organization">
                    {language === "vi" ? "Tổ chức" : "Organization"}
                  </TabsTrigger>
                </TabsList>

                {/* Login Tab */}
                <TabsContent value="login" className="space-y-6">
                  <form onSubmit={handleLogin} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">
                        {language === "vi" ? "Email" : "Email"}
                      </Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                        <Input
                          id="email"
                          type="email"
                          placeholder={
                            language === "vi"
                              ? "<EMAIL>"
                              : "<EMAIL>"
                          }
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="pl-10 rounded-xl"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password">
                        {language === "vi" ? "Mật khẩu" : "Password"}
                      </Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                        <Input
                          id="password"
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          className="pl-10 pr-10 rounded-xl"
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="remember"
                          checked={rememberMe}
                          onCheckedChange={setRememberMe}
                        />
                        <Label htmlFor="remember" className="text-sm">
                          {language === "vi"
                            ? "Ghi nhớ đăng nhập"
                            : "Remember me"}
                        </Label>
                      </div>
                      <Button variant="link" className="px-0 text-sm">
                        {language === "vi"
                          ? "Quên mật khẩu?"
                          : "Forgot password?"}
                      </Button>
                    </div>

                    <Button
                      type="submit"
                      className="w-full ai-button"
                      disabled={isLoading && loginMethod === "email"}
                    >
                      {isLoading && loginMethod === "email" ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <>
                          {language === "vi" ? "Đăng nhập" : "Sign In"}
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </>
                      )}
                    </Button>
                  </form>

                  <div className="space-y-4">
                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <Separator />
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-background px-2 text-muted-foreground">
                          {language === "vi"
                            ? "Hoặc tiếp tục với"
                            : "Or continue with"}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <Button
                        variant="outline"
                        onClick={() => handleSocialLogin("google")}
                        className="rounded-xl"
                        disabled={isLoading && loginMethod === "google"}
                      >
                        {isLoading && loginMethod === "google" ? (
                          <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2" />
                        ) : (
                          <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                            <path
                              fill="currentColor"
                              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                            />
                            <path
                              fill="currentColor"
                              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                            />
                            <path
                              fill="currentColor"
                              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                            />
                            <path
                              fill="currentColor"
                              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                            />
                          </svg>
                        )}
                        Google
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleSocialLogin("facebook")}
                        className="rounded-xl"
                        disabled={isLoading && loginMethod === "facebook"}
                      >
                        {isLoading && loginMethod === "facebook" ? (
                          <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2" />
                        ) : (
                          <svg
                            className="w-4 h-4 mr-2"
                            viewBox="0 0 24 24"
                            fill="#1877F2"
                          >
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                          </svg>
                        )}
                        Facebook
                      </Button>
                    </div>
                  </div>

                  <div className="text-center text-sm text-muted-foreground">
                    {language === "vi"
                      ? "Chưa có tài khoản?"
                      : "Don't have an account?"}{" "}
                    <Button variant="link" className="px-0">
                      {language === "vi"
                        ? "Đăng ký miễn phí"
                        : "Sign up for free"}
                    </Button>
                  </div>
                </TabsContent>

                {/* Organization Tab */}
                <TabsContent value="organization" className="space-y-6">
                  <div className="space-y-4">
                    <div className="text-center space-y-2">
                      <Building className="w-12 h-12 text-primary mx-auto" />
                      <h3 className="font-semibold">
                        Select Your Organization
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Choose the organization you want to access
                      </p>
                    </div>

                    <div className="space-y-3">
                      {mockOrganizations.map((org) => (
                        <Card
                          key={org.id}
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            selectedOrg === org.id
                              ? "ring-2 ring-primary border-primary"
                              : ""
                          }`}
                          onClick={() => setSelectedOrg(org.id)}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                                  <Building className="w-5 h-5 text-primary" />
                                </div>
                                <div>
                                  <div className="font-semibold">
                                    {org.name}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {org.domain}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                {getPlanBadge(org.plan)}
                                {selectedOrg === org.id && (
                                  <Check className="w-5 h-5 text-primary" />
                                )}
                              </div>
                            </div>
                            <div className="mt-3 flex items-center gap-4 text-xs text-muted-foreground">
                              <span>{org.size}</span>
                              <span>{org.industry}</span>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    <Button
                      className="w-full ai-button"
                      disabled={!selectedOrg}
                      onClick={() => {
                        if (selectedOrg) {
                          toast.success("Organization selected!");
                          // Switch to login tab or redirect
                        }
                      }}
                    >
                      Continue to Login
                      <ChevronRight className="w-4 h-4 ml-2" />
                    </Button>

                    <div className="text-center">
                      <Button variant="link" className="text-sm">
                        Don't see your organization? Contact support
                      </Button>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center text-xs text-muted-foreground mt-6">
            <p>
              By signing in, you agree to our{" "}
              <Button variant="link" className="px-0 text-xs h-auto">
                Terms of Service
              </Button>{" "}
              and{" "}
              <Button variant="link" className="px-0 text-xs h-auto">
                Privacy Policy
              </Button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
