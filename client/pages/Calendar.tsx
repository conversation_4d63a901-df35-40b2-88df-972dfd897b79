import { useState, useMemo, useEffect } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { MonthlyCalendar } from "@/domains/interviews/components/calendar";
import { EnhancedInterviewDetailModal } from "@/domains/interviews/components/calendar";
import { EditInterviewModal } from "@/domains/interviews/components/calendar";
import {
  Calendar as CalendarIcon,
  Plus,
  Clock,
  Video,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  Download,
  Zap,
  Loader2,
  RefreshCw,
} from "lucide-react";
import { Interview } from "@/domains/candidates/types";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";
import {
  useInterviews,
  useScheduleInterview,
  useUpdateInterviewStatus,
} from "@/domains/candidates/hooks";
import { useQueryClient } from "@tanstack/react-query";

// Convert API interview data to Calendar format with enhanced data
const convertApiInterviewToCalendarFormat = (apiInterview: any): Interview => {
  console.log("=== CONVERSION DEBUG ===");
  console.log("Raw API Interview:", JSON.stringify(apiInterview, null, 2));
  console.log("Key fields:");
  console.log("- ID:", apiInterview.id);
  console.log("- scheduled_at:", apiInterview.scheduled_at);
  console.log("- date:", apiInterview.date);
  console.log("- status:", apiInterview.status);
  console.log("- candidate:", apiInterview.candidate);
  console.log("- job_posting:", apiInterview.job_posting);
  const converted = {
    id: apiInterview.id?.toString() || `interview-${Date.now()}`,
    candidateId: apiInterview.candidate_id?.toString() || "",
    candidateName:
      apiInterview.candidate?.name ||
      apiInterview.candidate_name ||
      "Unknown Candidate",
    candidateEmail:
      apiInterview.candidate?.email || apiInterview.candidate_email || "",
    candidatePhone:
      apiInterview.candidate?.phone || apiInterview.candidate_phone || "",
    candidatePosition:
      apiInterview.candidate?.position || apiInterview.candidate_position || "",
    candidateRating:
      apiInterview.candidate?.rating || apiInterview.candidate_rating || 0,
    candidateAiScore:
      apiInterview.candidate?.ai_score || apiInterview.candidate_ai_score || 0,
    jobId: apiInterview.job_posting_id?.toString() || "",
    jobTitle:
      apiInterview.job_posting?.title ||
      apiInterview.job_title ||
      "Unknown Position",
    jobDepartment:
      apiInterview.job_posting?.department || apiInterview.job_department || "",
    jobLocation:
      apiInterview.job_posting?.location || apiInterview.job_location || "",
    jobSalary: apiInterview.job_posting?.salary_range || "",
    jobPriority:
      apiInterview.job_posting?.priority ||
      apiInterview.job_priority ||
      "medium",
    date: apiInterview.scheduled_at
      ? apiInterview.scheduled_at.split("T")[0]
      : apiInterview.date || new Date().toISOString().split("T")[0],
    time: apiInterview.scheduled_at
      ? new Date(apiInterview.scheduled_at).toLocaleTimeString("en-GB", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        })
      : apiInterview.time || "09:00",
    duration: apiInterview.duration || 60,
    type: mapInterviewType(apiInterview.type),
    interviewer:
      apiInterview.interviewer?.name ||
      apiInterview.interviewer?.user?.name ||
      apiInterview.interviewer_name ||
      "Unknown Interviewer",
    interviewerId: apiInterview.interviewer_id?.toString() || "",
    interviewerDepartment:
      apiInterview.interviewer?.department ||
      apiInterview.interviewer?.user?.department ||
      "",
    interviewerExpertise: Array.isArray(apiInterview.interviewer?.expertise)
      ? apiInterview.interviewer.expertise
      : [],
    status: mapInterviewStatus(apiInterview.status),
    location: apiInterview.location || "",
    notes: apiInterview.notes || "",
    meetingLink: apiInterview.meeting_link || "",
    meetingPassword: apiInterview.meeting_password || "",
    agenda: Array.isArray(apiInterview.agenda) ? apiInterview.agenda : [],
    feedback: apiInterview.feedback || "",
    round: apiInterview.round || 1,
    interviewType: apiInterview.interview_type || "technical",
    reminderSent: apiInterview.reminder_sent || false,
    apiData: apiInterview, // Keep original API data for reference
  };
  console.log("Converted interview:", converted); // Debug log

  // Validate critical fields
  if (!converted.date) {
    console.error("❌ Converted interview missing date field:", converted);
  }
  if (!converted.id) {
    console.error("❌ Converted interview missing id field:", converted);
  }

  console.log("Final converted date:", converted.date);
  return converted;
};

const mapInterviewType = (apiType: string): Interview["type"] => {
  switch (apiType?.toLowerCase()) {
    case "video":
    case "online":
      return "video";
    case "phone":
    case "call":
      return "phone";
    case "onsite":
    case "in-person":
    case "offline":
      return "in-person";
    default:
      return "video";
  }
};

const mapInterviewStatus = (apiStatus: string): Interview["status"] => {
  switch (apiStatus?.toLowerCase()) {
    case "scheduled":
      return "scheduled";
    case "completed":
    case "finished":
      return "completed";
    case "cancelled":
    case "canceled":
      return "cancelled";
    case "rescheduled":
    case "postponed":
      return "rescheduled";
    case "in_progress":
    case "ongoing":
      return "scheduled"; // Treat as scheduled for calendar purposes
    default:
      return "scheduled";
  }
};

export default function Calendar() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterInterviewer, setFilterInterviewer] = useState<string>("all");
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(
    null,
  );
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isInterviewModalOpen, setIsInterviewModalOpen] = useState(false);
  const [editingInterview, setEditingInterview] = useState<Interview | null>(
    null,
  );

  // Calculate date range for API query (current month +/- 1 month for better view)
  const dateRange = useMemo(() => {
    const startDate = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth() - 1,
      1,
    );
    const endDate = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth() + 2,
      0,
    );
    return {
      date_from: startDate.toISOString().split("T")[0],
      date_to: endDate.toISOString().split("T")[0],
    };
  }, [selectedDate]);

  // Debug auth token
  const authToken =
    typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
  console.log("Auth token exists:", !!authToken); // Debug log

  // API Hooks with enhanced parameters
  const {
    data: interviewsResponse,
    isLoading: isLoadingInterviews,
    error: interviewsError,
    refetch: refetchInterviews,
  } = useInterviews({
    per_page: 200, // Get more interviews for calendar view
    sort: "date", // Use the correct field from Laravel API
    date_from: dateRange.date_from,
    date_to: dateRange.date_to,
    // Add status filter if needed
    ...(filterStatus !== "all" && { status: filterStatus }),
    // Add type filter if needed
    ...(filterType !== "all" && { type: filterType }),
  });

  const scheduleInterviewMutation = useScheduleInterview();
  const updateInterviewStatusMutation = useUpdateInterviewStatus();

  //console.error("Schedule Interview không lấy được danh sách ứng viên, danh sách interviewer từ api")
  // Debug API state
  console.log(
    "API Loading:",
    isLoadingInterviews,
    "Error:",
    interviewsError,
    "Response:",
    interviewsResponse,
  );

  // Debug API URL and parameters
  console.log("API Parameters:", {
    per_page: 200,
    sort: "scheduled_at",
    date_from: dateRange.date_from,
    date_to: dateRange.date_to,
    status: filterStatus !== "all" ? filterStatus : undefined,
    type: filterType !== "all" ? filterType : undefined,
  });

  // Update interviews when API data changes
  useEffect(() => {
    console.log("=== API Response Debug ===");
    console.log("Full Response:", interviewsResponse);
    console.log("Response type:", typeof interviewsResponse);
    console.log(
      "Response keys:",
      interviewsResponse ? Object.keys(interviewsResponse) : "none",
    );

    // Handle ApiResponse<T> format from this.request method
    if (
      interviewsResponse?.status === "success" &&
      interviewsResponse?.data &&
      Array.isArray(interviewsResponse.data)
    ) {
      console.log(
        "✅ Found successful API response with",
        interviewsResponse.data.length,
        "items",
      );
      console.log("Sample item:", interviewsResponse.data[0]);
      const convertedInterviews = interviewsResponse.data.map(
        convertApiInterviewToCalendarFormat,
      );
      console.log("✅ Converted interviews:", convertedInterviews);
      console.log(
        "Setting interviews state with",
        convertedInterviews.length,
        "items",
      );
      setInterviews(convertedInterviews);

      // Verify state was set
      setTimeout(() => {
        console.log(
          "State verification - interviews in state:",
          interviews.length,
        );
      }, 100);
    }
    // Fallback for paginated response format
    else if (
      interviewsResponse?.data &&
      Array.isArray(interviewsResponse.data)
    ) {
      console.log(
        "✅ Found data array with",
        interviewsResponse.data.length,
        "items",
      );
      console.log("Sample item:", interviewsResponse.data[0]);
      const convertedInterviews = interviewsResponse.data.map(
        convertApiInterviewToCalendarFormat,
      );
      console.log("✅ Converted interviews:", convertedInterviews);
      setInterviews(convertedInterviews);
    }
    // Legacy format fallback
    else if (
      interviewsResponse?.interviews &&
      Array.isArray(interviewsResponse.interviews)
    ) {
      console.log(
        "✅ Found interviews array with",
        interviewsResponse.interviews.length,
        "items",
      );
      const convertedInterviews = interviewsResponse.interviews.map(
        convertApiInterviewToCalendarFormat,
      );
      console.log("✅ Converted interviews (fallback):", convertedInterviews);
      setInterviews(convertedInterviews);
    } else if (interviewsResponse) {
      console.log(
        "❌ API response received but no data/interviews array found",
      );
      console.log(
        "Response structure:",
        JSON.stringify(interviewsResponse, null, 2),
      );
      setInterviews([]);
    } else {
      console.log("❌ No API response yet");
    }
    console.log("=== End Debug ===");
  }, [interviewsResponse]);

  // Log API errors for debugging
  useEffect(() => {
    if (interviewsError) {
      console.error("API error:", interviewsError);
    }
  }, [interviewsError]);

  // Filter interviews based on current filters
  const filteredInterviews = useMemo(() => {
    console.log("=== FILTERING DEBUG ===");
    console.log("Input interviews:", interviews.length);
    console.log("Filter settings:", {
      filterType,
      filterStatus,
      filterInterviewer,
    });

    const filtered = interviews.filter((interview) => {
      const matchesType = filterType === "all" || interview.type === filterType;
      const matchesStatus =
        filterStatus === "all" || interview.status === filterStatus;
      const matchesInterviewer =
        filterInterviewer === "all" ||
        interview.interviewer === filterInterviewer;

      const passes = matchesType && matchesStatus && matchesInterviewer;
      console.log(
        `Interview ${interview.id}: type=${interview.type} status=${interview.status} interviewer=${interview.interviewer} passes=${passes}`,
      );

      return passes;
    });

    console.log("Filtered result:", filtered.length, "interviews");
    console.log("=== END FILTERING ===");
    return filtered;
  }, [interviews, filterType, filterStatus, filterInterviewer]);

  // Calculate calendar statistics
  const calendarStats = useMemo(() => {
    const currentMonth = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth(),
      1,
    );
    const nextMonth = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth() + 1,
      1,
    );

    const monthInterviews = filteredInterviews.filter((interview) => {
      const interviewDate = new Date(interview.date);
      return interviewDate >= currentMonth && interviewDate < nextMonth;
    });

    const today = new Date().toISOString().split("T")[0];
    const todayInterviews = filteredInterviews.filter(
      (interview) => interview.date === today,
    );

    const statusCounts = monthInterviews.reduce(
      (acc, interview) => {
        acc[interview.status] = (acc[interview.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const typeCounts = monthInterviews.reduce(
      (acc, interview) => {
        acc[interview.type] = (acc[interview.type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    return {
      total: monthInterviews.length,
      today: todayInterviews.length,
      scheduled: statusCounts.scheduled || 0,
      completed: statusCounts.completed || 0,
      cancelled: statusCounts.cancelled || 0,
      rescheduled: statusCounts.rescheduled || 0,
      video: typeCounts.video || 0,
      phone: typeCounts.phone || 0,
      inPerson: typeCounts["in-person"] || 0,
      completionRate:
        monthInterviews.length > 0
          ? (
              ((statusCounts.completed || 0) / monthInterviews.length) *
              100
            ).toFixed(1)
          : "0",
    };
  }, [filteredInterviews, selectedDate]);

  const handleInterviewClick = (interview: Interview) => {
    setSelectedInterview(interview);
    setIsDetailModalOpen(true);
  };

  const handleInterviewEdit = (interview: Interview) => {
    setEditingInterview(interview);
    setIsInterviewModalOpen(true);
    setIsDetailModalOpen(false);
  };

  const handleEditSuccess = () => {
    // Refresh interviews after successful edit with force refetch
    queryClient.invalidateQueries({ queryKey: ["interviews"] });
    queryClient.invalidateQueries({ queryKey: ["calendar-events"] });

    // Also trigger a manual refetch to ensure data is updated
    refetchInterviews();
  };

  const handleStatusChange = async (
    interviewId: string,
    newStatus: Interview["status"],
  ) => {
    try {
      await updateInterviewStatusMutation.mutateAsync({
        id: interviewId,
        status: newStatus,
      });

      // Update local state immediately for better UX
      setInterviews(
        interviews.map((interview) =>
          interview.id === interviewId
            ? { ...interview, status: newStatus }
            : interview,
        ),
      );
    } catch (error) {
      console.error("Error updating interview status:", error);
      toast.error("Có lỗi xảy ra khi cập nhật trạng thái phỏng vấn");
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "scheduled":
        return "Đã lên lịch";
      case "completed":
        return "Đã hoàn thành";
      case "cancelled":
        return "Đã hủy";
      case "rescheduled":
        return "Đã dời lịch";
      default:
        return status;
    }
  };


  const exportCalendarData = () => {
    const calendarData = {
      month: selectedDate.toLocaleDateString("vi-VN", {
        month: "long",
        year: "numeric",
      }),
      exportDate: new Date().toISOString(),
      statistics: calendarStats,
      interviews: filteredInterviews,
      totalInterviews: interviews.length,
      apiSource: "HireFlow ATS API v2.0.1",
    };

    const blob = new Blob([JSON.stringify(calendarData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `hireflow_calendar_${selectedDate.getFullYear()}_${selectedDate.getMonth() + 1}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Đã xuất dữ liệu lịch thành công");
  };

  // Loading state
  if (isLoadingInterviews && interviews.length === 0) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-96">
          <div className="flex items-center gap-2">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Đang tải lịch phỏng vấn...</span>
          </div>
        </div>
      </Layout>
    );
  }

  // Error state (only show if no data and there's an error)
  if (interviewsError && interviews.length === 0) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Có lỗi xảy ra</h3>
            <p className="text-muted-foreground mb-4">
              Không thể tải dữ liệu lịch phỏng vấn
            </p>
            <Button onClick={() => refetchInterviews()}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Thử lại
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              {t.calendar?.title || "Lịch Phỏng Vấn"}
            </h1>
            <p className="text-muted-foreground">
              {t.calendar?.subtitle || "Quản lý và theo dõi lịch phỏng vấn"}
            </p>
            {/* Status indicator */}
            <div className="text-sm text-muted-foreground mt-1">
              {isLoadingInterviews && "🔄 Đang tải dữ liệu từ API..."}
              {!isLoadingInterviews &&
                !interviewsError &&
                interviews.length > 0 &&
                "✅ Đã tải thành công " + interviews.length + " cuộc phỏng vấn"}
              {!isLoadingInterviews &&
                interviewsError &&
                "❌ Lỗi API: " + (interviewsError?.message || "Không xác định")}
              {!isLoadingInterviews &&
                !interviewsError &&
                interviews.length === 0 &&
                interviewsResponse &&
                "ℹ️ API trả về thành công nhưng không có dữ li��u phỏng vấn"}
              {!isLoadingInterviews &&
                !interviewsError &&
                interviews.length === 0 &&
                !interviewsResponse &&
                "⏳ Chưa có phản hồi từ API"}
            </div>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="outline">{interviews.length} phỏng vấn</Badge>
              <Badge
                variant={
                  localStorage.getItem("auth_token") ? "default" : "destructive"
                }
                className="text-xs"
              >
                Auth: {localStorage.getItem("auth_token") ? "OK" : "Missing"}
              </Badge>
              {isLoadingInterviews && (
                <Badge variant="secondary">
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  Đang c���p nhật...
                </Badge>
              )}
              {interviewsError && (
                <Badge variant="destructive">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Lỗi: {interviewsError?.message || "Lỗi kết nối API"}
                </Badge>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="gap-2 rounded-xl"
              onClick={exportCalendarData}
            >
              <Download className="w-4 h-4" />
              {t.common?.export || "Xuất dữ liệu"}
            </Button>
            <Button
              variant="outline"
              className="gap-2 rounded-xl"
              onClick={() => refetchInterviews()}
              disabled={isLoadingInterviews}
            >
              {isLoadingInterviews ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              Làm mới
            </Button>

            <Button
              className="ai-button gap-2"
              onClick={() => {
                setEditingInterview(null); // Clear any existing interview for create mode
                setIsInterviewModalOpen(true);
              }}
              disabled={scheduleInterviewMutation.isPending}
            >
              {scheduleInterviewMutation.isPending ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Plus className="w-4 h-4" />
              )}
              {t.calendar?.scheduleInterview || "Lên lịch phỏng vấn"}
            </Button>
          </div>
        </div>

        {/* Enhanced Statistics Dashboard */}
        {calendarStats && (
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/20 rounded-xl">
                    <CalendarIcon className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {calendarStats.total || 0}
                    </p>
                    <p className="text-xs text-muted-foreground">Tháng này</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-indigo-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-xl">
                    <Clock className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {calendarStats.today || 0}
                    </p>
                    <p className="text-xs text-muted-foreground">Hôm nay</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-emerald-500/20 bg-gradient-to-br from-emerald-500/5 to-green-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-emerald-500/20 rounded-xl">
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {calendarStats.completed || 0}
                    </p>
                    <p className="text-xs text-muted-foreground">Hoàn thành</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500/20 rounded-xl">
                    <Video className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {calendarStats.video || 0}
                    </p>
                    <p className="text-xs text-muted-foreground">Video</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-orange-500/20 bg-gradient-to-br from-orange-500/5 to-red-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-500/20 rounded-xl">
                    <BarChart3 className="h-4 w-4 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {calendarStats.completionRate || 0}%
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Tỷ lệ hoàn thành
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-indigo-500/20 bg-gradient-to-br from-indigo-500/5 to-purple-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-indigo-500/20 rounded-xl">
                    <Zap className="h-4 w-4 text-indigo-600" />
                  </div>
                  <div>
                    <p className="text-lg font-bold">
                      {calendarStats.cancelled || 0}
                    </p>
                    <p className="text-xs text-muted-foreground">Đã hủy</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Enhanced Filters */}
        <div className="flex items-center gap-4 flex-wrap">
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-[150px] rounded-xl">
              <SelectValue placeholder="Tất cả loại" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="all">Tất cả loại</SelectItem>
              <SelectItem value="video">Video</SelectItem>
              <SelectItem value="phone">Điện thoại</SelectItem>
              <SelectItem value="in-person">Trực tiếp</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-[150px] rounded-xl">
              <SelectValue placeholder="Tất cả trạng thái" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="all">Tất cả trạng thái</SelectItem>
              <SelectItem value="scheduled">Đã lên lịch</SelectItem>
              <SelectItem value="completed">Hoàn thành</SelectItem>
              <SelectItem value="cancelled">Đã hủy</SelectItem>
              <SelectItem value="rescheduled">Dời lịch</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filterInterviewer}
            onValueChange={setFilterInterviewer}
          >
            <SelectTrigger className="w-[180px] rounded-xl">
              <SelectValue placeholder="Tất cả người phỏng vấn" />
            </SelectTrigger>
            <SelectContent className="rounded-xl max-h-60">
              <SelectItem value="all">Tất cả người phỏng vấn</SelectItem>
              {Array.from(new Set(interviews.map((i) => i.interviewer)))
                .filter(Boolean)
                .sort()
                .map((interviewer) => (
                  <SelectItem key={interviewer} value={interviewer}>
                    {interviewer}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>

          <div className="flex items-center gap-4 text-sm text-muted-foreground ml-auto">
            <span>
              Hiển thị {filteredInterviews.length} / {interviews.length} phỏng
              vấn
            </span>
            {(interviewsResponse?.data || interviewsResponse?.interviews) && (
              <Badge variant="outline" className="text-xs">
                API:{" "}
                {
                  (
                    interviewsResponse.data ||
                    interviewsResponse.interviews ||
                    []
                  ).length
                }{" "}
                records
              </Badge>
            )}
            {(calendarStats.today || 0) > 0 && (
              <Badge
                variant="default"
                className="text-xs bg-primary/10 text-primary"
              >
                {calendarStats.today || 0} hôm nay
              </Badge>
            )}
          </div>
        </div>

        {/* Monthly Calendar */}
        <MonthlyCalendar
          interviews={filteredInterviews}
          selectedDate={selectedDate}
          onDateChange={setSelectedDate}
          onInterviewClick={handleInterviewClick}
        />

        {/* Enhanced Interview Detail Modal */}
        <EnhancedInterviewDetailModal
          interview={selectedInterview}
          isOpen={isDetailModalOpen}
          onClose={() => {
            setIsDetailModalOpen(false);
            setSelectedInterview(null);
          }}
          onEdit={handleInterviewEdit}
          onStatusChange={handleStatusChange}
        />

        {/* Unified Interview Modal - handles both create and edit */}
        <EditInterviewModal
          isOpen={isInterviewModalOpen}
          onClose={() => {
            setIsInterviewModalOpen(false);
            setEditingInterview(null);
          }}
          interview={editingInterview}
          onSuccess={handleEditSuccess}
          selectedDate={selectedDate}
        />
      </div>
    </Layout>
  );
}
