/**
 * Modern Calendar Page
 * Uses new domain components and hooks
 */

import { useState, useMemo } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Calendar as CalendarIcon,
  Plus,
  Clock,
  Video,
  CheckCircle,
  AlertTriangle,
  Download,
  Loader2,
  RefreshCw,
} from "lucide-react";
import { toast } from "sonner";

// New domain imports
import { CalendarView } from "@/domains/calendar/components";
import { InterviewModal } from "@/domains/interviews/components";
import {
  useInterviews,
  useUpcomingInterviews,
  useUpdateInterview,
  useDeleteInterview,
} from "@/domains/interviews/hooks";
import {
  CalendarEvent,
  CalendarFilters,
  CalendarViewType,
} from "@/domains/calendar/types";
import { Interview } from "@/domains/interviews/types";

// Convert interview to calendar event
const convertInterviewToCalendarEvent = (
  interview: Interview,
): CalendarEvent => {
  const primaryInterviewer =
    interview.interviewers?.find((i) => i.isPrimary) ||
    interview.interviewers?.[0];

  return {
    id: interview.id,
    title: `Interview: ${interview.candidateName}`,
    description: `${interview.type} interview for ${interview.jobTitle}`,
    type: "interview",
    status:
      interview.status === "completed"
        ? "completed"
        : interview.status === "cancelled"
          ? "cancelled"
          : "scheduled",
    priority: "medium", // Default priority since Interview doesn't have priority
    startDate: interview.scheduledAt,
    endDate: new Date(
      new Date(interview.scheduledAt).getTime() +
        (interview.duration || 60) * 60000,
    ).toISOString(),
    allDay: false,
    timezone: interview.timezone || "UTC",
    location: interview.location,
    isVirtual: interview.type === "video",
    meetingUrl: interview.meetingUrl,
    organizerId: primaryInterviewer?.userId,
    organizerName: primaryInterviewer?.name,
    organizerEmail: primaryInterviewer?.email,
    attendees: [
      {
        name: interview.candidateName,
        email: interview.candidateEmail || "",
        role: "required",
        status: "pending",
      },
      ...(interview.interviewers?.map((interviewer) => ({
        name: interviewer.name,
        email: interviewer.email,
        role: interviewer.isPrimary
          ? ("organizer" as const)
          : ("optional" as const),
        status: interviewer.confirmed
          ? ("accepted" as const)
          : ("pending" as const),
      })) || []),
    ],
    interviewId: interview.id,
    candidateId: interview.candidateId,
    jobId: interview.jobId,
    color: getEventColor(interview.type, interview.status),
    tags: [interview.type, interview.round || "general"],
    isRecurring: false,
    reminders: [],
    createdAt: interview.createdAt,
    updatedAt: interview.updatedAt,
  };
};

// Get event color based on type and status
const getEventColor = (type: string, status: string): string => {
  if (status === "cancelled") return "#ef4444";
  if (status === "completed") return "#10b981";

  const colors: Record<string, string> = {
    phone: "#3b82f6",
    video: "#10b981",
    "in-person": "#f59e0b",
  };
  return colors[type] || "#6b7280";
};

export default function Calendar() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewType, setViewType] = useState<CalendarViewType>("month");
  const [filters, setFilters] = useState<CalendarFilters>({});

  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(
    null,
  );
  const [showInterviewModal, setShowInterviewModal] = useState(false);

  // Calculate date range for current view
  const dateRange = useMemo(() => {
    const start = new Date(selectedDate);
    const end = new Date(selectedDate);

    switch (viewType) {
      case "month":
        start.setDate(1);
        end.setMonth(end.getMonth() + 1, 0);
        break;
      case "week":
        start.setDate(start.getDate() - start.getDay());
        end.setDate(start.getDate() + 6);
        break;
      case "day":
        end.setDate(end.getDate());
        break;
      default:
        start.setDate(1);
        end.setMonth(end.getMonth() + 1, 0);
    }

    return {
      start_date: start.toISOString().split("T")[0],
      end_date: end.toISOString().split("T")[0],
    };
  }, [selectedDate, viewType]);

  // Fetch interviews for calendar
  const {
    data: interviewsResponse,
    isLoading: isLoadingCalendar,
    error: calendarError,
    refetch: refetchCalendar,
  } = useInterviews({
    dateFrom: dateRange.start_date,
    dateTo: dateRange.end_date,
  });

  // Fetch upcoming interviews for stats
  const { data: upcomingResponse } = useUpcomingInterviews({ limit: 10 });

  // Mutations
  const updateInterviewMutation = useUpdateInterview();
  const deleteInterviewMutation = useDeleteInterview();

  // Convert interviews to calendar events
  const calendarEvents = useMemo(() => {
    if (interviewsResponse?.data) {
      return interviewsResponse.data.map(convertInterviewToCalendarEvent);
    }
    return [];
  }, [interviewsResponse]);

  // Handle event interactions
  const handleEventClick = (event: CalendarEvent) => {
    // If it's an interview event, find the original interview
    if (event.interviewId && interviewsResponse?.data) {
      const interview = interviewsResponse.data.find(
        (i) => i.id === event.interviewId,
      );
      if (interview) {
        setSelectedInterview(interview);
        setShowInterviewModal(true);
      }
    }
  };

  const handleEventCreate = (_date: Date) => {
    // For now, just show a toast - could open a create interview modal
    toast.info("Create interview functionality coming soon");
  };

  const handleEventUpdate = (event: CalendarEvent) => {
    // Handle event updates
    if (event.interviewId) {
      // Update interview
      updateInterviewMutation.mutate(
        {
          id: event.interviewId,
          data: {
            scheduledAt: event.startDate,
            location: event.location,
            meetingUrl: event.meetingUrl,
          },
        },
        {
          onSuccess: () => {
            toast.success("Interview updated successfully");
            refetchCalendar();
          },
          onError: () => {
            toast.error("Failed to update interview");
          },
        },
      );
    }
  };

  const handleEventDelete = (eventId: string) => {
    const event = calendarEvents.find((e) => e.id === eventId);
    if (event?.interviewId) {
      deleteInterviewMutation.mutate(event.interviewId, {
        onSuccess: () => {
          toast.success("Interview deleted successfully");
          refetchCalendar();
        },
        onError: () => {
          toast.error("Failed to delete interview");
        },
      });
    }
  };

  // Loading state
  if (isLoadingCalendar) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading calendar...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Error state
  if (calendarError) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">Failed to load calendar</p>
            <Button onClick={() => refetchCalendar()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Calendar</h1>
            <p className="text-muted-foreground">
              Manage your interview schedule and appointments
            </p>
          </div>

          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New Interview
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Today's Interviews
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {
                  calendarEvents.filter((e) => {
                    const eventDate = new Date(e.startDate);
                    const today = new Date();
                    return eventDate.toDateString() === today.toDateString();
                  }).length
                }
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Week</CardTitle>
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {upcomingResponse?.data?.length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Video Calls</CardTitle>
              <Video className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {calendarEvents.filter((e) => e.isVirtual).length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {calendarEvents.filter((e) => e.status === "completed").length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Calendar View */}
        <CalendarView
          events={calendarEvents}
          loading={isLoadingCalendar}
          viewType={viewType}
          selectedDate={selectedDate}
          filters={filters}
          onViewTypeChange={setViewType}
          onDateChange={setSelectedDate}
          onFiltersChange={setFilters}
          onEventClick={handleEventClick}
          onEventCreate={handleEventCreate}
          onEventUpdate={handleEventUpdate}
          onEventDelete={handleEventDelete}
          showCreateButton={true}
          showFilters={true}
          showExport={true}
        />

        {/* Interview Modal */}
        {selectedInterview && (
          <InterviewModal
            interview={selectedInterview}
            isOpen={showInterviewModal}
            onClose={() => {
              setShowInterviewModal(false);
              setSelectedInterview(null);
            }}
            onEdit={(_interview) => {
              // Handle edit
              toast.info("Edit functionality coming soon");
            }}
            onDelete={(interviewId) => {
              handleEventDelete(interviewId);
              setShowInterviewModal(false);
              setSelectedInterview(null);
            }}
            onStatusChange={(_interviewId, status) => {
              // For now, just show a toast since UpdateInterviewData doesn't have status
              toast.info(
                `Status change to ${status} functionality coming soon`,
              );
              setShowInterviewModal(false);
              setSelectedInterview(null);
            }}
          />
        )}
      </div>
    </Layout>
  );
}
