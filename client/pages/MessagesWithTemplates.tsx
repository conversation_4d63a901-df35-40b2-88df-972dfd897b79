import { useState, useEffect } from "react";
import { Layout } from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  MessageSquare,
  Plus,
  Send,
  Mail,
  FileText,
  Settings,
  CheckCircle,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";

// Import the new components
import {
  MessageTemplateList,
  MessageTemplateForm,
  MessageList,
  MessageDetail,
  MessageForm,
} from "@/components/messages";

// Import services
import {
  Message,
  messageService,
  CreateMessageData,
  MessageStatistics,
} from "@/lib/services/messageService";
import {
  MessageTemplate,
  messageTemplateService,
} from "@/lib/services/messageTemplateService";

export default function MessagesWithTemplates() {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState<
    "inbox" | "sent" | "drafts" | "templates"
  >("inbox");

  // Modal states
  const [isMessageDetailOpen, setIsMessageDetailOpen] = useState(false);
  const [isComposeModalOpen, setIsComposeModalOpen] = useState(false);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);

  // Selected items
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [selectedTemplate, setSelectedTemplate] =
    useState<MessageTemplate | null>(null);
  const [templateMode, setTemplateMode] = useState<"create" | "edit">("create");

  // Statistics
  const [stats, setStats] = useState({
    total: 0,
    sent: 0,
    inbox: 0,
    drafts: 0,
    read: 0,
    templates: 0,
  });
  const [loadingStats, setLoadingStats] = useState(true);
  const [apiAvailable, setApiAvailable] = useState(true);

  // Load statistics on component mount
  useEffect(() => {
    loadStatistics();
  }, []);

  const loadStatistics = async () => {
    try {
      setLoadingStats(true);
      let anyApiSuccess = false;

      // Load message statistics with error handling
      try {
        const messageStatsResponse =
          await messageService.getMessageStatistics();
        if (messageStatsResponse.status === "success") {
          anyApiSuccess = true;
          const messageStats = messageStatsResponse.data;
          setStats((prev) => ({
            ...prev,
            total: messageStats.total_messages,
            sent: messageStats.by_status.sent || 0,
            inbox:
              messageStats.total_messages - (messageStats.by_status.sent || 0),
            drafts: messageStats.by_status.draft || 0,
            read: messageStats.by_status.read || 0,
          }));
        }
      } catch (messageStatsError) {
        console.warn(
          "Message statistics API not available, using fallback:",
          messageStatsError,
        );
        setApiAvailable(false);

        // Check for specific database timestamp error
        if (
          messageStatsError instanceof Error &&
          messageStatsError.message.includes("diffInSeconds")
        ) {
          console.warn(
            "Backend database has null timestamp issue - this is a backend data integrity problem",
          );
        }

        // Set fallback statistics when API is not available
        setStats((prev) => ({
          ...prev,
          total: 0,
          sent: 0,
          inbox: 0,
          drafts: 0,
          read: 0,
        }));
      }

      // Load template count with error handling
      try {
        const templatesResponse = await messageTemplateService.getTemplates({
          per_page: 1,
        });
        if (templatesResponse.meta) {
          anyApiSuccess = true;
          setStats((prev) => ({
            ...prev,
            templates: templatesResponse.meta.total,
          }));
        }
      } catch (templateStatsError) {
        console.warn(
          "Template statistics API not available, using fallback:",
          templateStatsError,
        );
        // Set fallback template count when API is not available
        setStats((prev) => ({
          ...prev,
          templates: 0,
        }));
      }
    } catch (err) {
      console.error("Error loading statistics:", err);
      // Set all fallback values in case of complete failure
      setStats({
        total: 0,
        sent: 0,
        inbox: 0,
        drafts: 0,
        read: 0,
        templates: 0,
      });
    } finally {
      setLoadingStats(false);
      setApiAvailable(anyApiSuccess);
    }
  };

  const handleMessageSelect = (message: Message) => {
    setSelectedMessage(message);
    setIsMessageDetailOpen(true);
  };

  const handleComposeClick = (template?: MessageTemplate) => {
    setSelectedTemplate(template || null);
    setIsComposeModalOpen(true);
  };

  const handleTemplateCreate = () => {
    setSelectedTemplate(null);
    setTemplateMode("create");
    setIsTemplateModalOpen(true);
  };

  const handleTemplateEdit = (template: MessageTemplate) => {
    setSelectedTemplate(template);
    setTemplateMode("edit");
    setIsTemplateModalOpen(true);
  };

  const handleTemplateSelect = (template: MessageTemplate) => {
    handleComposeClick(template);
  };

  const handleTemplatePreview = (template: MessageTemplate) => {
    // Template preview is handled within MessageTemplateList
    console.log("Preview template:", template);
  };

  const handleTemplateSave = (template: MessageTemplate) => {
    toast.success(
      templateMode === "edit"
        ? "Template đã được cập nhật thành công!"
        : "Template đã được tạo thành công!",
    );
    setIsTemplateModalOpen(false);
    loadStatistics(); // Reload stats to update template count
  };

  const handleMessageSend = async (messageData: CreateMessageData) => {
    try {
      const response = await messageService.sendMessage(messageData);
      if (response.status === "success") {
        toast.success("Tin nhắn đã được gửi thành công!");
        setIsComposeModalOpen(false);
        loadStatistics(); // Reload stats
      }
    } catch (err) {
      console.error("Error sending message:", err);
      toast.error("Không thể gửi tin nhắn. Vui lòng thử lại.");
    }
  };

  const handleReplyClick = (message: Message) => {
    setSelectedMessage(message);
    setIsMessageDetailOpen(false);
    setIsComposeModalOpen(true);
  };

  const handleForwardClick = (message: Message) => {
    setSelectedMessage(message);
    setIsMessageDetailOpen(false);
    setIsComposeModalOpen(true);
  };

  const handleEditClick = (message: Message) => {
    setSelectedMessage(message);
    setIsMessageDetailOpen(false);
    setIsComposeModalOpen(true);
  };

  const handleMessageUpdate = () => {
    loadStatistics();
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              Hệ thống tin nhắn
            </h1>
            <p className="text-muted-foreground">
              Quản lý tin nhắn và template tuyển dụng với API integration
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="gap-2 rounded-xl"
              onClick={handleTemplateCreate}
            >
              <Plus className="w-4 h-4" />
              Template mới
            </Button>
            <Button
              className="ai-button gap-2"
              onClick={() => handleComposeClick()}
            >
              <Plus className="w-4 h-4" />
              Soạn tin nhắn
            </Button>
          </div>
        </div>

        {/* API Warning */}
        {!apiAvailable && (
          <Alert variant="default" className="border-orange-200 bg-orange-50">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Backend API không khả dụng hoặc có lỗi cơ sở dữ liệu. Đang hiển
              thị dữ liệu demo. Vui lòng kiểm tra backend server và database.
            </AlertDescription>
          </Alert>
        )}

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/20 rounded-xl">
                  <MessageSquare className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="text-lg font-bold">
                    {loadingStats ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      stats.total
                    )}
                  </p>
                  <p className="text-xs text-muted-foreground">Tổng số</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-indigo-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/20 rounded-xl">
                  <Send className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">
                    {loadingStats ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      stats.sent
                    )}
                  </p>
                  <p className="text-xs text-muted-foreground">Đã gửi</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-500/20 bg-gradient-to-br from-green-500/5 to-emerald-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/20 rounded-xl">
                  <Mail className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">
                    {loadingStats ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      stats.inbox
                    )}
                  </p>
                  <p className="text-xs text-muted-foreground">Hộp thư</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-gray-500/20 bg-gradient-to-br from-gray-500/5 to-slate-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gray-500/20 rounded-xl">
                  <FileText className="h-4 w-4 text-gray-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">
                    {loadingStats ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      stats.drafts
                    )}
                  </p>
                  <p className="text-xs text-muted-foreground">Bản nháp</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500/20 rounded-xl">
                  <FileText className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">
                    {loadingStats ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      stats.templates
                    )}
                  </p>
                  <p className="text-xs text-muted-foreground">Templates</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs
          value={selectedTab}
          onValueChange={(value: any) => setSelectedTab(value)}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="inbox" className="gap-2">
              <Mail className="w-4 h-4" />
              Hộp thư ({stats.inbox})
            </TabsTrigger>
            <TabsTrigger value="sent" className="gap-2">
              <Send className="w-4 h-4" />
              Đã gửi ({stats.sent})
            </TabsTrigger>
            <TabsTrigger value="drafts" className="gap-2">
              <FileText className="w-4 h-4" />
              Bản nháp ({stats.drafts})
            </TabsTrigger>
            <TabsTrigger value="templates" className="gap-2">
              <Settings className="w-4 h-4" />
              Templates ({stats.templates})
            </TabsTrigger>
          </TabsList>

          {/* Message Lists */}
          <TabsContent value="inbox">
            <MessageList
              type="inbox"
              onMessageSelect={handleMessageSelect}
              onComposeClick={() => handleComposeClick()}
              onReplyClick={handleReplyClick}
              onForwardClick={handleForwardClick}
              showActions={true}
            />
          </TabsContent>

          <TabsContent value="sent">
            <MessageList
              type="sent"
              onMessageSelect={handleMessageSelect}
              onComposeClick={() => handleComposeClick()}
              onReplyClick={handleReplyClick}
              onForwardClick={handleForwardClick}
              showActions={true}
            />
          </TabsContent>

          <TabsContent value="drafts">
            <MessageList
              type="drafts"
              onMessageSelect={handleMessageSelect}
              onComposeClick={() => handleComposeClick()}
              onReplyClick={handleReplyClick}
              onForwardClick={handleForwardClick}
              showActions={true}
            />
          </TabsContent>

          {/* Templates Content */}
          <TabsContent value="templates">
            <MessageTemplateList
              onTemplateSelect={handleTemplateSelect}
              onTemplateEdit={handleTemplateEdit}
              onTemplateCreate={handleTemplateCreate}
              onTemplatePreview={handleTemplatePreview}
              showActions={true}
              showCreateButton={true}
              selectable={false}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Message Detail Modal */}
      {selectedMessage && (
        <MessageDetail
          message={selectedMessage}
          isOpen={isMessageDetailOpen}
          onClose={() => {
            setIsMessageDetailOpen(false);
            setSelectedMessage(null);
          }}
          onReply={handleReplyClick}
          onForward={handleForwardClick}
          onEdit={handleEditClick}
          onUpdate={handleMessageUpdate}
          showThread={true}
        />
      )}

      {/* Compose Message Modal */}
      <MessageForm
        isOpen={isComposeModalOpen}
        onClose={() => {
          setIsComposeModalOpen(false);
          setSelectedMessage(null);
          setSelectedTemplate(null);
        }}
        onSend={handleMessageSend}
        mode={
          selectedMessage
            ? selectedMessage.status === "draft"
              ? "edit"
              : selectedMessage.subject?.startsWith("Fwd:")
                ? "forward"
                : "reply"
            : "compose"
        }
        replyToMessage={
          selectedMessage &&
          selectedMessage.status !== "draft" &&
          !selectedMessage.subject?.startsWith("Fwd:")
            ? selectedMessage
            : undefined
        }
        forwardMessage={
          selectedMessage &&
          selectedMessage.status !== "draft" &&
          selectedMessage.subject?.startsWith("Fwd:")
            ? selectedMessage
            : undefined
        }
        editMessage={
          selectedMessage && selectedMessage.status === "draft"
            ? selectedMessage
            : undefined
        }
        selectedTemplate={selectedTemplate || undefined}
      />

      {/* Template Form Modal */}
      <MessageTemplateForm
        isOpen={isTemplateModalOpen}
        onClose={() => {
          setIsTemplateModalOpen(false);
          setSelectedTemplate(null);
        }}
        template={selectedTemplate}
        mode={templateMode}
        onSave={handleTemplateSave}
      />
    </Layout>
  );
}
