/**
 * Candidates Page
 * Modern implementation using domain-driven architecture
 */

import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { ProtectedRoute } from "@/lib/auth";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Users,
  UserCheck,
  UserX,
  Clock,
} from "lucide-react";

// Domain imports
import {
  useCandidates,
  useCandidateActions,
  useCandidateForm,
} from "@/domains/candidates/hooks";
import {
  CandidateFormModal,
  CandidateDetailModal,
  BulkActionsBar,
  AdvancedFilters,
  ImportCandidatesModal,
  AIScoreBadge,
} from "@/domains/candidates/components";
import {
  Candidate,
  CandidateSearchFilters,
  CandidateStatus,
  CandidateListItem,
} from "@/domains/candidates/types";

// Shared components
import { DataTable } from "@/shared/components/data-display";
import { PageLayout } from "@/shared/components/layout";
import { LoadingSpinner } from "@/shared/components/feedback";

// Toast notifications
import { toast } from "sonner";

export default function CandidatesPage() {
  const navigate = useNavigate();

  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(
    null,
  );
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);

  // Filters state
  const [filters, setFilters] = useState<CandidateSearchFilters>({});
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Build search filters
  const searchFilters = useMemo(
    () => ({
      ...filters,
      query: searchQuery || undefined,
    }),
    [filters, searchQuery],
  );

  // Hooks
  const {
    data: candidatesData,
    isLoading,
    error,
    refetch,
  } = useCandidates(searchFilters, {
    page: currentPage,
    limit: pageSize,
  });

  const candidateActions = useCandidateActions();

  // Table columns configuration
  const columns = useMemo(
    () => [
      {
        id: "select",
        header: ({ table }: any) => (
          <input
            type="checkbox"
            checked={table.getIsAllPageRowsSelected()}
            onChange={(e) => {
              table.toggleAllPageRowsSelected(e.target.checked);
              if (e.target.checked) {
                setSelectedCandidates(
                  candidatesData?.candidates.map((c) => c.id) || [],
                );
              } else {
                setSelectedCandidates([]);
              }
            }}
          />
        ),
        cell: ({ row }: any) => (
          <input
            type="checkbox"
            checked={selectedCandidates.includes(row.original.id)}
            onChange={(e) => {
              if (e.target.checked) {
                setSelectedCandidates((prev) => [...prev, row.original.id]);
              } else {
                setSelectedCandidates((prev) =>
                  prev.filter((id) => id !== row.original.id),
                );
              }
            }}
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "name",
        header: "Candidate",
        cell: ({ row }: any) => {
          const candidate = row.original as CandidateListItem;
          return (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                {candidate.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </div>
              <div>
                <div className="font-medium">{candidate.name}</div>
                <div className="text-sm text-muted-foreground">
                  {candidate.email}
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: "position",
        header: "Position",
        cell: ({ row }: any) => {
          const candidate = row.original as CandidateListItem;
          return (
            <div>
              <div className="font-medium">{candidate.position}</div>
              <div className="text-sm text-muted-foreground">
                {candidate.experience} years exp.
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }: any) => {
          const candidate = row.original as CandidateListItem;
          const statusColors = {
            sourced: "bg-gray-100 text-gray-800",
            applied: "bg-blue-100 text-blue-800",
            screening: "bg-yellow-100 text-yellow-800",
            interview: "bg-purple-100 text-purple-800",
            offer: "bg-orange-100 text-orange-800",
            hired: "bg-green-100 text-green-800",
            rejected: "bg-red-100 text-red-800",
          };
          return (
            <Badge
              className={statusColors[candidate.status] || statusColors.sourced}
            >
              {candidate.status}
            </Badge>
          );
        },
      },
      {
        accessorKey: "aiScore",
        header: "AI Score",
        cell: ({ row }: any) => {
          const candidate = row.original as CandidateListItem;
          return candidate.aiScore ? (
            <AIScoreBadge candidate={candidate as any} size="sm" />
          ) : (
            <span className="text-muted-foreground">-</span>
          );
        },
      },
      {
        accessorKey: "appliedAt",
        header: "Applied",
        cell: ({ row }: any) => {
          const candidate = row.original as CandidateListItem;
          return candidate.appliedAt ? (
            <span className="text-sm">
              {new Date(candidate.appliedAt).toLocaleDateString()}
            </span>
          ) : (
            <span className="text-muted-foreground">-</span>
          );
        },
      },
      {
        id: "actions",
        header: "Actions",
        cell: ({ row }: any) => {
          const candidate = row.original as CandidateListItem;
          return (
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleViewCandidate(candidate.id)}
              >
                View
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/candidates/${candidate.id}`)}
              >
                Details
              </Button>
            </div>
          );
        },
        enableSorting: false,
      },
    ],
    [selectedCandidates, candidatesData, navigate],
  );

  // Event handlers
  const handleViewCandidate = async (candidateId: string) => {
    // In a real app, you'd fetch the full candidate data
    // For now, we'll use the list item data
    const candidate = candidatesData?.candidates.find(
      (c) => c.id === candidateId,
    );
    if (candidate) {
      setSelectedCandidate(candidate as any);
      setIsDetailModalOpen(true);
    }
  };

  const handleCreateCandidate = async (data: any) => {
    try {
      // This would use the candidateActions.createCandidate mutation
      // For now, we'll just show a success message
      toast.success("Candidate created successfully");
      setIsCreateModalOpen(false);
      refetch();
    } catch (error) {
      toast.error("Failed to create candidate");
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedCandidates.length === 0) {
      toast.error("Please select candidates first");
      return;
    }

    try {
      await candidateActions.bulkOperation.mutateAsync({
        operation: action as any,
        candidateIds: selectedCandidates,
        data: {},
      });
      setSelectedCandidates([]);
    } catch (error) {
      toast.error("Bulk operation failed");
    }
  };

  const handleFiltersChange = (newFilters: Partial<CandidateSearchFilters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleImport = async (importedCandidates: any[]) => {
    try {
      // Handle candidate import
      toast.success(`Imported ${importedCandidates.length} candidates`);
      setIsImportModalOpen(false);
      refetch();
    } catch (error) {
      toast.error("Failed to import candidates");
    }
  };

  // Statistics
  const stats = useMemo(() => {
    if (!candidatesData) return null;

    const { candidates } = candidatesData;
    const total = candidates.length;
    const active = candidates.filter(
      (c) => !["rejected", "hired"].includes(c.status),
    ).length;
    const inInterview = candidates.filter(
      (c) => c.status === "interview",
    ).length;
    const hired = candidates.filter((c) => c.status === "hired").length;

    return { total, active, inInterview, hired };
  }, [candidatesData]);

  if (error) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-600 mb-4">Failed to load candidates</p>
              <Button onClick={() => refetch()}>Try Again</Button>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout>
        <PageLayout
          title="Candidates"
          description="Manage and track your candidate pipeline"
        >
          {/* Statistics Cards */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Candidates
                  </CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.total}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.active}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    In Interview
                  </CardTitle>
                  <UserCheck className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.inInterview}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Hired</CardTitle>
                  <UserCheck className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.hired}</div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Actions Bar */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search candidates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-80"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => setIsImportModalOpen(true)}
              >
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button onClick={() => setIsCreateModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Candidate
              </Button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="mb-6">
              <AdvancedFilters
                onFiltersChange={handleFiltersChange}
                activeFiltersCount={Object.keys(filters).length}
              />
            </div>
          )}

          {/* Bulk Actions Bar */}
          <BulkActionsBar
            selectedCount={selectedCandidates.length}
            onClearSelection={() => setSelectedCandidates([])}
            onBulkAction={handleBulkAction}
          />

          {/* Data Table */}
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <LoadingSpinner />
            </div>
          ) : (
            <DataTable
              data={candidatesData?.candidates || []}
              columns={columns}
              pagination={{
                page: currentPage,
                pageSize,
                total: candidatesData?.total || 0,
                onPageChange: setCurrentPage,
                onPageSizeChange: setPageSize,
              }}
              loading={isLoading}
              onRowClick={(candidate) => handleViewCandidate(candidate.id)}
            />
          )}

          {/* Modals */}
          <CandidateFormModal
            isOpen={isCreateModalOpen}
            onClose={() => setIsCreateModalOpen(false)}
            onSubmit={handleCreateCandidate}
            loading={candidateActions.isLoading}
          />

          <ImportCandidatesModal
            isOpen={isImportModalOpen}
            onClose={() => setIsImportModalOpen(false)}
            onImport={handleImport}
          />

          {selectedCandidate && (
            <CandidateDetailModal
              isOpen={isDetailModalOpen}
              onClose={() => {
                setIsDetailModalOpen(false);
                setSelectedCandidate(null);
              }}
              candidate={selectedCandidate}
            />
          )}
        </PageLayout>
      </Layout>
    </ProtectedRoute>
  );
}
