/**
 * Modern Interviews Page
 * Uses new domain components and hooks
 */

import { useState, useMemo } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Calendar as CalendarIcon,
  Plus,
  Clock,
  Video,
  CheckCircle,
  AlertTriangle,
  Users,
  Filter,
  Download,
  Loader2,
  RefreshCw,
  Table,
  Grid,
} from "lucide-react";
import { toast } from "sonner";

// New domain imports
import { 
  InterviewCard, 
  InterviewTable, 
  InterviewModal, 
  InterviewFilters 
} from "@/domains/interviews/components";
import { 
  useInterviews,
  useInterviewStatistics,
  useCreateInterview,
  useUpdateInterview,
  useDeleteInterview,
  useCancelInterview,
  useCompleteInterview,
} from "@/domains/interviews/hooks";
import { 
  Interview, 
  InterviewFilters as IInterviewFilters,
  InterviewQueryParams 
} from "@/domains/interviews/types";

type ViewMode = 'cards' | 'table';

export default function Interviews() {
  const [viewMode, setViewMode] = useState<ViewMode>('cards');
  const [filters, setFilters] = useState<IInterviewFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(null);
  const [showInterviewModal, setShowInterviewModal] = useState(false);

  // Build query params from filters
  const queryParams = useMemo((): InterviewQueryParams => {
    return {
      search: filters.search,
      dateFrom: filters.dateFrom,
      dateTo: filters.dateTo,
      status: filters.status,
      type: filters.type,
      round: filters.round,
      interviewerId: filters.interviewerId,
      page: 1,
      limit: 50,
      sort: 'scheduled_at',
    };
  }, [filters]);

  // Fetch interviews
  const { 
    data: interviewsResponse, 
    isLoading: isLoadingInterviews,
    error: interviewsError,
    refetch: refetchInterviews 
  } = useInterviews(queryParams);

  // Fetch statistics
  const { 
    data: statsResponse,
    isLoading: isLoadingStats 
  } = useInterviewStatistics(filters);

  // Mutations
  const createInterviewMutation = useCreateInterview();
  const updateInterviewMutation = useUpdateInterview();
  const deleteInterviewMutation = useDeleteInterview();
  const cancelInterviewMutation = useCancelInterview();
  const completeInterviewMutation = useCompleteInterview();

  // Get interviews data
  const interviews = interviewsResponse?.data || [];
  const statistics = statsResponse?.data;

  // Handle interview interactions
  const handleInterviewClick = (interview: Interview) => {
    setSelectedInterview(interview);
    setShowInterviewModal(true);
  };

  const handleInterviewEdit = (interview: Interview) => {
    // For now, just show a toast - could open an edit modal
    toast.info('Edit interview functionality coming soon');
  };

  const handleInterviewDelete = (interviewId: string) => {
    deleteInterviewMutation.mutate(interviewId, {
      onSuccess: () => {
        toast.success('Interview deleted successfully');
        refetchInterviews();
      },
      onError: () => {
        toast.error('Failed to delete interview');
      }
    });
  };

  const handleStatusChange = (interviewId: string, status: string) => {
    switch (status) {
      case 'cancelled':
        cancelInterviewMutation.mutate({ id: interviewId }, {
          onSuccess: () => {
            toast.success('Interview cancelled successfully');
            refetchInterviews();
          },
          onError: () => {
            toast.error('Failed to cancel interview');
          }
        });
        break;
      case 'completed':
        completeInterviewMutation.mutate({ id: interviewId }, {
          onSuccess: () => {
            toast.success('Interview completed successfully');
            refetchInterviews();
          },
          onError: () => {
            toast.error('Failed to complete interview');
          }
        });
        break;
      default:
        toast.info(`Status change to ${status} functionality coming soon`);
    }
  };

  const handleReschedule = (interview: Interview) => {
    toast.info('Reschedule functionality coming soon');
  };

  const handleBulkAction = (interviewIds: string[], action: string) => {
    toast.info(`Bulk ${action} functionality coming soon`);
  };

  // Filter interviews by status for tabs
  const filterInterviewsByStatus = (status?: string) => {
    if (!status) return interviews;
    return interviews.filter(interview => interview.status === status);
  };

  // Loading state
  if (isLoadingInterviews) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading interviews...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Error state
  if (interviewsError) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">Failed to load interviews</p>
            <Button onClick={() => refetchInterviews()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Interviews</h1>
            <p className="text-muted-foreground">
              Manage and track all your interviews
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* View Mode Toggle */}
            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={viewMode === 'cards' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('cards')}
                className="h-8 px-3"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'table' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('table')}
                className="h-8 px-3"
              >
                <Table className="h-4 w-4" />
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? 'bg-muted' : ''}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>

            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>

            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Schedule Interview
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Interviews</CardTitle>
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statistics?.totalInterviews || interviews.length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filterInterviewsByStatus('scheduled').length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filterInterviewsByStatus('completed').length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Video Calls</CardTitle>
              <Video className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {interviews.filter(i => i.type === 'video').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <InterviewFilters
            filters={filters}
            onFiltersChange={setFilters}
            onClose={() => setShowFilters(false)}
            showCloseButton={true}
          />
        )}

        {/* Interviews Content */}
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">
              All ({interviews.length})
            </TabsTrigger>
            <TabsTrigger value="scheduled">
              Scheduled ({filterInterviewsByStatus('scheduled').length})
            </TabsTrigger>
            <TabsTrigger value="completed">
              Completed ({filterInterviewsByStatus('completed').length})
            </TabsTrigger>
            <TabsTrigger value="cancelled">
              Cancelled ({filterInterviewsByStatus('cancelled').length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {viewMode === 'cards' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {interviews.map((interview) => (
                  <InterviewCard
                    key={interview.id}
                    interview={interview}
                    onEdit={handleInterviewEdit}
                    onDelete={handleInterviewDelete}
                    onStatusChange={handleStatusChange}
                    onReschedule={handleReschedule}
                    onViewDetails={handleInterviewClick}
                    showActions={true}
                  />
                ))}
              </div>
            ) : (
              <InterviewTable
                interviews={interviews}
                onEdit={handleInterviewEdit}
                onDelete={handleInterviewDelete}
                onStatusChange={handleStatusChange}
                onReschedule={handleReschedule}
                onViewDetails={handleInterviewClick}
                onBulkAction={handleBulkAction}
                showActions={true}
                showBulkActions={true}
                sortable={true}
              />
            )}
          </TabsContent>

          <TabsContent value="scheduled" className="space-y-4">
            {viewMode === 'cards' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filterInterviewsByStatus('scheduled').map((interview) => (
                  <InterviewCard
                    key={interview.id}
                    interview={interview}
                    onEdit={handleInterviewEdit}
                    onDelete={handleInterviewDelete}
                    onStatusChange={handleStatusChange}
                    onReschedule={handleReschedule}
                    onViewDetails={handleInterviewClick}
                    showActions={true}
                  />
                ))}
              </div>
            ) : (
              <InterviewTable
                interviews={filterInterviewsByStatus('scheduled')}
                onEdit={handleInterviewEdit}
                onDelete={handleInterviewDelete}
                onStatusChange={handleStatusChange}
                onReschedule={handleReschedule}
                onViewDetails={handleInterviewClick}
                onBulkAction={handleBulkAction}
                showActions={true}
                showBulkActions={true}
                sortable={true}
              />
            )}
          </TabsContent>

          <TabsContent value="completed" className="space-y-4">
            {viewMode === 'cards' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filterInterviewsByStatus('completed').map((interview) => (
                  <InterviewCard
                    key={interview.id}
                    interview={interview}
                    onEdit={handleInterviewEdit}
                    onDelete={handleInterviewDelete}
                    onStatusChange={handleStatusChange}
                    onReschedule={handleReschedule}
                    onViewDetails={handleInterviewClick}
                    showActions={true}
                  />
                ))}
              </div>
            ) : (
              <InterviewTable
                interviews={filterInterviewsByStatus('completed')}
                onEdit={handleInterviewEdit}
                onDelete={handleInterviewDelete}
                onStatusChange={handleStatusChange}
                onReschedule={handleReschedule}
                onViewDetails={handleInterviewClick}
                onBulkAction={handleBulkAction}
                showActions={true}
                showBulkActions={true}
                sortable={true}
              />
            )}
          </TabsContent>

          <TabsContent value="cancelled" className="space-y-4">
            {viewMode === 'cards' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filterInterviewsByStatus('cancelled').map((interview) => (
                  <InterviewCard
                    key={interview.id}
                    interview={interview}
                    onEdit={handleInterviewEdit}
                    onDelete={handleInterviewDelete}
                    onStatusChange={handleStatusChange}
                    onReschedule={handleReschedule}
                    onViewDetails={handleInterviewClick}
                    showActions={true}
                  />
                ))}
              </div>
            ) : (
              <InterviewTable
                interviews={filterInterviewsByStatus('cancelled')}
                onEdit={handleInterviewEdit}
                onDelete={handleInterviewDelete}
                onStatusChange={handleStatusChange}
                onReschedule={handleReschedule}
                onViewDetails={handleInterviewClick}
                onBulkAction={handleBulkAction}
                showActions={true}
                showBulkActions={true}
                sortable={true}
              />
            )}
          </TabsContent>
        </Tabs>

        {/* Empty State */}
        {interviews.length === 0 && (
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <CalendarIcon className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No interviews found</h3>
                <p className="text-muted-foreground mb-4">
                  Get started by scheduling your first interview.
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Schedule Interview
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Interview Modal */}
        {selectedInterview && (
          <InterviewModal
            interview={selectedInterview}
            isOpen={showInterviewModal}
            onClose={() => {
              setShowInterviewModal(false);
              setSelectedInterview(null);
            }}
            onEdit={handleInterviewEdit}
            onDelete={handleInterviewDelete}
            onStatusChange={handleStatusChange}
            onReschedule={handleReschedule}
          />
        )}
      </div>
    </Layout>
  );
}
