import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  BarChart3,
  TrendingUp,
  Users,
  Clock,
  Target,
  Calendar,
  Download,
  Filter,
} from "lucide-react";
import { mockCandidates, mockJobs, mockInterviews } from "@/data/mockData";
import { useTranslation } from "@/lib/i18n";
import { MetricCard } from "@/domains/analytics/components";

export default function Analytics() {
  const { t } = useTranslation();
  // Calculate metrics from mock data
  const totalCandidates = mockCandidates.length;
  const totalJobs = mockJobs.length;
  const totalInterviews = mockInterviews.length;

  const hiredCandidates = mockCandidates.filter((c) => c.status === "hired");
  const activeJobs = mockJobs.filter((j) => j.status === "active");

  // Calculate conversion rates
  const appliedCandidates = mockCandidates.filter(
    (c) => c.status !== "sourced",
  ).length;
  const interviewCandidates = mockCandidates.filter(
    (c) => c.status === "interview",
  ).length;
  const offerCandidates = mockCandidates.filter(
    (c) => c.status === "offer",
  ).length;

  const applyToInterviewRate = (
    (interviewCandidates / appliedCandidates) *
    100
  ).toFixed(1);
  const interviewToOfferRate = (
    (offerCandidates / interviewCandidates) *
    100
  ).toFixed(1);
  const offerToHireRate = (
    (hiredCandidates.length / offerCandidates) *
    100
  ).toFixed(1);

  // Time to hire calculation (mock)
  const avgTimeToHire = 18; // days
  const timeToHireChange = -2; // improvement

  // Source effectiveness
  const sourceData = [
    { source: "LinkedIn", candidates: 8, hired: 2, rate: 25 },
    { source: "Company Website", candidates: 4, hired: 1, rate: 25 },
    { source: "Referral", candidates: 2, hired: 1, rate: 50 },
    { source: "Indeed", candidates: 3, hired: 1, rate: 33 },
    { source: "AngelList", candidates: 1, hired: 0, rate: 0 },
  ];

  // Pipeline health by stage
  const pipelineData = [
    {
      stage: "Sourced",
      count: mockCandidates.filter((c) => c.status === "sourced").length,
      color: "bg-gray-500",
    },
    {
      stage: "Applied",
      count: mockCandidates.filter((c) => c.status === "applied").length,
      color: "bg-blue-500",
    },
    {
      stage: "Screening",
      count: mockCandidates.filter((c) => c.status === "screening").length,
      color: "bg-yellow-500",
    },
    {
      stage: "Interview",
      count: mockCandidates.filter((c) => c.status === "interview").length,
      color: "bg-purple-500",
    },
    {
      stage: "Offer",
      count: mockCandidates.filter((c) => c.status === "offer").length,
      color: "bg-orange-500",
    },
    {
      stage: "Hired",
      count: mockCandidates.filter((c) => c.status === "hired").length,
      color: "bg-green-500",
    },
  ];
  // Performance by job
  const jobPerformance = mockJobs.map((job) => ({
    ...job,
    candidatesCount: mockCandidates.filter((c) => c.jobId === job.id).length,
    hiredCount: mockCandidates.filter(
      (c) => c.jobId === job.id && c.status === "hired",
    ).length,
  }));

  // Trending metrics (mock data for charts)
  const monthlyData = [
    { month: "Jan", applications: 45, hires: 3, interviews: 18 },
    { month: "Feb", applications: 52, hires: 4, interviews: 22 },
    { month: "Mar", applications: 38, hires: 2, interviews: 15 },
    { month: "Apr", applications: 61, hires: 5, interviews: 28 },
    { month: "May", applications: 48, hires: 3, interviews: 20 },
    { month: "Jun", applications: 55, hires: 4, interviews: 24 },
  ];

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">{t.analytics.title}</h1>
            <p className="text-muted-foreground">{t.analytics.subtitle}</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="gap-2">
              <Filter className="w-4 h-4" />
              {t.analytics.dateFilter}
            </Button>
            <Button className="gap-2">
              <Download className="w-4 h-4" />
              {t.analytics.exportReport}
            </Button>
          </div>
        </div>

        {/* Time Period Selector */}
        <div className="flex items-center gap-4">
          <Select defaultValue="30">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Chọn khoảng thời gian" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">7 ngày qua</SelectItem>
              <SelectItem value="30">30 ngày qua</SelectItem>
              <SelectItem value="90">90 ngày qua</SelectItem>
              <SelectItem value="365">Năm qua</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Tổng Ứng viên"
            value={totalCandidates.toString()}
            change="+12%"
            changeType="increase"
            icon={Users}
          />
          <MetricCard
            title="Thời gian Tuyển dụng"
            value={`${avgTimeToHire} ngày`}
            change={`${timeToHireChange} ngày`}
            changeType="decrease"
            icon={Clock}
          />
          <MetricCard
            title="Tỷ lệ Chuyển đổi"
            value={`${applyToInterviewRate}%`}
            change="+3.2%"
            changeType="increase"
            icon={Target}
          />
          <MetricCard
            title="Việc làm Hoạt động"
            value={activeJobs.length.toString()}
            change="+2"
            changeType="increase"
            icon={BarChart3}
          />
        </div>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Pipeline Health */}
          <Card>
            <CardHeader>
              <CardTitle>Tình trạng Quy trình</CardTitle>
              <CardDescription>Phân bố ứng viên theo giai đoạn</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pipelineData.map((stage) => {
                  const maxCount = Math.max(
                    ...pipelineData.map((p) => p.count),
                  );
                  const percentage =
                    maxCount > 0 ? (stage.count / maxCount) * 100 : 0;

                  return (
                    <div key={stage.stage} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{stage.stage}</span>
                        <span className="font-medium">{stage.count}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${stage.color}`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Conversion Funnel */}
          <Card>
            <CardHeader>
              <CardTitle>Phễu Chuyển đổi</CardTitle>
              <CardDescription>Tỷ lệ chuyển đổi từng giai đoạn</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium">
                    Ứng tuyển → Phỏng vấn
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold">
                      {applyToInterviewRate}%
                    </span>
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  </div>
                </div>
                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                  <span className="text-sm font-medium">
                    Phỏng vấn → Đề nghị
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold">
                      {interviewToOfferRate}%
                    </span>
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  </div>
                </div>
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span className="text-sm font-medium">
                    Đề nghị → Tuyển dụng
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold">
                      {offerToHireRate}%
                    </span>
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Source Effectiveness */}
          <Card>
            <CardHeader>
              <CardTitle>Hiệu quả Nguồn</CardTitle>
              <CardDescription>Hiệu suất theo nguồn ứng viên</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {sourceData.map((source) => (
                  <div
                    key={source.source}
                    className="flex items-center justify-between p-2 rounded-lg border"
                  >
                    <div>
                      <p className="font-medium text-sm">{source.source}</p>
                      <p className="text-xs text-muted-foreground">
                        {source.candidates} ứng viên, {source.hired} đã tuyển
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">{source.rate}%</p>
                      <p className="text-xs text-muted-foreground">
                        tỷ lệ thành công
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Job Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Hiệu suất Công việc</CardTitle>
              <CardDescription>
                Ứng tuyển và tuyển dụng theo vị trí
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {jobPerformance.map((job) => (
                  <div
                    key={job.id}
                    className="flex items-center justify-between p-2 rounded-lg border"
                  >
                    <div>
                      <p className="font-medium text-sm">{job.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {job.department}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">{job.candidatesCount}</p>
                      <p className="text-xs text-muted-foreground">
                        {job.hiredCount} đã tuyển
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Trending Data */}
        <Card>
          <CardHeader>
            <CardTitle>Xu hướng Hàng tháng</CardTitle>
            <CardDescription>
              Ứng tuyển, phỏng vấn và tuyển dụng theo thời gian
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-6 gap-4">
                {monthlyData.map((month) => (
                  <div key={month.month} className="text-center">
                    <p className="text-xs font-medium text-muted-foreground mb-2">
                      {month.month}
                    </p>
                    <div className="space-y-2">
                      <div className="bg-blue-100 rounded p-2">
                        <p className="text-xs text-blue-800">Ứng tuyển</p>
                        <p className="font-bold text-blue-900">
                          {month.applications}
                        </p>
                      </div>
                      <div className="bg-purple-100 rounded p-2">
                        <p className="text-xs text-purple-800">Phỏng vấn</p>
                        <p className="font-bold text-purple-900">
                          {month.interviews}
                        </p>
                      </div>
                      <div className="bg-green-100 rounded p-2">
                        <p className="text-xs text-green-800">Tuyển dụng</p>
                        <p className="font-bold text-green-900">
                          {month.hires}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Insights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                {t.analytics.keyInsights}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• {t.analytics.insights.linkedinTop}</li>
                <li>• {t.analytics.insights.referralsBest}</li>
                <li>• {t.analytics.insights.frontendMost}</li>
                <li>• {t.analytics.insights.interviewImproved}</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-600" />
                {t.analytics.recommendationsTitle}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• {t.analytics.recommendations.increaseReferral}</li>
                <li>• {t.analytics.recommendations.optimizeLinkedin}</li>
                <li>• {t.analytics.recommendations.reviewScreening}</li>
                <li>• {t.analytics.recommendations.expandUx}</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-purple-600" />
                {t.analytics.nextSteps}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• {t.analytics.nextStepsItems.scheduleReview}</li>
                <li>• {t.analytics.nextStepsItems.updateJobs}</li>
                <li>• {t.analytics.nextStepsItems.candidateSurvey}</li>
                <li>• {t.analytics.nextStepsItems.reviewCompensation}</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
