import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { JobDetailContent } from "@/components/jobs/JobDetailContent";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AlertTriangle,
  ArrowLeft,
  RefreshCw,
  Loader2,
  Briefcase,
} from "lucide-react";
import { useTranslation } from "@/lib/i18n";
import { toast } from "sonner";
import { useJob, useJobCandidates } from "@/hooks/useApi";
import { useSimplePageTitle } from "@/hooks/usePageTitle";

export default function JobDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Use the API hooks to fetch job data and candidates
  const {
    data: jobResponse,
    isLoading,
    error,
    refetch,
  } = useJob(id || "", "candidates");

  const { data: candidatesResponse, isLoading: isLoadingCandidates } =
    useJobCandidates(id || "", {});

  const handleRetry = () => {
    refetch();
  };

  const handleEdit = (jobToEdit: any) => {
    // Navigate to edit page or open edit modal
    toast.info("Edit functionality would open here");
  };

  const handleDuplicate = (jobToDuplicate: any) => {
    // Create a new job based on the current one
    toast.success("Job duplicated successfully");
  };

  // Loading state
  if (isLoading) {
    return (
      <Layout>
        <div className="p-6 max-w-6xl mx-auto space-y-6">
          {/* Header skeleton */}
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4 flex-1">
              <Skeleton className="w-10 h-10" />
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <Skeleton className="h-8 w-64" />
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-24" />
                </div>
                <div className="flex items-center gap-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-36" />
                  <Skeleton className="h-4 w-28" />
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-9 w-20" />
              <Skeleton className="h-9 w-24" />
              <Skeleton className="h-9 w-16" />
            </div>
          </div>

          {/* Metrics skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Skeleton className="h-4 w-20 mb-2" />
                      <Skeleton className="h-8 w-12" />
                    </div>
                    <Skeleton className="w-8 h-8 rounded" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Content skeleton */}
          <div className="space-y-6">
            <Skeleton className="h-10 w-full" />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <Skeleton className="h-6 w-32" />
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                </Card>
              </div>
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <Skeleton className="h-6 w-32" />
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Get job data from API response
  const job = jobResponse?.data;
  const candidates = candidatesResponse?.data || [];

  // Set page title with job title
  useSimplePageTitle("pageTitle.jobs.detail", job?.title || "Loading...");

  // Error state
  if (error) {
    return (
      <Layout>
        <div className="p-6 max-w-4xl mx-auto">
          <div className="flex items-center gap-2 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </Button>
          </div>

          <Alert variant="destructive" className="max-w-md mx-auto">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>
                {error instanceof Error
                  ? error.message
                  : "Failed to load job details"}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetry}
                className="ml-4"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>

          <div className="text-center mt-8">
            <Briefcase className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-2xl font-semibold mb-2">Job Not Found</h2>
            <p className="text-muted-foreground mb-6">
              The job posting you're looking for doesn't exist or may have been
              removed.
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="outline" onClick={() => navigate("/jobs")}>
                View All Jobs
              </Button>
              <Button onClick={handleRetry}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Success state
  if (!job) {
    return null;
  }

  return (
    <Layout>
      <JobDetailContent
        job={job}
        candidates={candidates}
        onEdit={handleEdit}
        onDuplicate={handleDuplicate}
        showBackButton={true}
        isFullPage={true}
      />
    </Layout>
  );
}
