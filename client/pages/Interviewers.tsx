import { useState, useMemo } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Users,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Calendar,
  Star,
  Clock,
  MapPin,
  Mail,
  Phone,
  Building2,
  Loader2,
  RefreshCw,
  Download,
  Settings,
  ChevronDown,
  AlertTriangle,
} from "lucide-react";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";
import {
  useInterviewers,
  useDeleteInterviewer,
  useCreateInterviewer,
  useUpdateInterviewer,
} from "@/hooks/useApi";
import { Interviewer, DEPARTMENTS } from "@/lib/types/interviewer";
import { CreateEditInterviewerModal } from "@/components/interviewers/CreateEditInterviewerModal";
import { InterviewerDetailModal } from "@/components/interviewers/InterviewerDetailModal";

export function Interviewers() {
  const t = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [expertiseFilter, setExpertiseFilter] = useState("");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingInterviewer, setEditingInterviewer] =
    useState<Interviewer | null>(null);
  const [viewingInterviewer, setViewingInterviewer] =
    useState<Interviewer | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    interviewer: Interviewer | null;
  }>({ isOpen: false, interviewer: null });

  // API hooks
  const {
    data: interviewersData,
    isLoading,
    error,
    refetch,
  } = useInterviewers({
    include_stats: true,
  });

  const deleteInterviewerMutation = useDeleteInterviewer();
  const createInterviewerMutation = useCreateInterviewer();
  const updateInterviewerMutation = useUpdateInterviewer();

  // Transform and filter data
  const interviewers = useMemo(() => {
    if (!interviewersData?.data) return [];

    // Ensure expertise and time_slots are always arrays and log any issues
    return interviewersData.data.map((interviewer: any) => {
      let expertise = interviewer.expertise;
      let time_slots = interviewer.time_slots;

      if (!Array.isArray(interviewer.expertise)) {
        console.warn(
          "Interviewer expertise is not an array:",
          interviewer.id,
          interviewer.expertise,
        );
        // Convert string to array or provide empty array
        expertise = interviewer.expertise
          ? typeof interviewer.expertise === "string"
            ? interviewer.expertise.split(",").map((s: string) => s.trim())
            : []
          : [];
      }

      if (!Array.isArray(interviewer.time_slots)) {
        console.warn(
          "Interviewer time_slots is not an array:",
          interviewer.id,
          interviewer.time_slots,
        );
        // Provide empty array fallback
        time_slots = [];
      }

      return { ...interviewer, expertise, time_slots };
    });
  }, [interviewersData]);

  const filteredInterviewers = useMemo(() => {
    return interviewers.filter((interviewer) => {
      const matchesSearch =
        interviewer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        interviewer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        interviewer.department.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesDepartment =
        departmentFilter === "all" ||
        interviewer.department === departmentFilter;

      const matchesStatus =
        statusFilter === "all" ||
        (statusFilter === "active" && interviewer.is_active) ||
        (statusFilter === "inactive" && !interviewer.is_active);

      const matchesExpertise =
        !expertiseFilter ||
        interviewer.expertise.some((skill) =>
          skill.toLowerCase().includes(expertiseFilter.toLowerCase()),
        );

      return (
        matchesSearch && matchesDepartment && matchesStatus && matchesExpertise
      );
    });
  }, [
    interviewers,
    searchTerm,
    departmentFilter,
    statusFilter,
    expertiseFilter,
  ]);

  // Statistics
  const stats = useMemo(() => {
    const total = interviewers.length;
    const active = interviewers.filter((i) => i.is_active).length;
    const totalInterviews = interviewers.reduce(
      (sum, i) => sum + (i.statistics?.total_interviews || 0),
      0,
    );
    const avgRating =
      interviewers.length > 0
        ? interviewers.reduce(
            (sum, i) => sum + (i.statistics?.average_rating || 0),
            0,
          ) / interviewers.length
        : 0;

    return { total, active, totalInterviews, avgRating };
  }, [interviewers]);

  // Handlers
  const handleEdit = (interviewer: Interviewer) => {
    setEditingInterviewer(interviewer);
  };

  const handleView = (interviewer: Interviewer) => {
    setViewingInterviewer(interviewer);
  };

  const handleDelete = (interviewer: Interviewer) => {
    setDeleteConfirmation({ isOpen: true, interviewer });
  };

  const confirmDelete = async () => {
    if (!deleteConfirmation.interviewer) return;

    try {
      await deleteInterviewerMutation.mutateAsync(
        deleteConfirmation.interviewer.id.toString(),
      );
      setDeleteConfirmation({ isOpen: false, interviewer: null });
      toast.success("Đã xóa người phỏng vấn thành công!");
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleSubmit = async (data: any) => {
    try {
      if (editingInterviewer) {
        // Update existing interviewer
        await updateInterviewerMutation.mutateAsync({
          id: editingInterviewer.id.toString(),
          data,
        });
      } else {
        // Create new interviewer
        await createInterviewerMutation.mutateAsync(data);
      }

      // Close modal and refresh data
      setIsCreateModalOpen(false);
      setEditingInterviewer(null);
      refetch();
    } catch (error) {
      console.error("Failed to save interviewer:", error);
      // Error handling is done by the mutation hooks
      throw error; // Re-throw to let the modal handle it
    }
  };

  const handleExportData = () => {
    const csvData = filteredInterviewers.map((interviewer) => ({
      Tên: interviewer.name,
      Email: interviewer.email,
      "Phòng ban": interviewer.department,
      "Vị trí": interviewer.location,
      "Chuyên môn":
        interviewer.expertise_list ||
        (Array.isArray(interviewer.expertise)
          ? interviewer.expertise.join(", ")
          : interviewer.expertise || ""),
      "Trạng thái": interviewer.is_active ? "Hoạt động" : "Không hoạt động",
      "Tổng số phỏng vấn": interviewer.statistics?.total_interviews || 0,
      "Đánh giá trung bình": interviewer.statistics?.average_rating || 0,
    }));

    const csvContent = [
      Object.keys(csvData[0] || {}).join(","),
      ...csvData.map((row) => Object.values(row).join(",")),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "interviewers.csv";
    a.click();
    URL.revokeObjectURL(url);
  };

  if (error) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Có lỗi xảy ra</h3>
            <p className="text-muted-foreground mb-4">
              Không thể tải danh sách người phỏng vấn
            </p>
            <Button onClick={() => refetch()}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Thử lại
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Quản lý Người phỏng vấn</h1>
            <p className="text-muted-foreground">
              Quản lý hồ sơ và lịch trình của các người phỏng vấn
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={handleExportData}
              className="gap-2"
            >
              <Download className="w-4 h-4" />
              Xuất dữ liệu
            </Button>
            <Button
              onClick={() => setIsCreateModalOpen(true)}
              className="gap-2"
            >
              <Plus className="w-4 h-4" />
              Thêm người phỏng vấn
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng số</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">Người phỏng vấn</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Đang hoạt động
              </CardTitle>
              <UserCheck className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats.active}
              </div>
              <p className="text-xs text-muted-foreground">
                Sẵn sàng phỏng vấn
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Tổng phỏng vấn
              </CardTitle>
              <Calendar className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats.totalInterviews}
              </div>
              <p className="text-xs text-muted-foreground">Đã thực hiện</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Đánh giá TB</CardTitle>
              <Star className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {stats.avgRating.toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">Trên 5 điểm</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm kiếm theo tên, email, phòng ban..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select
                value={departmentFilter}
                onValueChange={setDepartmentFilter}
              >
                <SelectTrigger className="w-full lg:w-48">
                  <SelectValue placeholder="Phòng ban" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả phòng ban</SelectItem>
                  {DEPARTMENTS.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full lg:w-40">
                  <SelectValue placeholder="Trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="active">Hoạt động</SelectItem>
                  <SelectItem value="inactive">Không hoạt động</SelectItem>
                </SelectContent>
              </Select>

              <Input
                placeholder="Chuyên môn..."
                value={expertiseFilter}
                onChange={(e) => setExpertiseFilter(e.target.value)}
                className="w-full lg:w-40"
              />

              <Button variant="outline" onClick={() => refetch()}>
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Interviewers Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>
                Danh sách Người phỏng vấn ({filteredInterviewers.length})
              </span>
              {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-48">
                <Loader2 className="w-8 h-8 animate-spin" />
              </div>
            ) : filteredInterviewers.length === 0 ? (
              <div className="text-center py-12">
                <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  Không có người phỏng vấn nào
                </h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm ||
                  departmentFilter !== "all" ||
                  statusFilter !== "all" ||
                  expertiseFilter
                    ? "Không tìm thấy người phỏng vấn phù hợp với bộ lọc"
                    : "Hãy thêm người phỏng vấn đầu tiên"}
                </p>
                {!searchTerm &&
                  departmentFilter === "all" &&
                  statusFilter === "all" &&
                  !expertiseFilter && (
                    <Button onClick={() => setIsCreateModalOpen(true)}>
                      <Plus className="w-4 h-4 mr-2" />
                      Thêm người phỏng vấn
                    </Button>
                  )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Người phỏng vấn</TableHead>
                      <TableHead>Phòng ban</TableHead>
                      <TableHead>Chuyên môn</TableHead>
                      <TableHead>Trạng thái</TableHead>
                      <TableHead>Phỏng vấn</TableHead>
                      <TableHead>Đánh giá</TableHead>
                      <TableHead className="text-right">Thao tác</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredInterviewers.map((interviewer) => (
                      <TableRow
                        key={interviewer.id}
                        className="cursor-pointer hover:bg-muted/50"
                      >
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="w-10 h-10">
                              <AvatarImage src={interviewer.user.avatar} />
                              <AvatarFallback>
                                {interviewer.user.initials}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">
                                {interviewer.name}
                              </div>
                              <div className="text-sm text-muted-foreground flex items-center gap-1">
                                <Mail className="w-3 h-3" />
                                {interviewer.email}
                              </div>
                              {interviewer.location && (
                                <div className="text-sm text-muted-foreground flex items-center gap-1">
                                  <MapPin className="w-3 h-3" />
                                  {interviewer.location}
                                </div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Building2 className="w-4 h-4 text-muted-foreground" />
                            {interviewer.department}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {interviewer.user.title}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1 max-w-xs">
                            {interviewer.expertise
                              .slice(0, 3)
                              .map((skill, index) => (
                                <Badge
                                  key={index}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {skill}
                                </Badge>
                              ))}
                            {interviewer.expertise.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{interviewer.expertise.length - 3}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              interviewer.is_active ? "default" : "secondary"
                            }
                            className={
                              interviewer.is_active
                                ? "bg-green-100 text-green-800"
                                : ""
                            }
                          >
                            {interviewer.is_active ? (
                              <>
                                <UserCheck className="w-3 h-3 mr-1" />
                                Hoạt động
                              </>
                            ) : (
                              <>
                                <UserX className="w-3 h-3 mr-1" />
                                Không hoạt động
                              </>
                            )}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="text-sm">
                              Tổng:{" "}
                              {interviewer.statistics?.total_interviews || 0}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Sắp tới:{" "}
                              {interviewer.statistics?.upcoming_interviews || 0}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {interviewer.statistics?.average_rating ? (
                            <div className="flex items-center gap-1">
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                              <span className="text-sm font-medium">
                                {interviewer.statistics.average_rating.toFixed(
                                  1,
                                )}
                              </span>
                            </div>
                          ) : (
                            <span className="text-sm text-muted-foreground">
                              Chưa có
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleView(interviewer)}
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                Xem chi tiết
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleEdit(interviewer)}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Chỉnh sửa
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDelete(interviewer)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Xóa
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Modals */}
        <CreateEditInterviewerModal
          isOpen={isCreateModalOpen || !!editingInterviewer}
          onClose={() => {
            setIsCreateModalOpen(false);
            setEditingInterviewer(null);
          }}
          interviewer={editingInterviewer}
          onSubmit={handleSubmit}
          isLoading={
            createInterviewerMutation.isPending ||
            updateInterviewerMutation.isPending
          }
        />

        <InterviewerDetailModal
          isOpen={!!viewingInterviewer}
          onClose={() => setViewingInterviewer(null)}
          interviewer={viewingInterviewer}
          onEdit={() => {
            setEditingInterviewer(viewingInterviewer);
            setViewingInterviewer(null);
          }}
        />

        {/* Delete Confirmation */}
        <AlertDialog
          open={deleteConfirmation.isOpen}
          onOpenChange={(open) =>
            setDeleteConfirmation({ isOpen: open, interviewer: null })
          }
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
              <AlertDialogDescription>
                Bạn có chắc chắn muốn xóa người phỏng vấn{" "}
                <strong>{deleteConfirmation.interviewer?.name}</strong>?
                <br />
                <br />
                Hành động này không thể hoàn tác và sẽ xóa tất cả dữ liệu liên
                quan.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Hủy</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-red-600 hover:bg-red-700"
                disabled={deleteInterviewerMutation.isPending}
              >
                {deleteInterviewerMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Xóa
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </Layout>
  );
}
