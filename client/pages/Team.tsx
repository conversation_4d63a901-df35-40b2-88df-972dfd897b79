import { useState, useMemo } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  MoreVertical,
  Star,
  Calendar,
  Clock,
  MapPin,
  Building,
  Mail,
  Phone,
  User,
  UserCheck,
  UserX,
  Crown,
  Shield,
  Eye,
  Award,
  TrendingUp,
  Target,
  Activity,
  Network,
  Download,
  Settings,
} from "lucide-react";
import {
  mockTeamMembers,
  TeamMember,
  getUserRole,
  getPermissionLabel,
} from "@/data/profilesData";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";

export default function Team() {
  const { t } = useTranslation();
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>(mockTeamMembers);
  const [searchTerm, setSearchTerm] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState<string>("all");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [view, setView] = useState<"grid" | "table" | "chart">("grid");

  // Filter team members
  const filteredMembers = useMemo(() => {
    return teamMembers.filter((member) => {
      const matchesSearch =
        member.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.title.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesDepartment =
        departmentFilter === "all" || member.department === departmentFilter;

      const matchesRole = roleFilter === "all" || member.role === roleFilter;

      const matchesStatus =
        statusFilter === "all" ||
        (statusFilter === "active" && member.isActive) ||
        (statusFilter === "inactive" && !member.isActive);

      return matchesSearch && matchesDepartment && matchesRole && matchesStatus;
    });
  }, [teamMembers, searchTerm, departmentFilter, roleFilter, statusFilter]);

  // Calculate team statistics
  const teamStats = useMemo(() => {
    const total = teamMembers.length;
    const active = teamMembers.filter((m) => m.isActive).length;
    const avgRating =
      teamMembers.reduce((sum, m) => sum + m.performanceRating, 0) / total || 0;
    const totalInterviews = teamMembers.reduce(
      (sum, m) => sum + m.interviewsCompleted,
      0,
    );
    const totalHires = teamMembers.reduce(
      (sum, m) => sum + m.candidatesHired,
      0,
    );

    return {
      total,
      active,
      inactive: total - active,
      avgRating: avgRating.toFixed(1),
      totalInterviews,
      totalHires,
      avgHireRate:
        totalInterviews > 0
          ? ((totalHires / totalInterviews) * 100).toFixed(1)
          : "0",
    };
  }, [teamMembers]);

  const departments = [...new Set(teamMembers.map((m) => m.department))];
  const roles = [...new Set(teamMembers.map((m) => m.role))];

  const handleViewMember = (member: TeamMember) => {
    setSelectedMember(member);
    setIsDetailModalOpen(true);
  };

  const toggleMemberStatus = (memberId: string) => {
    setTeamMembers(
      teamMembers.map((m) =>
        m.id === memberId ? { ...m, isActive: !m.isActive } : m,
      ),
    );
    toast.success("Member status updated!");
  };

  const exportTeamData = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      teamSize: teamMembers.length,
      statistics: teamStats,
      members: filteredMembers,
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `team_export_${new Date().toISOString().split("T")[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Team data exported!");
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "admin":
        return Crown;
      case "manager":
        return Shield;
      case "recruiter":
        return Users;
      case "interviewer":
        return UserCheck;
      default:
        return Eye;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "admin":
        return "text-amber-600 bg-amber-100 border-amber-200";
      case "manager":
        return "text-blue-600 bg-blue-100 border-blue-200";
      case "recruiter":
        return "text-green-600 bg-green-100 border-green-200";
      case "interviewer":
        return "text-purple-600 bg-purple-100 border-purple-200";
      default:
        return "text-gray-600 bg-gray-100 border-gray-200";
    }
  };

  const renderMemberCard = (member: TeamMember) => {
    const RoleIcon = getRoleIcon(member.role);
    return (
      <Card
        key={member.id}
        className="hover:shadow-md transition-shadow cursor-pointer"
        onClick={() => handleViewMember(member)}
      >
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Avatar className="w-12 h-12">
              <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                {member.initials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold">
                  {member.firstName} {member.lastName}
                </h3>
                <Badge
                  className={`text-xs ${getRoleColor(member.role)}`}
                  variant="outline"
                >
                  <RoleIcon className="w-3 h-3 mr-1" />
                  {getUserRole(member.role)}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                {member.title}
              </p>
              <div className="flex items-center gap-3 text-xs text-muted-foreground mb-2">
                <span className="flex items-center gap-1">
                  <Building className="w-3 h-3" />
                  {member.department}
                </span>
                <span className="flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  {member.location}
                </span>
              </div>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center p-1 bg-muted rounded">
                  <p className="font-semibold">{member.interviewsCompleted}</p>
                  <p className="text-muted-foreground">Interviews</p>
                </div>
                <div className="text-center p-1 bg-muted rounded">
                  <p className="font-semibold">{member.candidatesHired}</p>
                  <p className="text-muted-foreground">Hires</p>
                </div>
                <div className="text-center p-1 bg-muted rounded">
                  <p className="font-semibold">{member.performanceRating}</p>
                  <p className="text-muted-foreground">Rating</p>
                </div>
              </div>
              <div className="flex items-center justify-between mt-3">
                <Badge
                  variant={member.isActive ? "default" : "secondary"}
                  className="text-xs"
                >
                  {member.isActive ? "Active" : "Inactive"}
                </Badge>
                <DropdownMenu onClick={(e) => e.stopPropagation()}>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewMember(member)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => toggleMemberStatus(member.id)}
                    >
                      {member.isActive ? (
                        <>
                          <UserX className="mr-2 h-4 w-4" />
                          Deactivate
                        </>
                      ) : (
                        <>
                          <UserCheck className="mr-2 h-4 w-4" />
                          Activate
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-destructive">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Remove
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              {t.team.title}
            </h1>
            <p className="text-muted-foreground">{t.team.subtitle}</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="gap-2 rounded-xl"
              onClick={exportTeamData}
            >
              <Download className="w-4 h-4" />
              {t.common.export}
            </Button>
            <Button className="ai-button gap-2">
              <Plus className="w-4 h-4" />
              {t.team.addMember}
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/20 rounded-xl">
                  <Users className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="text-lg font-bold">{teamStats.total}</p>
                  <p className="text-xs text-muted-foreground">Total</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-500/20 bg-gradient-to-br from-green-500/5 to-emerald-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/20 rounded-xl">
                  <UserCheck className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">{teamStats.active}</p>
                  <p className="text-xs text-muted-foreground">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-yellow-500/20 bg-gradient-to-br from-yellow-500/5 to-orange-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-500/20 rounded-xl">
                  <Star className="h-4 w-4 text-yellow-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">{teamStats.avgRating}</p>
                  <p className="text-xs text-muted-foreground">Avg Rating</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-indigo-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/20 rounded-xl">
                  <Calendar className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">
                    {teamStats.totalInterviews}
                  </p>
                  <p className="text-xs text-muted-foreground">Interviews</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500/20 rounded-xl">
                  <Target className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">{teamStats.totalHires}</p>
                  <p className="text-xs text-muted-foreground">Hires</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-emerald-500/20 bg-gradient-to-br from-emerald-500/5 to-green-500/5">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-emerald-500/20 rounded-xl">
                  <TrendingUp className="h-4 w-4 text-emerald-600" />
                </div>
                <div>
                  <p className="text-lg font-bold">{teamStats.avgHireRate}%</p>
                  <p className="text-xs text-muted-foreground">Hire Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search team members..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 rounded-xl"
            />
          </div>

          <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
            <SelectTrigger className="w-[180px] rounded-xl">
              <SelectValue placeholder="All Departments" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept} value={dept}>
                  {dept}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[150px] rounded-xl">
              <SelectValue placeholder="All Roles" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              {roles.map((role) => (
                <SelectItem key={role} value={role}>
                  {getUserRole(role)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[120px] rounded-xl">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center gap-2">
            <Button
              variant={view === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setView("grid")}
              className="rounded-xl"
            >
              Grid
            </Button>
            <Button
              variant={view === "table" ? "default" : "outline"}
              size="sm"
              onClick={() => setView("table")}
              className="rounded-xl"
            >
              Table
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            Showing {filteredMembers.length} of {teamMembers.length} members
          </div>
        </div>

        {/* Team Members */}
        {view === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredMembers.map(renderMemberCard)}
          </div>
        ) : (
          <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Member</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Interviews</TableHead>
                  <TableHead>Hires</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMembers.map((member) => {
                  const RoleIcon = getRoleIcon(member.role);
                  return (
                    <TableRow key={member.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback className="bg-primary/10 text-primary text-xs">
                              {member.initials}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">
                              {member.firstName} {member.lastName}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {member.title}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={`text-xs ${getRoleColor(member.role)}`}
                          variant="outline"
                        >
                          <RoleIcon className="w-3 h-3 mr-1" />
                          {getUserRole(member.role)}
                        </Badge>
                      </TableCell>
                      <TableCell>{member.department}</TableCell>
                      <TableCell>{member.interviewsCompleted}</TableCell>
                      <TableCell>{member.candidatesHired}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 text-yellow-500" />
                          {member.performanceRating}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={member.isActive ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {member.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => handleViewMember(member)}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => toggleMemberStatus(member.id)}
                            >
                              {member.isActive ? (
                                <>
                                  <UserX className="mr-2 h-4 w-4" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Remove
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </Card>
        )}

        {/* Member Detail Modal */}
        <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Team Member Details
              </DialogTitle>
            </DialogHeader>
            {selectedMember && (
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <Avatar className="w-16 h-16">
                    <AvatarFallback className="bg-primary/10 text-primary font-semibold text-lg">
                      {selectedMember.initials}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold">
                      {selectedMember.firstName} {selectedMember.lastName}
                    </h3>
                    <p className="text-muted-foreground">
                      {selectedMember.title}
                    </p>
                    <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Mail className="w-4 h-4" />
                        {selectedMember.email}
                      </span>
                      <span className="flex items-center gap-1">
                        <Phone className="w-4 h-4" />
                        {selectedMember.phone}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-semibold mb-2">Performance</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Rating:</span>
                          <span className="font-medium">
                            {selectedMember.performanceRating}/5
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Interviews:</span>
                          <span className="font-medium">
                            {selectedMember.interviewsCompleted}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Hires:</span>
                          <span className="font-medium">
                            {selectedMember.candidatesHired}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-semibold mb-2">Role & Access</h4>
                      <div className="space-y-2">
                        <div>
                          <span className="text-sm">Role:</span>
                          <Badge
                            className={`ml-2 ${getRoleColor(selectedMember.role)}`}
                          >
                            {getUserRole(selectedMember.role)}
                          </Badge>
                        </div>
                        <div>
                          <span className="text-sm">Department:</span>
                          <span className="ml-2 font-medium">
                            {selectedMember.department}
                          </span>
                        </div>
                        <div>
                          <span className="text-sm">Location:</span>
                          <span className="ml-2 font-medium">
                            {selectedMember.location}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Permissions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {selectedMember.permissions.map((permission) => (
                        <Badge key={permission} variant="secondary">
                          {getPermissionLabel(permission)}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Current Projects</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {selectedMember.currentProjects.map((project, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 p-2 bg-muted rounded-lg"
                        >
                          <Activity className="w-4 h-4 text-primary" />
                          <span>{project}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
}
