/**
 * Jobs Page
 * Modern jobs management page using domain-driven architecture
 */

import React, { useState, useMemo, useCallback } from "react";
import { Layout } from "@/components/layout/Layout";
import { ProtectedRoute } from "@/lib/auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  JobFormModal,
  JobDetailModal,
  DeleteJobModal,
  BulkJobActions,
} from "@/domains/jobs/components";
import { useJobs, useJobActions } from "@/domains/jobs/hooks";
import {
  Job,
  JobListItem,
  JobSearchFilters,
  JobStatus,
  JobPriority,
  JobUtils,
} from "@/domains/jobs/types";
import {
  Plus,
  Search,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Co<PERSON>,
  ExternalLink,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";

interface JobsPageState {
  selectedJobs: Set<string>;
  searchQuery: string;
  statusFilter: JobStatus | "all";
  departmentFilter: string;
  priorityFilter: JobPriority | "all";
  showJobForm: boolean;
  showJobDetail: boolean;
  showDeleteConfirm: boolean;
  editingJob: JobListItem | null;
  viewingJob: JobListItem | null;
  deletingJob: JobListItem | null;
}

const JobsPage: React.FC = () => {
  // State management
  const [state, setState] = useState<JobsPageState>({
    selectedJobs: new Set(),
    searchQuery: "",
    statusFilter: "all",
    departmentFilter: "all",
    priorityFilter: "all",
    showJobForm: false,
    showJobDetail: false,
    showDeleteConfirm: false,
    editingJob: null,
    viewingJob: null,
    deletingJob: null,
  });

  // Build search filters
  const filters = useMemo((): JobSearchFilters => {
    const searchFilters: JobSearchFilters = {};

    if (state.searchQuery.trim()) {
      searchFilters.query = state.searchQuery.trim();
    }

    if (state.statusFilter !== "all") {
      searchFilters.status = [state.statusFilter];
    }

    if (state.departmentFilter !== "all") {
      searchFilters.department = [state.departmentFilter];
    }

    if (state.priorityFilter !== "all") {
      searchFilters.priority = [state.priorityFilter];
    }

    return searchFilters;
  }, [
    state.searchQuery,
    state.statusFilter,
    state.departmentFilter,
    state.priorityFilter,
  ]);

  // Hooks
  const {
    jobs,
    statistics,
    isLoading,
    createJob,
    updateJob,
    deleteJob,
    bulkAction,
  } = useJobs({ filters });

  const {
    publishJob,
    unpublishJob,
    closeJob,
    archiveJob,
    duplicateJob,
    shareJob,
    copyJobLink,
  } = useJobActions();

  // Event handlers
  const handleSelectJob = useCallback((jobId: string, selected: boolean) => {
    setState((prev) => {
      const newSelected = new Set(prev.selectedJobs);
      if (selected) {
        newSelected.add(jobId);
      } else {
        newSelected.delete(jobId);
      }
      return { ...prev, selectedJobs: newSelected };
    });
  }, []);

  const handleSelectAll = useCallback(
    (selected: boolean) => {
      setState((prev) => ({
        ...prev,
        selectedJobs: selected ? new Set(jobs.map((job) => job.id)) : new Set(),
      }));
    },
    [jobs],
  );

  const handleClearSelection = useCallback(() => {
    setState((prev) => ({ ...prev, selectedJobs: new Set() }));
  }, []);

  const handleBulkAction = useCallback(
    async (action: string) => {
      const jobIds = Array.from(state.selectedJobs);
      if (jobIds.length === 0) return;

      try {
        switch (action) {
          case "update_status":
            // This would typically open a status selection modal
            await bulkAction({
              jobIds,
              operation: "update_status",
              data: { status: "published" },
            });
            break;
          case "add_tags":
            // This would typically open a tag selection modal
            await bulkAction({
              jobIds,
              operation: "add_tags",
              data: { tags: ["bulk-updated"] },
            });
            break;
          case "remove_tags":
            await bulkAction({
              jobIds,
              operation: "remove_tags",
              data: { tags: ["old-tag"] },
            });
            break;
          case "delete":
            await bulkAction({ jobIds, operation: "delete" });
            break;
          case "export":
            await bulkAction({ jobIds, operation: "export" });
            break;
        }
        handleClearSelection();
      } catch (error) {
        console.error("Bulk action failed:", error);
      }
    },
    [state.selectedJobs, bulkAction, handleClearSelection],
  );

  const handleCreateJob = useCallback(
    async (data: any) => {
      await createJob(data);
      setState((prev) => ({ ...prev, showJobForm: false }));
    },
    [createJob],
  );

  const handleUpdateJob = useCallback(
    async (data: any) => {
      if (!state.editingJob) return;
      await updateJob(state.editingJob.id, data);
      setState((prev) => ({ ...prev, showJobForm: false, editingJob: null }));
    },
    [updateJob, state.editingJob],
  );

  const handleDeleteJob = useCallback(async () => {
    if (!state.deletingJob) return;
    await deleteJob(state.deletingJob.id);
    setState((prev) => ({
      ...prev,
      showDeleteConfirm: false,
      deletingJob: null,
    }));
  }, [deleteJob, state.deletingJob]);

  const handleJobAction = useCallback(
    async (job: JobListItem, action: string) => {
      try {
        switch (action) {
          case "view":
            setState((prev) => ({
              ...prev,
              viewingJob: job,
              showJobDetail: true,
            }));
            break;
          case "edit":
            setState((prev) => ({
              ...prev,
              editingJob: job,
              showJobForm: true,
            }));
            break;
          case "delete":
            setState((prev) => ({
              ...prev,
              deletingJob: job,
              showDeleteConfirm: true,
            }));
            break;
          case "duplicate":
            await duplicateJob(job.id);
            break;
          case "publish":
            await publishJob(job.id);
            break;
          case "unpublish":
            await unpublishJob(job.id);
            break;
          case "close":
            await closeJob(job.id);
            break;
          case "archive":
            await archiveJob(job.id);
            break;
          case "share":
            await shareJob(job as any); // Type assertion for now
            break;
          case "copy-link":
            await copyJobLink(job as any); // Type assertion for now
            break;
        }
      } catch (error) {
        console.error("Job action failed:", error);
      }
    },
    [
      publishJob,
      unpublishJob,
      closeJob,
      archiveJob,
      duplicateJob,
      shareJob,
      copyJobLink,
    ],
  );

  // Get unique departments for filter
  const departments = useMemo(() => {
    const depts = new Set(jobs.map((job) => job.department));
    return Array.from(depts).sort();
  }, [jobs]);

  const allSelected =
    jobs.length > 0 && state.selectedJobs.size === jobs.length;

  return (
    <ProtectedRoute>
      <Layout>
        <div className="space-y-6 p-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Jobs</h1>
              <p className="text-muted-foreground">
                Manage job postings and track applications
              </p>
            </div>
            <Button
              onClick={() =>
                setState((prev) => ({ ...prev, showJobForm: true }))
              }
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Create Job
            </Button>
          </div>

          {/* Statistics Cards */}
          {statistics && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Jobs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{statistics.total}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Published
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statistics.byStatus.published || 0}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Applications
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statistics.totalApplications}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Avg. Applications
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statistics.averageApplicationsPerJob.toFixed(1)}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search jobs..."
                      value={state.searchQuery}
                      onChange={(e) =>
                        setState((prev) => ({
                          ...prev,
                          searchQuery: e.target.value,
                        }))
                      }
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select
                  value={state.statusFilter}
                  onValueChange={(value) =>
                    setState((prev) => ({
                      ...prev,
                      statusFilter: value as JobStatus | "all",
                    }))
                  }
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={state.departmentFilter}
                  onValueChange={(value) =>
                    setState((prev) => ({ ...prev, departmentFilter: value }))
                  }
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={state.priorityFilter}
                  onValueChange={(value) =>
                    setState((prev) => ({
                      ...prev,
                      priorityFilter: value as JobPriority | "all",
                    }))
                  }
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priority</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Bulk Actions */}
          <BulkJobActions
            selectedCount={state.selectedJobs.size}
            onClearSelection={handleClearSelection}
            onBulkAction={handleBulkAction}
          />

          {/* Jobs Table */}
          <Card>
            <CardContent className="p-0">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Loading jobs...</p>
                  </div>
                </div>
              ) : jobs.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">No jobs found</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() =>
                      setState((prev) => ({ ...prev, showJobForm: true }))
                    }
                  >
                    Create your first job
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={allSelected}
                          onCheckedChange={handleSelectAll}
                          aria-label="Select all jobs"
                        />
                      </TableHead>
                      <TableHead>Job Title</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Applications</TableHead>
                      <TableHead>Days Open</TableHead>
                      <TableHead className="w-12"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {jobs.map((job) => (
                      <TableRow key={job.id}>
                        <TableCell>
                          <Checkbox
                            checked={state.selectedJobs.has(job.id)}
                            onCheckedChange={(checked) =>
                              handleSelectJob(job.id, !!checked)
                            }
                            aria-label={`Select ${job.title}`}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{job.title}</div>
                            <div className="text-sm text-muted-foreground">
                              {JobUtils.formatEmploymentType(
                                job.employmentType,
                              )}{" "}
                              • {JobUtils.formatRemoteType(job.remoteType)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{job.department}</TableCell>
                        <TableCell>
                          {JobUtils.formatLocation(
                            job.location,
                            job.remoteType,
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={JobUtils.getStatusColor(job.status)}
                          >
                            {job.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {job.priority && (
                            <Badge
                              className={JobUtils.getPriorityColor(
                                job.priority,
                              )}
                            >
                              {job.priority}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{job.applicationCount || 0}</TableCell>
                        <TableCell>{job.daysOpen || 0} days</TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => handleJobAction(job, "view")}
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleJobAction(job, "edit")}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Job
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleJobAction(job, "duplicate")
                                }
                              >
                                <Copy className="mr-2 h-4 w-4" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {job.status === "draft" && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleJobAction(job, "publish")
                                  }
                                >
                                  Publish Job
                                </DropdownMenuItem>
                              )}
                              {job.status === "published" && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleJobAction(job, "unpublish")
                                  }
                                >
                                  Unpublish Job
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem
                                onClick={() => handleJobAction(job, "close")}
                              >
                                Close Job
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleJobAction(job, "archive")}
                              >
                                Archive Job
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleJobAction(job, "share")}
                              >
                                <ExternalLink className="mr-2 h-4 w-4" />
                                Share Job
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleJobAction(job, "copy-link")
                                }
                              >
                                Copy Link
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleJobAction(job, "delete")}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Job
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Modals */}
          <JobFormModal
            isOpen={state.showJobForm}
            onClose={() =>
              setState((prev) => ({
                ...prev,
                showJobForm: false,
                editingJob: null,
              }))
            }
            job={null} // TODO: Fetch full job data for editing
            onSubmit={state.editingJob ? handleUpdateJob : handleCreateJob}
          />

          <JobDetailModal
            isOpen={state.showJobDetail}
            onClose={() =>
              setState((prev) => ({
                ...prev,
                showJobDetail: false,
                viewingJob: null,
              }))
            }
            job={null} // TODO: Fetch full job data for viewing
            onEdit={() => {}} // TODO: Implement edit handler
            onDuplicate={() => {}} // TODO: Implement duplicate handler
          />

          <DeleteJobModal
            isOpen={state.showDeleteConfirm}
            onClose={() =>
              setState((prev) => ({
                ...prev,
                showDeleteConfirm: false,
                deletingJob: null,
              }))
            }
            job={state.deletingJob as any} // Type assertion for now
            onConfirm={handleDeleteJob}
          />
        </div>
      </Layout>
    </ProtectedRoute>
  );
};

export default JobsPage;
