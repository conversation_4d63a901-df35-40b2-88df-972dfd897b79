import { useState, useMemo } from "react";
import { Layout } from "@/components/layout/Layout";
import {
  useJobs,
  useCreateJob,
  useUpdateJob,
  useDeleteJob,
} from "@/hooks/useApi";
import { jobAdapters, filterAdapters } from "@/lib/adapters/index";
import { ProtectedRoute } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AddEditJobModal } from "@/components/jobs/AddEditJobModal";
import { DeleteJobModal } from "@/components/jobs/DeleteJobModal";
import { BulkJobActions } from "@/components/jobs/BulkJobActions";
import { JobDetailModal } from "@/components/jobs/JobDetailModal";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  MapPin,
  DollarSign,
  Users,
  Calendar,
  Building2,
  Edit,
  Eye,
  Pause,
  Play,
  X,
  Copy,
  Download,
  Upload,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Trash2,
  Sparkles,
  FileText,
  TableIcon,
} from "lucide-react";
import { Job, mockCandidates } from "@/data/mockData";
import { useTranslation } from "@/lib/i18n";

const getStatusColor = (status: Job["status"]) => {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800";
    case "paused":
      return "bg-yellow-100 text-yellow-800";
    case "closed":
      return "bg-red-100 text-red-800";
    case "draft":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getPriorityColor = (priority: Job["priority"]) => {
  switch (priority) {
    case "high":
      return "bg-red-100 text-red-800";
    case "medium":
      return "bg-yellow-100 text-yellow-800";
    case "low":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getTypeColor = (type: Job["type"]) => {
  switch (type) {
    case "full-time":
      return "bg-blue-100 text-blue-800";
    case "part-time":
      return "bg-purple-100 text-purple-800";
    case "contract":
      return "bg-orange-100 text-orange-800";
    case "remote":
      return "bg-teal-100 text-teal-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function Jobs() {
  const { t } = useTranslation();
  const { toast } = useToast();

  // Local state
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [departmentFilter, setDepartmentFilter] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"list" | "table" | "grid">("table");
  const [sortBy, setSortBy] = useState<string>("postedDate");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [selectedJobs, setSelectedJobs] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);

  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedJob, setSelectedJob] = useState<any>(null);

  // API hooks
  const filters = {
    search: searchTerm,
    status: statusFilter === "all" ? "" : statusFilter,
    department: departmentFilter === "all" ? "" : departmentFilter,
  };

  const {
    data: jobsData,
    isLoading,
    error,
    refetch,
  } = useJobs({
    page: currentPage,
    ...filterAdapters.jobFilters(filters),
  });

  const createJobMutation = useCreateJob();
  const updateJobMutation = useUpdateJob();
  const deleteJobMutation = useDeleteJob();

  // Transform API data
  const jobs = jobsData ? jobAdapters.fromPaginatedApi(jobsData).jobs : []; // Fallback to mock data

  const pagination = jobsData
    ? jobAdapters.fromPaginatedApi(jobsData).pagination
    : null;

  // Get unique departments for filter
  const uniqueDepartments = Array.from(
    new Set(jobs.map((j: any) => j.department)),
  );

  // Filter jobs based on search and filters
  const filteredJobs = useMemo(() => {
    return jobs.filter((job) => {
      const matchesSearch =
        job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.hiringManager.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus =
        statusFilter === "all" || job.status === statusFilter;

      const matchesDepartment =
        departmentFilter === "all" || job.department === departmentFilter;

      return matchesSearch && matchesStatus && matchesDepartment;
    });
  }, [searchTerm, statusFilter, departmentFilter, jobs]);

  // Sort jobs
  const sortedJobs = useMemo(() => {
    const sorted = [...filteredJobs].sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case "title":
          aValue = a.title;
          bValue = b.title;
          break;
        case "postedDate":
          aValue = new Date(a.postedDate);
          bValue = new Date(b.postedDate);
          break;
        case "applicantCount":
          aValue = a.applicantCount;
          bValue = b.applicantCount;
          break;
        case "status":
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          aValue = a.postedDate;
          bValue = b.postedDate;
      }

      if (typeof aValue === "string") {
        return sortOrder === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
    });

    return sorted;
  }, [filteredJobs, sortBy, sortOrder]);

  // CRUD handlers
  const handleCreateJob = async (jobData: any) => {
    try {
      const apiJobData = jobAdapters.toApi(jobData);
      await createJobMutation.mutateAsync(apiJobData);
      toast({
        title: "Thành công",
        description: "Công việc đã được tạo thành công",
      });
      setIsAddModalOpen(false);
      refetch();
    } catch (error: any) {
      // Let the modal handle the error display, just re-throw
      throw error;
    }
  };

  const handleUpdateJob = async (jobData: any) => {
    try {
      const apiJobData = jobAdapters.toApi(jobData);

      await updateJobMutation.mutateAsync({
        id: selectedJob.id,
        data: apiJobData,
      });
      toast({
        title: "Thành công",
        description: "Công việc đã được cập nhật thành công",
      });
      setIsEditModalOpen(false);
      setSelectedJob(null);
      refetch();
    } catch (error: any) {
      // Let the modal handle the error display, just re-throw
      //throw error;
    }
  };

  const handleDeleteJob = async () => {
    if (!selectedJob) return;

    try {
      await deleteJobMutation.mutateAsync(selectedJob.id);
      toast({
        title: "Success",
        description: "Job deleted successfully",
      });
      setIsDeleteModalOpen(false);
      setSelectedJob(null);
      refetch();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete job",
        variant: "destructive",
      });
    }
  };

  // Get candidates count for each job

  const handleJobSelect = (jobId: string, selected: boolean) => {
    if (selected) {
      setSelectedJobs([...selectedJobs, jobId]);
    } else {
      setSelectedJobs(selectedJobs.filter((id) => id !== jobId));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedJobs(sortedJobs.map((j) => j.id));
    } else {
      setSelectedJobs([]);
    }
  };

  const handleBulkAction = (action: string) => {
    const selectedJobsData = jobs.filter((job) =>
      selectedJobs.includes(job.id),
    );

    switch (action) {
      case "export":
        handleExportJobs(selectedJobsData);
        break;
      case "export-detailed":
        handleDetailedExport(selectedJobsData);
        break;
      case "duplicate":
        handleDuplicateJobs(selectedJobsData);
        break;
      case "activate":
        handleBulkStatusChange("active");
        break;
      case "pause":
        handleBulkStatusChange("paused");
        break;
      case "close":
        handleBulkStatusChange("closed");
        break;
      case "delete":
        handleBulkDelete();
        break;
      default:
        toast({
          title: "Info",
          description: `Performing ${action} on ${selectedJobs.length} jobs`,
        });
    }
    setSelectedJobs([]);
  };

  const handleBulkStatusChange = (newStatus: Job["status"]) => {
    // This would typically update via API
    toast({
      title: "Success",
      description: `Updated ${selectedJobs.length} jobs to ${newStatus}`,
    });
  };

  const handleBulkDelete = () => {
    // This would typically delete via API
    toast({
      title: "Success",
      description: `Deleted ${selectedJobs.length} jobs`,
    });
  };

  const handleExportJobs = (jobsToExport: Job[]) => {
    const csvContent = [
      [
        "Title",
        "Department",
        "Location",
        "Status",
        "Applications",
        "Posted Date",
        "Salary Min",
        "Salary Max",
      ].join(","),
      ...jobsToExport.map((job) =>
        [
          job.title,
          job.department,
          job.location,
          job.status,
          job.applicantCount,
          job.postedDate,
          job.salary.min,
          job.salary.max,
        ].join(","),
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `jobs_export_${new Date().toISOString().split("T")[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
    toast({
      title: "Success",
      description: "Jobs exported successfully!",
    });
  };

  const handleDetailedExport = (jobsToExport: Job[]) => {
    const detailedData = jobsToExport.map((job) => ({
      ...job,
      candidates: mockCandidates.filter((c) => c.jobId === job.id),
    }));

    const jsonContent = JSON.stringify(detailedData, null, 2);
    const blob = new Blob([jsonContent], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `jobs_detailed_export_${new Date().toISOString().split("T")[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast({
      title: "Success",
      description: "Detailed export completed!",
    });
  };

  const handleDuplicateJobs = async (jobsToDuplicate: Job[]) => {
    try {
      for (const job of jobsToDuplicate) {
        const duplicatedJobData = {
          ...job,
          title: `${job.title} (Copy)`,
          status: "draft",
          // Remove fields that shouldn't be duplicated
          id: undefined,
          applicantCount: undefined,
          viewCount: undefined,
          postedDate: undefined,
          created_at: undefined,
          updated_at: undefined,
        };

        await createJobMutation.mutateAsync(duplicatedJobData);
      }

      toast({
        title: "Success",
        description: `Duplicated ${jobsToDuplicate.length} job${jobsToDuplicate.length > 1 ? "s" : ""}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate jobs",
        variant: "destructive",
      });
    }
  };

  const handleJobClick = (job: Job) => {
    setSelectedJob(job);
    setIsDetailModalOpen(true);
  };

  const handleEditClick = (job: Job) => {
    setSelectedJob(job);
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = (job: Job) => {
    setSelectedJob(job);
    setIsDeleteModalOpen(true);
  };

  const handleDuplicateJob = (job: Job) => {
    handleDuplicateJobs([job]);
  };

  // Enhanced List Card Component
  const EnhancedJobListCard = ({ job, index }: { job: Job; index: number }) => (
    <Card className="hover:shadow-lg transition-all duration-300 group relative overflow-hidden border-border/40 hover:border-primary/30 bg-gradient-to-r from-card to-card/95">
      <CardContent className="p-6">
        <div className="flex items-center gap-6">
          {/* Checkbox and Status Ring */}
          <div className="flex items-center gap-4">
            <Checkbox
              checked={selectedJobs.includes(job.id)}
              onCheckedChange={(checked) =>
                handleJobSelect(job.id, checked as boolean)
              }
              className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
            />
            <div className="relative">
              <div
                className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-sm ${
                  job.status === "active"
                    ? "bg-emerald-100 text-emerald-700 ring-2 ring-emerald-200"
                    : job.status === "paused"
                      ? "bg-yellow-100 text-yellow-700 ring-2 ring-yellow-200"
                      : job.status === "closed"
                        ? "bg-red-100 text-red-700 ring-2 ring-red-200"
                        : "bg-gray-100 text-gray-700 ring-2 ring-gray-200"
                }`}
              >
                {job.title.charAt(0).toUpperCase()}
              </div>
              {job.priority === "high" && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-2.5 h-2.5 text-white" />
                </div>
              )}
            </div>
          </div>

          {/* Job Title & Position - Column 1 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h3
                className="font-semibold text-lg cursor-pointer hover:text-primary transition-colors truncate group-hover:text-primary"
                onClick={() => handleJobClick(job)}
              >
                {job.title}
              </h3>
              <Badge className={getStatusColor(job.status)} variant="secondary">
                {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
              </Badge>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Building2 className="w-4 h-4" />
              <span>{job.department}</span>
              <span>•</span>
              <span className="font-medium text-foreground">
                {job.hiringManager}
              </span>
            </div>
          </div>

          {/* Location & Salary - Column 2 */}
          <div className="flex-1 min-w-0">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="w-4 h-4 text-muted-foreground" />
                <span className="font-medium">{job.location}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <DollarSign className="w-4 h-4 text-muted-foreground" />
                <span className="font-mono font-medium text-emerald-600">
                  {job.salary?.range}
                </span>
              </div>
            </div>
          </div>

          {/* Type & Requirements - Column 3 */}
          <div className="flex-1 min-w-0">
            <div className="space-y-2">
              <div className="flex flex-wrap gap-1">
                <Badge className={getTypeColor(job.type)} variant="outline">
                  {job.type.replace("-", " ")}
                </Badge>
                <Badge
                  className={getPriorityColor(job.priority)}
                  variant="outline"
                >
                  {job.priority}
                </Badge>
              </div>
              {job.requirements && job.requirements.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {job.requirements.slice(0, 2).map((req, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {req.length > 20 ? req.substring(0, 20) + "..." : req}
                    </Badge>
                  ))}
                  {job.requirements.length > 2 && (
                    <Badge variant="secondary" className="text-xs">
                      +{job.requirements.length - 2}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Stats & Progress - Column 4 */}
          <div className="flex-1 min-w-0">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Users className="w-4 h-4 text-muted-foreground" />
                <span className="font-semibold text-primary">
                  {job.applicantCount}
                </span>
                <span className="text-muted-foreground">applicants</span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Pipeline</span>
                  <span>
                    {Math.round(
                      (job.applicantCount / (job.positions || 1)) * 100,
                    )}
                    %
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-primary to-primary/70 h-2 rounded-full transition-all duration-500"
                    style={{
                      width: `${Math.min((job.applicantCount / (job.positions || 1)) * 100, 100)}%`,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Status & Actions - Column 5 */}
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="text-xs text-muted-foreground mb-1">Posted</div>
              <div className="text-sm font-medium">
                {new Date(job.postedDate).toLocaleDateString()}
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-10 w-10 p-0 rounded-full hover:bg-primary/10 opacity-0 group-hover:opacity-100 transition-all duration-200"
                >
                  <MoreHorizontal className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-56 rounded-xl border-border/50"
              >
                <DropdownMenuLabel className="font-semibold">
                  Job Actions
                </DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => handleJobClick(job)}
                  className="gap-3 cursor-pointer"
                >
                  <Eye className="w-4 h-4" />
                  <div>
                    <div className="font-medium">View Details</div>
                    <div className="text-xs text-muted-foreground">
                      Full job information
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleEditClick(job)}
                  className="gap-3 cursor-pointer"
                >
                  <Edit className="w-4 h-4" />
                  <div>
                    <div className="font-medium">Edit Job</div>
                    <div className="text-xs text-muted-foreground">
                      Modify job details
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleDuplicateJob(job)}
                  className="gap-3 cursor-pointer"
                >
                  <Copy className="w-4 h-4" />
                  <div>
                    <div className="font-medium">Duplicate</div>
                    <div className="text-xs text-muted-foreground">
                      Create a copy
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => handleExportJobs([job])}
                  className="gap-3 cursor-pointer"
                >
                  <Download className="w-4 h-4" />
                  <div>
                    <div className="font-medium">Export</div>
                    <div className="text-xs text-muted-foreground">
                      Download job data
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="gap-3 cursor-pointer text-red-600 focus:text-red-600"
                  onClick={() => handleDeleteClick(job)}
                >
                  <Trash2 className="w-4 h-4" />
                  <div>
                    <div className="font-medium">Delete</div>
                    <div className="text-xs text-muted-foreground">
                      Remove permanently
                    </div>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Table View Component
  const JobTableView = () => (
    <Card className="">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12 p-4">
              <Checkbox
                checked={
                  selectedJobs.length === sortedJobs.length &&
                  sortedJobs.length > 0
                }
                onCheckedChange={handleSelectAll}
                className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
              />
            </TableHead>
            <TableHead className="text-left p-4 font-semibold">
              Job Title
            </TableHead>
            <TableHead className="text-left p-4 font-semibold">
              Department
            </TableHead>
            <TableHead className="text-left p-4 font-semibold">
              Location
            </TableHead>
            <TableHead className="text-left p-4 font-semibold">
              Salary
            </TableHead>
            <TableHead className="text-left p-4 font-semibold">
              Status
            </TableHead>
            <TableHead className="text-left p-4 font-semibold">
              Applicants
            </TableHead>
            <TableHead className="text-left p-4 font-semibold">
              Posted
            </TableHead>
            <TableHead className="text-right p-4 font-semibold">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedJobs.map((job, index) => (
            <TableRow
              key={job.id}
              className={`hover:bg-muted/30 transition-colors group ${
                selectedJobs.includes(job.id)
                  ? "bg-primary/5 border-primary/20"
                  : ""
              }`}
            >
              <TableCell className="p-4">
                <Checkbox
                  checked={selectedJobs.includes(job.id)}
                  onCheckedChange={(checked) =>
                    handleJobSelect(job.id, checked as boolean)
                  }
                  className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
              </TableCell>
              <TableCell className="p-4">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-8 h-8 rounded-lg flex items-center justify-center text-xs font-bold ${
                      job.status === "active"
                        ? "bg-emerald-100 text-emerald-700"
                        : job.status === "paused"
                          ? "bg-yellow-100 text-yellow-700"
                          : job.status === "closed"
                            ? "bg-red-100 text-red-700"
                            : "bg-gray-100 text-gray-700"
                    }`}
                  >
                    {job.title.charAt(0)}
                  </div>
                  <div>
                    <div
                      className="font-medium cursor-pointer hover:text-primary transition-colors"
                      onClick={() => handleJobClick(job)}
                    >
                      {job.title}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {job.hiringManager}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell className="p-4">
                <div className="flex items-center gap-2">
                  <Building2 className="w-4 h-4 text-muted-foreground" />
                  <span className="font-medium">{job.department}</span>
                </div>
              </TableCell>
              <TableCell className="p-4">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span>{job.location}</span>
                </div>
              </TableCell>
              <TableCell className="p-4">
                <div className="font-mono text-sm font-medium text-emerald-600">
                  {job.salary?.range}
                </div>
              </TableCell>
              <TableCell className="p-4">
                <Badge
                  className={getStatusColor(job.status)}
                  variant="secondary"
                >
                  {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                </Badge>
              </TableCell>
              <TableCell className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-muted-foreground" />
                  <span className="font-semibold text-primary">
                    {job.applicantCount}
                  </span>
                </div>
              </TableCell>
              <TableCell className="p-4">
                <div className="text-sm">
                  {new Date(job.postedDate).toLocaleDateString()}
                </div>
              </TableCell>
              <TableCell className="p-4">
                <div className="flex items-center justify-end gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleJobClick(job)}
                    className="h-8 w-8 p-0 hover:bg-primary/10"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditClick(job)}
                    className="h-8 w-8 p-0 hover:bg-primary/10"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-primary/10"
                      >
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="w-48 rounded-xl"
                    >
                      <DropdownMenuItem onClick={() => handleDuplicateJob(job)}>
                        <Copy className="w-4 h-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleExportJobs([job])}>
                        <Download className="w-4 h-4 mr-2" />
                        Export
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteClick(job)}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Card>
  );

  // Original Grid Card (kept for compatibility)
  const JobCard = ({ job }: { job: Job }) => (
    <Card className="hover:shadow-md transition-all duration-200 group relative overflow-hidden">
      {/* AI Enhancement Indicator */}
      {job.priority === "high" && (
        <div className="absolute top-2 right-2 z-10">
          <div className="p-1 bg-primary/20 rounded-full">
            <Sparkles className="w-3 h-3 text-primary" />
          </div>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3 flex-1">
            <Checkbox
              checked={selectedJobs.includes(job.id)}
              onCheckedChange={(checked) =>
                handleJobSelect(job.id, checked as boolean)
              }
            />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <CardTitle
                  className="text-lg hover:text-primary cursor-pointer transition-colors"
                  onClick={() => handleJobClick(job)}
                >
                  {job.title}
                </CardTitle>
                <Badge className={getStatusColor(job.status)}>
                  {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                </Badge>
              </div>
              <CardDescription className="flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                {job.department}
              </CardDescription>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0 rounded-xl">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="rounded-xl">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleJobClick(job)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditClick(job)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Job
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDuplicateJob(job)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {job.status === "active" ? (
                <DropdownMenuItem
                  onClick={() => handleBulkStatusChange("paused")}
                >
                  <Pause className="mr-2 h-4 w-4" />
                  Pause Job
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem
                  onClick={() => handleBulkStatusChange("active")}
                >
                  <Play className="mr-2 h-4 w-4" />
                  Activate Job
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => handleExportJobs([job])}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => handleDeleteClick(job)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <Badge className={getTypeColor(job.type)}>
            {job.type.replace("-", " ")}
          </Badge>
          <Badge className={getPriorityColor(job.priority)}>
            {job.priority} priority
          </Badge>
        </div>

        <div className="space-y-2 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            <span>{job.location}</span>
          </div>
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            <span>
              {job.salary?.currency}{" "}
              {job.salary?.min?.toLocaleString() || "N/A"} -{" "}
              {job.salary?.max?.toLocaleString() || "N/A"}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>{job.applicantCount} candidates applied</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <span>Posted {new Date(job.postedDate).toLocaleDateString()}</span>
          </div>
        </div>

        <div className="space-y-2">
          <p className="text-sm font-medium">Hiring Manager</p>
          <p className="text-sm text-muted-foreground">{job.hiringManager}</p>
        </div>

        <div className="space-y-2">
          <p className="text-sm font-medium">Description</p>
          <p className="text-sm text-muted-foreground line-clamp-2">
            {job.description}
          </p>
        </div>

        {job.requirements && job.requirements.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Key Requirements</p>
            <div className="flex flex-wrap gap-1">
              {job.requirements.slice(0, 2).map((req, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {req.length > 30 ? req.substring(0, 30) + "..." : req}
                </Badge>
              ))}
              {job.requirements.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{job.requirements.length - 2} more
                </Badge>
              )}
            </div>
          </div>
        )}

        <div className="pt-2 border-t">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">
              Closes {new Date(job.closingDate).toLocaleDateString()}
            </span>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleJobClick(job)}
                className="rounded-xl"
              >
                View Pipeline
              </Button>
              <Button
                size="sm"
                className="rounded-xl"
                onClick={() => handleJobClick(job)}
              >
                {job.applicantCount} Candidates
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <ProtectedRoute>
      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                {t.jobs.title}
              </h1>
              <p className="text-muted-foreground">{t.jobs.subtitle}</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" className="gap-2 rounded-xl">
                <FileText className="w-4 h-4" />
                {t.pipeline.templates}
              </Button>
              <Button
                variant="outline"
                className="gap-2 rounded-xl"
                onClick={() => handleExportJobs(jobs)}
              >
                <Download className="w-4 h-4" />
                {t.common.export} {t.common.all}
              </Button>
              <Button
                className="ai-button gap-2"
                onClick={() => setIsAddModalOpen(true)}
              >
                <Plus className="w-4 h-4" />
                {t.jobs.addJob}
              </Button>
            </div>
          </div>

          {/* Enhanced Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/20 rounded-xl">
                    <Building2 className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t.common.total} {t.jobs.title}
                    </p>
                    <p className="text-2xl font-bold">{jobs.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="border-emerald-500/20 bg-gradient-to-br from-emerald-500/5 to-green-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-emerald-500/20 rounded-xl">
                    <Play className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t.common.active}
                    </p>
                    <p className="text-2xl font-bold">
                      {jobs.filter((j) => j.status === "active").length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-indigo-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-xl">
                    <Users className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t.common.total} {t.jobs.applicants}
                    </p>
                    <p className="text-2xl font-bold">
                      {jobs.reduce((sum, job) => sum + job.applicantCount, 0)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500/20 rounded-xl">
                    <Calendar className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Trung bình ngày mở
                    </p>
                    <p className="text-2xl font-bold">23</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Bulk Actions */}
          <BulkJobActions
            selectedCount={selectedJobs.length}
            onClearSelection={() => setSelectedJobs([])}
            onBulkAction={handleBulkAction}
          />

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Tìm kiếm việc làm theo tiêu đề, phòng ban, địa điểm hoặc quản lý tuyển dụng..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 rounded-xl border-border/50 focus:border-primary/50"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px] rounded-xl">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent className="rounded-xl">
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={departmentFilter}
              onValueChange={setDepartmentFilter}
            >
              <SelectTrigger className="w-[180px] rounded-xl">
                <SelectValue placeholder="All Departments" />
              </SelectTrigger>
              <SelectContent className="rounded-xl">
                <SelectItem value="all">All Departments</SelectItem>
                {uniqueDepartments.map((department) => (
                  <SelectItem key={department} value={department}>
                    {department}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={
                    selectedJobs.length === sortedJobs.length &&
                    sortedJobs.length > 0
                  }
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm text-muted-foreground">
                  Select all ({sortedJobs.length})
                </span>
              </div>
              <p className="text-sm text-muted-foreground">
                Showing {sortedJobs.length} of {jobs.length} jobs
              </p>
            </div>

            <div className="flex items-center gap-2">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[150px] rounded-xl">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent className="rounded-xl">
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="postedDate">Posted Date</SelectItem>
                  <SelectItem value="applicantCount">Applications</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                }
                className="rounded-xl"
              >
                {sortOrder === "asc" ? (
                  <SortAsc className="h-4 w-4" />
                ) : (
                  <SortDesc className="h-4 w-4" />
                )}
              </Button>

              <div className="flex gap-1 border rounded-xl">
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-xl rounded-r-none"
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "table" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("table")}
                  className="rounded-none"
                >
                  <TableIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-xl rounded-l-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Jobs Views */}
          {sortedJobs.length > 0 ? (
            <div>
              {viewMode === "list" ? (
                <div className="space-y-4">
                  {sortedJobs.map((job, index) => (
                    <EnhancedJobListCard key={job.id} job={job} index={index} />
                  ))}
                </div>
              ) : viewMode === "table" ? (
                <JobTableView />
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {sortedJobs.map((job) => (
                    <JobCard key={job.id} job={job} />
                  ))}
                </div>
              )}
            </div>
          ) : (
            <Card className="border-dashed border-2 border-muted-foreground/25">
              <CardContent className="p-12 text-center">
                <Building2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No jobs found</h3>
                <p className="text-muted-foreground mb-6">
                  Try adjusting your search terms or filters to find jobs.
                </p>
                <div className="flex gap-2 justify-center">
                  <Button
                    className="ai-button gap-2"
                    onClick={() => setIsAddModalOpen(true)}
                  >
                    <Plus className="w-4 h-4" />
                    Create Your First Job
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Modals */}
          <AddEditJobModal
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
            onSubmit={handleCreateJob}
            mode="add"
          />

          <AddEditJobModal
            isOpen={isEditModalOpen}
            onClose={() => {
              setIsEditModalOpen(false);
              setSelectedJob(null);
            }}
            onSubmit={handleUpdateJob}
            job={selectedJob}
            mode="edit"
          />

          <DeleteJobModal
            isOpen={isDeleteModalOpen}
            onClose={() => {
              setIsDeleteModalOpen(false);
              setSelectedJob(null);
            }}
            onConfirm={handleDeleteJob}
            job={selectedJob}
          />

          <JobDetailModal
            job={selectedJob}
            isOpen={isDetailModalOpen}
            onClose={() => {
              setIsDetailModalOpen(false);
              setSelectedJob(null);
            }}
            onEdit={handleEditClick}
            onDuplicate={handleDuplicateJob}
          />
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
