import { useState, useEffect } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Globe,
  Camera,
  Save,
  Shield,
  Bell,
  <PERSON>lette,
  Key,
  Linkedin,
  Twitter,
  Github,
  Calendar,
  Clock,
  Star,
  Award,
  Users,
  TrendingUp,
  Settings,
  Edit,
  Upload,
  Link as LinkIcon,
  Loader2,
  AlertTriangle,
} from "lucide-react";
import { getUserRole, getPermissionLabel } from "@/data/profilesData";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";
import { useCurrentUser } from "@/hooks/useCurrentUser";

export default function Profile() {
  const { t } = useTranslation();
  const { profile, isLoading, error, updateProfile } = useCurrentUser();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(profile);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    if (!formData) return;

    try {
      setIsSaving(true);
      // Note: Since the API only provides basic user info without update endpoint,
      // these changes are saved locally only
      const success = await updateProfile(formData);
      if (success) {
        setIsEditing(false);
        toast.success(
          "Profile updated locally (changes not persisted to server)",
        );
      } else {
        toast.error("Failed to update profile");
      }
    } catch (error) {
      toast.error("An error occurred while updating profile");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setFormData(profile);
    setIsEditing(false);
  };

  // Update formData when profile changes
  useEffect(() => {
    if (profile) {
      setFormData(profile);
    }
  }, [profile]);

  const updatePreference = (key: string, value: boolean) => {
    setFormData((prev) => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [key]: value,
      },
    }));
  };

  const stats = [
    { label: "Interviews Completed", value: "N/A", icon: Calendar },
    { label: "Candidates Hired", value: "N/A", icon: Users },
    { label: "Success Rate", value: "N/A", icon: TrendingUp },
    { label: "Average Rating", value: "N/A", icon: Star },
  ];

  // Loading state
  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-3 text-muted-foreground">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Loading profile...</span>
          </div>
        </div>
      </Layout>
    );
  }

  // Error state
  if (error) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-3">
            <AlertTriangle className="w-12 h-12 text-destructive mx-auto" />
            <h3 className="text-lg font-semibold">Failed to load profile</h3>
            <p className="text-muted-foreground">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Retry
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  // No profile data
  if (!profile || !formData) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-3">
            <User className="w-12 h-12 text-muted-foreground mx-auto" />
            <h3 className="text-lg font-semibold">No profile data</h3>
            <p className="text-muted-foreground">
              Unable to load your profile information
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              {t.profile.title}
            </h1>
            <p className="text-muted-foreground">{t.profile.subtitle}</p>
          </div>
          <div className="flex gap-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  className="rounded-xl"
                >
                  {t.common.cancel}
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="ai-button gap-2"
                >
                  {isSaving ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  {isSaving ? "Saving..." : t.common?.save || "Save"} Changes
                </Button>
              </>
            ) : (
              <Button
                onClick={() => setIsEditing(true)}
                className="ai-button gap-2"
              >
                <Edit className="w-4 h-4" />
                Edit Profile
              </Button>
            )}
          </div>
        </div>

        {/* Profile Overview Card */}
        <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
          <CardContent className="p-6">
            <div className="flex items-start gap-6">
              <div className="relative">
                <Avatar className="w-24 h-24">
                  <AvatarImage src={formData.avatar} />
                  <AvatarFallback className="text-2xl bg-primary/10 text-primary">
                    {formData.initials}
                  </AvatarFallback>
                </Avatar>
                {isEditing && (
                  <Button
                    size="sm"
                    className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0"
                  >
                    <Camera className="w-4 h-4" />
                  </Button>
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h2 className="text-2xl font-bold">{formData.fullName}</h2>
                  <Badge
                    variant="outline"
                    className="text-primary border-primary/30"
                  >
                    {getUserRole(formData.role)}
                  </Badge>
                  {formData.isActive && (
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      Active
                    </Badge>
                  )}
                  {formData.isInterviewer && (
                    <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                      Interviewer
                    </Badge>
                  )}
                </div>
                <p className="text-lg text-muted-foreground mb-2">
                  {formData.title}
                </p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                  <span className="flex items-center gap-1">
                    <Mail className="w-4 h-4" />
                    {formData.email}
                  </span>
                  <span className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {formData.department}
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {getUserRole(formData.role)}
                  </span>
                </div>
                {formData.bio && (
                  <p className="text-muted-foreground mb-4">{formData.bio}</p>
                )}
                <div className="flex flex-wrap gap-2">
                  {formData.skills && formData.skills.length > 0 ? (
                    formData.skills.map((skill) => (
                      <Badge
                        key={skill}
                        variant="secondary"
                        className="rounded-full"
                      >
                        {skill}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">
                      No skills added yet
                    </span>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics - Only show if user is an interviewer or has relevant permissions */}
        {(formData.isInterviewer ||
          formData.permissions.includes("view_analytics")) && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <Card key={stat.label}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/20 rounded-xl">
                        <Icon className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="text-lg font-bold">{stat.value}</p>
                        <p className="text-xs text-muted-foreground">
                          {stat.label}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Tabs */}
        <Tabs defaultValue="personal" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="personal" className="gap-2">
              <User className="w-4 h-4" />
              Personal
            </TabsTrigger>
            <TabsTrigger value="preferences" className="gap-2">
              <Settings className="w-4 h-4" />
              Preferences
            </TabsTrigger>
            <TabsTrigger value="security" className="gap-2">
              <Shield className="w-4 h-4" />
              Security
            </TabsTrigger>
            <TabsTrigger value="permissions" className="gap-2">
              <Key className="w-4 h-4" />
              Permissions
            </TabsTrigger>
          </TabsList>

          {/* Personal Information */}
          <TabsContent value="personal" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Personal Information
                </CardTitle>
                <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                  <p className="font-medium mb-1">📋 Information Source:</p>
                  <p>
                    • <strong>System Data:</strong> Email, Department, Role
                    (read-only)
                  </p>
                  <p>
                    • <strong>Editable Fields:</strong> Name, Title, Location,
                    Phone, Bio, Social Links
                  </p>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          firstName: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          lastName: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      disabled={true}
                      className="rounded-xl bg-muted"
                      title="Email cannot be changed"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={formData.phone || ""}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          phone: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                      placeholder="Phone number not provided by API"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Job Title</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          title: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                      placeholder="Job title not provided by API"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="department">Department</Label>
                    <Input
                      id="department"
                      value={formData.department}
                      disabled={true}
                      className="rounded-xl bg-muted"
                      title="Department is managed by the system"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          location: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="rounded-xl"
                      placeholder="Location not provided by API"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role">Role</Label>
                    <Input
                      id="role"
                      value={getUserRole(formData.role)}
                      disabled={true}
                      className="rounded-xl bg-muted"
                      title="Role is managed by system administrator"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio">Bio</Label>
                  <Textarea
                    id="bio"
                    value={formData.bio || ""}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, bio: e.target.value }))
                    }
                    disabled={!isEditing}
                    className="rounded-xl min-h-[100px]"
                    placeholder="Tell us about yourself..."
                  />
                </div>

                <Separator />

                <div className="space-y-3">
                  <Label>Social Links</Label>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Linkedin className="w-5 h-5 text-blue-600" />
                      <Input
                        placeholder="LinkedIn profile URL"
                        value={formData.socialLinks.linkedin || ""}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            socialLinks: {
                              ...prev.socialLinks,
                              linkedin: e.target.value,
                            },
                          }))
                        }
                        disabled={!isEditing}
                        className="rounded-xl"
                      />
                    </div>
                    <div className="flex items-center gap-3">
                      <Twitter className="w-5 h-5 text-blue-400" />
                      <Input
                        placeholder="Twitter profile URL"
                        value={formData.socialLinks.twitter || ""}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            socialLinks: {
                              ...prev.socialLinks,
                              twitter: e.target.value,
                            },
                          }))
                        }
                        disabled={!isEditing}
                        className="rounded-xl"
                      />
                    </div>
                    <div className="flex items-center gap-3">
                      <Github className="w-5 h-5 text-gray-800" />
                      <Input
                        placeholder="GitHub profile URL"
                        value={formData.socialLinks.github || ""}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            socialLinks: {
                              ...prev.socialLinks,
                              github: e.target.value,
                            },
                          }))
                        }
                        disabled={!isEditing}
                        className="rounded-xl"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Preferences */}
          <TabsContent value="preferences" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="w-5 h-5" />
                    Notifications
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {[
                    {
                      key: "emailNotifications",
                      label: "Email Notifications",
                      desc: "Receive notifications via email",
                    },
                    {
                      key: "pushNotifications",
                      label: "Push Notifications",
                      desc: "Browser push notifications",
                    },
                    {
                      key: "weeklyReports",
                      label: "Weekly Reports",
                      desc: "Weekly activity summaries",
                    },
                    {
                      key: "interviewReminders",
                      label: "Interview Reminders",
                      desc: "Notifications for upcoming interviews",
                    },
                    {
                      key: "candidateUpdates",
                      label: "Candidate Updates",
                      desc: "Updates on candidate status changes",
                    },
                    {
                      key: "systemAlerts",
                      label: "System Alerts",
                      desc: "Important system announcements",
                    },
                  ].map((pref) => (
                    <div
                      key={pref.key}
                      className="flex items-center justify-between"
                    >
                      <div>
                        <Label className="font-medium">{pref.label}</Label>
                        <p className="text-sm text-muted-foreground">
                          {pref.desc}
                        </p>
                      </div>
                      <Switch
                        checked={
                          formData.preferences[
                            pref.key as keyof typeof formData.preferences
                          ] as boolean
                        }
                        onCheckedChange={(checked) =>
                          updatePreference(pref.key, checked)
                        }
                        disabled={!isEditing}
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="w-5 h-5" />
                    Appearance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Theme</Label>
                    <Select
                      value={formData.preferences.theme}
                      onValueChange={(value) =>
                        setFormData((prev) => ({
                          ...prev,
                          preferences: {
                            ...prev.preferences,
                            theme: value as "light" | "dark" | "system",
                          },
                        }))
                      }
                      disabled={!isEditing}
                    >
                      <SelectTrigger className="rounded-xl">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">Compact View</Label>
                      <p className="text-sm text-muted-foreground">
                        Use condensed layouts
                      </p>
                    </div>
                    <Switch
                      checked={formData.preferences.compactView}
                      onCheckedChange={(checked) =>
                        updatePreference("compactView", checked)
                      }
                      disabled={!isEditing}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Security */}
          <TabsContent value="security" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="w-5 h-5" />
                    Password & Authentication
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full rounded-xl">
                    Change Password
                  </Button>
                  <Button variant="outline" className="w-full rounded-xl">
                    Enable Two-Factor Authentication
                  </Button>
                  <Button variant="outline" className="w-full rounded-xl">
                    Manage API Keys
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Account Activity
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm">
                    <p className="font-medium">Last Login</p>
                    <p className="text-muted-foreground">
                      Not available from API
                    </p>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium">Member Since</p>
                    <p className="text-muted-foreground">
                      Not available from API
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground mt-3 p-2 bg-muted/50 rounded">
                    ℹ️ Account activity data is not provided by the current API
                  </div>
                  <Button variant="outline" className="w-full rounded-xl">
                    View Login History
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Permissions */}
          <TabsContent value="permissions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Role & Permissions
                </CardTitle>
                <div className="text-sm text-muted-foreground bg-blue-50 p-3 rounded-lg border border-blue-200">
                  <p className="font-medium mb-1">🔐 System Managed</p>
                  <p>
                    Role and permissions are managed by system administrators
                    and retrieved from the API.
                  </p>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Award className="w-5 h-5 text-primary" />
                  <div>
                    <p className="font-medium">Current Role</p>
                    <p className="text-sm text-muted-foreground">
                      {getUserRole(formData.role)}
                    </p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-3">
                  <Label className="font-medium">Permissions</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {formData.permissions && formData.permissions.length > 0 ? (
                      formData.permissions.map((permission) => (
                        <Badge
                          key={permission}
                          variant="secondary"
                          className="justify-start"
                        >
                          {getPermissionLabel(permission)}
                        </Badge>
                      ))
                    ) : (
                      <p className="text-sm text-muted-foreground col-span-2">
                        No permissions assigned
                      </p>
                    )}
                  </div>
                </div>

                <div className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
                  <p className="font-medium mb-1">Note:</p>
                  <p>
                    Permission changes require administrator approval. Contact
                    your system administrator to modify your role or
                    permissions.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
}
