# Domain Architecture Documentation

This document provides comprehensive guidance on the domain-driven architecture implemented in the HireFlow ATS system.

## 🏗️ Architecture Overview

The domain architecture follows Domain-Driven Design (DDD) principles, organizing code by business domains rather than technical layers. Each domain is self-contained with its own components, hooks, services, types, and utilities.

## 📁 Domain Structure

```
domains/
├── candidates/           # Candidate management domain
├── jobs/                # Job posting and management domain
├── interviews/          # Interview scheduling and management domain
├── calendar/            # Calendar integration domain
├── analytics/           # Analytics and reporting domain
└── shared/              # Shared domain utilities
```

### Individual Domain Structure

Each domain follows a consistent structure:

```
domain-name/
├── components/          # React components specific to this domain
│   ├── ui/             # Basic UI components
│   ├── forms/          # Form components
│   ├── modals/         # Modal components
│   ├── tables/         # Table components
│   └── index.ts        # Barrel exports
├── hooks/              # Custom React hooks
│   ├── queries/        # React Query hooks for data fetching
│   ├── mutations/      # React Query hooks for data mutations
│   ├── forms/          # Form-related hooks
│   └── index.ts        # Barrel exports
├── services/           # API services and business logic
│   ├── api.ts          # API client methods
│   ├── adapters.ts     # Data transformation adapters
│   └── index.ts        # Barrel exports
├── types/              # TypeScript type definitions
│   ├── entities.ts     # Core entity types
│   ├── api.ts          # API request/response types
│   ├── forms.ts        # Form-related types
│   └── index.ts        # Barrel exports
├── utils/              # Domain-specific utilities
│   ├── validation.ts   # Validation schemas
│   ├── formatters.ts   # Data formatting utilities
│   └── index.ts        # Barrel exports
└── index.ts            # Main domain barrel export
```

## 🎯 Domain Responsibilities

### Candidates Domain
- **Purpose**: Manage candidate profiles, applications, and lifecycle
- **Key Components**: CandidateCard, CandidateTable, CandidateModal, CandidateForm
- **Key Hooks**: useCandidates, useCreateCandidate, useUpdateCandidate
- **Key Features**: Search, filtering, bulk actions, AI scoring

### Jobs Domain
- **Purpose**: Manage job postings, requirements, and status
- **Key Components**: JobCard, JobTable, JobModal, JobForm
- **Key Hooks**: useJobs, useCreateJob, useUpdateJob
- **Key Features**: Job posting, status management, requirements tracking

### Interviews Domain
- **Purpose**: Schedule and manage interviews throughout the process
- **Key Components**: InterviewCard, InterviewTable, InterviewModal, InterviewFilters
- **Key Hooks**: useInterviews, useCreateInterview, useUpdateInterview
- **Key Features**: Scheduling, status tracking, feedback collection

### Calendar Domain
- **Purpose**: Calendar integration and event management
- **Key Components**: CalendarView, EventModal, CalendarFilters
- **Key Hooks**: useCalendarEvents, useCreateEvent, useUpdateEvent
- **Key Features**: Multi-view calendar, event management, scheduling

### Analytics Domain
- **Purpose**: Reporting, metrics, and business intelligence
- **Key Components**: MetricCard, ChartComponents, ReportBuilder
- **Key Hooks**: useAnalytics, useMetrics, useReports
- **Key Features**: Dashboard widgets, custom reports, data visualization

## 🔧 Implementation Guidelines

### 1. Component Development

#### Component Naming Convention
```typescript
// ✅ Good - Clear, descriptive names
export const CandidateCard: React.FC<CandidateCardProps> = ({ ... }) => { ... }
export const InterviewScheduleModal: React.FC<InterviewScheduleModalProps> = ({ ... }) => { ... }

// ❌ Bad - Generic or unclear names
export const Card: React.FC<CardProps> = ({ ... }) => { ... }
export const Modal: React.FC<ModalProps> = ({ ... }) => { ... }
```

#### Component Props Interface
```typescript
// ✅ Good - Well-defined props with proper types
interface CandidateCardProps {
  candidate: Candidate;
  onEdit?: (candidate: Candidate) => void;
  onDelete?: (candidateId: string) => void;
  onStatusChange?: (candidateId: string, status: CandidateStatus) => void;
  showActions?: boolean;
  className?: string;
}

// ❌ Bad - Any types or unclear props
interface CardProps {
  data: any;
  onClick?: () => void;
}
```

### 2. Hook Development

#### Query Hooks Pattern
```typescript
// ✅ Good - Consistent query hook pattern
export const useCandidates = (
  filters?: CandidateFilters,
  options?: UseQueryOptions
) => {
  return useQuery({
    queryKey: ['candidates', filters],
    queryFn: () => candidateService.getCandidates(filters),
    ...options,
  });
};
```

#### Mutation Hooks Pattern
```typescript
// ✅ Good - Consistent mutation hook pattern
export const useCreateCandidate = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: candidateService.createCandidate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['candidates'] });
    },
  });
};
```

### 3. Service Development

#### API Service Pattern
```typescript
// ✅ Good - Consistent service pattern
export class CandidateService extends BaseService {
  async getCandidates(filters?: CandidateFilters): Promise<ApiResponse<Candidate[]>> {
    return this.get('/candidates', { params: filters });
  }

  async createCandidate(data: CreateCandidateData): Promise<ApiResponse<Candidate>> {
    return this.post('/candidates', data);
  }

  async updateCandidate(id: string, data: UpdateCandidateData): Promise<ApiResponse<Candidate>> {
    return this.put(`/candidates/${id}`, data);
  }
}

export const candidateService = new CandidateService();
```

### 4. Type Development

#### Entity Types
```typescript
// ✅ Good - Well-defined entity types
export interface Candidate extends BaseEntity {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  
  // Professional Information
  title?: string;
  experience?: number;
  skills: string[];
  
  // Application Information
  status: CandidateStatus;
  source: CandidateSource;
  appliedAt: string;
  
  // Computed fields
  fullName?: string;
  isActive?: boolean;
}
```

## 🧪 Testing Guidelines

### Component Testing
```typescript
// ✅ Good - Comprehensive component test
describe('CandidateCard', () => {
  const mockCandidate: Candidate = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    status: 'applied',
    // ... other required fields
  };

  it('renders candidate information correctly', () => {
    render(<CandidateCard candidate={mockCandidate} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', () => {
    const onEdit = jest.fn();
    render(<CandidateCard candidate={mockCandidate} onEdit={onEdit} />);
    
    fireEvent.click(screen.getByRole('button', { name: /edit/i }));
    expect(onEdit).toHaveBeenCalledWith(mockCandidate);
  });
});
```

### Hook Testing
```typescript
// ✅ Good - Hook test with proper mocking
describe('useCandidates', () => {
  it('fetches candidates successfully', async () => {
    const mockCandidates = [mockCandidate];
    jest.spyOn(candidateService, 'getCandidates').mockResolvedValue({
      data: mockCandidates,
      success: true,
    });

    const { result } = renderHook(() => useCandidates());

    await waitFor(() => {
      expect(result.current.data).toEqual(mockCandidates);
      expect(result.current.isLoading).toBe(false);
    });
  });
});
```

## 📚 Usage Examples

### Creating a New Domain Component

1. **Define the component interface:**
```typescript
// domains/interviews/types/components.ts
export interface InterviewCardProps {
  interview: Interview;
  onEdit?: (interview: Interview) => void;
  onDelete?: (interviewId: string) => void;
  onStatusChange?: (interviewId: string, status: InterviewStatus) => void;
  showActions?: boolean;
}
```

2. **Implement the component:**
```typescript
// domains/interviews/components/InterviewCard.tsx
export const InterviewCard: React.FC<InterviewCardProps> = ({
  interview,
  onEdit,
  onDelete,
  onStatusChange,
  showActions = true,
}) => {
  return (
    <Card className="interview-card">
      <CardHeader>
        <CardTitle>{interview.title}</CardTitle>
        <CardDescription>
          {format(new Date(interview.scheduledAt), 'PPP p')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p><strong>Candidate:</strong> {interview.candidateName}</p>
          <p><strong>Type:</strong> {interview.type}</p>
          <Badge variant={getStatusVariant(interview.status)}>
            {interview.status}
          </Badge>
        </div>
        {showActions && (
          <div className="flex gap-2 mt-4">
            <Button size="sm" onClick={() => onEdit?.(interview)}>
              Edit
            </Button>
            <Button 
              size="sm" 
              variant="destructive" 
              onClick={() => onDelete?.(interview.id)}
            >
              Delete
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
```

3. **Export from domain:**
```typescript
// domains/interviews/components/index.ts
export { InterviewCard } from './InterviewCard';
```

4. **Use in pages:**
```typescript
// pages/Interviews.tsx
import { InterviewCard } from '@/domains/interviews/components';

export default function Interviews() {
  const { data: interviews } = useInterviews();
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {interviews?.map((interview) => (
        <InterviewCard
          key={interview.id}
          interview={interview}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onStatusChange={handleStatusChange}
        />
      ))}
    </div>
  );
}
```

## 🚀 Best Practices

### 1. **Separation of Concerns**
- Keep components focused on presentation
- Move business logic to hooks and services
- Use adapters for data transformation

### 2. **Consistent Naming**
- Use descriptive, domain-specific names
- Follow established patterns across domains
- Maintain consistency in file and folder naming

### 3. **Type Safety**
- Define comprehensive TypeScript interfaces
- Use strict typing throughout the domain
- Avoid `any` types

### 4. **Performance**
- Use React.memo for expensive components
- Implement proper query caching strategies
- Optimize re-renders with proper dependencies

### 5. **Accessibility**
- Include proper ARIA labels
- Ensure keyboard navigation
- Maintain semantic HTML structure

### 6. **Error Handling**
- Implement proper error boundaries
- Handle loading and error states
- Provide meaningful error messages

## 🔄 Migration Guide

When migrating existing components to the domain architecture:

1. **Identify the domain** the component belongs to
2. **Move the component** to the appropriate domain folder
3. **Update imports** to use domain barrel exports
4. **Refactor to use domain hooks** instead of direct API calls
5. **Update types** to use domain-specific interfaces
6. **Add proper error handling** and loading states
7. **Write tests** for the migrated component

## 📖 Additional Resources

- [React Query Documentation](https://tanstack.com/query/latest)
- [TypeScript Best Practices](https://typescript-eslint.io/rules/)
- [Testing Library Documentation](https://testing-library.com/docs/)
- [Domain-Driven Design Principles](https://martinfowler.com/bliki/DomainDrivenDesign.html)
