/**
 * Interview Hooks
 * React hooks for interview operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { InterviewService } from '../services';
import {
  Interview,
  CreateInterviewData,
  UpdateInterviewData,
  InterviewFilters,
  InterviewQueryParams,
  InterviewExportOptions,
  InterviewBulkOperation,
  CreateInterviewFeedbackData,
  UpdateInterviewFeedbackData,
} from '../types';

// Query keys
export const interviewKeys = {
  all: ['interviews'] as const,
  lists: () => [...interviewKeys.all, 'list'] as const,
  list: (params?: InterviewQueryParams) => [...interviewKeys.lists(), params] as const,
  details: () => [...interviewKeys.all, 'detail'] as const,
  detail: (id: string) => [...interviewKeys.details(), id] as const,
  statistics: (filters?: InterviewFilters) => [...interviewKeys.all, 'statistics', filters] as const,
  search: (params: any) => [...interviewKeys.all, 'search', params] as const,
  upcoming: (params?: any) => [...interviewKeys.all, 'upcoming', params] as const,
  calendar: (params: any) => [...interviewKeys.all, 'calendar', params] as const,
  availability: (params: any) => [...interviewKeys.all, 'availability', params] as const,
  feedback: () => [...interviewKeys.all, 'feedback'] as const,
  feedbackList: (params?: any) => [...interviewKeys.feedback(), 'list', params] as const,
  feedbackDetail: (id: string) => [...interviewKeys.feedback(), 'detail', id] as const,
  reminders: (params?: any) => [...interviewKeys.all, 'reminders', params] as const,
};

// Main interviews hook
export function useInterviews(params?: InterviewQueryParams) {
  return useQuery({
    queryKey: interviewKeys.list(params),
    queryFn: () => InterviewService.getInterviews(params),
    enabled: true,
    staleTime: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: false,
  });
}

// Single interview hook
export function useInterview(id: string) {
  return useQuery({
    queryKey: interviewKeys.detail(id),
    queryFn: () => InterviewService.getInterviewById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Interview statistics hook
export function useInterviewStatistics(filters?: InterviewFilters) {
  return useQuery({
    queryKey: interviewKeys.statistics(filters),
    queryFn: () => InterviewService.getStatistics(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Search interviews hook
export function useInterviewSearch(params: {
  query: string;
  filters?: InterviewFilters;
  page?: number;
  limit?: number;
}) {
  return useQuery({
    queryKey: interviewKeys.search(params),
    queryFn: () => InterviewService.searchInterviews(params),
    enabled: !!params.query && params.query.length > 2,
    staleTime: 30 * 1000,
  });
}

// Upcoming interviews hook
export function useUpcomingInterviews(params?: {
  limit?: number;
  date_from?: string;
  interviewer_id?: string;
}) {
  return useQuery({
    queryKey: interviewKeys.upcoming(params),
    queryFn: () => InterviewService.getUpcomingInterviews(params),
    staleTime: 60 * 1000,
    refetchInterval: 2 * 60 * 1000, // Refresh every 2 minutes
  });
}

// Calendar events hook
export function useInterviewCalendarEvents(params: {
  start_date: string;
  end_date: string;
  interviewer_id?: string;
}) {
  return useQuery({
    queryKey: interviewKeys.calendar(params),
    queryFn: () => InterviewService.getCalendarEvents(params),
    enabled: !!(params.start_date && params.end_date),
    staleTime: 30 * 1000,
  });
}

// Availability hook
export function useInterviewAvailability(params: {
  interviewer_id: string;
  start_date: string;
  end_date: string;
  duration?: number;
}) {
  return useQuery({
    queryKey: interviewKeys.availability(params),
    queryFn: () => InterviewService.checkAvailability(params),
    enabled: !!(params.interviewer_id && params.start_date && params.end_date),
    staleTime: 30 * 1000,
  });
}

// Convenience hooks for specific time periods
export function useTodayInterviews(interviewerId?: string) {
  return useQuery({
    queryKey: [...interviewKeys.lists(), 'today', interviewerId],
    queryFn: () => InterviewService.getTodayInterviews(interviewerId),
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000, // Refresh every minute
  });
}

export function useThisWeekInterviews(interviewerId?: string) {
  return useQuery({
    queryKey: [...interviewKeys.lists(), 'week', interviewerId],
    queryFn: () => InterviewService.getThisWeekInterviews(interviewerId),
    staleTime: 60 * 1000,
  });
}

export function useInterviewsByCandidate(candidateId: string) {
  return useQuery({
    queryKey: [...interviewKeys.lists(), 'candidate', candidateId],
    queryFn: () => InterviewService.getInterviewsByCandidate(candidateId),
    enabled: !!candidateId,
    staleTime: 2 * 60 * 1000,
  });
}

export function useInterviewsByJob(jobId: string) {
  return useQuery({
    queryKey: [...interviewKeys.lists(), 'job', jobId],
    queryFn: () => InterviewService.getInterviewsByJob(jobId),
    enabled: !!jobId,
    staleTime: 2 * 60 * 1000,
  });
}

export function useInterviewsByInterviewer(interviewerId: string, dateFrom?: string, dateTo?: string) {
  return useQuery({
    queryKey: [...interviewKeys.lists(), 'interviewer', interviewerId, dateFrom, dateTo],
    queryFn: () => InterviewService.getInterviewsByInterviewer(interviewerId, dateFrom, dateTo),
    enabled: !!interviewerId,
    staleTime: 60 * 1000,
  });
}

export function useInterviewsByStatus(status: string, limit?: number) {
  return useQuery({
    queryKey: [...interviewKeys.lists(), 'status', status, limit],
    queryFn: () => InterviewService.getInterviewsByStatus(status, limit),
    staleTime: 60 * 1000,
  });
}

// Mutation hooks
export function useCreateInterview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateInterviewData) => InterviewService.createInterview(data),
    onSuccess: () => {
      // Invalidate and refetch interviews
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.statistics() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.upcoming() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.calendar() });
    },
  });
}

export function useScheduleInterview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateInterviewData) => InterviewService.scheduleInterview(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.statistics() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.upcoming() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.availability() });
    },
  });
}

export function useUpdateInterview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateInterviewData }) =>
      InterviewService.updateInterview(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.statistics() });
    },
  });
}

export function useRescheduleInterview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      InterviewService.rescheduleInterview(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.availability() });
    },
  });
}

export function useCancelInterview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) =>
      InterviewService.cancelInterview(id, reason),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.statistics() });
    },
  });
}

export function useCompleteInterview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data?: any }) =>
      InterviewService.completeInterview(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.statistics() });
    },
  });
}

export function useMarkInterviewNoShow() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) =>
      InterviewService.markNoShow(id, reason),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.statistics() });
    },
  });
}

export function useDeleteInterview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => InterviewService.deleteInterview(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: interviewKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.statistics() });
    },
  });
}

// Status transition hooks
export function useStartInterview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => InterviewService.startInterview(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
    },
  });
}

export function useEndInterview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, notes }: { id: string; notes?: string }) =>
      InterviewService.endInterview(id, notes),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.statistics() });
    },
  });
}

// Bulk operations
export function useExportInterviews() {
  return useMutation({
    mutationFn: (options: InterviewExportOptions) => InterviewService.exportInterviews(options),
  });
}

export function useImportInterviews() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, options }: { file: File; options?: any }) =>
      InterviewService.importInterviews(file, options),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.all });
    },
  });
}

export function useBulkInterviewOperation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (operation: InterviewBulkOperation) => InterviewService.bulkOperation(operation),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.lists() });
      queryClient.invalidateQueries({ queryKey: interviewKeys.statistics() });
    },
  });
}

// Feedback hooks
export function useInterviewFeedback(params?: any) {
  return useQuery({
    queryKey: interviewKeys.feedbackList(params),
    queryFn: () => InterviewService.getFeedback(params),
    staleTime: 2 * 60 * 1000,
  });
}

export function useInterviewFeedbackById(id: string) {
  return useQuery({
    queryKey: interviewKeys.feedbackDetail(id),
    queryFn: () => InterviewService.getFeedbackById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateInterviewFeedback() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateInterviewFeedbackData) => InterviewService.createFeedback(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.feedback() });
    },
  });
}

export function useUpdateInterviewFeedback() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateInterviewFeedbackData }) =>
      InterviewService.updateFeedback(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.feedbackDetail(id) });
      queryClient.invalidateQueries({ queryKey: interviewKeys.feedback() });
    },
  });
}

export function useDeleteInterviewFeedback() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => InterviewService.deleteFeedback(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: interviewKeys.feedback() });
    },
  });
}

// Reminders hooks
export function useInterviewReminders(params?: any) {
  return useQuery({
    queryKey: interviewKeys.reminders(params),
    queryFn: () => InterviewService.getReminders(params),
    staleTime: 60 * 1000,
  });
}

export function useSendInterviewReminders() {
  return useMutation({
    mutationFn: (params: any) => InterviewService.sendReminders(params),
  });
}

// Utility hooks
export function useInterviewValidation() {
  return {
    validateInterview: (data: CreateInterviewData | UpdateInterviewData) => {
      return InterviewService.validateInterviewData(data);
    },
  };
}

export function useInterviewConflicts() {
  return {
    checkConflicts: async (data: CreateInterviewData | UpdateInterviewData, excludeId?: string) => {
      return InterviewService.checkSchedulingConflicts(data, excludeId);
    },
  };
}

export function useInterviewScheduling() {
  return {
    findAvailableSlots: (params: any) => InterviewService.findAvailableSlots(params),
    suggestRescheduleOptions: (interviewId: string, preferredDates: string[]) =>
      InterviewService.suggestRescheduleOptions(interviewId, preferredDates),
  };
}
