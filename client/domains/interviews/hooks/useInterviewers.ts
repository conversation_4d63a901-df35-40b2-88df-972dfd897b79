/**
 * Interviewer Management Hooks
 * Handles interviewer data fetching and mutations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Interviewer } from '@/lib/types/interviewer';

// Mock API service - replace with actual service when available
const interviewerService = {
  async getInterviewers(params?: {
    include_stats?: boolean;
    department?: string;
    status?: string;
    search?: string;
  }) {
    // Mock implementation - replace with actual API call
    const mockInterviewers: Interviewer[] = [
      {
        id: 1,
        user_id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
        department: 'Engineering',
        expertise: ['JavaScript', 'React', 'Node.js'],
        expertise_list: 'JavaScript, React, Node.js',
        location: 'San Francisco',
        max_interviews_per_day: 3,
        availability: {
          monday: [{ start_time: '09:00', end_time: '17:00' }],
          tuesday: [{ start_time: '09:00', end_time: '17:00' }],
          wednesday: [{ start_time: '09:00', end_time: '17:00' }],
          thursday: [{ start_time: '09:00', end_time: '17:00' }],
          friday: [{ start_time: '09:00', end_time: '17:00' }],
        },
        time_slots: ['09:00-10:00', '10:00-11:00', '11:00-12:00'],
        timezone: 'America/Los_Angeles',
        is_active: true,
        user: {
          id: 1,
          name: 'John Smith',
          email: '<EMAIL>',
          phone: '******-0123',
          initials: 'JS',
          title: 'Senior Software Engineer',
          is_active: true,
        },
        statistics: {
          total_interviews: 45,
          completed_interviews: 42,
          scheduled_interviews: 3,
          upcoming_interviews: 2,
          average_rating: 4.8,
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
      },
      {
        id: 2,
        user_id: 2,
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        department: 'Product',
        expertise: ['Product Management', 'UX Design', 'Analytics'],
        expertise_list: 'Product Management, UX Design, Analytics',
        location: 'New York',
        max_interviews_per_day: 4,
        availability: {
          monday: [{ start_time: '10:00', end_time: '18:00' }],
          tuesday: [{ start_time: '10:00', end_time: '18:00' }],
          wednesday: [{ start_time: '10:00', end_time: '18:00' }],
          thursday: [{ start_time: '10:00', end_time: '18:00' }],
          friday: [{ start_time: '10:00', end_time: '16:00' }],
        },
        time_slots: ['10:00-11:00', '11:00-12:00', '14:00-15:00'],
        timezone: 'America/New_York',
        is_active: true,
        user: {
          id: 2,
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '******-0124',
          initials: 'SJ',
          title: 'Product Manager',
          is_active: true,
        },
        statistics: {
          total_interviews: 32,
          completed_interviews: 30,
          scheduled_interviews: 2,
          upcoming_interviews: 1,
          average_rating: 4.6,
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
      },
    ];

    // Apply filters
    let filteredInterviewers = mockInterviewers;
    
    if (params?.department && params.department !== 'all') {
      filteredInterviewers = filteredInterviewers.filter(
        interviewer => interviewer.department === params.department
      );
    }
    
    if (params?.status && params.status !== 'all') {
      const isActive = params.status === 'active';
      filteredInterviewers = filteredInterviewers.filter(
        interviewer => interviewer.is_active === isActive
      );
    }
    
    if (params?.search) {
      const searchLower = params.search.toLowerCase();
      filteredInterviewers = filteredInterviewers.filter(
        interviewer => 
          interviewer.name.toLowerCase().includes(searchLower) ||
          interviewer.email.toLowerCase().includes(searchLower) ||
          interviewer.expertise_list.toLowerCase().includes(searchLower)
      );
    }

    return {
      data: filteredInterviewers,
      success: true,
    };
  },

  async createInterviewer(data: Partial<Interviewer>) {
    // Mock implementation
    const newInterviewer: Interviewer = {
      id: Date.now(),
      user_id: Date.now(),
      name: data.name || '',
      email: data.email || '',
      department: data.department || '',
      expertise: data.expertise || [],
      expertise_list: data.expertise?.join(', ') || '',
      location: data.location || '',
      max_interviews_per_day: data.max_interviews_per_day || 3,
      availability: data.availability || {},
      time_slots: data.time_slots || [],
      timezone: data.timezone || 'UTC',
      is_active: data.is_active ?? true,
      user: {
        id: Date.now(),
        name: data.name || '',
        email: data.email || '',
        phone: '',
        initials: data.name?.split(' ').map(n => n[0]).join('') || '',
        title: '',
        is_active: true,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    return {
      data: newInterviewer,
      success: true,
    };
  },

  async updateInterviewer(id: number, data: Partial<Interviewer>) {
    // Mock implementation
    return {
      data: { ...data, id, updated_at: new Date().toISOString() },
      success: true,
    };
  },

  async deleteInterviewer(id: number) {
    // Mock implementation
    return {
      success: true,
    };
  },
};

// Query hook for fetching interviewers
export const useInterviewers = (params?: {
  include_stats?: boolean;
  department?: string;
  status?: string;
  search?: string;
}) => {
  return useQuery({
    queryKey: ['interviewers', params],
    queryFn: () => interviewerService.getInterviewers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Mutation hook for creating interviewer
export const useCreateInterviewer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: interviewerService.createInterviewer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['interviewers'] });
      toast.success('Interviewer created successfully');
    },
    onError: () => {
      toast.error('Failed to create interviewer');
    },
  });
};

// Mutation hook for updating interviewer
export const useUpdateInterviewer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<Interviewer> }) =>
      interviewerService.updateInterviewer(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['interviewers'] });
      toast.success('Interviewer updated successfully');
    },
    onError: () => {
      toast.error('Failed to update interviewer');
    },
  });
};

// Mutation hook for deleting interviewer
export const useDeleteInterviewer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: interviewerService.deleteInterviewer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['interviewers'] });
      toast.success('Interviewer deleted successfully');
    },
    onError: () => {
      toast.error('Failed to delete interviewer');
    },
  });
};
