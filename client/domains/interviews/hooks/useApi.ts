/**
 * Interview API Hooks
 * React Query hooks for interview feedback operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { InterviewFeedback } from '../lib/types/interviewFeedback';
import { toast } from 'sonner';

// Mock API functions - replace with actual API calls
const mockApi = {
  async checkFeedbackExists(interviewId: string): Promise<boolean> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return Math.random() > 0.5; // Random for demo
  },

  async getInterviewFeedbackByInterview(interviewId: string): Promise<InterviewFeedback[]> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return [
      {
        id: '1',
        interviewId,
        interviewerId: 'interviewer-1',
        candidateId: 'candidate-1',
        jobId: 'job-1',
        technicalSkills: {
          rating: 4,
          notes: 'Strong technical skills',
          strengths: ['React', 'TypeScript'],
          weaknesses: ['System design'],
        },
        communication: {
          rating: 5,
          notes: 'Excellent communication',
          clarity: 5,
          listening: 4,
          articulation: 5,
        },
        problemSolving: {
          rating: 4,
          notes: 'Good problem solving approach',
          approach: 4,
          creativity: 4,
          analyticalThinking: 4,
        },
        culturalFit: {
          rating: 4,
          notes: 'Good cultural fit',
          teamwork: 4,
          values: 4,
          adaptability: 4,
        },
        overall: {
          score: 8,
          summary: 'Strong candidate overall',
          highlights: ['Technical skills', 'Communication'],
          concerns: ['System design knowledge'],
          nextRound: 'yes',
          recommendation: 'Recommend for hire',
        },
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T11:30:00Z',
        submittedAt: '2024-01-15T11:30:00Z',
        status: 'submitted',
      },
    ];
  },

  async submitInterviewFeedback(feedback: Partial<InterviewFeedback>): Promise<InterviewFeedback> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      ...feedback,
      id: `feedback-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      submittedAt: new Date().toISOString(),
      status: 'submitted',
    } as InterviewFeedback;
  },

  async getInterviewFeedbackDetails(feedbackId: string): Promise<InterviewFeedback> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return {
      id: feedbackId,
      interviewId: 'interview-1',
      interviewerId: 'interviewer-1',
      candidateId: 'candidate-1',
      jobId: 'job-1',
      technicalSkills: {
        rating: 4,
        notes: 'Strong technical skills',
        strengths: ['React', 'TypeScript'],
        weaknesses: ['System design'],
      },
      communication: {
        rating: 5,
        notes: 'Excellent communication',
        clarity: 5,
        listening: 4,
        articulation: 5,
      },
      problemSolving: {
        rating: 4,
        notes: 'Good problem solving approach',
        approach: 4,
        creativity: 4,
        analyticalThinking: 4,
      },
      culturalFit: {
        rating: 4,
        notes: 'Good cultural fit',
        teamwork: 4,
        values: 4,
        adaptability: 4,
      },
      overall: {
        score: 8,
        summary: 'Strong candidate overall',
        highlights: ['Technical skills', 'Communication'],
        concerns: ['System design knowledge'],
        nextRound: 'yes',
        recommendation: 'Recommend for hire',
      },
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T11:30:00Z',
      submittedAt: '2024-01-15T11:30:00Z',
      status: 'submitted',
    };
  },

  async updateInterviewFeedback(feedbackId: string, updates: Partial<InterviewFeedback>): Promise<InterviewFeedback> {
    await new Promise(resolve => setTimeout(resolve, 400));
    const existing = await this.getInterviewFeedbackDetails(feedbackId);
    return {
      ...existing,
      ...updates,
      updatedAt: new Date().toISOString(),
    };
  },

  async deleteInterviewFeedback(feedbackId: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 300));
  },
};

// Query keys
export const interviewFeedbackKeys = {
  all: ['interview-feedback'] as const,
  exists: (interviewId: string) => [...interviewFeedbackKeys.all, 'exists', interviewId] as const,
  byInterview: (interviewId: string) => [...interviewFeedbackKeys.all, 'by-interview', interviewId] as const,
  details: (feedbackId: string) => [...interviewFeedbackKeys.all, 'details', feedbackId] as const,
};

// Hook to check if feedback exists for an interview
export const useFeedbackExists = (interviewId: string) => {
  return useQuery({
    queryKey: interviewFeedbackKeys.exists(interviewId),
    queryFn: () => mockApi.checkFeedbackExists(interviewId),
    enabled: !!interviewId,
  });
};

// Hook to get feedback by interview ID
export const useInterviewFeedbackByInterview = (interviewId: string) => {
  return useQuery({
    queryKey: interviewFeedbackKeys.byInterview(interviewId),
    queryFn: () => mockApi.getInterviewFeedbackByInterview(interviewId),
    enabled: !!interviewId,
  });
};

// Hook to submit interview feedback
export const useSubmitInterviewFeedback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (feedback: Partial<InterviewFeedback>) => mockApi.submitInterviewFeedback(feedback),
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: interviewFeedbackKeys.all });
      if (data.interviewId) {
        queryClient.invalidateQueries({ 
          queryKey: interviewFeedbackKeys.byInterview(data.interviewId) 
        });
        queryClient.invalidateQueries({ 
          queryKey: interviewFeedbackKeys.exists(data.interviewId) 
        });
      }
      toast.success('Feedback submitted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to submit feedback');
    },
  });
};

// Hook to get feedback details
export const useInterviewFeedbackDetails = (feedbackId: string) => {
  return useQuery({
    queryKey: interviewFeedbackKeys.details(feedbackId),
    queryFn: () => mockApi.getInterviewFeedbackDetails(feedbackId),
    enabled: !!feedbackId,
  });
};

// Hook to update interview feedback
export const useUpdateInterviewFeedback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ feedbackId, updates }: { feedbackId: string; updates: Partial<InterviewFeedback> }) =>
      mockApi.updateInterviewFeedback(feedbackId, updates),
    onSuccess: (data) => {
      // Update the cache with new data
      queryClient.setQueryData(interviewFeedbackKeys.details(data.id), data);
      
      // Invalidate related queries
      if (data.interviewId) {
        queryClient.invalidateQueries({ 
          queryKey: interviewFeedbackKeys.byInterview(data.interviewId) 
        });
      }
      toast.success('Feedback updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update feedback');
    },
  });
};

// Hook to delete interview feedback
export const useDeleteInterviewFeedback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (feedbackId: string) => mockApi.deleteInterviewFeedback(feedbackId),
    onSuccess: (_, feedbackId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: interviewFeedbackKeys.details(feedbackId) });
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: interviewFeedbackKeys.all });
      toast.success('Feedback deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete feedback');
    },
  });
};
