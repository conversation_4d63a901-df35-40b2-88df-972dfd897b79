/**
 * Interview Modal Component
 * Modal for viewing and managing interview details
 */

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Calendar as CalendarIcon,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCcw,
  ExternalLink,
  Star,
  Mail,
  MessageSquare,
  FileText,
  Briefcase,
} from 'lucide-react';
import { Interview } from '../types';

export interface InterviewModalProps {
  interview: Interview | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (interview: Interview) => void;
  onDelete?: (interviewId: string) => void;
  onStatusChange?: (interviewId: string, status: string) => void;
  onReschedule?: (interview: Interview) => void;
  onAddFeedback?: (interview: Interview) => void;
  readOnly?: boolean;
}

export const InterviewModal: React.FC<InterviewModalProps> = ({
  interview,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onStatusChange,
  onReschedule,
  onAddFeedback,
  readOnly = false,
}) => {
  if (!interview) return null;

  // Get status display
  const getStatusDisplay = () => {
    switch (interview.status) {
      case 'scheduled':
        return {
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: <Clock className="w-4 h-4" />,
        };
      case 'in-progress':
        return {
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: <AlertCircle className="w-4 h-4" />,
        };
      case 'completed':
        return {
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: <CheckCircle className="w-4 h-4" />,
        };
      case 'cancelled':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: <XCircle className="w-4 h-4" />,
        };
      case 'rescheduled':
        return {
          color: 'bg-orange-100 text-orange-800 border-orange-200',
          icon: <RotateCcw className="w-4 h-4" />,
        };
      case 'no-show':
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <XCircle className="w-4 h-4" />,
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <Clock className="w-4 h-4" />,
        };
    }
  };

  // Get type icon
  const getTypeIcon = () => {
    switch (interview.type) {
      case 'video':
        return <Video className="w-5 h-5" />;
      case 'phone':
        return <Phone className="w-5 h-5" />;
      case 'in-person':
        return <MapPin className="w-5 h-5" />;
      default:
        return <CalendarIcon className="w-5 h-5" />;
    }
  };

  // Format date and time
  const formatDateTime = () => {
    const date = new Date(interview.scheduledAt);
    const dateStr = date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
    const timeStr = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    return `${dateStr} at ${timeStr}`;
  };

  const statusDisplay = getStatusDisplay();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {getTypeIcon()}
            <span>Interview Details</span>
          </DialogTitle>
          <DialogDescription>
            {formatDateTime()}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge className={`${statusDisplay.color} gap-1`}>
                {statusDisplay.icon}
                <span className="capitalize">{interview.status}</span>
              </Badge>
              
              <Badge variant="outline" className="gap-1">
                {getTypeIcon()}
                <span className="capitalize">{interview.type}</span>
              </Badge>

              {interview.priority && (
                <Badge
                  variant={interview.priority === 'high' ? 'destructive' : 'secondary'}
                >
                  {interview.priority} priority
                </Badge>
              )}
            </div>

            {!readOnly && (
              <div className="flex items-center gap-2">
                {interview.meetingUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(interview.meetingUrl, '_blank')}
                    className="gap-2"
                  >
                    <ExternalLink className="w-4 h-4" />
                    Join Meeting
                  </Button>
                )}

                {onEdit && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(interview)}
                    className="gap-2"
                  >
                    <Edit className="w-4 h-4" />
                    Edit
                  </Button>
                )}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Candidate Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Candidate Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={interview.candidateAvatar} />
                    <AvatarFallback>
                      {interview.candidateName
                        .split(' ')
                        .map(n => n[0])
                        .join('')
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-lg">{interview.candidateName}</h3>
                    {interview.candidateRating && (
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm text-muted-foreground">
                          {interview.candidateRating}/5
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {interview.candidateEmail && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <span>{interview.candidateEmail}</span>
                  </div>
                )}

                {interview.candidatePhone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="w-4 h-4 text-muted-foreground" />
                    <span>{interview.candidatePhone}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Job Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Briefcase className="w-5 h-5" />
                  Job Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">{interview.jobTitle}</h3>
                  {interview.jobDepartment && (
                    <p className="text-sm text-muted-foreground">{interview.jobDepartment}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Round:</span>
                    <p className="font-medium">{interview.round || 'General'}</p>
                  </div>
                  
                  {interview.duration && (
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <p className="font-medium">{interview.duration} minutes</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Interview Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarIcon className="w-5 h-5" />
                Interview Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <span className="text-sm text-muted-foreground">Date & Time:</span>
                  <p className="font-medium">{formatDateTime()}</p>
                </div>

                <div>
                  <span className="text-sm text-muted-foreground">Type:</span>
                  <div className="flex items-center gap-2">
                    {getTypeIcon()}
                    <span className="font-medium capitalize">{interview.type}</span>
                  </div>
                </div>

                {interview.location && (
                  <div>
                    <span className="text-sm text-muted-foreground">Location:</span>
                    <p className="font-medium">{interview.location}</p>
                  </div>
                )}

                {interview.interviewerName && (
                  <div>
                    <span className="text-sm text-muted-foreground">Interviewer:</span>
                    <p className="font-medium">{interview.interviewerName}</p>
                  </div>
                )}

                {interview.interviewerEmail && (
                  <div>
                    <span className="text-sm text-muted-foreground">Interviewer Email:</span>
                    <p className="font-medium">{interview.interviewerEmail}</p>
                  </div>
                )}
              </div>

              {interview.notes && (
                <div>
                  <span className="text-sm text-muted-foreground">Notes:</span>
                  <div className="mt-2 p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm whitespace-pre-wrap">{interview.notes}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Status Actions */}
          {!readOnly && onStatusChange && (
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {interview.status === 'scheduled' && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onStatusChange(interview.id, 'in-progress')}
                        className="gap-2"
                      >
                        <AlertCircle className="w-4 h-4" />
                        Start Interview
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onStatusChange(interview.id, 'completed')}
                        className="gap-2"
                      >
                        <CheckCircle className="w-4 h-4" />
                        Mark Complete
                      </Button>
                      {onReschedule && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onReschedule(interview)}
                          className="gap-2"
                        >
                          <RotateCcw className="w-4 h-4" />
                          Reschedule
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onStatusChange(interview.id, 'cancelled')}
                        className="gap-2"
                      >
                        <XCircle className="w-4 h-4" />
                        Cancel
                      </Button>
                    </>
                  )}

                  {interview.status === 'in-progress' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onStatusChange(interview.id, 'completed')}
                      className="gap-2"
                    >
                      <CheckCircle className="w-4 h-4" />
                      Complete Interview
                    </Button>
                  )}

                  {interview.status === 'completed' && onAddFeedback && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onAddFeedback(interview)}
                      className="gap-2"
                    >
                      <MessageSquare className="w-4 h-4" />
                      Add Feedback
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          <Separator />

          {/* Footer */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div>
              Created: {new Date(interview.createdAt).toLocaleDateString()}
              {interview.updatedAt !== interview.createdAt && (
                <span className="ml-2">
                  • Updated: {new Date(interview.updatedAt).toLocaleDateString()}
                </span>
              )}
            </div>

            {!readOnly && onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(interview.id)}
                className="gap-2 text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
                Delete Interview
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
