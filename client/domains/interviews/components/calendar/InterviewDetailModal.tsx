import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Calendar,
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Mail,
  MessageSquare,
  Edit,
  Trash2,
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCcw,
} from "lucide-react";
import { Interview } from "@/domains/candidates/types";
import { toast } from "sonner";

interface InterviewDetailModalProps {
  interview: Interview | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (interview: Interview) => void;
  onStatusChange: (interviewId: string, newStatus: Interview["status"]) => void;
}

export const InterviewDetailModal = ({
  interview,
  isOpen,
  onClose,
  onEdit,
  onStatusChange,
}: InterviewDetailModalProps) => {
  if (!interview) return null;

  // Ensure required arrays are properly initialized
  const safeInterview = {
    ...interview,
    interviewerExpertise: Array.isArray(interview.interviewerExpertise)
      ? interview.interviewerExpertise
      : [],
    agenda: Array.isArray(interview.agenda) ? interview.agenda : [],
  };

  const getStatusIcon = (status: Interview["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case "cancelled":
        return <XCircle className="w-5 h-5 text-red-600" />;
      case "rescheduled":
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      default:
        return <Clock className="w-5 h-5 text-blue-600" />;
    }
  };

  const getTypeIcon = (type: Interview["type"]) => {
    switch (type) {
      case "video":
        return <Video className="w-5 h-5 text-blue-600" />;
      case "phone":
        return <Phone className="w-5 h-5 text-green-600" />;
      case "in-person":
        return <MapPin className="w-5 h-5 text-purple-600" />;
      default:
        return <User className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status: Interview["status"]) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "rescheduled":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  const handleStatusChange = (newStatus: Interview["status"]) => {
    onStatusChange(interview.id, newStatus);
    toast.success(`Interview ${newStatus} successfully`);
  };

  const handleJoinMeeting = () => {
    if (interview.meetingLink) {
      window.open(interview.meetingLink, "_blank");
    } else {
      toast.error("No meeting link available");
    }
  };

  const formatDateTime = (date: string, time: string) => {
    try {
      const dateObj = new Date(`${date}T${time}`);
      if (isNaN(dateObj.getTime())) {
        throw new Error("Invalid date");
      }
      return {
        date: dateObj.toLocaleDateString("vi-VN", {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        }),
        time: dateObj.toLocaleTimeString("vi-VN", {
          hour: "numeric",
          minute: "2-digit",
          hour12: false,
        }),
      };
    } catch (error) {
      console.error("Error formatting date/time:", error, { date, time });
      return {
        date: date || "Chưa xác định",
        time: time || "Chưa xác định",
      };
    }
  };

  const { date, time } = formatDateTime(interview.date, interview.time);
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              {getTypeIcon(interview.type)}
            </div>
            <div>
              <h2 className="text-xl font-bold">Chi tiết phỏng vấn</h2>
              <p className="text-muted-foreground font-normal">
                {interview.candidateName || "Ứng viên"} •{" "}
                {interview.jobTitle || "Vị trí công việc"}
              </p>
              {interview.jobDepartment && (
                <p className="text-xs text-muted-foreground">
                  {interview.jobDepartment}{" "}
                  {interview.jobLocation && `• ${interview.jobLocation}`}
                </p>
              )}
              <p className="text-xs text-primary mt-1">
                ID: {interview.id} • Ngày: {interview.date} • Giờ:{" "}
                {interview.time}
              </p>
            </div>
          </DialogTitle>
          <DialogDescription>
            View and manage interview information and status.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Quick Actions */}
          <div className="flex items-center justify-between">
            <Badge className={`${getStatusColor(interview.status)} text-sm`}>
              <div className="flex items-center gap-2">
                {getStatusIcon(interview.status)}
                {interview.status.charAt(0).toUpperCase() +
                  interview.status.slice(1)}
              </div>
            </Badge>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => onEdit(interview)}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              {interview.type === "video" && interview.meetingLink && (
                <Button size="sm" onClick={handleJoinMeeting} className="gap-2">
                  <Video className="w-4 h-4" />
                  Join Meeting
                </Button>
              )}
            </div>
          </div>

          {/* Interview Information */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Interview Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{date}</p>
                    <p className="text-sm text-muted-foreground">{time}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {getTypeIcon(interview.type)}
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="font-medium">
                        {interview.type.charAt(0).toUpperCase() +
                          interview.type.slice(1)}{" "}
                        Interview
                      </p>
                      {interview.interviewType && (
                        <Badge variant="secondary" className="text-xs">
                          {interview.interviewType}
                        </Badge>
                      )}
                      {interview.round > 1 && (
                        <Badge variant="outline" className="text-xs">
                          Round {interview.round}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {interview.type === "video" && "Video call"}
                      {interview.type === "phone" && "Phone call"}
                      {interview.type === "in-person" &&
                        interview.location &&
                        interview.location}
                      {interview.duration && ` • ${interview.duration} minutes`}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <User className="w-4 h-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{interview.interviewer}</p>
                    <p className="text-sm text-muted-foreground">
                      Interviewer
                      {interview.interviewerDepartment &&
                        ` • ${interview.interviewerDepartment}`}
                    </p>
                    {safeInterview.interviewerExpertise &&
                    safeInterview.interviewerExpertise.length > 0 ? (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {safeInterview.interviewerExpertise
                          .slice(0, 3)
                          .map((skill, index) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="text-xs"
                            >
                              {skill}
                            </Badge>
                          ))}
                        {safeInterview.interviewerExpertise.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{safeInterview.interviewerExpertise.length - 3}{" "}
                            khác
                          </Badge>
                        )}
                      </div>
                    ) : (
                      <p className="text-xs text-muted-foreground mt-1">
                        Chưa có thông tin chuyên môn
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Candidate Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                      {interview.candidateName
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <p className="font-medium">{interview.candidateName}</p>
                      {interview.candidateRating > 0 && (
                        <Badge variant="outline" className="text-xs">
                          ★ {interview.candidateRating}/5
                        </Badge>
                      )}
                      {interview.candidateAiScore > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          AI: {interview.candidateAiScore}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {interview.candidatePosition || interview.jobTitle}
                    </p>
                    {interview.candidateEmail && (
                      <p className="text-xs text-muted-foreground">
                        {interview.candidateEmail}
                      </p>
                    )}
                    {interview.candidatePhone && (
                      <p className="text-xs text-muted-foreground">
                        {interview.candidatePhone}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start gap-2"
                  >
                    <Mail className="w-4 h-4" />
                    Send Email
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start gap-2"
                  >
                    <MessageSquare className="w-4 h-4" />
                    Add Note
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Job Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium">{interview.jobTitle}</p>
                  <p className="text-sm text-muted-foreground">
                    {interview.jobDepartment}
                    {interview.jobLocation && ` • ${interview.jobLocation}`}
                  </p>
                </div>

                {interview.jobSalary && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Salary:</span>
                    <span className="text-sm text-muted-foreground">
                      {interview.jobSalary}
                    </span>
                  </div>
                )}

                {interview.jobPriority && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Priority:</span>
                    <Badge
                      variant={
                        interview.jobPriority === "urgent"
                          ? "destructive"
                          : interview.jobPriority === "high"
                            ? "default"
                            : "secondary"
                      }
                      className="text-xs"
                    >
                      {interview.jobPriority}
                    </Badge>
                  </div>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start gap-2"
                  onClick={() => {
                    // Navigate to job details - you can implement this
                    toast.info("Job details view coming soon");
                  }}
                >
                  <ExternalLink className="w-4 h-4" />
                  View Job Posting
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Meeting Details */}
          {interview.type === "video" && interview.meetingLink && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Meeting Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Video className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Video Conference</p>
                      <p className="text-sm text-muted-foreground">
                        Click to join the meeting
                      </p>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    onClick={handleJoinMeeting}
                    className="gap-2"
                  >
                    <ExternalLink className="w-4 h-4" />
                    Join
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {interview.type === "in-person" && interview.location && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Location</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <MapPin className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="font-medium">In-Person Meeting</p>
                    <p className="text-sm text-muted-foreground">
                      {interview.location}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {interview.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{interview.notes}</p>
              </CardContent>
            </Card>
          )}

          {/* Feedback */}
          {interview.feedback && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Interview Feedback</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Rating:</span>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <div
                        key={star}
                        className={`w-4 h-4 ${
                          star <= interview.feedback!.rating
                            ? "text-yellow-400 fill-current"
                            : "text-gray-300"
                        }`}
                      >
                        ★
                      </div>
                    ))}
                  </div>
                  <span className="text-sm text-muted-foreground">
                    ({interview.feedback.rating}/5)
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium mb-2">Comments:</p>
                  <p className="text-sm text-muted-foreground">
                    {interview.feedback.comments}
                  </p>
                </div>
                <Badge
                  variant={
                    interview.feedback.recommend ? "default" : "secondary"
                  }
                >
                  {interview.feedback.recommend
                    ? "Recommended"
                    : "Not Recommended"}
                </Badge>
              </CardContent>
            </Card>
          )}

          {/* Status Actions */}
          <div className="flex gap-2 pt-4 border-t">
            {interview.status === "scheduled" && (
              <>
                <Button
                  size="sm"
                  onClick={() => handleStatusChange("completed")}
                  className="gap-2"
                >
                  <CheckCircle className="w-4 h-4" />
                  Mark Completed
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleStatusChange("rescheduled")}
                  className="gap-2"
                >
                  <RotateCcw className="w-4 h-4" />
                  Reschedule
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleStatusChange("cancelled")}
                  className="gap-2"
                >
                  <XCircle className="w-4 h-4" />
                  Cancel
                </Button>
              </>
            )}
            {interview.status === "rescheduled" && (
              <Button
                size="sm"
                onClick={() => handleStatusChange("scheduled")}
                className="gap-2"
              >
                <Calendar className="w-4 h-4" />
                Confirm New Time
              </Button>
            )}
            {interview.status === "cancelled" && (
              <Button
                size="sm"
                onClick={() => handleStatusChange("scheduled")}
                className="gap-2"
              >
                <RotateCcw className="w-4 h-4" />
                Reactivate
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
