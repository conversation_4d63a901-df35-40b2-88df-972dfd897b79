import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  CalendarIcon,
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Briefcase,
  Check,
  ChevronsUpDown,
  Wand2,
  Send,
  Plus,
} from "lucide-react";
import { mockCandidates, Interview, Candidate } from "@/domains/candidates/types";
import { getActiveInterviewers } from "@/data/interviewersData";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface ScheduleInterviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSchedule: (interview: Omit<Interview, "id">) => void;
  selectedDate?: Date;
  preSelectedCandidate?: Candidate;
}

const interviewTypes = [
  { value: "video", label: "Video Call", icon: Video, color: "bg-blue-500" },
  { value: "phone", label: "Phone Call", icon: Phone, color: "bg-green-500" },
  {
    value: "in-person",
    label: "In-Person",
    icon: MapPin,
    color: "bg-purple-500",
  },
] as const;

const timeSlots = [
  "08:00",
  "08:30",
  "09:00",
  "09:30",
  "10:00",
  "10:30",
  "11:00",
  "11:30",
  "12:00",
  "12:30",
  "13:00",
  "13:30",
  "14:00",
  "14:30",
  "15:00",
  "15:30",
  "16:00",
  "16:30",
  "17:00",
  "17:30",
  "18:00",
];

const availableInterviewers = getActiveInterviewers();

export function ScheduleInterviewModal({
  isOpen,
  onClose,
  onSchedule,
  selectedDate,
  preSelectedCandidate,
}: ScheduleInterviewModalProps) {
  const [formData, setFormData] = useState({
    candidateId: preSelectedCandidate?.id || "",
    date: selectedDate || new Date(),
    time: "",
    type: "" as Interview["type"] | "",
    interviewer: "",
    location: "",
    meetingLink: "",
    notes: "",
  });

  const [candidateOpen, setCandidateOpen] = useState(false);
  const [interviewerOpen, setInterviewerOpen] = useState(false);
  const [datePickerOpen, setDatePickerOpen] = useState(false);
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);

  const selectedCandidate = mockCandidates.find(
    (c) => c.id === formData.candidateId,
  );

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        candidateId: preSelectedCandidate?.id || "",
        date: selectedDate || new Date(),
        time: "",
        type: "" as Interview["type"] | "",
        interviewer: "",
        location: "",
        meetingLink: "",
        notes: "",
      });
    }
  }, [isOpen, selectedDate, preSelectedCandidate]);

  // Auto-generate meeting link for video interviews

  // Auto-suggest location for in-person interviews

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.candidateId ||
      !formData.time ||
      !formData.type ||
      !formData.interviewer
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    const interview: Omit<Interview, "id"> = {
      candidateId: formData.candidateId,
      candidateName: selectedCandidate?.name || "",
      jobTitle: selectedCandidate?.position || "",
      date: format(formData.date, "yyyy-MM-dd"),
      time: formData.time,
      type: formData.type as Interview["type"],
      interviewer: formData.interviewer,
      status: "scheduled",
      location: formData.type === "in-person" ? formData.location : undefined,
      meetingLink: formData.type === "video" ? formData.meetingLink : undefined,
      notes: formData.notes || undefined,
    };

    onSchedule(interview);
    onClose();
    toast.success("Interview scheduled successfully!");
  };

  const sendCalendarInvite = () => {
    toast.success("Calendar invites sent to all participants!");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CalendarIcon className="w-5 h-5 text-primary" />
            Schedule Interview
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Candidate Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Candidate *</Label>
            <Popover open={candidateOpen} onOpenChange={setCandidateOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={candidateOpen}
                  className="w-full justify-between rounded-xl"
                >
                  {selectedCandidate ? (
                    <div className="flex items-center gap-3">
                      <Avatar className="w-6 h-6">
                        <AvatarFallback className="text-xs bg-primary/10">
                          {selectedCandidate.initials}
                        </AvatarFallback>
                      </Avatar>
                      <div className="text-left">
                        <div className="font-medium">
                          {selectedCandidate.name}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {selectedCandidate.position}
                        </div>
                      </div>
                    </div>
                  ) : (
                    "Select candidate..."
                  )}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[400px] p-0">
                <Command>
                  <CommandInput placeholder="Search candidates..." />
                  <CommandList>
                    <CommandEmpty>No candidate found.</CommandEmpty>
                    <CommandGroup>
                      {mockCandidates.map((candidate) => (
                        <CommandItem
                          key={candidate.id}
                          value={candidate.name.toLowerCase()}
                          onSelect={(value) => {
                            setFormData((prev) => ({
                              ...prev,
                              candidateId: candidate.id,
                            }));
                            setCandidateOpen(false);
                          }}
                        >
                          <div className="flex items-center gap-3 w-full">
                            <Avatar className="w-8 h-8">
                              <AvatarFallback className="text-xs bg-primary/10">
                                {candidate.initials}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="font-medium">
                                {candidate.name}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {candidate.position}
                              </div>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {candidate.status}
                            </Badge>
                            <Check
                              className={cn(
                                "ml-2 h-4 w-4",
                                formData.candidateId === candidate.id
                                  ? "opacity-100"
                                  : "opacity-0",
                              )}
                            />
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Date *</Label>
              <Popover open={datePickerOpen} onOpenChange={setDatePickerOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal rounded-xl",
                      !formData.date && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.date
                      ? format(formData.date, "PPP")
                      : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={formData.date}
                    onSelect={(date) => {
                      if (date) {
                        setFormData((prev) => ({ ...prev, date }));
                        setDatePickerOpen(false);
                      }
                    }}
                    disabled={(date) => {
                      const today = new Date();
                      today.setHours(0, 0, 0, 0);
                      return date < today;
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Time *</Label>
              <Select
                value={formData.time}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, time: value }))
                }
              >
                <SelectTrigger className="rounded-xl">
                  <SelectValue placeholder="Select time" />
                </SelectTrigger>
                <SelectContent>
                  {timeSlots.map((time) => (
                    <SelectItem key={time} value={time}>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        {time}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Interview Type */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Interview Type *</Label>
            <div className="grid grid-cols-3 gap-3">
              {interviewTypes.map((type) => {
                const Icon = type.icon;
                const isSelected = formData.type === type.value;
                return (
                  <Button
                    key={type.value}
                    type="button"
                    variant={isSelected ? "default" : "outline"}
                    className={cn(
                      "h-20 flex-col gap-2 rounded-xl",
                      isSelected && "ai-button",
                    )}
                    onClick={() =>
                      setFormData((prev) => ({ ...prev, type: type.value }))
                    }
                  >
                    <Icon className="w-5 h-5" />
                    <span className="text-sm">{type.label}</span>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Interviewer */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Interviewer *</Label>
            <Popover open={interviewerOpen} onOpenChange={setInterviewerOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  className="w-full justify-between rounded-xl"
                >
                  {formData.interviewer || "Select interviewer..."}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[400px] p-0">
                <Command>
                  <CommandInput placeholder="Search interviewers..." />
                  <CommandList>
                    <CommandEmpty>No interviewer found.</CommandEmpty>
                    <CommandGroup>
                      {availableInterviewers.map((interviewer) => {
                        const displayName = `${interviewer.name} - ${interviewer.title}`;
                        return (
                          <CommandItem
                            key={interviewer.id}
                            value={displayName.toLowerCase()}
                            onSelect={(value) => {
                              setFormData((prev) => ({
                                ...prev,
                                interviewer: displayName,
                              }));
                              setInterviewerOpen(false);
                            }}
                          >
                            <User className="mr-2 h-4 w-4" />
                            <div className="flex flex-col">
                              <span>{interviewer.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {interviewer.title} • {interviewer.department}
                              </span>
                            </div>
                            <Check
                              className={cn(
                                "ml-auto h-4 w-4",
                                formData.interviewer === displayName
                                  ? "opacity-100"
                                  : "opacity-0",
                              )}
                            />
                          </CommandItem>
                        );
                      })}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          {/* Location/Meeting Link */}
          {formData.type === "in-person" && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Location</Label>
              <Input
                placeholder="Conference room, address, etc."
                value={formData.location}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, location: e.target.value }))
                }
                className="rounded-xl"
              />
            </div>
          )}

          {formData.type === "video" && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Meeting Link</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="https://zoom.us/j/..."
                  value={formData.meetingLink}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      meetingLink: e.target.value,
                    }))
                  }
                  className="rounded-xl"
                />
              </div>
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Notes</Label>
            <Textarea
              placeholder="Additional notes or agenda items..."
              value={formData.notes}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, notes: e.target.value }))
              }
              className="rounded-xl min-h-[80px]"
            />
          </div>

          {/* Actions */}
          <div className="flex justify-between pt-4">
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={sendCalendarInvite}
                className="gap-2 rounded-xl"
              >
                <Send className="w-4 h-4" />
                Send Invite
              </Button>
            </div>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="rounded-xl"
              >
                Cancel
              </Button>
              <Button type="submit" className="ai-button gap-2">
                <Plus className="w-4 h-4" />
                Schedule Interview
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
