import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Alert, AlertDescription } from "../ui/alert";
import { Separator } from "../ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import {
  Calendar,
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Mail,
  MessageSquare,
  Edit,
  Trash2,
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCcw,
  Star,
  Plus,
  Eye,
  FileText,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react";
import { toast } from "sonner";
import { useTranslation } from "../../lib/i18n";
import { Link } from "react-router-dom";
import {
  useFeedbackExists,
  useInterviewFeedbackByInterview,
  useSubmitInterviewFeedback,
} from "../../hooks/useApi";
import {
  InterviewFeedback,
  FeedbackFormData,
  getRatingLabel,
  getRatingColor,
} from "../../lib/types/interviewFeedback";
import { InterviewFeedbackForm } from "./InterviewFeedbackForm";
import { InterviewFeedbackModal } from "./InterviewFeedbackModal";

// Basic interview interface (simplified for this component)
interface Interview {
  id: string;
  candidateId: string;
  candidateName: string;
  candidateEmail: string;
  candidateAvatar?: string;
  jobTitle: string;
  date: string;
  time: string;
  duration: number;
  type: "video" | "phone" | "in-person";
  interviewType: string;
  interviewer: string;
  interviewerId: string;
  interviewerEmail: string;
  status: "scheduled" | "completed" | "cancelled" | "rescheduled" | "no-show";
  meetingLink?: string;
  meetingPassword?: string;
  location?: string;
  notes?: string;
  agenda?: string[];
  feedback?: any;
  round: number;
}

interface EnhancedInterviewDetailModalProps {
  interview: Interview | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (interview: Interview) => void;
  onStatusChange: (interviewId: string, newStatus: Interview["status"]) => void;
}

export const EnhancedInterviewDetailModal = ({
  interview,
  isOpen,
  onClose,
  onEdit,
  onStatusChange,
}: EnhancedInterviewDetailModalProps) => {
  const { t } = useTranslation();
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [selectedFeedbackId, setSelectedFeedbackId] = useState<number | null>(
    null,
  );
  const [feedbackModalMode, setFeedbackModalMode] = useState<"view" | "edit">(
    "view",
  );
  const [justCompleted, setJustCompleted] = useState(false);
  const [previousStatus, setPreviousStatus] = useState<string | null>(null);

  // API hooks for feedback
  const {
    data: existingFeedbackData,
    isLoading: isLoadingFeedback,
    refetch: refetchFeedback,
  } = useInterviewFeedbackByInterview(
    interview ? parseInt(interview.id) : 0,
    "interviewer.user",
  );

  const {
    data: feedbackExists,
    isLoading: isCheckingFeedback,
    refetch: recheckFeedback,
  } = useFeedbackExists(
    interview ? parseInt(interview.id) : 0,
    interview ? parseInt(interview.interviewerId) : 0,
  );

  const submitFeedbackMutation = useSubmitInterviewFeedback();

  const existingFeedback = existingFeedbackData?.data?.[0];

  // Monitor status changes to auto-trigger feedback form
  useEffect(() => {
    if (!interview) return;

    // If interview just changed to completed and no feedback exists yet
    if (
      interview.status === "completed" &&
      previousStatus &&
      previousStatus !== "completed" &&
      !feedbackExists &&
      !isCheckingFeedback
    ) {
      setJustCompleted(true);
      // Auto-show feedback form after a brief delay
      setTimeout(() => {
        setShowFeedbackForm(true);
      }, 1000);
    }

    setPreviousStatus(interview.status);
  }, [interview?.status, feedbackExists, isCheckingFeedback, previousStatus]);

  // Reset form state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setShowFeedbackForm(false);
      setShowFeedbackModal(false);
      setJustCompleted(false);
      setSelectedFeedbackId(null);
      setFeedbackModalMode("view");
    }
  }, [isOpen]);

  if (!interview) return null;
  const safeInterview = {
    ...interview,
    agenda: Array.isArray(interview.agenda) ? interview.agenda : [],
  };

  const getStatusIcon = (status: Interview["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case "cancelled":
        return <XCircle className="w-5 h-5 text-red-600" />;
      case "rescheduled":
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      default:
        return <Clock className="w-5 h-5 text-blue-600" />;
    }
  };

  const getTypeIcon = (type: Interview["type"]) => {
    switch (type) {
      case "video":
        return <Video className="w-5 h-5 text-blue-600" />;
      case "phone":
        return <Phone className="w-5 h-5 text-green-600" />;
      case "in-person":
        return <MapPin className="w-5 h-5 text-purple-600" />;
      default:
        return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };

  const handleStatusChange = async (newStatus: Interview["status"]) => {
    try {
      await onStatusChange(interview.id, newStatus);
      toast.success(`Interview marked as ${newStatus}`);
    } catch (error) {
      toast.error("Failed to update interview status");
    }
  };

  const handleSubmitFeedback = async (data: FeedbackFormData) => {
    try {
      const feedbackData = {
        interview_id: parseInt(interview.id),
        interviewer_id: parseInt(interview.interviewerId),
        ...data,
      };

      await submitFeedbackMutation.mutateAsync(feedbackData);
      toast.success(t.toast?.success?.sent || "Sent successfully!");
      setShowFeedbackForm(false);
      setJustCompleted(false);
      refetchFeedback();
      recheckFeedback();
    } catch (error: any) {
      console.error("Error submitting feedback:", error);
      toast.error(
        error.message || t.toast?.error?.failed || "Operation failed!",
      );
    }
  };

  const handleFeedbackUpdated = () => {
    refetchFeedback();
    recheckFeedback();
  };

  const handleFeedbackDeleted = () => {
    refetchFeedback();
    recheckFeedback();
    setShowFeedbackModal(false);
  };

  const handleViewFeedback = (feedbackId: number) => {
    setSelectedFeedbackId(feedbackId);
    setFeedbackModalMode("view");
    setShowFeedbackModal(true);
  };

  const handleEditFeedback = (feedbackId: number) => {
    setSelectedFeedbackId(feedbackId);
    setFeedbackModalMode("edit");
    setShowFeedbackModal(true);
  };

  const canEditFeedback = (feedback: any) => {
    // Check if current user is the interviewer who created this feedback
    // This would typically come from auth context or user context
    if (!interview || !feedback) return false;

    // Allow editing if the current interviewer matches the feedback creator
    // In a real app, you'd compare with the current authenticated user
    const isOwner =
      interview.interviewerId === feedback.interviewer_id?.toString();

    // Also allow editing if the interview is recent (within 24 hours) and not yet finalized
    const interviewDate = new Date(interview.date);
    const now = new Date();
    const hoursSinceInterview =
      (now.getTime() - interviewDate.getTime()) / (1000 * 60 * 60);
    const isRecent = hoursSinceInterview <= 24;

    return isOwner && (isRecent || !feedback.submitted_at);
  };

  const FeedbackSection = () => {
    if (isLoadingFeedback || isCheckingFeedback) {
      return (
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 animate-spin" />
              <span className="text-sm">
                {t.interviewFeedback?.loading || "Loading feedback..."}
              </span>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Check if we have feedback data and it's not empty
    const hasFeedback = existingFeedback && existingFeedback.id;

    if (hasFeedback) {
      const isComplete =
        existingFeedback.rating &&
        existingFeedback.comments &&
        existingFeedback.recommend !== null;
      const canEdit = canEditFeedback(existingFeedback);
      const hasDetailedScores =
        existingFeedback.technical_score ||
        existingFeedback.communication_score ||
        existingFeedback.cultural_fit_score;

      return (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-500" />
              {t.interviewFeedback?.title || "Interview Feedback"}
              <Badge
                variant={isComplete ? "default" : "outline"}
                className="ml-2"
              >
                {isComplete
                  ? t.interviewFeedback?.complete || "Complete"
                  : t.interviewFeedback?.incomplete || "Incomplete"}
              </Badge>
              {canEdit && (
                <div className="ml-auto flex items-center gap-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            handleEditFeedback(existingFeedback.id)
                          }
                          className="flex items-center gap-2"
                        >
                          <Edit className="w-4 h-4" />
                          {t.common?.edit || "Edit"}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>
                          {t.interviewFeedback?.editTooltip ||
                            "Edit your interview feedback"}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              )}
            </CardTitle>
            {existingFeedback.interviewer && (
              <CardDescription>
                {t.interviewFeedback?.submittedBy || "Submitted by"}{" "}
                {existingFeedback.interviewer.name || "Unknown Interviewer"}
              </CardDescription>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`w-4 h-4 ${
                      star <= (existingFeedback.rating || 0)
                        ? "text-yellow-400 fill-current"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-muted-foreground">
                {existingFeedback.rating || "N/A"}/5
              </span>
              {existingFeedback.rating && (
                <Badge
                  variant="outline"
                  className={getRatingColor(existingFeedback.rating)}
                >
                  {getRatingLabel(existingFeedback.rating)}
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              {existingFeedback.recommend === true ? (
                <>
                  <ThumbsUp className="w-4 h-4 text-green-600" />
                  <Badge variant="default" className="bg-green-600">
                    {t.interviewFeedback?.recommended || "Recommended"}
                  </Badge>
                </>
              ) : existingFeedback.recommend === false ? (
                <>
                  <ThumbsDown className="w-4 h-4 text-red-600" />
                  <Badge variant="destructive">
                    {t.interviewFeedback?.notRecommended || "Not Recommended"}
                  </Badge>
                </>
              ) : (
                <>
                  <AlertCircle className="w-4 h-4 text-yellow-600" />
                  <Badge variant="outline">
                    {t.interviewFeedback?.noRecommendation ||
                      "No Recommendation"}
                  </Badge>
                </>
              )}
            </div>

            {existingFeedback.comments && (
              <div>
                <p className="text-sm text-muted-foreground mb-2">
                  {t.interviewFeedback?.comments || "Comments"}:
                </p>
                <p className="text-sm line-clamp-3">
                  {existingFeedback.comments}
                </p>
              </div>
            )}

            {/* Show detailed scores if available */}
            {hasDetailedScores && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">
                  {t.interviewFeedback?.detailedScores || "Detailed Scores"}:
                </p>
                <div className="grid grid-cols-3 gap-2 text-sm">
                  {existingFeedback.technical_score && (
                    <div className="text-center">
                      <div className="font-medium">
                        {existingFeedback.technical_score}%
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {t.interviewFeedback?.technical || "Technical"}
                      </div>
                    </div>
                  )}
                  {existingFeedback.communication_score && (
                    <div className="text-center">
                      <div className="font-medium">
                        {existingFeedback.communication_score}%
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {t.interviewFeedback?.communication || "Communication"}
                      </div>
                    </div>
                  )}
                  {existingFeedback.cultural_fit_score && (
                    <div className="text-center">
                      <div className="font-medium">
                        {existingFeedback.cultural_fit_score}%
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {t.interviewFeedback?.cultureFit || "Culture Fit"}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Show feedback quality indicator */}
            {isComplete && (
              <div className="flex items-center gap-2 pt-2 border-t">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-700 font-medium">
                  {t.interviewFeedback?.completeFeedbackProvided ||
                    "Complete feedback provided"}
                </span>
                {hasDetailedScores && (
                  <Badge variant="outline" className="ml-auto text-xs">
                    {t.interviewFeedback?.detailedAssessment ||
                      "Detailed Assessment"}
                  </Badge>
                )}
              </div>
            )}

            {/* Show completion status and prompt for incomplete feedback */}
            {!isComplete && canEdit && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-800">
                  {t.interviewFeedback?.feedbackIncomplete ||
                    "This feedback is incomplete."}
                </AlertDescription>
              </Alert>
            )}

            <div className="flex items-center gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewFeedback(existingFeedback.id)}
                className="flex items-center gap-2"
              >
                <Eye className="w-4 h-4" />
                {t.interviewFeedback?.viewDetails || "View Details"}
              </Button>
              {canEdit && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditFeedback(existingFeedback.id)}
                        className="flex items-center gap-2"
                      >
                        <Edit className="w-4 h-4" />
                        {t.interviewFeedback?.editFeedback || "Edit Feedback"}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        {t.interviewFeedback?.updateTooltip ||
                          "Update your feedback and ratings"}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </CardContent>
        </Card>
      );
    }

    // Show create feedback section for completed interviews without feedback
    // Also check feedbackExists from the API hook for additional safety
    if (interview.status === "completed" && !hasFeedback && !feedbackExists) {
      return (
        <Card className="border-dashed border-2 border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-600" />
              {t.interviewFeedback?.feedbackRequired || "Feedback Required"}
              <Badge
                variant="outline"
                className="ml-2 border-yellow-600 text-yellow-700"
              >
                {t.interviewFeedback?.actionNeeded || "Action Needed"}
              </Badge>
            </CardTitle>
            <CardDescription>
              {t.interviewFeedback?.noFeedbackYet ||
                "No feedback submitted yet"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {justCompleted && (
              <Alert className="mb-4 border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  {t.interviewFeedback?.interviewCompleted ||
                    "Interview completed!"}
                </AlertDescription>
              </Alert>
            )}
            <div className="flex flex-col gap-3">
              <Button
                onClick={() => setShowFeedbackForm(true)}
                className="flex items-center gap-2 w-full sm:w-auto"
                size="default"
              >
                <Plus className="w-4 h-4" />
                {t.interviewFeedback?.submitFeedback || "Submit Feedback"}
              </Button>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="w-4 h-4" />
                <span>
                  {t.interviewFeedback?.takesMinutes || "Takes 2-3 minutes"}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Show feedback option for other statuses if allowed
    if (interview.status === "scheduled") {
      return (
        <Card className="border-dashed border-muted-foreground/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-muted-foreground">
              <FileText className="w-5 h-5" />
              Interview Feedback
            </CardTitle>
            <CardDescription>
              Feedback can be submitted once the interview is completed.
            </CardDescription>
          </CardHeader>
        </Card>
      );
    }

    // Fallback: if interview is completed but we haven't determined feedback status yet
    if (interview.status === "completed") {
      return (
        <Card className="border-dashed border-2 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="w-5 h-5 text-blue-600" />
              Interview Feedback
              <Badge
                variant="outline"
                className="ml-2 border-blue-600 text-blue-700"
              >
                Ready
              </Badge>
            </CardTitle>
            <CardDescription>
              Interview completed. You can now submit feedback.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-3">
              <Button
                onClick={() => setShowFeedbackForm(true)}
                className="flex items-center gap-2 w-full sm:w-auto"
                size="default"
              >
                <Plus className="w-4 h-4" />
                Submit Feedback
              </Button>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle className="w-4 h-4" />
                <span>
                  {t.interviewFeedback?.helpHiringDecision ||
                    "Help hiring decision"}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    return null;
  };

  if (showFeedbackForm) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {t.interviewFeedback?.submitFeedback || "Submit Feedback"}
            </DialogTitle>
            <DialogDescription>
              Provide feedback for {interview.candidateName}'s{" "}
              {interview.interviewType} interview
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <InterviewFeedbackForm
              interview={{
                id: parseInt(interview.id),
                candidate_name: interview.candidateName,
                job_title: interview.jobTitle,
                date: interview.date,
                time: interview.time,
                interviewer_id: parseInt(interview.interviewerId),
              }}
              onSubmit={handleSubmitFeedback}
              onCancel={() => setShowFeedbackForm(false)}
              isLoading={submitFeedbackMutation.isPending}
            />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {getStatusIcon(interview.status)}
              {t.interviewDetails?.information || "Interview Details"}
            </DialogTitle>
            <DialogDescription>
              {interview.interviewType} interview with {interview.candidateName}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Basic Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t.interviewDetails?.information || "Interview Information"}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={interview.candidateAvatar} />
                    <AvatarFallback>
                      {interview.candidateName
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">
                        {interview.candidateName}
                      </h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                        className="h-6 px-2 text-xs"
                      >
                        <Link
                          to={`/candidates/detail/${interview.candidateId || interview.candidate_id || "unknown"}`}
                        >
                          <ExternalLink className="w-3 h-3 mr-1" />
                          View Profile
                        </Link>
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      <p className="text-sm text-muted-foreground">
                        {t.interviewDetails?.applyingFor || "Applying for"}{" "}
                        {interview.jobTitle}
                      </p>
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                        className="h-5 px-1 text-xs"
                      >
                        <Link
                          to={`/jobs/detail/${interview.jobId || interview.job_id || interview.job_posting_id || "unknown"}`}
                        >
                          <ExternalLink className="w-3 h-3" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{interview.date}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">
                      {interview.time} ({interview.duration}{" "}
                      {t.interviewDetails?.duration || "min"})
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getTypeIcon(interview.type)}
                    <span className="text-sm capitalize">{interview.type}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{interview.interviewer}</span>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    {t.common?.status || "Status"}:
                  </span>
                  <Badge
                    variant={
                      interview.status === "completed"
                        ? "default"
                        : interview.status === "cancelled"
                          ? "destructive"
                          : "secondary"
                    }
                  >
                    {interview.status.charAt(0).toUpperCase() +
                      interview.status.slice(1)}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Meeting Details */}
            {(interview.meetingLink || interview.location) && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {t.interviewDetails?.meetingDetails || "Meeting Details"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {interview.meetingLink && (
                    <div className="flex items-center gap-2">
                      <ExternalLink className="w-4 h-4 text-muted-foreground" />
                      <a
                        href={interview.meetingLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline"
                      >
                        {t.interviewDetails?.joinMeeting || "Join Meeting"}
                      </a>
                    </div>
                  )}
                  {interview.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{interview.location}</span>
                    </div>
                  )}
                  {interview.meetingPassword && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {t.interviewDetails?.password || "Password"}:
                      </span>
                      <code className="text-sm bg-muted px-1 rounded">
                        {interview.meetingPassword}
                      </code>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Agenda */}
            {safeInterview.agenda && safeInterview.agenda.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {t.interviewDetails?.agenda || "Agenda"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {safeInterview.agenda.map((item, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                        <span className="text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Notes */}
            {interview.notes && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {t.interviewDetails?.notes || "Notes"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm whitespace-pre-wrap">
                    {interview.notes}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Feedback Section */}
            <FeedbackSection />

            {/* Status Actions */}
            <div className="flex gap-2 pt-4 border-t">
              {interview.status === "scheduled" && (
                <>
                  <Button
                    size="sm"
                    onClick={() => handleStatusChange("completed")}
                    className="gap-2"
                  >
                    <CheckCircle className="w-4 h-4" />
                    {t.interviewActions?.markCompleted || "Mark Completed"}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleStatusChange("rescheduled")}
                    className="gap-2"
                  >
                    <RotateCcw className="w-4 h-4" />
                    {t.interviewActions?.reschedule || "Reschedule"}
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleStatusChange("cancelled")}
                    className="gap-2"
                  >
                    <XCircle className="w-4 h-4" />
                    {t.interviewActions?.cancel || "Cancel"}
                  </Button>
                </>
              )}
              {interview.status === "rescheduled" && (
                <Button
                  size="sm"
                  onClick={() => handleStatusChange("scheduled")}
                  className="gap-2"
                >
                  <Calendar className="w-4 h-4" />
                  {t.interviewActions?.confirmNewTime || "Confirm New Time"}
                </Button>
              )}
              {interview.status === "cancelled" && (
                <Button
                  size="sm"
                  onClick={() => handleStatusChange("scheduled")}
                  className="gap-2"
                >
                  <RotateCcw className="w-4 h-4" />
                  {t.interviewActions?.reactivate || "Reactivate"}
                </Button>
              )}
              <Button
                size="sm"
                variant="outline"
                onClick={() => onEdit(interview)}
                className="gap-2 ml-auto"
              >
                <Edit className="w-4 h-4" />
                {t.interviewActions?.edit || "Edit"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Enhanced Feedback Modal */}
      {showFeedbackModal && selectedFeedbackId && (
        <InterviewFeedbackModal
          isOpen={showFeedbackModal}
          onClose={() => {
            setShowFeedbackModal(false);
            setSelectedFeedbackId(null);
            setFeedbackModalMode("view");
          }}
          feedbackId={selectedFeedbackId}
          interview={{
            id: parseInt(interview.id),
            candidate_name: interview.candidateName,
            job_title: interview.jobTitle,
            date: interview.date,
            time: interview.time,
            interviewer_id: parseInt(interview.interviewerId),
          }}
          mode={feedbackModalMode}
          onFeedbackUpdated={handleFeedbackUpdated}
          onFeedbackDeleted={handleFeedbackDeleted}
        />
      )}
    </>
  );
};

export default EnhancedInterviewDetailModal;
