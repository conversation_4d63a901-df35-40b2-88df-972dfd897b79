import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Calendar,
  Clock,
  User,
  Star,
  FileText,
  CheckCircle,
  Info,
  Play,
  Code,
  Settings,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

// Import all feedback components
import { InterviewFeedbackForm } from "./InterviewFeedbackForm";
import { InterviewFeedbackModal } from "./InterviewFeedbackModal";
import { InterviewFeedbackManager } from "./InterviewFeedbackManager";
import { EnhancedInterviewDetailModal } from "./EnhancedInterviewDetailModal";
import {
  CandidateActivityTimeline,
  ActivityItem,
  generateSampleActivities,
} from "../candidates/CandidateActivityTimeline";

// Sample data for demonstration
const sampleInterview = {
  id: "1",
  candidateId: "1",
  candidateName: "<PERSON>",
  candidateEmail: "<EMAIL>",
  candidateAvatar: undefined,
  jobTitle: "Senior Frontend Developer",
  date: "2024-02-20",
  time: "14:00",
  duration: 60,
  type: "video" as const,
  interviewType: "technical",
  interviewer: "John Smith",
  interviewerId: "2",
  interviewerEmail: "<EMAIL>",
  status: "completed" as const,
  meetingLink: "https://meet.google.com/abc-def-ghi",
  meetingPassword: "123456",
  location: undefined,
  notes: "Technical interview focusing on React and TypeScript",
  agenda: [
    "Introduction and background",
    "Technical questions on React",
    "Code review exercise",
    "System design discussion",
    "Q&A session",
  ],
  feedback: undefined,
  round: 1,
};

const sampleInterviewForForm = {
  id: 1,
  candidate_name: "Sarah Johnson",
  job_title: "Senior Frontend Developer",
  date: "2024-02-20",
  time: "14:00",
  interviewer_id: 2,
};

/**
 * Comprehensive Integration Component showcasing the complete Interview Feedback System
 *
 * This component demonstrates:
 * 1. Feedback form for creating/editing feedback
 * 2. Feedback modal for viewing detailed feedback
 * 3. Feedback manager for comprehensive feedback management
 * 4. Enhanced interview detail modal with auto-trigger functionality
 * 5. Activity timeline with feedback activities
 * 6. Integration patterns and best practices
 */
export const InterviewFeedbackIntegration: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string>("overview");
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [showInterviewModal, setShowInterviewModal] = useState(false);
  const { toast } = useToast();

  // Demo feedback data
  const [sampleFeedbackId] = useState(1);
  const [sampleActivities] = useState<ActivityItem[]>(
    generateSampleActivities("1"),
  );

  const handleFeedbackSubmit = async (data: any) => {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    toast({
      title: "Success",
      description: "Feedback submitted successfully!",
    });
    setShowFeedbackForm(false);
  };

  const handleInterviewStatusChange = async (id: string, status: string) => {
    toast({
      title: "Status Updated",
      description: `Interview marked as ${status}`,
    });
  };

  const handleInterviewEdit = (interview: any) => {
    toast({
      title: "Edit Interview",
      description: "Opening edit interface...",
    });
  };

  const OverviewTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-500" />
            Interview Feedback System Overview
          </CardTitle>
          <CardDescription>
            Complete feedback management solution for interview processes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="border-dashed">
              <CardContent className="pt-4">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-sm">Form Component</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Comprehensive feedback form with validation, rating system,
                  and detailed scoring
                </p>
              </CardContent>
            </Card>

            <Card className="border-dashed">
              <CardContent className="pt-4">
                <div className="flex items-center gap-2 mb-2">
                  <Settings className="w-4 h-4 text-green-600" />
                  <span className="font-medium text-sm">Manager Component</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Complete feedback management with viewing, editing, and
                  deletion capabilities
                </p>
              </CardContent>
            </Card>

            <Card className="border-dashed">
              <CardContent className="pt-4">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="w-4 h-4 text-purple-600" />
                  <span className="font-medium text-sm">Auto-trigger</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Automatic feedback prompts when interviews are marked as
                  completed
                </p>
              </CardContent>
            </Card>

            <Card className="border-dashed">
              <CardContent className="pt-4">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4 text-orange-600" />
                  <span className="font-medium text-sm">Activity Timeline</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Track feedback activities in candidate timelines and history
                </p>
              </CardContent>
            </Card>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              This system enforces the one-feedback-per-interview constraint and
              provides comprehensive CRUD operations with proper validation and
              error handling.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Key Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                Core Functionality
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Auto-trigger feedback UI on interview completion</li>
                <li>• One feedback per interview constraint enforcement</li>
                <li>• Full CRUD operations with validation</li>
                <li>• Activity timeline integration</li>
                <li>• Real-time feedback status updates</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                Technical Features
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• TypeScript interfaces and validation</li>
                <li>• React Query for state management</li>
                <li>• Comprehensive error handling</li>
                <li>• Optimistic updates</li>
                <li>• Responsive and accessible UI</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const DemoTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Interactive Demo</CardTitle>
          <CardDescription>
            Try out the different feedback components
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={() => setShowFeedbackForm(true)}
              className="h-20 flex-col gap-2"
              variant="outline"
            >
              <FileText className="w-6 h-6" />
              <span>Feedback Form</span>
            </Button>

            <Button
              onClick={() => setShowFeedbackModal(true)}
              className="h-20 flex-col gap-2"
              variant="outline"
            >
              <Star className="w-6 h-6" />
              <span>Feedback Modal</span>
            </Button>

            <Button
              onClick={() => setShowInterviewModal(true)}
              className="h-20 flex-col gap-2"
              variant="outline"
            >
              <Calendar className="w-6 h-6" />
              <span>Enhanced Interview</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Feedback Manager Demo */}
      <InterviewFeedbackManager
        interviewId={1}
        interviewerId={2}
        interview={sampleInterviewForForm}
        className="mb-6"
      />

      {/* Activity Timeline Demo */}
      <CandidateActivityTimeline
        candidateId="1"
        activities={sampleActivities}
        onViewFeedback={(id) =>
          toast({
            title: "View Feedback",
            description: `Opening feedback ${id}`,
          })
        }
        onEditFeedback={(id) =>
          toast({
            title: "Edit Feedback",
            description: `Editing feedback ${id}`,
          })
        }
        onDeleteFeedback={(id) =>
          toast({
            title: "Delete Feedback",
            description: `Deleting feedback ${id}`,
          })
        }
      />
    </div>
  );

  const IntegrationTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="w-5 h-5" />
            Integration Guide
          </CardTitle>
          <CardDescription>
            How to integrate the feedback system into your application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">1. Basic Feedback Form</h4>
              <div className="bg-muted p-3 rounded text-sm font-mono">
                {`<InterviewFeedbackForm
  interview={{
    id: interview.id,
    candidate_name: "John Doe",
    job_title: "Developer",
    date: "2024-02-20",
    time: "14:00",
    interviewer_id: currentUserId
  }}
  onSubmit={handleFeedbackSubmit}
  onCancel={() => setShowForm(false)}
/>`}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">2. Enhanced Interview Modal</h4>
              <div className="bg-muted p-3 rounded text-sm font-mono">
                {`<EnhancedInterviewDetailModal
  interview={interview}
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  onEdit={handleEdit}
  onStatusChange={handleStatusChange}
/>`}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">3. Feedback Manager</h4>
              <div className="bg-muted p-3 rounded text-sm font-mono">
                {`<InterviewFeedbackManager
  interviewId={interview.id}
  interviewerId={currentUserId}
  interview={interview}
  compact={false}
  autoExpandOnComplete={true}
/>`}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">4. Activity Timeline</h4>
              <div className="bg-muted p-3 rounded text-sm font-mono">
                {`<CandidateActivityTimeline
  candidateId={candidate.id}
  activities={candidateActivities}
  onViewFeedback={handleViewFeedback}
  onEditFeedback={handleEditFeedback}
  onDeleteFeedback={handleDeleteFeedback}
/>`}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>API Integration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">React Hooks Usage</h4>
              <div className="bg-muted p-3 rounded text-sm font-mono">
                {`// Get feedback for interview
const { data: feedback } = useInterviewFeedbackByInterview(
  interviewId, 
  "interviewer.user"
);

// Submit new feedback
const submitMutation = useSubmitInterviewFeedback();
await submitMutation.mutateAsync(feedbackData);

// Update existing feedback
const updateMutation = useUpdateInterviewFeedback();
await updateMutation.mutateAsync({ id, data });

// Delete feedback
const deleteMutation = useDeleteInterviewFeedback();
await deleteMutation.mutateAsync(feedbackId);`}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Interview Feedback System</h1>
          <p className="text-muted-foreground">
            Complete implementation with all components and integrations
          </p>
        </div>
        <Badge variant="default" className="bg-green-600">
          <CheckCircle className="w-3 h-3 mr-1" />
          Ready for Production
        </Badge>
      </div>

      <Tabs value={activeDemo} onValueChange={setActiveDemo} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="demo">Interactive Demo</TabsTrigger>
          <TabsTrigger value="integration">Integration Guide</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <OverviewTab />
        </TabsContent>

        <TabsContent value="demo">
          <DemoTab />
        </TabsContent>

        <TabsContent value="integration">
          <IntegrationTab />
        </TabsContent>
      </Tabs>

      {/* Modal Dialogs */}
      {showFeedbackForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <InterviewFeedbackForm
              interview={sampleInterviewForForm}
              onSubmit={handleFeedbackSubmit}
              onCancel={() => setShowFeedbackForm(false)}
            />
          </div>
        </div>
      )}

      <InterviewFeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        feedbackId={sampleFeedbackId}
        mode="view"
      />

      <EnhancedInterviewDetailModal
        interview={sampleInterview}
        isOpen={showInterviewModal}
        onClose={() => setShowInterviewModal(false)}
        onEdit={handleInterviewEdit}
        onStatusChange={handleInterviewStatusChange}
      />
    </div>
  );
};

export default InterviewFeedbackIntegration;
