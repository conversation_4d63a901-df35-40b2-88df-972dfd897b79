import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/components/ui/use-toast";
import {
  Star,
  Edit,
  Trash2,
  Calendar,
  User,
  Briefcase,
  Clock,
  ThumbsUp,
  ThumbsDown,
  Target,
  TrendingUp,
  MessageSquare,
  Users,
  Brain,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
} from "lucide-react";
import {
  InterviewFeedback,
  FeedbackFormData,
  getRatingLabel,
  getRatingColor,
  getScoreLabel,
  getScoreColor,
  feedbackUtils,
} from "../../lib/types/interviewFeedback";
import {
  useInterviewFeedbackDetails,
  useUpdateInterviewFeedback,
  useDeleteInterviewFeedback,
} from "../../hooks/useApi";
import { InterviewFeedbackForm } from "./InterviewFeedbackForm";

interface InterviewFeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  feedbackId?: number;
  interview?: {
    id: number;
    candidate_name: string;
    job_title: string;
    date: string;
    time: string;
    interviewer_id: number;
  };
  mode?: "view" | "edit" | "create";
  onFeedbackUpdated?: () => void;
  onFeedbackDeleted?: () => void;
}

export const InterviewFeedbackModal: React.FC<InterviewFeedbackModalProps> = ({
  isOpen,
  onClose,
  feedbackId,
  interview,
  mode = "view",
  onFeedbackUpdated,
  onFeedbackDeleted,
}) => {
  const [currentMode, setCurrentMode] = useState<"view" | "edit" | "create">(
    mode,
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // API hooks
  const {
    data: feedbackData,
    isLoading: isLoadingFeedback,
    error: feedbackError,
    refetch,
  } = useInterviewFeedbackDetails(
    feedbackId?.toString() || "",
    "interview.candidate,interview.jobPosting,interviewer.user",
  );

  const updateFeedbackMutation = useUpdateInterviewFeedback();
  const deleteFeedbackMutation = useDeleteInterviewFeedback();

  const feedback = feedbackData?.data;

  // Reset mode when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentMode(mode);
    }
  }, [isOpen, mode]);

  const handleEdit = () => {
    setCurrentMode("edit");
  };

  const handleCancelEdit = () => {
    setCurrentMode("view");
  };

  const handleUpdateFeedback = async (data: FeedbackFormData) => {
    if (!feedbackId) return;

    try {
      setIsSubmitting(true);
      await updateFeedbackMutation.mutateAsync({
        id: feedbackId.toString(),
        data,
      });

      toast({
        title: "Success",
        description: "Feedback updated successfully",
      });

      setCurrentMode("view");
      refetch();
      onFeedbackUpdated?.();
    } catch (error: any) {
      console.error("Error updating feedback:", error);
      toast({
        title: "Error",
        description:
          error.message || "Failed to update feedback. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteFeedback = async () => {
    if (!feedbackId) return;

    if (!confirm("Are you sure you want to delete this feedback?")) {
      return;
    }

    try {
      await deleteFeedbackMutation.mutateAsync(feedbackId.toString());

      toast({
        title: "Success",
        description: "Feedback deleted successfully",
      });

      onClose();
      onFeedbackDeleted?.();
    } catch (error: any) {
      console.error("Error deleting feedback:", error);
      toast({
        title: "Error",
        description:
          error.message || "Failed to delete feedback. Please try again.",
        variant: "destructive",
      });
    }
  };

  const FeedbackViewContent: React.FC<{ feedback: InterviewFeedback }> = ({
    feedback,
  }) => {
    const formattedFeedback = feedbackUtils.formatFeedbackForDisplay(feedback);
    const isComplete = feedbackUtils.isFeedbackComplete(feedback);
    const summary = feedbackUtils.generateFeedbackSummary(feedback);

    return (
      <TooltipProvider>
        <div className="space-y-6">
          {/* Header Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                Interview Feedback
                {isComplete ? (
                  <Badge variant="default" className="ml-2">
                    Complete
                  </Badge>
                ) : (
                  <Badge variant="outline" className="ml-2">
                    Incomplete
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>{summary}</CardDescription>
            </CardHeader>
          </Card>

          {/* Interview Details */}
          {feedback.interview && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Calendar className="w-4 h-4" />
                  Interview Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">
                      {feedback.interview.candidate?.name ||
                        "Unknown Candidate"}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Briefcase className="w-4 h-4 text-muted-foreground" />
                    <span>
                      {feedback.interview.job_posting?.title ||
                        "Unknown Position"}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span>{feedback.interview.date}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span>{feedback.interview.time}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Overall Rating */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Star className="w-4 h-4 text-yellow-500" />
                Overall Rating
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`w-6 h-6 ${
                        star <= (feedback.rating || 0)
                          ? "text-yellow-400 fill-current"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xl font-bold">
                    {feedback.rating || "N/A"}
                  </span>
                  <span className="text-sm text-muted-foreground">/5</span>
                  {feedback.rating && (
                    <Badge
                      variant="outline"
                      className={getRatingColor(feedback.rating)}
                    >
                      {getRatingLabel(feedback.rating)}
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recommendation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Target className="w-4 h-4" />
                Recommendation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                {feedback.recommend === true ? (
                  <>
                    <ThumbsUp className="w-5 h-5 text-green-600" />
                    <Badge variant="default" className="bg-green-600">
                      Recommended
                    </Badge>
                  </>
                ) : feedback.recommend === false ? (
                  <>
                    <ThumbsDown className="w-5 h-5 text-red-600" />
                    <Badge variant="destructive">Not Recommended</Badge>
                  </>
                ) : (
                  <>
                    <AlertTriangle className="w-5 h-5 text-yellow-600" />
                    <Badge variant="outline">No Recommendation</Badge>
                  </>
                )}
                {feedback.next_round_recommendation && (
                  <div className="ml-4">
                    <span className="text-sm text-muted-foreground">
                      Next Round:
                    </span>
                    <Badge variant="secondary" className="ml-1">
                      {feedback.next_round_recommendation.replace("-", " ")}
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Detailed Scores */}
          {formattedFeedback.has_detailed_scores && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <TrendingUp className="w-4 h-4" />
                  Detailed Scores
                  <Badge variant="outline" className="ml-2">
                    Overall: {feedback.overall_score}%
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {feedback.technical_score !== null && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Brain className="w-4 h-4 text-blue-600" />
                      <span className="font-medium">Technical Skills</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold">
                        {feedback.technical_score}%
                      </span>
                      <Badge
                        variant="outline"
                        className={getScoreColor(feedback.technical_score)}
                      >
                        {getScoreLabel(feedback.technical_score)}
                      </Badge>
                    </div>
                  </div>
                )}

                {feedback.communication_score !== null && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="w-4 h-4 text-green-600" />
                      <span className="font-medium">Communication</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold">
                        {feedback.communication_score}%
                      </span>
                      <Badge
                        variant="outline"
                        className={getScoreColor(feedback.communication_score)}
                      >
                        {getScoreLabel(feedback.communication_score)}
                      </Badge>
                    </div>
                  </div>
                )}

                {feedback.cultural_fit_score !== null && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-purple-600" />
                      <span className="font-medium">Cultural Fit</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold">
                        {feedback.cultural_fit_score}%
                      </span>
                      <Badge
                        variant="outline"
                        className={getScoreColor(feedback.cultural_fit_score)}
                      >
                        {getScoreLabel(feedback.cultural_fit_score)}
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Comments */}
          {feedback.comments && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <MessageSquare className="w-4 h-4" />
                  Detailed Comments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                  {feedback.comments}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Strengths and Concerns */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Strengths */}
            {feedback.strengths && feedback.strengths.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    Strengths
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feedback.strengths.map((strength, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>{strength}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Concerns */}
            {feedback.concerns && feedback.concerns.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base">
                    <XCircle className="w-4 h-4 text-orange-600" />
                    Areas for Improvement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feedback.concerns.map((concern, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <XCircle className="w-3 h-3 text-orange-600 mt-0.5 flex-shrink-0" />
                        <span>{concern}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Interviewer Info */}
          {feedback.interviewer && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <User className="w-4 h-4" />
                  Interviewer
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {feedback.interviewer.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </div>
                  <div>
                    <p className="font-medium">{feedback.interviewer.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {feedback.interviewer.email} •{" "}
                      {feedback.interviewer.department}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Metadata */}
          <Card className="border-dashed">
            <CardContent className="pt-4">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>
                  Created: {new Date(feedback.created_at).toLocaleString()}
                </span>
                <span>
                  Updated: {new Date(feedback.updated_at).toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </TooltipProvider>
    );
  };

  const LoadingContent = () => (
    <div className="flex items-center justify-center py-8">
      <Loader2 className="w-6 h-6 animate-spin" />
      <span className="ml-2">Loading feedback...</span>
    </div>
  );

  const ErrorContent = () => (
    <Alert variant="destructive">
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription>
        Failed to load feedback. Please try again.
      </AlertDescription>
    </Alert>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {currentMode === "view"
              ? "Interview Feedback"
              : currentMode === "edit"
                ? "Edit Feedback"
                : "Create Feedback"}
          </DialogTitle>
          <DialogDescription>
            {currentMode === "view"
              ? "View detailed interview feedback and assessment"
              : currentMode === "edit"
                ? "Update the interview feedback"
                : "Provide feedback for the completed interview"}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {currentMode === "edit" ? (
            <InterviewFeedbackForm
              interview={interview!}
              existingFeedback={feedback}
              onSubmit={handleUpdateFeedback}
              onCancel={handleCancelEdit}
              isLoading={isSubmitting}
              isEditMode={true}
            />
          ) : isLoadingFeedback ? (
            <LoadingContent />
          ) : feedbackError ? (
            <ErrorContent />
          ) : feedback ? (
            <FeedbackViewContent feedback={feedback} />
          ) : (
            <ErrorContent />
          )}
        </div>

        {currentMode === "view" && feedback && (
          <DialogFooter className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleEdit}
                className="flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                Edit Feedback
              </Button>
              <Button
                variant="outline"
                onClick={handleDeleteFeedback}
                className="flex items-center gap-2 text-destructive hover:text-destructive"
                disabled={deleteFeedbackMutation.isPending}
              >
                {deleteFeedbackMutation.isPending ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
                Delete
              </Button>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default InterviewFeedbackModal;
