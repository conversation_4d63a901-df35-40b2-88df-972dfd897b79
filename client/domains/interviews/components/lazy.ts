/**
 * Lazy-loaded Interviews Components
 * Lazy loading configuration for interviews domain components
 */

import React from 'react';
import { withLazyLoading, ComponentLoadingFallbacks, createRetryableLazy } from '../../shared/utils/lazyLoading';

// Lazy load main components
export const LazyInterviewCard = withLazyLoading(
  createRetryableLazy(() => import('./InterviewCard')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyInterviewTable = withLazyLoading(
  createRetryableLazy(() => import('./InterviewTable')),
  {
    fallback: ComponentLoadingFallbacks.table,
  }
);

export const LazyInterviewFilters = withLazyLoading(
  createRetryableLazy(() => import('./InterviewFilters')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyInterviewStats = withLazyLoading(
  createRetryableLazy(() => import('./InterviewStats')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

// Lazy load calendar components
export const LazyEnhancedInterviewDetailModal = withLazyLoading(
  createRetryableLazy(() => import('./calendar/EnhancedInterviewDetailModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyEditInterviewModal = withLazyLoading(
  createRetryableLazy(() => import('./calendar/EditInterviewModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyScheduleInterviewModal = withLazyLoading(
  createRetryableLazy(() => import('./calendar/ScheduleInterviewModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyInterviewFeedbackForm = withLazyLoading(
  createRetryableLazy(() => import('./calendar/InterviewFeedbackForm')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyInterviewFeedbackModal = withLazyLoading(
  createRetryableLazy(() => import('./calendar/InterviewFeedbackModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyInterviewFeedbackManager = withLazyLoading(
  createRetryableLazy(() => import('./calendar/InterviewFeedbackManager')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyInterviewFeedbackIntegration = withLazyLoading(
  createRetryableLazy(() => import('./calendar/InterviewFeedbackIntegration')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

// Lazy load interviewer components
export const LazyInterviewerManagement = withLazyLoading(
  createRetryableLazy(() => import('./interviewers/InterviewerManagement')),
  {
    fallback: ComponentLoadingFallbacks.page,
  }
);

export const LazyCreateEditInterviewerModal = withLazyLoading(
  createRetryableLazy(() => import('./interviewers/CreateEditInterviewerModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyInterviewerDetailModal = withLazyLoading(
  createRetryableLazy(() => import('./interviewers/InterviewerDetailModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyInterviewerCard = withLazyLoading(
  createRetryableLazy(() => import('./interviewers/InterviewerCard')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyInterviewerTable = withLazyLoading(
  createRetryableLazy(() => import('./interviewers/InterviewerTable')),
  {
    fallback: ComponentLoadingFallbacks.table,
  }
);

// Lazy load utility components
export const LazyInterviewStatusBadge = withLazyLoading(
  createRetryableLazy(() => import('./utils/InterviewStatusBadge')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyInterviewTypeBadge = withLazyLoading(
  createRetryableLazy(() => import('./utils/InterviewTypeBadge')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyBulkInterviewActions = withLazyLoading(
  createRetryableLazy(() => import('./utils/BulkInterviewActions')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

// Preload critical components
export const preloadInterviewComponents = () => {
  return Promise.all([
    import('./InterviewCard'),
    import('./InterviewTable'),
    import('./calendar/EnhancedInterviewDetailModal'),
    import('./calendar/EditInterviewModal'),
  ]);
};

// Component map for dynamic loading
export const interviewComponentMap = {
  InterviewCard: LazyInterviewCard,
  InterviewTable: LazyInterviewTable,
  InterviewFilters: LazyInterviewFilters,
  InterviewStats: LazyInterviewStats,
  EnhancedInterviewDetailModal: LazyEnhancedInterviewDetailModal,
  EditInterviewModal: LazyEditInterviewModal,
  ScheduleInterviewModal: LazyScheduleInterviewModal,
  InterviewFeedbackForm: LazyInterviewFeedbackForm,
  InterviewFeedbackModal: LazyInterviewFeedbackModal,
  InterviewFeedbackManager: LazyInterviewFeedbackManager,
  InterviewFeedbackIntegration: LazyInterviewFeedbackIntegration,
  InterviewerManagement: LazyInterviewerManagement,
  CreateEditInterviewerModal: LazyCreateEditInterviewerModal,
  InterviewerDetailModal: LazyInterviewerDetailModal,
  InterviewerCard: LazyInterviewerCard,
  InterviewerTable: LazyInterviewerTable,
  InterviewStatusBadge: LazyInterviewStatusBadge,
  InterviewTypeBadge: LazyInterviewTypeBadge,
  BulkInterviewActions: LazyBulkInterviewActions,
} as const;

export type InterviewComponentName = keyof typeof interviewComponentMap;
