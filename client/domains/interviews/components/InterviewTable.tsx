/**
 * Interview Table Component
 * Modern table component for displaying interviews in a tabular format
 */

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Calendar as CalendarIcon,
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCcw,
  ExternalLink,
  Star,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from 'lucide-react';
import { Interview } from '../types';

export interface InterviewTableProps {
  interviews: Interview[];
  onEdit?: (interview: Interview) => void;
  onDelete?: (interviewId: string) => void;
  onStatusChange?: (interviewId: string, status: string) => void;
  onReschedule?: (interview: Interview) => void;
  onViewDetails?: (interview: Interview) => void;
  onBulkAction?: (interviewIds: string[], action: string) => void;
  showActions?: boolean;
  showBulkActions?: boolean;
  sortable?: boolean;
  className?: string;
}

type SortField = 'candidateName' | 'jobTitle' | 'scheduledAt' | 'status' | 'type';
type SortDirection = 'asc' | 'desc';

export const InterviewTable: React.FC<InterviewTableProps> = ({
  interviews,
  onEdit,
  onDelete,
  onStatusChange,
  onReschedule,
  onViewDetails,
  onBulkAction,
  showActions = true,
  showBulkActions = false,
  sortable = true,
  className,
}) => {
  const [selectedInterviews, setSelectedInterviews] = useState<string[]>([]);
  const [sortField, setSortField] = useState<SortField>('scheduledAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort interviews
  const sortedInterviews = useMemo(() => {
    if (!sortable) return interviews;

    return [...interviews].sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === 'scheduledAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [interviews, sortField, sortDirection, sortable]);

  // Handle selection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedInterviews(interviews.map(interview => interview.id));
    } else {
      setSelectedInterviews([]);
    }
  };

  const handleSelectInterview = (interviewId: string, checked: boolean) => {
    if (checked) {
      setSelectedInterviews(prev => [...prev, interviewId]);
    } else {
      setSelectedInterviews(prev => prev.filter(id => id !== interviewId));
    }
  };

  // Get status display
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'scheduled':
        return {
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: <Clock className="w-3 h-3" />,
        };
      case 'in-progress':
        return {
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: <AlertCircle className="w-3 h-3" />,
        };
      case 'completed':
        return {
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: <CheckCircle className="w-3 h-3" />,
        };
      case 'cancelled':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: <XCircle className="w-3 h-3" />,
        };
      case 'rescheduled':
        return {
          color: 'bg-orange-100 text-orange-800 border-orange-200',
          icon: <RotateCcw className="w-3 h-3" />,
        };
      case 'no-show':
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <XCircle className="w-3 h-3" />,
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <Clock className="w-3 h-3" />,
        };
    }
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-4 h-4" />;
      case 'phone':
        return <Phone className="w-4 h-4" />;
      case 'in-person':
        return <MapPin className="w-4 h-4" />;
      default:
        return <CalendarIcon className="w-4 h-4" />;
    }
  };

  // Format date and time
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const dateStr = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
    const timeStr = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    return { date: dateStr, time: timeStr };
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (!sortable || sortField !== field) {
      return <ArrowUpDown className="w-4 h-4 opacity-50" />;
    }
    return sortDirection === 'asc' ? 
      <ArrowUp className="w-4 h-4" /> : 
      <ArrowDown className="w-4 h-4" />;
  };

  return (
    <div className={className}>
      {/* Bulk Actions */}
      {showBulkActions && selectedInterviews.length > 0 && (
        <div className="mb-4 p-3 bg-muted/50 rounded-lg flex items-center justify-between">
          <span className="text-sm text-muted-foreground">
            {selectedInterviews.length} interviews selected
          </span>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onBulkAction?.(selectedInterviews, 'reschedule')}
            >
              Reschedule
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onBulkAction?.(selectedInterviews, 'cancel')}
            >
              Cancel
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onBulkAction?.(selectedInterviews, 'delete')}
              className="text-red-600"
            >
              Delete
            </Button>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {showBulkActions && (
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedInterviews.length === interviews.length}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
              )}
              
              <TableHead 
                className={sortable ? 'cursor-pointer select-none' : ''}
                onClick={() => sortable && handleSort('candidateName')}
              >
                <div className="flex items-center gap-2">
                  Candidate
                  {sortable && renderSortIcon('candidateName')}
                </div>
              </TableHead>
              
              <TableHead 
                className={sortable ? 'cursor-pointer select-none' : ''}
                onClick={() => sortable && handleSort('jobTitle')}
              >
                <div className="flex items-center gap-2">
                  Position
                  {sortable && renderSortIcon('jobTitle')}
                </div>
              </TableHead>
              
              <TableHead 
                className={sortable ? 'cursor-pointer select-none' : ''}
                onClick={() => sortable && handleSort('scheduledAt')}
              >
                <div className="flex items-center gap-2">
                  Date & Time
                  {sortable && renderSortIcon('scheduledAt')}
                </div>
              </TableHead>
              
              <TableHead 
                className={sortable ? 'cursor-pointer select-none' : ''}
                onClick={() => sortable && handleSort('type')}
              >
                <div className="flex items-center gap-2">
                  Type
                  {sortable && renderSortIcon('type')}
                </div>
              </TableHead>
              
              <TableHead 
                className={sortable ? 'cursor-pointer select-none' : ''}
                onClick={() => sortable && handleSort('status')}
              >
                <div className="flex items-center gap-2">
                  Status
                  {sortable && renderSortIcon('status')}
                </div>
              </TableHead>
              
              <TableHead>Interviewer</TableHead>
              
              {showActions && <TableHead className="w-12"></TableHead>}
            </TableRow>
          </TableHeader>
          
          <TableBody>
            {sortedInterviews.map((interview) => {
              const statusDisplay = getStatusDisplay(interview.status);
              const { date, time } = formatDateTime(interview.scheduledAt);
              
              return (
                <TableRow key={interview.id} className="hover:bg-muted/50">
                  {showBulkActions && (
                    <TableCell>
                      <Checkbox
                        checked={selectedInterviews.includes(interview.id)}
                        onCheckedChange={(checked) => 
                          handleSelectInterview(interview.id, !!checked)
                        }
                      />
                    </TableCell>
                  )}
                  
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={interview.candidateAvatar} />
                        <AvatarFallback>
                          {interview.candidateName
                            .split(' ')
                            .map(n => n[0])
                            .join('')
                            .toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{interview.candidateName}</div>
                        {interview.candidateRating && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                            {interview.candidateRating}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div>
                      <div className="font-medium">{interview.jobTitle}</div>
                      <div className="text-xs text-muted-foreground">
                        {interview.round || 'General'}
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div>
                      <div className="font-medium">{date}</div>
                      <div className="text-xs text-muted-foreground">{time}</div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getTypeIcon(interview.type)}
                      <span className="capitalize">{interview.type}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Badge className={`${statusDisplay.color} gap-1`}>
                      {statusDisplay.icon}
                      <span className="capitalize">{interview.status}</span>
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{interview.interviewerName || 'TBD'}</span>
                    </div>
                  </TableCell>
                  
                  {showActions && (
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {onViewDetails && (
                            <DropdownMenuItem onClick={() => onViewDetails(interview)}>
                              <User className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                          )}
                          
                          {onEdit && (
                            <DropdownMenuItem onClick={() => onEdit(interview)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                          )}
                          
                          {onReschedule && interview.status === 'scheduled' && (
                            <DropdownMenuItem onClick={() => onReschedule(interview)}>
                              <RotateCcw className="mr-2 h-4 w-4" />
                              Reschedule
                            </DropdownMenuItem>
                          )}
                          
                          {interview.meetingUrl && (
                            <DropdownMenuItem
                              onClick={() => window.open(interview.meetingUrl, '_blank')}
                            >
                              <ExternalLink className="mr-2 h-4 w-4" />
                              Join Meeting
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuSeparator />
                          
                          {onDelete && (
                            <DropdownMenuItem
                              onClick={() => onDelete(interview.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  )}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
