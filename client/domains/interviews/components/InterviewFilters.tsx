/**
 * Interview Filters Component
 * Advanced filtering component for interviews
 */

import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
  X,
  Search,
  CalendarIcon,
  Filter,
  RotateCcw,
} from 'lucide-react';
import { InterviewFilters as IInterviewFilters, InterviewStatus, InterviewType, InterviewRound } from '../types';

export interface InterviewFiltersProps {
  filters: IInterviewFilters;
  onFiltersChange: (filters: IInterviewFilters) => void;
  onClose?: () => void;
  showCloseButton?: boolean;
  className?: string;
}

export const InterviewFilters: React.FC<InterviewFiltersProps> = ({
  filters,
  onFiltersChange,
  onClose,
  showCloseButton = false,
  className,
}) => {
  const [localFilters, setLocalFilters] = useState<IInterviewFilters>(filters);

  // Status options
  const statusOptions: { value: InterviewStatus; label: string }[] = [
    { value: 'scheduled', label: 'Scheduled' },
    { value: 'in-progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'rescheduled', label: 'Rescheduled' },
    { value: 'no-show', label: 'No Show' },
  ];

  // Type options
  const typeOptions: { value: InterviewType; label: string }[] = [
    { value: 'video', label: 'Video Call' },
    { value: 'phone', label: 'Phone Call' },
    { value: 'in-person', label: 'In Person' },
  ];

  // Round options
  const roundOptions: { value: InterviewRound; label: string }[] = [
    { value: 'screening', label: 'Screening' },
    { value: 'technical', label: 'Technical' },
    { value: 'behavioral', label: 'Behavioral' },
    { value: 'final', label: 'Final' },
    { value: 'panel', label: 'Panel' },
  ];

  // Handle filter changes
  const handleFilterChange = useCallback((key: keyof IInterviewFilters, value: any) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  // Handle array filter changes
  const handleArrayFilterChange = useCallback((
    key: keyof IInterviewFilters,
    value: string,
    checked: boolean
  ) => {
    setLocalFilters(prev => {
      const currentArray = (prev[key] as string[]) || [];
      const newArray = checked
        ? [...currentArray, value]
        : currentArray.filter(item => item !== value);
      
      return {
        ...prev,
        [key]: newArray.length > 0 ? newArray : undefined,
      };
    });
  }, []);

  // Apply filters
  const handleApplyFilters = useCallback(() => {
    onFiltersChange(localFilters);
  }, [localFilters, onFiltersChange]);

  // Reset filters
  const handleResetFilters = useCallback(() => {
    const emptyFilters: IInterviewFilters = {};
    setLocalFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  }, [onFiltersChange]);

  // Check if filters are active
  const hasActiveFilters = Object.values(localFilters).some(value => 
    value !== undefined && value !== null && 
    (Array.isArray(value) ? value.length > 0 : true)
  );

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            <span>Filter Interviews</span>
          </div>
          {showCloseButton && onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search">Search</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              id="search"
              placeholder="Search by candidate name, job title..."
              value={localFilters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value || undefined)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Date Range */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>From Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {localFilters.dateFrom
                    ? new Date(localFilters.dateFrom).toLocaleDateString()
                    : 'Select date'
                  }
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={localFilters.dateFrom ? new Date(localFilters.dateFrom) : undefined}
                  onSelect={(date) => handleFilterChange('dateFrom', date?.toISOString())}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label>To Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {localFilters.dateTo
                    ? new Date(localFilters.dateTo).toLocaleDateString()
                    : 'Select date'
                  }
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={localFilters.dateTo ? new Date(localFilters.dateTo) : undefined}
                  onSelect={(date) => handleFilterChange('dateTo', date?.toISOString())}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <Label>Status</Label>
          <div className="grid grid-cols-2 gap-2">
            {statusOptions.map((status) => (
              <div key={status.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${status.value}`}
                  checked={localFilters.status?.includes(status.value) || false}
                  onCheckedChange={(checked) =>
                    handleArrayFilterChange('status', status.value, !!checked)
                  }
                />
                <Label
                  htmlFor={`status-${status.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {status.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Type Filter */}
        <div className="space-y-2">
          <Label>Interview Type</Label>
          <div className="grid grid-cols-1 gap-2">
            {typeOptions.map((type) => (
              <div key={type.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`type-${type.value}`}
                  checked={localFilters.type?.includes(type.value) || false}
                  onCheckedChange={(checked) =>
                    handleArrayFilterChange('type', type.value, !!checked)
                  }
                />
                <Label
                  htmlFor={`type-${type.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {type.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Round Filter */}
        <div className="space-y-2">
          <Label>Interview Round</Label>
          <div className="grid grid-cols-2 gap-2">
            {roundOptions.map((round) => (
              <div key={round.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`round-${round.value}`}
                  checked={localFilters.round?.includes(round.value) || false}
                  onCheckedChange={(checked) =>
                    handleArrayFilterChange('round', round.value, !!checked)
                  }
                />
                <Label
                  htmlFor={`round-${round.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {round.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Interviewer Filter */}
        <div className="space-y-2">
          <Label htmlFor="interviewer">Interviewer</Label>
          <Input
            id="interviewer"
            placeholder="Filter by interviewer name"
            value={localFilters.interviewerId || ''}
            onChange={(e) => handleFilterChange('interviewerId', e.target.value || undefined)}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetFilters}
            disabled={!hasActiveFilters}
            className="gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Reset
          </Button>
          
          <div className="flex items-center gap-2">
            {showCloseButton && onClose && (
              <Button variant="outline" size="sm" onClick={onClose}>
                Cancel
              </Button>
            )}
            <Button size="sm" onClick={handleApplyFilters}>
              Apply Filters
            </Button>
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-4 border-t">
            <Label className="text-xs text-muted-foreground">Active Filters:</Label>
            <div className="flex flex-wrap gap-1 mt-2">
              {localFilters.search && (
                <Badge variant="secondary" className="text-xs">
                  Search: {localFilters.search}
                </Badge>
              )}
              {localFilters.status?.map(status => (
                <Badge key={status} variant="secondary" className="text-xs">
                  Status: {status}
                </Badge>
              ))}
              {localFilters.type?.map(type => (
                <Badge key={type} variant="secondary" className="text-xs">
                  Type: {type}
                </Badge>
              ))}
              {localFilters.round?.map(round => (
                <Badge key={round} variant="secondary" className="text-xs">
                  Round: {round}
                </Badge>
              ))}
              {localFilters.dateFrom && (
                <Badge variant="secondary" className="text-xs">
                  From: {new Date(localFilters.dateFrom).toLocaleDateString()}
                </Badge>
              )}
              {localFilters.dateTo && (
                <Badge variant="secondary" className="text-xs">
                  To: {new Date(localFilters.dateTo).toLocaleDateString()}
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
