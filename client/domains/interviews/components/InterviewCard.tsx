/**
 * Interview Card Component
 * Modern card component for displaying interview information
 */

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Calendar as CalendarIcon,
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCcw,
  ExternalLink,
  Star,
} from 'lucide-react';
import { Interview } from '../types';

export interface InterviewCardProps {
  interview: Interview;
  onEdit?: (interview: Interview) => void;
  onDelete?: (interviewId: string) => void;
  onStatusChange?: (interviewId: string, status: string) => void;
  onReschedule?: (interview: Interview) => void;
  onViewDetails?: (interview: Interview) => void;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

export const InterviewCard: React.FC<InterviewCardProps> = ({
  interview,
  onEdit,
  onDelete,
  onStatusChange,
  onReschedule,
  onViewDetails,
  showActions = true,
  compact = false,
  className,
}) => {
  // Get status color and icon
  const getStatusDisplay = () => {
    switch (interview.status) {
      case 'scheduled':
        return {
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: <Clock className="w-3 h-3" />,
        };
      case 'in-progress':
        return {
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: <AlertCircle className="w-3 h-3" />,
        };
      case 'completed':
        return {
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: <CheckCircle className="w-3 h-3" />,
        };
      case 'cancelled':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: <XCircle className="w-3 h-3" />,
        };
      case 'rescheduled':
        return {
          color: 'bg-orange-100 text-orange-800 border-orange-200',
          icon: <RotateCcw className="w-3 h-3" />,
        };
      case 'no-show':
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <XCircle className="w-3 h-3" />,
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <Clock className="w-3 h-3" />,
        };
    }
  };

  // Get interview type icon
  const getTypeIcon = () => {
    switch (interview.type) {
      case 'video':
        return <Video className="w-4 h-4" />;
      case 'phone':
        return <Phone className="w-4 h-4" />;
      case 'in-person':
        return <MapPin className="w-4 h-4" />;
      default:
        return <CalendarIcon className="w-4 h-4" />;
    }
  };

  // Format interview time
  const formatTime = () => {
    const date = new Date(interview.scheduledAt);
    const time = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    const dateStr = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
    return `${dateStr} at ${time}`;
  };

  // Check if interview is today
  const isToday = () => {
    const today = new Date();
    const interviewDate = new Date(interview.scheduledAt);
    return today.toDateString() === interviewDate.toDateString();
  };

  // Check if interview is upcoming
  const isUpcoming = () => {
    return new Date(interview.scheduledAt) > new Date();
  };

  const statusDisplay = getStatusDisplay();

  return (
    <Card className={`hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className={`pb-3 ${compact ? 'p-4' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            {/* Candidate Avatar */}
            <Avatar className={compact ? 'h-8 w-8' : 'h-10 w-10'}>
              <AvatarImage src={interview.candidateAvatar} />
              <AvatarFallback>
                {interview.candidateName
                  .split(' ')
                  .map(n => n[0])
                  .join('')
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 min-w-0">
              {/* Candidate Name and Job */}
              <div className="flex items-center gap-2 mb-1">
                <h3 className={`font-semibold truncate ${compact ? 'text-sm' : 'text-base'}`}>
                  {interview.candidateName}
                </h3>
                {interview.candidateRating && (
                  <div className="flex items-center gap-1">
                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs text-muted-foreground">
                      {interview.candidateRating}
                    </span>
                  </div>
                )}
              </div>

              <p className={`text-muted-foreground truncate ${compact ? 'text-xs' : 'text-sm'}`}>
                {interview.jobTitle} • {interview.round || 'General'}
              </p>

              {/* Interview Details */}
              <div className={`flex items-center gap-4 mt-2 ${compact ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatTime()}</span>
                  {isToday() && (
                    <Badge variant="secondary" className="ml-1 text-xs">
                      Today
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-1">
                  {getTypeIcon()}
                  <span className="capitalize">{interview.type}</span>
                </div>

                {interview.duration && (
                  <div className="flex items-center gap-1">
                    <span>{interview.duration}min</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Status and Actions */}
          <div className="flex items-start gap-2">
            <Badge className={`${statusDisplay.color} gap-1`}>
              {statusDisplay.icon}
              <span className="capitalize">{interview.status}</span>
            </Badge>

            {showActions && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onViewDetails && (
                    <DropdownMenuItem onClick={() => onViewDetails(interview)}>
                      <User className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                  )}

                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(interview)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                  )}

                  {onReschedule && interview.status === 'scheduled' && (
                    <DropdownMenuItem onClick={() => onReschedule(interview)}>
                      <RotateCcw className="mr-2 h-4 w-4" />
                      Reschedule
                    </DropdownMenuItem>
                  )}

                  {interview.meetingUrl && (
                    <DropdownMenuItem
                      onClick={() => window.open(interview.meetingUrl, '_blank')}
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Join Meeting
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />

                  {onStatusChange && interview.status === 'scheduled' && (
                    <>
                      <DropdownMenuItem
                        onClick={() => onStatusChange(interview.id, 'in-progress')}
                      >
                        <AlertCircle className="mr-2 h-4 w-4" />
                        Start Interview
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => onStatusChange(interview.id, 'completed')}
                      >
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Mark Complete
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => onStatusChange(interview.id, 'cancelled')}
                      >
                        <XCircle className="mr-2 h-4 w-4" />
                        Cancel
                      </DropdownMenuItem>
                    </>
                  )}

                  {onStatusChange && interview.status === 'in-progress' && (
                    <DropdownMenuItem
                      onClick={() => onStatusChange(interview.id, 'completed')}
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Complete Interview
                    </DropdownMenuItem>
                  )}

                  {onDelete && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => onDelete(interview.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardHeader>

      {!compact && (
        <CardContent className="pt-0">
          {/* Additional Details */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              {interview.interviewerName && (
                <div className="flex items-center gap-1">
                  <User className="w-3 h-3" />
                  <span>{interview.interviewerName}</span>
                </div>
              )}

              {interview.location && (
                <div className="flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  <span className="truncate max-w-32">{interview.location}</span>
                </div>
              )}
            </div>

            {interview.priority && (
              <Badge
                variant={interview.priority === 'high' ? 'destructive' : 'secondary'}
                className="text-xs"
              >
                {interview.priority} priority
              </Badge>
            )}
          </div>

          {/* Notes Preview */}
          {interview.notes && (
            <div className="mt-3 p-2 bg-muted/50 rounded text-xs text-muted-foreground">
              <p className="line-clamp-2">{interview.notes}</p>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};
