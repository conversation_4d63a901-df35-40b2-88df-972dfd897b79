/**
 * useInterviews Hook Tests
 * Tests for the interviews data fetching hook
 */

import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';
import { useInterviews } from '../../hooks/useInterviews';
import { Interview, InterviewQueryParams } from '../../types';

// Mock the interview service
const mockInterviewService = {
  getInterviews: vi.fn(),
};

vi.mock('../../services/interviewService', () => ({
  interviewService: mockInterviewService,
}));

// Mock data
const mockInterviews: Interview[] = [
  {
    id: '1',
    title: 'Technical Interview',
    description: 'Frontend developer interview',
    type: 'technical',
    round: 'first',
    status: 'scheduled',
    candidateId: 'candidate1',
    candidateName: '<PERSON>',
    candidateEmail: '<EMAIL>',
    jobId: 'job1',
    jobTitle: 'Frontend Developer',
    scheduledAt: '2024-01-15T10:00:00Z',
    duration: 60,
    timezone: 'UTC',
    location: 'Conference Room A',
    interviewers: [
      {
        userId: 'user1',
        name: '<PERSON>',
        email: '<EMAIL>',
        isPrimary: true,
        confirmed: true,
      },
    ],
    feedback: [],
    notes: 'Technical assessment for React skills',
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: '2',
    title: 'HR Interview',
    description: 'Cultural fit assessment',
    type: 'hr',
    round: 'second',
    status: 'completed',
    candidateId: 'candidate2',
    candidateName: 'Jane Wilson',
    candidateEmail: '<EMAIL>',
    jobId: 'job2',
    jobTitle: 'Backend Developer',
    scheduledAt: '2024-01-14T14:00:00Z',
    duration: 45,
    timezone: 'UTC',
    meetingUrl: 'https://meet.example.com/456',
    interviewers: [
      {
        userId: 'user2',
        name: 'Bob Johnson',
        email: '<EMAIL>',
        isPrimary: true,
        confirmed: true,
      },
    ],
    feedback: [
      {
        interviewerId: 'user2',
        rating: 4,
        comments: 'Good cultural fit',
        createdAt: '2024-01-14T15:00:00Z',
      },
    ],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-14T15:00:00Z',
  },
];

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useInterviews', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('fetches interviews successfully', async () => {
    mockInterviewService.getInterviews.mockResolvedValue({
      data: mockInterviews,
      success: true,
    });

    const { result } = renderHook(
      () => useInterviews({}),
      { wrapper: createWrapper() }
    );

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toEqual({
      data: mockInterviews,
      success: true,
    });
    expect(result.current.error).toBeNull();
    expect(mockInterviewService.getInterviews).toHaveBeenCalledWith({});
  });

  it('handles error when fetching interviews fails', async () => {
    const errorMessage = 'Failed to fetch interviews';
    mockInterviewService.getInterviews.mockRejectedValue(
      new Error(errorMessage)
    );

    const { result } = renderHook(
      () => useInterviews({}),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toBeTruthy();
  });

  it('passes correct query parameters to service', async () => {
    const queryParams: InterviewQueryParams = {
      search: 'John',
      status: ['scheduled'],
      type: ['technical'],
      dateFrom: '2024-01-01',
      dateTo: '2024-01-31',
      page: 1,
      limit: 25,
      sort: 'scheduled_at',
    };

    mockInterviewService.getInterviews.mockResolvedValue({
      data: [mockInterviews[0]],
      success: true,
    });

    renderHook(
      () => useInterviews(queryParams),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(mockInterviewService.getInterviews).toHaveBeenCalledWith(queryParams);
    });
  });

  it('handles empty response', async () => {
    mockInterviewService.getInterviews.mockResolvedValue({
      data: [],
      success: true,
    });

    const { result } = renderHook(
      () => useInterviews({}),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toEqual({
      data: [],
      success: true,
    });
  });

  it('filters interviews by status', async () => {
    const scheduledInterviews = [mockInterviews[0]];
    mockInterviewService.getInterviews.mockResolvedValue({
      data: scheduledInterviews,
      success: true,
    });

    const { result } = renderHook(
      () => useInterviews({ status: ['scheduled'] }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockInterviewService.getInterviews).toHaveBeenCalledWith({
      status: ['scheduled'],
    });
    expect(result.current.data?.data).toEqual(scheduledInterviews);
  });

  it('filters interviews by type', async () => {
    const technicalInterviews = [mockInterviews[0]];
    mockInterviewService.getInterviews.mockResolvedValue({
      data: technicalInterviews,
      success: true,
    });

    const { result } = renderHook(
      () => useInterviews({ type: ['technical'] }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockInterviewService.getInterviews).toHaveBeenCalledWith({
      type: ['technical'],
    });
    expect(result.current.data?.data).toEqual(technicalInterviews);
  });

  it('searches interviews by candidate name', async () => {
    const searchResults = [mockInterviews[0]];
    mockInterviewService.getInterviews.mockResolvedValue({
      data: searchResults,
      success: true,
    });

    const { result } = renderHook(
      () => useInterviews({ search: 'John' }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockInterviewService.getInterviews).toHaveBeenCalledWith({
      search: 'John',
    });
    expect(result.current.data?.data).toEqual(searchResults);
  });

  it('filters interviews by date range', async () => {
    mockInterviewService.getInterviews.mockResolvedValue({
      data: mockInterviews,
      success: true,
    });

    const { result } = renderHook(
      () => useInterviews({
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31',
      }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockInterviewService.getInterviews).toHaveBeenCalledWith({
      dateFrom: '2024-01-01',
      dateTo: '2024-01-31',
    });
  });

  it('supports pagination', async () => {
    mockInterviewService.getInterviews.mockResolvedValue({
      data: mockInterviews,
      success: true,
    });

    const { result } = renderHook(
      () => useInterviews({
        page: 2,
        limit: 10,
      }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockInterviewService.getInterviews).toHaveBeenCalledWith({
      page: 2,
      limit: 10,
    });
  });

  it('supports sorting', async () => {
    mockInterviewService.getInterviews.mockResolvedValue({
      data: mockInterviews,
      success: true,
    });

    const { result } = renderHook(
      () => useInterviews({
        sort: 'candidate_name',
      }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockInterviewService.getInterviews).toHaveBeenCalledWith({
      sort: 'candidate_name',
    });
  });

  it('refetches data when parameters change', async () => {
    mockInterviewService.getInterviews.mockResolvedValue({
      data: mockInterviews,
      success: true,
    });

    const { result, rerender } = renderHook(
      ({ params }) => useInterviews(params),
      {
        wrapper: createWrapper(),
        initialProps: {
          params: { status: ['scheduled'] },
        },
      }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockInterviewService.getInterviews).toHaveBeenCalledTimes(1);

    // Change parameters
    rerender({
      params: { status: ['completed'] },
    });

    await waitFor(() => {
      expect(mockInterviewService.getInterviews).toHaveBeenCalledTimes(2);
    });

    expect(mockInterviewService.getInterviews).toHaveBeenLastCalledWith({
      status: ['completed'],
    });
  });

  it('supports refetch functionality', async () => {
    mockInterviewService.getInterviews.mockResolvedValue({
      data: mockInterviews,
      success: true,
    });

    const { result } = renderHook(
      () => useInterviews({}),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockInterviewService.getInterviews).toHaveBeenCalledTimes(1);

    // Trigger refetch
    result.current.refetch();

    await waitFor(() => {
      expect(mockInterviewService.getInterviews).toHaveBeenCalledTimes(2);
    });
  });
});
