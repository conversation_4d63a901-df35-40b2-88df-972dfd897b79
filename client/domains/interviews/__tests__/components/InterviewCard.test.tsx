/**
 * InterviewCard Component Tests
 * Tests for the interview card component
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { InterviewCard } from '../../components/InterviewCard';
import { Interview } from '../../types';

// Mock the UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardTitle: ({ children }: any) => <h3>{children}</h3>,
  CardDescription: ({ children }: any) => <p>{children}</p>,
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant }: any) => <span data-variant={variant}>{children}</span>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variant, size }: any) => (
    <button onClick={onClick} data-variant={variant} data-size={size}>
      {children}
    </button>
  ),
}));

// Mock icons
vi.mock('lucide-react', () => ({
  Calendar: () => <span data-testid="calendar-icon">Calendar</span>,
  Clock: () => <span data-testid="clock-icon">Clock</span>,
  MapPin: () => <span data-testid="mappin-icon">MapPin</span>,
  Video: () => <span data-testid="video-icon">Video</span>,
  User: () => <span data-testid="user-icon">User</span>,
  Edit: () => <span data-testid="edit-icon">Edit</span>,
  Trash2: () => <span data-testid="trash-icon">Trash</span>,
  MoreVertical: () => <span data-testid="more-icon">More</span>,
}));

// Mock date formatting
vi.mock('date-fns', () => ({
  format: (date: Date, formatStr: string) => {
    if (formatStr === 'PPP p') return 'January 15, 2024 at 10:00 AM';
    if (formatStr === 'p') return '10:00 AM';
    return date.toISOString();
  },
}));

// Mock data
const mockInterview: Interview = {
  id: '1',
  title: 'Technical Interview',
  description: 'Frontend developer interview',
  type: 'technical',
  round: 'first',
  status: 'scheduled',
  candidateId: 'candidate1',
  candidateName: 'John Doe',
  candidateEmail: '<EMAIL>',
  jobId: 'job1',
  jobTitle: 'Frontend Developer',
  scheduledAt: '2024-01-15T10:00:00Z',
  duration: 60,
  timezone: 'UTC',
  location: 'Conference Room A',
  meetingUrl: 'https://meet.example.com/123',
  interviewers: [
    {
      userId: 'user1',
      name: 'Jane Smith',
      email: '<EMAIL>',
      isPrimary: true,
      confirmed: true,
    },
  ],
  feedback: [],
  notes: 'Technical assessment for React skills',
  createdAt: '2024-01-10T00:00:00Z',
  updatedAt: '2024-01-10T00:00:00Z',
};

describe('InterviewCard', () => {
  const defaultProps = {
    interview: mockInterview,
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onStatusChange: vi.fn(),
    onReschedule: vi.fn(),
    onViewDetails: vi.fn(),
    showActions: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders interview information correctly', () => {
    render(<InterviewCard {...defaultProps} />);
    
    expect(screen.getByText('Technical Interview')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Frontend Developer')).toBeInTheDocument();
    expect(screen.getByText('January 15, 2024 at 10:00 AM')).toBeInTheDocument();
  });

  it('displays interview type and round', () => {
    render(<InterviewCard {...defaultProps} />);
    
    expect(screen.getByText('technical')).toBeInTheDocument();
    expect(screen.getByText('first')).toBeInTheDocument();
  });

  it('displays interview status badge', () => {
    render(<InterviewCard {...defaultProps} />);
    
    const statusBadge = screen.getByText('scheduled');
    expect(statusBadge).toBeInTheDocument();
  });

  it('shows location when provided', () => {
    render(<InterviewCard {...defaultProps} />);
    
    expect(screen.getByText('Conference Room A')).toBeInTheDocument();
    expect(screen.getByTestId('mappin-icon')).toBeInTheDocument();
  });

  it('shows video icon for virtual interviews', () => {
    const virtualInterview = {
      ...mockInterview,
      location: undefined,
      meetingUrl: 'https://meet.example.com/123',
    };
    
    render(<InterviewCard {...defaultProps} interview={virtualInterview} />);
    
    expect(screen.getByTestId('video-icon')).toBeInTheDocument();
  });

  it('displays interviewer information', () => {
    render(<InterviewCard {...defaultProps} />);
    
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  it('shows action buttons when showActions is true', () => {
    render(<InterviewCard {...defaultProps} showActions={true} />);
    
    expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /delete/i })).toBeInTheDocument();
  });

  it('hides action buttons when showActions is false', () => {
    render(<InterviewCard {...defaultProps} showActions={false} />);
    
    expect(screen.queryByRole('button', { name: /edit/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /delete/i })).not.toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', () => {
    const onEdit = vi.fn();
    render(<InterviewCard {...defaultProps} onEdit={onEdit} />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);
    
    expect(onEdit).toHaveBeenCalledWith(mockInterview);
  });

  it('calls onDelete when delete button is clicked', () => {
    const onDelete = vi.fn();
    render(<InterviewCard {...defaultProps} onDelete={onDelete} />);
    
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(deleteButton);
    
    expect(onDelete).toHaveBeenCalledWith(mockInterview.id);
  });

  it('calls onViewDetails when card is clicked', () => {
    const onViewDetails = vi.fn();
    render(<InterviewCard {...defaultProps} onViewDetails={onViewDetails} />);
    
    // Click on the card (not on action buttons)
    const cardTitle = screen.getByText('Technical Interview');
    fireEvent.click(cardTitle);
    
    expect(onViewDetails).toHaveBeenCalledWith(mockInterview);
  });

  it('calls onStatusChange when status is changed', () => {
    const onStatusChange = vi.fn();
    render(<InterviewCard {...defaultProps} onStatusChange={onStatusChange} />);
    
    // This would depend on the actual implementation of status change
    // For now, we'll test that the handler is available
    expect(onStatusChange).toBeDefined();
  });

  it('calls onReschedule when reschedule is triggered', () => {
    const onReschedule = vi.fn();
    render(<InterviewCard {...defaultProps} onReschedule={onReschedule} />);
    
    // This would depend on the actual implementation of reschedule
    // For now, we'll test that the handler is available
    expect(onReschedule).toBeDefined();
  });

  it('displays duration information', () => {
    render(<InterviewCard {...defaultProps} />);
    
    expect(screen.getByText('60 min')).toBeInTheDocument();
  });

  it('handles different interview statuses', () => {
    const completedInterview = { ...mockInterview, status: 'completed' as const };
    render(<InterviewCard {...defaultProps} interview={completedInterview} />);
    
    expect(screen.getByText('completed')).toBeInTheDocument();
  });

  it('handles different interview types', () => {
    const phoneInterview = { ...mockInterview, type: 'phone' as const };
    render(<InterviewCard {...defaultProps} interview={phoneInterview} />);
    
    expect(screen.getByText('phone')).toBeInTheDocument();
  });

  it('displays notes when provided', () => {
    render(<InterviewCard {...defaultProps} />);
    
    expect(screen.getByText('Technical assessment for React skills')).toBeInTheDocument();
  });

  it('handles missing optional fields gracefully', () => {
    const minimalInterview: Interview = {
      ...mockInterview,
      description: undefined,
      location: undefined,
      meetingUrl: undefined,
      notes: undefined,
    };
    
    render(<InterviewCard {...defaultProps} interview={minimalInterview} />);
    
    expect(screen.getByText('Technical Interview')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('applies correct styling classes', () => {
    const { container } = render(<InterviewCard {...defaultProps} />);
    
    // Check that the card has appropriate classes
    const card = container.firstChild;
    expect(card).toHaveClass('interview-card');
  });
});
