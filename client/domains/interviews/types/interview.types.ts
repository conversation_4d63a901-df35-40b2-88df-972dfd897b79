/**
 * Interview Domain Types
 * Core types and interfaces for the interviews domain
 */

import { BaseEntity } from '@/core/api';

// Interview status enum
export type InterviewStatus = 
  | 'scheduled'
  | 'in-progress'
  | 'completed'
  | 'cancelled'
  | 'no-show'
  | 'rescheduled';

// Interview types
export type InterviewType = 
  | 'phone'
  | 'video'
  | 'in-person'
  | 'technical'
  | 'behavioral'
  | 'panel'
  | 'group';

// Interview round types
export type InterviewRound = 
  | 'screening'
  | 'first'
  | 'second'
  | 'third'
  | 'final'
  | 'technical'
  | 'cultural';

// Interview outcome
export type InterviewOutcome = 
  | 'hire'
  | 'no-hire'
  | 'maybe'
  | 'strong-hire'
  | 'strong-no-hire';

// Core Interview interface
export interface Interview extends BaseEntity {
  // Basic Information
  title: string;
  type: InterviewType;
  round: InterviewRound;
  status: InterviewStatus;
  
  // Participants
  candidateId: string;
  candidateName: string;
  candidateEmail: string;
  jobId: string;
  jobTitle: string;
  
  // Interviewers
  interviewers: InterviewParticipant[];
  primaryInterviewerId: string;
  
  // Scheduling
  scheduledAt: string;
  duration: number; // in minutes
  timezone: string;
  
  // Location/Meeting Details
  location?: string;
  meetingUrl?: string;
  meetingId?: string;
  dialInNumber?: string;
  
  // Interview Content
  description?: string;
  agenda?: string;
  questions?: InterviewQuestion[];
  
  // Feedback and Evaluation
  feedback?: InterviewFeedback[];
  overallRating?: number;
  outcome?: InterviewOutcome;
  recommendation?: string;
  
  // Notes and Comments
  notes?: string;
  internalNotes?: string;
  
  // Reminders and Notifications
  remindersSent?: boolean;
  confirmationSent?: boolean;
  
  // Timestamps
  startedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  
  // Computed fields
  isUpcoming?: boolean;
  isPast?: boolean;
  canStart?: boolean;
  canComplete?: boolean;
  formattedDuration?: string;
}

// Interview participant interface
export interface InterviewParticipant {
  id: string;
  userId: string;
  name: string;
  email: string;
  role: 'interviewer' | 'observer' | 'coordinator';
  isPrimary?: boolean;
  confirmed?: boolean;
  joinedAt?: string;
  leftAt?: string;
}

// Interview question interface
export interface InterviewQuestion {
  id: string;
  question: string;
  category: string;
  type: 'behavioral' | 'technical' | 'situational' | 'cultural';
  difficulty?: 'easy' | 'medium' | 'hard';
  expectedAnswer?: string;
  timeAllocation?: number; // in minutes
  order: number;
  isRequired?: boolean;
}

// Interview feedback interface
export interface InterviewFeedback {
  id: string;
  interviewerId: string;
  interviewerName: string;
  
  // Ratings (1-5 scale)
  overallRating: number;
  technicalSkills?: number;
  communication?: number;
  problemSolving?: number;
  culturalFit?: number;
  leadership?: number;
  experience?: number;
  
  // Detailed feedback
  strengths: string[];
  weaknesses: string[];
  comments: string;
  
  // Recommendation
  recommendation: InterviewOutcome;
  confidence: number; // 1-5 scale
  
  // Question responses
  questionResponses?: Array<{
    questionId: string;
    response: string;
    rating?: number;
    notes?: string;
  }>;
  
  // Next steps
  nextSteps?: string;
  followUpRequired?: boolean;
  
  // Timestamps
  submittedAt: string;
  updatedAt?: string;
}

// Create interview data interface
export interface CreateInterviewData {
  title: string;
  type: InterviewType;
  round: InterviewRound;
  candidateId: string;
  jobId: string;
  interviewers: Array<{
    userId: string;
    role: 'interviewer' | 'observer';
    isPrimary?: boolean;
  }>;
  scheduledAt: string;
  duration: number;
  timezone: string;
  location?: string;
  meetingUrl?: string;
  description?: string;
  agenda?: string;
  questions?: string[];
}

// Update interview data interface
export interface UpdateInterviewData {
  title?: string;
  type?: InterviewType;
  round?: InterviewRound;
  scheduledAt?: string;
  duration?: number;
  timezone?: string;
  location?: string;
  meetingUrl?: string;
  description?: string;
  agenda?: string;
  questions?: string[];
  notes?: string;
  internalNotes?: string;
}

// Interview search filters
export interface InterviewSearchFilters {
  query?: string;
  status?: InterviewStatus[];
  type?: InterviewType[];
  round?: InterviewRound[];
  candidateId?: string;
  jobId?: string;
  interviewerId?: string;
  dateRange?: {
    from: string;
    to: string;
  };
  outcome?: InterviewOutcome[];
}

// Interview list item (for tables and lists)
export interface InterviewListItem {
  id: string;
  title: string;
  type: InterviewType;
  round: InterviewRound;
  status: InterviewStatus;
  candidateName: string;
  jobTitle: string;
  scheduledAt: string;
  duration: number;
  primaryInterviewerName: string;
  overallRating?: number;
  outcome?: InterviewOutcome;
  
  // Computed fields
  isUpcoming?: boolean;
  isPast?: boolean;
  formattedTime?: string;
}

// Interview statistics
export interface InterviewStatistics {
  total: number;
  byStatus: Record<InterviewStatus, number>;
  byType: Record<InterviewType, number>;
  byOutcome: Record<InterviewOutcome, number>;
  averageRating: number;
  completionRate: number;
  noShowRate: number;
  averageDuration: number;
}

// Interview template interface
export interface InterviewTemplate {
  id: string;
  name: string;
  description?: string;
  type: InterviewType;
  round: InterviewRound;
  duration: number;
  questions: InterviewQuestion[];
  agenda?: string;
  isDefault?: boolean;
  departmentId?: string;
  createdBy: string;
  createdAt: string;
}

// Interview calendar event
export interface InterviewCalendarEvent {
  id: string;
  interviewId: string;
  title: string;
  start: string;
  end: string;
  location?: string;
  meetingUrl?: string;
  attendees: Array<{
    email: string;
    name: string;
    role: string;
  }>;
  description?: string;
  calendarId?: string;
  externalEventId?: string;
}

// Interview reminder
export interface InterviewReminder {
  id: string;
  interviewId: string;
  type: 'email' | 'sms' | 'push';
  recipient: 'candidate' | 'interviewer' | 'all';
  scheduledFor: string;
  sent: boolean;
  sentAt?: string;
  template?: string;
}

// Interview scorecard
export interface InterviewScorecard {
  id: string;
  interviewId: string;
  templateId?: string;
  criteria: Array<{
    name: string;
    description?: string;
    weight: number;
    rating: number;
    comments?: string;
  }>;
  overallScore: number;
  recommendation: InterviewOutcome;
  submittedBy: string;
  submittedAt: string;
}

// Bulk operations
export interface BulkInterviewOperation {
  interviewIds: string[];
  operation: 'update_status' | 'cancel' | 'reschedule' | 'delete' | 'export';
  data?: any;
}

// Export options
export interface InterviewExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  fields: string[];
  filters?: InterviewSearchFilters;
  includeFeedback?: boolean;
  includeQuestions?: boolean;
}

// Interview form validation
export interface InterviewFormErrors {
  title?: string;
  candidateId?: string;
  jobId?: string;
  scheduledAt?: string;
  duration?: string;
  interviewers?: string;
  general?: string;
}

// Interview availability
export interface InterviewAvailability {
  userId: string;
  userName: string;
  email: string;
  availableSlots: Array<{
    start: string;
    end: string;
    timezone: string;
  }>;
  busySlots: Array<{
    start: string;
    end: string;
    title: string;
  }>;
}

// Interview scheduling request
export interface InterviewSchedulingRequest {
  candidateId: string;
  jobId: string;
  interviewerIds: string[];
  preferredDates: string[];
  duration: number;
  type: InterviewType;
  round: InterviewRound;
  timezone: string;
  notes?: string;
}

// Interview activity log
export interface InterviewActivity {
  id: string;
  type: 'scheduled' | 'rescheduled' | 'cancelled' | 'started' | 'completed' | 'feedback_submitted';
  description: string;
  details?: Record<string, any>;
  userId: string;
  userName: string;
  timestamp: string;
}
