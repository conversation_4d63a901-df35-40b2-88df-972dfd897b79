/**
 * Interview Feedback Types and Utilities
 * Types and helper functions for interview feedback system
 */

// Rating scale for different aspects
export type Rating = 1 | 2 | 3 | 4 | 5;

// Score scale for overall assessment
export type Score = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;

// Next round options
export const NEXT_ROUND_OPTIONS = [
  { value: 'yes', label: 'Yes - Proceed to next round' },
  { value: 'no', label: 'No - Do not proceed' },
  { value: 'maybe', label: 'Maybe - Needs discussion' },
] as const;

export type NextRoundDecision = typeof NEXT_ROUND_OPTIONS[number]['value'];

// Interview feedback data structure
export interface InterviewFeedback {
  id: string;
  interviewId: string;
  interviewerId: string;
  candidateId: string;
  jobId: string;
  
  // Technical assessment
  technicalSkills: {
    rating: Rating;
    notes: string;
    strengths: string[];
    weaknesses: string[];
  };
  
  // Communication assessment
  communication: {
    rating: Rating;
    notes: string;
    clarity: Rating;
    listening: Rating;
    articulation: Rating;
  };
  
  // Problem solving
  problemSolving: {
    rating: Rating;
    notes: string;
    approach: Rating;
    creativity: Rating;
    analyticalThinking: Rating;
  };
  
  // Cultural fit
  culturalFit: {
    rating: Rating;
    notes: string;
    teamwork: Rating;
    values: Rating;
    adaptability: Rating;
  };
  
  // Overall assessment
  overall: {
    score: Score;
    summary: string;
    highlights: string[];
    concerns: string[];
    nextRound: NextRoundDecision;
    recommendation: string;
  };
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  submittedAt?: string;
  status: 'draft' | 'submitted' | 'reviewed';
}

// Helper functions
export const getRatingLabel = (rating: Rating): string => {
  const labels = {
    1: 'Poor',
    2: 'Below Average',
    3: 'Average',
    4: 'Good',
    5: 'Excellent',
  };
  return labels[rating];
};

export const getRatingColor = (rating: Rating): string => {
  const colors = {
    1: 'text-red-600',
    2: 'text-orange-600',
    3: 'text-yellow-600',
    4: 'text-blue-600',
    5: 'text-green-600',
  };
  return colors[rating];
};

export const getScoreLabel = (score: Score): string => {
  if (score <= 3) return 'Not Recommended';
  if (score <= 5) return 'Below Expectations';
  if (score <= 7) return 'Meets Expectations';
  if (score <= 9) return 'Exceeds Expectations';
  return 'Outstanding';
};

export const getScoreColor = (score: Score): string => {
  if (score <= 3) return 'text-red-600';
  if (score <= 5) return 'text-orange-600';
  if (score <= 7) return 'text-yellow-600';
  if (score <= 9) return 'text-blue-600';
  return 'text-green-600';
};

export const calculateOverallScore = (feedback: Partial<InterviewFeedback>): Score => {
  if (!feedback.technicalSkills || !feedback.communication || !feedback.problemSolving || !feedback.culturalFit) {
    return 5; // Default middle score
  }
  
  const weights = {
    technical: 0.3,
    communication: 0.25,
    problemSolving: 0.25,
    cultural: 0.2,
  };
  
  const weightedScore = 
    (feedback.technicalSkills.rating * weights.technical) +
    (feedback.communication.rating * weights.communication) +
    (feedback.problemSolving.rating * weights.problemSolving) +
    (feedback.culturalFit.rating * weights.cultural);
  
  // Convert 1-5 scale to 1-10 scale
  return Math.round(weightedScore * 2) as Score;
};

// Validation function
export const validateFeedbackData = (feedback: Partial<InterviewFeedback>): string[] => {
  const errors: string[] = [];
  
  if (!feedback.technicalSkills?.rating) {
    errors.push('Technical skills rating is required');
  }
  
  if (!feedback.communication?.rating) {
    errors.push('Communication rating is required');
  }
  
  if (!feedback.problemSolving?.rating) {
    errors.push('Problem solving rating is required');
  }
  
  if (!feedback.culturalFit?.rating) {
    errors.push('Cultural fit rating is required');
  }
  
  if (!feedback.overall?.score) {
    errors.push('Overall score is required');
  }
  
  if (!feedback.overall?.summary?.trim()) {
    errors.push('Overall summary is required');
  }
  
  if (!feedback.overall?.nextRound) {
    errors.push('Next round decision is required');
  }
  
  return errors;
};

// Utility object for feedback operations
export const feedbackUtils = {
  getRatingLabel,
  getRatingColor,
  getScoreLabel,
  getScoreColor,
  calculateOverallScore,
  validateFeedbackData,
  
  // Generate feedback summary
  generateSummary: (feedback: InterviewFeedback): string => {
    const score = feedback.overall.score;
    const recommendation = getScoreLabel(score);
    const nextRound = feedback.overall.nextRound;
    
    return `${recommendation} (${score}/10) - ${nextRound === 'yes' ? 'Recommended for next round' : nextRound === 'no' ? 'Not recommended' : 'Requires discussion'}`;
  },
  
  // Check if feedback is complete
  isComplete: (feedback: Partial<InterviewFeedback>): boolean => {
    return validateFeedbackData(feedback).length === 0;
  },
  
  // Get feedback status color
  getStatusColor: (status: InterviewFeedback['status']): string => {
    const colors = {
      draft: 'text-gray-600',
      submitted: 'text-blue-600',
      reviewed: 'text-green-600',
    };
    return colors[status];
  },
};

// Mock data for development
export const mockInterviewFeedback: InterviewFeedback = {
  id: '1',
  interviewId: 'interview-1',
  interviewerId: 'interviewer-1',
  candidateId: 'candidate-1',
  jobId: 'job-1',
  
  technicalSkills: {
    rating: 4,
    notes: 'Strong understanding of React and TypeScript. Good problem-solving approach.',
    strengths: ['React expertise', 'Clean code', 'Testing knowledge'],
    weaknesses: ['Limited backend experience', 'Could improve system design'],
  },
  
  communication: {
    rating: 5,
    notes: 'Excellent communication skills. Clear and articulate responses.',
    clarity: 5,
    listening: 4,
    articulation: 5,
  },
  
  problemSolving: {
    rating: 4,
    notes: 'Good analytical thinking. Approached problems systematically.',
    approach: 4,
    creativity: 4,
    analyticalThinking: 4,
  },
  
  culturalFit: {
    rating: 4,
    notes: 'Good cultural fit. Shows enthusiasm and collaborative spirit.',
    teamwork: 4,
    values: 4,
    adaptability: 4,
  },
  
  overall: {
    score: 8,
    summary: 'Strong candidate with excellent technical skills and communication. Would be a good addition to the team.',
    highlights: ['Technical expertise', 'Communication skills', 'Problem-solving approach'],
    concerns: ['Limited backend experience', 'System design knowledge'],
    nextRound: 'yes',
    recommendation: 'Recommend for hire with mentoring on backend technologies.',
  },
  
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T11:30:00Z',
  submittedAt: '2024-01-15T11:30:00Z',
  status: 'submitted',
};
