/**
 * Internationalization utilities for interviews domain
 * Simple i18n implementation for interview-related text
 */

// Translation keys and values
const translations = {
  en: {
    // Interview status
    'interview.status.scheduled': 'Scheduled',
    'interview.status.in_progress': 'In Progress',
    'interview.status.completed': 'Completed',
    'interview.status.cancelled': 'Cancelled',
    'interview.status.rescheduled': 'Rescheduled',
    
    // Interview types
    'interview.type.phone': 'Phone Interview',
    'interview.type.video': 'Video Interview',
    'interview.type.onsite': 'On-site Interview',
    'interview.type.technical': 'Technical Interview',
    'interview.type.behavioral': 'Behavioral Interview',
    'interview.type.final': 'Final Interview',
    
    // Actions
    'interview.action.schedule': 'Schedule Interview',
    'interview.action.reschedule': 'Reschedule',
    'interview.action.cancel': 'Cancel',
    'interview.action.start': 'Start Interview',
    'interview.action.complete': 'Complete',
    'interview.action.edit': 'Edit',
    'interview.action.delete': 'Delete',
    'interview.action.view_details': 'View Details',
    'interview.action.add_feedback': 'Add Feedback',
    'interview.action.view_feedback': 'View Feedback',
    
    // Feedback
    'feedback.title': 'Interview Feedback',
    'feedback.technical_skills': 'Technical Skills',
    'feedback.communication': 'Communication',
    'feedback.problem_solving': 'Problem Solving',
    'feedback.cultural_fit': 'Cultural Fit',
    'feedback.overall_assessment': 'Overall Assessment',
    'feedback.rating': 'Rating',
    'feedback.notes': 'Notes',
    'feedback.strengths': 'Strengths',
    'feedback.weaknesses': 'Areas for Improvement',
    'feedback.recommendation': 'Recommendation',
    'feedback.next_round': 'Next Round Decision',
    'feedback.submit': 'Submit Feedback',
    'feedback.save_draft': 'Save as Draft',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.close': 'Close',
    'common.confirm': 'Confirm',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.maybe': 'Maybe',
    
    // Time and dates
    'time.today': 'Today',
    'time.tomorrow': 'Tomorrow',
    'time.yesterday': 'Yesterday',
    'time.this_week': 'This Week',
    'time.next_week': 'Next Week',
    'time.duration': 'Duration',
    'time.start_time': 'Start Time',
    'time.end_time': 'End Time',
    
    // Notifications
    'notification.interview_scheduled': 'Interview scheduled successfully',
    'notification.interview_cancelled': 'Interview cancelled',
    'notification.interview_rescheduled': 'Interview rescheduled',
    'notification.feedback_submitted': 'Feedback submitted successfully',
    'notification.feedback_saved': 'Feedback saved as draft',
    'notification.error_occurred': 'An error occurred',
    
    // Validation messages
    'validation.required': 'This field is required',
    'validation.invalid_email': 'Please enter a valid email address',
    'validation.invalid_date': 'Please enter a valid date',
    'validation.invalid_time': 'Please enter a valid time',
    'validation.past_date': 'Date cannot be in the past',
    'validation.rating_required': 'Please provide a rating',
    'validation.notes_required': 'Please provide notes',
  },
  
  // Add more languages as needed
  vi: {
    // Vietnamese translations would go here
    'interview.status.scheduled': 'Đã lên lịch',
    'interview.status.completed': 'Hoàn thành',
    'interview.status.cancelled': 'Đã hủy',
    'common.loading': 'Đang tải...',
    'common.error': 'Lỗi',
    'common.success': 'Thành công',
    // ... more Vietnamese translations
  },
};

// Current language (could be managed by a state management system)
let currentLanguage: keyof typeof translations = 'en';

// Translation function
export const useTranslation = () => {
  const t = (key: string, fallback?: string): string => {
    const translation = translations[currentLanguage]?.[key as keyof typeof translations['en']];
    return translation || fallback || key;
  };
  
  const setLanguage = (lang: keyof typeof translations) => {
    currentLanguage = lang;
  };
  
  const getCurrentLanguage = () => currentLanguage;
  
  const getAvailableLanguages = () => Object.keys(translations);
  
  return {
    t,
    setLanguage,
    getCurrentLanguage,
    getAvailableLanguages,
  };
};

// Export translation function for direct use
export const t = (key: string, fallback?: string): string => {
  const translation = translations[currentLanguage]?.[key as keyof typeof translations['en']];
  return translation || fallback || key;
};

// Export language management functions
export const setLanguage = (lang: keyof typeof translations) => {
  currentLanguage = lang;
};

export const getCurrentLanguage = () => currentLanguage;

export const getAvailableLanguages = () => Object.keys(translations);

// Helper function to format dates based on current language
export const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const locale = currentLanguage === 'vi' ? 'vi-VN' : 'en-US';
  
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options,
  }).format(dateObj);
};

// Helper function to format time based on current language
export const formatTime = (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const locale = currentLanguage === 'vi' ? 'vi-VN' : 'en-US';
  
  return new Intl.DateTimeFormat(locale, {
    hour: '2-digit',
    minute: '2-digit',
    ...options,
  }).format(dateObj);
};

// Helper function to format relative time
export const formatRelativeTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInMs = dateObj.getTime() - now.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return t('time.today');
  if (diffInDays === 1) return t('time.tomorrow');
  if (diffInDays === -1) return t('time.yesterday');
  if (diffInDays > 1 && diffInDays <= 7) return t('time.this_week');
  if (diffInDays > 7 && diffInDays <= 14) return t('time.next_week');
  
  return formatDate(dateObj);
};

// Export all translation utilities
export default {
  useTranslation,
  t,
  setLanguage,
  getCurrentLanguage,
  getAvailableLanguages,
  formatDate,
  formatTime,
  formatRelativeTime,
};
