/**
 * Interview Business Service
 * Handles interview business logic and data transformation
 */

import { InterviewApiService } from './InterviewApiService';
import { notifications } from '@/shared/utils';
import {
  Interview,
  CreateInterviewData,
  UpdateInterviewData,
  InterviewFilters,
  InterviewStatistics,
  InterviewFeedback,
  CreateInterviewFeedbackData,
  UpdateInterviewFeedbackData,
  InterviewBulkOperation,
  InterviewExportOptions,
  InterviewImportResult,
  InterviewSearchResult,
  InterviewQueryParams,
  InterviewAvailability,
  InterviewStatus,
  InterviewType,
  InterviewRound,
  InterviewOutcome
} from '../types';

export class InterviewBusinessService {
  constructor(private apiService: InterviewApiService) {}

  /**
   * Get interviews with business logic applied
   */
  async getInterviews(params?: InterviewQueryParams) {
    try {
      const response = await this.apiService.getInterviews(params);
      
      return {
        ...response,
        data: response.data?.map(this.enrichInterviewData) || [],
      };
    } catch (error) {
      notifications.error.generic('Failed to load interviews');
      throw error;
    }
  }

  /**
   * Get interview by ID
   */
  async getInterviewById(id: string) {
    try {
      const response = await this.apiService.getInterviewById(id);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: this.enrichInterviewData(response.data),
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to load interview');
      throw error;
    }
  }

  /**
   * Create interview with validation
   */
  async createInterview(data: CreateInterviewData) {
    try {
      // Validate interview data
      this.validateInterviewData(data);
      
      // Check for scheduling conflicts
      const conflicts = await this.checkSchedulingConflicts(data);
      if (conflicts.length > 0) {
        notifications.warning.generic('Scheduling conflicts detected. Please review the suggested times.');
      }
      
      const response = await this.apiService.createInterview(data);
      
      if (response.status === 'success') {
        notifications.success.generic('Interview created successfully');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to create interview');
      throw error;
    }
  }

  /**
   * Schedule interview with smart suggestions
   */
  async scheduleInterview(data: CreateInterviewData) {
    try {
      // Validate scheduling data
      this.validateSchedulingData(data);
      
      // Get availability suggestions
      const suggestions = await this.getSchedulingSuggestions(data);
      
      const response = await this.apiService.scheduleInterview(data);
      
      if (response.status === 'success') {
        notifications.success.generic('Interview scheduled successfully');
        
        // Auto-send calendar invites if enabled
        if (data.sendCalendarInvite) {
          await this.sendCalendarInvites(response.data!.id);
        }
      }
      
      return {
        ...response,
        suggestions,
      };
    } catch (error) {
      notifications.error.generic('Failed to schedule interview');
      throw error;
    }
  }

  /**
   * Update interview with validation
   */
  async updateInterview(id: string, data: UpdateInterviewData) {
    try {
      // If updating scheduling, check for conflicts
      if (data.scheduledAt || data.endTime) {
        const conflicts = await this.checkSchedulingConflicts(data, id);
        if (conflicts.length > 0) {
          notifications.warning.generic('Scheduling conflicts detected');
        }
      }
      
      const response = await this.apiService.updateInterview(id, data);
      
      if (response.status === 'success') {
        notifications.success.generic('Interview updated successfully');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to update interview');
      throw error;
    }
  }

  /**
   * Reschedule interview with notifications
   */
  async rescheduleInterview(id: string, data: {
    scheduledAt: string;
    endTime?: string;
    location?: string;
    meetingUrl?: string;
    notes?: string;
  }) {
    try {
      const response = await this.apiService.rescheduleInterview(id, data);
      
      if (response.status === 'success') {
        notifications.success.generic('Interview rescheduled successfully');
        
        // Send reschedule notifications
        await this.sendRescheduleNotifications(id, data);
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to reschedule interview');
      throw error;
    }
  }

  /**
   * Cancel interview with reason
   */
  async cancelInterview(id: string, reason?: string) {
    try {
      const response = await this.apiService.cancelInterview(id, reason);
      
      if (response.status === 'success') {
        notifications.success.generic('Interview cancelled successfully');
        
        // Send cancellation notifications
        await this.sendCancellationNotifications(id, reason);
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to cancel interview');
      throw error;
    }
  }

  /**
   * Complete interview with follow-up actions
   */
  async completeInterview(id: string, data?: {
    actualEndTime?: string;
    notes?: string;
  }) {
    try {
      const response = await this.apiService.completeInterview(id, data);
      
      if (response.status === 'success') {
        notifications.success.generic('Interview completed successfully');
        
        // Trigger follow-up actions
        await this.triggerPostInterviewActions(id);
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to complete interview');
      throw error;
    }
  }

  /**
   * Get interview statistics with computed metrics
   */
  async getStatistics(filters?: InterviewFilters) {
    try {
      const response = await this.apiService.getStatistics(filters);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: this.enrichStatistics(response.data),
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to load interview statistics');
      throw error;
    }
  }

  /**
   * Search interviews with enhanced results
   */
  async searchInterviews(params: {
    query: string;
    filters?: InterviewFilters;
    page?: number;
    limit?: number;
  }) {
    try {
      const response = await this.apiService.searchInterviews(params);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: {
            ...response.data,
            interviews: response.data.interviews.map(this.enrichInterviewData),
          },
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to search interviews');
      throw error;
    }
  }

  /**
   * Get upcoming interviews with smart prioritization
   */
  async getUpcomingInterviews(params?: {
    limit?: number;
    date_from?: string;
    interviewer_id?: string;
  }) {
    try {
      const response = await this.apiService.getUpcomingInterviews(params);
      
      if (response.status === 'success' && response.data) {
        const enrichedInterviews = response.data
          .map(this.enrichInterviewData)
          .sort(this.prioritizeUpcomingInterviews);
        
        return {
          ...response,
          data: enrichedInterviews,
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to load upcoming interviews');
      throw error;
    }
  }

  /**
   * Check availability with smart suggestions
   */
  async checkAvailability(params: {
    interviewer_id: string;
    start_date: string;
    end_date: string;
    duration?: number;
  }) {
    try {
      const response = await this.apiService.checkAvailability(params);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: response.data.map(this.enrichAvailabilityData),
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to check availability');
      throw error;
    }
  }

  /**
   * Export interviews
   */
  async exportInterviews(options: InterviewExportOptions) {
    try {
      const response = await this.apiService.exportInterviews(options);
      
      if (response.status === 'success') {
        notifications.success.generic('Interviews exported successfully');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to export interviews');
      throw error;
    }
  }

  /**
   * Import interviews
   */
  async importInterviews(file: File, options?: {
    format?: 'csv' | 'json';
    merge_strategy?: 'skip' | 'update' | 'replace';
  }) {
    try {
      const response = await this.apiService.importInterviews(file, options);
      
      if (response.status === 'success' && response.data) {
        const result = response.data;
        notifications.success.generic(
          `Imported ${result.imported} interviews successfully. ${result.failed} failed, ${result.duplicates} duplicates.`
        );
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to import interviews');
      throw error;
    }
  }

  /**
   * Perform bulk operations
   */
  async bulkOperation(operation: InterviewBulkOperation) {
    try {
      const response = await this.apiService.bulkOperation(operation);
      
      if (response.status === 'success' && response.data) {
        const result = response.data;
        notifications.success.generic(
          `Bulk operation completed. ${result.success} successful, ${result.failed} failed.`
        );
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to perform bulk operation');
      throw error;
    }
  }

  // Interview Feedback Methods
  async createFeedback(data: CreateInterviewFeedbackData) {
    try {
      this.validateFeedbackData(data);
      
      const response = await this.apiService.createFeedback(data);
      
      if (response.status === 'success') {
        notifications.success.generic('Interview feedback submitted successfully');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to submit feedback');
      throw error;
    }
  }

  async updateFeedback(id: string, data: UpdateInterviewFeedbackData) {
    try {
      const response = await this.apiService.updateFeedback(id, data);
      
      if (response.status === 'success') {
        notifications.success.generic('Interview feedback updated successfully');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to update feedback');
      throw error;
    }
  }

  // Private helper methods
  private enrichInterviewData = (interview: Interview): Interview => {
    return {
      ...interview,
      // Add computed properties
      isUpcoming: new Date(interview.scheduledAt) > new Date(),
      isPast: new Date(interview.scheduledAt) < new Date(),
      isToday: this.isToday(interview.scheduledAt),
      duration: this.calculateDuration(interview.scheduledAt, interview.endTime),
      formattedDuration: this.formatDuration(interview.duration || 60),
      canReschedule: this.canReschedule(interview),
      canCancel: this.canCancel(interview),
      canComplete: this.canComplete(interview),
      urgencyLevel: this.calculateUrgencyLevel(interview),
    } as Interview;
  };

  private enrichStatistics = (stats: InterviewStatistics): InterviewStatistics => {
    const total = stats.totalInterviews;
    
    return {
      ...stats,
      // Add percentage calculations
      completionRate: total > 0 ? (stats.byStatus.completed || 0) / total * 100 : 0,
      cancellationRate: total > 0 ? (stats.byStatus.cancelled || 0) / total * 100 : 0,
      noShowRate: total > 0 ? (stats.byStatus['no-show'] || 0) / total * 100 : 0,
      averageRating: this.calculateAverageRating(stats),
    } as InterviewStatistics;
  };

  private enrichAvailabilityData = (availability: InterviewAvailability): InterviewAvailability => {
    return {
      ...availability,
      // Add computed properties
      availableSlots: availability.timeSlots?.filter(slot => slot.available).length || 0,
      busySlots: availability.timeSlots?.filter(slot => !slot.available).length || 0,
      utilizationPercentage: this.calculateUtilization(availability.timeSlots || []),
    } as InterviewAvailability;
  };

  private validateInterviewData(data: CreateInterviewData | UpdateInterviewData): void {
    if (data.scheduledAt && data.endTime) {
      const start = new Date(data.scheduledAt);
      const end = new Date(data.endTime);
      
      if (start >= end) {
        throw new Error('End time must be after start time');
      }
      
      if (start < new Date()) {
        throw new Error('Cannot schedule interviews in the past');
      }
    }
  }

  private validateSchedulingData(data: CreateInterviewData): void {
    if (!data.candidateId) {
      throw new Error('Candidate is required for scheduling');
    }
    
    if (!data.interviewerId) {
      throw new Error('Interviewer is required for scheduling');
    }
    
    if (!data.scheduledAt) {
      throw new Error('Scheduled time is required');
    }
  }

  private validateFeedbackData(data: CreateInterviewFeedbackData): void {
    if (!data.interviewId) {
      throw new Error('Interview ID is required');
    }
    
    if (!data.overallRating || data.overallRating < 1 || data.overallRating > 5) {
      throw new Error('Overall rating must be between 1 and 5');
    }
  }

  private async checkSchedulingConflicts(data: CreateInterviewData | UpdateInterviewData, excludeId?: string): Promise<Interview[]> {
    // This would typically call an API endpoint to check conflicts
    // For now, return empty array
    return [];
  }

  private async getSchedulingSuggestions(data: CreateInterviewData): Promise<string[]> {
    // Generate smart scheduling suggestions
    return [
      'Consider scheduling during business hours (9 AM - 5 PM)',
      'Allow 15 minutes buffer between interviews',
      'Video interviews tend to have higher attendance rates',
    ];
  }

  private async sendCalendarInvites(interviewId: string): Promise<void> {
    // Send calendar invites to participants
    console.log(`Sending calendar invites for interview ${interviewId}`);
  }

  private async sendRescheduleNotifications(interviewId: string, data: any): Promise<void> {
    // Send reschedule notifications
    console.log(`Sending reschedule notifications for interview ${interviewId}`);
  }

  private async sendCancellationNotifications(interviewId: string, reason?: string): Promise<void> {
    // Send cancellation notifications
    console.log(`Sending cancellation notifications for interview ${interviewId}`);
  }

  private async triggerPostInterviewActions(interviewId: string): Promise<void> {
    // Trigger post-interview actions like feedback requests
    console.log(`Triggering post-interview actions for ${interviewId}`);
  }

  private prioritizeUpcomingInterviews = (a: Interview, b: Interview): number => {
    // Sort by urgency, then by scheduled time
    const urgencyA = this.calculateUrgencyLevel(a);
    const urgencyB = this.calculateUrgencyLevel(b);
    
    if (urgencyA !== urgencyB) {
      const urgencyOrder = { low: 0, medium: 1, high: 2, critical: 3 };
      return urgencyOrder[urgencyB] - urgencyOrder[urgencyA];
    }
    
    return new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime();
  };

  // Helper methods
  private isToday(date: string): boolean {
    const today = new Date();
    const interviewDate = new Date(date);
    return today.toDateString() === interviewDate.toDateString();
  }

  private calculateDuration(start: string, end?: string): number {
    if (!end) return 60; // Default 1 hour
    return Math.round((new Date(end).getTime() - new Date(start).getTime()) / 60000);
  }

  private formatDuration(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
  }

  private canReschedule(interview: Interview): boolean {
    return interview.status === 'scheduled' && new Date(interview.scheduledAt) > new Date();
  }

  private canCancel(interview: Interview): boolean {
    return ['scheduled', 'rescheduled'].includes(interview.status);
  }

  private canComplete(interview: Interview): boolean {
    return interview.status === 'in-progress' || 
           (interview.status === 'scheduled' && new Date(interview.scheduledAt) <= new Date());
  }

  private calculateUrgencyLevel(interview: Interview): 'low' | 'medium' | 'high' | 'critical' {
    const now = new Date();
    const scheduledTime = new Date(interview.scheduledAt);
    const hoursUntil = (scheduledTime.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (hoursUntil < 1) return 'critical';
    if (hoursUntil < 4) return 'high';
    if (hoursUntil < 24) return 'medium';
    return 'low';
  }

  private calculateAverageRating(stats: InterviewStatistics): number {
    // This would calculate from actual feedback data
    return 4.2; // Placeholder
  }

  private calculateUtilization(timeSlots: any[]): number {
    if (!timeSlots.length) return 0;
    const busySlots = timeSlots.filter(slot => !slot.available).length;
    return (busySlots / timeSlots.length) * 100;
  }
}
