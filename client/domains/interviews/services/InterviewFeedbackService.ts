import {
  InterviewF<PERSON>back,
  Interview<PERSON><PERSON><PERSON><PERSON>reateRequest,
  InterviewFeedbackUpdateRequest,
  InterviewFeedbackListParams,
  ApiResponse,
  PaginatedResponse,
  FeedbackValidationErrors,
  validateFeedbackData,
} from "../types/interviewFeedback";
import { apiService } from "../api";

export class FeedbackApiError extends Error {
  public status: number;
  public errors?: FeedbackValidationErrors;

  constructor(
    message: string,
    status: number,
    errors?: FeedbackValidationErrors,
  ) {
    super(message);
    this.name = "FeedbackApiError";
    this.status = status;
    this.errors = errors;
  }
}

export class InterviewFeedbackService {
  /**
   * Get paginated list of interview feedback
   */
  async getFeedbackList(
    params: InterviewFeedbackListParams = {},
  ): Promise<PaginatedResponse<InterviewFeedback>> {
    try {
      const apiParams: any = {
        page: params.page,
        per_page: params.per_page,
        sort: params.sort,
        include: params.include,
      };

      if (params.filters) {
        apiParams.filter = params.filters;
      }

      const response = await apiService.getInterviewFeedback(apiParams);
      return response;
    } catch (error) {
      this.handleApiError(error);
    }
  }

  /**
   * Create new interview feedback
   */
  async createFeedback(
    data: InterviewFeedbackCreateRequest,
  ): Promise<ApiResponse<InterviewFeedback>> {
    try {
      // Validate data before sending
      const validationErrors = validateFeedbackData(data);
      if (Object.keys(validationErrors).length > 0) {
        throw new FeedbackApiError("Validation failed", 422, validationErrors);
      }

      const response = await apiService.submitInterviewFeedback(data);
      return response;
    } catch (error) {
      this.handleApiError(error);
    }
  }

  /**
   * Get specific interview feedback
   */
  async getFeedback(
    id: number,
    include?: string,
  ): Promise<ApiResponse<InterviewFeedback>> {
    try {
      const response = await apiService.getInterviewFeedbackDetails(
        id.toString(),
        include,
      );
      return response;
    } catch (error) {
      this.handleApiError(error);
    }
  }

  /**
   * Update interview feedback
   */
  async updateFeedback(
    id: number,
    data: InterviewFeedbackUpdateRequest,
  ): Promise<ApiResponse<InterviewFeedback>> {
    try {
      // Validate data before sending
      const validationErrors = validateFeedbackData(data);
      if (Object.keys(validationErrors).length > 0) {
        throw new FeedbackApiError("Validation failed", 422, validationErrors);
      }

      const response = await apiService.updateInterviewFeedback(
        id.toString(),
        data,
      );
      return response;
    } catch (error) {
      this.handleApiError(error);
    }
  }

  /**
   * Partially update interview feedback
   */
  async patchFeedback(
    id: number,
    data: Partial<InterviewFeedbackUpdateRequest>,
  ): Promise<ApiResponse<InterviewFeedback>> {
    try {
      const response = await apiService.patchInterviewFeedback(
        id.toString(),
        data,
      );
      return response;
    } catch (error) {
      this.handleApiError(error);
    }
  }

  /**
   * Delete interview feedback
   */
  async deleteFeedback(id: number): Promise<ApiResponse<null>> {
    try {
      const response = await apiService.deleteInterviewFeedback(id.toString());
      return response;
    } catch (error) {
      this.handleApiError(error);
    }
  }

  /**
   * Get feedback for a specific interview
   */
  async getFeedbackByInterview(
    interviewId: number,
    include?: string,
  ): Promise<PaginatedResponse<InterviewFeedback>> {
    try {
      const response = await apiService.getFeedbackByInterview(
        interviewId,
        include,
      );
      return response;
    } catch (error) {
      this.handleApiError(error);
    }
  }

  /**
   * Get feedback by interviewer
   */
  async getFeedbackByInterviewer(
    interviewerId: number,
    params: {
      page?: number;
      per_page?: number;
      include?: string;
    } = {},
  ): Promise<PaginatedResponse<InterviewFeedback>> {
    try {
      const response = await apiService.getFeedbackByInterviewer(
        interviewerId,
        params,
      );
      return response;
    } catch (error) {
      this.handleApiError(error);
    }
  }

  /**
   * Check if feedback exists for interview-interviewer combination
   */
  async checkFeedbackExists(
    interviewId: number,
    interviewerId: number,
  ): Promise<boolean> {
    try {
      const response = await this.getFeedbackList({
        filters: {
          interview_id: interviewId,
          interviewer_id: interviewerId,
        },
        per_page: 1,
      });

      return response.data && response.data.length > 0;
    } catch (error) {
      console.error("Error checking feedback existence:", error);
      return false;
    }
  }

  /**
   * Get feedback statistics for an interviewer
   */
  async getInterviewerFeedbackStats(interviewerId: number): Promise<{
    total_feedback: number;
    average_rating: number;
    positive_recommendations: number;
    average_technical_score: number;
    average_communication_score: number;
    average_cultural_fit_score: number;
    average_overall_score: number;
  }> {
    try {
      const response = await this.getFeedbackByInterviewer(interviewerId, {
        per_page: 100, // Get all feedback for statistics
      });

      const feedbacks = response.data || [];
      const total_feedback = feedbacks.length;

      if (total_feedback === 0) {
        return {
          total_feedback: 0,
          average_rating: 0,
          positive_recommendations: 0,
          average_technical_score: 0,
          average_communication_score: 0,
          average_cultural_fit_score: 0,
          average_overall_score: 0,
        };
      }

      const validRatings = feedbacks
        .map((f) => f.rating)
        .filter((r): r is number => r !== null && r !== undefined);
      const validTechnicalScores = feedbacks
        .map((f) => f.technical_score)
        .filter((s): s is number => s !== null && s !== undefined);
      const validCommunicationScores = feedbacks
        .map((f) => f.communication_score)
        .filter((s): s is number => s !== null && s !== undefined);
      const validCulturalFitScores = feedbacks
        .map((f) => f.cultural_fit_score)
        .filter((s): s is number => s !== null && s !== undefined);

      const average_rating =
        validRatings.length > 0
          ? validRatings.reduce((sum, r) => sum + r, 0) / validRatings.length
          : 0;

      const positive_recommendations = feedbacks.filter(
        (f) => f.recommend === true,
      ).length;

      const average_technical_score =
        validTechnicalScores.length > 0
          ? validTechnicalScores.reduce((sum, s) => sum + s, 0) /
            validTechnicalScores.length
          : 0;

      const average_communication_score =
        validCommunicationScores.length > 0
          ? validCommunicationScores.reduce((sum, s) => sum + s, 0) /
            validCommunicationScores.length
          : 0;

      const average_cultural_fit_score =
        validCulturalFitScores.length > 0
          ? validCulturalFitScores.reduce((sum, s) => sum + s, 0) /
            validCulturalFitScores.length
          : 0;

      const average_overall_score =
        feedbacks.reduce((sum, f) => sum + f.overall_score, 0) / total_feedback;

      return {
        total_feedback,
        average_rating: Math.round(average_rating * 100) / 100,
        positive_recommendations,
        average_technical_score:
          Math.round(average_technical_score * 100) / 100,
        average_communication_score:
          Math.round(average_communication_score * 100) / 100,
        average_cultural_fit_score:
          Math.round(average_cultural_fit_score * 100) / 100,
        average_overall_score: Math.round(average_overall_score * 100) / 100,
      };
    } catch (error) {
      console.error("Error getting interviewer feedback stats:", error);
      throw error;
    }
  }

  /**
   * Handle API errors and convert them to FeedbackApiError
   */
  private handleApiError(error: any): never {
    if (error instanceof FeedbackApiError) {
      throw error;
    }

    // Handle fetch/network errors
    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new FeedbackApiError(
        "Network error - please check your connection",
        0,
      );
    }

    // Handle API response errors
    if (error?.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          throw new FeedbackApiError("Authentication required", 401);
        case 403:
          throw new FeedbackApiError(
            "Access denied - insufficient permissions",
            403,
          );
        case 404:
          throw new FeedbackApiError("Feedback not found", 404);
        case 422:
          const validationData = data as {
            message: string;
            errors: FeedbackValidationErrors;
          };
          throw new FeedbackApiError(
            validationData.message || "Validation failed",
            422,
            validationData.errors,
          );
        case 500:
          const serverData = data as { message: string; error?: string };
          throw new FeedbackApiError(
            serverData.message || "Internal server error",
            500,
          );
        default:
          throw new FeedbackApiError(`HTTP ${status} error`, status);
      }
    }

    // Handle other errors
    if (error?.message) {
      throw new FeedbackApiError(error.message, 0);
    }

    throw new FeedbackApiError("Unknown error occurred", 0);
  }
}

// Export singleton instance
export const interviewFeedbackService = new InterviewFeedbackService();

// Utility functions for common operations
export const feedbackUtils = {
  /**
   * Format feedback for display
   */
  formatFeedbackForDisplay: (feedback: InterviewFeedback) => ({
    ...feedback,
    formatted_rating: feedback.rating ? `${feedback.rating}/5` : "Not rated",
    formatted_overall_score: `${feedback.overall_score}%`,
    has_detailed_scores:
      feedback.technical_score !== null ||
      feedback.communication_score !== null ||
      feedback.cultural_fit_score !== null,
    recommendation_text: feedback.recommend
      ? "Recommended"
      : feedback.recommend === false
        ? "Not recommended"
        : "No recommendation",
  }),

  /**
   * Check if feedback is complete
   */
  isFeedbackComplete: (feedback: InterviewFeedback): boolean => {
    return !!(
      feedback.rating &&
      feedback.comments &&
      feedback.recommend !== null &&
      (feedback.strengths?.length || feedback.concerns?.length)
    );
  },

  /**
   * Generate feedback summary
   */
  generateFeedbackSummary: (feedback: InterviewFeedback): string => {
    const parts: string[] = [];

    if (feedback.rating) {
      parts.push(`Rating: ${feedback.rating}/5`);
    }

    if (feedback.overall_score) {
      parts.push(`Overall Score: ${feedback.overall_score}%`);
    }

    if (feedback.recommend !== null) {
      parts.push(feedback.recommend ? "Recommended" : "Not Recommended");
    }

    if (feedback.next_round_recommendation) {
      parts.push(`Next: ${feedback.next_round_recommendation}`);
    }

    return parts.join(" | ");
  },
};
