import { apiService, ApiResponse, ValidationError, ApiError } from "@/lib/api";
import {
  Interviewer,
  Interviewer<PERSON>ilt<PERSON>,
  InterviewerCreateRequest,
  InterviewerUpdateRequest,
  InterviewerResponse,
  SingleInterviewerResponse,
  InterviewerDropdownOption,
} from "@/lib/types/interviewer";

export class InterviewerService {
  /**
   * Get list of interviewers with optional filtering
   */
  static async getInterviewers(
    filters: InterviewerFilters = {},
  ): Promise<ApiResponse<Interviewer[]>> {
    return apiService.getInterviewers(filters);
  }

  /**
   * Get a single interviewer by ID
   */
  static async getInterviewer(
    id: number,
    options: {
      include_interviews?: boolean;
      include_stats?: boolean;
    } = {},
  ): Promise<ApiResponse<Interviewer>> {
    return apiService.getInterviewer(id.toString(), options);
  }

  /**
   * Create a new interviewer
   */
  static async createInterviewer(
    data: InterviewerCreateRequest,
  ): Promise<ApiResponse<Interviewer>> {
    try {
      return await apiService.createInterviewer(data);
    } catch (error) {
      this.handleInterviewerError(error);
      throw error;
    }
  }

  /**
   * Update an existing interviewer
   */
  static async updateInterviewer(
    id: number,
    data: InterviewerUpdateRequest,
  ): Promise<ApiResponse<Interviewer>> {
    try {
      return await apiService.updateInterviewer(id.toString(), data);
    } catch (error) {
      this.handleInterviewerError(error);
      throw error;
    }
  }

  /**
   * Delete an interviewer
   */
  static async deleteInterviewer(id: number): Promise<ApiResponse<any>> {
    try {
      return await apiService.deleteInterviewer(id.toString());
    } catch (error) {
      this.handleInterviewerError(error);
      throw error;
    }
  }

  /**
   * Get active interviewers for dropdown components
   */
  static async getActiveInterviewersForDropdown(): Promise<
    InterviewerDropdownOption[]
  > {
    try {
      const response = await this.getInterviewers({
        is_active: true,
        include_stats: true,
      });

      if (response.status === "success" && response.data) {
        return response.data.map((interviewer) => ({
          value: interviewer.id,
          label: `${interviewer.name} (${interviewer.department})`,
          department: interviewer.department,
          expertise: interviewer.expertise,
          maxPerDay: interviewer.max_interviews_per_day,
          isActive: interviewer.is_active,
          avatar: interviewer.user.avatar,
          initials: interviewer.user.initials,
        }));
      }

      return [];
    } catch (error) {
      console.error("Error fetching active interviewers:", error);
      return [];
    }
  }

  /**
   * Get interviewers by department
   */
  static async getInterviewersByDepartment(
    department: string,
  ): Promise<ApiResponse<Interviewer[]>> {
    return this.getInterviewers({
      department,
      is_active: true,
    });
  }

  /**
   * Get interviewers by expertise
   */
  static async getInterviewersByExpertise(
    expertise: string,
  ): Promise<ApiResponse<Interviewer[]>> {
    return this.getInterviewers({
      expertise,
      is_active: true,
    });
  }

  /**
   * Validate interviewer data before submission
   */
  static validateInterviewerData(data: Partial<InterviewerCreateRequest>): {
    isValid: boolean;
    errors: Record<string, string[]>;
  } {
    const errors: Record<string, string[]> = {};

    // Validate user_id
    if (!data.user_id) {
      errors.user_id = ["User ID is required"];
    }

    // Validate max_interviews_per_day
    if (
      data.max_interviews_per_day !== undefined &&
      (data.max_interviews_per_day < 1 || data.max_interviews_per_day > 20)
    ) {
      errors.max_interviews_per_day = [
        "Maximum interviews per day must be between 1 and 20",
      ];
    }

    // Validate department length
    if (data.department && data.department.length > 100) {
      errors.department = ["Department name cannot exceed 100 characters"];
    }

    // Validate location length
    if (data.location && data.location.length > 255) {
      errors.location = ["Location cannot exceed 255 characters"];
    }

    // Validate expertise
    if (data.expertise) {
      const invalidExpertise = data.expertise.filter(
        (skill) => skill.length > 255,
      );
      if (invalidExpertise.length > 0) {
        errors.expertise = ["Each skill cannot exceed 255 characters"];
      }
    }

    // Validate time slots format
    if (data.time_slots) {
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      const invalidSlots = data.time_slots.filter(
        (slot) => !timeRegex.test(slot),
      );
      if (invalidSlots.length > 0) {
        errors.time_slots = ["Time slots must be in HH:MM format"];
      }
    }

    // Validate availability time slots
    if (data.availability) {
      const availabilityErrors: string[] = [];
      Object.entries(data.availability).forEach(([day, slots]) => {
        if (Array.isArray(slots)) {
          slots.forEach((slot, index) => {
            if (!slot.start_time || !slot.end_time) {
              availabilityErrors.push(
                `${day}: Slot ${index + 1} must have start and end time`,
              );
            } else if (slot.start_time >= slot.end_time) {
              availabilityErrors.push(
                `${day}: Slot ${index + 1} start time must be before end time`,
              );
            }
          });

          // Check for overlapping slots within the same day
          for (let i = 0; i < slots.length - 1; i++) {
            for (let j = i + 1; j < slots.length; j++) {
              const slot1 = slots[i];
              const slot2 = slots[j];
              if (
                (slot1.start_time < slot2.end_time &&
                  slot1.end_time > slot2.start_time) ||
                (slot2.start_time < slot1.end_time &&
                  slot2.end_time > slot1.start_time)
              ) {
                availabilityErrors.push(`${day}: Time slots cannot overlap`);
                break;
              }
            }
          }
        }
      });

      if (availabilityErrors.length > 0) {
        errors.availability = availabilityErrors;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  /**
   * Transform interviewer data for form editing
   */
  static transformInterviewerForForm(interviewer: Interviewer) {
    return {
      user_id: interviewer.user_id,
      department: interviewer.department,
      expertise: interviewer.expertise,
      location: interviewer.location,
      max_interviews_per_day: interviewer.max_interviews_per_day,
      timezone: interviewer.timezone,
      is_active: interviewer.is_active,
      availability: null,
      // availability: {
      //   monday: interviewer.availability.monday || [],
      //   tuesday: interviewer.availability.tuesday || [],
      //   wednesday: interviewer.availability.wednesday || [],
      //   thursday: interviewer.availability.thursday || [],
      //   friday: interviewer.availability.friday || [],
      //   saturday: interviewer.availability.saturday || [],
      //   sunday: interviewer.availability.sunday || [],
      // },
      time_slots: Array.isArray(interviewer.time_slots)
        ? interviewer.time_slots
        : [],
    };
  }

  /**
   * Handle API errors specific to interviewer operations
   */
  private static handleInterviewerError(error: any): void {
    if (error instanceof ValidationError) {
      // Handle specific validation errors
      const errors = error.validationErrors;

      if (
        errors.user_id?.some((msg: string) =>
          msg.includes("already has an interviewer profile"),
        )
      ) {
        throw new ValidationError(
          "This user already has an interviewer profile",
          { user_id: ["This user already has an interviewer profile"] },
          422,
        );
      }

      if (
        errors["availability.monday"]?.some((msg: string) =>
          msg.includes("overlap"),
        )
      ) {
        throw new ValidationError(
          "Time slots cannot overlap",
          { availability: ["Time slots for one or more days cannot overlap"] },
          422,
        );
      }
    }

    if (error instanceof ApiError) {
      if (error.statusCode === 403) {
        throw new ApiError(
          "You don't have permission to perform this action",
          403,
          error.response,
        );
      }

      if (
        error.statusCode === 422 &&
        error.message.includes("scheduled interviews")
      ) {
        throw new ApiError(
          "Cannot modify interviewer with scheduled interviews",
          422,
          error.response,
        );
      }
    }
  }

  /**
   * Check if current user can perform specific actions
   */
  static async checkPermissions(): Promise<{
    canCreate: boolean;
    canEdit: boolean;
    canDelete: boolean;
    canView: boolean;
  }> {
    // In a real app, this would check user permissions/roles
    // For now, we'll assume basic permissions based on authentication
    const isAuthenticated = !!localStorage.getItem("auth_token");

    return {
      canView: isAuthenticated,
      canCreate: isAuthenticated, // In real app: check for 'create_interviewers' permission
      canEdit: isAuthenticated, // In real app: check for 'edit_interviewers' permission
      canDelete: isAuthenticated, // In real app: check for 'delete_interviewers' permission
    };
  }

  /**
   * Generate time slots based on availability
   */
  static generateTimeSlots(
    availability: Record<string, { start_time: string; end_time: string }[]>,
  ): string[] {
    const allSlots = new Set<string>();

    Object.values(availability).forEach((daySlots) => {
      daySlots.forEach((slot) => {
        const start = new Date(`1970-01-01T${slot.start_time}:00`);
        const end = new Date(`1970-01-01T${slot.end_time}:00`);

        // Generate 30-minute intervals
        const current = new Date(start);
        while (current < end) {
          const timeString = current.toTimeString().substring(0, 5);
          allSlots.add(timeString);
          current.setMinutes(current.getMinutes() + 30);
        }
      });
    });

    return Array.from(allSlots).sort();
  }
}

export default InterviewerService;
