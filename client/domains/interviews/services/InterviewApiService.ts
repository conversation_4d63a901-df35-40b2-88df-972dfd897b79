/**
 * Interview API Service
 * Handles all interview-related API operations
 */

import { BaseService } from '@/core/api';
import { ApiClient, ApiResponse, PaginatedResponse } from '@/core/api';
import {
  Interview,
  CreateInterviewData,
  UpdateInterviewData,
  InterviewQueryParams,
  InterviewFilters,
  InterviewStatistics,
  InterviewFeedback,
  CreateInterviewFeedbackData,
  UpdateInterviewFeedbackData,
  InterviewBulkOperation,
  InterviewExportOptions,
  InterviewImportResult,
  InterviewSearchResult,
  InterviewAvailability,
  InterviewReminder
} from '../types';

export class InterviewApiService extends BaseService<Interview, CreateInterviewData, UpdateInterviewData> {
  constructor(apiClient: ApiClient) {
    super(apiClient, '/interviews');
  }

  /**
   * Get interviews with advanced filtering
   */
  async getInterviews(params?: InterviewQueryParams): Promise<PaginatedResponse<Interview>> {
    const queryParams = this.buildInterviewQueryParams(params);
    return this.apiClient.get<Interview[]>(this.baseEndpoint, queryParams);
  }

  /**
   * Get interview by ID
   */
  async getInterviewById(id: string): Promise<ApiResponse<Interview>> {
    return this.getById(id);
  }

  /**
   * Create new interview
   */
  async createInterview(data: CreateInterviewData): Promise<ApiResponse<Interview>> {
    return this.create(data);
  }

  /**
   * Update interview
   */
  async updateInterview(id: string, data: UpdateInterviewData): Promise<ApiResponse<Interview>> {
    return this.update(id, data);
  }

  /**
   * Delete interview
   */
  async deleteInterview(id: string): Promise<ApiResponse<void>> {
    return this.delete(id);
  }

  /**
   * Schedule interview
   */
  async scheduleInterview(data: CreateInterviewData): Promise<ApiResponse<Interview>> {
    return this.apiClient.post<Interview>(`${this.baseEndpoint}/schedule`, data);
  }

  /**
   * Reschedule interview
   */
  async rescheduleInterview(id: string, data: {
    scheduledAt: string;
    endTime?: string;
    location?: string;
    meetingUrl?: string;
    notes?: string;
  }): Promise<ApiResponse<Interview>> {
    return this.apiClient.put<Interview>(`${this.baseEndpoint}/${id}/reschedule`, data);
  }

  /**
   * Cancel interview
   */
  async cancelInterview(id: string, reason?: string): Promise<ApiResponse<Interview>> {
    return this.apiClient.put<Interview>(`${this.baseEndpoint}/${id}/cancel`, { reason });
  }

  /**
   * Complete interview
   */
  async completeInterview(id: string, data?: {
    actualEndTime?: string;
    notes?: string;
  }): Promise<ApiResponse<Interview>> {
    return this.apiClient.put<Interview>(`${this.baseEndpoint}/${id}/complete`, data);
  }

  /**
   * Mark interview as no-show
   */
  async markNoShow(id: string, reason?: string): Promise<ApiResponse<Interview>> {
    return this.apiClient.put<Interview>(`${this.baseEndpoint}/${id}/no-show`, { reason });
  }

  /**
   * Get interview statistics
   */
  async getStatistics(filters?: InterviewFilters): Promise<ApiResponse<InterviewStatistics>> {
    const queryParams = this.buildFilterQueryParams(filters);
    return this.apiClient.get<InterviewStatistics>(`${this.baseEndpoint}/statistics`, queryParams);
  }

  /**
   * Search interviews
   */
  async searchInterviews(params: {
    query: string;
    filters?: InterviewFilters;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<InterviewSearchResult>> {
    const queryParams = new URLSearchParams();
    queryParams.append('q', params.query);
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    
    // Add filters
    if (params.filters) {
      const filterParams = this.buildFilterQueryParams(params.filters);
      filterParams.forEach((value, key) => queryParams.append(key, value));
    }

    return this.apiClient.get<InterviewSearchResult>(`${this.baseEndpoint}/search?${queryParams}`);
  }

  /**
   * Get upcoming interviews
   */
  async getUpcomingInterviews(params?: {
    limit?: number;
    date_from?: string;
    interviewer_id?: string;
  }): Promise<ApiResponse<Interview[]>> {
    const queryParams = new URLSearchParams();
    
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.date_from) queryParams.append('date_from', params.date_from);
    if (params?.interviewer_id) queryParams.append('interviewer_id', params.interviewer_id);

    return this.apiClient.get<Interview[]>(`${this.baseEndpoint}/upcoming?${queryParams}`);
  }

  /**
   * Get calendar events for interviews
   */
  async getCalendarEvents(params: {
    start_date: string;
    end_date: string;
    interviewer_id?: string;
  }): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    queryParams.append('start_date', params.start_date);
    queryParams.append('end_date', params.end_date);
    
    if (params.interviewer_id) {
      queryParams.append('interviewer_id', params.interviewer_id);
    }

    return this.apiClient.get<any[]>(`${this.baseEndpoint}/calendar/events?${queryParams}`);
  }

  /**
   * Check interviewer availability
   */
  async checkAvailability(params: {
    interviewer_id: string;
    start_date: string;
    end_date: string;
    duration?: number;
  }): Promise<ApiResponse<InterviewAvailability[]>> {
    const queryParams = new URLSearchParams();
    queryParams.append('interviewer_id', params.interviewer_id);
    queryParams.append('start_date', params.start_date);
    queryParams.append('end_date', params.end_date);
    
    if (params.duration) {
      queryParams.append('duration', params.duration.toString());
    }

    return this.apiClient.get<InterviewAvailability[]>(`${this.baseEndpoint}/availability?${queryParams}`);
  }

  /**
   * Send interview reminders
   */
  async sendReminders(params: {
    interview_ids: string[];
    hours_before: number;
    type?: 'email' | 'sms' | 'notification';
  }): Promise<ApiResponse<{ sent_count: number; failed_count: number }>> {
    return this.apiClient.post<{ sent_count: number; failed_count: number }>(`${this.baseEndpoint}/reminders/send`, params);
  }

  /**
   * Get interview reminders
   */
  async getReminders(params?: {
    interview_id?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<InterviewReminder>> {
    const queryParams = new URLSearchParams();
    
    if (params?.interview_id) queryParams.append('interview_id', params.interview_id);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    return this.apiClient.get<InterviewReminder[]>(`${this.baseEndpoint}/reminders?${queryParams}`);
  }

  /**
   * Export interviews
   */
  async exportInterviews(options: InterviewExportOptions): Promise<ApiResponse<Blob>> {
    return this.apiClient.post<Blob>(`${this.baseEndpoint}/export`, options, {
      responseType: 'blob'
    });
  }

  /**
   * Import interviews
   */
  async importInterviews(file: File, options?: {
    format?: 'csv' | 'json';
    merge_strategy?: 'skip' | 'update' | 'replace';
  }): Promise<ApiResponse<InterviewImportResult>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (options?.format) {
      formData.append('format', options.format);
    }
    
    if (options?.merge_strategy) {
      formData.append('merge_strategy', options.merge_strategy);
    }

    return this.apiClient.post<InterviewImportResult>(`${this.baseEndpoint}/import`, formData);
  }

  /**
   * Perform bulk operations on interviews
   */
  async bulkOperation(operation: InterviewBulkOperation): Promise<ApiResponse<{ success: number; failed: number; errors: string[] }>> {
    return this.apiClient.post<{ success: number; failed: number; errors: string[] }>(`${this.baseEndpoint}/bulk`, operation);
  }

  // Interview Feedback Methods
  /**
   * Get interview feedback
   */
  async getFeedback(params?: {
    interview_id?: string;
    interviewer_id?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<InterviewFeedback>> {
    const queryParams = new URLSearchParams();
    
    if (params?.interview_id) queryParams.append('interview_id', params.interview_id);
    if (params?.interviewer_id) queryParams.append('interviewer_id', params.interviewer_id);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    return this.apiClient.get<InterviewFeedback[]>(`${this.baseEndpoint}/feedback?${queryParams}`);
  }

  /**
   * Get feedback by ID
   */
  async getFeedbackById(id: string): Promise<ApiResponse<InterviewFeedback>> {
    return this.apiClient.get<InterviewFeedback>(`${this.baseEndpoint}/feedback/${id}`);
  }

  /**
   * Create interview feedback
   */
  async createFeedback(data: CreateInterviewFeedbackData): Promise<ApiResponse<InterviewFeedback>> {
    return this.apiClient.post<InterviewFeedback>(`${this.baseEndpoint}/feedback`, data);
  }

  /**
   * Update interview feedback
   */
  async updateFeedback(id: string, data: UpdateInterviewFeedbackData): Promise<ApiResponse<InterviewFeedback>> {
    return this.apiClient.put<InterviewFeedback>(`${this.baseEndpoint}/feedback/${id}`, data);
  }

  /**
   * Delete interview feedback
   */
  async deleteFeedback(id: string): Promise<ApiResponse<void>> {
    return this.apiClient.delete<void>(`${this.baseEndpoint}/feedback/${id}`);
  }

  /**
   * Build query parameters for interview filtering
   */
  private buildInterviewQueryParams(params?: InterviewQueryParams): URLSearchParams {
    const queryParams = new URLSearchParams();
    
    if (!params) return queryParams;

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sort) queryParams.append('sort', params.sort);
    if (params.search) queryParams.append('search', params.search);
    
    // Date filters
    if (params.dateFrom) queryParams.append('date_from', params.dateFrom);
    if (params.dateTo) queryParams.append('date_to', params.dateTo);
    
    // Array parameters
    if (params.status?.length) {
      params.status.forEach(s => queryParams.append('status[]', s));
    }
    
    if (params.type?.length) {
      params.type.forEach(t => queryParams.append('type[]', t));
    }
    
    if (params.round?.length) {
      params.round.forEach(r => queryParams.append('round[]', r));
    }
    
    if (params.include?.length) {
      params.include.forEach(inc => queryParams.append('include[]', inc));
    }

    // Single value parameters
    if (params.interviewerId) queryParams.append('interviewer_id', params.interviewerId);
    if (params.candidateId) queryParams.append('candidate_id', params.candidateId);
    if (params.jobId) queryParams.append('job_id', params.jobId);

    return queryParams;
  }

  /**
   * Build query parameters for interview filters
   */
  private buildFilterQueryParams(filters?: InterviewFilters): URLSearchParams {
    const queryParams = new URLSearchParams();
    
    if (!filters) return queryParams;

    if (filters.search) queryParams.append('search', filters.search);
    if (filters.dateFrom) queryParams.append('date_from', filters.dateFrom);
    if (filters.dateTo) queryParams.append('date_to', filters.dateTo);
    
    // Array parameters
    if (filters.status?.length) {
      filters.status.forEach(s => queryParams.append('status[]', s));
    }
    
    if (filters.type?.length) {
      filters.type.forEach(t => queryParams.append('type[]', t));
    }
    
    if (filters.round?.length) {
      filters.round.forEach(r => queryParams.append('round[]', r));
    }

    // Single value parameters
    if (filters.interviewerId) queryParams.append('interviewer_id', filters.interviewerId);
    if (filters.candidateId) queryParams.append('candidate_id', filters.candidateId);
    if (filters.jobId) queryParams.append('job_id', filters.jobId);

    return queryParams;
  }
}
