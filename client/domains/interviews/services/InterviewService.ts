/**
 * Interview Service
 * Main service that combines API and business logic for interview operations
 */

import { ApiClient } from '@/core/api';
import { InterviewApiService } from './InterviewApiService';
import { InterviewBusinessService } from './InterviewBusinessService';
import {
  Interview,
  CreateInterviewData,
  UpdateInterviewData,
  InterviewFilters,
  InterviewStatistics,
  InterviewFeedback,
  CreateInterviewFeedbackData,
  UpdateInterviewFeedbackData,
  InterviewBulkOperation,
  InterviewExportOptions,
  InterviewImportResult,
  InterviewSearchResult,
  InterviewQueryParams,
  InterviewAvailability,
  InterviewReminder
} from '../types';

class InterviewServiceClass {
  private apiService: InterviewApiService;
  private businessService: InterviewBusinessService;

  constructor() {
    const apiClient = new ApiClient();
    this.apiService = new InterviewApiService(apiClient);
    this.businessService = new InterviewBusinessService(this.apiService);
  }

  // Interview Management
  async getInterviews(params?: InterviewQueryParams) {
    return this.businessService.getInterviews(params);
  }

  async getInterviewById(id: string) {
    return this.businessService.getInterviewById(id);
  }

  async createInterview(data: CreateInterviewData) {
    return this.businessService.createInterview(data);
  }

  async scheduleInterview(data: CreateInterviewData) {
    return this.businessService.scheduleInterview(data);
  }

  async updateInterview(id: string, data: UpdateInterviewData) {
    return this.businessService.updateInterview(id, data);
  }

  async rescheduleInterview(id: string, data: {
    scheduledAt: string;
    endTime?: string;
    location?: string;
    meetingUrl?: string;
    notes?: string;
  }) {
    return this.businessService.rescheduleInterview(id, data);
  }

  async cancelInterview(id: string, reason?: string) {
    return this.businessService.cancelInterview(id, reason);
  }

  async completeInterview(id: string, data?: {
    actualEndTime?: string;
    notes?: string;
  }) {
    return this.businessService.completeInterview(id, data);
  }

  async markNoShow(id: string, reason?: string) {
    return this.apiService.markNoShow(id, reason);
  }

  async deleteInterview(id: string) {
    return this.apiService.deleteInterview(id);
  }

  // Statistics and Analytics
  async getStatistics(filters?: InterviewFilters) {
    return this.businessService.getStatistics(filters);
  }

  // Search and Discovery
  async searchInterviews(params: {
    query: string;
    filters?: InterviewFilters;
    page?: number;
    limit?: number;
  }) {
    return this.businessService.searchInterviews(params);
  }

  async getUpcomingInterviews(params?: {
    limit?: number;
    date_from?: string;
    interviewer_id?: string;
  }) {
    return this.businessService.getUpcomingInterviews(params);
  }

  // Calendar Integration
  async getCalendarEvents(params: {
    start_date: string;
    end_date: string;
    interviewer_id?: string;
  }) {
    return this.apiService.getCalendarEvents(params);
  }

  // Availability Management
  async checkAvailability(params: {
    interviewer_id: string;
    start_date: string;
    end_date: string;
    duration?: number;
  }) {
    return this.businessService.checkAvailability(params);
  }

  // Reminders and Notifications
  async sendReminders(params: {
    interview_ids: string[];
    hours_before: number;
    type?: 'email' | 'sms' | 'notification';
  }) {
    return this.apiService.sendReminders(params);
  }

  async getReminders(params?: {
    interview_id?: string;
    status?: string;
    page?: number;
    limit?: number;
  }) {
    return this.apiService.getReminders(params);
  }

  // Import/Export
  async exportInterviews(options: InterviewExportOptions) {
    return this.businessService.exportInterviews(options);
  }

  async importInterviews(file: File, options?: {
    format?: 'csv' | 'json';
    merge_strategy?: 'skip' | 'update' | 'replace';
  }) {
    return this.businessService.importInterviews(file, options);
  }

  // Bulk Operations
  async bulkOperation(operation: InterviewBulkOperation) {
    return this.businessService.bulkOperation(operation);
  }

  // Feedback Management
  async getFeedback(params?: {
    interview_id?: string;
    interviewer_id?: string;
    page?: number;
    limit?: number;
  }) {
    return this.apiService.getFeedback(params);
  }

  async getFeedbackById(id: string) {
    return this.apiService.getFeedbackById(id);
  }

  async createFeedback(data: CreateInterviewFeedbackData) {
    return this.businessService.createFeedback(data);
  }

  async updateFeedback(id: string, data: UpdateInterviewFeedbackData) {
    return this.businessService.updateFeedback(id, data);
  }

  async deleteFeedback(id: string) {
    return this.apiService.deleteFeedback(id);
  }

  // Convenience methods for common operations
  async getTodayInterviews(interviewerId?: string) {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

    return this.getInterviews({
      dateFrom: startOfDay.toISOString(),
      dateTo: endOfDay.toISOString(),
      interviewerId,
      status: ['scheduled', 'in-progress'],
    });
  }

  async getThisWeekInterviews(interviewerId?: string) {
    const now = new Date();
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const endOfWeek = new Date(now.setDate(now.getDate() - now.getDay() + 6));

    return this.getInterviews({
      dateFrom: startOfWeek.toISOString(),
      dateTo: endOfWeek.toISOString(),
      interviewerId,
      sort: 'scheduled_at',
    });
  }

  async getInterviewsByCandidate(candidateId: string) {
    return this.getInterviews({
      candidateId,
      sort: 'scheduled_at',
    });
  }

  async getInterviewsByJob(jobId: string) {
    return this.getInterviews({
      jobId,
      sort: 'scheduled_at',
    });
  }

  async getInterviewsByInterviewer(interviewerId: string, dateFrom?: string, dateTo?: string) {
    return this.getInterviews({
      interviewerId,
      dateFrom,
      dateTo,
      sort: 'scheduled_at',
    });
  }

  async getInterviewsByStatus(status: string, limit?: number) {
    return this.getInterviews({
      status: [status as any],
      limit,
      sort: 'scheduled_at',
    });
  }

  async getInterviewsByType(type: string, dateFrom?: string, dateTo?: string) {
    return this.getInterviews({
      type: [type as any],
      dateFrom,
      dateTo,
      sort: 'scheduled_at',
    });
  }

  async getInterviewsByRound(round: string, jobId?: string) {
    return this.getInterviews({
      round: [round as any],
      jobId,
      sort: 'scheduled_at',
    });
  }

  // Status transition helpers
  async startInterview(id: string) {
    return this.updateInterview(id, {
      status: 'in-progress',
      actualStartTime: new Date().toISOString(),
    });
  }

  async endInterview(id: string, notes?: string) {
    return this.completeInterview(id, {
      actualEndTime: new Date().toISOString(),
      notes,
    });
  }

  // Scheduling helpers
  async findAvailableSlots(params: {
    interviewer_id: string;
    date: string;
    duration?: number;
  }) {
    const startOfDay = new Date(params.date);
    startOfDay.setHours(9, 0, 0, 0); // 9 AM
    
    const endOfDay = new Date(params.date);
    endOfDay.setHours(17, 0, 0, 0); // 5 PM

    return this.checkAvailability({
      interviewer_id: params.interviewer_id,
      start_date: startOfDay.toISOString(),
      end_date: endOfDay.toISOString(),
      duration: params.duration || 60,
    });
  }

  async suggestRescheduleOptions(interviewId: string, preferredDates: string[]) {
    const interview = await this.getInterviewById(interviewId);
    
    if (interview.status !== 'success' || !interview.data) {
      throw new Error('Interview not found');
    }

    const suggestions = [];
    
    for (const date of preferredDates) {
      const availability = await this.findAvailableSlots({
        interviewer_id: interview.data.interviewerId,
        date,
        duration: interview.data.duration || 60,
      });
      
      if (availability.status === 'success' && availability.data) {
        suggestions.push({
          date,
          availableSlots: availability.data.filter(slot => slot.availableSlots > 0),
        });
      }
    }
    
    return suggestions;
  }

  // Validation helpers
  validateInterviewData(data: CreateInterviewData | UpdateInterviewData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (data.scheduledAt && data.endTime) {
      const start = new Date(data.scheduledAt);
      const end = new Date(data.endTime);

      if (start >= end) {
        errors.push('End time must be after start time');
      }

      if (start < new Date()) {
        errors.push('Cannot schedule interviews in the past');
      }
    }

    if (data.scheduledAt) {
      const scheduledTime = new Date(data.scheduledAt);
      const hour = scheduledTime.getHours();
      
      if (hour < 8 || hour > 18) {
        errors.push('Interviews should be scheduled during business hours (8 AM - 6 PM)');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Time zone utilities
  convertToTimezone(date: string, timezone: string): string {
    return new Date(date).toLocaleString('en-US', { timeZone: timezone });
  }

  formatInterviewTime(interview: Interview, timezone?: string): string {
    const start = new Date(interview.scheduledAt);
    const end = interview.endTime ? new Date(interview.endTime) : new Date(start.getTime() + (interview.duration || 60) * 60000);
    
    const timeZone = timezone || 'UTC';
    const startTime = start.toLocaleTimeString('en-US', { 
      timeZone, 
      hour: 'numeric', 
      minute: '2-digit' 
    });
    const endTime = end.toLocaleTimeString('en-US', { 
      timeZone, 
      hour: 'numeric', 
      minute: '2-digit' 
    });

    return `${startTime} - ${endTime}`;
  }

  // Conflict detection
  async checkSchedulingConflicts(data: CreateInterviewData | UpdateInterviewData, excludeId?: string) {
    if (!data.scheduledAt || !data.interviewerId) {
      return [];
    }

    const startTime = new Date(data.scheduledAt);
    const endTime = data.endTime ? new Date(data.endTime) : new Date(startTime.getTime() + 60 * 60000);

    const existingInterviews = await this.getInterviews({
      interviewerId: data.interviewerId,
      dateFrom: startTime.toISOString(),
      dateTo: endTime.toISOString(),
      status: ['scheduled', 'in-progress'],
    });

    if (existingInterviews.status === 'success' && existingInterviews.data) {
      return existingInterviews.data.filter(interview => {
        if (excludeId && interview.id === excludeId) {
          return false;
        }

        const interviewStart = new Date(interview.scheduledAt);
        const interviewEnd = interview.endTime ? new Date(interview.endTime) : new Date(interviewStart.getTime() + (interview.duration || 60) * 60000);

        // Check for overlap
        return (startTime < interviewEnd && endTime > interviewStart);
      });
    }

    return [];
  }
}

// Export singleton instance
export const InterviewService = new InterviewServiceClass();
