/**
 * Communications Domain Types
 * Types for messaging, templates, and communications
 */

import { BaseEntity } from '@/core/api';

// Message types
export type MessageType = 'email' | 'sms' | 'notification' | 'system';
export type MessageStatus = 'draft' | 'sent' | 'delivered' | 'failed' | 'scheduled';
export type MessagePriority = 'low' | 'normal' | 'high' | 'urgent';

// Template types
export type TemplateType = 'email' | 'sms' | 'notification';
export type TemplateCategory = 
  | 'interview_invitation'
  | 'interview_reminder'
  | 'interview_feedback'
  | 'job_application'
  | 'offer_letter'
  | 'rejection'
  | 'follow_up'
  | 'welcome'
  | 'general';

// Core Message interface
export interface Message extends BaseEntity {
  // Basic Information
  subject?: string;
  content: string;
  type: MessageType;
  status: MessageStatus;
  priority: MessagePriority;
  
  // Recipients
  recipientType: 'candidate' | 'interviewer' | 'team' | 'external';
  recipientId?: string;
  recipientEmail: string;
  recipientName?: string;
  
  // Sender
  senderId: string;
  senderName: string;
  senderEmail: string;
  
  // Template Information
  templateId?: string;
  templateName?: string;
  
  // Scheduling
  scheduledAt?: string;
  sentAt?: string;
  deliveredAt?: string;
  
  // Metadata
  variables?: Record<string, any>;
  attachments?: MessageAttachment[];
  tags?: string[];
  
  // Tracking
  openedAt?: string;
  clickedAt?: string;
  bounced?: boolean;
  unsubscribed?: boolean;
  
  // Related Entities
  candidateId?: string;
  jobId?: string;
  interviewId?: string;
}

// Message Template interface
export interface MessageTemplate extends BaseEntity {
  // Basic Information
  name: string;
  description?: string;
  subject?: string;
  content: string;
  
  // Template Configuration
  type: TemplateType;
  category: TemplateCategory;
  isActive: boolean;
  isDefault?: boolean;
  
  // Variables
  variables: TemplateVariable[];
  requiredVariables: string[];
  
  // Metadata
  tags?: string[];
  language?: string;
  
  // Usage Statistics
  usageCount?: number;
  lastUsedAt?: string;
  
  // Ownership
  createdBy: string;
  departmentId?: string;
}

// Template Variable interface
export interface TemplateVariable {
  name: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select';
  required: boolean;
  defaultValue?: any;
  options?: string[]; // for select type
  description?: string;
  placeholder?: string;
}

// Message Attachment interface
export interface MessageAttachment {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  url: string;
  isInline?: boolean;
}

// Message Campaign interface
export interface MessageCampaign extends BaseEntity {
  // Basic Information
  name: string;
  description?: string;
  
  // Campaign Configuration
  templateId: string;
  templateName: string;
  
  // Recipients
  recipientType: 'candidates' | 'interviewers' | 'team';
  recipientFilters?: Record<string, any>;
  recipientIds?: string[];
  totalRecipients: number;
  
  // Scheduling
  scheduledAt?: string;
  startedAt?: string;
  completedAt?: string;
  
  // Status
  status: 'draft' | 'scheduled' | 'running' | 'completed' | 'cancelled' | 'failed';
  
  // Statistics
  sentCount: number;
  deliveredCount: number;
  openedCount: number;
  clickedCount: number;
  failedCount: number;
  
  // Settings
  sendRate?: number; // messages per hour
  timezone?: string;
}

// Notification interface
export interface Notification extends BaseEntity {
  // Basic Information
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  
  // Recipients
  userId: string;
  userEmail: string;
  
  // Status
  isRead: boolean;
  readAt?: string;
  
  // Action
  actionUrl?: string;
  actionLabel?: string;
  
  // Related Entities
  entityType?: 'candidate' | 'job' | 'interview' | 'application';
  entityId?: string;
  
  // Metadata
  metadata?: Record<string, any>;
}

// Communication Settings interface
export interface CommunicationSettings {
  // Email Settings
  emailEnabled: boolean;
  emailSignature?: string;
  emailFromName?: string;
  emailFromAddress?: string;
  
  // SMS Settings
  smsEnabled: boolean;
  smsFromNumber?: string;
  
  // Notification Settings
  notificationsEnabled: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  
  // Template Settings
  defaultTemplates: Record<TemplateCategory, string>;
  
  // Automation Settings
  autoResponders: boolean;
  followUpReminders: boolean;
  interviewReminders: boolean;
}

// Message Statistics interface
export interface MessageStatistics {
  totalSent: number;
  totalDelivered: number;
  totalOpened: number;
  totalClicked: number;
  totalFailed: number;
  
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  bounceRate: number;
  
  byType: Record<MessageType, {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
  }>;
  
  byTemplate: Record<string, {
    name: string;
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
  }>;
  
  timeline: Array<{
    date: string;
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
  }>;
}

// Create message data
export interface CreateMessageData {
  subject?: string;
  content: string;
  type: MessageType;
  priority?: MessagePriority;
  recipientType: 'candidate' | 'interviewer' | 'team' | 'external';
  recipientId?: string;
  recipientEmail: string;
  recipientName?: string;
  templateId?: string;
  scheduledAt?: string;
  variables?: Record<string, any>;
  attachments?: File[];
  tags?: string[];
  candidateId?: string;
  jobId?: string;
  interviewId?: string;
}

// Create template data
export interface CreateTemplateData {
  name: string;
  description?: string;
  subject?: string;
  content: string;
  type: TemplateType;
  category: TemplateCategory;
  variables: TemplateVariable[];
  tags?: string[];
  language?: string;
}

// Message filters
export interface MessageFilters {
  type?: MessageType[];
  status?: MessageStatus[];
  priority?: MessagePriority[];
  recipientType?: string[];
  senderId?: string;
  templateId?: string;
  dateRange?: {
    from: string;
    to: string;
  };
  tags?: string[];
  search?: string;
}
