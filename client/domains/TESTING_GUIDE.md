# Domain Testing Guide

This guide provides comprehensive testing strategies and examples for the domain-driven architecture in HireFlow ATS.

## 🧪 Testing Philosophy

Our testing approach follows the Testing Pyramid:
- **Unit Tests (70%)**: Test individual components, hooks, and utilities
- **Integration Tests (20%)**: Test component interactions and data flow
- **E2E Tests (10%)**: Test complete user workflows

## 🛠️ Testing Stack

- **Test Runner**: Vitest
- **Testing Library**: React Testing Library
- **Mocking**: MSW (Mock Service Worker)
- **Assertions**: Vitest assertions + Jest DOM matchers
- **Coverage**: Vitest coverage

## 📁 Test Structure

```
domains/
├── candidates/
│   ├── __tests__/
│   │   ├── components/
│   │   │   ├── CandidateCard.test.tsx
│   │   │   ├── CandidateTable.test.tsx
│   │   │   └── CandidateModal.test.tsx
│   │   ├── hooks/
│   │   │   ├── useCandidates.test.ts
│   │   │   └── useCreateCandidate.test.ts
│   │   ├── services/
│   │   │   └── candidateService.test.ts
│   │   └── utils/
│   │       └── validation.test.ts
│   └── ...
```

## 🎯 Component Testing

### Basic Component Test

```typescript
// domains/candidates/__tests__/components/CandidateCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { CandidateCard } from '../../components/CandidateCard';
import { mockCandidate } from '../__mocks__/candidate.mock';

describe('CandidateCard', () => {
  it('renders candidate information correctly', () => {
    render(<CandidateCard candidate={mockCandidate} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Software Engineer')).toBeInTheDocument();
  });

  it('displays correct status badge', () => {
    render(<CandidateCard candidate={mockCandidate} />);
    
    const statusBadge = screen.getByText('Applied');
    expect(statusBadge).toBeInTheDocument();
    expect(statusBadge).toHaveClass('bg-blue-100'); // Assuming applied status is blue
  });

  it('calls onEdit when edit button is clicked', () => {
    const onEdit = vi.fn();
    render(<CandidateCard candidate={mockCandidate} onEdit={onEdit} />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);
    
    expect(onEdit).toHaveBeenCalledWith(mockCandidate);
  });

  it('calls onDelete when delete button is clicked', () => {
    const onDelete = vi.fn();
    render(<CandidateCard candidate={mockCandidate} onDelete={onDelete} />);
    
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(deleteButton);
    
    expect(onDelete).toHaveBeenCalledWith(mockCandidate.id);
  });

  it('hides actions when showActions is false', () => {
    render(<CandidateCard candidate={mockCandidate} showActions={false} />);
    
    expect(screen.queryByRole('button', { name: /edit/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /delete/i })).not.toBeInTheDocument();
  });
});
```

### Modal Component Test

```typescript
// domains/candidates/__tests__/components/CandidateModal.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CandidateModal } from '../../components/CandidateModal';
import { mockCandidate } from '../__mocks__/candidate.mock';

describe('CandidateModal', () => {
  it('renders modal when isOpen is true', () => {
    render(
      <CandidateModal 
        candidate={mockCandidate} 
        isOpen={true} 
        onClose={vi.fn()} 
      />
    );
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Candidate Details')).toBeInTheDocument();
  });

  it('does not render modal when isOpen is false', () => {
    render(
      <CandidateModal 
        candidate={mockCandidate} 
        isOpen={false} 
        onClose={vi.fn()} 
      />
    );
    
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    const onClose = vi.fn();
    render(
      <CandidateModal 
        candidate={mockCandidate} 
        isOpen={true} 
        onClose={onClose} 
      />
    );
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    expect(onClose).toHaveBeenCalled();
  });

  it('calls onClose when escape key is pressed', () => {
    const onClose = vi.fn();
    render(
      <CandidateModal 
        candidate={mockCandidate} 
        isOpen={true} 
        onClose={onClose} 
      />
    );
    
    fireEvent.keyDown(document, { key: 'Escape' });
    
    expect(onClose).toHaveBeenCalled();
  });
});
```

## 🪝 Hook Testing

### Query Hook Test

```typescript
// domains/candidates/__tests__/hooks/useCandidates.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useCandidates } from '../../hooks/useCandidates';
import { candidateService } from '../../services/candidateService';
import { mockCandidates } from '../__mocks__/candidate.mock';

// Mock the service
vi.mock('../../services/candidateService');

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useCandidates', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('fetches candidates successfully', async () => {
    vi.mocked(candidateService.getCandidates).mockResolvedValue({
      data: mockCandidates,
      success: true,
    });

    const { result } = renderHook(() => useCandidates(), {
      wrapper: createWrapper(),
    });

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toEqual(mockCandidates);
    expect(result.current.error).toBeNull();
  });

  it('handles error when fetching candidates fails', async () => {
    const errorMessage = 'Failed to fetch candidates';
    vi.mocked(candidateService.getCandidates).mockRejectedValue(
      new Error(errorMessage)
    );

    const { result } = renderHook(() => useCandidates(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toBeTruthy();
  });

  it('passes filters to service correctly', async () => {
    const filters = { status: 'applied', search: 'john' };
    
    vi.mocked(candidateService.getCandidates).mockResolvedValue({
      data: mockCandidates,
      success: true,
    });

    renderHook(() => useCandidates(filters), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(candidateService.getCandidates).toHaveBeenCalledWith(filters);
    });
  });
});
```

### Mutation Hook Test

```typescript
// domains/candidates/__tests__/hooks/useCreateCandidate.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useCreateCandidate } from '../../hooks/useCreateCandidate';
import { candidateService } from '../../services/candidateService';
import { mockCandidate, mockCreateCandidateData } from '../__mocks__/candidate.mock';

vi.mock('../../services/candidateService');

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useCreateCandidate', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('creates candidate successfully', async () => {
    vi.mocked(candidateService.createCandidate).mockResolvedValue({
      data: mockCandidate,
      success: true,
    });

    const { result } = renderHook(() => useCreateCandidate(), {
      wrapper: createWrapper(),
    });

    result.current.mutate(mockCreateCandidateData);

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual({ data: mockCandidate, success: true });
    expect(candidateService.createCandidate).toHaveBeenCalledWith(mockCreateCandidateData);
  });

  it('handles error when creating candidate fails', async () => {
    const errorMessage = 'Failed to create candidate';
    vi.mocked(candidateService.createCandidate).mockRejectedValue(
      new Error(errorMessage)
    );

    const { result } = renderHook(() => useCreateCandidate(), {
      wrapper: createWrapper(),
    });

    result.current.mutate(mockCreateCandidateData);

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toBeTruthy();
  });
});
```

## 🔧 Service Testing

```typescript
// domains/candidates/__tests__/services/candidateService.test.ts
import { candidateService } from '../../services/candidateService';
import { mockCandidate, mockCreateCandidateData } from '../__mocks__/candidate.mock';

// Mock the base service
vi.mock('../../../core/api/BaseService');

describe('CandidateService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getCandidates', () => {
    it('calls GET /candidates with correct parameters', async () => {
      const filters = { status: 'applied' };
      const mockResponse = { data: [mockCandidate], success: true };
      
      vi.spyOn(candidateService, 'get').mockResolvedValue(mockResponse);

      const result = await candidateService.getCandidates(filters);

      expect(candidateService.get).toHaveBeenCalledWith('/candidates', {
        params: filters,
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createCandidate', () => {
    it('calls POST /candidates with correct data', async () => {
      const mockResponse = { data: mockCandidate, success: true };
      
      vi.spyOn(candidateService, 'post').mockResolvedValue(mockResponse);

      const result = await candidateService.createCandidate(mockCreateCandidateData);

      expect(candidateService.post).toHaveBeenCalledWith('/candidates', mockCreateCandidateData);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateCandidate', () => {
    it('calls PUT /candidates/:id with correct data', async () => {
      const candidateId = '123';
      const updateData = { firstName: 'Jane' };
      const mockResponse = { data: { ...mockCandidate, ...updateData }, success: true };
      
      vi.spyOn(candidateService, 'put').mockResolvedValue(mockResponse);

      const result = await candidateService.updateCandidate(candidateId, updateData);

      expect(candidateService.put).toHaveBeenCalledWith(`/candidates/${candidateId}`, updateData);
      expect(result).toEqual(mockResponse);
    });
  });
});
```

## 🎭 Mock Data

```typescript
// domains/candidates/__tests__/__mocks__/candidate.mock.ts
import { Candidate, CreateCandidateData } from '../../types';

export const mockCandidate: Candidate = {
  id: '1',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+**********',
  title: 'Software Engineer',
  experience: 5,
  skills: ['JavaScript', 'React', 'Node.js'],
  status: 'applied',
  source: 'linkedin',
  appliedAt: '2024-01-15T10:00:00Z',
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
  fullName: 'John Doe',
  isActive: true,
};

export const mockCandidates: Candidate[] = [
  mockCandidate,
  {
    ...mockCandidate,
    id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    fullName: 'Jane Smith',
  },
];

export const mockCreateCandidateData: CreateCandidateData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+**********',
  title: 'Software Engineer',
  skills: ['JavaScript', 'React', 'Node.js'],
  source: 'linkedin',
};
```

## 🌐 Integration Testing

```typescript
// domains/candidates/__tests__/integration/CandidateManagement.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CandidateManagement } from '../../components/CandidateManagement';
import { server } from '../__mocks__/server';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('CandidateManagement Integration', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  it('loads and displays candidates', async () => {
    render(<CandidateManagement />, { wrapper: createWrapper() });

    expect(screen.getByText('Loading...')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });
  });

  it('creates new candidate successfully', async () => {
    render(<CandidateManagement />, { wrapper: createWrapper() });

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // Click create button
    fireEvent.click(screen.getByRole('button', { name: /add candidate/i }));

    // Fill form
    fireEvent.change(screen.getByLabelText(/first name/i), {
      target: { value: 'New' },
    });
    fireEvent.change(screen.getByLabelText(/last name/i), {
      target: { value: 'Candidate' },
    });
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });

    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /save/i }));

    // Verify new candidate appears
    await waitFor(() => {
      expect(screen.getByText('New Candidate')).toBeInTheDocument();
    });
  });
});
```

## 🚀 Running Tests

### Commands

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific domain tests
npm run test domains/candidates

# Run specific test file
npm run test CandidateCard.test.tsx
```

### Coverage Targets

- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 80%
- **Lines**: 80%

## 📋 Testing Checklist

### Component Testing
- [ ] Renders correctly with required props
- [ ] Handles optional props appropriately
- [ ] Calls event handlers with correct parameters
- [ ] Displays loading and error states
- [ ] Handles edge cases (empty data, null values)
- [ ] Accessibility features work correctly

### Hook Testing
- [ ] Returns correct initial state
- [ ] Handles successful API responses
- [ ] Handles API errors gracefully
- [ ] Invalidates cache when appropriate
- [ ] Passes correct parameters to services

### Service Testing
- [ ] Makes correct API calls
- [ ] Transforms data appropriately
- [ ] Handles network errors
- [ ] Retries failed requests when configured

### Integration Testing
- [ ] Components work together correctly
- [ ] Data flows properly between components
- [ ] User workflows complete successfully
- [ ] Error boundaries catch and handle errors

## 🔍 Best Practices

1. **Test Behavior, Not Implementation**: Focus on what the component does, not how it does it
2. **Use Descriptive Test Names**: Test names should clearly describe what is being tested
3. **Keep Tests Simple**: Each test should focus on one specific behavior
4. **Mock External Dependencies**: Mock API calls, external services, and complex dependencies
5. **Test Edge Cases**: Include tests for error states, empty data, and boundary conditions
6. **Maintain Test Data**: Keep mock data consistent and realistic
7. **Regular Test Maintenance**: Update tests when requirements change
