/**
 * Users Domain Types
 * Types for user management, teams, and permissions
 */

import { BaseEntity } from '@/core/api';

// User roles and permissions
export type UserRole = 
  | 'admin'
  | 'hr_manager'
  | 'recruiter'
  | 'hiring_manager'
  | 'interviewer'
  | 'viewer';

export type Permission = 
  | 'candidates.view'
  | 'candidates.create'
  | 'candidates.edit'
  | 'candidates.delete'
  | 'jobs.view'
  | 'jobs.create'
  | 'jobs.edit'
  | 'jobs.delete'
  | 'interviews.view'
  | 'interviews.create'
  | 'interviews.edit'
  | 'interviews.delete'
  | 'analytics.view'
  | 'users.view'
  | 'users.create'
  | 'users.edit'
  | 'users.delete'
  | 'settings.view'
  | 'settings.edit';

export type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended';

// Core User interface
export interface User extends BaseEntity {
  // Basic Information
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  avatar?: string;
  
  // Authentication
  password?: string; // Only for creation/updates
  lastLoginAt?: string;
  emailVerified: boolean;
  emailVerifiedAt?: string;
  
  // Profile Information
  title?: string;
  department?: string;
  location?: string;
  timezone?: string;
  language?: string;
  
  // Role and Permissions
  role: UserRole;
  permissions: Permission[];
  customPermissions?: Permission[];
  
  // Status
  status: UserStatus;
  isActive: boolean;
  
  // Team Information
  managerId?: string;
  managerName?: string;
  teamId?: string;
  teamName?: string;
  
  // Preferences
  preferences: UserPreferences;
  
  // Statistics
  candidatesManaged?: number;
  interviewsConducted?: number;
  jobsPosted?: number;
  
  // Metadata
  notes?: string;
  tags?: string[];
  
  // Computed fields
  fullName?: string;
  initials?: string;
  isManager?: boolean;
}

// User Preferences interface
export interface UserPreferences {
  // Notifications
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  
  // Email Preferences
  dailyDigest: boolean;
  weeklyReport: boolean;
  candidateUpdates: boolean;
  interviewReminders: boolean;
  jobApplications: boolean;
  
  // UI Preferences
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  
  // Dashboard Preferences
  defaultView: 'dashboard' | 'candidates' | 'jobs' | 'interviews';
  dashboardWidgets: string[];
  
  // Privacy
  profileVisibility: 'public' | 'team' | 'private';
  showEmail: boolean;
  showPhone: boolean;
}

// Team interface
export interface Team extends BaseEntity {
  // Basic Information
  name: string;
  description?: string;
  
  // Team Structure
  managerId: string;
  managerName: string;
  memberIds: string[];
  memberCount: number;
  
  // Department Information
  department: string;
  location?: string;
  
  // Permissions
  defaultRole: UserRole;
  permissions: Permission[];
  
  // Statistics
  candidatesManaged: number;
  jobsPosted: number;
  interviewsConducted: number;
  
  // Settings
  isActive: boolean;
  settings: TeamSettings;
}

// Team Settings interface
export interface TeamSettings {
  // Access Control
  allowSelfRegistration: boolean;
  requireManagerApproval: boolean;
  
  // Collaboration
  sharedCandidates: boolean;
  sharedJobs: boolean;
  sharedTemplates: boolean;
  
  // Notifications
  teamNotifications: boolean;
  mentionNotifications: boolean;
  
  // Workflow
  defaultCandidateOwner: 'creator' | 'manager' | 'team';
  autoAssignInterviewers: boolean;
}

// Department interface
export interface Department extends BaseEntity {
  // Basic Information
  name: string;
  description?: string;
  code?: string;
  
  // Hierarchy
  parentId?: string;
  parentName?: string;
  level: number;
  
  // Management
  headId?: string;
  headName?: string;
  
  // Statistics
  userCount: number;
  teamCount: number;
  
  // Settings
  isActive: boolean;
  budget?: number;
  location?: string;
}

// User Activity interface
export interface UserActivity extends BaseEntity {
  // Activity Information
  userId: string;
  userName: string;
  action: string;
  description: string;
  
  // Context
  entityType?: 'candidate' | 'job' | 'interview' | 'user' | 'team';
  entityId?: string;
  entityName?: string;
  
  // Metadata
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  
  // Timestamp
  timestamp: string;
}

// User Session interface
export interface UserSession extends BaseEntity {
  // Session Information
  userId: string;
  sessionToken: string;
  
  // Device Information
  ipAddress: string;
  userAgent: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  browser?: string;
  os?: string;
  
  // Location
  location?: string;
  country?: string;
  city?: string;
  
  // Status
  isActive: boolean;
  lastActivityAt: string;
  expiresAt: string;
}

// Create user data
export interface CreateUserData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  department?: string;
  location?: string;
  role: UserRole;
  teamId?: string;
  managerId?: string;
  permissions?: Permission[];
  preferences?: Partial<UserPreferences>;
  notes?: string;
  tags?: string[];
}

// Update user data
export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  title?: string;
  department?: string;
  location?: string;
  role?: UserRole;
  teamId?: string;
  managerId?: string;
  permissions?: Permission[];
  status?: UserStatus;
  preferences?: Partial<UserPreferences>;
  notes?: string;
  tags?: string[];
}

// User filters
export interface UserFilters {
  role?: UserRole[];
  status?: UserStatus[];
  department?: string[];
  team?: string[];
  location?: string[];
  managerId?: string;
  search?: string;
  tags?: string[];
  dateRange?: {
    from: string;
    to: string;
  };
}

// User statistics
export interface UserStatistics {
  total: number;
  active: number;
  inactive: number;
  pending: number;
  
  byRole: Record<UserRole, number>;
  byDepartment: Record<string, number>;
  byStatus: Record<UserStatus, number>;
  
  recentLogins: number;
  newUsers: number;
  
  topPerformers: Array<{
    userId: string;
    userName: string;
    candidatesManaged: number;
    interviewsConducted: number;
    jobsPosted: number;
  }>;
}

// Role definition
export interface RoleDefinition {
  role: UserRole;
  name: string;
  description: string;
  permissions: Permission[];
  isCustom?: boolean;
  level: number; // hierarchy level
}

// Permission group
export interface PermissionGroup {
  name: string;
  description: string;
  permissions: Permission[];
}
