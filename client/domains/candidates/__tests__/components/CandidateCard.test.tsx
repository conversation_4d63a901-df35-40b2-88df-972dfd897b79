/**
 * CandidateCard Component Tests
 * Tests for the candidate card component
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { CandidateCard } from '../../components/CandidateCard';
import { mockCandidates, createMockCandidate } from '../../../__tests__/mocks/mockData';

// Mock the UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardTitle: ({ children }: any) => <h3>{children}</h3>,
  CardDescription: ({ children }: any) => <p>{children}</p>,
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant }: any) => <span data-variant={variant}>{children}</span>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variant, size }: any) => (
    <button onClick={onClick} data-variant={variant} data-size={size}>
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/avatar', () => ({
  Avatar: ({ children }: any) => <div data-testid="avatar">{children}</div>,
  AvatarImage: ({ src, alt }: any) => <img src={src} alt={alt} />,
  AvatarFallback: ({ children }: any) => <span data-testid="avatar-fallback">{children}</span>,
}));

describe('CandidateCard', () => {
  const mockCandidate = mockCandidates[0];
  const defaultProps = {
    candidate: mockCandidate,
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onStatusChange: vi.fn(),
    onViewDetails: vi.fn(),
    showActions: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders candidate information correctly', () => {
    render(<CandidateCard {...defaultProps} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Frontend Developer')).toBeInTheDocument();
    expect(screen.getByText('+1-555-0123')).toBeInTheDocument();
  });

  it('displays candidate skills', () => {
    render(<CandidateCard {...defaultProps} />);
    
    expect(screen.getByText('React')).toBeInTheDocument();
    expect(screen.getByText('TypeScript')).toBeInTheDocument();
    expect(screen.getByText('JavaScript')).toBeInTheDocument();
  });

  it('displays candidate status badge', () => {
    render(<CandidateCard {...defaultProps} />);
    
    const statusBadge = screen.getByText('interview');
    expect(statusBadge).toBeInTheDocument();
  });

  it('displays candidate experience', () => {
    render(<CandidateCard {...defaultProps} />);
    
    expect(screen.getByText('3 years')).toBeInTheDocument();
  });

  it('displays candidate location', () => {
    render(<CandidateCard {...defaultProps} />);
    
    expect(screen.getByText('San Francisco, CA')).toBeInTheDocument();
  });

  it('displays expected salary range', () => {
    render(<CandidateCard {...defaultProps} />);
    
    expect(screen.getByText('$80,000 - $100,000')).toBeInTheDocument();
  });

  it('shows action buttons when showActions is true', () => {
    render(<CandidateCard {...defaultProps} showActions={true} />);
    
    expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /delete/i })).toBeInTheDocument();
  });

  it('hides action buttons when showActions is false', () => {
    render(<CandidateCard {...defaultProps} showActions={false} />);
    
    expect(screen.queryByRole('button', { name: /edit/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /delete/i })).not.toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', () => {
    const onEdit = vi.fn();
    render(<CandidateCard {...defaultProps} onEdit={onEdit} />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);
    
    expect(onEdit).toHaveBeenCalledWith(mockCandidate);
  });

  it('calls onDelete when delete button is clicked', () => {
    const onDelete = vi.fn();
    render(<CandidateCard {...defaultProps} onDelete={onDelete} />);
    
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(deleteButton);
    
    expect(onDelete).toHaveBeenCalledWith(mockCandidate.id);
  });

  it('calls onViewDetails when card is clicked', () => {
    const onViewDetails = vi.fn();
    render(<CandidateCard {...defaultProps} onViewDetails={onViewDetails} />);
    
    // Click on the card (not on action buttons)
    const cardTitle = screen.getByText('John Doe');
    fireEvent.click(cardTitle);
    
    expect(onViewDetails).toHaveBeenCalledWith(mockCandidate);
  });

  it('displays avatar with initials fallback', () => {
    render(<CandidateCard {...defaultProps} />);
    
    expect(screen.getByTestId('avatar')).toBeInTheDocument();
    expect(screen.getByTestId('avatar-fallback')).toBeInTheDocument();
    expect(screen.getByText('JD')).toBeInTheDocument(); // John Doe initials
  });

  it('handles different candidate statuses', () => {
    const appliedCandidate = createMockCandidate({ status: 'applied' });
    render(<CandidateCard {...defaultProps} candidate={appliedCandidate} />);
    
    expect(screen.getByText('applied')).toBeInTheDocument();
  });

  it('handles different candidate sources', () => {
    const referralCandidate = createMockCandidate({ source: 'referral' });
    render(<CandidateCard {...defaultProps} candidate={referralCandidate} />);
    
    expect(screen.getByText('referral')).toBeInTheDocument();
  });

  it('displays portfolio link when available', () => {
    render(<CandidateCard {...defaultProps} />);
    
    const portfolioLink = screen.getByRole('link', { name: /portfolio/i });
    expect(portfolioLink).toBeInTheDocument();
    expect(portfolioLink).toHaveAttribute('href', 'https://johndoe.dev');
  });

  it('displays LinkedIn link when available', () => {
    render(<CandidateCard {...defaultProps} />);
    
    const linkedinLink = screen.getByRole('link', { name: /linkedin/i });
    expect(linkedinLink).toBeInTheDocument();
    expect(linkedinLink).toHaveAttribute('href', 'https://linkedin.com/in/johndoe');
  });

  it('displays GitHub link when available', () => {
    render(<CandidateCard {...defaultProps} />);
    
    const githubLink = screen.getByRole('link', { name: /github/i });
    expect(githubLink).toBeInTheDocument();
    expect(githubLink).toHaveAttribute('href', 'https://github.com/johndoe');
  });

  it('displays resume link when available', () => {
    render(<CandidateCard {...defaultProps} />);
    
    const resumeLink = screen.getByRole('link', { name: /resume/i });
    expect(resumeLink).toBeInTheDocument();
    expect(resumeLink).toHaveAttribute('href', 'https://example.com/resumes/john-doe.pdf');
  });

  it('handles missing optional fields gracefully', () => {
    const minimalCandidate = createMockCandidate({
      phone: undefined,
      title: undefined,
      location: undefined,
      portfolioUrl: undefined,
      linkedinUrl: undefined,
      githubUrl: undefined,
      resumeUrl: undefined,
      expectedSalary: undefined,
    });
    
    render(<CandidateCard {...defaultProps} candidate={minimalCandidate} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('displays candidate tags', () => {
    render(<CandidateCard {...defaultProps} />);
    
    expect(screen.getByText('frontend')).toBeInTheDocument();
    expect(screen.getByText('react')).toBeInTheDocument();
    expect(screen.getByText('typescript')).toBeInTheDocument();
  });

  it('displays availability information', () => {
    render(<CandidateCard {...defaultProps} />);
    
    expect(screen.getByText('immediate')).toBeInTheDocument();
  });

  it('displays application date', () => {
    render(<CandidateCard {...defaultProps} />);
    
    // This would depend on the actual date formatting implementation
    expect(screen.getByText(/applied/i)).toBeInTheDocument();
  });

  it('applies correct styling classes', () => {
    const { container } = render(<CandidateCard {...defaultProps} />);
    
    // Check that the card has appropriate classes
    const card = container.firstChild;
    expect(card).toHaveClass('candidate-card');
  });

  it('handles status change', () => {
    const onStatusChange = vi.fn();
    render(<CandidateCard {...defaultProps} onStatusChange={onStatusChange} />);
    
    // This would depend on the actual implementation of status change
    // For now, we'll test that the handler is available
    expect(onStatusChange).toBeDefined();
  });

  it('displays notes when available', () => {
    render(<CandidateCard {...defaultProps} />);
    
    expect(screen.getByText('Strong React skills, good portfolio')).toBeInTheDocument();
  });

  it('handles long skill lists appropriately', () => {
    const candidateWithManySkills = createMockCandidate({
      skills: ['React', 'TypeScript', 'JavaScript', 'CSS', 'HTML', 'Node.js', 'Python', 'Java', 'C++', 'Go'],
    });
    
    render(<CandidateCard {...defaultProps} candidate={candidateWithManySkills} />);
    
    // Should display skills (implementation may limit the number shown)
    expect(screen.getByText('React')).toBeInTheDocument();
    expect(screen.getByText('TypeScript')).toBeInTheDocument();
  });
});
