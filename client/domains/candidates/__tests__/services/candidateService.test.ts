/**
 * Candidate Service Tests
 * Unit tests for candidate service layer
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { candidateService } from '../../services/candidateService';
import { Candidate, CandidateQueryParams } from '../../types';

// Mock the base API client
vi.mock('@/core/api/ApiClient', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock data
const mockCandidate: Candidate = {
  id: '1',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '******-0123',
  title: 'Frontend Developer',
  experience: 3,
  skills: ['React', 'TypeScript', 'JavaScript'],
  status: 'interview',
  source: 'linkedin',
  appliedAt: '2024-01-05T00:00:00Z',
  resumeUrl: 'https://example.com/resumes/john-doe.pdf',
  location: 'San Francisco, CA',
  expectedSalary: {
    min: 80000,
    max: 100000,
    currency: 'USD',
  },
  availability: 'immediate',
  notes: 'Strong React skills',
  tags: ['frontend', 'react'],
  createdAt: '2024-01-05T00:00:00Z',
  updatedAt: '2024-01-10T00:00:00Z',
};

const mockCandidates = [mockCandidate];

describe('CandidateService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getCandidates', () => {
    it('fetches candidates successfully', async () => {
      const mockResponse = {
        data: mockCandidates,
        success: true,
        pagination: {
          page: 1,
          limit: 25,
          total: 1,
          totalPages: 1,
        },
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const params: CandidateQueryParams = {
        page: 1,
        limit: 25,
        search: 'John',
      };

      const result = await candidateService.getCandidates(params);

      expect(apiClient.get).toHaveBeenCalledWith('/candidates', { params });
      expect(result).toEqual(mockResponse);
    });

    it('handles API errors gracefully', async () => {
      const mockError = new Error('API Error');
      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.get).mockRejectedValue(mockError);

      await expect(candidateService.getCandidates({})).rejects.toThrow('API Error');
    });

    it('applies filters correctly', async () => {
      const mockResponse = { data: mockCandidates, success: true };
      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const params: CandidateQueryParams = {
        status: ['interview', 'offer'],
        skills: ['React', 'TypeScript'],
        experience: { min: 2, max: 5 },
        location: 'San Francisco',
      };

      await candidateService.getCandidates(params);

      expect(apiClient.get).toHaveBeenCalledWith('/candidates', { params });
    });
  });

  describe('getCandidateById', () => {
    it('fetches candidate by ID successfully', async () => {
      const mockResponse = { data: mockCandidate, success: true };
      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await candidateService.getCandidateById('1');

      expect(apiClient.get).toHaveBeenCalledWith('/candidates/1');
      expect(result).toEqual(mockResponse);
    });

    it('handles not found error', async () => {
      const mockError = new Error('Candidate not found');
      (mockError as any).response = { status: 404 };
      
      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.get).mockRejectedValue(mockError);

      await expect(candidateService.getCandidateById('999')).rejects.toThrow('Candidate not found');
    });
  });

  describe('createCandidate', () => {
    it('creates candidate successfully', async () => {
      const newCandidate = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '******-0124',
        title: 'Backend Developer',
      };

      const mockResponse = {
        data: { ...mockCandidate, ...newCandidate, id: '2' },
        success: true,
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const result = await candidateService.createCandidate(newCandidate);

      expect(apiClient.post).toHaveBeenCalledWith('/candidates', newCandidate);
      expect(result).toEqual(mockResponse);
    });

    it('handles validation errors', async () => {
      const invalidCandidate = {
        firstName: '',
        email: 'invalid-email',
      };

      const mockError = new Error('Validation failed');
      (mockError as any).response = {
        status: 400,
        data: {
          success: false,
          errors: ['First name is required', 'Invalid email format'],
        },
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.post).mockRejectedValue(mockError);

      await expect(candidateService.createCandidate(invalidCandidate)).rejects.toThrow('Validation failed');
    });
  });

  describe('updateCandidate', () => {
    it('updates candidate successfully', async () => {
      const updates = {
        title: 'Senior Frontend Developer',
        experience: 5,
        skills: ['React', 'TypeScript', 'Node.js'],
      };

      const mockResponse = {
        data: { ...mockCandidate, ...updates },
        success: true,
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.put).mockResolvedValue(mockResponse);

      const result = await candidateService.updateCandidate('1', updates);

      expect(apiClient.put).toHaveBeenCalledWith('/candidates/1', updates);
      expect(result).toEqual(mockResponse);
    });

    it('handles concurrent update conflicts', async () => {
      const mockError = new Error('Conflict');
      (mockError as any).response = {
        status: 409,
        data: {
          success: false,
          message: 'Candidate was updated by another user',
        },
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.put).mockRejectedValue(mockError);

      await expect(candidateService.updateCandidate('1', {})).rejects.toThrow('Conflict');
    });
  });

  describe('deleteCandidate', () => {
    it('deletes candidate successfully', async () => {
      const mockResponse = { success: true };
      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.delete).mockResolvedValue(mockResponse);

      const result = await candidateService.deleteCandidate('1');

      expect(apiClient.delete).toHaveBeenCalledWith('/candidates/1');
      expect(result).toEqual(mockResponse);
    });

    it('handles delete constraints', async () => {
      const mockError = new Error('Cannot delete candidate with active interviews');
      (mockError as any).response = {
        status: 409,
        data: {
          success: false,
          message: 'Cannot delete candidate with active interviews',
        },
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.delete).mockRejectedValue(mockError);

      await expect(candidateService.deleteCandidate('1')).rejects.toThrow('Cannot delete candidate with active interviews');
    });
  });

  describe('updateCandidateStatus', () => {
    it('updates candidate status successfully', async () => {
      const mockResponse = {
        data: { ...mockCandidate, status: 'offer' },
        success: true,
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.put).mockResolvedValue(mockResponse);

      const result = await candidateService.updateCandidateStatus('1', 'offer');

      expect(apiClient.put).toHaveBeenCalledWith('/candidates/1/status', { status: 'offer' });
      expect(result).toEqual(mockResponse);
    });

    it('validates status transitions', async () => {
      const mockError = new Error('Invalid status transition');
      (mockError as any).response = {
        status: 400,
        data: {
          success: false,
          message: 'Cannot transition from interview to rejected without feedback',
        },
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.put).mockRejectedValue(mockError);

      await expect(candidateService.updateCandidateStatus('1', 'rejected')).rejects.toThrow('Invalid status transition');
    });
  });

  describe('searchCandidates', () => {
    it('searches candidates by query', async () => {
      const mockResponse = {
        data: mockCandidates,
        success: true,
        pagination: {
          page: 1,
          limit: 25,
          total: 1,
          totalPages: 1,
        },
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await candidateService.searchCandidates('React developer');

      expect(apiClient.get).toHaveBeenCalledWith('/candidates/search', {
        params: { q: 'React developer' },
      });
      expect(result).toEqual(mockResponse);
    });

    it('handles empty search results', async () => {
      const mockResponse = {
        data: [],
        success: true,
        pagination: {
          page: 1,
          limit: 25,
          total: 0,
          totalPages: 0,
        },
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await candidateService.searchCandidates('nonexistent skill');

      expect(result.data).toEqual([]);
      expect(result.pagination.total).toBe(0);
    });
  });

  describe('bulkUpdateCandidates', () => {
    it('updates multiple candidates successfully', async () => {
      const candidateIds = ['1', '2', '3'];
      const updates = { status: 'reviewed' as const };

      const mockResponse = {
        data: {
          updated: candidateIds,
          failed: [],
        },
        success: true,
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.put).mockResolvedValue(mockResponse);

      const result = await candidateService.bulkUpdateCandidates(candidateIds, updates);

      expect(apiClient.put).toHaveBeenCalledWith('/candidates/bulk', {
        candidateIds,
        updates,
      });
      expect(result).toEqual(mockResponse);
    });

    it('handles partial failures in bulk update', async () => {
      const candidateIds = ['1', '2', '3'];
      const updates = { status: 'reviewed' as const };

      const mockResponse = {
        data: {
          updated: ['1', '3'],
          failed: [{ id: '2', error: 'Candidate not found' }],
        },
        success: true,
      };

      const { apiClient } = await import('@/core/api/ApiClient');
      vi.mocked(apiClient.put).mockResolvedValue(mockResponse);

      const result = await candidateService.bulkUpdateCandidates(candidateIds, updates);

      expect(result.data.updated).toHaveLength(2);
      expect(result.data.failed).toHaveLength(1);
    });
  });
});
