/**
 * Candidates Hook
 * Main hook for managing candidates data and operations
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CandidateApiService, CandidateBusinessService } from '../services';
import { 
  Candidate, 
  CandidateFilters, 
  CandidateStatus,
  CandidateListItem,
  CandidateStatistics 
} from '../types';
import { apiClient } from '@/core/api/services';
import { notifications } from '@/shared/components/feedback';

// Query keys
export const candidateKeys = {
  all: ['candidates'] as const,
  lists: () => [...candidateKeys.all, 'list'] as const,
  list: (filters: CandidateFilters) => [...candidateKeys.lists(), filters] as const,
  details: () => [...candidateKeys.all, 'detail'] as const,
  detail: (id: string) => [...candidateKeys.details(), id] as const,
  stats: () => [...candidateKeys.all, 'stats'] as const,
  search: (query: string) => [...candidateKeys.all, 'search', query] as const,
};

// Services
const candidateApiService = new CandidateApiService(apiClient);
const candidateBusinessService = new CandidateBusinessService(candidateApiService);

// Main candidates hook
export function useCandidates(filters?: CandidateFilters, options?: {
  page?: number;
  limit?: number;
  enabled?: boolean;
}) {
  const { page = 1, limit = 10, enabled = true } = options || {};

  const query = useQuery({
    queryKey: candidateKeys.list({ ...filters, page, limit }),
    queryFn: () => candidateBusinessService.getCandidates(filters, page, limit),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    candidates: query.data?.data || [],
    meta: query.data?.meta,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// Single candidate hook
export function useCandidate(id: string, options?: { enabled?: boolean }) {
  const { enabled = true } = options || {};

  const query = useQuery({
    queryKey: candidateKeys.detail(id),
    queryFn: () => candidateBusinessService.getCandidateById(id),
    enabled: enabled && !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  return {
    candidate: query.data?.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// Candidate statistics hook
export function useCandidateStats(filters?: CandidateFilters) {
  const query = useQuery({
    queryKey: candidateKeys.stats(),
    queryFn: () => candidateBusinessService.getCandidateMetrics(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    stats: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// Candidate mutations hook
export function useCandidateMutations() {
  const queryClient = useQueryClient();

  // Create candidate mutation
  const createMutation = useMutation({
    mutationFn: candidateBusinessService.createCandidate.bind(candidateBusinessService),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.lists() });
      queryClient.invalidateQueries({ queryKey: candidateKeys.stats() });
    },
  });

  // Update candidate mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      candidateBusinessService.updateCandidate(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: candidateKeys.lists() });
      queryClient.invalidateQueries({ queryKey: candidateKeys.stats() });
    },
  });

  // Delete candidate mutation
  const deleteMutation = useMutation({
    mutationFn: ({ id, name }: { id: string; name?: string }) =>
      candidateBusinessService.deleteCandidate(id, name),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.lists() });
      queryClient.invalidateQueries({ queryKey: candidateKeys.stats() });
    },
  });

  // Update status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status, notes, name }: { 
      id: string; 
      status: CandidateStatus; 
      notes?: string;
      name?: string;
    }) => candidateBusinessService.updateCandidateStatus(id, status, notes, name),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: candidateKeys.lists() });
      queryClient.invalidateQueries({ queryKey: candidateKeys.stats() });
    },
  });

  // Bulk update status mutation
  const bulkUpdateStatusMutation = useMutation({
    mutationFn: ({ candidateIds, status, notes }: {
      candidateIds: string[];
      status: CandidateStatus;
      notes?: string;
    }) => candidateBusinessService.bulkUpdateStatus(candidateIds, status, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.lists() });
      queryClient.invalidateQueries({ queryKey: candidateKeys.stats() });
    },
  });

  // Resume upload mutation
  const uploadResumeMutation = useMutation({
    mutationFn: ({ id, file }: { id: string; file: File }) =>
      candidateBusinessService.processResumeUpload(id, file),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.detail(id) });
    },
  });

  return {
    // Mutations
    createCandidate: createMutation.mutate,
    updateCandidate: updateMutation.mutate,
    deleteCandidate: deleteMutation.mutate,
    updateStatus: updateStatusMutation.mutate,
    bulkUpdateStatus: bulkUpdateStatusMutation.mutate,
    uploadResume: uploadResumeMutation.mutate,

    // Async mutations
    createCandidateAsync: createMutation.mutateAsync,
    updateCandidateAsync: updateMutation.mutateAsync,
    deleteCandidateAsync: deleteMutation.mutateAsync,
    updateStatusAsync: updateStatusMutation.mutateAsync,
    bulkUpdateStatusAsync: bulkUpdateStatusMutation.mutateAsync,
    uploadResumeAsync: uploadResumeMutation.mutateAsync,

    // Loading states
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isUpdatingStatus: updateStatusMutation.isPending,
    isBulkUpdating: bulkUpdateStatusMutation.isPending,
    isUploading: uploadResumeMutation.isPending,

    // Error states
    createError: createMutation.error,
    updateError: updateMutation.error,
    deleteError: deleteMutation.error,
    statusError: updateStatusMutation.error,
    bulkError: bulkUpdateStatusMutation.error,
    uploadError: uploadResumeMutation.error,
  };
}

// Candidate search hook
export function useCandidateSearch(initialQuery?: string) {
  const [query, setQuery] = useState(initialQuery || '');
  const [debouncedQuery, setDebouncedQuery] = useState(query);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  const searchQuery = useQuery({
    queryKey: candidateKeys.search(debouncedQuery),
    queryFn: () => candidateBusinessService.getCandidates({ search: debouncedQuery }),
    enabled: debouncedQuery.length > 2,
    staleTime: 30 * 1000, // 30 seconds
  });

  return {
    query,
    setQuery,
    results: searchQuery.data?.data || [],
    isSearching: searchQuery.isLoading,
    searchError: searchQuery.error,
  };
}

// Candidate filters hook
export function useCandidateFilters(initialFilters?: CandidateFilters) {
  const [filters, setFilters] = useState<CandidateFilters>(initialFilters || {});

  const updateFilter = useCallback((key: keyof CandidateFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(value => 
      value !== undefined && 
      value !== null && 
      (Array.isArray(value) ? value.length > 0 : true)
    );
  }, [filters]);

  return {
    filters,
    setFilters,
    updateFilter,
    clearFilters,
    hasActiveFilters,
  };
}

// Candidate selection hook
export function useCandidateSelection() {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const toggleSelection = useCallback((id: string) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
    );
  }, []);

  const selectAll = useCallback((ids: string[]) => {
    setSelectedIds(ids);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedIds([]);
  }, []);

  const isSelected = useCallback((id: string) => {
    return selectedIds.includes(id);
  }, [selectedIds]);

  return {
    selectedIds,
    selectedCount: selectedIds.length,
    toggleSelection,
    selectAll,
    clearSelection,
    isSelected,
    hasSelection: selectedIds.length > 0,
  };
}

// Candidate table hook (combines multiple hooks)
export function useCandidateTable(initialFilters?: CandidateFilters) {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  
  const filtersHook = useCandidateFilters(initialFilters);
  const selectionHook = useCandidateSelection();
  const candidatesHook = useCandidates(filtersHook.filters, { page, limit });
  const mutationsHook = useCandidateMutations();

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
    selectionHook.clearSelection();
  }, [selectionHook]);

  const handleLimitChange = useCallback((newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
    selectionHook.clearSelection();
  }, [selectionHook]);

  return {
    // Data
    candidates: candidatesHook.candidates,
    meta: candidatesHook.meta,
    
    // Loading states
    isLoading: candidatesHook.isLoading,
    isError: candidatesHook.isError,
    error: candidatesHook.error,
    
    // Pagination
    page,
    limit,
    setPage: handlePageChange,
    setLimit: handleLimitChange,
    
    // Filters
    ...filtersHook,
    
    // Selection
    ...selectionHook,
    
    // Mutations
    ...mutationsHook,
    
    // Actions
    refetch: candidatesHook.refetch,
  };
}
