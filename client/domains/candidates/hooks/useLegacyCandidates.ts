/**
 * Legacy Candidates Hooks
 * Backward compatibility layer for existing components
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/lib/api";
import { useNotifications } from "@/hooks/use-notifications";

// Legacy query keys (matching original structure)
export const legacyQueryKeys = {
  candidates: (params?: any) => ["candidates", params],
  candidate: (id: string, include?: string) => ["candidate", id, include],
};

// Legacy candidates hook
export const useLegacyCandidates = (params?: {
  page?: number;
  per_page?: number;
  filter?: Record<string, any>;
  sort?: string;
  include?: string;
}) => {
  return useQuery({
    queryKey: legacyQueryKeys.candidates(params),
    queryFn: () => apiService.getCandidates(params || {}),
    enabled: !!localStorage.getItem("auth_token"),
  });
};

// Legacy single candidate hook
export const useLegacyCandidate = (id: string, include?: string) => {
  return useQuery({
    queryKey: legacyQueryKeys.candidate(id, include),
    queryFn: () => apiService.getCandidate(id, include),
    enabled: !!id && !!localStorage.getItem("auth_token"),
  });
};

// Legacy create candidate hook
export const useCreateCandidate = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (candidateData: any) =>
      apiService.createCandidate(candidateData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      notifications.success.created();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
      console.error("Create candidate error:", error);
    },
  });
};

// Legacy update candidate hook
export const useUpdateCandidate = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      apiService.updateCandidate(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      queryClient.invalidateQueries({ queryKey: ["candidate", variables.id] });
      notifications.success.updated();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

// Legacy update candidate status hook
export const useUpdateCandidateStatus = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({
      id,
      status,
      notes,
    }: {
      id: string;
      status: string;
      notes?: string;
    }) => apiService.updateCandidateStatus(id, status, notes),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      queryClient.invalidateQueries({ queryKey: ["candidate", variables.id] });
      notifications.success.updated();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

// Legacy delete candidate hook
export const useDeleteCandidate = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (id: string) => apiService.deleteCandidate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      notifications.success.deleted();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

// Backward compatibility exports
export const useCandidates = useLegacyCandidates;
export const useCandidate = useLegacyCandidate;
