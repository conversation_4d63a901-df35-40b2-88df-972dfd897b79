/**
 * Candidate Actions Hook
 * Provides common candidate actions and operations
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CandidateApiService, CandidateBusinessService } from '../services';
import { 
  Candidate, 
  CandidateStatus,
  BulkCandidateOperation,
  CandidateNote,
  CandidateDocument 
} from '../types';
import { apiClient } from '@/core/api/services';
import { candidateKeys } from './useCandidates';
import { notifications } from '@/shared/components/feedback';
import { toast } from 'sonner';

// Services
const candidateApiService = new CandidateApiService(apiClient);
const candidateBusinessService = new CandidateBusinessService(candidateApiService);

// Candidate actions hook
export function useCandidateActions() {
  const queryClient = useQueryClient();

  // Update candidate status
  const updateStatus = useMutation({
    mutationFn: async ({ candidateId, status, reason }: {
      candidateId: string;
      status: CandidateStatus;
      reason?: string;
    }) => {
      return candidateBusinessService.updateCandidateStatus(candidateId, status, reason);
    },
    onSuccess: (candidate, variables) => {
      // Invalidate and update cache
      queryClient.invalidateQueries({ queryKey: candidateKeys.all });
      queryClient.setQueryData(
        candidateKeys.detail(variables.candidateId),
        candidate
      );
      
      toast.success(`Candidate status updated to ${variables.status}`);
    },
    onError: (error) => {
      console.error('Failed to update candidate status:', error);
      toast.error('Failed to update candidate status');
    },
  });

  // Add note to candidate
  const addNote = useMutation({
    mutationFn: async ({ candidateId, note }: {
      candidateId: string;
      note: Omit<CandidateNote, 'id' | 'createdAt' | 'updatedAt'>;
    }) => {
      return candidateBusinessService.addCandidateNote(candidateId, note);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: candidateKeys.detail(variables.candidateId) 
      });
      toast.success('Note added successfully');
    },
    onError: (error) => {
      console.error('Failed to add note:', error);
      toast.error('Failed to add note');
    },
  });

  // Update candidate rating
  const updateRating = useMutation({
    mutationFn: async ({ candidateId, rating }: {
      candidateId: string;
      rating: number;
    }) => {
      return candidateBusinessService.updateCandidateRating(candidateId, rating);
    },
    onSuccess: (candidate, variables) => {
      queryClient.setQueryData(
        candidateKeys.detail(variables.candidateId),
        candidate
      );
      toast.success('Rating updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update rating:', error);
      toast.error('Failed to update rating');
    },
  });

  // Add tags to candidate
  const addTags = useMutation({
    mutationFn: async ({ candidateId, tags }: {
      candidateId: string;
      tags: string[];
    }) => {
      return candidateBusinessService.addCandidateTags(candidateId, tags);
    },
    onSuccess: (candidate, variables) => {
      queryClient.setQueryData(
        candidateKeys.detail(variables.candidateId),
        candidate
      );
      toast.success('Tags added successfully');
    },
    onError: (error) => {
      console.error('Failed to add tags:', error);
      toast.error('Failed to add tags');
    },
  });

  // Remove tags from candidate
  const removeTags = useMutation({
    mutationFn: async ({ candidateId, tags }: {
      candidateId: string;
      tags: string[];
    }) => {
      return candidateBusinessService.removeCandidateTags(candidateId, tags);
    },
    onSuccess: (candidate, variables) => {
      queryClient.setQueryData(
        candidateKeys.detail(variables.candidateId),
        candidate
      );
      toast.success('Tags removed successfully');
    },
    onError: (error) => {
      console.error('Failed to remove tags:', error);
      toast.error('Failed to remove tags');
    },
  });

  // Upload document
  const uploadDocument = useMutation({
    mutationFn: async ({ candidateId, file, type }: {
      candidateId: string;
      file: File;
      type: CandidateDocument['type'];
    }) => {
      return candidateBusinessService.uploadCandidateDocument(candidateId, file, type);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: candidateKeys.detail(variables.candidateId) 
      });
      toast.success('Document uploaded successfully');
    },
    onError: (error) => {
      console.error('Failed to upload document:', error);
      toast.error('Failed to upload document');
    },
  });

  // Bulk operations
  const bulkOperation = useMutation({
    mutationFn: async (operation: BulkCandidateOperation) => {
      return candidateBusinessService.performBulkOperation(operation);
    },
    onSuccess: (result, operation) => {
      // Invalidate all candidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: candidateKeys.all });
      
      const successMessage = {
        update_status: `Updated status for ${result.successCount} candidates`,
        add_tags: `Added tags to ${result.successCount} candidates`,
        remove_tags: `Removed tags from ${result.successCount} candidates`,
        assign_to_job: `Assigned ${result.successCount} candidates to job`,
        delete: `Deleted ${result.successCount} candidates`,
        export: `Exported ${result.successCount} candidates`,
      }[operation.operation] || `Bulk operation completed for ${result.successCount} candidates`;

      toast.success(successMessage);

      if (result.failureCount > 0) {
        toast.warning(`${result.failureCount} operations failed`);
      }
    },
    onError: (error) => {
      console.error('Bulk operation failed:', error);
      toast.error('Bulk operation failed');
    },
  });

  // Archive candidate
  const archiveCandidate = useMutation({
    mutationFn: async (candidateId: string) => {
      return candidateBusinessService.archiveCandidate(candidateId);
    },
    onSuccess: (_, candidateId) => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.all });
      queryClient.removeQueries({ queryKey: candidateKeys.detail(candidateId) });
      toast.success('Candidate archived successfully');
    },
    onError: (error) => {
      console.error('Failed to archive candidate:', error);
      toast.error('Failed to archive candidate');
    },
  });

  // Restore candidate
  const restoreCandidate = useMutation({
    mutationFn: async (candidateId: string) => {
      return candidateBusinessService.restoreCandidate(candidateId);
    },
    onSuccess: (candidate) => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.all });
      queryClient.setQueryData(candidateKeys.detail(candidate.id), candidate);
      toast.success('Candidate restored successfully');
    },
    onError: (error) => {
      console.error('Failed to restore candidate:', error);
      toast.error('Failed to restore candidate');
    },
  });

  // Delete candidate permanently
  const deleteCandidate = useMutation({
    mutationFn: async (candidateId: string) => {
      return candidateBusinessService.deleteCandidate(candidateId);
    },
    onSuccess: (_, candidateId) => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.all });
      queryClient.removeQueries({ queryKey: candidateKeys.detail(candidateId) });
      toast.success('Candidate deleted permanently');
    },
    onError: (error) => {
      console.error('Failed to delete candidate:', error);
      toast.error('Failed to delete candidate');
    },
  });

  // Duplicate candidate
  const duplicateCandidate = useMutation({
    mutationFn: async (candidateId: string) => {
      return candidateBusinessService.duplicateCandidate(candidateId);
    },
    onSuccess: (newCandidate) => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.all });
      queryClient.setQueryData(candidateKeys.detail(newCandidate.id), newCandidate);
      toast.success('Candidate duplicated successfully');
    },
    onError: (error) => {
      console.error('Failed to duplicate candidate:', error);
      toast.error('Failed to duplicate candidate');
    },
  });

  return {
    // Status operations
    updateStatus,
    isUpdatingStatus: updateStatus.isPending,

    // Note operations
    addNote,
    isAddingNote: addNote.isPending,

    // Rating operations
    updateRating,
    isUpdatingRating: updateRating.isPending,

    // Tag operations
    addTags,
    removeTags,
    isManagingTags: addTags.isPending || removeTags.isPending,

    // Document operations
    uploadDocument,
    isUploadingDocument: uploadDocument.isPending,

    // Bulk operations
    bulkOperation,
    isBulkOperating: bulkOperation.isPending,

    // Archive/restore operations
    archiveCandidate,
    restoreCandidate,
    isArchiving: archiveCandidate.isPending,
    isRestoring: restoreCandidate.isPending,

    // Delete operations
    deleteCandidate,
    isDeleting: deleteCandidate.isPending,

    // Duplicate operations
    duplicateCandidate,
    isDuplicating: duplicateCandidate.isPending,

    // General loading state
    isLoading: updateStatus.isPending || 
               addNote.isPending || 
               updateRating.isPending || 
               addTags.isPending || 
               removeTags.isPending || 
               uploadDocument.isPending || 
               bulkOperation.isPending || 
               archiveCandidate.isPending || 
               restoreCandidate.isPending || 
               deleteCandidate.isPending || 
               duplicateCandidate.isPending,
  };
}
