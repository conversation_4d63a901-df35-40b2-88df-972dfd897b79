/**
 * Candidate Form Hook
 * Handles candidate form state, validation, and submission
 */

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { candidateSchemas, validateData } from '@/shared/utils/validation';
import { CandidateApiService, CandidateBusinessService } from '../services';
import { Candidate, CreateCandidateData, UpdateCandidateData } from '../types';
import { apiClient } from '@/core/api/services';
import { candidateKeys } from './useCandidates';
import { notifications } from '@/shared/components/feedback';

// Services
const candidateApiService = new CandidateApiService(apiClient);
const candidateBusinessService = new CandidateBusinessService(candidateApiService);

// Form data types
export type CandidateFormData = CreateCandidateData & UpdateCandidateData;

// Form options
export interface UseCandidateFormOptions {
  candidateId?: string;
  initialData?: Partial<CandidateFormData>;
  onSuccess?: (candidate: Candidate) => void;
  onError?: (error: any) => void;
  validationSchema?: 'basic' | 'professional' | 'full';
}

// Main candidate form hook
export function useCandidateForm(options: UseCandidateFormOptions = {}) {
  const {
    candidateId,
    initialData,
    onSuccess,
    onError,
    validationSchema = 'full',
  } = options;

  const queryClient = useQueryClient();
  const isEditing = !!candidateId;

  // Select validation schema
  const schema = {
    basic: candidateSchemas.basic,
    professional: candidateSchemas.professional,
    full: candidateSchemas.full,
  }[validationSchema];

  // Form setup
  const form = useForm<CandidateFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      position: '',
      experience: 0,
      expectedSalary: undefined,
      currentSalary: undefined,
      skills: [],
      location: '',
      notes: '',
      tags: [],
      status: 'new',
      ...initialData,
    },
    mode: 'onChange',
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data: CreateCandidateData) => 
      candidateBusinessService.createCandidate(data),
    onSuccess: (response) => {
      if (response.success && response.data) {
        queryClient.invalidateQueries({ queryKey: candidateKeys.lists() });
        queryClient.invalidateQueries({ queryKey: candidateKeys.stats() });
        onSuccess?.(response.data);
        form.reset();
      }
    },
    onError: (error) => {
      onError?.(error);
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: (data: UpdateCandidateData) => 
      candidateBusinessService.updateCandidate(candidateId!, data),
    onSuccess: (response) => {
      if (response.success && response.data) {
        queryClient.invalidateQueries({ queryKey: candidateKeys.detail(candidateId!) });
        queryClient.invalidateQueries({ queryKey: candidateKeys.lists() });
        onSuccess?.(response.data);
      }
    },
    onError: (error) => {
      onError?.(error);
    },
  });

  // Form submission handler
  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      if (isEditing) {
        await updateMutation.mutateAsync(data);
      } else {
        await createMutation.mutateAsync(data);
      }
    } catch (error) {
      console.error('Form submission error:', error);
    }
  });

  // Field validation helpers
  const validateField = (fieldName: keyof CandidateFormData, value: any) => {
    const fieldSchema = schema.shape[fieldName];
    if (fieldSchema) {
      const result = validateData(fieldSchema, value);
      return result.success ? null : result.errors?.[fieldName];
    }
    return null;
  };

  // Skills management
  const addSkill = (skill: string) => {
    const currentSkills = form.getValues('skills') || [];
    if (!currentSkills.includes(skill)) {
      form.setValue('skills', [...currentSkills, skill], { shouldValidate: true });
    }
  };

  const removeSkill = (skill: string) => {
    const currentSkills = form.getValues('skills') || [];
    form.setValue('skills', currentSkills.filter(s => s !== skill), { shouldValidate: true });
  };

  // Tags management
  const addTag = (tag: string) => {
    const currentTags = form.getValues('tags') || [];
    if (!currentTags.includes(tag)) {
      form.setValue('tags', [...currentTags, tag], { shouldValidate: true });
    }
  };

  const removeTag = (tag: string) => {
    const currentTags = form.getValues('tags') || [];
    form.setValue('tags', currentTags.filter(t => t !== tag), { shouldValidate: true });
  };

  // Auto-save functionality
  const enableAutoSave = (intervalMs: number = 30000) => {
    if (!isEditing) return;

    const interval = setInterval(() => {
      if (form.formState.isDirty && form.formState.isValid) {
        const data = form.getValues();
        updateMutation.mutate(data);
      }
    }, intervalMs);

    return () => clearInterval(interval);
  };

  // Reset form with new data
  const resetForm = (data?: Partial<CandidateFormData>) => {
    form.reset({
      name: '',
      email: '',
      phone: '',
      position: '',
      experience: 0,
      expectedSalary: undefined,
      currentSalary: undefined,
      skills: [],
      location: '',
      notes: '',
      tags: [],
      status: 'new',
      ...initialData,
      ...data,
    });
  };

  return {
    // Form instance
    form,
    
    // Form state
    formState: form.formState,
    isValid: form.formState.isValid,
    isDirty: form.formState.isDirty,
    errors: form.formState.errors,
    
    // Submission
    handleSubmit,
    onSubmit: handleSubmit,
    
    // Loading states
    isSubmitting: createMutation.isPending || updateMutation.isPending,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    
    // Error states
    submitError: createMutation.error || updateMutation.error,
    
    // Field helpers
    validateField,
    
    // Skills management
    addSkill,
    removeSkill,
    skills: form.watch('skills') || [],
    
    // Tags management
    addTag,
    removeTag,
    tags: form.watch('tags') || [],
    
    // Utilities
    resetForm,
    enableAutoSave,
    isEditing,
    
    // Form values (for debugging)
    values: form.watch(),
  };
}

// Candidate quick form hook (for modals/drawers)
export function useCandidateQuickForm(options: {
  onSuccess?: (candidate: Candidate) => void;
  onCancel?: () => void;
} = {}) {
  const { onSuccess, onCancel } = options;

  const formHook = useCandidateForm({
    validationSchema: 'basic',
    onSuccess: (candidate) => {
      notifications.success.created('Candidate');
      onSuccess?.(candidate);
    },
    onError: (error) => {
      notifications.error.generic('Failed to create candidate');
    },
  });

  const handleCancel = () => {
    formHook.resetForm();
    onCancel?.();
  };

  return {
    ...formHook,
    handleCancel,
  };
}

// Candidate import form hook
export function useCandidateImportForm() {
  const queryClient = useQueryClient();

  const importMutation = useMutation({
    mutationFn: (file: File) => candidateApiService.importCandidates(file),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries({ queryKey: candidateKeys.lists() });
        queryClient.invalidateQueries({ queryKey: candidateKeys.stats() });
        
        const { imported, errors } = response.data!;
        if (errors.length > 0) {
          notifications.warning.generic(
            `Imported ${imported} candidates with ${errors.length} errors`
          );
        } else {
          notifications.success.generic(`Successfully imported ${imported} candidates`);
        }
      }
    },
    onError: () => {
      notifications.error.generic('Failed to import candidates');
    },
  });

  const handleFileUpload = (file: File) => {
    // Validate file
    if (!file.name.endsWith('.csv')) {
      notifications.error.validation('Please upload a CSV file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB
      notifications.error.validation('File size must be less than 10MB');
      return;
    }

    importMutation.mutate(file);
  };

  return {
    handleFileUpload,
    isImporting: importMutation.isPending,
    importError: importMutation.error,
    importResult: importMutation.data?.data,
  };
}

// Candidate bulk edit form hook
export function useCandidateBulkEditForm(candidateIds: string[]) {
  const queryClient = useQueryClient();

  const form = useForm({
    defaultValues: {
      status: '',
      tags: [] as string[],
      notes: '',
    },
  });

  const bulkUpdateMutation = useMutation({
    mutationFn: (data: any) => 
      candidateBusinessService.bulkUpdateStatus(candidateIds, data.status, data.notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: candidateKeys.lists() });
      queryClient.invalidateQueries({ queryKey: candidateKeys.stats() });
      notifications.success.updated(`${candidateIds.length} candidates`);
    },
    onError: () => {
      notifications.error.generic('Failed to update candidates');
    },
  });

  const handleSubmit = form.handleSubmit((data) => {
    bulkUpdateMutation.mutate(data);
  });

  return {
    form,
    handleSubmit,
    isSubmitting: bulkUpdateMutation.isPending,
    error: bulkUpdateMutation.error,
    candidateCount: candidateIds.length,
  };
}
