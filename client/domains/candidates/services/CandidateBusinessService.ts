/**
 * Candidate Business Service
 * Handles business logic and data transformation for candidates
 */

import { Candidate, CreateCandidateData, UpdateCandidateData, CandidateStatus } from '../types';
import { CandidateApiService } from './CandidateApiService';
import { notifications } from '@/shared/components/feedback';

export interface CandidateFilters {
  status?: CandidateStatus[];
  skills?: string[];
  experienceRange?: [number, number];
  salaryRange?: [number, number];
  location?: string;
  search?: string;
}

export interface CandidateMetrics {
  totalCandidates: number;
  newThisWeek: number;
  inInterview: number;
  hired: number;
  conversionRate: number;
  averageTimeToHire: number;
}

export class CandidateBusinessService {
  constructor(private apiService: CandidateApiService) {}

  /**
   * Get candidates with business logic applied
   */
  async getCandidates(filters?: CandidateFilters, page: number = 1, limit: number = 10) {
    try {
      const params = this.transformFiltersToQueryParams(filters);
      const response = await this.apiService.getCandidates({
        ...params,
        page,
        limit,
      });

      return {
        ...response,
        data: response.data?.map(this.enrichCandidateData) || [],
      };
    } catch (error) {
      notifications.error.generic('Failed to load candidates');
      throw error;
    }
  }

  /**
   * Get candidate by ID with enriched data
   */
  async getCandidateById(id: string) {
    try {
      const response = await this.apiService.getCandidateById(id);
      
      if (response.success && response.data) {
        return {
          ...response,
          data: this.enrichCandidateData(response.data),
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.notFound('Candidate');
      throw error;
    }
  }

  /**
   * Create new candidate with validation
   */
  async createCandidate(data: CreateCandidateData) {
    try {
      // Apply business rules
      const processedData = this.preprocessCandidateData(data);
      
      const response = await this.apiService.createCandidate(processedData);
      
      if (response.success) {
        notifications.success.created('Candidate');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to create candidate');
      throw error;
    }
  }

  /**
   * Update candidate with validation
   */
  async updateCandidate(id: string, data: UpdateCandidateData) {
    try {
      const processedData = this.preprocessCandidateData(data);
      
      const response = await this.apiService.updateCandidate(id, processedData);
      
      if (response.success) {
        notifications.success.updated('Candidate');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to update candidate');
      throw error;
    }
  }

  /**
   * Delete candidate with confirmation
   */
  async deleteCandidate(id: string, candidateName?: string) {
    try {
      const response = await this.apiService.deleteCandidate(id);
      
      if (response.success) {
        notifications.success.deleted(candidateName || 'Candidate');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to delete candidate');
      throw error;
    }
  }

  /**
   * Update candidate status with business logic
   */
  async updateCandidateStatus(
    id: string, 
    status: CandidateStatus, 
    notes?: string,
    candidateName?: string
  ) {
    try {
      // Validate status transition
      this.validateStatusTransition(status);
      
      const response = await this.apiService.updateCandidateStatus(id, status, notes);
      
      if (response.success) {
        notifications.success.updated(`${candidateName || 'Candidate'} status`);
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to update candidate status');
      throw error;
    }
  }

  /**
   * Process resume upload and extraction
   */
  async processResumeUpload(id: string, file: File) {
    try {
      // Validate file
      this.validateResumeFile(file);
      
      // Upload resume
      const uploadResponse = await this.apiService.uploadResume(id, file);
      
      if (!uploadResponse.success) {
        throw new Error('Failed to upload resume');
      }
      
      // Extract data from resume
      const extractResponse = await this.apiService.extractResumeData(id);
      
      if (extractResponse.success) {
        notifications.success.uploaded('Resume');
        return {
          uploadUrl: uploadResponse.data?.url,
          extractedData: extractResponse.data,
        };
      }
      
      return { uploadUrl: uploadResponse.data?.url };
    } catch (error) {
      notifications.error.generic('Failed to process resume');
      throw error;
    }
  }

  /**
   * Get candidate metrics and analytics
   */
  async getCandidateMetrics(filters?: CandidateFilters): Promise<CandidateMetrics> {
    try {
      const params = this.transformFiltersToQueryParams(filters);
      const response = await this.apiService.getCandidateStats(params);
      
      if (response.success && response.data) {
        return this.calculateMetrics(response.data);
      }
      
      throw new Error('Failed to get candidate metrics');
    } catch (error) {
      notifications.error.generic('Failed to load candidate metrics');
      throw error;
    }
  }

  /**
   * Search candidates by skills with ranking
   */
  async searchCandidatesBySkills(skills: string[], limit: number = 20) {
    try {
      const response = await this.apiService.searchBySkills(skills, { limit });
      
      if (response.success && response.data) {
        // Rank candidates by skill match
        const rankedCandidates = this.rankCandidatesBySkills(response.data, skills);
        
        return {
          ...response,
          data: rankedCandidates,
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to search candidates');
      throw error;
    }
  }

  /**
   * Bulk operations with progress tracking
   */
  async bulkUpdateStatus(
    candidateIds: string[],
    status: CandidateStatus,
    notes?: string
  ) {
    try {
      if (candidateIds.length === 0) {
        throw new Error('No candidates selected');
      }
      
      // Show processing notification
      const processingId = notifications.info.processing(`${candidateIds.length} candidates`);
      
      const response = await this.apiService.bulkUpdateStatus(candidateIds, status, notes);
      
      // Dismiss processing notification
      notifications.dismiss(processingId);
      
      if (response.success) {
        notifications.success.updated(`${candidateIds.length} candidates`);
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to update candidates');
      throw error;
    }
  }

  // Private helper methods
  private enrichCandidateData(candidate: Candidate): Candidate {
    return {
      ...candidate,
      // Add computed fields
      fullName: candidate.name,
      initials: this.getInitials(candidate.name),
      experienceLevel: this.getExperienceLevel(candidate.experience || 0),
      statusColor: this.getStatusColor(candidate.status),
      // Add formatted fields
      formattedSalary: this.formatSalary(candidate.expectedSalary),
      formattedExperience: this.formatExperience(candidate.experience || 0),
    };
  }

  private preprocessCandidateData(data: CreateCandidateData | UpdateCandidateData) {
    return {
      ...data,
      // Normalize email
      email: data.email?.toLowerCase().trim(),
      // Normalize phone
      phone: data.phone?.replace(/\D/g, ''),
      // Process skills
      skills: data.skills?.map(skill => skill.trim()).filter(Boolean),
      // Set default status for new candidates
      ...('status' in data ? {} : { status: 'new' as CandidateStatus }),
    };
  }

  private validateStatusTransition(newStatus: CandidateStatus) {
    // Define valid status transitions
    const validTransitions: Record<CandidateStatus, CandidateStatus[]> = {
      new: ['screening', 'rejected'],
      screening: ['interview', 'rejected'],
      interview: ['offer', 'rejected'],
      offer: ['hired', 'rejected'],
      hired: [],
      rejected: ['new'], // Allow reactivation
    };

    // For now, allow all transitions (can be enhanced later)
    return true;
  }

  private validateResumeFile(file: File) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    
    if (file.size > maxSize) {
      throw new Error('Resume file size must be less than 10MB');
    }
    
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Resume must be a PDF or Word document');
    }
  }

  private transformFiltersToQueryParams(filters?: CandidateFilters) {
    if (!filters) return {};

    return {
      status: filters.status,
      skills: filters.skills,
      experience: filters.experienceRange ? {
        min: filters.experienceRange[0],
        max: filters.experienceRange[1],
      } : undefined,
      salaryRange: filters.salaryRange ? {
        min: filters.salaryRange[0],
        max: filters.salaryRange[1],
      } : undefined,
      location: filters.location,
      search: filters.search,
    };
  }

  private calculateMetrics(stats: any): CandidateMetrics {
    return {
      totalCandidates: stats.total || 0,
      newThisWeek: stats.recentApplications || 0,
      inInterview: stats.byStatus?.interview || 0,
      hired: stats.byStatus?.hired || 0,
      conversionRate: this.calculateConversionRate(stats.byStatus),
      averageTimeToHire: stats.averageTimeToHire || 0,
    };
  }

  private calculateConversionRate(statusCounts: Record<string, number>): number {
    const total = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);
    const hired = statusCounts.hired || 0;
    return total > 0 ? (hired / total) * 100 : 0;
  }

  private rankCandidatesBySkills(candidates: Candidate[], targetSkills: string[]): Candidate[] {
    return candidates
      .map(candidate => ({
        ...candidate,
        skillMatchScore: this.calculateSkillMatchScore(candidate.skills || [], targetSkills),
      }))
      .sort((a, b) => (b as any).skillMatchScore - (a as any).skillMatchScore);
  }

  private calculateSkillMatchScore(candidateSkills: string[], targetSkills: string[]): number {
    const matches = candidateSkills.filter(skill => 
      targetSkills.some(target => 
        skill.toLowerCase().includes(target.toLowerCase()) ||
        target.toLowerCase().includes(skill.toLowerCase())
      )
    );
    
    return matches.length / targetSkills.length;
  }

  private getInitials(name: string): string {
    return name
      .split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2);
  }

  private getExperienceLevel(years: number): string {
    if (years < 2) return 'Junior';
    if (years < 5) return 'Mid-level';
    if (years < 10) return 'Senior';
    return 'Expert';
  }

  private getStatusColor(status: CandidateStatus): string {
    const colors = {
      new: 'blue',
      screening: 'yellow',
      interview: 'purple',
      offer: 'green',
      hired: 'emerald',
      rejected: 'red',
    };
    
    return colors[status] || 'gray';
  }

  private formatSalary(salary?: number): string {
    if (!salary) return 'Not specified';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(salary);
  }

  private formatExperience(years: number): string {
    if (years === 0) return 'No experience';
    if (years === 1) return '1 year';
    return `${years} years`;
  }
}
