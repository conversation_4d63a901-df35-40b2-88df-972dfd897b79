import { apiService } from "@/lib/api";
import {
  AIAnalysisRequest,
  CandidateAnalysisResponse,
  CandidateAnalysisData,
} from "@/lib/types/candidateAnalysis";

export class CandidateAnalysisService {
  private static readonly BASE_URL = "/candidate-analysis";

  /**
   * Generate AI analysis for a candidate
   */
  static async generateAnalysis(
    candidateId: number,
    jobPostingId?: number,
  ): Promise<CandidateAnalysisResponse> {
    try {
      const requestData: AIAnalysisRequest = {
        candidate_id: candidateId,
      };

      if (jobPostingId) {
        requestData.job_posting_id = jobPostingId;
      }

            const response = await apiService.generateCandidateAnalysis(requestData);

      // The API service returns: { status: "success", message: "...", data: {...} }
      if (response.status === "error") {
        throw new Error(response.message || "AI analysis generation failed");
      }

      // Return the proper response structure
      const analysisResponse: CandidateAnalysisResponse = {
        status: "success",
        message: response.message || "Candidate analysis completed successfully",
        data: response.data as CandidateAnalysisData,
      };

      return analysisResponse;
    } catch (error: any) {
      // Handle API errors from our ApiService error classes
      if (error.statusCode && error.message) {
        throw new Error(error.message || "Failed to generate AI analysis");
      }
      
      // Handle network or other errors
      throw new Error(
        error.message || "Network error occurred while generating AI analysis",
      );
    }
  }

  /**
   * Check analysis status by polling
   */
  static async checkAnalysisStatus(
    analysisId: number,
  ): Promise<CandidateAnalysisData> {
    try {
            const response = await apiService.getCandidateAnalysis(analysisId);

      if (response.status === "error") {
        throw new Error(response.message || "Failed to check analysis status");
      }

      return response.data as CandidateAnalysisData;
    } catch (error: any) {
      throw new Error(
        error.message || "Failed to check analysis status",
      );
    }
  }

  /**
   * Get existing analysis for a candidate
   */
  static async getExistingAnalysis(
    candidateId: number,
    jobPostingId?: number,
  ): Promise<CandidateAnalysisData | null> {
    try {
      const params = new URLSearchParams({
        candidate_id: candidateId.toString(),
      });

      if (jobPostingId) {
        params.append("job_posting_id", jobPostingId.toString());
      }

            const requestParams = {
        candidate_id: candidateId,
        job_posting_id: jobPostingId,
        per_page: 1,
      };

      const response = await apiService.getCandidateAnalyses(requestParams);

            if (response.status === "error") {
        return null;
      }

      // Handle both paginated and direct array responses
      const analyses = Array.isArray(response.data)
        ? response.data as CandidateAnalysisData[]
        : (response.data as any)?.data || [];
      return analyses.length > 0 ? analyses[0] : null;
    } catch (error: any) {
      console.warn("Failed to get existing analysis:", error.message);
      return null;
    }
  }

  /**
   * Format analysis scores for display
   */
  static formatScores(scores: any): {
    overall: number;
    skills: number;
    experience: number;
    education: number;
    cultural_fit: number;
    average: number;
  } {
    return {
      overall: scores?.overall || 0,
      skills: scores?.skills || 0,
      experience: scores?.experience || 0,
      education: scores?.education || 0,
      cultural_fit: scores?.cultural_fit || 0,
      average: scores?.average || 0,
    };
  }

  /**
   * Format AI analysis data for display
   */
  static formatAnalysisData(data: CandidateAnalysisData) {
    return {
      id: data.id,
      candidateId: data.candidate_id,
      jobPostingId: data.job_posting_id,
      analysisType: data.analysis_type,
      status: data.status,
      summary: data.ai_analysis?.summary || "",
      strengths: data.ai_analysis?.strengths || [],
      weaknesses: data.ai_analysis?.weaknesses || [],
      improvementAreas: data.ai_analysis?.improvement_areas || [],
      recommendations: data.ai_analysis?.recommendations || [],
      scores: this.formatScores(data.scores),
      jobMatching: data.job_matching ? {
        matchPercentage: data.job_matching.match_details?.match_percentage || 0,
        keyAlignments: data.job_matching.match_details?.key_alignments || [],
        missingRequirements: data.job_matching.missing_requirements || [],
        matchingCriteria: data.job_matching.matching_criteria || [],
      } : null,
      createdAt: data.created_at,
      processing: data.processing,
    };
  }
}
