/**
 * Candidate API Service
 * Handles all API interactions for candidates
 */

import { BaseService, ApiResponse, PaginatedResponse, QueryParams } from '@/core/api';
import { ApiClient } from '@/core/api/ApiClient';
import { Candidate, CreateCandidateData, UpdateCandidateData } from '../types';

export interface CandidateQueryParams extends QueryParams {
  status?: string[];
  skills?: string[];
  experience?: {
    min?: number;
    max?: number;
  };
  location?: string;
  salaryRange?: {
    min?: number;
    max?: number;
  };
  jobId?: string;
}

export interface CandidateStats {
  total: number;
  byStatus: Record<string, number>;
  recentApplications: number;
  averageExperience: number;
}

export class CandidateApiService extends BaseService<Candidate, CreateCandidateData, UpdateCandidateData> {
  constructor(apiClient: ApiClient) {
    super(apiClient, '/candidates');
  }

  /**
   * Get candidates with advanced filtering
   */
  async getCandidates(params?: CandidateQueryParams): Promise<PaginatedResponse<Candidate>> {
    const queryParams = this.buildCandidateQueryParams(params);
    return this.apiClient.get<Candidate[]>(this.baseEndpoint, queryParams);
  }

  /**
   * Get candidate by ID with full details
   */
  async getCandidateById(id: string): Promise<ApiResponse<Candidate>> {
    return this.getById(id);
  }

  /**
   * Create new candidate
   */
  async createCandidate(data: CreateCandidateData): Promise<ApiResponse<Candidate>> {
    return this.create(data);
  }

  /**
   * Update candidate
   */
  async updateCandidate(id: string, data: UpdateCandidateData): Promise<ApiResponse<Candidate>> {
    return this.update(id, data);
  }

  /**
   * Delete candidate
   */
  async deleteCandidate(id: string): Promise<ApiResponse<void>> {
    return this.delete(id);
  }

  /**
   * Update candidate status
   */
  async updateCandidateStatus(
    id: string, 
    status: Candidate['status'],
    notes?: string
  ): Promise<ApiResponse<Candidate>> {
    return this.apiClient.patch<Candidate>(`${this.baseEndpoint}/${id}/status`, {
      status,
      notes,
    });
  }

  /**
   * Add notes to candidate
   */
  async addCandidateNote(
    id: string,
    note: string,
    type: 'general' | 'interview' | 'feedback' = 'general'
  ): Promise<ApiResponse<Candidate>> {
    return this.apiClient.post<Candidate>(`${this.baseEndpoint}/${id}/notes`, {
      note,
      type,
    });
  }

  /**
   * Upload candidate resume
   */
  async uploadResume(id: string, file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData();
    formData.append('resume', file);

    return this.apiClient.request<{ url: string }>(`${this.baseEndpoint}/${id}/resume`, {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set content-type for FormData
    });
  }

  /**
   * Extract resume data using AI
   */
  async extractResumeData(id: string): Promise<ApiResponse<Partial<Candidate>>> {
    return this.apiClient.post<Partial<Candidate>>(`${this.baseEndpoint}/${id}/extract-resume`);
  }

  /**
   * Get candidate statistics
   */
  async getCandidateStats(filters?: Partial<CandidateQueryParams>): Promise<ApiResponse<CandidateStats>> {
    const queryParams = filters ? this.buildCandidateQueryParams(filters) : {};
    return this.apiClient.get<CandidateStats>(`${this.baseEndpoint}/stats`, queryParams);
  }

  /**
   * Search candidates by skills
   */
  async searchBySkills(skills: string[], params?: QueryParams): Promise<PaginatedResponse<Candidate>> {
    const queryParams = {
      ...this.buildQueryParams(params),
      skills: skills.join(','),
    };
    return this.apiClient.get<Candidate[]>(`${this.baseEndpoint}/search/skills`, queryParams);
  }

  /**
   * Get similar candidates
   */
  async getSimilarCandidates(id: string, limit: number = 5): Promise<ApiResponse<Candidate[]>> {
    return this.apiClient.get<Candidate[]>(`${this.baseEndpoint}/${id}/similar`, { limit });
  }

  /**
   * Bulk update candidate status
   */
  async bulkUpdateStatus(
    candidateIds: string[],
    status: Candidate['status'],
    notes?: string
  ): Promise<ApiResponse<Candidate[]>> {
    return this.apiClient.patch<Candidate[]>(`${this.baseEndpoint}/bulk/status`, {
      candidateIds,
      status,
      notes,
    });
  }

  /**
   * Export candidates to CSV
   */
  async exportCandidates(params?: CandidateQueryParams): Promise<ApiResponse<{ downloadUrl: string }>> {
    const queryParams = this.buildCandidateQueryParams(params);
    return this.apiClient.get<{ downloadUrl: string }>(`${this.baseEndpoint}/export`, queryParams);
  }

  /**
   * Import candidates from CSV
   */
  async importCandidates(file: File): Promise<ApiResponse<{ imported: number; errors: string[] }>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.apiClient.request<{ imported: number; errors: string[] }>(`${this.baseEndpoint}/import`, {
      method: 'POST',
      body: formData,
      headers: {},
    });
  }

  /**
   * Get candidate activity timeline
   */
  async getCandidateActivity(id: string): Promise<ApiResponse<any[]>> {
    return this.apiClient.get<any[]>(`${this.baseEndpoint}/${id}/activity`);
  }

  /**
   * Add candidate to job
   */
  async addToJob(candidateId: string, jobId: string): Promise<ApiResponse<void>> {
    return this.apiClient.post<void>(`${this.baseEndpoint}/${candidateId}/jobs/${jobId}`);
  }

  /**
   * Remove candidate from job
   */
  async removeFromJob(candidateId: string, jobId: string): Promise<ApiResponse<void>> {
    return this.apiClient.delete<void>(`${this.baseEndpoint}/${candidateId}/jobs/${jobId}`);
  }

  /**
   * Get candidates for a specific job
   */
  async getCandidatesForJob(jobId: string, params?: QueryParams): Promise<PaginatedResponse<Candidate>> {
    const queryParams = this.buildQueryParams(params);
    return this.apiClient.get<Candidate[]>(`/jobs/${jobId}/candidates`, queryParams);
  }

  // Protected method overrides
  protected validateCreateData(data: CreateCandidateData): void {
    super.validateCreateData(data);
    
    if (!data.name?.trim()) {
      throw new Error('Candidate name is required');
    }
    
    if (!data.email?.trim()) {
      throw new Error('Candidate email is required');
    }
    
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      throw new Error('Invalid email format');
    }
  }

  protected validateUpdateData(data: UpdateCandidateData): void {
    super.validateUpdateData(data);
    
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      throw new Error('Invalid email format');
    }
  }

  private buildCandidateQueryParams(params?: CandidateQueryParams): Record<string, any> {
    if (!params) return {};

    const queryParams = this.buildQueryParams(params);

    // Add candidate-specific filters
    if (params.status?.length) {
      queryParams.status = params.status.join(',');
    }

    if (params.skills?.length) {
      queryParams.skills = params.skills.join(',');
    }

    if (params.experience) {
      if (params.experience.min !== undefined) {
        queryParams.experienceMin = params.experience.min;
      }
      if (params.experience.max !== undefined) {
        queryParams.experienceMax = params.experience.max;
      }
    }

    if (params.location) {
      queryParams.location = params.location;
    }

    if (params.salaryRange) {
      if (params.salaryRange.min !== undefined) {
        queryParams.salaryMin = params.salaryRange.min;
      }
      if (params.salaryRange.max !== undefined) {
        queryParams.salaryMax = params.salaryRange.max;
      }
    }

    if (params.jobId) {
      queryParams.jobId = params.jobId;
    }

    return queryParams;
  }
}
