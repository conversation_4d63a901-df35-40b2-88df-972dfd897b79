/**
 * Candidate Type Adapters
 * Functions to convert between different candidate type representations
 */

import {
  Candidate as ModernCandida<PERSON>,
  CandidateListItem,
  CandidateUtils,
  CandidateStatus,
} from "./candidate.types";
import { LegacyCandidate } from "./legacy.types";
import { UiCandidate, ApiCandidate } from "@/lib/adapters/types";

/**
 * Convert Legacy Candidate to Modern Candidate
 */
export function legacyToModern(legacy: LegacyCandidate): ModernCandidate {
  return {
    id: legacy.id,
    name: legacy.name,
    email: legacy.email,
    phone: legacy.phone,
    avatar: legacy.avatar,
    initials: legacy.initials,
    location: legacy.location,

    // Professional Information
    position: legacy.position,
    experience: parseInt(legacy.experience) || 0,
    expectedSalary: legacy.salaryExpectationMin,
    skills: legacy.skills || [],

    // Status and Priority
    status: legacy.status as CandidateStatus,
    priority: "medium", // Default priority

    // Application Information
    appliedAt: legacy.appliedDate,
    source: legacy.source,

    // Additional Information
    notes: legacy.notes || undefined,
    tags: legacy.tags || [],

    // Documents and Links
    resumeUrl: legacy.resumeUrl,
    linkedinUrl: legacy.linkedinUrl,
    githubUrl: legacy.githubUrl,
    portfolioUrl: legacy.portfolioUrl,

    // Legacy compatibility fields
    rating: legacy.rating,
    interviewDate: legacy.interviewDate,
    jobId: legacy.jobId,
    educationDetails: legacy.education,
    workHistory: legacy.workHistory,

    // Ratings and Scores
    aiScore: legacy.aiScore,

    // Computed fields
    fullName: legacy.name,
    formattedExperience: CandidateUtils.formatExperience(
      parseInt(legacy.experience) || 0,
    ),
    statusColor: CandidateUtils.getStatusColor(
      legacy.status as CandidateStatus,
    ),

    // Base entity fields
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

/**
 * Convert Modern Candidate to Legacy Candidate
 */
export function modernToLegacy(modern: ModernCandidate): LegacyCandidate {
  return {
    id: modern.id,
    name: modern.name,
    email: modern.email,
    phone: modern.phone || "",
    avatar: modern.avatar,
    initials: modern.initials || CandidateUtils.generateInitials(modern.name),
    position: modern.position || "",
    experience: modern.experience?.toString() || "0",
    skills: modern.skills || [],
    status: modern.status,
    appliedDate: modern.appliedAt || new Date().toISOString(),
    source: modern.source || "direct",
    location: modern.location || "",
    salary: modern.expectedSalary?.toString(),
    notes: typeof modern.notes === "string" ? modern.notes : undefined,
    resumeUrl: modern.resumeUrl,
    linkedinUrl: modern.linkedinUrl,
    githubUrl: modern.githubUrl,
    portfolioUrl: modern.portfolioUrl,
    rating: modern.rating,
    aiScore: modern.aiScore,
    tags: modern.tags,
    education: modern.educationDetails,
    workHistory: modern.workHistory,
    salaryExpectationMin: modern.expectedSalary,
    salaryExpectationMax: modern.expectedSalary
      ? modern.expectedSalary * 1.2
      : undefined,
    salaryCurrency: "USD",
    salaryExpectation: modern.expectedSalary
      ? {
          min: modern.expectedSalary.toString(),
          max: (modern.expectedSalary * 1.2).toString(),
          currency: "USD",
          range: `${modern.expectedSalary} - ${modern.expectedSalary * 1.2} USD`,
        }
      : undefined,
  };
}

/**
 * Convert UI Candidate to Modern Candidate
 */
export function uiToModern(ui: UiCandidate): ModernCandidate {
  return {
    id: ui.id,
    name: ui.name,
    email: ui.email,
    phone: ui.phone,
    avatar: ui.avatar,
    initials: ui.initials,
    location: ui.location,

    // Professional Information
    position: ui.position,
    experience: parseInt(ui.experience || "0") || 0,
    expectedSalary: ui.salaryExpectationMin,
    skills: ui.skills || [],

    // Status and Priority
    status: ui.status as CandidateStatus,
    priority: "medium", // Default priority

    // Application Information
    appliedAt: ui.appliedDate,
    source: ui.source,

    // Additional Information
    notes: ui.notes,
    tags: ui.tags || [],

    // Documents and Links
    resumeUrl: ui.resumeUrl,
    linkedinUrl: ui.linkedinUrl,
    githubUrl: ui.githubUrl,
    portfolioUrl: ui.portfolioUrl,

    // Ratings and Scores
    rating: ui.rating,
    aiScore: ui.aiScore,

    // Computed fields
    fullName: ui.name,
    formattedExperience: CandidateUtils.formatExperience(
      parseInt(ui.experience || "0") || 0,
    ),
    statusColor: CandidateUtils.getStatusColor(ui.status as CandidateStatus),

    // Base entity fields
    createdAt: ui.createdAt || new Date().toISOString(),
    updatedAt: ui.updatedAt || new Date().toISOString(),
  };
}

/**
 * Convert Modern Candidate to UI Candidate
 */
export function modernToUi(modern: ModernCandidate): UiCandidate {
  return {
    id: modern.id,
    name: modern.name,
    email: modern.email,
    phone: modern.phone,
    initials: modern.initials || CandidateUtils.generateInitials(modern.name),
    position: modern.position,
    experience: modern.experience?.toString(),
    skills: modern.skills || [],
    status: modern.status,
    appliedDate: modern.appliedAt,
    source: modern.source,
    location: modern.location,
    salary: modern.expectedSalary?.toString() || "",
    rating: modern.rating,
    interviewDate: modern.interviewDate,
    jobId: modern.jobId,
    linkedinUrl: modern.linkedinUrl,
    githubUrl: modern.githubUrl,
    portfolioUrl: modern.portfolioUrl,
    avatar: modern.avatar,
    resumeUrl: modern.resumeUrl,
    notes: modern.notes,
    aiScore: modern.aiScore || 0,
    tags: modern.tags || [],
    education: modern.educationDetails || "",
    workHistory: modern.workHistory || "",
    salaryExpectationMin: modern.expectedSalary,
    salaryExpectationMax: modern.expectedSalary
      ? modern.expectedSalary * 1.2
      : undefined,
    salaryCurrency: "USD",
    createdAt: modern.createdAt,
    updatedAt: modern.updatedAt,
  };
}

/**
 * Convert API Candidate to Modern Candidate
 */
export function apiToModern(api: ApiCandidate): ModernCandidate {
  return {
    id: api.id.toString(),
    name: api.name,
    email: api.email,
    phone: api.phone,
    avatar: api.avatar_url || api.avatar,
    location: api.location,

    // Professional Information
    position: api.position,
    experience: parseInt(api.experience || "0") || 0,
    expectedSalary: api.salary_expectation_min,
    skills: api.skills || [],

    // Status and Priority
    status: api.status as CandidateStatus,
    priority: "medium", // Default priority

    // Application Information
    appliedAt: api.applied_date,
    source: api.source,

    // Additional Information
    notes: api.notes,

    // Documents and Links
    resumeUrl: api.resume_url,

    // Ratings and Scores
    rating: api.rating,

    // Computed fields
    fullName: api.name,
    initials: CandidateUtils.generateInitials(api.name),
    formattedExperience: CandidateUtils.formatExperience(
      parseInt(api.experience || "0") || 0,
    ),
    statusColor: CandidateUtils.getStatusColor(api.status as CandidateStatus),

    // Base entity fields
    createdAt: api.created_at || new Date().toISOString(),
    updatedAt: api.updated_at || new Date().toISOString(),
  };
}

/**
 * Convert Modern Candidate to CandidateListItem
 */
export function modernToListItem(modern: ModernCandidate): CandidateListItem {
  return {
    id: modern.id,
    name: modern.name,
    email: modern.email,
    position: modern.position,
    status: modern.status,
    experience: modern.experience,
    expectedSalary: modern.expectedSalary,
    appliedAt: modern.appliedAt,
    avatar: modern.avatar,
    skills: modern.skills,
    aiScore: modern.aiScore,

    // Computed fields
    initials: modern.initials || CandidateUtils.generateInitials(modern.name),
    statusColor:
      modern.statusColor || CandidateUtils.getStatusColor(modern.status),
    experienceLevel: modern.experienceLevel,
  };
}

/**
 * Batch conversion utilities
 */
export const CandidateAdapters = {
  legacyToModern,
  modernToLegacy,
  uiToModern,
  modernToUi,
  apiToModern,
  modernToListItem,

  // Batch conversions
  legacyArrayToModern: (legacyArray: LegacyCandidate[]): ModernCandidate[] =>
    legacyArray.map(legacyToModern),

  modernArrayToLegacy: (modernArray: ModernCandidate[]): LegacyCandidate[] =>
    modernArray.map(modernToLegacy),

  modernArrayToListItems: (
    modernArray: ModernCandidate[],
  ): CandidateListItem[] => modernArray.map(modernToListItem),
};
