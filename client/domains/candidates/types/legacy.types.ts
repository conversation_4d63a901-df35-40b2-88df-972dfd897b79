/**
 * Legacy Candidate Types
 * Compatibility layer for existing legacy components
 */

import { BaseEntity } from "../../../core/api";

// Legacy candidate status (matching mockData.ts)
export type LegacyCandidateStatus =
  | "sourced"
  | "applied"
  | "screening"
  | "interview"
  | "offer"
  | "hired"
  | "rejected";

// Legacy Candidate interface (matching mockData.ts structure)
export interface LegacyCandidate {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  initials: string;
  position: string;
  experience: string; // legacy uses string, not number
  skills: string[];
  status: LegacyCandidateStatus;
  appliedDate: string;
  source: string;
  location: string;
  salary?: string;
  notes?: string;
  resumeUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  rating?: number;
  interviewDate?: string;
  jobId?: string;
  tags?: string[];
  education?: string;
  workHistory?: string;
  aiScore?: number;
  salaryExpectationMin?: number;
  salaryExpectationMax?: number;
  salaryCurrency?: string;
  salaryExpectation?: {
    min: string;
    max: string;
    currency: string;
    range: string;
  };
  jobPosting?: {
    id: number;
    title: string;
    department: string;
  };
  createdBy?: {
    id: number;
    name: string;
    email: string;
  };
  assignedTo?: {
    id: number;
    name: string;
    email: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

// Type alias for backward compatibility
export type Candidate = LegacyCandidate;

// Legacy Job interface (from mockData.ts)
export interface LegacyJob {
  positions: number;
  id: string;
  title: string;
  department: string;
  location: string;
  type: "full-time" | "part-time" | "contract" | "internship";
  workLocation?: "onsite" | "remote" | "hybrid";
  salary: {
    min: string;
    max: string;
    currency: string;
    range?: string;
  };
  description: string;
  requirements: string[];
  benefits: string[];
  responsibilities?: string[];
  skills?: string[];
  status: "draft" | "active" | "paused" | "closed";
  postedDate: string;
  closingDate: string;
  applicantCount: number;
  viewCount?: number;
  hiringManager: string;
  hiringManagerId?: number;
  recruiter?: string;
  recruiterId?: number;
  priority: "low" | "medium" | "high" | "urgent";
  experienceLevel?: "entry" | "mid" | "senior" | "lead";
  educationRequired?: string;
  companyCulture?: string;
}

// Type alias for backward compatibility
export type Job = LegacyJob;

// Legacy Interview interface (from mockData.ts)
export interface LegacyInterview {
  id: string;
  candidateId: string;
  candidateName: string;
  jobId: string;
  jobTitle: string;
  interviewerId: string;
  interviewerName: string;
  type: "phone" | "video" | "in-person" | "technical";
  status: "scheduled" | "completed" | "cancelled" | "no-show";
  scheduledDate: string;
  duration: number;
  location?: string;
  meetingUrl?: string;
  notes?: string;
  feedback?: string;
  rating?: number;
  outcome?: "hire" | "no-hire" | "maybe";
  createdAt: string;
  updatedAt: string;
}

// Type alias for backward compatibility
export type Interview = LegacyInterview;

// Utility functions for legacy compatibility
export const getLegacyCandidateInitials = (name: string): string => {
  return name
    .split(" ")
    .map((part) => part.charAt(0).toUpperCase())
    .join("")
    .slice(0, 2);
};

export const formatLegacyExperience = (experience: string | number): string => {
  if (typeof experience === "number") {
    return experience === 1 ? "1 year" : `${experience} years`;
  }
  return experience;
};

export const getLegacyStatusColor = (status: LegacyCandidateStatus): string => {
  switch (status) {
    case "hired":
      return "bg-green-100 text-green-800";
    case "rejected":
      return "bg-red-100 text-red-800";
    case "interview":
      return "bg-blue-100 text-blue-800";
    case "offer":
      return "bg-purple-100 text-purple-800";
    case "screening":
      return "bg-yellow-100 text-yellow-800";
    case "applied":
      return "bg-blue-100 text-blue-800";
    case "sourced":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};
