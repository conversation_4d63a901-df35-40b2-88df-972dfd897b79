# Candidate Types Documentation

## Overview

The candidate types system provides a comprehensive, type-safe foundation for managing candidate data throughout the ATS application. It includes modern domain types, legacy compatibility, and seamless adapters for migration.

## Type System Architecture

### 1. Modern Domain Types (`candidate.types.ts`)

**Primary Types:**
- `Candidate` - Core candidate entity with full domain model
- `CandidateStatus` - Standardized status enum
- `CandidatePriority` - Priority levels for candidate management
- `ExperienceLevel` - Standardized experience classifications
- `CandidateSource` - Source tracking for candidate acquisition

**Supporting Interfaces:**
- `CandidateNote` - Notes and comments system
- `CandidateApplication` - Job application tracking
- `Interview` - Interview scheduling and feedback
- `CandidateDocument` - Document management
- `CandidateActivity` - Activity tracking and audit trail

**Search and Filtering:**
- `CandidateSearchFilters` - Comprehensive search criteria
- `CandidateListItem` - Optimized list view representation
- `CandidateListResponse` - API response structure

**Analytics and Reporting:**
- `CandidateAnalytics` - Comprehensive analytics data
- `CandidateComparison` - Side-by-side candidate comparison
- `CandidateAIScore` - AI-powered candidate scoring

### 2. Legacy Compatibility (`legacy.types.ts`)

**Purpose:** Maintains backward compatibility with existing components and mock data.

**Key Types:**
- `LegacyCandidate` - Original candidate structure
- `LegacyCandidateStatus` - Original status enum
- `LegacyJob` - Job structure for compatibility
- `LegacyInterview` - Interview structure for compatibility

### 3. Type Adapters (`adapters.ts`)

**Conversion Functions:**
- `legacyToModern()` - Convert legacy to modern types
- `modernToLegacy()` - Convert modern to legacy types
- `uiToModern()` - Convert UI layer types to domain types
- `modernToUi()` - Convert domain types to UI layer
- `apiToModern()` - Convert API responses to domain types
- `modernToListItem()` - Convert to optimized list items

## Usage Examples

### Basic Type Usage

```typescript
import { 
  Candidate, 
  CandidateStatus, 
  CandidateUtils,
  CandidateTypeGuards 
} from '@/domains/candidates/types';

// Create a new candidate
const candidate: Candidate = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  status: 'applied',
  skills: ['JavaScript', 'React', 'Node.js'],
  experience: 5,
  // ... other fields
};

// Use utility functions
const initials = CandidateUtils.generateInitials(candidate.name); // "JD"
const statusColor = CandidateUtils.getStatusColor(candidate.status);
const formattedExp = CandidateUtils.formatExperience(candidate.experience);

// Type guards
if (CandidateTypeGuards.isCandidate(data)) {
  // TypeScript knows data is a Candidate
  console.log(data.name);
}
```

### Legacy Compatibility

```typescript
import { 
  Candidate, // This is actually LegacyCandidate for backward compatibility
  LegacyCandidate,
  ModernCandidate 
} from '@/domains/candidates/types';

// Existing components continue to work
const legacyCandidate: Candidate = {
  id: '1',
  name: 'John Doe',
  // ... legacy structure
};

// New components can use modern types
const modernCandidate: ModernCandidate = {
  id: '1',
  name: 'John Doe',
  // ... modern structure with better typing
};
```

### Type Conversion

```typescript
import { CandidateAdapters } from '@/domains/candidates/types';

// Convert between type systems
const modernCandidate = CandidateAdapters.legacyToModern(legacyCandidate);
const legacyCandidate = CandidateAdapters.modernToLegacy(modernCandidate);
const listItem = CandidateAdapters.modernToListItem(modernCandidate);

// Batch conversions
const modernCandidates = CandidateAdapters.legacyArrayToModern(legacyArray);
const listItems = CandidateAdapters.modernArrayToListItems(modernArray);
```

### Search and Filtering

```typescript
import { CandidateSearchFilters } from '@/domains/candidates/types';

const filters: CandidateSearchFilters = {
  query: 'JavaScript developer',
  status: ['applied', 'screening'],
  skills: ['JavaScript', 'React'],
  experienceRange: { min: 2, max: 8 },
  salaryRange: { min: 50000, max: 120000 },
  location: 'San Francisco',
  education: ['bachelor', 'master'],
  priority: ['high', 'urgent'],
};
```

### Analytics and Reporting

```typescript
import { CandidateAnalytics, CandidateComparison } from '@/domains/candidates/types';

// Analytics data structure
const analytics: CandidateAnalytics = {
  totalCandidates: 150,
  newCandidates: 25,
  activeCandidates: 80,
  hiredCandidates: 12,
  byStatus: {
    sourced: 30,
    applied: 45,
    screening: 25,
    interview: 20,
    offer: 8,
    hired: 12,
    rejected: 10,
  },
  conversionRates: {
    sourcedToApplied: 0.75,
    appliedToScreening: 0.55,
    screeningToInterview: 0.80,
    interviewToOffer: 0.40,
    offerToHired: 0.85,
  },
  // ... more analytics data
};

// Candidate comparison
const comparison: CandidateComparison = {
  candidates: [candidate1, candidate2, candidate3],
  criteria: {
    skills: 0.4,
    experience: 0.3,
    education: 0.2,
    salary: 0.1,
    location: 0.0,
  },
  scores: {
    '1': 85,
    '2': 92,
    '3': 78,
  },
  recommendations: ['Candidate 2 has the highest overall score'],
};
```

## Migration Strategy

### Phase 1: Backward Compatibility (Current)
- Legacy types exported as primary types (`Candidate` = `LegacyCandidate`)
- Modern types available with explicit names (`ModernCandidate`)
- All existing components continue to work without changes

### Phase 2: Gradual Migration
- New components use modern types
- Existing components gradually migrated using adapters
- Type conversion at boundaries (API, UI, business logic)

### Phase 3: Full Migration
- Switch primary exports to modern types
- Legacy types available for specific compatibility needs
- Remove adapters where no longer needed

## Best Practices

### 1. Type Selection
- **New Components:** Use modern types (`ModernCandidate`)
- **Legacy Components:** Continue using current types during migration
- **API Layer:** Use adapters to convert between API and domain types
- **UI Layer:** Use `CandidateListItem` for lists and tables

### 2. Validation
```typescript
import { CandidateTypeGuards, CandidateUtils } from '@/domains/candidates/types';

// Always validate external data
if (!CandidateTypeGuards.isCandidate(data)) {
  throw new Error('Invalid candidate data');
}

// Validate specific fields
if (!CandidateUtils.isValidEmail(candidate.email)) {
  throw new Error('Invalid email address');
}
```

### 3. Utility Usage
```typescript
// Use utilities for consistent formatting
const initials = CandidateUtils.generateInitials(name);
const statusColor = CandidateUtils.getStatusColor(status);
const formattedSalary = CandidateUtils.formatSalary(amount, currency);
```

### 4. Type Conversion
```typescript
// Convert at boundaries, not in business logic
const apiResponse = await fetchCandidates();
const candidates = apiResponse.data.map(CandidateAdapters.apiToModern);

// Use appropriate list types for performance
const listItems = CandidateAdapters.modernArrayToListItems(candidates);
```

## Type Safety Features

1. **Strict Enums:** All status, priority, and level types are strictly typed
2. **Required Fields:** Core fields are required, optional fields clearly marked
3. **Type Guards:** Runtime type checking for external data
4. **Utility Functions:** Type-safe utility functions for common operations
5. **Adapter Functions:** Safe conversion between type systems

## Future Enhancements

1. **Schema Validation:** Add runtime schema validation using Zod
2. **Serialization:** Add JSON serialization/deserialization helpers
3. **Immutability:** Consider immutable data structures for state management
4. **GraphQL Types:** Generate GraphQL schema from TypeScript types
5. **API Documentation:** Auto-generate API documentation from types
