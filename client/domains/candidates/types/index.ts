// Modern candidate types (primary exports)
export * from "./candidate.types";

// Legacy types for backward compatibility
export type {
  LegacyCandidate,
  LegacyJob,
  LegacyInterview,
  LegacyCandidateStatus,
} from "./legacy.types";

export {
  getLegacyCandidateInitials,
  formatLegacyExperience,
  getLegacyStatusColor,
} from "./legacy.types";

// Type aliases for backward compatibility
// Components can import 'Candidate' and get the legacy type for now
export type { LegacyCandidate as Candidate } from "./legacy.types";
export type { LegacyJob as Job } from "./legacy.types";
export type { LegacyInterview as Interview } from "./legacy.types";

// Re-export modern types with explicit names for gradual migration
export type {
  Candidate as ModernCandidate,
  CandidateStatus as ModernCandidateStatus,
  CandidatePriority,
  ExperienceLevel,
  EducationLevel,
  EmploymentType,
  CandidateSource,
  CandidateNote,
  CandidateApplication,
  Interview as ModernInterview,
  InterviewFeedback,
  CreateCandidateData,
  UpdateCandidateData,
  CandidateSearchFilters,
  CandidateListItem,
  CandidateListResponse,
  CandidateRecommendation,
  CandidateAnalytics,
  CandidateActivity,
  CandidateDocument,
  CandidateCommunication,
  BulkCandidateOperation,
  CandidateImportData,
  CandidateExportOptions,
  PipelineStage,
  CandidatePipeline,
  CandidateAIScore,
  CandidateComparison,
} from "./candidate.types";

// Re-export utilities
export { CandidateUtils, CandidateTypeGuards } from "./candidate.types";

// Re-export adapters
export { CandidateAdapters } from "./adapters";
