/**
 * Candidate Domain Types
 * Core types and interfaces for the candidates domain
 */

import { BaseEntity } from "../../../core/api";

// Candidate status enum (matching legacy and API)
export type CandidateStatus =
  | "sourced"
  | "applied"
  | "screening"
  | "interview"
  | "offer"
  | "hired"
  | "rejected";

// Candidate priority levels
export type CandidatePriority = "low" | "medium" | "high" | "urgent";

// Experience levels
export type ExperienceLevel =
  | "entry"
  | "junior"
  | "mid"
  | "senior"
  | "lead"
  | "expert";

// Employment types
export type EmploymentType =
  | "full-time"
  | "part-time"
  | "contract"
  | "internship"
  | "freelance";

// Education levels
export type EducationLevel =
  | "high-school"
  | "associate"
  | "bachelor"
  | "master"
  | "doctorate"
  | "other";

// Candidate source types
export type CandidateSource =
  | "linkedin"
  | "indeed"
  | "glassdoor"
  | "referral"
  | "direct"
  | "agency"
  | "career-site"
  | "other";

// Core Candidate interface
export interface Candidate extends BaseEntity {
  // Basic Information
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  initials?: string; // Computed field for UI
  location?: string;

  // Professional Information
  position?: string;
  currentCompany?: string;
  experience?: number; // years of experience
  expectedSalary?: number;
  currentSalary?: number;
  currency?: string;

  // Skills and Education
  skills?: string[];
  education?: EducationLevel;
  educationDetails?: string;
  certifications?: string[];
  languages?: string[];

  // Status and Metadata
  status: CandidateStatus;
  priority?: CandidatePriority;
  source?: string; // where the candidate came from
  tags?: string[];

  // Documents and Links
  resumeUrl?: string;
  portfolioUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;

  // Notes and Communication
  notes?: CandidateNote[] | string; // Support both new and legacy formats
  summary?: string;

  // Job Applications
  applications?: CandidateApplication[];

  // Legacy compatibility fields
  rating?: number;
  interviewDate?: string;
  jobId?: string;
  workHistory?: string;

  // AI and Analytics
  aiScore?: number;
  aiSummary?: string;
  skillMatchScore?: number;

  // Timestamps
  appliedAt?: string;
  lastContactedAt?: string;

  // Computed fields (added by business service)
  fullName?: string;
  experienceLevel?: ExperienceLevel;
  statusColor?: string;
  formattedSalary?: string;
  formattedExperience?: string;
}

// Candidate note interface
export interface CandidateNote {
  id: string;
  content: string;
  type: "general" | "interview" | "feedback" | "system";
  authorId: string;
  authorName: string;
  createdAt: string;
  isPrivate?: boolean;
}

// Candidate application interface
export interface CandidateApplication {
  id: string;
  jobId: string;
  jobTitle: string;
  status: CandidateStatus;
  appliedAt: string;
  updatedAt: string;
  notes?: string;
  interviews?: Interview[];
}

// Interview interface
export interface Interview {
  id: string;
  type: "phone" | "video" | "in-person" | "technical";
  scheduledAt: string;
  duration: number; // minutes
  interviewerId: string;
  interviewerName: string;
  status: "scheduled" | "completed" | "cancelled" | "no-show";
  feedback?: InterviewFeedback;
  location?: string;
  meetingUrl?: string;
}

// Interview feedback interface
export interface InterviewFeedback {
  rating: number; // 1-5
  technicalSkills: number;
  communication: number;
  culturalFit: number;
  experience: number;
  comments: string;
  recommendation: "hire" | "no-hire" | "maybe";
  nextSteps?: string;
}

// Create candidate data interface
export interface CreateCandidateData {
  name: string;
  email: string;
  phone?: string;
  position?: string;
  experience?: number;
  expectedSalary?: number;
  skills?: string[];
  education?: EducationLevel;
  location?: string;
  source?: string;
  resumeUrl?: string;
  linkedinUrl?: string;
  notes?: string;
  tags?: string[];
}

// Update candidate data interface
export interface UpdateCandidateData {
  name?: string;
  email?: string;
  phone?: string;
  position?: string;
  currentCompany?: string;
  experience?: number;
  expectedSalary?: number;
  currentSalary?: number;
  skills?: string[];
  education?: EducationLevel;
  educationDetails?: string;
  certifications?: string[];
  languages?: string[];
  location?: string;
  priority?: CandidatePriority;
  tags?: string[];
  resumeUrl?: string;
  portfolioUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  summary?: string;
}

// Candidate search filters
export interface CandidateSearchFilters {
  query?: string;
  status?: CandidateStatus[];
  skills?: string[];
  experienceRange?: {
    min: number;
    max: number;
  };
  salaryRange?: {
    min: number;
    max: number;
  };
  location?: string;
  education?: EducationLevel[];
  priority?: CandidatePriority[];
  tags?: string[];
  source?: string[];
  dateRange?: {
    from: string;
    to: string;
  };
}

// Candidate list item (for tables and lists)
export interface CandidateListItem {
  id: string;
  name: string;
  email: string;
  position?: string;
  status: CandidateStatus;
  experience?: number;
  expectedSalary?: number;
  appliedAt?: string;
  avatar?: string;
  skills?: string[];
  aiScore?: number;

  // Computed fields
  initials?: string;
  statusColor?: string;
  experienceLevel?: ExperienceLevel;
}

// Candidate list response (for API responses)
export interface CandidateListResponse {
  candidates: CandidateListItem[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Candidate statistics
export interface CandidateStatistics {
  total: number;
  byStatus: Record<CandidateStatus, number>;
  bySource: Record<string, number>;
  byExperienceLevel: Record<ExperienceLevel, number>;
  averageExperience: number;
  averageSalary: number;
  recentApplications: number;
  conversionRate: number;
  timeToHire: number;
}

// Candidate activity log
export interface CandidateActivity {
  id: string;
  type:
    | "status_change"
    | "note_added"
    | "interview_scheduled"
    | "document_uploaded"
    | "email_sent";
  description: string;
  details?: Record<string, any>;
  userId: string;
  userName: string;
  timestamp: string;
}

// Bulk operations
export interface BulkCandidateOperation {
  candidateIds: string[];
  operation:
    | "update_status"
    | "add_tags"
    | "remove_tags"
    | "assign_to_job"
    | "delete"
    | "export";
  data?: Record<string, any>;
}

// Export options
export interface CandidateExportOptions {
  format: "csv" | "xlsx" | "pdf";
  fields: string[];
  filters?: CandidateSearchFilters;
  includeNotes?: boolean;
  includeDocuments?: boolean;
}

// Import result
export interface CandidateImportResult {
  total: number;
  imported: number;
  failed: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
  duplicates: Array<{
    row: number;
    email: string;
    existingId: string;
  }>;
}

// Candidate form validation
export interface CandidateFormErrors {
  name?: string;
  email?: string;
  phone?: string;
  position?: string;
  experience?: string;
  expectedSalary?: string;
  skills?: string;
  general?: string;
}

// Status transition rules
export interface StatusTransitionRule {
  from: CandidateStatus;
  to: CandidateStatus[];
  requiresNote?: boolean;
  requiresApproval?: boolean;
  autoActions?: string[];
}

// Candidate matching criteria
export interface CandidateMatchingCriteria {
  jobId: string;
  requiredSkills: string[];
  preferredSkills: string[];
  minExperience: number;
  maxExperience?: number;
  salaryRange?: {
    min: number;
    max: number;
  };
  location?: string;
  education?: EducationLevel[];
  excludeStatuses?: CandidateStatus[];
}

// Candidate recommendation
export interface CandidateRecommendation {
  candidate: CandidateListItem;
  matchScore: number;
  skillMatches: string[];
  missingSkills: string[];
  reasons: string[];
  confidence: number;
}

// Candidate analytics interface
export interface CandidateAnalytics {
  totalCandidates: number;
  newCandidates: number;
  activeCandidates: number;
  hiredCandidates: number;

  byStatus: Record<CandidateStatus, number>;
  bySource: Record<string, number>;
  byLocation: Record<string, number>;
  byExperience: Record<string, number>;

  conversionRates: {
    sourcedToApplied: number;
    appliedToScreening: number;
    screeningToInterview: number;
    interviewToOffer: number;
    offerToHired: number;
  };

  averageTimeToHire: number; // days
  topSkills: Array<{ skill: string; count: number }>;
  salaryRanges: Array<{ range: string; count: number }>;
}

// Candidate activity interface
export interface CandidateActivity {
  id: string;
  candidateId: string;
  type:
    | "status_change"
    | "note_added"
    | "interview_scheduled"
    | "email_sent"
    | "document_uploaded";
  description: string;
  details?: Record<string, any>;
  performedBy: string;
  performedByName: string;
  timestamp: string;
}

// Candidate document interface
export interface CandidateDocument {
  id: string;
  name: string;
  type: "resume" | "cover_letter" | "portfolio" | "certificate" | "other";
  url: string;
  size: number;
  uploadedAt: string;
  uploadedBy: string;
}

// Candidate communication interface
export interface CandidateCommunication {
  id: string;
  type: "email" | "phone" | "sms" | "meeting";
  subject?: string;
  content: string;
  direction: "inbound" | "outbound";
  timestamp: string;
  performedBy?: string;
  status?: "sent" | "delivered" | "read" | "failed";
}

// Candidate import interface
export interface CandidateImportData {
  name: string;
  email: string;
  phone?: string;
  position?: string;
  experience?: string;
  skills?: string;
  location?: string;
  source?: string;
  notes?: string;
}

// Candidate export interface
export interface CandidateExportOptions {
  format: "csv" | "xlsx" | "pdf";
  fields: string[];
  filters?: CandidateSearchFilters;
  includeNotes?: boolean;
  includeDocuments?: boolean;
}

// Pipeline stage interface
export interface PipelineStage {
  id: string;
  name: string;
  status: CandidateStatus;
  order: number;
  color: string;
  description?: string;
  candidates: CandidateListItem[];
  candidateCount: number;
}

// Candidate pipeline interface
export interface CandidatePipeline {
  jobId?: string;
  jobTitle?: string;
  stages: PipelineStage[];
  totalCandidates: number;
  lastUpdated: string;
}

// AI scoring interface
export interface CandidateAIScore {
  overall: number; // 0-100
  skillMatch: number;
  experienceMatch: number;
  educationMatch: number;
  locationMatch: number;
  salaryMatch: number;

  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  confidence: number;
  lastUpdated: string;
}

// Candidate comparison interface
export interface CandidateComparison {
  candidates: Candidate[];
  criteria: {
    skills: number;
    experience: number;
    education: number;
    salary: number;
    location: number;
  };
  scores: Record<string, number>; // candidateId -> total score
  recommendations: string[];
}

// Utility functions and type guards
export const CandidateUtils = {
  // Generate initials from name
  generateInitials: (name: string): string => {
    return name
      .split(" ")
      .map((part) => part.charAt(0).toUpperCase())
      .join("")
      .substring(0, 2);
  },

  // Format experience
  formatExperience: (years?: number): string => {
    if (!years) return "No experience";
    if (years < 1) return "Less than 1 year";
    if (years === 1) return "1 year";
    return `${years} years`;
  },

  // Get status color
  getStatusColor: (status: CandidateStatus): string => {
    const colors: Record<CandidateStatus, string> = {
      sourced: "bg-gray-100 text-gray-800",
      applied: "bg-blue-100 text-blue-800",
      screening: "bg-yellow-100 text-yellow-800",
      interview: "bg-purple-100 text-purple-800",
      offer: "bg-orange-100 text-orange-800",
      hired: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
    };
    return colors[status] || colors.sourced;
  },

  // Get priority color
  getPriorityColor: (priority: CandidatePriority): string => {
    const colors: Record<CandidatePriority, string> = {
      low: "bg-gray-100 text-gray-800",
      medium: "bg-yellow-100 text-yellow-800",
      high: "bg-orange-100 text-orange-800",
      urgent: "bg-red-100 text-red-800",
    };
    return colors[priority] || colors.medium;
  },

  // Format salary
  formatSalary: (amount?: number, currency = "USD"): string => {
    if (!amount) return "Not specified";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  },

  // Calculate match score
  calculateMatchScore: (
    candidate: Candidate,
    jobRequirements: string[],
  ): number => {
    if (!candidate.skills || !jobRequirements.length) return 0;

    const candidateSkills = candidate.skills.map((s) => s.toLowerCase());
    const requiredSkills = jobRequirements.map((s) => s.toLowerCase());

    const matches = requiredSkills.filter((skill) =>
      candidateSkills.some(
        (candidateSkill) =>
          candidateSkill.includes(skill) || skill.includes(candidateSkill),
      ),
    );

    return Math.round((matches.length / requiredSkills.length) * 100);
  },

  // Validate email
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate phone
  isValidPhone: (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ""));
  },
};

// Type guards
export const CandidateTypeGuards = {
  isCandidate: (obj: any): obj is Candidate => {
    return (
      obj &&
      typeof obj === "object" &&
      typeof obj.name === "string" &&
      typeof obj.email === "string" &&
      typeof obj.id === "string"
    );
  },

  isCandidateStatus: (status: string): status is CandidateStatus => {
    return [
      "sourced",
      "applied",
      "screening",
      "interview",
      "offer",
      "hired",
      "rejected",
    ].includes(status);
  },

  isCandidatePriority: (priority: string): priority is CandidatePriority => {
    return ["low", "medium", "high", "urgent"].includes(priority);
  },

  isExperienceLevel: (level: string): level is ExperienceLevel => {
    return ["entry", "junior", "mid", "senior", "lead", "expert"].includes(
      level,
    );
  },
};
