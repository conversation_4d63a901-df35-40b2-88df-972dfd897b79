/**
 * Candidate Domain Types
 * Core types and interfaces for the candidates domain
 */

import { BaseEntity } from '@/core/api';

// Candidate status enum
export type CandidateStatus = 
  | 'new'
  | 'screening' 
  | 'interview'
  | 'offer'
  | 'hired'
  | 'rejected';

// Candidate priority levels
export type CandidatePriority = 'low' | 'medium' | 'high' | 'urgent';

// Experience levels
export type ExperienceLevel = 'junior' | 'mid' | 'senior' | 'expert';

// Employment types
export type EmploymentType = 'full-time' | 'part-time' | 'contract' | 'internship' | 'freelance';

// Education levels
export type EducationLevel = 
  | 'high-school'
  | 'associate'
  | 'bachelor'
  | 'master'
  | 'doctorate'
  | 'other';

// Core Candidate interface
export interface Candidate extends BaseEntity {
  // Basic Information
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  location?: string;
  
  // Professional Information
  position?: string;
  currentCompany?: string;
  experience?: number; // years of experience
  expectedSalary?: number;
  currentSalary?: number;
  currency?: string;
  
  // Skills and Education
  skills?: string[];
  education?: EducationLevel;
  educationDetails?: string;
  certifications?: string[];
  languages?: string[];
  
  // Status and Metadata
  status: CandidateStatus;
  priority?: CandidatePriority;
  source?: string; // where the candidate came from
  tags?: string[];
  
  // Documents and Links
  resumeUrl?: string;
  portfolioUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  
  // Notes and Communication
  notes?: CandidateNote[];
  summary?: string;
  
  // Job Applications
  applications?: CandidateApplication[];
  
  // AI and Analytics
  aiScore?: number;
  aiSummary?: string;
  skillMatchScore?: number;
  
  // Timestamps
  appliedAt?: string;
  lastContactedAt?: string;
  
  // Computed fields (added by business service)
  fullName?: string;
  initials?: string;
  experienceLevel?: ExperienceLevel;
  statusColor?: string;
  formattedSalary?: string;
  formattedExperience?: string;
}

// Candidate note interface
export interface CandidateNote {
  id: string;
  content: string;
  type: 'general' | 'interview' | 'feedback' | 'system';
  authorId: string;
  authorName: string;
  createdAt: string;
  isPrivate?: boolean;
}

// Candidate application interface
export interface CandidateApplication {
  id: string;
  jobId: string;
  jobTitle: string;
  status: CandidateStatus;
  appliedAt: string;
  updatedAt: string;
  notes?: string;
  interviews?: Interview[];
}

// Interview interface
export interface Interview {
  id: string;
  type: 'phone' | 'video' | 'in-person' | 'technical';
  scheduledAt: string;
  duration: number; // minutes
  interviewerId: string;
  interviewerName: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no-show';
  feedback?: InterviewFeedback;
  location?: string;
  meetingUrl?: string;
}

// Interview feedback interface
export interface InterviewFeedback {
  rating: number; // 1-5
  technicalSkills: number;
  communication: number;
  culturalFit: number;
  experience: number;
  comments: string;
  recommendation: 'hire' | 'no-hire' | 'maybe';
  nextSteps?: string;
}

// Create candidate data interface
export interface CreateCandidateData {
  name: string;
  email: string;
  phone?: string;
  position?: string;
  experience?: number;
  expectedSalary?: number;
  skills?: string[];
  education?: EducationLevel;
  location?: string;
  source?: string;
  resumeUrl?: string;
  linkedinUrl?: string;
  notes?: string;
  tags?: string[];
}

// Update candidate data interface
export interface UpdateCandidateData {
  name?: string;
  email?: string;
  phone?: string;
  position?: string;
  currentCompany?: string;
  experience?: number;
  expectedSalary?: number;
  currentSalary?: number;
  skills?: string[];
  education?: EducationLevel;
  educationDetails?: string;
  certifications?: string[];
  languages?: string[];
  location?: string;
  priority?: CandidatePriority;
  tags?: string[];
  resumeUrl?: string;
  portfolioUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  summary?: string;
}

// Candidate search filters
export interface CandidateSearchFilters {
  query?: string;
  status?: CandidateStatus[];
  skills?: string[];
  experienceRange?: {
    min: number;
    max: number;
  };
  salaryRange?: {
    min: number;
    max: number;
  };
  location?: string;
  education?: EducationLevel[];
  priority?: CandidatePriority[];
  tags?: string[];
  source?: string[];
  dateRange?: {
    from: string;
    to: string;
  };
}

// Candidate list item (for tables and lists)
export interface CandidateListItem {
  id: string;
  name: string;
  email: string;
  position?: string;
  status: CandidateStatus;
  experience?: number;
  expectedSalary?: number;
  appliedAt?: string;
  avatar?: string;
  skills?: string[];
  aiScore?: number;
  
  // Computed fields
  initials?: string;
  statusColor?: string;
  experienceLevel?: ExperienceLevel;
}

// Candidate statistics
export interface CandidateStatistics {
  total: number;
  byStatus: Record<CandidateStatus, number>;
  bySource: Record<string, number>;
  byExperienceLevel: Record<ExperienceLevel, number>;
  averageExperience: number;
  averageSalary: number;
  recentApplications: number;
  conversionRate: number;
  timeToHire: number;
}

// Candidate activity log
export interface CandidateActivity {
  id: string;
  type: 'status_change' | 'note_added' | 'interview_scheduled' | 'document_uploaded' | 'email_sent';
  description: string;
  details?: Record<string, any>;
  userId: string;
  userName: string;
  timestamp: string;
}

// Bulk operations
export interface BulkCandidateOperation {
  candidateIds: string[];
  operation: 'update_status' | 'add_tags' | 'remove_tags' | 'delete' | 'export';
  data?: any;
}

// Export options
export interface CandidateExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  fields: string[];
  filters?: CandidateSearchFilters;
  includeNotes?: boolean;
  includeDocuments?: boolean;
}

// Import result
export interface CandidateImportResult {
  total: number;
  imported: number;
  failed: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
  duplicates: Array<{
    row: number;
    email: string;
    existingId: string;
  }>;
}

// Candidate form validation
export interface CandidateFormErrors {
  name?: string;
  email?: string;
  phone?: string;
  position?: string;
  experience?: string;
  expectedSalary?: string;
  skills?: string;
  general?: string;
}

// Status transition rules
export interface StatusTransitionRule {
  from: CandidateStatus;
  to: CandidateStatus[];
  requiresNote?: boolean;
  requiresApproval?: boolean;
  autoActions?: string[];
}

// Candidate matching criteria
export interface CandidateMatchingCriteria {
  jobId: string;
  requiredSkills: string[];
  preferredSkills: string[];
  minExperience: number;
  maxExperience?: number;
  salaryRange?: {
    min: number;
    max: number;
  };
  location?: string;
  education?: EducationLevel[];
  excludeStatuses?: CandidateStatus[];
}

// Candidate recommendation
export interface CandidateRecommendation {
  candidate: CandidateListItem;
  matchScore: number;
  skillMatches: string[];
  missingSkills: string[];
  reasons: string[];
  confidence: number;
}
