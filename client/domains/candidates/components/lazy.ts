/**
 * Lazy-loaded Candidates Components
 * Lazy loading configuration for candidates domain components
 */

import React from 'react';
import { withLazyLoading, ComponentLoadingFallbacks, createRetryableLazy } from '../../shared/utils/lazyLoading';

// Lazy load main components
export const LazyCandidateDetailContent = withLazyLoading(
  createRetryableLazy(() => import('./CandidateDetailContent')),
  {
    fallback: ComponentLoadingFallbacks.page,
  }
);

export const LazyEditCandidateModal = withLazyLoading(
  createRetryableLazy(() => import('./EditCandidateModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyCandidateCard = withLazyLoading(
  createRetryableLazy(() => import('./CandidateCard')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyCandidateTable = withLazyLoading(
  createRetryableLazy(() => import('./CandidateTable')),
  {
    fallback: ComponentLoadingFallbacks.table,
  }
);

// Lazy load detail subcomponents
export const LazyCandidateHeader = withLazyLoading(
  createRetryableLazy(() => import('./detail/CandidateHeader')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyCandidateContactInfo = withLazyLoading(
  createRetryableLazy(() => import('./detail/CandidateContactInfo')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyCandidateStatusActions = withLazyLoading(
  createRetryableLazy(() => import('./detail/CandidateStatusActions')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyCandidateProfessionalInfo = withLazyLoading(
  createRetryableLazy(() => import('./detail/CandidateProfessionalInfo')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyCandidateSkills = withLazyLoading(
  createRetryableLazy(() => import('./detail/CandidateSkills')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyCandidateDocuments = withLazyLoading(
  createRetryableLazy(() => import('./detail/CandidateDocuments')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyCandidateNotes = withLazyLoading(
  createRetryableLazy(() => import('./detail/CandidateNotes')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

// Lazy load utility components
export const LazyAIScoreBadge = withLazyLoading(
  createRetryableLazy(() => import('./utils/AIScoreBadge')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyAICandidateSummary = withLazyLoading(
  createRetryableLazy(() => import('./utils/AICandidateSummary')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyCandidateActivityTimeline = withLazyLoading(
  createRetryableLazy(() => import('./utils/CandidateActivityTimeline')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyAdvancedFilters = withLazyLoading(
  createRetryableLazy(() => import('./utils/AdvancedFilters')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyBulkActionsBar = withLazyLoading(
  createRetryableLazy(() => import('./utils/BulkActionsBar')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyResumeExtractionModal = withLazyLoading(
  createRetryableLazy(() => import('./ResumeExtractionModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

// Preload critical components
export const preloadCandidateComponents = () => {
  return Promise.all([
    import('./CandidateDetailContent'),
    import('./EditCandidateModal'),
    import('./CandidateCard'),
    import('./CandidateTable'),
  ]);
};

// Component map for dynamic loading
export const candidateComponentMap = {
  CandidateDetailContent: LazyCandidateDetailContent,
  EditCandidateModal: LazyEditCandidateModal,
  CandidateCard: LazyCandidateCard,
  CandidateTable: LazyCandidateTable,
  CandidateHeader: LazyCandidateHeader,
  CandidateContactInfo: LazyCandidateContactInfo,
  CandidateStatusActions: LazyCandidateStatusActions,
  CandidateProfessionalInfo: LazyCandidateProfessionalInfo,
  CandidateSkills: LazyCandidateSkills,
  CandidateDocuments: LazyCandidateDocuments,
  CandidateNotes: LazyCandidateNotes,
  AIScoreBadge: LazyAIScoreBadge,
  AICandidateSummary: LazyAICandidateSummary,
  CandidateActivityTimeline: LazyCandidateActivityTimeline,
  AdvancedFilters: LazyAdvancedFilters,
  BulkActionsBar: LazyBulkActionsBar,
  ResumeExtractionModal: LazyResumeExtractionModal,
} as const;

export type CandidateComponentName = keyof typeof candidateComponentMap;
