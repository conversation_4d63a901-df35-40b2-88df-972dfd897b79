import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Mail,
  Phone,
  MapPin,
  Calendar,
  Star,
  ExternalLink,
  Download,
  Edit,
  MessageSquare,
  Clock,
  User,
  FileText,
  Linkedin,
  Gith<PERSON>,
  Eye,
  ChevronLeft,
  Share2,
  Heart,
  ThumbsUp,
  TrendingUp,
  Award,
  Building2,
  GraduationCap,
  Briefcase,
  DollarSign,
} from "lucide-react";
import { Candidate } from "@/domains/candidates/types";
import {
  CandidateActivityTimeline,
  generateSampleActivities,
} from "./CandidateActivityTimeline";
import { AIScoreBadge } from "./AIScoreBadge";
import { AICandidateSummary } from "./AICandidateSummary";
import {
  CandidateHeader,
  CandidateContactInfo,
  CandidateStatusActions,
  CandidateProfessionalInfo,
  CandidateSkills,
  CandidateDocuments,
  CandidateNotes,
} from "./detail";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/lib/i18n";
import { useUpdateCandidateStatus } from "@/domains/candidates/hooks";
import { useNavigate } from "react-router-dom";
import { PDFViewer } from "./PDFViewer";
import { AIAnalysisSuggestion } from "./AIAnalysisSuggestion";
import { useAIAnalysis } from "@/hooks/useAIAnalysis";

interface CandidateDetailContentProps {
  candidate: Candidate; // Use proper Candidate type
  onStatusChange?: (candidateId: string, newStatus: string) => void;
  onEdit?: (candidate: Candidate) => void;
  showBackButton?: boolean;
  isFullPage?: boolean;
}

export const CandidateDetailContent = ({
  candidate,
  onStatusChange,
  onEdit,
  showBackButton = false,
  isFullPage = false,
}: CandidateDetailContentProps) => {
  const [notes, setNotes] = useState("");
  const [rating, setRating] = useState(candidate?.rating || 0);
  const { toast } = useToast();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const updateStatusMutation = useUpdateCandidateStatus();

  // Check if AI analysis exists
  const { analysis, needsGeneration, generateAnalysis } = useAIAnalysis({
    candidateId: candidate.id,
    jobPostingId: candidate.jobId ? parseInt(candidate.jobId) : undefined,
    autoLoad: true,
  });

  if (!candidate) return null;

  // Job data might be included in the candidate response or we'll need to fetch it separately
  const job = (candidate as any).jobPosting || (candidate as any).job;

  const handleStatusChange = async (newStatus: string) => {
    try {
      await updateStatusMutation.mutateAsync({
        id: candidate.id?.toString() || candidate.id,
        status: newStatus,
        notes: `Trạng thái đã được thay đổi thành ${newStatus} từ trang chi tiết ứng viên`,
      });

      toast({
        title: "Đã cập nhật trạng thái",
        description: `Trạng thái ứng viên đã được thay đổi thành ${newStatus}`,
      });

      if (onStatusChange) {
        onStatusChange(candidate.id, newStatus as Candidate["status"]);
      }
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật trạng thái ứng viên",
        variant: "destructive",
      });
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "hired":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      case "interview":
        return "bg-blue-100 text-blue-800";
      case "offer":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ));
  };
  const containerClass = isFullPage
    ? "space-y-6 p-6 max-w-6xl mx-auto"
    : "space-y-6";

  return (
    <TooltipProvider>
      <div className={containerClass}>
        {/* Header Section */}
        <CandidateHeader
          candidate={candidate}
          showBackButton={showBackButton}
          isFullPage={isFullPage}
          onBack={handleBack}
          onEdit={onEdit}
          onShare={() => {
            // Handle share functionality
            if (navigator.share) {
              navigator.share({
                title: `${candidate.name} - Hồ sơ ứng viên`,
                text: `Xem hồ sơ của ${candidate.name} cho vị trí ${candidate.position}`,
                url: window.location.href,
              });
            }
          }}
        />

        {/* AI Score Badge */}
        <div className="flex justify-end">
          <AIScoreBadge candidate={candidate} size={isFullPage ? "md" : "sm"} />
        </div>

        <Separator />

        {/* Main Content */}
        <Tabs defaultValue="ai-summary" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="ai-summary">Tóm tắt AI</TabsTrigger>
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="activity">Hoạt động</TabsTrigger>
            <TabsTrigger value="documents">Tài liệu</TabsTrigger>
            <TabsTrigger value="feedback">Phản hồi</TabsTrigger>
          </TabsList>

          {/* AI Summary Tab */}
          <TabsContent value="ai-summary" className="space-y-6">
            {/* AI Analysis Suggestion */}
            {needsGeneration && (
              <AIAnalysisSuggestion
                candidateName={candidate.name || "ứng viên này"}
                jobPostingId={
                  candidate.jobId ? parseInt(candidate.jobId) : undefined
                }
                onGenerateAnalysis={async () => {
                  try {
                    await generateAnalysis();
                    toast({
                      title: "Đã tạo phân tích AI",
                      description:
                        "Phân tích toàn diện về ứng viên hiện đã có sẵn",
                    });
                  } catch (error: any) {
                    toast({
                      title: "Phân tích thất bại",
                      description:
                        error.message || "Không thể tạo phân tích AI",
                      variant: "destructive",
                    });
                  }
                }}
                onDismiss={() => {
                  /* Optionally handle dismissal */
                }}
              />
            )}

            {/* AI Summary */}
            <AICandidateSummary
              candidate={candidate}
              jobPostingId={
                candidate.jobId ? parseInt(candidate.jobId) : undefined
              }
            />
          </TabsContent>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Contact Information */}
              <CandidateContactInfo
                candidate={{
                  email: candidate.email,
                  phone: candidate.phone,
                  location: candidate.location,
                  appliedDate: candidate.appliedDate,
                  linkedin: candidate.linkedinUrl,
                  github: candidate.githubUrl,
                  portfolio: candidate.portfolioUrl,
                }}
              />

              {/* Status and Actions */}
              <CandidateStatusActions
                candidate={{
                  id: candidate.id,
                  status: candidate.status,
                  rating: candidate.rating,
                }}
                jobInfo={
                  job
                    ? {
                        title: job.title,
                        department: job.department,
                        location: job.location,
                        salaryRange: job.salaryRange,
                      }
                    : undefined
                }
                onStatusChange={handleStatusChange}
                onEdit={onEdit}
                disabled={updateStatusMutation.isPending}
              />
            </div>

            {/* Professional Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <CandidateProfessionalInfo
                candidate={{
                  experience: candidate.experience,
                  education: candidate.education,
                  workHistory: candidate.workHistory,
                  source: candidate.source,
                  salaryExpectation: candidate.salaryExpectation || {
                    range: candidate.salary,
                  },
                }}
              />

              <CandidateSkills
                candidate={{
                  skills: candidate.skills,
                  tags: candidate.tags,
                  linkedinUrl: candidate.linkedinUrl,
                  githubUrl: candidate.githubUrl,
                  portfolioUrl: candidate.portfolioUrl,
                  resumeUrl: candidate.resumeUrl,
                }}
              />
            </div>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Dòng thời gian hoạt động</CardTitle>
                <CardDescription>
                  Theo dõi tương tác và tiến trình của ứng viên
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CandidateActivityTimeline
                  candidateId={candidate.id}
                  activities={generateSampleActivities(candidate.id)}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <CandidateDocuments
              candidate={{
                resumeUrl: candidate.resumeUrl,
                appliedDate: candidate.appliedDate,
              }}
              documents={[
                {
                  id: "cover-letter",
                  name: "Thư xin việc.pdf",
                  type: "cover_letter",
                  uploadDate: candidate.appliedDate,
                  size: "245 KB",
                },
              ]}
              onDownload={(documentId) => {
                console.log("Đang tải tài liệu:", documentId);
              }}
              onView={(documentId) => {
                console.log("Đang xem tài liệu:", documentId);
              }}
              onUpload={() => {
                console.log("Tải lên tài liệu mới");
              }}
            />
          </TabsContent>

          {/* Feedback Tab */}
          <TabsContent value="feedback" className="space-y-4">
            <CandidateNotes
              notes={[
                {
                  id: "1",
                  content: "Mẫu ghi chú.",
                  author: {
                    id: "john-smith",
                    name: "Nguyễn Văn Minh",
                    initials: "NM",
                  },
                  createdAt: new Date(
                    Date.now() - 2 * 24 * 60 * 60 * 1000,
                  ).toISOString(),
                  type: "feedback",
                  rating: 4,
                },
              ]}
              onAddNote={(content) => {
                console.log("Đang thêm ghi chú:", content);
                toast({
                  title: "Đã thêm ghi chú",
                  description: "Ghi chú đã được lưu thành công",
                });
              }}
              onEditNote={(noteId, content) => {
                console.log("Đang chỉnh sửa ghi chú:", noteId, content);
                toast({
                  title: "Đã cập nhật ghi chú",
                  description: "Ghi chú đã được cập nhật thành công",
                });
              }}
              onDeleteNote={(noteId) => {
                console.log("Đang xóa ghi chú:", noteId);
                toast({
                  title: "Đã xóa ghi chú",
                  description: "Ghi chú đã được xóa thành công",
                });
              }}
              disabled={updateStatusMutation.isPending}
            />
          </TabsContent>
        </Tabs>
      </div>
    </TooltipProvider>
  );
};

export default CandidateDetailContent;
