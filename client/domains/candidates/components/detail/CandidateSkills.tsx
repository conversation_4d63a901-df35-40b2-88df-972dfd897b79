import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Target,
  ExternalLink,
  Download,
  Github,
  Linkedin,
  Globe,
} from "lucide-react";

export interface CandidateSkillsProps {
  candidate: {
    skills?: string[];
    tags?: string[];
    linkedinUrl?: string;
    githubUrl?: string;
    portfolioUrl?: string;
    resumeUrl?: string;
  };
}

export const CandidateSkills: React.FC<CandidateSkillsProps> = ({
  candidate,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="w-5 h-5" />
          Kỹ năng & Kinh nghiệm
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Technical Skills */}
        {candidate.skills && candidate.skills.length > 0 && (
          <div>
            <h4 className="font-medium mb-3"><PERSON><PERSON> năng kỹ thuật</h4>
            <div className="flex flex-wrap gap-2">
              {candidate.skills.map((skill, index) => (
                <Badge key={index} variant="secondary">
                  {skill}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Tags */}
        {candidate.tags && candidate.tags.length > 0 && (
          <div>
            <h4 className="font-medium mb-3">Nhãn</h4>
            <div className="flex flex-wrap gap-2">
              {candidate.tags.map((tag, index) => (
                <Badge key={index} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* External Links */}
        <div className="space-y-2">
          <h4 className="font-medium mb-3">Liên kết & Tài liệu</h4>

          {candidate.linkedinUrl && (
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              asChild
            >
              <a
                href={candidate.linkedinUrl}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Linkedin className="w-4 h-4 mr-2" />
                Hồ sơ LinkedIn
                <ExternalLink className="w-3 h-3 ml-auto" />
              </a>
            </Button>
          )}

          {candidate.githubUrl && (
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              asChild
            >
              <a
                href={candidate.githubUrl}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Github className="w-4 h-4 mr-2" />
                Hồ sơ GitHub
                <ExternalLink className="w-3 h-3 ml-auto" />
              </a>
            </Button>
          )}

          {candidate.portfolioUrl && (
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              asChild
            >
              <a
                href={candidate.portfolioUrl}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Globe className="w-4 h-4 mr-2" />
                Website danh mục dự án
                <ExternalLink className="w-3 h-3 ml-auto" />
              </a>
            </Button>
          )}

          {candidate.resumeUrl && (
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              asChild
            >
              <a
                href={candidate.resumeUrl}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Download className="w-4 h-4 mr-2" />
                Tải xuống CV
                <ExternalLink className="w-3 h-3 ml-auto" />
              </a>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
