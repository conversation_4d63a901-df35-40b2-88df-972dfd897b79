import React from "react";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, Share2, Edit } from "lucide-react";
import { Candidate, CandidateUtils } from "../../types";

export interface CandidateHeaderProps {
  candidate: Candidate;
  showBackButton?: boolean;
  isFullPage?: boolean;
  onBack?: () => void;
  onEdit?: (candidate: Candidate) => void;
  onShare?: () => void;
}

export const CandidateHeader: React.FC<CandidateHeaderProps> = ({
  candidate,
  showBackButton = false,
  isFullPage = false,
  onBack,
  onEdit,
  onShare,
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        {showBackButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="w-4 h-4" />
            Quay lại
          </Button>
        )}
        <div className="flex items-center gap-4">
          <Avatar className={isFullPage ? "h-16 w-16" : "h-12 w-12"}>
            <AvatarImage src={candidate.avatar} alt={candidate.name} />
            <AvatarFallback className={isFullPage ? "text-lg" : ""}>
              {candidate.name
                .split(" ")
                .map((n) => n[0])
                .join("")
                .slice(0, 2)}
            </AvatarFallback>
          </Avatar>
          <div>
            <h1
              className={`font-semibold ${isFullPage ? "text-3xl" : "text-xl"}`}
            >
              {candidate.name}
            </h1>
            <p className="text-muted-foreground">{candidate.position}</p>
            <div className="flex items-center gap-2 mt-1">
              <Badge
                className={CandidateUtils.getStatusColor(candidate.status)}
              >
                {candidate.status}
              </Badge>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {onShare && (
          <Button variant="outline" size="sm" onClick={onShare}>
            <Share2 className="w-4 h-4 mr-2" />
            Chia sẻ
          </Button>
        )}
        {onEdit && (
          <Button variant="outline" size="sm" onClick={() => onEdit(candidate)}>
            <Edit className="w-4 h-4 mr-2" />
            Chỉnh sửa
          </Button>
        )}
      </div>
    </div>
  );
};
