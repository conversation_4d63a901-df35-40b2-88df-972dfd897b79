import React from "react";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Share2, Edit } from "lucide-react";
import { Candidate } from "../../types";

export interface CandidateHeaderProps {
  candidate: Candidate;
  showBackButton?: boolean;
  isFullPage?: boolean;
  onBack?: () => void;
  onEdit?: (candidate: Candidate) => void;
  onShare?: () => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "hired":
      return "bg-green-100 text-green-800";
    case "rejected":
      return "bg-red-100 text-red-800";
    case "interview":
      return "bg-blue-100 text-blue-800";
    case "offer":
      return "bg-purple-100 text-purple-800";
    case "screening":
      return "bg-yellow-100 text-yellow-800";
    case "applied":
      return "bg-blue-100 text-blue-800";
    case "sourced":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const CandidateHeader: React.FC<CandidateHeaderProps> = ({
  candidate,
  showBackButton = false,
  isFullPage = false,
  onBack,
  onEdit,
  onShare,
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        {showBackButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="w-4 h-4" />
            Quay lại
          </Button>
        )}
        <div className="flex items-center gap-4">
          <Avatar className={isFullPage ? "h-16 w-16" : "h-12 w-12"}>
            <AvatarImage src={candidate.avatar} alt={candidate.name} />
            <AvatarFallback className={isFullPage ? "text-lg" : ""}>
              {candidate.name
                .split(" ")
                .map((n) => n[0])
                .join("")
                .slice(0, 2)}
            </AvatarFallback>
          </Avatar>
          <div>
            <h1
              className={`font-semibold ${isFullPage ? "text-3xl" : "text-xl"}`}
            >
              {candidate.name}
            </h1>
            <p className="text-muted-foreground">{candidate.position}</p>
            <div className="flex items-center gap-2 mt-1">
              <Badge className={getStatusColor(candidate.status)}>
                {candidate.status}
              </Badge>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {onShare && (
          <Button variant="outline" size="sm" onClick={onShare}>
            <Share2 className="w-4 h-4 mr-2" />
            Chia sẻ
          </Button>
        )}
        {onEdit && (
          <Button variant="outline" size="sm" onClick={() => onEdit(candidate)}>
            <Edit className="w-4 h-4 mr-2" />
            Chỉnh sửa
          </Button>
        )}
      </div>
    </div>
  );
};
