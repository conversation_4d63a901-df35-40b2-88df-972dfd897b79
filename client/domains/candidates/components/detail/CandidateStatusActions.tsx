import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Star,
  Mail,
  Calendar,
  Edit,
  Briefcase,
  DollarSign,
} from "lucide-react";

export interface CandidateStatusActionsProps {
  candidate: {
    id: string;
    status: string;
    rating?: number;
  };
  jobInfo?: {
    title?: string;
    department?: string;
    location?: string;
    salaryRange?: string;
  };
  onStatusChange?: (candidateId: string, newStatus: string) => void;
  onEdit?: (candidate: any) => void;
  disabled?: boolean;
}

export const CandidateStatusActions: React.FC<CandidateStatusActionsProps> = ({
  candidate,
  jobInfo,
  onStatusChange,
  onEdit,
  disabled = false,
}) => {
  const [rating, setRating] = useState(candidate.rating || 0);

  const handleStatusChange = (newStatus: string) => {
    if (onStatusChange) {
      onStatusChange(candidate.id, newStatus);
    }
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 cursor-pointer ${
          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
        onClick={() => setRating(i + 1)}
      />
    ));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Briefcase className="w-5 h-5" />
          Đơn ứng tuyển
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {jobInfo && (
          <>
            <div>
              <p className="font-medium">{jobInfo.title}</p>
              <p className="text-sm text-muted-foreground">
                {jobInfo.department} • {jobInfo.location}
              </p>
            </div>
            {jobInfo.salaryRange && (
              <div className="flex items-center gap-3">
                <DollarSign className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">{jobInfo.salaryRange}</span>
              </div>
            )}
          </>
        )}

        <div className="pt-2">
          <label className="text-sm font-medium">Cập nhật trạng thái</label>
          <Select
            value={candidate.status}
            onValueChange={handleStatusChange}
            disabled={disabled}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sourced">Đã tìm kiếm</SelectItem>
              <SelectItem value="applied">Đã ứng tuyển</SelectItem>
              <SelectItem value="screening">Sàng lọc</SelectItem>
              <SelectItem value="interview">Phỏng vấn</SelectItem>
              <SelectItem value="offer">Đề nghị</SelectItem>
              <SelectItem value="hired">Đã tuyển</SelectItem>
              <SelectItem value="rejected">Từ chối</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium">Đánh giá tổng thể</label>
          <div className="flex items-center gap-1 mt-1">
            {getRatingStars(rating)}
            <span className="ml-2 text-sm text-muted-foreground">
              {rating}/5
            </span>
          </div>
        </div>

        <div className="flex gap-2 pt-2 hidden">
          <Button size="sm" variant="outline" className="flex-1">
            <Mail className="w-4 h-4 mr-2" />
            Email
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <Calendar className="w-4 h-4 mr-2" />
            Lên lịch
          </Button>
        </div>

        {onEdit && false && (
          <Button
            size="sm"
            variant="outline"
            className="w-full"
            onClick={() => onEdit(candidate)}
          >
            <Edit className="w-4 h-4 mr-2" />
            Chỉnh sửa ứng viên
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
