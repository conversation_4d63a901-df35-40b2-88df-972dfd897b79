/**
 * Candidate Card Component
 * Displays candidate information in a card format
 */

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  Star,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  UserPlus,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Candidate, CandidateStatus } from '../types';
import { format } from 'date-fns';

// Status color mapping
const statusColors: Record<CandidateStatus, string> = {
  new: 'bg-blue-100 text-blue-800',
  screening: 'bg-yellow-100 text-yellow-800',
  interview: 'bg-purple-100 text-purple-800',
  offer: 'bg-green-100 text-green-800',
  hired: 'bg-emerald-100 text-emerald-800',
  rejected: 'bg-red-100 text-red-800',
};

export interface CandidateCardProps {
  candidate: Candidate;
  variant?: 'default' | 'compact' | 'detailed';
  showActions?: boolean;
  selectable?: boolean;
  selected?: boolean;
  onSelect?: (candidate: Candidate) => void;
  onView?: (candidate: Candidate) => void;
  onEdit?: (candidate: Candidate) => void;
  onDelete?: (candidate: Candidate) => void;
  onAddToJob?: (candidate: Candidate) => void;
  className?: string;
}

export const CandidateCard: React.FC<CandidateCardProps> = ({
  candidate,
  variant = 'default',
  showActions = true,
  selectable = false,
  selected = false,
  onSelect,
  onView,
  onEdit,
  onDelete,
  onAddToJob,
  className,
}) => {
  const handleCardClick = () => {
    if (selectable && onSelect) {
      onSelect(candidate);
    } else if (onView) {
      onView(candidate);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2);
  };

  const formatSalary = (salary?: number) => {
    if (!salary) return null;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(salary);
  };

  const formatExperience = (years?: number) => {
    if (!years) return 'No experience';
    return years === 1 ? '1 year' : `${years} years`;
  };

  if (variant === 'compact') {
    return (
      <Card
        className={cn(
          'cursor-pointer transition-all hover:shadow-md',
          selected && 'ring-2 ring-primary',
          className
        )}
        onClick={handleCardClick}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={candidate.avatar} />
                <AvatarFallback>{getInitials(candidate.name)}</AvatarFallback>
              </Avatar>
              
              <div>
                <h3 className="font-medium text-sm">{candidate.name}</h3>
                <p className="text-xs text-muted-foreground">{candidate.position}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge className={cn('text-xs', statusColors[candidate.status])}>
                {candidate.status}
              </Badge>
              
              {showActions && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onView && (
                      <DropdownMenuItem onClick={() => onView(candidate)}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </DropdownMenuItem>
                    )}
                    {onEdit && (
                      <DropdownMenuItem onClick={() => onEdit(candidate)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                    )}
                    {onAddToJob && (
                      <DropdownMenuItem onClick={() => onAddToJob(candidate)}>
                        <UserPlus className="h-4 w-4 mr-2" />
                        Add to Job
                      </DropdownMenuItem>
                    )}
                    {onDelete && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => onDelete(candidate)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={cn(
        'cursor-pointer transition-all hover:shadow-md',
        selected && 'ring-2 ring-primary',
        className
      )}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={candidate.avatar} />
              <AvatarFallback>{getInitials(candidate.name)}</AvatarFallback>
            </Avatar>
            
            <div>
              <h3 className="font-semibold text-lg">{candidate.name}</h3>
              {candidate.position && (
                <p className="text-muted-foreground">{candidate.position}</p>
              )}
              {candidate.currentCompany && (
                <p className="text-sm text-muted-foreground">
                  at {candidate.currentCompany}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge className={statusColors[candidate.status]}>
              {candidate.status}
            </Badge>
            
            {candidate.aiScore && (
              <div className="flex items-center space-x-1">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">{candidate.aiScore}</span>
              </div>
            )}
            
            {showActions && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onView && (
                    <DropdownMenuItem onClick={() => onView(candidate)}>
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                  )}
                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(candidate)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                  )}
                  {onAddToJob && (
                    <DropdownMenuItem onClick={() => onAddToJob(candidate)}>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Add to Job
                    </DropdownMenuItem>
                  )}
                  {onDelete && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => onDelete(candidate)}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          <div className="flex items-center space-x-2 text-muted-foreground">
            <Mail className="h-4 w-4" />
            <span>{candidate.email}</span>
          </div>
          
          {candidate.phone && (
            <div className="flex items-center space-x-2 text-muted-foreground">
              <Phone className="h-4 w-4" />
              <span>{candidate.phone}</span>
            </div>
          )}
          
          {candidate.location && (
            <div className="flex items-center space-x-2 text-muted-foreground">
              <MapPin className="h-4 w-4" />
              <span>{candidate.location}</span>
            </div>
          )}
          
          {candidate.appliedAt && (
            <div className="flex items-center space-x-2 text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>Applied {format(new Date(candidate.appliedAt), 'MMM d, yyyy')}</span>
            </div>
          )}
        </div>
        
        {/* Professional Information */}
        {variant === 'detailed' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            {candidate.experience !== undefined && (
              <div>
                <span className="font-medium">Experience: </span>
                <span className="text-muted-foreground">
                  {formatExperience(candidate.experience)}
                </span>
              </div>
            )}
            
            {candidate.expectedSalary && (
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {formatSalary(candidate.expectedSalary)} expected
                </span>
              </div>
            )}
          </div>
        )}
        
        {/* Skills */}
        {candidate.skills && candidate.skills.length > 0 && (
          <div>
            <div className="flex flex-wrap gap-1">
              {candidate.skills.slice(0, 5).map((skill) => (
                <Badge key={skill} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {candidate.skills.length > 5 && (
                <Badge variant="outline" className="text-xs">
                  +{candidate.skills.length - 5} more
                </Badge>
              )}
            </div>
          </div>
        )}
        
        {/* Tags */}
        {candidate.tags && candidate.tags.length > 0 && (
          <div>
            <div className="flex flex-wrap gap-1">
              {candidate.tags.map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
