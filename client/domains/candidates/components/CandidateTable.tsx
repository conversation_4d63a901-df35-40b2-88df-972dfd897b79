/**
 * Candidate Table Component
 * Advanced table for displaying and managing candidates
 */

import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable, columnHelpers, commonActions } from '@/shared/components/data-display';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Star,
  UserPlus,
  MessageSquare,
} from 'lucide-react';
import { Candidate, CandidateStatus } from '../types';
import { format } from 'date-fns';

// Status color mapping
const statusVariants = {
  new: 'default' as const,
  screening: 'secondary' as const,
  interview: 'outline' as const,
  offer: 'default' as const,
  hired: 'default' as const,
  rejected: 'destructive' as const,
};

export interface CandidateTableProps {
  candidates: Candidate[];
  loading?: boolean;
  selectedIds?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
  onView?: (candidate: Candidate) => void;
  onEdit?: (candidate: Candidate) => void;
  onDelete?: (candidate: Candidate) => void;
  onStatusChange?: (candidate: Candidate, status: CandidateStatus) => void;
  onAddToJob?: (candidate: Candidate) => void;
  onSendMessage?: (candidate: Candidate) => void;
  enableSelection?: boolean;
  enableActions?: boolean;
  className?: string;
}

export const CandidateTable: React.FC<CandidateTableProps> = ({
  candidates,
  loading = false,
  selectedIds = [],
  onSelectionChange,
  onView,
  onEdit,
  onDelete,
  onStatusChange,
  onAddToJob,
  onSendMessage,
  enableSelection = false,
  enableActions = true,
  className,
}) => {
  // Helper functions
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2);
  };

  const formatSalary = (salary?: number) => {
    if (!salary) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(salary);
  };

  const formatExperience = (years?: number) => {
    if (!years) return 'No exp.';
    return years === 1 ? '1 yr' : `${years} yrs`;
  };

  // Define columns
  const columns: ColumnDef<Candidate>[] = [
    // Selection column
    ...(enableSelection ? [{
      id: 'select',
      header: ({ table }: any) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }: any) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    }] : []),

    // Candidate info column
    {
      id: 'candidate',
      header: 'Candidate',
      cell: ({ row }) => {
        const candidate = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={candidate.avatar} />
              <AvatarFallback>{getInitials(candidate.name)}</AvatarFallback>
            </Avatar>
            
            <div>
              <div className="font-medium">{candidate.name}</div>
              <div className="text-sm text-muted-foreground flex items-center space-x-1">
                <Mail className="h-3 w-3" />
                <span>{candidate.email}</span>
              </div>
              {candidate.phone && (
                <div className="text-sm text-muted-foreground flex items-center space-x-1">
                  <Phone className="h-3 w-3" />
                  <span>{candidate.phone}</span>
                </div>
              )}
            </div>
          </div>
        );
      },
      enableSorting: false,
    },

    // Position column
    columnHelpers.text('position', 'Position', {
      className: 'font-medium',
    }),

    // Status column
    columnHelpers.badge('status', 'Status', statusVariants),

    // Experience column
    {
      accessorKey: 'experience',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium hover:bg-transparent"
        >
          Experience
        </Button>
      ),
      cell: ({ getValue }) => formatExperience(getValue() as number),
    },

    // Expected salary column
    {
      accessorKey: 'expectedSalary',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium hover:bg-transparent"
        >
          Expected Salary
        </Button>
      ),
      cell: ({ getValue }) => formatSalary(getValue() as number),
    },

    // Location column
    {
      accessorKey: 'location',
      header: 'Location',
      cell: ({ getValue }) => {
        const location = getValue() as string;
        if (!location) return '-';
        return (
          <div className="flex items-center space-x-1">
            <MapPin className="h-3 w-3 text-muted-foreground" />
            <span>{location}</span>
          </div>
        );
      },
    },

    // Skills column
    {
      accessorKey: 'skills',
      header: 'Skills',
      cell: ({ getValue }) => {
        const skills = getValue() as string[];
        if (!skills || skills.length === 0) return '-';
        
        return (
          <div className="flex flex-wrap gap-1">
            {skills.slice(0, 2).map((skill) => (
              <Badge key={skill} variant="secondary" className="text-xs">
                {skill}
              </Badge>
            ))}
            {skills.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{skills.length - 2}
              </Badge>
            )}
          </div>
        );
      },
      enableSorting: false,
    },

    // AI Score column
    {
      accessorKey: 'aiScore',
      header: 'AI Score',
      cell: ({ getValue }) => {
        const score = getValue() as number;
        if (!score) return '-';
        
        return (
          <div className="flex items-center space-x-1">
            <Star className="h-4 w-4 text-yellow-500" />
            <span className="font-medium">{score}</span>
          </div>
        );
      },
    },

    // Applied date column
    columnHelpers.date('appliedAt', 'Applied', 'MMM d, yyyy'),

    // Actions column
    ...(enableActions ? [columnHelpers.actions([
      ...(onView ? [commonActions.view((row) => onView(row))] : []),
      ...(onEdit ? [commonActions.edit((row) => onEdit(row))] : []),
      ...(onAddToJob ? [{
        label: 'Add to Job',
        icon: <UserPlus className="h-4 w-4" />,
        onClick: (row: Candidate) => onAddToJob(row),
      }] : []),
      ...(onSendMessage ? [{
        label: 'Send Message',
        icon: <MessageSquare className="h-4 w-4" />,
        onClick: (row: Candidate) => onSendMessage(row),
      }] : []),
      ...(onDelete ? [commonActions.delete((row) => onDelete(row))] : []),
    ])] : []),
  ];

  // Handle selection change
  const handleSelectionChange = (selectedRows: Candidate[]) => {
    const ids = selectedRows.map(row => row.id);
    onSelectionChange?.(ids);
  };

  return (
    <DataTable
      columns={columns}
      data={candidates}
      loading={loading}
      enableSelection={enableSelection}
      enableSorting={true}
      enableFiltering={true}
      enablePagination={true}
      enableColumnVisibility={true}
      onSelectionChange={handleSelectionChange}
      searchPlaceholder="Search candidates..."
      emptyMessage="No candidates found"
      className={className}
    />
  );
};

// Candidate table with built-in hooks
export const CandidateTableWithHooks: React.FC<{
  filters?: any;
  onView?: (candidate: Candidate) => void;
  onEdit?: (candidate: Candidate) => void;
  onDelete?: (candidate: Candidate) => void;
  className?: string;
}> = ({ filters, onView, onEdit, onDelete, className }) => {
  // This would use the useCandidateTable hook
  // const tableHook = useCandidateTable(filters);
  
  // For now, return basic table
  return (
    <CandidateTable
      candidates={[]}
      loading={false}
      onView={onView}
      onEdit={onEdit}
      onDelete={onDelete}
      className={className}
    />
  );
};
