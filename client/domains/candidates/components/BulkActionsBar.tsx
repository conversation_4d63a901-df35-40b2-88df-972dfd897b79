import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Mail,
  Download,
  Archive,
  Trash2,
  UserCheck,
  UserX,
  Calendar,
  Tag,
  MoreHorizontal,
  X,
} from "lucide-react";
import { BulkCandidateOperation } from "../types";

interface BulkActionsBarProps {
  selectedCount: number;
  onClearSelection: () => void;
  onBulkAction: (action: BulkCandidateOperation["operation"]) => void;
}

export const BulkActionsBar = ({
  selectedCount,
  onClearSelection,
  onBulkAction,
}: BulkActionsBarProps) => {
  if (selectedCount === 0) return null;

  return (
    <div className="bg-primary text-primary-foreground rounded-lg p-4 mb-6 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <Badge
          variant="secondary"
          className="bg-primary-foreground text-primary"
        >
          {selectedCount} selected
        </Badge>
        <span className="text-sm">Bulk actions available</span>
      </div>

      <div className="flex items-center gap-2">
        <Button
          size="sm"
          variant="secondary"
          onClick={() => onBulkAction("add_tags")}
          className="gap-2"
        >
          <Mail className="h-4 w-4" />
          Send Email
        </Button>

        <Button
          size="sm"
          variant="secondary"
          onClick={() => onBulkAction("assign_to_job")}
          className="gap-2"
        >
          <Calendar className="h-4 w-4" />
          Assign to Job
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="sm" variant="secondary" className="gap-2">
              <MoreHorizontal className="h-4 w-4" />
              More Actions
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Status Changes</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onBulkAction("update_status")}>
              <UserCheck className="mr-2 h-4 w-4" />
              Update Status
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkAction("assign_to_job")}>
              <Calendar className="mr-2 h-4 w-4" />
              Assign to Job
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkAction("update_status")}>
              <UserX className="mr-2 h-4 w-4" />
              Update Status
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>Data Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onBulkAction("export")}>
              <Download className="mr-2 h-4 w-4" />
              Export Data
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkAction("add_tags")}>
              <Tag className="mr-2 h-4 w-4" />
              Add Tags
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkAction("remove_tags")}>
              <Archive className="mr-2 h-4 w-4" />
              Remove Tags
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onBulkAction("delete")}
              className="text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          size="sm"
          variant="secondary"
          onClick={onClearSelection}
          className="gap-2"
        >
          <X className="h-4 w-4" />
          Clear
        </Button>
      </div>
    </div>
  );
};
