/**
 * Candidate Form Modal Component
 * Modern, unified modal for creating and editing candidates using FormBuilder system
 */

import React, { useMemo } from 'react';
import { z } from 'zod';
import { User, Mail, Phone, MapPin, Briefcase } from 'lucide-react';
import { FormBuilder, FieldConfig } from '@/shared/components/forms';
import { FormModal } from '@/shared/components/modals';
import { 
  Candidate, 
  CreateCandidateData, 
  UpdateCandidateData,
  CandidateUtils
} from '../types';
import { toast } from 'sonner';

// Validation schema
const candidateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  location: z.string().optional(),
  position: z.string().min(2, 'Position is required'),
  experience: z.number().min(0, 'Experience must be positive').optional(),
  expectedSalary: z.number().min(0, 'Salary must be positive').optional(),
  skills: z.array(z.string()).optional(),
  status: z.enum(['sourced', 'applied', 'screening', 'interview', 'offer', 'hired', 'rejected']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  source: z.string().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

type CandidateFormData = z.infer<typeof candidateSchema>;

export interface CandidateFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidate?: Candidate | null;
  onSubmit: (data: CreateCandidateData | UpdateCandidateData) => Promise<void>;
  loading?: boolean;
}

export const CandidateFormModal: React.FC<CandidateFormModalProps> = ({
  isOpen,
  onClose,
  candidate,
  onSubmit,
  loading = false,
}) => {
  const isEditing = !!candidate;

  // Form field configuration
  const fieldConfig: FieldConfig<CandidateFormData>[] = useMemo(() => [
    // Basic Information Section
    {
      name: 'name',
      type: 'text',
      label: 'Full Name',
      placeholder: 'Enter candidate full name',
      required: true,
      icon: <User className="h-4 w-4" />,
      section: 'Basic Information',
    },
    {
      name: 'email',
      type: 'email',
      label: 'Email Address',
      placeholder: 'Enter email address',
      required: true,
      icon: <Mail className="h-4 w-4" />,
      section: 'Basic Information',
    },
    {
      name: 'phone',
      type: 'tel',
      label: 'Phone Number',
      placeholder: 'Enter phone number',
      icon: <Phone className="h-4 w-4" />,
      section: 'Basic Information',
    },
    {
      name: 'location',
      type: 'text',
      label: 'Location',
      placeholder: 'Enter location',
      icon: <MapPin className="h-4 w-4" />,
      section: 'Basic Information',
    },

    // Professional Information Section
    {
      name: 'position',
      type: 'text',
      label: 'Position',
      placeholder: 'Enter desired position',
      required: true,
      icon: <Briefcase className="h-4 w-4" />,
      section: 'Professional Information',
    },
    {
      name: 'experience',
      type: 'number',
      label: 'Years of Experience',
      placeholder: 'Enter years of experience',
      min: 0,
      max: 50,
      section: 'Professional Information',
    },
    {
      name: 'expectedSalary',
      type: 'number',
      label: 'Expected Salary',
      placeholder: 'Enter expected salary',
      min: 0,
      section: 'Professional Information',
    },
    {
      name: 'skills',
      type: 'tags',
      label: 'Skills',
      placeholder: 'Add skills (press Enter to add)',
      description: 'Add relevant skills and technologies',
      section: 'Professional Information',
    },

    // Status Section
    {
      name: 'status',
      type: 'select',
      label: 'Status',
      required: true,
      options: [
        { value: 'sourced', label: 'Sourced' },
        { value: 'applied', label: 'Applied' },
        { value: 'screening', label: 'Screening' },
        { value: 'interview', label: 'Interview' },
        { value: 'offer', label: 'Offer' },
        { value: 'hired', label: 'Hired' },
        { value: 'rejected', label: 'Rejected' },
      ],
      section: 'Status & Priority',
    },
    {
      name: 'priority',
      type: 'select',
      label: 'Priority',
      options: [
        { value: 'low', label: 'Low' },
        { value: 'medium', label: 'Medium' },
        { value: 'high', label: 'High' },
        { value: 'urgent', label: 'Urgent' },
      ],
      section: 'Status & Priority',
    },
    {
      name: 'source',
      type: 'select',
      label: 'Source',
      options: [
        { value: 'linkedin', label: 'LinkedIn' },
        { value: 'indeed', label: 'Indeed' },
        { value: 'glassdoor', label: 'Glassdoor' },
        { value: 'referral', label: 'Referral' },
        { value: 'direct', label: 'Direct Application' },
        { value: 'agency', label: 'Agency' },
        { value: 'career-site', label: 'Career Site' },
        { value: 'other', label: 'Other' },
      ],
      section: 'Status & Priority',
    },

    // Additional Information Section
    {
      name: 'tags',
      type: 'tags',
      label: 'Tags',
      placeholder: 'Add tags (press Enter to add)',
      description: 'Add tags for categorization',
      section: 'Additional Information',
    },
    {
      name: 'notes',
      type: 'textarea',
      label: 'Notes',
      placeholder: 'Add any additional notes about the candidate',
      rows: 4,
      section: 'Additional Information',
    },
  ], []);

  // Default values for form
  const defaultValues: Partial<CandidateFormData> = useMemo(() => {
    if (candidate) {
      return {
        name: candidate.name,
        email: candidate.email,
        phone: candidate.phone || '',
        location: candidate.location || '',
        position: candidate.position || '',
        experience: candidate.experience || 0,
        expectedSalary: candidate.expectedSalary || 0,
        skills: candidate.skills || [],
        status: candidate.status,
        priority: candidate.priority || 'medium',
        source: candidate.source || 'direct',
        notes: typeof candidate.notes === 'string' ? candidate.notes : '',
        tags: candidate.tags || [],
      };
    }
    return {
      status: 'applied',
      priority: 'medium',
      source: 'direct',
      skills: [],
      tags: [],
    };
  }, [candidate]);

  const handleSubmit = async (data: CandidateFormData) => {
    try {
      // Validate email
      if (!CandidateUtils.isValidEmail(data.email)) {
        toast.error('Please enter a valid email address');
        return;
      }

      // Validate phone if provided
      if (data.phone && !CandidateUtils.isValidPhone(data.phone)) {
        toast.error('Please enter a valid phone number');
        return;
      }

      // Prepare data for submission
      const submitData = {
        ...data,
        phone: data.phone || undefined,
        location: data.location || undefined,
        notes: data.notes || undefined,
        skills: data.skills?.length ? data.skills : undefined,
        tags: data.tags?.length ? data.tags : undefined,
      };

      await onSubmit(submitData);
      onClose();
      toast.success(
        isEditing 
          ? 'Candidate updated successfully' 
          : 'Candidate created successfully'
      );
    } catch (error) {
      console.error('Error submitting candidate:', error);
      toast.error(
        isEditing 
          ? 'Failed to update candidate' 
          : 'Failed to create candidate'
      );
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? 'Edit Candidate' : 'Add New Candidate'}
      description={
        isEditing 
          ? 'Update candidate information' 
          : 'Add a new candidate to the system'
      }
      icon={<User className="h-5 w-5" />}
      size="xl"
      loading={loading}
    >
      <FormBuilder
        schema={candidateSchema}
        fields={fieldConfig}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
        submitText={isEditing ? 'Update Candidate' : 'Create Candidate'}
        cancelText="Cancel"
        onCancel={onClose}
        loading={loading}
        sectioned={true}
        className="space-y-6"
      />
    </FormModal>
  );
};
