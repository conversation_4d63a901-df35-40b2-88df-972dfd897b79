import { useState, useCallback } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Upload,
  Download,
  FileText,
  CheckCircle,
  AlertCircle,
  X,
  Sparkles,
  FileSpreadsheet,
  Users,
} from "lucide-react";
import { Candidate } from "@/domains/candidates/types";
import { toast } from "sonner";

interface ImportCandidatesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (candidates: Omit<Candidate, "id">[]) => void;
}

interface ImportResult {
  success: number;
  errors: number;
  warnings: number;
  data: Omit<Candidate, "id">[];
  errorDetails: string[];
}

const csvTemplate = `name,email,phone,location,position,experience,skills,source,salary,linkedinUrl,githubUrl
John Doe,<EMAIL>,******-0123,San Francisco CA,Frontend Developer,5+ years,"React,TypeScript,CSS",LinkedIn,$120000-140000,https://linkedin.com/in/johndoe,https://github.com/johndoe
Jane Smith,<EMAIL>,******-0124,New York NY,Backend Engineer,3-5 years,"Node.js,Python,PostgreSQL",Indeed,$100000-120000,https://linkedin.com/in/janesmith,
Mike Johnson,<EMAIL>,******-0125,Austin TX,Product Manager,7+ years,"Product Strategy,Agile,Analytics",Referral,$130000-150000,https://linkedin.com/in/mikejohnson,`;

const sampleData = [
  {
    name: "Alice Cooper",
    email: "<EMAIL>",
    phone: "******-0126",
    location: "Seattle, WA",
    position: "UX Designer",
    experience: "4+ years",
    skills: "Figma,Sketch,User Research",
    source: "AngelList",
    salary: "$90000-110000",
  },
  {
    name: "Bob Wilson",
    email: "<EMAIL>",
    phone: "******-0127",
    location: "Boston, MA",
    position: "Data Scientist",
    experience: "3+ years",
    skills: "Python,Machine Learning,SQL",
    source: "LinkedIn",
    salary: "$110000-130000",
  },
];

export const ImportCandidatesModal = ({
  isOpen,
  onClose,
  onImport,
}: ImportCandidatesModalProps) => {
  const [currentTab, setCurrentTab] = useState("upload");
  const [isDragging, setIsDragging] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [useAI, setUseAI] = useState(true);

  const handleClose = () => {
    setUploadedFile(null);
    setImportResult(null);
    setProgress(0);
    setIsProcessing(false);
    setCurrentTab("upload");
    onClose();
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const files = Array.from(e.dataTransfer.files);
    const csvFile = files.find(
      (file) => file.type.includes("csv") || file.name.endsWith(".csv"),
    );
    if (csvFile) {
      setUploadedFile(csvFile);
    } else {
      toast.error("Please upload a CSV file");
    }
  }, []);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const downloadTemplate = () => {
    const blob = new Blob([csvTemplate], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "candidate_template.csv";
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Template downloaded successfully!");
  };

  const processFile = async () => {
    if (!uploadedFile) return;

    setIsProcessing(true);
    setProgress(0);
    setCurrentTab("processing");

    try {
      // Simulate file processing with progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise((resolve) => setTimeout(resolve, 200));
        setProgress(i);
      }

      // Simulate parsing CSV and creating candidates
      const mockResults: ImportResult = {
        success: 8,
        errors: 1,
        warnings: 2,
        data: sampleData.map((item) => ({
          ...item,
          initials: item.name
            .split(" ")
            .map((n) => n[0])
            .join("")
            .toUpperCase(),
          status: "sourced" as const,
          appliedDate: new Date().toISOString().split("T")[0],
          skills: item.skills.split(","),
          jobId: "1", // Default job
        })),
        errorDetails: [
          "Row 5: Missing required email address",
          "Row 7: Invalid phone number format",
        ],
      };

      setImportResult(mockResults);
      setCurrentTab("results");
      toast.success(
        `Successfully processed ${mockResults.success} candidates!`,
      );
    } catch (error) {
      toast.error("Failed to process file");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImport = () => {
    if (importResult && importResult.data.length > 0) {
      onImport(importResult.data);
      toast.success(`Imported ${importResult.success} candidates!`);
      handleClose();
    }
  };

  const importSampleData = () => {
    const mockResults: ImportResult = {
      success: 2,
      errors: 0,
      warnings: 0,
      data: sampleData.map((item) => ({
        ...item,
        initials: item.name
          .split(" ")
          .map((n) => n[0])
          .join("")
          .toUpperCase(),
        status: "sourced" as const,
        appliedDate: new Date().toISOString().split("T")[0],
        skills: item.skills.split(","),
        jobId: "1",
      })),
      errorDetails: [],
    };

    setImportResult(mockResults);
    setCurrentTab("results");
    toast.success("Sample data loaded successfully!");
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Upload className="w-5 h-5 text-primary" />
            </div>
            Import Candidates
          </DialogTitle>
          <DialogDescription>
            Bulk import candidates from CSV files with AI-powered data
            enhancement and validation.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={currentTab} onValueChange={setCurrentTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload">Upload File</TabsTrigger>
            <TabsTrigger value="processing" disabled={!isProcessing}>
              Processing
            </TabsTrigger>
            <TabsTrigger value="results" disabled={!importResult}>
              Results
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6">
            {/* AI Enhancement Toggle */}
            <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-emerald-500/5">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Sparkles className="w-5 h-5 text-primary" />
                    <div>
                      <h4 className="font-semibold">AI Data Enhancement</h4>
                      <p className="text-sm text-muted-foreground">
                        Automatically enrich candidate profiles and validate
                        data
                      </p>
                    </div>
                  </div>
                  <Badge
                    variant="secondary"
                    className="bg-primary/10 text-primary"
                  >
                    Recommended
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* File Upload Area */}
            <Card
              className={`border-dashed border-2 transition-colors ${
                isDragging
                  ? "border-primary bg-primary/5"
                  : "border-muted-foreground/25 hover:border-primary/50"
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <CardContent className="p-8">
                <div className="text-center space-y-4">
                  <div className="flex justify-center">
                    {uploadedFile ? (
                      <CheckCircle className="w-16 h-16 text-primary" />
                    ) : (
                      <FileSpreadsheet className="w-16 h-16 text-muted-foreground" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">
                      {uploadedFile ? "File Ready" : "Upload CSV File"}
                    </h3>
                    <p className="text-muted-foreground">
                      {uploadedFile
                        ? `${uploadedFile.name} (${(uploadedFile.size / 1024).toFixed(1)} KB)`
                        : "Drag and drop your CSV file here or click to browse"}
                    </p>
                  </div>
                  <div className="space-y-3">
                    {!uploadedFile && (
                      <>
                        <input
                          type="file"
                          accept=".csv"
                          onChange={handleFileSelect}
                          className="hidden"
                          id="csv-upload"
                        />
                        <label htmlFor="csv-upload">
                          <Button
                            type="button"
                            variant="outline"
                            className="cursor-pointer rounded-xl"
                            asChild
                          >
                            <span>Choose CSV File</span>
                          </Button>
                        </label>
                      </>
                    )}
                    {uploadedFile && (
                      <div className="flex gap-2 justify-center">
                        <Button
                          onClick={() => setUploadedFile(null)}
                          variant="outline"
                          size="sm"
                          className="rounded-xl"
                        >
                          <X className="w-4 h-4 mr-2" />
                          Remove
                        </Button>
                        <Button
                          onClick={processFile}
                          className="ai-button"
                          size="sm"
                        >
                          <Sparkles className="w-4 h-4 mr-2" />
                          Process File
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Template and Sample Data */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-base">
                    <Download className="w-4 h-4" />
                    CSV Template
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Download the CSV template with all required and optional
                    fields.
                  </p>
                  <Button
                    onClick={downloadTemplate}
                    variant="outline"
                    size="sm"
                    className="w-full rounded-xl"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Template
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-base">
                    <Users className="w-4 h-4" />
                    Sample Data
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Try the import feature with pre-loaded sample candidate
                    data.
                  </p>
                  <Button
                    onClick={importSampleData}
                    variant="outline"
                    size="sm"
                    className="w-full rounded-xl"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Load Sample Data
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Required Fields Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">
                  Required CSV Columns
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {["name", "email", "position", "experience"].map((field) => (
                    <Badge
                      key={field}
                      variant="secondary"
                      className="justify-center"
                    >
                      {field}
                    </Badge>
                  ))}
                </div>
                <p className="text-sm text-muted-foreground mt-3">
                  Optional fields: phone, location, skills, source, salary,
                  linkedinUrl, githubUrl
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="processing" className="space-y-6">
            <Card>
              <CardContent className="p-8">
                <div className="text-center space-y-6">
                  <div className="flex justify-center">
                    <div className="relative">
                      <Sparkles className="w-16 h-16 text-primary animate-pulse" />
                      <div className="absolute inset-0 animate-ping">
                        <Sparkles className="w-16 h-16 text-primary/50" />
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Processing File</h3>
                    <p className="text-muted-foreground">
                      AI is analyzing and enhancing your candidate data...
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Progress value={progress} className="w-full" />
                    <p className="text-sm text-muted-foreground">
                      {progress}% complete
                    </p>
                  </div>
                  <div className="text-left space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-primary" />
                      Parsing CSV data
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-primary" />
                      Validating email addresses
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Sparkles className="w-4 h-4 text-primary animate-pulse" />
                      Enriching profiles with AI
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="results" className="space-y-6">
            {importResult && (
              <>
                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-primary/20">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-8 h-8 text-primary" />
                        <div>
                          <p className="text-2xl font-bold text-primary">
                            {importResult.success}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Successful
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-yellow-500/20">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <AlertCircle className="w-8 h-8 text-yellow-500" />
                        <div>
                          <p className="text-2xl font-bold text-yellow-500">
                            {importResult.warnings}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Warnings
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-destructive/20">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <X className="w-8 h-8 text-destructive" />
                        <div>
                          <p className="text-2xl font-bold text-destructive">
                            {importResult.errors}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Errors
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Error Details */}
                {importResult.errors > 0 && (
                  <Card className="border-destructive/20">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-destructive">
                        <AlertCircle className="w-5 h-5" />
                        Import Errors
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-1">
                        {importResult.errorDetails.map((error, index) => (
                          <li key={index} className="text-sm text-destructive">
                            • {error}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}

                {/* Successfully Imported Candidates Preview */}
                <Card>
                  <CardHeader>
                    <CardTitle>Import Preview</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Preview of candidates that will be imported
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {importResult.data.slice(0, 5).map((candidate, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div>
                            <p className="font-medium">{candidate.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {candidate.email} • {candidate.position}
                            </p>
                          </div>
                          <CheckCircle className="w-5 h-5 text-primary" />
                        </div>
                      ))}
                      {importResult.data.length > 5 && (
                        <p className="text-sm text-muted-foreground text-center">
                          ... and {importResult.data.length - 5} more candidates
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex gap-3 pt-6 border-t">
          <Button
            variant="outline"
            onClick={handleClose}
            className="flex-1 rounded-xl"
          >
            Cancel
          </Button>
          {currentTab === "results" && importResult && (
            <Button
              onClick={handleImport}
              className="flex-1 ai-button"
              disabled={importResult.success === 0}
            >
              Import {importResult.success} Candidates
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
