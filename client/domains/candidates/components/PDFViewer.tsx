import { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  FileText,
  Maximize2,
  Download,
  X,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Printer,
} from "lucide-react";
import { useTranslation } from "@/lib/i18n";

interface PDFViewerProps {
  fileName: string;
  fileUrl?: string;
  uploadDate?: string;
  onDownload?: () => void;
}

export const PDFViewer = ({
  fileName,
  fileUrl = "/sample-resume.pdf",
  uploadDate,
  onDownload,
}: PDFViewerProps) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [zoom, setZoom] = useState(100);
  const { t } = useTranslation();

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 25, 50));
  };

  const PDFContent = ({ isInModal = false }) => (
    <div className="space-y-4">
      {/* PDF Controls */}
      <div className="flex items-center justify-between p-2 bg-muted/30 rounded-lg">
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleZoomOut}
            disabled={zoom <= 50}
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          <span className="text-sm font-medium min-w-[60px] text-center">
            {zoom}%
          </span>
          <Button
            size="sm"
            variant="outline"
            onClick={handleZoomIn}
            disabled={zoom >= 200}
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <RotateCw className="h-4 w-4 mr-1" />
            {t.common?.rotate || "Rotate"}
          </Button>
          <Button size="sm" variant="outline">
            <Printer className="h-4 w-4 mr-1" />
            {t.common?.print || "Print"}
          </Button>
          {onDownload && (
            <Button size="sm" variant="outline" onClick={onDownload}>
              <Download className="h-4 w-4 mr-1" />
              {t.common?.download || "Download"}
            </Button>
          )}
          {!isInModal && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsFullscreen(true)}
            >
              <Maximize2 className="h-4 w-4 mr-1" />
              {t.common?.fullscreen || "Fullscreen"}
            </Button>
          )}
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="border rounded-lg overflow-hidden bg-white">
        <div
          className="overflow-auto"
          style={{
            height: isInModal ? "70vh" : "400px",
            transform: `scale(${zoom / 100})`,
            transformOrigin: "top left",
            width: `${10000 / zoom}%`,
          }}
        >
          <iframe
            src={`${fileUrl}#toolbar=0&navpanes=0&scrollbar=0`}
            className="w-full h-full border-0"
            title={fileName}
            style={{ minHeight: "600px" }}
          />
        </div>
      </div>

      {/* File Info */}
      <div className="flex items-center justify-between text-sm text-muted-foreground p-2 bg-muted/20 rounded">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          <span>{fileName}</span>
        </div>
        {uploadDate && (
          <span>
            {t.candidates?.uploaded || "Uploaded"}:{" "}
            {new Date(uploadDate).toLocaleDateString()}
          </span>
        )}
      </div>
    </div>
  );

  return (
    <>
      <PDFContent />

      {/* Fullscreen Modal */}
      <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
        <DialogContent className="max-w-7xl max-h-[95vh] p-6">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {fileName}
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setIsFullscreen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <PDFContent isInModal={true} />
        </DialogContent>
      </Dialog>
    </>
  );
};
