import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Clock,
  Star,
  MessageSquare,
  Calendar,
  Phone,
  Video,
  MapPin,
  User,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
  ThumbsUp,
  ThumbsDown,
  Target,
  FileText,
  Mail,
  UserPlus,
  UserCheck,
  UserX,
  Briefcase,
  Plus,
  ArrowRight,
} from "lucide-react";
import { formatDistanceToNow, format } from "date-fns";
import {
  FeedbackActivity,
  getRatingColor,
  getRatingLabel,
} from "@/lib/types/interviewFeedback";
import { CandidateActivity } from "../types";

// Legacy activity item interface for backward compatibility
export interface ActivityItem {
  id: string;
  type:
    | "interview_scheduled"
    | "interview_completed"
    | "interview_cancelled"
    | "feedback_created"
    | "feedback_updated"
    | "feedback_deleted"
    | "status_change"
    | "note_added"
    | "email_sent"
    | "call_made"
    | "application_received"
    | "offer_sent"
    | "offer_accepted"
    | "offer_declined";
  title: string;
  description: string;
  timestamp: string;
  user: {
    id: number;
    name: string;
    avatar?: string;
    initials: string;
  };
  metadata?: any;
}

interface CandidateActivityTimelineProps {
  candidateId: string;
  activities?: ActivityItem[];
  onViewFeedback?: (feedbackId: number) => void;
  onEditFeedback?: (feedbackId: number) => void;
  onDeleteFeedback?: (feedbackId: number) => void;
  className?: string;
}

export const CandidateActivityTimeline: React.FC<
  CandidateActivityTimelineProps
> = ({
  candidateId,
  activities = [],
  onViewFeedback,
  onEditFeedback,
  onDeleteFeedback,
  className = "",
}) => {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "interview_scheduled":
        return <Calendar className="w-4 h-4 text-blue-600" />;
      case "interview_completed":
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case "interview_cancelled":
        return <XCircle className="w-4 h-4 text-red-600" />;
      case "feedback_created":
        return <Star className="w-4 h-4 text-yellow-600" />;
      case "feedback_updated":
        return <Edit className="w-4 h-4 text-blue-600" />;
      case "feedback_deleted":
        return <Trash2 className="w-4 h-4 text-red-600" />;
      case "status_change":
        return <ArrowRight className="w-4 h-4 text-purple-600" />;
      case "note_added":
        return <MessageSquare className="w-4 h-4 text-gray-600" />;
      case "email_sent":
        return <Mail className="w-4 h-4 text-blue-600" />;
      case "call_made":
        return <Phone className="w-4 h-4 text-green-600" />;
      case "application_received":
        return <UserPlus className="w-4 h-4 text-blue-600" />;
      case "offer_sent":
        return <FileText className="w-4 h-4 text-orange-600" />;
      case "offer_accepted":
        return <UserCheck className="w-4 h-4 text-green-600" />;
      case "offer_declined":
        return <UserX className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case "interview_completed":
      case "feedback_created":
      case "offer_accepted":
        return "border-green-200 bg-green-50";
      case "interview_cancelled":
      case "feedback_deleted":
      case "offer_declined":
        return "border-red-200 bg-red-50";
      case "feedback_updated":
      case "interview_scheduled":
      case "email_sent":
        return "border-blue-200 bg-blue-50";
      case "status_change":
        return "border-purple-200 bg-purple-50";
      case "offer_sent":
        return "border-orange-200 bg-orange-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  const FeedbackActivityDetails: React.FC<{
    activity: ActivityItem & {
      type: "feedback_created" | "feedback_updated" | "feedback_deleted";
    };
  }> = ({ activity }) => {
    const { metadata } = activity;

    return (
      <div className="mt-2 space-y-2">
        {metadata?.rating && (
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Rating:</span>
            <div className="flex items-center gap-1">
              {Array.from({ length: 5 }, (_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    i < metadata.rating
                      ? "text-yellow-400 fill-current"
                      : "text-gray-300"
                  }`}
                />
              ))}
              <span className={`text-xs ${getRatingColor(metadata.rating)}`}>
                {getRatingLabel(metadata.rating)}
              </span>
            </div>
          </div>
        )}

        {metadata?.recommend !== undefined && (
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              Recommendation:
            </span>
            {metadata.recommend ? (
              <Badge variant="default" className="text-xs bg-green-600">
                <ThumbsUp className="w-2 h-2 mr-1" />
                Recommended
              </Badge>
            ) : (
              <Badge variant="destructive" className="text-xs">
                <ThumbsDown className="w-2 h-2 mr-1" />
                Not Recommended
              </Badge>
            )}
          </div>
        )}

        {metadata?.changes && metadata.changes.length > 0 && (
          <div className="space-y-1">
            <span className="text-xs text-muted-foreground">Changes:</span>
            <ul className="text-xs space-y-0.5">
              {metadata.changes.map((change: string, index: number) => (
                <li key={index} className="flex items-center gap-1">
                  <div className="w-1 h-1 bg-blue-400 rounded-full" />
                  {change}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className="flex items-center gap-2 pt-2">
          {onViewFeedback && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewFeedback(metadata?.feedback_id)}
              className="h-6 px-2 text-xs"
            >
              <Eye className="w-3 h-3 mr-1" />
              View
            </Button>
          )}
          {onEditFeedback && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEditFeedback(metadata?.feedback_id)}
              className="h-6 px-2 text-xs"
            >
              <Edit className="w-3 h-3 mr-1" />
              Edit
            </Button>
          )}
          {onDeleteFeedback && activity.type !== "feedback_deleted" && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteFeedback(metadata?.feedback_id)}
              className="h-6 px-2 text-xs text-destructive hover:text-destructive"
            >
              <Trash2 className="w-3 h-3 mr-1" />
              Delete
            </Button>
          )}
        </div>
      </div>
    );
  };

  const InterviewActivityDetails: React.FC<{
    activity: ActivityItem & {
      type:
        | "interview_scheduled"
        | "interview_completed"
        | "interview_cancelled";
    };
  }> = ({ activity }) => {
    const { metadata } = activity;

    return (
      <div className="mt-2 space-y-2">
        {metadata?.interview_type && (
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Type:</span>
            <Badge variant="outline" className="text-xs">
              {metadata.interview_type.replace("-", " ")}
            </Badge>
          </div>
        )}

        {metadata?.interview_format && (
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Format:</span>
            <div className="flex items-center gap-1">
              {metadata.interview_format === "video" && (
                <Video className="w-3 h-3" />
              )}
              {metadata.interview_format === "phone" && (
                <Phone className="w-3 h-3" />
              )}
              {metadata.interview_format === "in-person" && (
                <MapPin className="w-3 h-3" />
              )}
              <span className="text-xs">{metadata.interview_format}</span>
            </div>
          </div>
        )}

        {metadata?.interviewer_name && (
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Interviewer:</span>
            <span className="text-xs font-medium">
              {metadata.interviewer_name}
            </span>
          </div>
        )}

        {metadata?.scheduled_time && (
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Scheduled:</span>
            <span className="text-xs">
              {format(new Date(metadata.scheduled_time), "PPp")}
            </span>
          </div>
        )}
      </div>
    );
  };

  const StatusChangeDetails: React.FC<{
    activity: ActivityItem & { type: "status_change" };
  }> = ({ activity }) => {
    const { metadata } = activity;

    return (
      <div className="mt-2">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {metadata?.from_status}
          </Badge>
          <ArrowRight className="w-3 h-3 text-muted-foreground" />
          <Badge variant="default" className="text-xs">
            {metadata?.to_status}
          </Badge>
        </div>
        {metadata?.reason && (
          <p className="text-xs text-muted-foreground mt-1">
            Reason: {metadata.reason}
          </p>
        )}
      </div>
    );
  };

  const renderActivityDetails = (activity: ActivityItem) => {
    switch (activity.type) {
      case "feedback_created":
      case "feedback_updated":
        return (
          <FeedbackActivityDetails
            activity={
              activity as ActivityItem & {
                type: "feedback_created" | "feedback_updated";
              }
            }
          />
        );
      case "interview_scheduled":
      case "interview_completed":
      case "interview_cancelled":
        return (
          <InterviewActivityDetails
            activity={
              activity as ActivityItem & {
                type:
                  | "interview_scheduled"
                  | "interview_completed"
                  | "interview_cancelled";
              }
            }
          />
        );
      case "status_change":
        return (
          <StatusChangeDetails
            activity={activity as ActivityItem & { type: "status_change" }}
          />
        );
      default:
        return null;
    }
  };

  if (activities.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Activity Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No activities yet</p>
            <p className="text-sm text-muted-foreground">
              Activities will appear here as they happen
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Activity Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {activities.map((activity, index) => (
              <div key={activity.id} className="relative">
                {/* Timeline line */}
                {index < activities.length - 1 && (
                  <div className="absolute left-6 top-8 w-0.5 h-12 bg-border" />
                )}

                <div
                  className={`flex gap-3 p-3 rounded-lg border ${getActivityColor(
                    activity.type,
                  )}`}
                >
                  {/* Activity Icon */}
                  <div className="flex-shrink-0 w-8 h-8 bg-white border rounded-full flex items-center justify-center">
                    {getActivityIcon(activity.type)}
                  </div>

                  {/* Activity Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-foreground">
                          {activity.title}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {activity.description}
                        </p>
                        {renderActivityDetails(activity)}
                      </div>

                      {/* User avatar and timestamp */}
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Tooltip>
                          <TooltipTrigger>
                            <Avatar className="w-6 h-6">
                              <AvatarImage src={activity.user.avatar} />
                              <AvatarFallback className="text-xs">
                                {activity.user.initials}
                              </AvatarFallback>
                            </Avatar>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{activity.user.name}</p>
                          </TooltipContent>
                        </Tooltip>
                        <span>
                          {formatDistanceToNow(new Date(activity.timestamp), {
                            addSuffix: true,
                          })}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};

// Sample data generator for testing
export const generateSampleActivities = (
  candidateId: string,
): ActivityItem[] => {
  return [
    {
      id: "1",
      type: "application_received",
      title: "Application Received",
      description:
        "Candidate submitted application for Senior Developer position",
      timestamp: "2024-02-15T10:00:00Z",
      user: {
        id: 1,
        name: "System",
        initials: "SY",
      },
    },
    {
      id: "2",
      type: "interview_scheduled",
      title: "Phone Screening Scheduled",
      description: "Initial phone screening scheduled",
      timestamp: "2024-02-16T14:30:00Z",
      user: {
        id: 2,
        name: "Sarah Johnson",
        initials: "SJ",
      },
      metadata: {
        interview_type: "screening",
        interview_format: "phone",
        interviewer_name: "Sarah Johnson",
        scheduled_time: "2024-02-18T10:00:00Z",
      },
    },
    {
      id: "3",
      type: "interview_completed",
      title: "Phone Screening Completed",
      description: "Completed phone screening with positive outcome",
      timestamp: "2024-02-18T10:30:00Z",
      user: {
        id: 2,
        name: "Sarah Johnson",
        initials: "SJ",
      },
      metadata: {
        interview_type: "screening",
        interview_format: "phone",
        interviewer_name: "Sarah Johnson",
      },
    },
    {
      id: "4",
      type: "feedback_created",
      title: "Interview Feedback Submitted",
      description: "Feedback provided for phone screening interview",
      timestamp: "2024-02-18T11:00:00Z",
      user: {
        id: 2,
        name: "Sarah Johnson",
        initials: "SJ",
      },
      metadata: {
        feedback_id: 1,
        interview_id: 1,
        rating: 4,
        recommend: true,
      },
    },
    {
      id: "5",
      type: "status_change",
      title: "Status Updated",
      description: "Candidate moved to technical interview stage",
      timestamp: "2024-02-19T09:00:00Z",
      user: {
        id: 3,
        name: "John Smith",
        initials: "JS",
      },
      metadata: {
        from_status: "screening",
        to_status: "interview",
        reason: "Passed phone screening",
      },
    },
    {
      id: "6",
      type: "interview_scheduled",
      title: "Technical Interview Scheduled",
      description: "Technical interview with engineering team",
      timestamp: "2024-02-19T15:00:00Z",
      user: {
        id: 3,
        name: "John Smith",
        initials: "JS",
      },
      metadata: {
        interview_type: "technical",
        interview_format: "video",
        interviewer_name: "John Smith",
        scheduled_time: "2024-02-22T14:00:00Z",
      },
    },
  ];
};

export default CandidateActivityTimeline;
