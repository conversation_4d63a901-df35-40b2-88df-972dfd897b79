/**
 * Calendar Domain Types
 * Core types and interfaces for the calendar domain
 */

import { BaseEntity } from '@/core/api';
import { Interview } from '@/domains/interviews/types';

// Calendar event types
export type CalendarEventType = 
  | 'interview'
  | 'meeting'
  | 'deadline'
  | 'reminder'
  | 'holiday'
  | 'personal';

// Calendar event status
export type CalendarEventStatus = 
  | 'scheduled'
  | 'confirmed'
  | 'cancelled'
  | 'completed'
  | 'rescheduled'
  | 'no-show';

// Calendar view types
export type CalendarViewType = 
  | 'month'
  | 'week'
  | 'day'
  | 'agenda'
  | 'list';

// Calendar event priority
export type CalendarEventPriority = 
  | 'low'
  | 'medium'
  | 'high'
  | 'urgent';

// Core Calendar Event interface
export interface CalendarEvent extends BaseEntity {
  // Basic Information
  title: string;
  description?: string;
  type: CalendarEventType;
  status: CalendarEventStatus;
  priority: CalendarEventPriority;
  
  // Timing
  startDate: string; // ISO string
  endDate: string; // ISO string
  allDay: boolean;
  timezone: string;
  
  // Location
  location?: string;
  isVirtual: boolean;
  meetingUrl?: string;
  
  // Participants
  organizerId?: string;
  organizerName?: string;
  organizerEmail?: string;
  attendees: CalendarAttendee[];
  
  // Related entities
  interviewId?: string;
  candidateId?: string;
  jobId?: string;
  
  // Metadata
  color?: string;
  tags: string[];
  isRecurring: boolean;
  recurringRule?: RecurringRule;
  
  // Reminders
  reminders: CalendarReminder[];
  
  // External integration
  externalId?: string;
  externalSource?: 'google' | 'outlook' | 'apple';
  syncStatus?: 'synced' | 'pending' | 'failed';
}

// Calendar attendee
export interface CalendarAttendee {
  id?: string;
  name: string;
  email: string;
  role: 'organizer' | 'required' | 'optional';
  status: 'pending' | 'accepted' | 'declined' | 'tentative';
  responseDate?: string;
}

// Recurring rule
export interface RecurringRule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number; // Every N days/weeks/months/years
  endDate?: string;
  count?: number; // Number of occurrences
  daysOfWeek?: number[]; // 0-6, Sunday = 0
  dayOfMonth?: number;
  monthOfYear?: number;
}

// Calendar reminder
export interface CalendarReminder {
  id: string;
  type: 'email' | 'notification' | 'sms';
  minutesBefore: number;
  sent: boolean;
  sentAt?: string;
}

// Calendar filters
export interface CalendarFilters {
  startDate?: string;
  endDate?: string;
  types?: CalendarEventType[];
  statuses?: CalendarEventStatus[];
  priorities?: CalendarEventPriority[];
  interviewerId?: string;
  candidateId?: string;
  jobId?: string;
  tags?: string[];
  search?: string;
}

// Calendar statistics
export interface CalendarStatistics {
  totalEvents: number;
  eventsByType: Record<CalendarEventType, number>;
  eventsByStatus: Record<CalendarEventStatus, number>;
  eventsByPriority: Record<CalendarEventPriority, number>;
  upcomingEvents: number;
  overdueEvents: number;
  todayEvents: number;
  thisWeekEvents: number;
  thisMonthEvents: number;
}

// Calendar view configuration
export interface CalendarViewConfig {
  type: CalendarViewType;
  startDate: string;
  endDate: string;
  timezone: string;
  workingHours: {
    start: string; // HH:mm format
    end: string; // HH:mm format
  };
  workingDays: number[]; // 0-6, Sunday = 0
  showWeekends: boolean;
  showAllDay: boolean;
  defaultEventDuration: number; // minutes
}

// Calendar export options
export interface CalendarExportOptions {
  format: 'ics' | 'csv' | 'json' | 'pdf';
  startDate: string;
  endDate: string;
  filters?: CalendarFilters;
  includeDetails: boolean;
  includeAttendees: boolean;
  includeReminders: boolean;
}

// Calendar import result
export interface CalendarImportResult {
  imported: number;
  failed: number;
  duplicates: number;
  errors: string[];
  warnings: string[];
}

// Calendar availability
export interface CalendarAvailability {
  date: string;
  timeSlots: TimeSlot[];
  isWorkingDay: boolean;
  isHoliday: boolean;
  holidayName?: string;
}

// Time slot
export interface TimeSlot {
  start: string; // HH:mm format
  end: string; // HH:mm format
  available: boolean;
  eventId?: string;
  eventTitle?: string;
  conflictLevel: 'none' | 'soft' | 'hard';
}

// Calendar sync settings
export interface CalendarSyncSettings {
  enabled: boolean;
  provider: 'google' | 'outlook' | 'apple';
  calendarId: string;
  syncDirection: 'import' | 'export' | 'bidirectional';
  syncFrequency: number; // minutes
  lastSyncAt?: string;
  syncStatus: 'active' | 'paused' | 'error';
  errorMessage?: string;
}

// Create calendar event data
export interface CreateCalendarEventData {
  title: string;
  description?: string;
  type: CalendarEventType;
  priority: CalendarEventPriority;
  startDate: string;
  endDate: string;
  allDay?: boolean;
  timezone?: string;
  location?: string;
  isVirtual?: boolean;
  meetingUrl?: string;
  attendees?: Omit<CalendarAttendee, 'id' | 'status' | 'responseDate'>[];
  interviewId?: string;
  candidateId?: string;
  jobId?: string;
  color?: string;
  tags?: string[];
  isRecurring?: boolean;
  recurringRule?: RecurringRule;
  reminders?: Omit<CalendarReminder, 'id' | 'sent' | 'sentAt'>[];
}

// Update calendar event data
export interface UpdateCalendarEventData extends Partial<CreateCalendarEventData> {
  status?: CalendarEventStatus;
  syncStatus?: 'synced' | 'pending' | 'failed';
}

// Calendar event query parameters
export interface CalendarEventQueryParams {
  startDate?: string;
  endDate?: string;
  type?: CalendarEventType[];
  status?: CalendarEventStatus[];
  priority?: CalendarEventPriority[];
  interviewerId?: string;
  candidateId?: string;
  jobId?: string;
  tags?: string[];
  search?: string;
  page?: number;
  limit?: number;
  sort?: string;
  include?: string[];
}

// Calendar dashboard data
export interface CalendarDashboard {
  statistics: CalendarStatistics;
  upcomingEvents: CalendarEvent[];
  todayEvents: CalendarEvent[];
  conflicts: CalendarConflict[];
  availability: CalendarAvailability[];
}

// Calendar conflict
export interface CalendarConflict {
  id: string;
  type: 'overlap' | 'double-booking' | 'travel-time';
  severity: 'low' | 'medium' | 'high';
  events: CalendarEvent[];
  suggestion?: string;
  autoResolvable: boolean;
}

// Calendar notification
export interface CalendarNotification {
  id: string;
  eventId: string;
  type: 'reminder' | 'update' | 'cancellation' | 'invitation';
  recipientId: string;
  recipientEmail: string;
  subject: string;
  message: string;
  scheduledAt: string;
  sentAt?: string;
  status: 'pending' | 'sent' | 'failed';
}

// Calendar integration
export interface CalendarIntegration {
  id: string;
  provider: 'google' | 'outlook' | 'apple' | 'zoom' | 'teams';
  userId: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: string;
  calendarId?: string;
  calendarName?: string;
  isActive: boolean;
  lastSyncAt?: string;
  syncErrors?: string[];
}

// Calendar bulk operation
export interface CalendarBulkOperation {
  eventIds: string[];
  operation: 'delete' | 'update_status' | 'reschedule' | 'add_attendees' | 'remove_attendees' | 'export';
  data?: any;
}

// Calendar search result
export interface CalendarSearchResult {
  events: CalendarEvent[];
  totalCount: number;
  facets: {
    types: Record<CalendarEventType, number>;
    statuses: Record<CalendarEventStatus, number>;
    priorities: Record<CalendarEventPriority, number>;
    dates: Record<string, number>;
  };
}
