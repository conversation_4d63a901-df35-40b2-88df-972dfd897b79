/**
 * Calendar Hooks
 * React hooks for calendar operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CalendarService } from '../services';
import {
  CalendarEvent,
  CreateCalendarEventData,
  UpdateCalendarEventData,
  CalendarFilters,
  CalendarEventQueryParams,
  CalendarExportOptions,
  CalendarBulkOperation,
} from '../types';

// Query keys
export const calendarKeys = {
  all: ['calendar'] as const,
  events: () => [...calendarKeys.all, 'events'] as const,
  event: (id: string) => [...calendarKeys.events(), id] as const,
  eventsList: (params?: CalendarEventQueryParams) => [...calendarKeys.events(), 'list', params] as const,
  statistics: (filters?: CalendarFilters) => [...calendarKeys.all, 'statistics', filters] as const,
  dashboard: (params?: { date_from?: string; date_to?: string }) => [...calendarKeys.all, 'dashboard', params] as const,
  availability: (params: any) => [...calendarKeys.all, 'availability', params] as const,
  search: (params: any) => [...calendarKeys.all, 'search', params] as const,
};

// Main calendar events hook
export function useCalendarEvents(params?: CalendarEventQueryParams) {
  return useQuery({
    queryKey: calendarKeys.eventsList(params),
    queryFn: () => CalendarService.getEvents(params),
    enabled: true,
    staleTime: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: false,
  });
}

// Legacy calendar events hook (for backward compatibility)
export function useCalendarEventsLegacy(params: {
  start_date: string;
  end_date: string;
  interviewer_id?: string;
  type?: string[];
  status?: string[];
}) {
  return useQuery({
    queryKey: ['calendar-events-legacy', params],
    queryFn: () => CalendarService.getCalendarEvents(params),
    enabled: !!(params.start_date && params.end_date),
    staleTime: 30 * 1000,
    refetchOnWindowFocus: false,
  });
}

// Single event hook
export function useCalendarEvent(id: string) {
  return useQuery({
    queryKey: calendarKeys.event(id),
    queryFn: () => CalendarService.getEventById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Calendar statistics hook
export function useCalendarStatistics(filters?: CalendarFilters) {
  return useQuery({
    queryKey: calendarKeys.statistics(filters),
    queryFn: () => CalendarService.getStatistics(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Calendar dashboard hook
export function useCalendarDashboard(params?: { date_from?: string; date_to?: string }) {
  return useQuery({
    queryKey: calendarKeys.dashboard(params),
    queryFn: () => CalendarService.getDashboard(params),
    staleTime: 60 * 1000, // 1 minute
    refetchInterval: 60 * 1000, // Refresh every minute
  });
}

// Availability hook
export function useCalendarAvailability(params: {
  start_date: string;
  end_date: string;
  interviewer_id?: string;
  duration?: number;
}) {
  return useQuery({
    queryKey: calendarKeys.availability(params),
    queryFn: () => CalendarService.getAvailability(params),
    enabled: !!(params.start_date && params.end_date),
    staleTime: 30 * 1000,
  });
}

// Search events hook
export function useCalendarSearch(params: {
  query: string;
  filters?: CalendarFilters;
  page?: number;
  limit?: number;
}) {
  return useQuery({
    queryKey: calendarKeys.search(params),
    queryFn: () => CalendarService.searchEvents(params),
    enabled: !!params.query && params.query.length > 2,
    staleTime: 30 * 1000,
  });
}

// Convenience hooks for specific time periods
export function useTodayEvents() {
  return useQuery({
    queryKey: [...calendarKeys.events(), 'today'],
    queryFn: () => CalendarService.getTodayEvents(),
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000, // Refresh every minute
  });
}

export function useUpcomingEvents(limit: number = 10) {
  return useQuery({
    queryKey: [...calendarKeys.events(), 'upcoming', limit],
    queryFn: () => CalendarService.getUpcomingEvents(limit),
    staleTime: 60 * 1000,
    refetchInterval: 2 * 60 * 1000, // Refresh every 2 minutes
  });
}

export function useMonthEvents(year: number, month: number) {
  return useQuery({
    queryKey: [...calendarKeys.events(), 'month', year, month],
    queryFn: () => CalendarService.getMonthEvents(year, month),
    staleTime: 60 * 1000,
  });
}

export function useWeekEvents(startOfWeek: Date) {
  return useQuery({
    queryKey: [...calendarKeys.events(), 'week', startOfWeek.toISOString()],
    queryFn: () => CalendarService.getWeekEvents(startOfWeek),
    staleTime: 30 * 1000,
  });
}

export function useDayEvents(date: Date) {
  return useQuery({
    queryKey: [...calendarKeys.events(), 'day', date.toISOString()],
    queryFn: () => CalendarService.getDayEvents(date),
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000, // Refresh every minute for today
  });
}

// Mutation hooks
export function useCreateCalendarEvent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCalendarEventData) => CalendarService.createEvent(data),
    onSuccess: () => {
      // Invalidate and refetch calendar events
      queryClient.invalidateQueries({ queryKey: calendarKeys.events() });
      queryClient.invalidateQueries({ queryKey: calendarKeys.statistics() });
      queryClient.invalidateQueries({ queryKey: calendarKeys.dashboard() });
    },
  });
}

export function useUpdateCalendarEvent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCalendarEventData }) =>
      CalendarService.updateEvent(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate specific event and lists
      queryClient.invalidateQueries({ queryKey: calendarKeys.event(id) });
      queryClient.invalidateQueries({ queryKey: calendarKeys.events() });
      queryClient.invalidateQueries({ queryKey: calendarKeys.statistics() });
    },
  });
}

export function useDeleteCalendarEvent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => CalendarService.deleteEvent(id),
    onSuccess: (_, id) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: calendarKeys.event(id) });
      queryClient.invalidateQueries({ queryKey: calendarKeys.events() });
      queryClient.invalidateQueries({ queryKey: calendarKeys.statistics() });
    },
  });
}

export function useExportCalendarEvents() {
  return useMutation({
    mutationFn: (options: CalendarExportOptions) => CalendarService.exportEvents(options),
  });
}

export function useImportCalendarEvents() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, options }: { file: File; options?: any }) =>
      CalendarService.importEvents(file, options),
    onSuccess: () => {
      // Invalidate all calendar data after import
      queryClient.invalidateQueries({ queryKey: calendarKeys.all });
    },
  });
}

export function useBulkCalendarOperation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (operation: CalendarBulkOperation) => CalendarService.bulkOperation(operation),
    onSuccess: () => {
      // Invalidate all calendar data after bulk operation
      queryClient.invalidateQueries({ queryKey: calendarKeys.events() });
      queryClient.invalidateQueries({ queryKey: calendarKeys.statistics() });
    },
  });
}

// Sync and integration hooks
export function useCalendarSyncSettings() {
  return useQuery({
    queryKey: [...calendarKeys.all, 'sync-settings'],
    queryFn: () => CalendarService.getSyncSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUpdateCalendarSyncSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: any) => CalendarService.updateSyncSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [...calendarKeys.all, 'sync-settings'] });
    },
  });
}

export function useTriggerCalendarSync() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (provider?: string) => CalendarService.triggerSync(provider),
    onSuccess: () => {
      // Invalidate events after sync
      queryClient.invalidateQueries({ queryKey: calendarKeys.events() });
    },
  });
}

export function useCalendarIntegrations() {
  return useQuery({
    queryKey: [...calendarKeys.all, 'integrations'],
    queryFn: () => CalendarService.getIntegrations(),
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateCalendarIntegration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => CalendarService.createIntegration(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [...calendarKeys.all, 'integrations'] });
    },
  });
}

export function useDeleteCalendarIntegration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => CalendarService.deleteIntegration(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [...calendarKeys.all, 'integrations'] });
    },
  });
}

// Notification hooks
export function useCalendarNotifications(params?: any) {
  return useQuery({
    queryKey: [...calendarKeys.all, 'notifications', params],
    queryFn: () => CalendarService.getNotifications(params),
    staleTime: 60 * 1000,
  });
}

export function useSendCalendarReminders() {
  return useMutation({
    mutationFn: (params: any) => CalendarService.sendReminders(params),
  });
}

// Utility hooks
export function useCalendarConflicts() {
  return {
    checkConflicts: async (eventData: CreateCalendarEventData | UpdateCalendarEventData, excludeEventId?: string) => {
      return CalendarService.checkConflicts(eventData, excludeEventId);
    },
  };
}

export function useCalendarValidation() {
  return {
    validateEvent: (data: CreateCalendarEventData | UpdateCalendarEventData) => {
      return CalendarService.validateEventData(data);
    },
  };
}
