/**
 * useCalendarEvents Hook Tests
 * Tests for the calendar events data fetching hook
 */

import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';
import { useCalendarEventsLegacy } from '../../hooks/useCalendarEvents';
import { CalendarEvent } from '../../types';

// Mock the calendar service
const mockCalendarService = {
  getCalendarEvents: vi.fn(),
};

vi.mock('../../services/calendarService', () => ({
  calendarService: mockCalendarService,
}));

// Mock data
const mockCalendarEvents: CalendarEvent[] = [
  {
    id: '1',
    title: 'Interview with <PERSON>',
    description: 'Technical interview',
    type: 'interview',
    status: 'scheduled',
    priority: 'high',
    startDate: '2024-01-15T10:00:00Z',
    endDate: '2024-01-15T11:00:00Z',
    allDay: false,
    timezone: 'UTC',
    location: 'Conference Room A',
    isVirtual: false,
    organizerId: 'user1',
    organizerName: '<PERSON>',
    organizerEmail: '<EMAIL>',
    attendees: [
      {
        name: '<PERSON>e',
        email: '<EMAIL>',
        role: 'required',
        status: 'pending',
      },
    ],
    color: '#3b82f6',
    tags: ['technical', 'frontend'],
    isRecurring: false,
    reminders: [],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: '2',
    title: 'Team Meeting',
    description: 'Weekly team sync',
    type: 'meeting',
    status: 'scheduled',
    priority: 'medium',
    startDate: '2024-01-16T14:00:00Z',
    endDate: '2024-01-16T15:00:00Z',
    allDay: false,
    timezone: 'UTC',
    isVirtual: true,
    meetingUrl: 'https://meet.example.com/123',
    organizerId: 'user2',
    organizerName: 'Bob Wilson',
    organizerEmail: '<EMAIL>',
    attendees: [],
    color: '#10b981',
    tags: ['meeting', 'team'],
    isRecurring: true,
    reminders: [],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
];

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useCalendarEventsLegacy', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('fetches calendar events successfully', async () => {
    mockCalendarService.getCalendarEvents.mockResolvedValue({
      status: 'success',
      data: mockCalendarEvents,
    });

    const { result } = renderHook(
      () => useCalendarEventsLegacy({
        start_date: '2024-01-01',
        end_date: '2024-01-31',
      }),
      { wrapper: createWrapper() }
    );

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toEqual({
      status: 'success',
      data: mockCalendarEvents,
    });
    expect(result.current.error).toBeNull();
    expect(mockCalendarService.getCalendarEvents).toHaveBeenCalledWith({
      start_date: '2024-01-01',
      end_date: '2024-01-31',
    });
  });

  it('handles error when fetching calendar events fails', async () => {
    const errorMessage = 'Failed to fetch calendar events';
    mockCalendarService.getCalendarEvents.mockRejectedValue(
      new Error(errorMessage)
    );

    const { result } = renderHook(
      () => useCalendarEventsLegacy({
        start_date: '2024-01-01',
        end_date: '2024-01-31',
      }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toBeTruthy();
  });

  it('passes correct parameters to service', async () => {
    const params = {
      start_date: '2024-02-01',
      end_date: '2024-02-29',
      type: ['interview', 'meeting'],
      status: ['scheduled'],
    };

    mockCalendarService.getCalendarEvents.mockResolvedValue({
      status: 'success',
      data: [],
    });

    renderHook(
      () => useCalendarEventsLegacy(params),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(mockCalendarService.getCalendarEvents).toHaveBeenCalledWith(params);
    });
  });

  it('handles empty response', async () => {
    mockCalendarService.getCalendarEvents.mockResolvedValue({
      status: 'success',
      data: [],
    });

    const { result } = renderHook(
      () => useCalendarEventsLegacy({
        start_date: '2024-01-01',
        end_date: '2024-01-31',
      }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toEqual({
      status: 'success',
      data: [],
    });
  });

  it('refetches data when parameters change', async () => {
    mockCalendarService.getCalendarEvents.mockResolvedValue({
      status: 'success',
      data: mockCalendarEvents,
    });

    const { result, rerender } = renderHook(
      ({ params }) => useCalendarEventsLegacy(params),
      {
        wrapper: createWrapper(),
        initialProps: {
          params: {
            start_date: '2024-01-01',
            end_date: '2024-01-31',
          },
        },
      }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockCalendarService.getCalendarEvents).toHaveBeenCalledTimes(1);

    // Change parameters
    rerender({
      params: {
        start_date: '2024-02-01',
        end_date: '2024-02-29',
      },
    });

    await waitFor(() => {
      expect(mockCalendarService.getCalendarEvents).toHaveBeenCalledTimes(2);
    });

    expect(mockCalendarService.getCalendarEvents).toHaveBeenLastCalledWith({
      start_date: '2024-02-01',
      end_date: '2024-02-29',
    });
  });

  it('uses correct query key for caching', async () => {
    mockCalendarService.getCalendarEvents.mockResolvedValue({
      status: 'success',
      data: mockCalendarEvents,
    });

    const params = {
      start_date: '2024-01-01',
      end_date: '2024-01-31',
    };

    const { result } = renderHook(
      () => useCalendarEventsLegacy(params),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // The query should be cached with the correct key
    expect(result.current.data).toBeDefined();
  });

  it('handles network errors gracefully', async () => {
    mockCalendarService.getCalendarEvents.mockRejectedValue(
      new Error('Network error')
    );

    const { result } = renderHook(
      () => useCalendarEventsLegacy({
        start_date: '2024-01-01',
        end_date: '2024-01-31',
      }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
    expect(result.current.data).toBeUndefined();
  });

  it('supports refetch functionality', async () => {
    mockCalendarService.getCalendarEvents.mockResolvedValue({
      status: 'success',
      data: mockCalendarEvents,
    });

    const { result } = renderHook(
      () => useCalendarEventsLegacy({
        start_date: '2024-01-01',
        end_date: '2024-01-31',
      }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockCalendarService.getCalendarEvents).toHaveBeenCalledTimes(1);

    // Trigger refetch
    result.current.refetch();

    await waitFor(() => {
      expect(mockCalendarService.getCalendarEvents).toHaveBeenCalledTimes(2);
    });
  });
});
