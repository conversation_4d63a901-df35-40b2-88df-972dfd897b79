/**
 * CalendarView Component Tests
 * Tests for the main calendar view component
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { CalendarView } from '../../components/CalendarView';
import { CalendarEvent, CalendarViewType } from '../../types';

// Mock the view components
vi.mock('../../components/views', () => ({
  MonthView: ({ events, onEventClick }: any) => (
    <div data-testid="month-view">
      {events.map((event: CalendarEvent) => (
        <div 
          key={event.id} 
          data-testid={`event-${event.id}`}
          onClick={() => onEventClick(event)}
        >
          {event.title}
        </div>
      ))}
    </div>
  ),
  WeekView: ({ events, onEventClick }: any) => (
    <div data-testid="week-view">
      {events.map((event: CalendarEvent) => (
        <div 
          key={event.id} 
          data-testid={`event-${event.id}`}
          onClick={() => onEventClick(event)}
        >
          {event.title}
        </div>
      ))}
    </div>
  ),
  DayView: ({ events, onEventClick }: any) => (
    <div data-testid="day-view">
      {events.map((event: CalendarEvent) => (
        <div 
          key={event.id} 
          data-testid={`event-${event.id}`}
          onClick={() => onEventClick(event)}
        >
          {event.title}
        </div>
      ))}
    </div>
  ),
  AgendaView: ({ events, onEventClick }: any) => (
    <div data-testid="agenda-view">
      {events.map((event: CalendarEvent) => (
        <div 
          key={event.id} 
          data-testid={`event-${event.id}`}
          onClick={() => onEventClick(event)}
        >
          {event.title}
        </div>
      ))}
    </div>
  ),
}));

// Mock the filters panel
vi.mock('../../components/CalendarFiltersPanel', () => ({
  CalendarFiltersPanel: ({ onFiltersChange, onClose }: any) => (
    <div data-testid="filters-panel">
      <button onClick={() => onFiltersChange({ status: 'scheduled' })}>
        Apply Filter
      </button>
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

// Mock data
const mockEvents: CalendarEvent[] = [
  {
    id: '1',
    title: 'Interview with John Doe',
    description: 'Technical interview',
    type: 'interview',
    status: 'scheduled',
    priority: 'high',
    startDate: '2024-01-15T10:00:00Z',
    endDate: '2024-01-15T11:00:00Z',
    allDay: false,
    timezone: 'UTC',
    location: 'Conference Room A',
    isVirtual: false,
    organizerId: 'user1',
    organizerName: 'Jane Smith',
    organizerEmail: '<EMAIL>',
    attendees: [
      {
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'required',
        status: 'pending',
      },
    ],
    color: '#3b82f6',
    tags: ['technical', 'frontend'],
    isRecurring: false,
    reminders: [],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: '2',
    title: 'Team Meeting',
    description: 'Weekly team sync',
    type: 'meeting',
    status: 'scheduled',
    priority: 'medium',
    startDate: '2024-01-16T14:00:00Z',
    endDate: '2024-01-16T15:00:00Z',
    allDay: false,
    timezone: 'UTC',
    isVirtual: true,
    meetingUrl: 'https://meet.example.com/123',
    organizerId: 'user2',
    organizerName: 'Bob Wilson',
    organizerEmail: '<EMAIL>',
    attendees: [],
    color: '#10b981',
    tags: ['meeting', 'team'],
    isRecurring: true,
    reminders: [],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
];

describe('CalendarView', () => {
  const defaultProps = {
    events: mockEvents,
    loading: false,
    viewType: 'month' as CalendarViewType,
    selectedDate: new Date('2024-01-15'),
    filters: {},
    onViewTypeChange: vi.fn(),
    onDateChange: vi.fn(),
    onFiltersChange: vi.fn(),
    onEventClick: vi.fn(),
    onEventCreate: vi.fn(),
    onEventUpdate: vi.fn(),
    onEventDelete: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders calendar view with events', () => {
    render(<CalendarView {...defaultProps} />);
    
    expect(screen.getByTestId('month-view')).toBeInTheDocument();
    expect(screen.getByTestId('event-1')).toBeInTheDocument();
    expect(screen.getByTestId('event-2')).toBeInTheDocument();
    expect(screen.getByText('Interview with John Doe')).toBeInTheDocument();
    expect(screen.getByText('Team Meeting')).toBeInTheDocument();
  });

  it('renders loading state', () => {
    render(<CalendarView {...defaultProps} loading={true} />);
    
    expect(screen.getByText('Loading calendar...')).toBeInTheDocument();
  });

  it('switches between view types', () => {
    const onViewTypeChange = vi.fn();
    render(<CalendarView {...defaultProps} onViewTypeChange={onViewTypeChange} />);
    
    // Click week view button
    const weekButton = screen.getByRole('button', { name: /week/i });
    fireEvent.click(weekButton);
    
    expect(onViewTypeChange).toHaveBeenCalledWith('week');
  });

  it('renders week view when viewType is week', () => {
    render(<CalendarView {...defaultProps} viewType="week" />);
    
    expect(screen.getByTestId('week-view')).toBeInTheDocument();
    expect(screen.queryByTestId('month-view')).not.toBeInTheDocument();
  });

  it('renders day view when viewType is day', () => {
    render(<CalendarView {...defaultProps} viewType="day" />);
    
    expect(screen.getByTestId('day-view')).toBeInTheDocument();
    expect(screen.queryByTestId('month-view')).not.toBeInTheDocument();
  });

  it('renders agenda view when viewType is agenda', () => {
    render(<CalendarView {...defaultProps} viewType="agenda" />);
    
    expect(screen.getByTestId('agenda-view')).toBeInTheDocument();
    expect(screen.queryByTestId('month-view')).not.toBeInTheDocument();
  });

  it('handles event click', () => {
    const onEventClick = vi.fn();
    render(<CalendarView {...defaultProps} onEventClick={onEventClick} />);
    
    fireEvent.click(screen.getByTestId('event-1'));
    
    expect(onEventClick).toHaveBeenCalledWith(mockEvents[0]);
  });

  it('shows filters panel when showFilters is true', () => {
    render(<CalendarView {...defaultProps} showFilters={true} />);
    
    // Click filters button
    const filtersButton = screen.getByRole('button', { name: /filters/i });
    fireEvent.click(filtersButton);
    
    expect(screen.getByTestId('filters-panel')).toBeInTheDocument();
  });

  it('handles filter changes', async () => {
    const onFiltersChange = vi.fn();
    render(<CalendarView {...defaultProps} showFilters={true} onFiltersChange={onFiltersChange} />);
    
    // Open filters panel
    const filtersButton = screen.getByRole('button', { name: /filters/i });
    fireEvent.click(filtersButton);
    
    // Apply filter
    const applyFilterButton = screen.getByText('Apply Filter');
    fireEvent.click(applyFilterButton);
    
    expect(onFiltersChange).toHaveBeenCalledWith({ status: 'scheduled' });
  });

  it('navigates to previous period', () => {
    const onDateChange = vi.fn();
    render(<CalendarView {...defaultProps} onDateChange={onDateChange} />);
    
    const prevButton = screen.getByRole('button', { name: /previous/i });
    fireEvent.click(prevButton);
    
    expect(onDateChange).toHaveBeenCalled();
  });

  it('navigates to next period', () => {
    const onDateChange = vi.fn();
    render(<CalendarView {...defaultProps} onDateChange={onDateChange} />);
    
    const nextButton = screen.getByRole('button', { name: /next/i });
    fireEvent.click(nextButton);
    
    expect(onDateChange).toHaveBeenCalled();
  });

  it('shows create button when showCreateButton is true', () => {
    render(<CalendarView {...defaultProps} showCreateButton={true} />);
    
    expect(screen.getByRole('button', { name: /create/i })).toBeInTheDocument();
  });

  it('handles create event', () => {
    const onEventCreate = vi.fn();
    render(<CalendarView {...defaultProps} showCreateButton={true} onEventCreate={onEventCreate} />);
    
    const createButton = screen.getByRole('button', { name: /create/i });
    fireEvent.click(createButton);
    
    expect(onEventCreate).toHaveBeenCalled();
  });

  it('shows export button when showExport is true', () => {
    render(<CalendarView {...defaultProps} showExport={true} />);
    
    expect(screen.getByRole('button', { name: /export/i })).toBeInTheDocument();
  });

  it('renders empty state when no events', () => {
    render(<CalendarView {...defaultProps} events={[]} />);
    
    expect(screen.getByText(/no events/i)).toBeInTheDocument();
  });

  it('handles date selection', () => {
    const onDateChange = vi.fn();
    render(<CalendarView {...defaultProps} onDateChange={onDateChange} />);
    
    // This would depend on the actual implementation of date selection
    // For now, we'll test that the date change handler is available
    expect(onDateChange).toBeDefined();
  });
});
