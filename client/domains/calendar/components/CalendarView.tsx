/**
 * Calendar View Component
 * Modern calendar view with multiple view types (month, week, day)
 */

import React, { useState, useMemo, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  Plus,
  Filter,
  Download,
  Settings,
} from 'lucide-react';
import { CalendarEvent, CalendarViewType, CalendarFilters } from '../types';
import { MonthView } from './views/MonthView';
import { WeekView } from './views/WeekView';
import { DayView } from './views/DayView';
import { AgendaView } from './views/AgendaView';
import { CalendarFiltersPanel } from './CalendarFiltersPanel';
import { EventModal } from './EventModal';

export interface CalendarViewProps {
  events: CalendarEvent[];
  loading?: boolean;
  viewType?: CalendarViewType;
  selectedDate?: Date;
  filters?: CalendarFilters;
  onViewTypeChange?: (viewType: CalendarViewType) => void;
  onDateChange?: (date: Date) => void;
  onFiltersChange?: (filters: CalendarFilters) => void;
  onEventClick?: (event: CalendarEvent) => void;
  onEventCreate?: (date: Date) => void;
  onEventUpdate?: (event: CalendarEvent) => void;
  onEventDelete?: (eventId: string) => void;
  showCreateButton?: boolean;
  showFilters?: boolean;
  showExport?: boolean;
  className?: string;
}

export const CalendarView: React.FC<CalendarViewProps> = ({
  events,
  loading = false,
  viewType = 'month',
  selectedDate = new Date(),
  filters = {},
  onViewTypeChange,
  onDateChange,
  onFiltersChange,
  onEventClick,
  onEventCreate,
  onEventUpdate,
  onEventDelete,
  showCreateButton = true,
  showFilters = true,
  showExport = true,
  className,
}) => {
  const [currentDate, setCurrentDate] = useState(selectedDate);
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [showEventModal, setShowEventModal] = useState(false);

  // Handle date navigation
  const handleDateChange = useCallback((date: Date) => {
    setCurrentDate(date);
    onDateChange?.(date);
  }, [onDateChange]);

  const handlePrevious = useCallback(() => {
    const newDate = new Date(currentDate);
    switch (viewType) {
      case 'month':
        newDate.setMonth(newDate.getMonth() - 1);
        break;
      case 'week':
        newDate.setDate(newDate.getDate() - 7);
        break;
      case 'day':
        newDate.setDate(newDate.getDate() - 1);
        break;
      default:
        newDate.setMonth(newDate.getMonth() - 1);
    }
    handleDateChange(newDate);
  }, [currentDate, viewType, handleDateChange]);

  const handleNext = useCallback(() => {
    const newDate = new Date(currentDate);
    switch (viewType) {
      case 'month':
        newDate.setMonth(newDate.getMonth() + 1);
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + 7);
        break;
      case 'day':
        newDate.setDate(newDate.getDate() + 1);
        break;
      default:
        newDate.setMonth(newDate.getMonth() + 1);
    }
    handleDateChange(newDate);
  }, [currentDate, viewType, handleDateChange]);

  const handleToday = useCallback(() => {
    handleDateChange(new Date());
  }, [handleDateChange]);

  // Handle event interactions
  const handleEventClick = useCallback((event: CalendarEvent) => {
    setSelectedEvent(event);
    setShowEventModal(true);
    onEventClick?.(event);
  }, [onEventClick]);

  const handleEventCreate = useCallback((date: Date) => {
    onEventCreate?.(date);
  }, [onEventCreate]);

  const handleEventUpdate = useCallback((event: CalendarEvent) => {
    setShowEventModal(false);
    setSelectedEvent(null);
    onEventUpdate?.(event);
  }, [onEventUpdate]);

  const handleEventDelete = useCallback((eventId: string) => {
    setShowEventModal(false);
    setSelectedEvent(null);
    onEventDelete?.(eventId);
  }, [onEventDelete]);

  // Format date for display
  const formatDateHeader = useMemo(() => {
    switch (viewType) {
      case 'month':
        return currentDate.toLocaleDateString('en-US', {
          month: 'long',
          year: 'numeric',
        });
      case 'week':
        const startOfWeek = new Date(currentDate);
        startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `${startOfWeek.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        })} - ${endOfWeek.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })}`;
      case 'day':
        return currentDate.toLocaleDateString('en-US', {
          weekday: 'long',
          month: 'long',
          day: 'numeric',
          year: 'numeric',
        });
      default:
        return currentDate.toLocaleDateString('en-US', {
          month: 'long',
          year: 'numeric',
        });
    }
  }, [currentDate, viewType]);

  // Filter events based on current view and filters
  const filteredEvents = useMemo(() => {
    let filtered = events;

    // Apply filters
    if (filters.types?.length) {
      filtered = filtered.filter(event => filters.types!.includes(event.type));
    }

    if (filters.statuses?.length) {
      filtered = filtered.filter(event => filters.statuses!.includes(event.status));
    }

    if (filters.priorities?.length) {
      filtered = filtered.filter(event => filters.priorities!.includes(event.priority));
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(searchLower) ||
        event.description?.toLowerCase().includes(searchLower)
      );
    }

    return filtered;
  }, [events, filters]);

  // Render the appropriate view
  const renderCalendarView = () => {
    const commonProps = {
      events: filteredEvents,
      currentDate,
      onDateChange: handleDateChange,
      onEventClick: handleEventClick,
      onEventCreate: handleEventCreate,
      loading,
    };

    switch (viewType) {
      case 'month':
        return <MonthView {...commonProps} />;
      case 'week':
        return <WeekView {...commonProps} />;
      case 'day':
        return <DayView {...commonProps} />;
      case 'agenda':
        return <AgendaView {...commonProps} />;
      default:
        return <MonthView {...commonProps} />;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Calendar Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            <h2 className="text-2xl font-bold">{formatDateHeader}</h2>
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevious}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNext}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleToday}
              className="ml-2"
            >
              Today
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* View Type Selector */}
          <Select
            value={viewType}
            onValueChange={(value) => onViewTypeChange?.(value as CalendarViewType)}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="week">Week</SelectItem>
              <SelectItem value="day">Day</SelectItem>
              <SelectItem value="agenda">Agenda</SelectItem>
            </SelectContent>
          </Select>

          {/* Action Buttons */}
          {showFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
              className={showFiltersPanel ? 'bg-muted' : ''}
            >
              <Filter className="h-4 w-4" />
            </Button>
          )}

          {showExport && (
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4" />
            </Button>
          )}

          {showCreateButton && (
            <Button
              size="sm"
              onClick={() => handleEventCreate(currentDate)}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              New Event
            </Button>
          )}
        </div>
      </div>

      {/* Filters Panel */}
      {showFiltersPanel && (
        <CalendarFiltersPanel
          filters={filters}
          onFiltersChange={onFiltersChange}
          onClose={() => setShowFiltersPanel(false)}
        />
      )}

      {/* Calendar View */}
      <div className="min-h-[600px]">
        {renderCalendarView()}
      </div>

      {/* Event Modal */}
      {showEventModal && selectedEvent && (
        <EventModal
          event={selectedEvent}
          isOpen={showEventModal}
          onClose={() => {
            setShowEventModal(false);
            setSelectedEvent(null);
          }}
          onUpdate={handleEventUpdate}
          onDelete={handleEventDelete}
        />
      )}
    </div>
  );
};
