/**
 * Calendar Filters Panel Component
 * Advanced filtering panel for calendar events
 */

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
  X,
  Search,
  CalendarIcon,
  Filter,
  RotateCcw,
} from 'lucide-react';
import { CalendarFilters, CalendarEventType, CalendarEventStatus, CalendarEventPriority } from '../types';

export interface CalendarFiltersPanelProps {
  filters: CalendarFilters;
  onFiltersChange: (filters: CalendarFilters) => void;
  onClose: () => void;
  className?: string;
}

export const CalendarFiltersPanel: React.FC<CalendarFiltersPanelProps> = ({
  filters,
  onFiltersChange,
  onClose,
  className,
}) => {
  const [localFilters, setLocalFilters] = useState<CalendarFilters>(filters);

  // Event type options
  const eventTypes: { value: CalendarEventType; label: string }[] = [
    { value: 'interview', label: 'Interview' },
    { value: 'meeting', label: 'Meeting' },
    { value: 'deadline', label: 'Deadline' },
    { value: 'reminder', label: 'Reminder' },
    { value: 'holiday', label: 'Holiday' },
    { value: 'personal', label: 'Personal' },
  ];

  // Event status options
  const eventStatuses: { value: CalendarEventStatus; label: string }[] = [
    { value: 'scheduled', label: 'Scheduled' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'completed', label: 'Completed' },
    { value: 'rescheduled', label: 'Rescheduled' },
    { value: 'no-show', label: 'No Show' },
  ];

  // Event priority options
  const eventPriorities: { value: CalendarEventPriority; label: string }[] = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' },
  ];

  // Handle filter changes
  const handleFilterChange = useCallback((key: keyof CalendarFilters, value: any) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  // Handle array filter changes
  const handleArrayFilterChange = useCallback((
    key: keyof CalendarFilters,
    value: string,
    checked: boolean
  ) => {
    setLocalFilters(prev => {
      const currentArray = (prev[key] as string[]) || [];
      const newArray = checked
        ? [...currentArray, value]
        : currentArray.filter(item => item !== value);
      
      return {
        ...prev,
        [key]: newArray.length > 0 ? newArray : undefined,
      };
    });
  }, []);

  // Apply filters
  const handleApplyFilters = useCallback(() => {
    onFiltersChange(localFilters);
  }, [localFilters, onFiltersChange]);

  // Reset filters
  const handleResetFilters = useCallback(() => {
    const emptyFilters: CalendarFilters = {};
    setLocalFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  }, [onFiltersChange]);

  // Check if filters are active
  const hasActiveFilters = Object.values(localFilters).some(value => 
    value !== undefined && value !== null && 
    (Array.isArray(value) ? value.length > 0 : true)
  );

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            <span>Filter Events</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0"
          >
            <X className="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search">Search Events</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              id="search"
              placeholder="Search by title or description..."
              value={localFilters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value || undefined)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Date Range */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {localFilters.startDate
                    ? new Date(localFilters.startDate).toLocaleDateString()
                    : 'Select date'
                  }
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={localFilters.startDate ? new Date(localFilters.startDate) : undefined}
                  onSelect={(date) => handleFilterChange('startDate', date?.toISOString())}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label>End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {localFilters.endDate
                    ? new Date(localFilters.endDate).toLocaleDateString()
                    : 'Select date'
                  }
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={localFilters.endDate ? new Date(localFilters.endDate) : undefined}
                  onSelect={(date) => handleFilterChange('endDate', date?.toISOString())}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Event Types */}
        <div className="space-y-2">
          <Label>Event Types</Label>
          <div className="grid grid-cols-2 gap-2">
            {eventTypes.map((type) => (
              <div key={type.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`type-${type.value}`}
                  checked={localFilters.types?.includes(type.value) || false}
                  onCheckedChange={(checked) =>
                    handleArrayFilterChange('types', type.value, !!checked)
                  }
                />
                <Label
                  htmlFor={`type-${type.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {type.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Event Statuses */}
        <div className="space-y-2">
          <Label>Event Status</Label>
          <div className="grid grid-cols-2 gap-2">
            {eventStatuses.map((status) => (
              <div key={status.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${status.value}`}
                  checked={localFilters.statuses?.includes(status.value) || false}
                  onCheckedChange={(checked) =>
                    handleArrayFilterChange('statuses', status.value, !!checked)
                  }
                />
                <Label
                  htmlFor={`status-${status.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {status.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Event Priorities */}
        <div className="space-y-2">
          <Label>Priority</Label>
          <div className="grid grid-cols-2 gap-2">
            {eventPriorities.map((priority) => (
              <div key={priority.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`priority-${priority.value}`}
                  checked={localFilters.priorities?.includes(priority.value) || false}
                  onCheckedChange={(checked) =>
                    handleArrayFilterChange('priorities', priority.value, !!checked)
                  }
                />
                <Label
                  htmlFor={`priority-${priority.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {priority.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <Label htmlFor="tags">Tags</Label>
          <Input
            id="tags"
            placeholder="Enter tags separated by commas"
            value={localFilters.tags?.join(', ') || ''}
            onChange={(e) => {
              const tags = e.target.value
                .split(',')
                .map(tag => tag.trim())
                .filter(tag => tag.length > 0);
              handleFilterChange('tags', tags.length > 0 ? tags : undefined);
            }}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetFilters}
            disabled={!hasActiveFilters}
            className="gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Reset
          </Button>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={onClose}>
              Cancel
            </Button>
            <Button size="sm" onClick={handleApplyFilters}>
              Apply Filters
            </Button>
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-4 border-t">
            <Label className="text-xs text-muted-foreground">Active Filters:</Label>
            <div className="flex flex-wrap gap-1 mt-2">
              {localFilters.search && (
                <Badge variant="secondary" className="text-xs">
                  Search: {localFilters.search}
                </Badge>
              )}
              {localFilters.types?.map(type => (
                <Badge key={type} variant="secondary" className="text-xs">
                  Type: {type}
                </Badge>
              ))}
              {localFilters.statuses?.map(status => (
                <Badge key={status} variant="secondary" className="text-xs">
                  Status: {status}
                </Badge>
              ))}
              {localFilters.priorities?.map(priority => (
                <Badge key={priority} variant="secondary" className="text-xs">
                  Priority: {priority}
                </Badge>
              ))}
              {localFilters.tags?.map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  Tag: {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
