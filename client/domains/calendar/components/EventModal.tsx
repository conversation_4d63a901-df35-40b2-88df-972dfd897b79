/**
 * Event Modal Component
 * Modal for viewing and editing calendar events
 */

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import {
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Calendar as CalendarIcon,
  Edit,
  Trash2,
  ExternalLink,
} from 'lucide-react';
import { CalendarEvent } from '../types';

export interface EventModalProps {
  event: CalendarEvent;
  isOpen: boolean;
  onClose: () => void;
  onUpdate?: (event: CalendarEvent) => void;
  onDelete?: (eventId: string) => void;
  readOnly?: boolean;
}

export const EventModal: React.FC<EventModalProps> = ({
  event,
  isOpen,
  onClose,
  onUpdate,
  onDelete,
  readOnly = false,
}) => {
  // Get event type icon
  const getEventTypeIcon = () => {
    if (event.type === 'interview') {
      if (event.isVirtual) return <Video className="w-5 h-5" />;
      if (event.location?.toLowerCase().includes('phone')) return <Phone className="w-5 h-5" />;
      return <MapPin className="w-5 h-5" />;
    }
    return <CalendarIcon className="w-5 h-5" />;
  };

  // Get event priority color
  const getEventPriorityColor = () => {
    switch (event.priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  // Get event status color
  const getEventStatusColor = () => {
    switch (event.status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'rescheduled':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  // Format event time
  const formatEventTime = () => {
    if (event.allDay) return 'All day';
    
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    
    const startTime = start.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    const endTime = end.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    return `${startTime} - ${endTime}`;
  };

  // Format event date
  const formatEventDate = () => {
    const start = new Date(event.startDate);
    return start.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {getEventTypeIcon()}
            <span>{event.title}</span>
          </DialogTitle>
          <DialogDescription>
            {formatEventDate()} • {formatEventTime()}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Priority */}
          <div className="flex items-center gap-2">
            <Badge className={getEventStatusColor()}>
              {event.status}
            </Badge>
            <Badge className={getEventPriorityColor()}>
              {event.priority} priority
            </Badge>
            <Badge variant="outline">
              {event.type}
            </Badge>
          </div>

          {/* Description */}
          {event.description && (
            <div>
              <h4 className="font-semibold mb-2">Description</h4>
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                {event.description}
              </p>
            </div>
          )}

          {/* Event Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Time */}
            <div className="flex items-center gap-3">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Time</p>
                <p className="text-sm text-muted-foreground">
                  {formatEventTime()}
                </p>
              </div>
            </div>

            {/* Location */}
            {event.location && (
              <div className="flex items-center gap-3">
                <MapPin className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Location</p>
                  <p className="text-sm text-muted-foreground">
                    {event.location}
                  </p>
                </div>
              </div>
            )}

            {/* Meeting URL */}
            {event.meetingUrl && (
              <div className="flex items-center gap-3">
                <Video className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Meeting Link</p>
                  <Button
                    variant="link"
                    size="sm"
                    className="h-auto p-0 text-sm"
                    onClick={() => window.open(event.meetingUrl, '_blank')}
                  >
                    Join Meeting
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </Button>
                </div>
              </div>
            )}

            {/* Organizer */}
            {event.organizerName && (
              <div className="flex items-center gap-3">
                <User className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Organizer</p>
                  <p className="text-sm text-muted-foreground">
                    {event.organizerName}
                    {event.organizerEmail && (
                      <span className="block">{event.organizerEmail}</span>
                    )}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Attendees */}
          {event.attendees && event.attendees.length > 0 && (
            <div>
              <h4 className="font-semibold mb-3">Attendees ({event.attendees.length})</h4>
              <div className="space-y-2">
                {event.attendees.map((attendee, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <div>
                      <p className="text-sm font-medium">{attendee.name}</p>
                      <p className="text-xs text-muted-foreground">{attendee.email}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {attendee.role}
                      </Badge>
                      <Badge
                        variant={attendee.status === 'accepted' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {attendee.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Tags */}
          {event.tags && event.tags.length > 0 && (
            <div>
              <h4 className="font-semibold mb-2">Tags</h4>
              <div className="flex flex-wrap gap-1">
                {event.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Reminders */}
          {event.reminders && event.reminders.length > 0 && (
            <div>
              <h4 className="font-semibold mb-2">Reminders</h4>
              <div className="space-y-1">
                {event.reminders.map((reminder, index) => (
                  <div key={index} className="text-sm text-muted-foreground">
                    {reminder.type} reminder {reminder.minutesBefore} minutes before
                    {reminder.sent && <span className="text-green-600 ml-2">✓ Sent</span>}
                  </div>
                ))}
              </div>
            </div>
          )}

          <Separator />

          {/* Actions */}
          <div className="flex items-center justify-between">
            <div className="text-xs text-muted-foreground">
              Created: {new Date(event.createdAt).toLocaleDateString()}
              {event.updatedAt !== event.createdAt && (
                <span className="ml-2">
                  • Updated: {new Date(event.updatedAt).toLocaleDateString()}
                </span>
              )}
            </div>

            {!readOnly && (
              <div className="flex items-center gap-2">
                {onUpdate && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onUpdate(event)}
                    className="gap-2"
                  >
                    <Edit className="w-4 h-4" />
                    Edit
                  </Button>
                )}
                
                {onDelete && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDelete(event.id)}
                    className="gap-2 text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
