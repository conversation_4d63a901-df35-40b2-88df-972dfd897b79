/**
 * Day View Component
 * Calendar day view for displaying events in a single day
 */

import React, { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CalendarEvent } from '../../types';

export interface DayViewProps {
  events: CalendarEvent[];
  currentDate: Date;
  onDateChange: (date: Date) => void;
  onEventClick: (event: CalendarEvent) => void;
  onEventCreate?: (date: Date) => void;
  loading?: boolean;
}

export const DayView: React.FC<DayViewProps> = ({
  events,
  currentDate,
  onDateChange,
  onEventClick,
  onEventCreate,
  loading = false,
}) => {
  const timeSlots = useMemo(() => {
    const slots = [];
    for (let hour = 0; hour < 24; hour++) {
      slots.push({
        hour,
        time: new Date(0, 0, 0, hour).toLocaleTimeString('en-US', {
          hour: 'numeric',
          hour12: true,
        }),
      });
    }
    return slots;
  }, []);

  // Filter events for the current day
  const dayEvents = useMemo(() => {
    const dayStart = new Date(currentDate);
    dayStart.setHours(0, 0, 0, 0);
    
    const dayEnd = new Date(currentDate);
    dayEnd.setHours(23, 59, 59, 999);

    return events.filter(event => {
      const eventDate = new Date(event.startDate);
      return eventDate >= dayStart && eventDate <= dayEnd;
    }).sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());
  }, [events, currentDate]);

  // Get event position and height
  const getEventStyle = (event: CalendarEvent) => {
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    
    const startHour = start.getHours() + start.getMinutes() / 60;
    const endHour = end.getHours() + end.getMinutes() / 60;
    
    const top = (startHour / 24) * 100;
    const height = ((endHour - startHour) / 24) * 100;
    
    return {
      top: `${top}%`,
      height: `${Math.max(height, 4)}%`, // Minimum height for visibility
    };
  };

  // Get event color based on type and status
  const getEventColor = (event: CalendarEvent) => {
    if (event.status === 'cancelled') return 'bg-red-100 text-red-800 border-red-200';
    if (event.status === 'completed') return 'bg-green-100 text-green-800 border-green-200';
    
    switch (event.type) {
      case 'interview':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'meeting':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'deadline':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format event time
  const formatEventTime = (event: CalendarEvent) => {
    if (event.allDay) return 'All day';
    
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    
    const startTime = start.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    const endTime = end.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    return `${startTime} - ${endTime}`;
  };

  const isToday = useMemo(() => {
    const today = new Date();
    return currentDate.toDateString() === today.toDateString();
  }, [currentDate]);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading calendar...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Day Header */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">
                {currentDate.toLocaleDateString('en-US', {
                  weekday: 'long',
                  month: 'long',
                  day: 'numeric',
                  year: 'numeric',
                })}
              </h3>
              {isToday && (
                <Badge variant="secondary" className="mt-1">
                  Today
                </Badge>
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              {dayEvents.length} events
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Day Schedule */}
      <Card>
        <CardContent className="p-0">
          <div className="flex">
            {/* Time Column */}
            <div className="w-20 border-r">
              {timeSlots.map((slot) => (
                <div
                  key={slot.hour}
                  className="h-16 border-b text-xs text-muted-foreground p-2 text-right"
                >
                  {slot.hour > 0 && slot.time}
                </div>
              ))}
            </div>

            {/* Events Column */}
            <div className="flex-1 relative">
              {/* Grid Lines */}
              {timeSlots.map((slot) => (
                <div
                  key={slot.hour}
                  className="h-16 border-b hover:bg-muted/30 cursor-pointer"
                  onClick={() => {
                    const clickDate = new Date(currentDate);
                    clickDate.setHours(slot.hour, 0, 0, 0);
                    onEventCreate?.(clickDate);
                  }}
                />
              ))}

              {/* Events */}
              {dayEvents.map((event) => {
                const style = getEventStyle(event);
                return (
                  <div
                    key={event.id}
                    className={`absolute left-2 right-2 p-2 rounded border cursor-pointer hover:shadow-sm transition-shadow ${getEventColor(event)}`}
                    style={style}
                    onClick={() => onEventClick(event)}
                  >
                    <div className="font-medium text-sm truncate">
                      {event.title}
                    </div>
                    {!event.allDay && (
                      <div className="text-xs opacity-75 mt-1">
                        {formatEventTime(event)}
                      </div>
                    )}
                    {event.location && (
                      <div className="text-xs opacity-75 truncate">
                        📍 {event.location}
                      </div>
                    )}
                  </div>
                );
              })}

              {/* Current Time Indicator */}
              {isToday && (
                <div
                  className="absolute left-0 right-0 h-0.5 bg-red-500 z-10"
                  style={{
                    top: `${(new Date().getHours() + new Date().getMinutes() / 60) / 24 * 100}%`,
                  }}
                >
                  <div className="absolute -left-1 -top-1 w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* All Day Events */}
      {dayEvents.some(event => event.allDay) && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">All Day Events</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {dayEvents
              .filter(event => event.allDay)
              .map((event) => (
                <div
                  key={event.id}
                  className={`p-2 rounded border cursor-pointer hover:shadow-sm transition-shadow ${getEventColor(event)}`}
                  onClick={() => onEventClick(event)}
                >
                  <div className="font-medium text-sm">{event.title}</div>
                  {event.description && (
                    <div className="text-xs opacity-75 mt-1 truncate">
                      {event.description}
                    </div>
                  )}
                </div>
              ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
