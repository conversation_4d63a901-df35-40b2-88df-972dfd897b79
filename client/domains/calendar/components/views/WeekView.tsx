/**
 * Week View Component
 * Calendar week view for displaying events in a weekly grid
 */

import React, { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { CalendarEvent } from '../../types';

interface WeekDay {
  date: Date;
  isToday: boolean;
  events: CalendarEvent[];
}

export interface WeekViewProps {
  events: CalendarEvent[];
  currentDate: Date;
  onDateChange: (date: Date) => void;
  onEventClick: (event: CalendarEvent) => void;
  onEventCreate?: (date: Date) => void;
  loading?: boolean;
}

export const WeekView: React.FC<WeekViewProps> = ({
  events,
  currentDate,
  onDateChange,
  onEventClick,
  onEventCreate,
  loading = false,
}) => {
  const timeSlots = useMemo(() => {
    const slots = [];
    for (let hour = 0; hour < 24; hour++) {
      slots.push({
        hour,
        time: new Date(0, 0, 0, hour).toLocaleTimeString('en-US', {
          hour: 'numeric',
          hour12: true,
        }),
      });
    }
    return slots;
  }, []);

  // Generate week days
  const weekDays = useMemo((): WeekDay[] => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());

    const days: WeekDay[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      date.setHours(0, 0, 0, 0);

      const dayEvents = events.filter(event => {
        const eventDate = new Date(event.startDate);
        eventDate.setHours(0, 0, 0, 0);
        return eventDate.getTime() === date.getTime();
      });

      days.push({
        date: new Date(date),
        isToday: date.getTime() === today.getTime(),
        events: dayEvents,
      });
    }

    return days;
  }, [currentDate, events]);

  // Get event position and height
  const getEventStyle = (event: CalendarEvent) => {
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    
    const startHour = start.getHours() + start.getMinutes() / 60;
    const endHour = end.getHours() + end.getMinutes() / 60;
    
    const top = (startHour / 24) * 100;
    const height = ((endHour - startHour) / 24) * 100;
    
    return {
      top: `${top}%`,
      height: `${Math.max(height, 4)}%`, // Minimum height for visibility
    };
  };

  // Get event color based on type and status
  const getEventColor = (event: CalendarEvent) => {
    if (event.status === 'cancelled') return 'bg-red-100 text-red-800 border-red-200';
    if (event.status === 'completed') return 'bg-green-100 text-green-800 border-green-200';
    
    switch (event.type) {
      case 'interview':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'meeting':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'deadline':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format event time
  const formatEventTime = (event: CalendarEvent) => {
    if (event.allDay) return 'All day';
    
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    
    const startTime = start.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    const endTime = end.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    return `${startTime} - ${endTime}`;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading calendar...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-0">
        <div className="flex">
          {/* Time Column */}
          <div className="w-20 border-r">
            <div className="h-12 border-b"></div> {/* Header spacer */}
            {timeSlots.map((slot) => (
              <div
                key={slot.hour}
                className="h-12 border-b text-xs text-muted-foreground p-2 text-right"
              >
                {slot.hour > 0 && slot.time}
              </div>
            ))}
          </div>

          {/* Days Columns */}
          <div className="flex-1">
            {/* Days Header */}
            <div className="grid grid-cols-7 border-b h-12">
              {weekDays.map((day, index) => (
                <div
                  key={index}
                  className={`p-2 text-center border-r last:border-r-0 cursor-pointer hover:bg-muted/50 ${
                    day.isToday ? 'bg-primary/5 text-primary font-semibold' : ''
                  }`}
                  onClick={() => onDateChange(day.date)}
                >
                  <div className="text-xs text-muted-foreground">
                    {day.date.toLocaleDateString('en-US', { weekday: 'short' })}
                  </div>
                  <div className={`text-sm ${day.isToday ? 'font-bold' : ''}`}>
                    {day.date.getDate()}
                  </div>
                </div>
              ))}
            </div>

            {/* Time Grid */}
            <div className="relative">
              {/* Grid Lines */}
              <div className="grid grid-cols-7">
                {weekDays.map((day, dayIndex) => (
                  <div key={dayIndex} className="border-r last:border-r-0">
                    {timeSlots.map((slot) => (
                      <div
                        key={slot.hour}
                        className="h-12 border-b hover:bg-muted/30 cursor-pointer"
                        onClick={() => {
                          const clickDate = new Date(day.date);
                          clickDate.setHours(slot.hour, 0, 0, 0);
                          onEventCreate?.(clickDate);
                        }}
                      />
                    ))}
                  </div>
                ))}
              </div>

              {/* Events */}
              {weekDays.map((day, dayIndex) => (
                <div
                  key={dayIndex}
                  className="absolute inset-0"
                  style={{
                    left: `${(dayIndex / 7) * 100}%`,
                    width: `${100 / 7}%`,
                  }}
                >
                  {day.events.map((event) => {
                    const style = getEventStyle(event);
                    return (
                      <div
                        key={event.id}
                        className={`absolute left-1 right-1 p-1 rounded border cursor-pointer hover:shadow-sm transition-shadow ${getEventColor(event)}`}
                        style={style}
                        onClick={() => onEventClick(event)}
                      >
                        <div className="text-xs font-medium truncate">
                          {event.title}
                        </div>
                        {!event.allDay && (
                          <div className="text-xs opacity-75 truncate">
                            {formatEventTime(event)}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              ))}

              {/* Current Time Indicator */}
              {weekDays.some(day => day.isToday) && (
                <div
                  className="absolute left-0 right-0 h-0.5 bg-red-500 z-10"
                  style={{
                    top: `${(new Date().getHours() + new Date().getMinutes() / 60) / 24 * 100}%`,
                  }}
                >
                  <div className="absolute -left-1 -top-1 w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
