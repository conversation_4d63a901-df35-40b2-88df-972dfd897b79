/**
 * Agenda View Component
 * Calendar agenda view for displaying events in a list format
 */

import React, { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  CheckCircle,
  XCircle,
  AlertCircle,
  Calendar as CalendarIcon,
} from 'lucide-react';
import { CalendarEvent } from '../../types';

interface AgendaDay {
  date: Date;
  isToday: boolean;
  events: CalendarEvent[];
}

export interface AgendaViewProps {
  events: CalendarEvent[];
  currentDate: Date;
  onDateChange: (date: Date) => void;
  onEventClick: (event: CalendarEvent) => void;
  onEventCreate?: (date: Date) => void;
  loading?: boolean;
}

export const AgendaView: React.FC<AgendaViewProps> = ({
  events,
  currentDate,
  onDateChange,
  onEventClick,
  onEventCreate,
  loading = false,
}) => {
  // Group events by date for the next 30 days
  const agendaDays = useMemo((): AgendaDay[] => {
    const days: AgendaDay[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Generate next 30 days
    for (let i = 0; i < 30; i++) {
      const date = new Date(currentDate);
      date.setDate(currentDate.getDate() + i);
      date.setHours(0, 0, 0, 0);

      const dayEvents = events
        .filter(event => {
          const eventDate = new Date(event.startDate);
          eventDate.setHours(0, 0, 0, 0);
          return eventDate.getTime() === date.getTime();
        })
        .sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());

      // Only include days with events or today
      if (dayEvents.length > 0 || date.getTime() === today.getTime()) {
        days.push({
          date: new Date(date),
          isToday: date.getTime() === today.getTime(),
          events: dayEvents,
        });
      }
    }

    return days;
  }, [currentDate, events]);

  // Get event type icon
  const getEventTypeIcon = (event: CalendarEvent) => {
    if (event.type === 'interview') {
      if (event.isVirtual) return <Video className="w-4 h-4" />;
      if (event.location?.toLowerCase().includes('phone')) return <Phone className="w-4 h-4" />;
      return <MapPin className="w-4 h-4" />;
    }
    return <CalendarIcon className="w-4 h-4" />;
  };

  // Get event status icon
  const getEventStatusIcon = (event: CalendarEvent) => {
    switch (event.status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'rescheduled':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Clock className="w-4 h-4 text-blue-600" />;
    }
  };

  // Get event priority color
  const getEventPriorityColor = (event: CalendarEvent) => {
    switch (event.priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  // Format event time
  const formatEventTime = (event: CalendarEvent) => {
    if (event.allDay) return 'All day';
    
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    
    const startTime = start.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    const endTime = end.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    return `${startTime} - ${endTime}`;
  };

  // Format relative date
  const formatRelativeDate = (date: Date) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      const diffTime = date.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays < 7) {
        return date.toLocaleDateString('en-US', { weekday: 'long' });
      } else {
        return date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        });
      }
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading agenda...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (agendaDays.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <CalendarIcon className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No upcoming events</h3>
            <p className="text-muted-foreground mb-4">
              You don't have any events scheduled for the next 30 days.
            </p>
            {onEventCreate && (
              <Button onClick={() => onEventCreate(new Date())}>
                Create Event
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {agendaDays.map((day, dayIndex) => (
        <Card key={dayIndex}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div>
                  <h3 className="text-lg font-semibold">
                    {formatRelativeDate(day.date)}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {day.date.toLocaleDateString('en-US', {
                      weekday: 'long',
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric',
                    })}
                  </p>
                </div>
                {day.isToday && (
                  <Badge variant="secondary">Today</Badge>
                )}
              </div>
              <div className="text-sm text-muted-foreground">
                {day.events.length} events
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {day.events.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">No events scheduled</p>
                {onEventCreate && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEventCreate(day.date)}
                  >
                    Add Event
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-3">
                {day.events.map((event, eventIndex) => (
                  <div key={event.id}>
                    <div
                      className={`p-3 rounded-lg border cursor-pointer hover:shadow-sm transition-shadow ${getEventPriorityColor(event)}`}
                      onClick={() => onEventClick(event)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            {getEventStatusIcon(event)}
                            <h4 className="font-semibold truncate">{event.title}</h4>
                            {getEventTypeIcon(event)}
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                            <div className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              <span>{formatEventTime(event)}</span>
                            </div>
                            
                            {event.location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                <span className="truncate">{event.location}</span>
                              </div>
                            )}
                            
                            {event.attendees && event.attendees.length > 0 && (
                              <div className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                <span>{event.attendees.length} attendees</span>
                              </div>
                            )}
                          </div>
                          
                          {event.description && (
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {event.description}
                            </p>
                          )}
                        </div>
                        
                        <div className="ml-4 flex flex-col items-end gap-2">
                          <Badge className={getEventPriorityColor(event)}>
                            {event.priority}
                          </Badge>
                          
                          {event.tags && event.tags.length > 0 && (
                            <div className="flex gap-1">
                              {event.tags.slice(0, 2).map((tag, tagIndex) => (
                                <Badge key={tagIndex} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                              {event.tags.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{event.tags.length - 2}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {eventIndex < day.events.length - 1 && (
                      <Separator className="my-3" />
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
