/**
 * Month View Component
 * Calendar month view for displaying events in a monthly grid
 */

import React, { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  CheckCircle,
  XCircle,
  AlertCircle,
  MoreHorizontal,
  Calendar as CalendarIcon,
} from 'lucide-react';
import { CalendarEvent } from '../../types';

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  events: CalendarEvent[];
}

export interface MonthViewProps {
  events: CalendarEvent[];
  currentDate: Date;
  selectedDate?: Date;
  onDateChange: (date: Date) => void;
  onEventClick: (event: CalendarEvent) => void;
  onEventCreate?: (date: Date) => void;
  loading?: boolean;
}

export const MonthView: React.FC<MonthViewProps> = ({
  events,
  currentDate,
  selectedDate,
  onDateChange,
  onEventClick,
  onEventCreate,
  loading = false,
}) => {
  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // Generate calendar days
  const calendarDays = useMemo((): CalendarDay[] => {
    const firstDayOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1
    );
    const lastDayOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      0
    );

    const startDate = new Date(firstDayOfMonth);
    startDate.setDate(startDate.getDate() - firstDayOfMonth.getDay());

    const endDate = new Date(lastDayOfMonth);
    endDate.setDate(endDate.getDate() + (6 - lastDayOfMonth.getDay()));

    const days: CalendarDay[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const dayDate = new Date(date);
      dayDate.setHours(0, 0, 0, 0);

      const dayEvents = events.filter(event => {
        const eventDate = new Date(event.startDate);
        eventDate.setHours(0, 0, 0, 0);
        return eventDate.getTime() === dayDate.getTime();
      });

      days.push({
        date: new Date(dayDate),
        isCurrentMonth: dayDate.getMonth() === currentDate.getMonth(),
        isToday: dayDate.getTime() === today.getTime(),
        isSelected: selectedDate ? dayDate.getTime() === selectedDate.getTime() : false,
        events: dayEvents,
      });
    }

    return days;
  }, [currentDate, events, selectedDate]);

  // Get event type icon
  const getEventTypeIcon = (event: CalendarEvent) => {
    if (event.type === 'interview') {
      if (event.isVirtual) return <Video className="w-3 h-3" />;
      if (event.location?.toLowerCase().includes('phone')) return <Phone className="w-3 h-3" />;
      return <MapPin className="w-3 h-3" />;
    }
    return <CalendarIcon className="w-3 h-3" />;
  };

  // Get event status icon
  const getEventStatusIcon = (event: CalendarEvent) => {
    switch (event.status) {
      case 'completed':
        return <CheckCircle className="w-3 h-3 text-green-600" />;
      case 'cancelled':
        return <XCircle className="w-3 h-3 text-red-600" />;
      case 'rescheduled':
        return <AlertCircle className="w-3 h-3 text-yellow-600" />;
      default:
        return <Clock className="w-3 h-3 text-blue-600" />;
    }
  };

  // Get event priority color
  const getEventPriorityColor = (event: CalendarEvent) => {
    switch (event.priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  // Format event time
  const formatEventTime = (event: CalendarEvent) => {
    if (event.allDay) return 'All day';
    
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    
    const startTime = start.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    const endTime = end.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    
    return `${startTime} - ${endTime}`;
  };

  // Handle date click
  const handleDateClick = (day: CalendarDay) => {
    if (day.isCurrentMonth) {
      onDateChange(day.date);
    }
  };

  // Handle event click
  const handleEventClick = (event: CalendarEvent, e: React.MouseEvent) => {
    e.stopPropagation();
    onEventClick(event);
  };

  // Handle create event
  const handleCreateEvent = (date: Date, e: React.MouseEvent) => {
    e.stopPropagation();
    onEventCreate?.(date);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading calendar...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-0">
        {/* Week Days Header */}
        <div className="grid grid-cols-7 border-b">
          {weekDays.map((day) => (
            <div
              key={day}
              className="p-3 text-center text-sm font-medium text-muted-foreground border-r last:border-r-0"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7">
          {calendarDays.map((day, index) => (
            <div
              key={index}
              className={`min-h-[120px] p-2 border-r border-b last:border-r-0 cursor-pointer transition-colors ${
                day.isCurrentMonth
                  ? 'bg-background hover:bg-muted/50'
                  : 'bg-muted/30 text-muted-foreground'
              } ${
                day.isToday
                  ? 'bg-primary/5 border-primary/20'
                  : ''
              } ${
                day.isSelected ? 'ring-2 ring-primary ring-offset-1' : ''
              }`}
              onClick={() => handleDateClick(day)}
            >
              <div className="space-y-1">
                {/* Date Header */}
                <div className="flex items-center justify-between">
                  <span
                    className={`text-sm ${
                      day.isToday
                        ? 'font-bold text-primary bg-primary/10 w-6 h-6 rounded-full flex items-center justify-center'
                        : day.isCurrentMonth
                          ? 'font-medium'
                          : 'text-muted-foreground'
                    }`}
                  >
                    {day.date.getDate()}
                  </span>
                  
                  {day.events.length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      {day.events.length}
                    </Badge>
                  )}
                </div>

                {/* Events */}
                <div className="space-y-1">
                  {day.events.slice(0, 3).map((event, eventIndex) => (
                    <Popover key={event.id}>
                      <PopoverTrigger asChild>
                        <div
                          className={`text-xs p-1 rounded border cursor-pointer hover:shadow-sm transition-shadow ${getEventPriorityColor(event)}`}
                          onClick={(e) => handleEventClick(event, e)}
                        >
                          <div className="flex items-center gap-1 truncate">
                            {getEventStatusIcon(event)}
                            <span className="truncate flex-1">{event.title}</span>
                            {getEventTypeIcon(event)}
                          </div>
                        </div>
                      </PopoverTrigger>
                      <PopoverContent className="w-80" side="right">
                        <div className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold">{event.title}</h4>
                              <p className="text-sm text-muted-foreground">
                                {formatEventTime(event)}
                              </p>
                            </div>
                            <div className="flex items-center gap-1">
                              {getEventStatusIcon(event)}
                              {getEventTypeIcon(event)}
                            </div>
                          </div>

                          {event.description && (
                            <p className="text-sm">{event.description}</p>
                          )}

                          {event.location && (
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <MapPin className="w-4 h-4" />
                              <span>{event.location}</span>
                            </div>
                          )}

                          {event.attendees && event.attendees.length > 0 && (
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <User className="w-4 h-4" />
                              <span>{event.attendees.length} attendees</span>
                            </div>
                          )}

                          <div className="flex items-center justify-between pt-2 border-t">
                            <Badge className={getEventPriorityColor(event)}>
                              {event.priority} priority
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => handleEventClick(event, e)}
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  ))}

                  {/* Show more indicator */}
                  {day.events.length > 3 && (
                    <div className="text-xs text-muted-foreground text-center py-1">
                      +{day.events.length - 3} more
                    </div>
                  )}

                  {/* Create event button for empty days */}
                  {day.events.length === 0 && day.isCurrentMonth && onEventCreate && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full h-6 text-xs opacity-0 hover:opacity-100 transition-opacity"
                      onClick={(e) => handleCreateEvent(day.date, e)}
                    >
                      + Add Event
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
