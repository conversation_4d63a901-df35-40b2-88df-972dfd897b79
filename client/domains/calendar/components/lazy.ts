/**
 * Lazy-loaded Calendar Components
 * Lazy loading configuration for calendar domain components
 */

import React from 'react';
import { withLazyLoading, ComponentLoadingFallbacks, createRetryableLazy } from '../../shared/utils/lazyLoading';

// Lazy load main components
export const LazyCalendarView = withLazyLoading(
  createRetryableLazy(() => import('./CalendarView')),
  {
    fallback: ComponentLoadingFallbacks.page,
  }
);

export const LazyCalendarFiltersPanel = withLazyLoading(
  createRetryableLazy(() => import('./CalendarFiltersPanel')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyEventModal = withLazyLoading(
  createRetryableLazy(() => import('./EventModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

// Lazy load view components
export const LazyMonthView = withLazyLoading(
  createRetryableLazy(() => import('./views/MonthView')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyWeekView = withLazyLoading(
  createRetryableLazy(() => import('./views/WeekView')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyDayView = withLazyLoading(
  createRetryableLazy(() => import('./views/DayView')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyAgendaView = withLazyLoading(
  createRetryableLazy(() => import('./views/AgendaView')),
  {
    fallback: ComponentLoadingFallbacks.table,
  }
);

// Lazy load utility components
export const LazyEventCard = withLazyLoading(
  createRetryableLazy(() => import('./utils/EventCard')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyEventList = withLazyLoading(
  createRetryableLazy(() => import('./utils/EventList')),
  {
    fallback: ComponentLoadingFallbacks.table,
  }
);

export const LazyCalendarToolbar = withLazyLoading(
  createRetryableLazy(() => import('./utils/CalendarToolbar')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyDatePicker = withLazyLoading(
  createRetryableLazy(() => import('./utils/DatePicker')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyTimeSlotPicker = withLazyLoading(
  createRetryableLazy(() => import('./utils/TimeSlotPicker')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyRecurrenceSelector = withLazyLoading(
  createRetryableLazy(() => import('./utils/RecurrenceSelector')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyTimezoneSelector = withLazyLoading(
  createRetryableLazy(() => import('./utils/TimezoneSelector')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyEventTypeBadge = withLazyLoading(
  createRetryableLazy(() => import('./utils/EventTypeBadge')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyEventStatusBadge = withLazyLoading(
  createRetryableLazy(() => import('./utils/EventStatusBadge')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

// Preload critical components
export const preloadCalendarComponents = () => {
  return Promise.all([
    import('./CalendarView'),
    import('./views/MonthView'),
    import('./views/WeekView'),
    import('./views/DayView'),
    import('./EventModal'),
  ]);
};

// Component map for dynamic loading
export const calendarComponentMap = {
  CalendarView: LazyCalendarView,
  CalendarFiltersPanel: LazyCalendarFiltersPanel,
  EventModal: LazyEventModal,
  MonthView: LazyMonthView,
  WeekView: LazyWeekView,
  DayView: LazyDayView,
  AgendaView: LazyAgendaView,
  EventCard: LazyEventCard,
  EventList: LazyEventList,
  CalendarToolbar: LazyCalendarToolbar,
  DatePicker: LazyDatePicker,
  TimeSlotPicker: LazyTimeSlotPicker,
  RecurrenceSelector: LazyRecurrenceSelector,
  TimezoneSelector: LazyTimezoneSelector,
  EventTypeBadge: LazyEventTypeBadge,
  EventStatusBadge: LazyEventStatusBadge,
} as const;

export type CalendarComponentName = keyof typeof calendarComponentMap;
