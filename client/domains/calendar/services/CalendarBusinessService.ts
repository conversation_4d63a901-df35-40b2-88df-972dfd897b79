/**
 * Calendar Business Service
 * Handles calendar business logic and data transformation
 */

import { CalendarApiService } from './CalendarApiService';
import { notifications } from '@/shared/utils';
import {
  CalendarEvent,
  CreateCalendarEventData,
  UpdateCalendarEventData,
  CalendarFilters,
  CalendarStatistics,
  CalendarAvailability,
  CalendarDashboard,
  CalendarExportOptions,
  CalendarImportResult,
  CalendarBulkOperation,
  CalendarSearchResult,
  CalendarEventQueryParams,
  CalendarConflict,
  TimeSlot
} from '../types';
import { Interview } from '@/domains/interviews/types';

export class CalendarBusinessService {
  constructor(private apiService: CalendarApiService) {}

  /**
   * Get calendar events with business logic applied
   */
  async getEvents(params?: CalendarEventQueryParams) {
    try {
      const response = await this.apiService.getEvents(params);
      
      return {
        ...response,
        data: response.data?.map(this.enrichEventData) || [],
      };
    } catch (error) {
      notifications.error.generic('Failed to load calendar events');
      throw error;
    }
  }

  /**
   * Get calendar events for calendar view (legacy compatibility)
   */
  async getCalendarEvents(params: {
    start_date: string;
    end_date: string;
    interviewer_id?: string;
    type?: string[];
    status?: string[];
  }) {
    try {
      const response = await this.apiService.getCalendarEvents(params);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: response.data.map(this.enrichEventData),
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to load calendar events');
      throw error;
    }
  }

  /**
   * Get calendar event by ID
   */
  async getEventById(id: string) {
    try {
      const response = await this.apiService.getEventById(id);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: this.enrichEventData(response.data),
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to load calendar event');
      throw error;
    }
  }

  /**
   * Create calendar event with validation
   */
  async createEvent(data: CreateCalendarEventData) {
    try {
      // Validate event data
      this.validateEventData(data);
      
      // Check for conflicts
      const conflicts = await this.checkEventConflicts(data);
      if (conflicts.length > 0) {
        const hasHardConflicts = conflicts.some(c => c.severity === 'high');
        if (hasHardConflicts) {
          throw new Error('Cannot create event due to scheduling conflicts');
        }
      }
      
      const response = await this.apiService.createEvent(data);
      
      if (response.status === 'success') {
        notifications.success.generic('Calendar event created successfully');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to create calendar event');
      throw error;
    }
  }

  /**
   * Update calendar event with validation
   */
  async updateEvent(id: string, data: UpdateCalendarEventData) {
    try {
      // If updating timing, check for conflicts
      if (data.startDate || data.endDate) {
        const currentEvent = await this.apiService.getEventById(id);
        if (currentEvent.status === 'success' && currentEvent.data) {
          const updatedEventData = { ...currentEvent.data, ...data };
          const conflicts = await this.checkEventConflicts(updatedEventData, id);
          
          if (conflicts.length > 0) {
            const hasHardConflicts = conflicts.some(c => c.severity === 'high');
            if (hasHardConflicts) {
              throw new Error('Cannot update event due to scheduling conflicts');
            }
          }
        }
      }
      
      const response = await this.apiService.updateEvent(id, data);
      
      if (response.status === 'success') {
        notifications.success.generic('Calendar event updated successfully');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to update calendar event');
      throw error;
    }
  }

  /**
   * Delete calendar event
   */
  async deleteEvent(id: string) {
    try {
      const response = await this.apiService.deleteEvent(id);
      
      if (response.status === 'success') {
        notifications.success.generic('Calendar event deleted successfully');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to delete calendar event');
      throw error;
    }
  }

  /**
   * Get calendar statistics with computed metrics
   */
  async getStatistics(filters?: CalendarFilters) {
    try {
      const response = await this.apiService.getStatistics(filters);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: this.enrichStatistics(response.data),
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to load calendar statistics');
      throw error;
    }
  }

  /**
   * Get calendar dashboard with enriched data
   */
  async getDashboard(params?: { date_from?: string; date_to?: string }) {
    try {
      const response = await this.apiService.getDashboard(params);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: {
            ...response.data,
            upcomingEvents: response.data.upcomingEvents.map(this.enrichEventData),
            todayEvents: response.data.todayEvents.map(this.enrichEventData),
            conflicts: response.data.conflicts.map(this.enrichConflictData),
          },
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to load calendar dashboard');
      throw error;
    }
  }

  /**
   * Get availability with smart suggestions
   */
  async getAvailability(params: {
    start_date: string;
    end_date: string;
    interviewer_id?: string;
    duration?: number;
  }) {
    try {
      const response = await this.apiService.getAvailability(params);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: response.data.map(this.enrichAvailabilityData),
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to load availability');
      throw error;
    }
  }

  /**
   * Search events with enhanced results
   */
  async searchEvents(params: {
    query: string;
    filters?: CalendarFilters;
    page?: number;
    limit?: number;
  }) {
    try {
      const response = await this.apiService.searchEvents(params);
      
      if (response.status === 'success' && response.data) {
        return {
          ...response,
          data: {
            ...response.data,
            events: response.data.events.map(this.enrichEventData),
          },
        };
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to search calendar events');
      throw error;
    }
  }

  /**
   * Export calendar events
   */
  async exportEvents(options: CalendarExportOptions) {
    try {
      const response = await this.apiService.exportEvents(options);
      
      if (response.status === 'success') {
        notifications.success.generic('Calendar events exported successfully');
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to export calendar events');
      throw error;
    }
  }

  /**
   * Import calendar events
   */
  async importEvents(file: File, options?: {
    format?: 'ics' | 'csv';
    merge_strategy?: 'skip' | 'update' | 'replace';
  }) {
    try {
      const response = await this.apiService.importEvents(file, options);
      
      if (response.status === 'success' && response.data) {
        const result = response.data;
        notifications.success.generic(
          `Imported ${result.imported} events successfully. ${result.failed} failed, ${result.duplicates} duplicates.`
        );
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to import calendar events');
      throw error;
    }
  }

  /**
   * Perform bulk operations
   */
  async bulkOperation(operation: CalendarBulkOperation) {
    try {
      const response = await this.apiService.bulkOperation(operation);
      
      if (response.status === 'success' && response.data) {
        const result = response.data;
        notifications.success.generic(
          `Bulk operation completed. ${result.success} successful, ${result.failed} failed.`
        );
      }
      
      return response;
    } catch (error) {
      notifications.error.generic('Failed to perform bulk operation');
      throw error;
    }
  }

  /**
   * Convert interview to calendar event
   */
  convertInterviewToEvent(interview: Interview): CalendarEvent {
    return {
      id: `interview-${interview.id}`,
      title: `Interview: ${interview.candidateName}`,
      description: `${interview.type} interview for ${interview.jobTitle}`,
      type: 'interview',
      status: this.mapInterviewStatusToEventStatus(interview.status),
      priority: this.mapInterviewPriorityToEventPriority(interview.priority || 'medium'),
      startDate: interview.scheduledAt,
      endDate: interview.endTime || this.calculateEndTime(interview.scheduledAt, interview.duration || 60),
      allDay: false,
      timezone: 'UTC',
      location: interview.location,
      isVirtual: interview.type === 'video',
      meetingUrl: interview.meetingUrl,
      organizerId: interview.interviewerId,
      organizerName: interview.interviewerName,
      organizerEmail: interview.interviewerEmail,
      attendees: [
        {
          name: interview.candidateName,
          email: interview.candidateEmail || '',
          role: 'required',
          status: 'pending',
        },
        {
          name: interview.interviewerName,
          email: interview.interviewerEmail || '',
          role: 'organizer',
          status: 'accepted',
        },
      ],
      interviewId: interview.id,
      candidateId: interview.candidateId,
      jobId: interview.jobId,
      color: this.getEventColor(interview.type, interview.status),
      tags: [interview.type, interview.round || 'general'],
      isRecurring: false,
      reminders: [
        {
          id: `reminder-${interview.id}-1`,
          type: 'email',
          minutesBefore: 60,
          sent: false,
        },
        {
          id: `reminder-${interview.id}-2`,
          type: 'notification',
          minutesBefore: 15,
          sent: false,
        },
      ],
      createdAt: interview.createdAt,
      updatedAt: interview.updatedAt,
    };
  }

  /**
   * Enrich event data with computed properties
   */
  private enrichEventData = (event: CalendarEvent): CalendarEvent => {
    return {
      ...event,
      // Add computed properties
      isUpcoming: new Date(event.startDate) > new Date(),
      isPast: new Date(event.endDate) < new Date(),
      isToday: this.isToday(event.startDate),
      duration: this.calculateDuration(event.startDate, event.endDate),
      formattedDuration: this.formatDuration(event.startDate, event.endDate),
      canEdit: this.canEditEvent(event),
      canDelete: this.canDeleteEvent(event),
    } as CalendarEvent;
  };

  /**
   * Enrich statistics with computed metrics
   */
  private enrichStatistics = (stats: CalendarStatistics): CalendarStatistics => {
    const total = stats.totalEvents;
    
    return {
      ...stats,
      // Add percentage calculations
      completionRate: total > 0 ? (stats.eventsByStatus.completed || 0) / total * 100 : 0,
      cancellationRate: total > 0 ? (stats.eventsByStatus.cancelled || 0) / total * 100 : 0,
      utilizationRate: this.calculateUtilizationRate(stats),
    } as CalendarStatistics;
  };

  /**
   * Enrich conflict data
   */
  private enrichConflictData = (conflict: CalendarConflict): CalendarConflict => {
    return {
      ...conflict,
      events: conflict.events.map(this.enrichEventData),
      resolutionOptions: this.generateResolutionOptions(conflict),
    } as CalendarConflict;
  };

  /**
   * Enrich availability data
   */
  private enrichAvailabilityData = (availability: CalendarAvailability): CalendarAvailability => {
    return {
      ...availability,
      timeSlots: availability.timeSlots.map(this.enrichTimeSlot),
      availableSlots: availability.timeSlots.filter(slot => slot.available).length,
      busySlots: availability.timeSlots.filter(slot => !slot.available).length,
      utilizationPercentage: this.calculateDayUtilization(availability.timeSlots),
    } as CalendarAvailability;
  };

  /**
   * Enrich time slot data
   */
  private enrichTimeSlot = (slot: TimeSlot): TimeSlot => {
    return {
      ...slot,
      duration: this.calculateSlotDuration(slot.start, slot.end),
      isBusinessHours: this.isBusinessHours(slot.start),
      isPeakTime: this.isPeakTime(slot.start),
    } as TimeSlot;
  };

  /**
   * Validate event data
   */
  private validateEventData(data: CreateCalendarEventData | UpdateCalendarEventData): void {
    if (data.startDate && data.endDate) {
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);
      
      if (start >= end) {
        throw new Error('End date must be after start date');
      }
      
      if (start < new Date()) {
        throw new Error('Cannot schedule events in the past');
      }
    }
    
    if (data.attendees && data.attendees.length > 50) {
      throw new Error('Cannot have more than 50 attendees');
    }
  }

  /**
   * Check for event conflicts
   */
  private async checkEventConflicts(
    eventData: CreateCalendarEventData | CalendarEvent, 
    excludeEventId?: string
  ): Promise<CalendarConflict[]> {
    // This would typically call an API endpoint to check conflicts
    // For now, return empty array
    return [];
  }

  // Helper methods
  private mapInterviewStatusToEventStatus(status: string): any {
    const mapping: Record<string, any> = {
      'scheduled': 'scheduled',
      'in-progress': 'confirmed',
      'completed': 'completed',
      'cancelled': 'cancelled',
      'no-show': 'cancelled',
      'rescheduled': 'rescheduled',
    };
    return mapping[status] || 'scheduled';
  }

  private mapInterviewPriorityToEventPriority(priority: string): any {
    return priority as any;
  }

  private calculateEndTime(startTime: string, durationMinutes: number): string {
    const start = new Date(startTime);
    const end = new Date(start.getTime() + durationMinutes * 60000);
    return end.toISOString();
  }

  private getEventColor(type: string, status: string): string {
    const colors: Record<string, string> = {
      'phone': '#3B82F6',
      'video': '#10B981',
      'in-person': '#F59E0B',
      'technical': '#8B5CF6',
      'behavioral': '#EF4444',
    };
    return colors[type] || '#6B7280';
  }

  private isToday(date: string): boolean {
    const today = new Date();
    const eventDate = new Date(date);
    return today.toDateString() === eventDate.toDateString();
  }

  private calculateDuration(start: string, end: string): number {
    return Math.round((new Date(end).getTime() - new Date(start).getTime()) / 60000);
  }

  private formatDuration(start: string, end: string): string {
    const minutes = this.calculateDuration(start, end);
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
  }

  private canEditEvent(event: CalendarEvent): boolean {
    return event.status !== 'completed' && event.status !== 'cancelled';
  }

  private canDeleteEvent(event: CalendarEvent): boolean {
    return event.status === 'scheduled' || event.status === 'cancelled';
  }

  private calculateUtilizationRate(stats: CalendarStatistics): number {
    // Simple utilization calculation
    const activeEvents = stats.totalEvents - (stats.eventsByStatus.cancelled || 0);
    return stats.totalEvents > 0 ? (activeEvents / stats.totalEvents) * 100 : 0;
  }

  private generateResolutionOptions(conflict: CalendarConflict): string[] {
    // Generate smart resolution suggestions
    return [
      'Reschedule one of the conflicting events',
      'Shorten the duration of overlapping events',
      'Assign different interviewers',
    ];
  }

  private calculateDayUtilization(slots: TimeSlot[]): number {
    const totalSlots = slots.length;
    const busySlots = slots.filter(slot => !slot.available).length;
    return totalSlots > 0 ? (busySlots / totalSlots) * 100 : 0;
  }

  private calculateSlotDuration(start: string, end: string): number {
    const [startHour, startMin] = start.split(':').map(Number);
    const [endHour, endMin] = end.split(':').map(Number);
    return (endHour * 60 + endMin) - (startHour * 60 + startMin);
  }

  private isBusinessHours(time: string): boolean {
    const [hour] = time.split(':').map(Number);
    return hour >= 9 && hour < 17;
  }

  private isPeakTime(time: string): boolean {
    const [hour] = time.split(':').map(Number);
    return (hour >= 10 && hour < 12) || (hour >= 14 && hour < 16);
  }
}
