/**
 * Calendar API Service
 * Handles all calendar-related API operations
 */

import { BaseService } from '@/core/api';
import { ApiClient, ApiResponse, PaginatedResponse } from '@/core/api';
import {
  CalendarEvent,
  CreateCalendarEventData,
  UpdateCalendarEventData,
  CalendarEventQueryParams,
  CalendarFilters,
  CalendarStatistics,
  CalendarAvailability,
  CalendarDashboard,
  CalendarExportOptions,
  CalendarImportResult,
  CalendarBulkOperation,
  CalendarSearchResult,
  CalendarSyncSettings,
  CalendarIntegration,
  CalendarNotification
} from '../types';

export class CalendarApiService extends BaseService<CalendarEvent, CreateCalendarEventData, UpdateCalendarEventData> {
  constructor(apiClient: ApiClient) {
    super(apiClient, '/calendar');
  }

  /**
   * Get calendar events with advanced filtering
   */
  async getEvents(params?: CalendarEventQueryParams): Promise<PaginatedResponse<CalendarEvent>> {
    const queryParams = this.buildEventQueryParams(params);
    return this.apiClient.get<CalendarEvent[]>(`${this.baseEndpoint}/events`, queryParams);
  }

  /**
   * Get calendar events for a specific date range (used by calendar views)
   */
  async getCalendarEvents(params: {
    start_date: string;
    end_date: string;
    interviewer_id?: string;
    type?: string[];
    status?: string[];
  }): Promise<ApiResponse<CalendarEvent[]>> {
    const queryParams = new URLSearchParams();
    queryParams.append('start_date', params.start_date);
    queryParams.append('end_date', params.end_date);
    
    if (params.interviewer_id) {
      queryParams.append('interviewer_id', params.interviewer_id);
    }
    
    if (params.type?.length) {
      params.type.forEach(t => queryParams.append('type[]', t));
    }
    
    if (params.status?.length) {
      params.status.forEach(s => queryParams.append('status[]', s));
    }

    return this.apiClient.get<CalendarEvent[]>(`/interviews/calendar/events?${queryParams}`);
  }

  /**
   * Get calendar event by ID
   */
  async getEventById(id: string): Promise<ApiResponse<CalendarEvent>> {
    return this.getById(id);
  }

  /**
   * Create new calendar event
   */
  async createEvent(data: CreateCalendarEventData): Promise<ApiResponse<CalendarEvent>> {
    return this.create(data);
  }

  /**
   * Update calendar event
   */
  async updateEvent(id: string, data: UpdateCalendarEventData): Promise<ApiResponse<CalendarEvent>> {
    return this.update(id, data);
  }

  /**
   * Delete calendar event
   */
  async deleteEvent(id: string): Promise<ApiResponse<void>> {
    return this.delete(id);
  }

  /**
   * Get calendar statistics
   */
  async getStatistics(filters?: CalendarFilters): Promise<ApiResponse<CalendarStatistics>> {
    const queryParams = this.buildFilterQueryParams(filters);
    return this.apiClient.get<CalendarStatistics>(`${this.baseEndpoint}/statistics`, queryParams);
  }

  /**
   * Get calendar dashboard data
   */
  async getDashboard(params?: {
    date_from?: string;
    date_to?: string;
  }): Promise<ApiResponse<CalendarDashboard>> {
    const queryParams = new URLSearchParams();
    if (params?.date_from) queryParams.append('date_from', params.date_from);
    if (params?.date_to) queryParams.append('date_to', params.date_to);
    
    return this.apiClient.get<CalendarDashboard>(`${this.baseEndpoint}/dashboard?${queryParams}`);
  }

  /**
   * Get availability for a date range
   */
  async getAvailability(params: {
    start_date: string;
    end_date: string;
    interviewer_id?: string;
    duration?: number; // minutes
  }): Promise<ApiResponse<CalendarAvailability[]>> {
    const queryParams = new URLSearchParams();
    queryParams.append('start_date', params.start_date);
    queryParams.append('end_date', params.end_date);
    
    if (params.interviewer_id) {
      queryParams.append('interviewer_id', params.interviewer_id);
    }
    
    if (params.duration) {
      queryParams.append('duration', params.duration.toString());
    }

    return this.apiClient.get<CalendarAvailability[]>(`${this.baseEndpoint}/availability?${queryParams}`);
  }

  /**
   * Search calendar events
   */
  async searchEvents(params: {
    query: string;
    filters?: CalendarFilters;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<CalendarSearchResult>> {
    const queryParams = new URLSearchParams();
    queryParams.append('q', params.query);
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    
    // Add filters
    if (params.filters) {
      const filterParams = this.buildFilterQueryParams(params.filters);
      filterParams.forEach((value, key) => queryParams.append(key, value));
    }

    return this.apiClient.get<CalendarSearchResult>(`${this.baseEndpoint}/search?${queryParams}`);
  }

  /**
   * Export calendar events
   */
  async exportEvents(options: CalendarExportOptions): Promise<ApiResponse<Blob>> {
    return this.apiClient.post<Blob>(`${this.baseEndpoint}/export`, options, {
      responseType: 'blob'
    });
  }

  /**
   * Import calendar events
   */
  async importEvents(file: File, options?: {
    format?: 'ics' | 'csv';
    merge_strategy?: 'skip' | 'update' | 'replace';
  }): Promise<ApiResponse<CalendarImportResult>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (options?.format) {
      formData.append('format', options.format);
    }
    
    if (options?.merge_strategy) {
      formData.append('merge_strategy', options.merge_strategy);
    }

    return this.apiClient.post<CalendarImportResult>(`${this.baseEndpoint}/import`, formData);
  }

  /**
   * Perform bulk operations on calendar events
   */
  async bulkOperation(operation: CalendarBulkOperation): Promise<ApiResponse<{ success: number; failed: number; errors: string[] }>> {
    return this.apiClient.post<{ success: number; failed: number; errors: string[] }>(`${this.baseEndpoint}/bulk`, operation);
  }

  /**
   * Get calendar sync settings
   */
  async getSyncSettings(): Promise<ApiResponse<CalendarSyncSettings[]>> {
    return this.apiClient.get<CalendarSyncSettings[]>(`${this.baseEndpoint}/sync/settings`);
  }

  /**
   * Update calendar sync settings
   */
  async updateSyncSettings(settings: Partial<CalendarSyncSettings>): Promise<ApiResponse<CalendarSyncSettings>> {
    return this.apiClient.put<CalendarSyncSettings>(`${this.baseEndpoint}/sync/settings`, settings);
  }

  /**
   * Trigger calendar sync
   */
  async triggerSync(provider?: string): Promise<ApiResponse<{ status: string; message: string }>> {
    const params = provider ? { provider } : {};
    return this.apiClient.post<{ status: string; message: string }>(`${this.baseEndpoint}/sync/trigger`, params);
  }

  /**
   * Get calendar integrations
   */
  async getIntegrations(): Promise<ApiResponse<CalendarIntegration[]>> {
    return this.apiClient.get<CalendarIntegration[]>(`${this.baseEndpoint}/integrations`);
  }

  /**
   * Create calendar integration
   */
  async createIntegration(data: {
    provider: string;
    access_token: string;
    refresh_token?: string;
    calendar_id?: string;
  }): Promise<ApiResponse<CalendarIntegration>> {
    return this.apiClient.post<CalendarIntegration>(`${this.baseEndpoint}/integrations`, data);
  }

  /**
   * Delete calendar integration
   */
  async deleteIntegration(id: string): Promise<ApiResponse<void>> {
    return this.apiClient.delete<void>(`${this.baseEndpoint}/integrations/${id}`);
  }

  /**
   * Get calendar notifications
   */
  async getNotifications(params?: {
    event_id?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<CalendarNotification>> {
    const queryParams = new URLSearchParams();
    
    if (params?.event_id) queryParams.append('event_id', params.event_id);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    return this.apiClient.get<CalendarNotification[]>(`${this.baseEndpoint}/notifications?${queryParams}`);
  }

  /**
   * Send calendar reminders
   */
  async sendReminders(params: {
    event_ids: string[];
    hours_before: number;
    type?: 'email' | 'notification' | 'sms';
  }): Promise<ApiResponse<{ sent_count: number; failed_count: number }>> {
    return this.apiClient.post<{ sent_count: number; failed_count: number }>(`${this.baseEndpoint}/reminders/send`, params);
  }

  /**
   * Build query parameters for event filtering
   */
  private buildEventQueryParams(params?: CalendarEventQueryParams): URLSearchParams {
    const queryParams = new URLSearchParams();
    
    if (!params) return queryParams;

    if (params.startDate) queryParams.append('start_date', params.startDate);
    if (params.endDate) queryParams.append('end_date', params.endDate);
    if (params.search) queryParams.append('search', params.search);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sort) queryParams.append('sort', params.sort);
    
    // Array parameters
    if (params.type?.length) {
      params.type.forEach(t => queryParams.append('type[]', t));
    }
    
    if (params.status?.length) {
      params.status.forEach(s => queryParams.append('status[]', s));
    }
    
    if (params.priority?.length) {
      params.priority.forEach(p => queryParams.append('priority[]', p));
    }
    
    if (params.tags?.length) {
      params.tags.forEach(tag => queryParams.append('tags[]', tag));
    }
    
    if (params.include?.length) {
      params.include.forEach(inc => queryParams.append('include[]', inc));
    }

    // Single value parameters
    if (params.interviewerId) queryParams.append('interviewer_id', params.interviewerId);
    if (params.candidateId) queryParams.append('candidate_id', params.candidateId);
    if (params.jobId) queryParams.append('job_id', params.jobId);

    return queryParams;
  }

  /**
   * Build query parameters for calendar filters
   */
  private buildFilterQueryParams(filters?: CalendarFilters): URLSearchParams {
    const queryParams = new URLSearchParams();
    
    if (!filters) return queryParams;

    if (filters.startDate) queryParams.append('start_date', filters.startDate);
    if (filters.endDate) queryParams.append('end_date', filters.endDate);
    if (filters.search) queryParams.append('search', filters.search);
    
    // Array parameters
    if (filters.types?.length) {
      filters.types.forEach(t => queryParams.append('types[]', t));
    }
    
    if (filters.statuses?.length) {
      filters.statuses.forEach(s => queryParams.append('statuses[]', s));
    }
    
    if (filters.priorities?.length) {
      filters.priorities.forEach(p => queryParams.append('priorities[]', p));
    }
    
    if (filters.tags?.length) {
      filters.tags.forEach(tag => queryParams.append('tags[]', tag));
    }

    // Single value parameters
    if (filters.interviewerId) queryParams.append('interviewer_id', filters.interviewerId);
    if (filters.candidateId) queryParams.append('candidate_id', filters.candidateId);
    if (filters.jobId) queryParams.append('job_id', filters.jobId);

    return queryParams;
  }
}
