/**
 * Calendar Service
 * Main service that combines API and business logic for calendar operations
 */

import { ApiClient } from '@/core/api';
import { CalendarApiService } from './CalendarApiService';
import { CalendarBusinessService } from './CalendarBusinessService';
import {
  CalendarEvent,
  CreateCalendarEventData,
  UpdateCalendarEventData,
  CalendarFilters,
  CalendarStatistics,
  CalendarAvailability,
  CalendarDashboard,
  CalendarExportOptions,
  CalendarImportResult,
  CalendarBulkOperation,
  CalendarSearchResult,
  CalendarEventQueryParams,
  CalendarSyncSettings,
  CalendarIntegration,
  CalendarNotification
} from '../types';
import { Interview } from '@/domains/interviews/types';

class CalendarServiceClass {
  private apiService: CalendarApiService;
  private businessService: CalendarBusinessService;

  constructor() {
    const apiClient = new ApiClient();
    this.apiService = new CalendarApiService(apiClient);
    this.businessService = new CalendarBusinessService(this.apiService);
  }

  // Event Management
  async getEvents(params?: CalendarEventQueryParams) {
    return this.businessService.getEvents(params);
  }

  async getCalendarEvents(params: {
    start_date: string;
    end_date: string;
    interviewer_id?: string;
    type?: string[];
    status?: string[];
  }) {
    return this.businessService.getCalendarEvents(params);
  }

  async getEventById(id: string) {
    return this.businessService.getEventById(id);
  }

  async createEvent(data: CreateCalendarEventData) {
    return this.businessService.createEvent(data);
  }

  async updateEvent(id: string, data: UpdateCalendarEventData) {
    return this.businessService.updateEvent(id, data);
  }

  async deleteEvent(id: string) {
    return this.businessService.deleteEvent(id);
  }

  // Statistics and Dashboard
  async getStatistics(filters?: CalendarFilters) {
    return this.businessService.getStatistics(filters);
  }

  async getDashboard(params?: { date_from?: string; date_to?: string }) {
    return this.businessService.getDashboard(params);
  }

  // Availability
  async getAvailability(params: {
    start_date: string;
    end_date: string;
    interviewer_id?: string;
    duration?: number;
  }) {
    return this.businessService.getAvailability(params);
  }

  // Search
  async searchEvents(params: {
    query: string;
    filters?: CalendarFilters;
    page?: number;
    limit?: number;
  }) {
    return this.businessService.searchEvents(params);
  }

  // Import/Export
  async exportEvents(options: CalendarExportOptions) {
    return this.businessService.exportEvents(options);
  }

  async importEvents(file: File, options?: {
    format?: 'ics' | 'csv';
    merge_strategy?: 'skip' | 'update' | 'replace';
  }) {
    return this.businessService.importEvents(file, options);
  }

  // Bulk Operations
  async bulkOperation(operation: CalendarBulkOperation) {
    return this.businessService.bulkOperation(operation);
  }

  // Sync and Integration
  async getSyncSettings() {
    return this.apiService.getSyncSettings();
  }

  async updateSyncSettings(settings: Partial<CalendarSyncSettings>) {
    return this.apiService.updateSyncSettings(settings);
  }

  async triggerSync(provider?: string) {
    return this.apiService.triggerSync(provider);
  }

  async getIntegrations() {
    return this.apiService.getIntegrations();
  }

  async createIntegration(data: {
    provider: string;
    access_token: string;
    refresh_token?: string;
    calendar_id?: string;
  }) {
    return this.apiService.createIntegration(data);
  }

  async deleteIntegration(id: string) {
    return this.apiService.deleteIntegration(id);
  }

  // Notifications
  async getNotifications(params?: {
    event_id?: string;
    status?: string;
    page?: number;
    limit?: number;
  }) {
    return this.apiService.getNotifications(params);
  }

  async sendReminders(params: {
    event_ids: string[];
    hours_before: number;
    type?: 'email' | 'notification' | 'sms';
  }) {
    return this.apiService.sendReminders(params);
  }

  // Utility Methods
  convertInterviewToEvent(interview: Interview): CalendarEvent {
    return this.businessService.convertInterviewToEvent(interview);
  }

  // Convenience methods for common operations
  async getTodayEvents() {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

    return this.getEvents({
      startDate: startOfDay.toISOString(),
      endDate: endOfDay.toISOString(),
    });
  }

  async getUpcomingEvents(limit: number = 10) {
    const now = new Date();
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    return this.getEvents({
      startDate: now.toISOString(),
      endDate: nextWeek.toISOString(),
      limit,
      sort: 'start_date',
    });
  }

  async getEventsByDateRange(startDate: string, endDate: string) {
    return this.getEvents({
      startDate,
      endDate,
      sort: 'start_date',
    });
  }

  async getEventsByType(type: string, startDate?: string, endDate?: string) {
    return this.getEvents({
      type: [type as any],
      startDate,
      endDate,
      sort: 'start_date',
    });
  }

  async getEventsByStatus(status: string, startDate?: string, endDate?: string) {
    return this.getEvents({
      status: [status as any],
      startDate,
      endDate,
      sort: 'start_date',
    });
  }

  async getInterviewerEvents(interviewerId: string, startDate?: string, endDate?: string) {
    return this.getEvents({
      interviewerId,
      startDate,
      endDate,
      sort: 'start_date',
    });
  }

  async getCandidateEvents(candidateId: string) {
    return this.getEvents({
      candidateId,
      sort: 'start_date',
    });
  }

  async getJobEvents(jobId: string) {
    return this.getEvents({
      jobId,
      sort: 'start_date',
    });
  }

  // Calendar view helpers
  async getMonthEvents(year: number, month: number) {
    const startOfMonth = new Date(year, month, 1);
    const endOfMonth = new Date(year, month + 1, 0, 23, 59, 59);

    return this.getEvents({
      startDate: startOfMonth.toISOString(),
      endDate: endOfMonth.toISOString(),
      sort: 'start_date',
    });
  }

  async getWeekEvents(startOfWeek: Date) {
    const endOfWeek = new Date(startOfWeek.getTime() + 6 * 24 * 60 * 60 * 1000);
    endOfWeek.setHours(23, 59, 59, 999);

    return this.getEvents({
      startDate: startOfWeek.toISOString(),
      endDate: endOfWeek.toISOString(),
      sort: 'start_date',
    });
  }

  async getDayEvents(date: Date) {
    const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);

    return this.getEvents({
      startDate: startOfDay.toISOString(),
      endDate: endOfDay.toISOString(),
      sort: 'start_date',
    });
  }

  // Conflict detection
  async checkConflicts(eventData: CreateCalendarEventData | UpdateCalendarEventData, excludeEventId?: string) {
    // This would call a dedicated conflict detection endpoint
    // For now, we'll implement basic logic
    const events = await this.getEvents({
      startDate: eventData.startDate,
      endDate: eventData.endDate,
    });

    if (events.status === 'success' && events.data) {
      const conflicts = events.data.filter(event => {
        if (excludeEventId && event.id === excludeEventId) {
          return false;
        }

        const eventStart = new Date(event.startDate);
        const eventEnd = new Date(event.endDate);
        const newStart = new Date(eventData.startDate!);
        const newEnd = new Date(eventData.endDate!);

        // Check for overlap
        return (newStart < eventEnd && newEnd > eventStart);
      });

      return conflicts;
    }

    return [];
  }

  // Time zone utilities
  convertToTimezone(date: string, timezone: string): string {
    // This would use a proper timezone library like date-fns-tz
    return new Date(date).toLocaleString('en-US', { timeZone: timezone });
  }

  formatEventTime(event: CalendarEvent, timezone?: string): string {
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    
    if (event.allDay) {
      return 'All day';
    }

    const timeZone = timezone || event.timezone || 'UTC';
    const startTime = start.toLocaleTimeString('en-US', { 
      timeZone, 
      hour: 'numeric', 
      minute: '2-digit' 
    });
    const endTime = end.toLocaleTimeString('en-US', { 
      timeZone, 
      hour: 'numeric', 
      minute: '2-digit' 
    });

    return `${startTime} - ${endTime}`;
  }

  // Event validation
  validateEventData(data: CreateCalendarEventData | UpdateCalendarEventData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (data.startDate && data.endDate) {
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);

      if (start >= end) {
        errors.push('End time must be after start time');
      }

      if (start < new Date()) {
        errors.push('Cannot schedule events in the past');
      }
    }

    if (data.title && data.title.trim().length === 0) {
      errors.push('Event title is required');
    }

    if (data.attendees && data.attendees.length > 50) {
      errors.push('Cannot have more than 50 attendees');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const CalendarService = new CalendarServiceClass();
