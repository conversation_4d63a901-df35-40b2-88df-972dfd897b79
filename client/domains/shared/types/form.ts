/**
 * Form Types
 * Common form-related types and interfaces
 */

export interface FormFieldOption {
  label: string;
  value: string | number;
  disabled?: boolean;
  description?: string;
}

export interface FormFieldConfig {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'time' | 'datetime' | 'file' | 'group';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  
  // Validation
  validation?: {
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    custom?: (value: any) => string | null;
  };
  
  // Options for select/radio fields
  options?: FormFieldOption[];
  
  // For group fields
  fields?: FormFieldConfig[];
  
  // Conditional rendering
  condition?: (values: Record<string, any>) => boolean;
  
  // Styling
  className?: string;
  labelClassName?: string;
  inputClassName?: string;
  
  // Help text
  helpText?: string;
  
  // Default value
  defaultValue?: any;
  
  // Custom props
  props?: Record<string, any>;
}

export interface FormConfig {
  fields: FormFieldConfig[];
  submitText?: string;
  loadingText?: string;
  resetText?: string;
  showReset?: boolean;
  layout?: 'vertical' | 'horizontal' | 'inline';
  className?: string;
  onSubmit?: (values: Record<string, any>) => void | Promise<void>;
  onChange?: (name: string, value: any, allValues: Record<string, any>) => void;
  onReset?: () => void;
  initialValues?: Record<string, any>;
  validationMode?: 'onChange' | 'onBlur' | 'onSubmit';
}

export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
  isDirty: boolean;
}

export interface FormActions {
  setValue: (name: string, value: any) => void;
  setError: (name: string, error: string) => void;
  clearError: (name: string) => void;
  setTouched: (name: string, touched: boolean) => void;
  reset: () => void;
  submit: () => Promise<void>;
  validate: (name?: string) => boolean;
}

export interface UseFormReturn extends FormState, FormActions {
  register: (name: string) => {
    name: string;
    value: any;
    onChange: (value: any) => void;
    onBlur: () => void;
    error?: string;
    required?: boolean;
  };
  handleSubmit: (onSubmit: (values: Record<string, any>) => void | Promise<void>) => (e?: React.FormEvent) => Promise<void>;
}

// Validation helpers
export const validators = {
  required: (message = 'This field is required') => (value: any) => {
    if (value === null || value === undefined || value === '') {
      return message;
    }
    return null;
  },
  
  email: (message = 'Please enter a valid email address') => (value: string) => {
    if (!value) return null;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? null : message;
  },
  
  minLength: (min: number, message?: string) => (value: string) => {
    if (!value) return null;
    return value.length >= min ? null : message || `Must be at least ${min} characters`;
  },
  
  maxLength: (max: number, message?: string) => (value: string) => {
    if (!value) return null;
    return value.length <= max ? null : message || `Must be no more than ${max} characters`;
  },
  
  min: (min: number, message?: string) => (value: number) => {
    if (value === null || value === undefined) return null;
    return value >= min ? null : message || `Must be at least ${min}`;
  },
  
  max: (max: number, message?: string) => (value: number) => {
    if (value === null || value === undefined) return null;
    return value <= max ? null : message || `Must be no more than ${max}`;
  },
  
  pattern: (pattern: RegExp, message = 'Invalid format') => (value: string) => {
    if (!value) return null;
    return pattern.test(value) ? null : message;
  },
  
  phone: (message = 'Please enter a valid phone number') => (value: string) => {
    if (!value) return null;
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(value.replace(/\s/g, '')) ? null : message;
  },
  
  url: (message = 'Please enter a valid URL') => (value: string) => {
    if (!value) return null;
    try {
      new URL(value);
      return null;
    } catch {
      return message;
    }
  },
  
  date: (message = 'Please enter a valid date') => (value: string) => {
    if (!value) return null;
    const date = new Date(value);
    return !isNaN(date.getTime()) ? null : message;
  },
  
  custom: (validator: (value: any) => boolean, message: string) => (value: any) => {
    return validator(value) ? null : message;
  },
};

// Form field types for better type safety
export type FormFieldType = FormFieldConfig['type'];
export type FormValidationMode = FormConfig['validationMode'];
export type FormLayout = FormConfig['layout'];

// Export utility types
export type FormValues = Record<string, any>;
export type FormErrors = Record<string, string>;
export type FormTouched = Record<string, boolean>;

// Form builder helpers
export const createFormField = (config: Partial<FormFieldConfig> & Pick<FormFieldConfig, 'name' | 'label' | 'type'>): FormFieldConfig => ({
  required: false,
  disabled: false,
  readonly: false,
  ...config,
});

export const createFormConfig = (config: Partial<FormConfig> & Pick<FormConfig, 'fields'>): FormConfig => ({
  submitText: 'Submit',
  loadingText: 'Submitting...',
  resetText: 'Reset',
  showReset: false,
  layout: 'vertical',
  validationMode: 'onBlur',
  initialValues: {},
  ...config,
});

// Form validation utilities
export const validateField = (field: FormFieldConfig, value: any): string | null => {
  // Required validation
  if (field.required && (value === null || value === undefined || value === '')) {
    return `${field.label} is required`;
  }
  
  // Type-specific validation
  if (value && field.validation) {
    const { min, max, minLength, maxLength, pattern, custom } = field.validation;
    
    if (typeof value === 'string') {
      if (minLength && value.length < minLength) {
        return `${field.label} must be at least ${minLength} characters`;
      }
      if (maxLength && value.length > maxLength) {
        return `${field.label} must be no more than ${maxLength} characters`;
      }
      if (pattern && !new RegExp(pattern).test(value)) {
        return `${field.label} format is invalid`;
      }
    }
    
    if (typeof value === 'number') {
      if (min !== undefined && value < min) {
        return `${field.label} must be at least ${min}`;
      }
      if (max !== undefined && value > max) {
        return `${field.label} must be no more than ${max}`;
      }
    }
    
    if (custom) {
      const customError = custom(value);
      if (customError) {
        return customError;
      }
    }
  }
  
  return null;
};

export const validateForm = (fields: FormFieldConfig[], values: FormValues): FormErrors => {
  const errors: FormErrors = {};
  
  fields.forEach(field => {
    if (field.type === 'group' && field.fields) {
      // Validate group fields recursively
      const groupErrors = validateForm(field.fields, values);
      Object.assign(errors, groupErrors);
    } else {
      const error = validateField(field, values[field.name]);
      if (error) {
        errors[field.name] = error;
      }
    }
  });
  
  return errors;
};
