/**
 * Table Types
 * Common table-related types and interfaces
 */

export interface ColumnDef<T = any> {
  id: string;
  header: string;
  accessorKey?: keyof T;
  accessorFn?: (row: T) => any;
  cell?: ({ row, value }: { row: { original: T }; value: any }) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: number | string;
  minWidth?: number;
  maxWidth?: number;
  align?: 'left' | 'center' | 'right';
  className?: string;
  headerClassName?: string;
  cellClassName?: string;
  meta?: {
    [key: string]: any;
  };
}

export interface SortingState {
  id: string;
  desc: boolean;
}

export interface PaginationState {
  pageIndex: number;
  pageSize: number;
}

export interface FilterState {
  id: string;
  value: any;
}

export interface TableState {
  sorting: SortingState[];
  pagination: PaginationState;
  filters: FilterState[];
  globalFilter: string;
  columnVisibility: Record<string, boolean>;
  rowSelection: Record<string, boolean>;
}

export interface TableOptions<T = any> {
  data: T[];
  columns: ColumnDef<T>[];
  
  // Pagination
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
  };
  
  // Sorting
  sorting?: {
    state: SortingState[];
    onSortingChange: (sorting: SortingState[]) => void;
  };
  
  // Filtering
  filtering?: {
    globalFilter?: string;
    columnFilters?: FilterState[];
    onGlobalFilterChange?: (filter: string) => void;
    onColumnFiltersChange?: (filters: FilterState[]) => void;
  };
  
  // Selection
  selection?: {
    rowSelection: Record<string, boolean>;
    onRowSelectionChange: (selection: Record<string, boolean>) => void;
    enableMultiRowSelection?: boolean;
  };
  
  // Loading and empty states
  loading?: boolean;
  error?: string | null;
  emptyMessage?: string;
  
  // Row interactions
  onRowClick?: (row: T) => void;
  onRowDoubleClick?: (row: T) => void;
  getRowId?: (row: T) => string;
  getRowClassName?: (row: T) => string;
  
  // Table styling
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  striped?: boolean;
  bordered?: boolean;
  hover?: boolean;
  
  // Features
  enableSorting?: boolean;
  enableFiltering?: boolean;
  enablePagination?: boolean;
  enableSelection?: boolean;
  enableColumnResizing?: boolean;
  enableColumnReordering?: boolean;
  enableColumnVisibility?: boolean;
  
  // Bulk actions
  bulkActions?: Array<{
    label: string;
    action: (selectedRows: T[]) => void;
    icon?: React.ReactNode;
    variant?: 'default' | 'destructive';
    disabled?: (selectedRows: T[]) => boolean;
  }>;
}

export interface TableInstance<T = any> {
  // Data
  getRowModel: () => { rows: Array<{ original: T; id: string }> };
  getSelectedRowModel: () => { rows: Array<{ original: T; id: string }> };
  getFilteredRowModel: () => { rows: Array<{ original: T; id: string }> };
  
  // State
  getState: () => TableState;
  setState: (updater: Partial<TableState> | ((prev: TableState) => Partial<TableState>)) => void;
  
  // Pagination
  getCanPreviousPage: () => boolean;
  getCanNextPage: () => boolean;
  previousPage: () => void;
  nextPage: () => void;
  setPageIndex: (pageIndex: number) => void;
  setPageSize: (pageSize: number) => void;
  getPageCount: () => number;
  
  // Sorting
  setSorting: (sorting: SortingState[]) => void;
  resetSorting: () => void;
  
  // Filtering
  setGlobalFilter: (filter: string) => void;
  setColumnFilters: (filters: FilterState[]) => void;
  resetColumnFilters: () => void;
  resetGlobalFilter: () => void;
  
  // Selection
  toggleAllRowsSelected: (selected?: boolean) => void;
  toggleRowSelected: (rowId: string, selected?: boolean) => void;
  getIsAllRowsSelected: () => boolean;
  getIsSomeRowsSelected: () => boolean;
  resetRowSelection: () => void;
  
  // Column visibility
  toggleColumnVisibility: (columnId: string, visible?: boolean) => void;
  toggleAllColumnsVisible: (visible?: boolean) => void;
  resetColumnVisibility: () => void;
}

// Filter types
export interface FilterOption {
  label: string;
  value: any;
}

export interface FilterConfig {
  id: string;
  label: string;
  type: 'text' | 'select' | 'multiselect' | 'date' | 'daterange' | 'number' | 'boolean';
  options?: FilterOption[];
  placeholder?: string;
  defaultValue?: any;
}

// Search configuration
export interface SearchConfig {
  enabled: boolean;
  placeholder?: string;
  debounceMs?: number;
  searchableColumns?: string[];
}

// Export configuration
export interface ExportConfig {
  enabled: boolean;
  formats: Array<'csv' | 'xlsx' | 'pdf'>;
  filename?: string;
  onExport?: (format: string, data: any[]) => void;
}

// Table toolbar configuration
export interface ToolbarConfig {
  search?: SearchConfig;
  filters?: FilterConfig[];
  export?: ExportConfig;
  refresh?: {
    enabled: boolean;
    onRefresh: () => void;
  };
  customActions?: Array<{
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'ghost';
  }>;
}

// Utility types
export type SortDirection = 'asc' | 'desc';
export type TableSize = 'sm' | 'md' | 'lg';
export type CellAlignment = 'left' | 'center' | 'right';

// Helper functions
export const createColumn = <T>(config: Partial<ColumnDef<T>> & Pick<ColumnDef<T>, 'id' | 'header'>): ColumnDef<T> => ({
  sortable: true,
  filterable: false,
  align: 'left',
  ...config,
});

export const createTableOptions = <T>(config: Partial<TableOptions<T>> & Pick<TableOptions<T>, 'data' | 'columns'>): TableOptions<T> => ({
  loading: false,
  emptyMessage: 'No data available',
  size: 'md',
  striped: false,
  bordered: false,
  hover: true,
  enableSorting: true,
  enableFiltering: false,
  enablePagination: true,
  enableSelection: false,
  enableColumnResizing: false,
  enableColumnReordering: false,
  enableColumnVisibility: false,
  ...config,
});

// Table state helpers
export const getInitialTableState = (): TableState => ({
  sorting: [],
  pagination: { pageIndex: 0, pageSize: 25 },
  filters: [],
  globalFilter: '',
  columnVisibility: {},
  rowSelection: {},
});

export const getSelectedRows = <T>(data: T[], rowSelection: Record<string, boolean>, getRowId?: (row: T) => string): T[] => {
  return data.filter((row, index) => {
    const id = getRowId ? getRowId(row) : index.toString();
    return rowSelection[id];
  });
};

export const isRowSelected = (rowId: string, rowSelection: Record<string, boolean>): boolean => {
  return !!rowSelection[rowId];
};

export const toggleRowSelection = (rowId: string, rowSelection: Record<string, boolean>): Record<string, boolean> => {
  return {
    ...rowSelection,
    [rowId]: !rowSelection[rowId],
  };
};

export const selectAllRows = <T>(data: T[], getRowId?: (row: T) => string): Record<string, boolean> => {
  const selection: Record<string, boolean> = {};
  data.forEach((row, index) => {
    const id = getRowId ? getRowId(row) : index.toString();
    selection[id] = true;
  });
  return selection;
};

export const clearRowSelection = (): Record<string, boolean> => ({});

// Sorting helpers
export const applySorting = <T>(data: T[], sorting: SortingState[]): T[] => {
  if (sorting.length === 0) return data;
  
  return [...data].sort((a, b) => {
    for (const sort of sorting) {
      const aValue = (a as any)[sort.id];
      const bValue = (b as any)[sort.id];
      
      if (aValue === bValue) continue;
      
      const comparison = aValue < bValue ? -1 : 1;
      return sort.desc ? -comparison : comparison;
    }
    return 0;
  });
};

// Filtering helpers
export const applyFilters = <T>(data: T[], filters: FilterState[], globalFilter?: string): T[] => {
  let filteredData = data;
  
  // Apply column filters
  filters.forEach(filter => {
    if (filter.value !== undefined && filter.value !== null && filter.value !== '') {
      filteredData = filteredData.filter(row => {
        const value = (row as any)[filter.id];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(filter.value.toLowerCase());
        }
        return value === filter.value;
      });
    }
  });
  
  // Apply global filter
  if (globalFilter) {
    filteredData = filteredData.filter(row => {
      return Object.values(row as any).some(value => 
        String(value).toLowerCase().includes(globalFilter.toLowerCase())
      );
    });
  }
  
  return filteredData;
};

// Pagination helpers
export const applyPagination = <T>(data: T[], pagination: PaginationState): T[] => {
  const start = pagination.pageIndex * pagination.pageSize;
  const end = start + pagination.pageSize;
  return data.slice(start, end);
};

export const getPageCount = (totalItems: number, pageSize: number): number => {
  return Math.ceil(totalItems / pageSize);
};

export const getPageInfo = (pageIndex: number, pageSize: number, totalItems: number) => {
  const totalPages = getPageCount(totalItems, pageSize);
  const start = pageIndex * pageSize + 1;
  const end = Math.min((pageIndex + 1) * pageSize, totalItems);
  
  return {
    page: pageIndex + 1,
    totalPages,
    start,
    end,
    total: totalItems,
    hasNext: pageIndex < totalPages - 1,
    hasPrev: pageIndex > 0,
  };
};
