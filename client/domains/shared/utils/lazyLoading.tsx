/**
 * Lazy Loading Utilities
 * Utilities for implementing lazy loading with proper error boundaries and fallbacks
 */

import React, { Suspense, ComponentType, ReactNode } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Loader2 } from 'lucide-react';

// Loading fallback component
export const LoadingFallback = ({ 
  message = 'Loading...', 
  size = 'default' 
}: { 
  message?: string; 
  size?: 'small' | 'default' | 'large' 
}) => {
  const sizeClasses = {
    small: 'h-4 w-4',
    default: 'h-6 w-6',
    large: 'h-8 w-8',
  };

  return (
    <div className="flex items-center justify-center p-4">
      <div className="flex items-center gap-2 text-muted-foreground">
        <Loader2 className={`${sizeClasses[size]} animate-spin`} />
        <span className="text-sm">{message}</span>
      </div>
    </div>
  );
};

// Error fallback component
export const ErrorFallback = ({ 
  error, 
  resetErrorBoundary 
}: { 
  error: Error; 
  resetErrorBoundary: () => void 
}) => (
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <div className="mb-4 text-red-500">
      <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    </div>
    <h3 className="text-lg font-semibold text-gray-900 mb-2">Something went wrong</h3>
    <p className="text-sm text-gray-600 mb-4">
      {error.message || 'An unexpected error occurred while loading this component.'}
    </p>
    <button
      onClick={resetErrorBoundary}
      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
    >
      Try again
    </button>
  </div>
);

// Lazy wrapper with error boundary and suspense
export const withLazyLoading = <P extends object>(
  LazyComponent: React.LazyExoticComponent<ComponentType<P>>,
  options: {
    fallback?: ReactNode;
    errorFallback?: ComponentType<{ error: Error; resetErrorBoundary: () => void }>;
    onError?: (error: Error, errorInfo: { componentStack: string }) => void;
  } = {}
) => {
  const {
    fallback = <LoadingFallback />,
    errorFallback = ErrorFallback,
    onError,
  } = options;

  return (props: P) => (
    <ErrorBoundary
      FallbackComponent={errorFallback}
      onError={onError}
      onReset={() => window.location.reload()}
    >
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

// Preload function for lazy components
export const preloadComponent = (lazyComponent: React.LazyExoticComponent<any>) => {
  // Trigger the lazy loading
  lazyComponent._payload._result;
};

// Hook for preloading components on hover or focus
export const usePreloadOnHover = (lazyComponent: React.LazyExoticComponent<any>) => {
  const preload = () => preloadComponent(lazyComponent);
  
  return {
    onMouseEnter: preload,
    onFocus: preload,
  };
};

// Lazy loading with retry mechanism
export const createRetryableLazy = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  maxRetries = 3
) => {
  return React.lazy(async () => {
    let lastError: Error;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await importFn();
      } catch (error) {
        lastError = error as Error;
        
        // Wait before retrying (exponential backoff)
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
      }
    }
    
    throw lastError!;
  });
};

// Domain-specific lazy loading factories
export const createDomainLazy = (domain: string) => {
  return {
    component: <T extends ComponentType<any>>(componentName: string) =>
      React.lazy(() => import(`../../${domain}/components/${componentName}`)),
    
    page: <T extends ComponentType<any>>(pageName: string) =>
      React.lazy(() => import(`../../../pages/${pageName}`)),
    
    modal: <T extends ComponentType<any>>(modalName: string) =>
      React.lazy(() => import(`../../${domain}/components/modals/${modalName}`)),
  };
};

// Specific loading fallbacks for different component types
export const ComponentLoadingFallbacks = {
  card: <LoadingFallback message="Loading card..." size="small" />,
  modal: <LoadingFallback message="Loading modal..." size="default" />,
  page: <LoadingFallback message="Loading page..." size="large" />,
  table: (
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
    </div>
  ),
  form: (
    <div className="space-y-4 animate-pulse">
      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
      <div className="h-10 bg-gray-200 rounded"></div>
      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
      <div className="h-10 bg-gray-200 rounded"></div>
    </div>
  ),
};

// Performance monitoring for lazy loading
export const withLoadingMetrics = <P extends object>(
  LazyComponent: React.LazyExoticComponent<ComponentType<P>>,
  componentName: string
) => {
  return withLazyLoading(LazyComponent, {
    onError: (error, errorInfo) => {
      // Log loading errors for monitoring
      console.error(`Lazy loading failed for ${componentName}:`, error);
      
      // Send to analytics service if available
      if (typeof window !== 'undefined' && (window as any).analytics) {
        (window as any).analytics.track('Component Loading Error', {
          component: componentName,
          error: error.message,
          stack: errorInfo.componentStack,
        });
      }
    },
  });
};

// Batch preloader for multiple components
export const batchPreload = (components: React.LazyExoticComponent<any>[]) => {
  return Promise.all(components.map(component => {
    try {
      return preloadComponent(component);
    } catch (error) {
      console.warn('Failed to preload component:', error);
      return null;
    }
  }));
};

// Route-based preloading
export const preloadRouteComponents = (routeName: string) => {
  const routeComponentMap: Record<string, React.LazyExoticComponent<any>[]> = {
    candidates: [
      // Add candidate route components here
    ],
    jobs: [
      // Add job route components here
    ],
    interviews: [
      // Add interview route components here
    ],
    calendar: [
      // Add calendar route components here
    ],
    analytics: [
      // Add analytics route components here
    ],
  };

  const components = routeComponentMap[routeName];
  if (components) {
    return batchPreload(components);
  }
  
  return Promise.resolve();
};
