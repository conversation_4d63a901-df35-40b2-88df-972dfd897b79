/**
 * FormBuilder Component Tests
 * Comprehensive unit tests for the FormBuilder system
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { FormBuilder } from '../../components/FormBuilder';
import { FormFieldConfig } from '../../types/form';

// Mock the UI components
vi.mock('@/components/ui/form', () => ({
  Form: ({ children }: any) => <form data-testid="form">{children}</form>,
  FormField: ({ children }: any) => <div data-testid="form-field">{children}</div>,
  FormItem: ({ children }: any) => <div data-testid="form-item">{children}</div>,
  FormLabel: ({ children }: any) => <label data-testid="form-label">{children}</label>,
  FormControl: ({ children }: any) => <div data-testid="form-control">{children}</div>,
  FormMessage: ({ children }: any) => <div data-testid="form-message">{children}</div>,
}));

vi.mock('@/components/ui/input', () => ({
  Input: ({ placeholder, value, onChange, type, ...props }: any) => (
    <input
      data-testid="input"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      type={type}
      {...props}
    />
  ),
}));

vi.mock('@/components/ui/textarea', () => ({
  Textarea: ({ placeholder, value, onChange, ...props }: any) => (
    <textarea
      data-testid="textarea"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      {...props}
    />
  ),
}));

vi.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange, value }: any) => (
    <select data-testid="select" onChange={(e) => onValueChange(e.target.value)} value={value}>
      {children}
    </select>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}));

vi.mock('@/components/ui/checkbox', () => ({
  Checkbox: ({ checked, onCheckedChange, ...props }: any) => (
    <input
      data-testid="checkbox"
      type="checkbox"
      checked={checked}
      onChange={(e) => onCheckedChange(e.target.checked)}
      {...props}
    />
  ),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, type, disabled }: any) => (
    <button
      data-testid="button"
      onClick={onClick}
      type={type}
      disabled={disabled}
    >
      {children}
    </button>
  ),
}));

describe('FormBuilder Component', () => {
  const mockOnSubmit = vi.fn();
  const mockOnChange = vi.fn();

  const basicFields: FormFieldConfig[] = [
    {
      name: 'firstName',
      label: 'First Name',
      type: 'text',
      required: true,
      placeholder: 'Enter first name',
    },
    {
      name: 'email',
      label: 'Email',
      type: 'email',
      required: true,
      placeholder: 'Enter email address',
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Enter description',
    },
  ];

  const defaultProps = {
    fields: basicFields,
    onSubmit: mockOnSubmit,
    onChange: mockOnChange,
    initialValues: {},
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders form with all field types', () => {
    render(<FormBuilder {...defaultProps} />);
    
    expect(screen.getByTestId('form')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter first name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter email address')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter description')).toBeInTheDocument();
  });

  it('renders field labels correctly', () => {
    render(<FormBuilder {...defaultProps} />);
    
    expect(screen.getByText('First Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
  });

  it('shows required indicators for required fields', () => {
    render(<FormBuilder {...defaultProps} />);
    
    // Required fields should have asterisk or required indicator
    const firstNameLabel = screen.getByText('First Name');
    const emailLabel = screen.getByText('Email');
    
    expect(firstNameLabel.textContent).toContain('*');
    expect(emailLabel.textContent).toContain('*');
  });

  it('handles text input changes', () => {
    render(<FormBuilder {...defaultProps} />);
    
    const firstNameInput = screen.getByPlaceholderText('Enter first name');
    fireEvent.change(firstNameInput, { target: { value: 'John' } });
    
    expect(mockOnChange).toHaveBeenCalledWith('firstName', 'John');
  });

  it('handles textarea changes', () => {
    render(<FormBuilder {...defaultProps} />);
    
    const descriptionTextarea = screen.getByPlaceholderText('Enter description');
    fireEvent.change(descriptionTextarea, { target: { value: 'Test description' } });
    
    expect(mockOnChange).toHaveBeenCalledWith('description', 'Test description');
  });

  it('renders select field with options', () => {
    const fieldsWithSelect: FormFieldConfig[] = [
      {
        name: 'status',
        label: 'Status',
        type: 'select',
        options: [
          { label: 'Active', value: 'active' },
          { label: 'Inactive', value: 'inactive' },
        ],
      },
    ];

    render(<FormBuilder {...defaultProps} fields={fieldsWithSelect} />);
    
    expect(screen.getByTestId('select')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Inactive')).toBeInTheDocument();
  });

  it('handles select field changes', () => {
    const fieldsWithSelect: FormFieldConfig[] = [
      {
        name: 'status',
        label: 'Status',
        type: 'select',
        options: [
          { label: 'Active', value: 'active' },
          { label: 'Inactive', value: 'inactive' },
        ],
      },
    ];

    render(<FormBuilder {...defaultProps} fields={fieldsWithSelect} />);
    
    const select = screen.getByTestId('select');
    fireEvent.change(select, { target: { value: 'active' } });
    
    expect(mockOnChange).toHaveBeenCalledWith('status', 'active');
  });

  it('renders checkbox field', () => {
    const fieldsWithCheckbox: FormFieldConfig[] = [
      {
        name: 'isActive',
        label: 'Is Active',
        type: 'checkbox',
      },
    ];

    render(<FormBuilder {...defaultProps} fields={fieldsWithCheckbox} />);
    
    expect(screen.getByTestId('checkbox')).toBeInTheDocument();
    expect(screen.getByText('Is Active')).toBeInTheDocument();
  });

  it('handles checkbox changes', () => {
    const fieldsWithCheckbox: FormFieldConfig[] = [
      {
        name: 'isActive',
        label: 'Is Active',
        type: 'checkbox',
      },
    ];

    render(<FormBuilder {...defaultProps} fields={fieldsWithCheckbox} />);
    
    const checkbox = screen.getByTestId('checkbox');
    fireEvent.click(checkbox);
    
    expect(mockOnChange).toHaveBeenCalledWith('isActive', true);
  });

  it('populates initial values', () => {
    const initialValues = {
      firstName: 'John',
      email: '<EMAIL>',
      description: 'Initial description',
    };

    render(<FormBuilder {...defaultProps} initialValues={initialValues} />);
    
    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Initial description')).toBeInTheDocument();
  });

  it('handles form submission', async () => {
    render(<FormBuilder {...defaultProps} />);
    
    // Fill out the form
    fireEvent.change(screen.getByPlaceholderText('Enter first name'), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter email address'), {
      target: { value: '<EMAIL>' },
    });
    
    // Submit the form
    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        firstName: 'John',
        email: '<EMAIL>',
        description: '',
      });
    });
  });

  it('shows validation errors', async () => {
    render(<FormBuilder {...defaultProps} />);
    
    // Try to submit without filling required fields
    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('First Name is required')).toBeInTheDocument();
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });
  });

  it('disables submit button when loading', () => {
    render(<FormBuilder {...defaultProps} loading={true} />);
    
    const submitButton = screen.getByText('Submitting...');
    expect(submitButton).toBeDisabled();
  });

  it('renders custom submit button text', () => {
    render(<FormBuilder {...defaultProps} submitText="Save Changes" />);
    
    expect(screen.getByText('Save Changes')).toBeInTheDocument();
  });

  it('renders custom loading text', () => {
    render(
      <FormBuilder 
        {...defaultProps} 
        loading={true} 
        loadingText="Saving..." 
      />
    );
    
    expect(screen.getByText('Saving...')).toBeInTheDocument();
  });

  it('handles conditional field visibility', () => {
    const conditionalFields: FormFieldConfig[] = [
      {
        name: 'hasExperience',
        label: 'Has Experience',
        type: 'checkbox',
      },
      {
        name: 'yearsExperience',
        label: 'Years of Experience',
        type: 'number',
        condition: (values) => values.hasExperience === true,
      },
    ];

    render(<FormBuilder {...defaultProps} fields={conditionalFields} />);
    
    // Initially, years experience field should not be visible
    expect(screen.queryByText('Years of Experience')).not.toBeInTheDocument();
    
    // Check the has experience checkbox
    const checkbox = screen.getByTestId('checkbox');
    fireEvent.click(checkbox);
    
    // Now years experience field should be visible
    expect(screen.getByText('Years of Experience')).toBeInTheDocument();
  });

  it('applies custom field classes', () => {
    const fieldsWithClasses: FormFieldConfig[] = [
      {
        name: 'customField',
        label: 'Custom Field',
        type: 'text',
        className: 'custom-field-class',
      },
    ];

    render(<FormBuilder {...defaultProps} fields={fieldsWithClasses} />);
    
    const formField = screen.getByTestId('form-field');
    expect(formField).toHaveClass('custom-field-class');
  });

  it('handles field groups', () => {
    const fieldsWithGroups: FormFieldConfig[] = [
      {
        name: 'personalInfo',
        label: 'Personal Information',
        type: 'group',
        fields: [
          {
            name: 'firstName',
            label: 'First Name',
            type: 'text',
          },
          {
            name: 'lastName',
            label: 'Last Name',
            type: 'text',
          },
        ],
      },
    ];

    render(<FormBuilder {...defaultProps} fields={fieldsWithGroups} />);
    
    expect(screen.getByText('Personal Information')).toBeInTheDocument();
    expect(screen.getByText('First Name')).toBeInTheDocument();
    expect(screen.getByText('Last Name')).toBeInTheDocument();
  });
});
