/**
 * DataTable Component Tests
 * Comprehensive unit tests for the DataTable system
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { DataTable } from '../../components/DataTable';
import { ColumnDef } from '../../types/table';

// Mock the UI components
vi.mock('@/components/ui/table', () => ({
  Table: ({ children }: any) => <table data-testid="table">{children}</table>,
  TableHeader: ({ children }: any) => <thead data-testid="table-header">{children}</thead>,
  TableBody: ({ children }: any) => <tbody data-testid="table-body">{children}</tbody>,
  TableRow: ({ children }: any) => <tr data-testid="table-row">{children}</tr>,
  TableHead: ({ children, onClick }: any) => (
    <th data-testid="table-head" onClick={onClick}>
      {children}
    </th>
  ),
  TableCell: ({ children }: any) => <td data-testid="table-cell">{children}</td>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variant, size, disabled }: any) => (
    <button
      data-testid="button"
      onClick={onClick}
      data-variant={variant}
      data-size={size}
      disabled={disabled}
    >
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/input', () => ({
  Input: ({ placeholder, value, onChange, ...props }: any) => (
    <input
      data-testid="input"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      {...props}
    />
  ),
}));

vi.mock('@/components/ui/checkbox', () => ({
  Checkbox: ({ checked, onCheckedChange }: any) => (
    <input
      data-testid="checkbox"
      type="checkbox"
      checked={checked}
      onChange={(e) => onCheckedChange(e.target.checked)}
    />
  ),
}));

// Mock data
const mockData = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive' },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active' },
];

const mockColumns: ColumnDef<typeof mockData[0]>[] = [
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
    sortable: true,
  },
  {
    id: 'email',
    header: 'Email',
    accessorKey: 'email',
    sortable: true,
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    cell: ({ row }) => (
      <span className={`status-${row.original.status}`}>
        {row.original.status}
      </span>
    ),
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <button onClick={() => console.log('Edit', row.original.id)}>
        Edit
      </button>
    ),
  },
];

describe('DataTable Component', () => {
  const defaultProps = {
    data: mockData,
    columns: mockColumns,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders table with data', () => {
    render(<DataTable {...defaultProps} />);
    
    expect(screen.getByTestId('table')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
  });

  it('renders column headers', () => {
    render(<DataTable {...defaultProps} />);
    
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
  });

  it('renders custom cell content', () => {
    render(<DataTable {...defaultProps} />);
    
    // Status column should render custom cell content
    expect(screen.getByText('active')).toBeInTheDocument();
    expect(screen.getByText('inactive')).toBeInTheDocument();
    
    // Actions column should render edit buttons
    const editButtons = screen.getAllByText('Edit');
    expect(editButtons).toHaveLength(3);
  });

  it('handles sorting when column is clicked', () => {
    const onSort = vi.fn();
    render(<DataTable {...defaultProps} onSort={onSort} />);
    
    const nameHeader = screen.getByText('Name');
    fireEvent.click(nameHeader);
    
    expect(onSort).toHaveBeenCalledWith('name', 'asc');
  });

  it('toggles sort direction on repeated clicks', () => {
    const onSort = vi.fn();
    render(<DataTable {...defaultProps} onSort={onSort} />);
    
    const nameHeader = screen.getByText('Name');
    
    // First click - ascending
    fireEvent.click(nameHeader);
    expect(onSort).toHaveBeenCalledWith('name', 'asc');
    
    // Second click - descending
    fireEvent.click(nameHeader);
    expect(onSort).toHaveBeenCalledWith('name', 'desc');
  });

  it('renders loading state', () => {
    render(<DataTable {...defaultProps} loading={true} />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders empty state when no data', () => {
    render(<DataTable {...defaultProps} data={[]} />);
    
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders custom empty state message', () => {
    render(
      <DataTable 
        {...defaultProps} 
        data={[]} 
        emptyMessage="No users found"
      />
    );
    
    expect(screen.getByText('No users found')).toBeInTheDocument();
  });

  it('handles row selection when selectable is true', () => {
    const onSelectionChange = vi.fn();
    render(
      <DataTable 
        {...defaultProps} 
        selectable={true}
        onSelectionChange={onSelectionChange}
      />
    );
    
    // Should render checkboxes for each row
    const checkboxes = screen.getAllByTestId('checkbox');
    expect(checkboxes.length).toBeGreaterThan(0);
    
    // Click first row checkbox
    fireEvent.click(checkboxes[1]); // Skip header checkbox
    
    expect(onSelectionChange).toHaveBeenCalledWith([mockData[0]]);
  });

  it('handles select all functionality', () => {
    const onSelectionChange = vi.fn();
    render(
      <DataTable 
        {...defaultProps} 
        selectable={true}
        onSelectionChange={onSelectionChange}
      />
    );
    
    // Click select all checkbox (first checkbox)
    const selectAllCheckbox = screen.getAllByTestId('checkbox')[0];
    fireEvent.click(selectAllCheckbox);
    
    expect(onSelectionChange).toHaveBeenCalledWith(mockData);
  });

  it('renders pagination when enabled', () => {
    render(
      <DataTable 
        {...defaultProps} 
        pagination={{
          page: 1,
          pageSize: 10,
          total: 100,
          onPageChange: vi.fn(),
          onPageSizeChange: vi.fn(),
        }}
      />
    );
    
    expect(screen.getByText('Previous')).toBeInTheDocument();
    expect(screen.getByText('Next')).toBeInTheDocument();
    expect(screen.getByText('Page 1 of 10')).toBeInTheDocument();
  });

  it('handles page navigation', () => {
    const onPageChange = vi.fn();
    render(
      <DataTable 
        {...defaultProps} 
        pagination={{
          page: 1,
          pageSize: 10,
          total: 100,
          onPageChange,
          onPageSizeChange: vi.fn(),
        }}
      />
    );
    
    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);
    
    expect(onPageChange).toHaveBeenCalledWith(2);
  });

  it('renders search functionality when enabled', () => {
    const onSearch = vi.fn();
    render(
      <DataTable 
        {...defaultProps} 
        searchable={true}
        onSearch={onSearch}
      />
    );
    
    const searchInput = screen.getByPlaceholderText('Search...');
    expect(searchInput).toBeInTheDocument();
    
    fireEvent.change(searchInput, { target: { value: 'John' } });
    
    expect(onSearch).toHaveBeenCalledWith('John');
  });

  it('renders filters when provided', () => {
    const filters = [
      {
        key: 'status',
        label: 'Status',
        options: [
          { label: 'Active', value: 'active' },
          { label: 'Inactive', value: 'inactive' },
        ],
      },
    ];

    render(
      <DataTable 
        {...defaultProps} 
        filters={filters}
        onFilterChange={vi.fn()}
      />
    );
    
    expect(screen.getByText('Status')).toBeInTheDocument();
  });

  it('handles bulk actions when rows are selected', () => {
    const bulkActions = [
      { label: 'Delete Selected', action: vi.fn() },
      { label: 'Export Selected', action: vi.fn() },
    ];

    const onSelectionChange = vi.fn();
    render(
      <DataTable 
        {...defaultProps} 
        selectable={true}
        onSelectionChange={onSelectionChange}
        bulkActions={bulkActions}
      />
    );
    
    // Select a row first
    const checkboxes = screen.getAllByTestId('checkbox');
    fireEvent.click(checkboxes[1]);
    
    // Bulk actions should be visible
    expect(screen.getByText('Delete Selected')).toBeInTheDocument();
    expect(screen.getByText('Export Selected')).toBeInTheDocument();
  });

  it('applies custom row className', () => {
    const getRowClassName = (row: any) => 
      row.original.status === 'active' ? 'active-row' : 'inactive-row';

    render(
      <DataTable 
        {...defaultProps} 
        getRowClassName={getRowClassName}
      />
    );
    
    const rows = screen.getAllByTestId('table-row');
    // Skip header row
    expect(rows[1]).toHaveClass('active-row');
    expect(rows[2]).toHaveClass('inactive-row');
  });

  it('handles row click events', () => {
    const onRowClick = vi.fn();
    render(
      <DataTable 
        {...defaultProps} 
        onRowClick={onRowClick}
      />
    );
    
    const firstDataRow = screen.getAllByTestId('table-row')[1]; // Skip header
    fireEvent.click(firstDataRow);
    
    expect(onRowClick).toHaveBeenCalledWith(mockData[0]);
  });

  it('renders with custom table props', () => {
    render(
      <DataTable 
        {...defaultProps} 
        tableProps={{ 
          className: 'custom-table',
          'data-testid': 'custom-table'
        }}
      />
    );
    
    expect(screen.getByTestId('custom-table')).toHaveClass('custom-table');
  });
});
