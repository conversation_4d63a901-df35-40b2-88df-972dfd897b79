/**
 * Modal Component Tests
 * Comprehensive unit tests for the Modal system
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../../components/Modal';

// Mock the UI components
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open, onOpenChange }: any) => (
    <div data-testid="dialog" data-open={open}>
      {open && children}
      <button onClick={() => onOpenChange(false)}>Close Dialog</button>
    </div>
  ),
  DialogContent: ({ children, className }: any) => (
    <div data-testid="dialog-content" className={className}>
      {children}
    </div>
  ),
  DialogHeader: ({ children }: any) => (
    <div data-testid="dialog-header">{children}</div>
  ),
  DialogTitle: ({ children }: any) => (
    <h2 data-testid="dialog-title">{children}</h2>
  ),
  DialogDescription: ({ children }: any) => (
    <p data-testid="dialog-description">{children}</p>
  ),
  DialogFooter: ({ children }: any) => (
    <div data-testid="dialog-footer">{children}</div>
  ),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variant, disabled }: any) => (
    <button 
      onClick={onClick} 
      data-variant={variant} 
      disabled={disabled}
      data-testid="button"
    >
      {children}
    </button>
  ),
}));

describe('Modal Component', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    title: 'Test Modal',
    children: <div>Modal content</div>,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders modal when isOpen is true', () => {
    render(<Modal {...defaultProps} />);
    
    expect(screen.getByTestId('dialog')).toHaveAttribute('data-open', 'true');
    expect(screen.getByText('Test Modal')).toBeInTheDocument();
    expect(screen.getByText('Modal content')).toBeInTheDocument();
  });

  it('does not render modal when isOpen is false', () => {
    render(<Modal {...defaultProps} isOpen={false} />);
    
    expect(screen.getByTestId('dialog')).toHaveAttribute('data-open', 'false');
    expect(screen.queryByText('Test Modal')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    const onClose = vi.fn();
    render(<Modal {...defaultProps} onClose={onClose} />);
    
    fireEvent.click(screen.getByText('Close Dialog'));
    
    expect(onClose).toHaveBeenCalledTimes(1);
  });

  it('renders with different sizes', () => {
    const { rerender } = render(<Modal {...defaultProps} size="small" />);
    expect(screen.getByTestId('dialog-content')).toHaveClass('max-w-md');
    
    rerender(<Modal {...defaultProps} size="medium" />);
    expect(screen.getByTestId('dialog-content')).toHaveClass('max-w-lg');
    
    rerender(<Modal {...defaultProps} size="large" />);
    expect(screen.getByTestId('dialog-content')).toHaveClass('max-w-2xl');
    
    rerender(<Modal {...defaultProps} size="xlarge" />);
    expect(screen.getByTestId('dialog-content')).toHaveClass('max-w-4xl');
  });

  it('renders description when provided', () => {
    render(<Modal {...defaultProps} description="Modal description" />);
    
    expect(screen.getByText('Modal description')).toBeInTheDocument();
  });

  it('renders loading state', () => {
    render(<Modal {...defaultProps} loading={true} />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('disables close when closeDisabled is true', () => {
    const onClose = vi.fn();
    render(<Modal {...defaultProps} onClose={onClose} closeDisabled={true} />);
    
    // The close functionality should be disabled
    // This would depend on the actual implementation
    expect(onClose).not.toHaveBeenCalled();
  });

  it('renders custom footer when provided', () => {
    const customFooter = <div>Custom footer</div>;
    render(<Modal {...defaultProps} footer={customFooter} />);
    
    expect(screen.getByText('Custom footer')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<Modal {...defaultProps} className="custom-modal" />);
    
    expect(screen.getByTestId('dialog-content')).toHaveClass('custom-modal');
  });
});

describe('ModalHeader Component', () => {
  it('renders title and description', () => {
    render(
      <ModalHeader title="Header Title" description="Header description" />
    );
    
    expect(screen.getByText('Header Title')).toBeInTheDocument();
    expect(screen.getByText('Header description')).toBeInTheDocument();
  });

  it('renders children when provided', () => {
    render(
      <ModalHeader title="Header Title">
        <div>Custom header content</div>
      </ModalHeader>
    );
    
    expect(screen.getByText('Custom header content')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<ModalHeader title="Title" className="custom-header" />);
    
    expect(screen.getByTestId('dialog-header')).toHaveClass('custom-header');
  });
});

describe('ModalBody Component', () => {
  it('renders children content', () => {
    render(
      <ModalBody>
        <div>Body content</div>
      </ModalBody>
    );
    
    expect(screen.getByText('Body content')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <ModalBody className="custom-body">
        <div>Content</div>
      </ModalBody>
    );
    
    expect(container.firstChild).toHaveClass('custom-body');
  });

  it('renders with padding by default', () => {
    const { container } = render(
      <ModalBody>
        <div>Content</div>
      </ModalBody>
    );
    
    expect(container.firstChild).toHaveClass('p-6');
  });

  it('renders without padding when noPadding is true', () => {
    const { container } = render(
      <ModalBody noPadding>
        <div>Content</div>
      </ModalBody>
    );
    
    expect(container.firstChild).not.toHaveClass('p-6');
  });
});

describe('ModalFooter Component', () => {
  it('renders children content', () => {
    render(
      <ModalFooter>
        <button>Footer button</button>
      </ModalFooter>
    );
    
    expect(screen.getByText('Footer button')).toBeInTheDocument();
  });

  it('renders primary and secondary actions', () => {
    const primaryAction = { label: 'Save', onClick: vi.fn() };
    const secondaryAction = { label: 'Cancel', onClick: vi.fn() };
    
    render(
      <ModalFooter 
        primaryAction={primaryAction}
        secondaryAction={secondaryAction}
      />
    );
    
    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('calls action handlers when buttons are clicked', () => {
    const primaryAction = { label: 'Save', onClick: vi.fn() };
    const secondaryAction = { label: 'Cancel', onClick: vi.fn() };
    
    render(
      <ModalFooter 
        primaryAction={primaryAction}
        secondaryAction={secondaryAction}
      />
    );
    
    fireEvent.click(screen.getByText('Save'));
    fireEvent.click(screen.getByText('Cancel'));
    
    expect(primaryAction.onClick).toHaveBeenCalledTimes(1);
    expect(secondaryAction.onClick).toHaveBeenCalledTimes(1);
  });

  it('disables primary action when loading', () => {
    const primaryAction = { label: 'Save', onClick: vi.fn() };
    
    render(
      <ModalFooter 
        primaryAction={primaryAction}
        loading={true}
      />
    );
    
    const saveButton = screen.getByText('Save');
    expect(saveButton).toBeDisabled();
  });

  it('shows loading text when loading', () => {
    const primaryAction = { label: 'Save', onClick: vi.fn(), loadingText: 'Saving...' };
    
    render(
      <ModalFooter 
        primaryAction={primaryAction}
        loading={true}
      />
    );
    
    expect(screen.getByText('Saving...')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <ModalFooter className="custom-footer">
        <div>Footer content</div>
      </ModalFooter>
    );
    
    expect(screen.getByTestId('dialog-footer')).toHaveClass('custom-footer');
  });
});

describe('Modal Integration Tests', () => {
  it('renders complete modal with all components', () => {
    const onClose = vi.fn();
    const onSave = vi.fn();
    
    render(
      <Modal isOpen={true} onClose={onClose} size="large">
        <ModalHeader 
          title="Complete Modal" 
          description="This is a complete modal example"
        />
        <ModalBody>
          <div>Modal body content</div>
        </ModalBody>
        <ModalFooter
          primaryAction={{ label: 'Save', onClick: onSave }}
          secondaryAction={{ label: 'Cancel', onClick: onClose }}
        />
      </Modal>
    );
    
    expect(screen.getByText('Complete Modal')).toBeInTheDocument();
    expect(screen.getByText('This is a complete modal example')).toBeInTheDocument();
    expect(screen.getByText('Modal body content')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('handles complete user interaction flow', async () => {
    const onClose = vi.fn();
    const onSave = vi.fn();
    
    render(
      <Modal isOpen={true} onClose={onClose}>
        <ModalHeader title="User Flow Modal" />
        <ModalBody>
          <input data-testid="modal-input" placeholder="Enter text" />
        </ModalBody>
        <ModalFooter
          primaryAction={{ label: 'Save', onClick: onSave }}
          secondaryAction={{ label: 'Cancel', onClick: onClose }}
        />
      </Modal>
    );
    
    // User types in input
    const input = screen.getByTestId('modal-input');
    fireEvent.change(input, { target: { value: 'test input' } });
    
    // User clicks save
    fireEvent.click(screen.getByText('Save'));
    
    expect(onSave).toHaveBeenCalledTimes(1);
    expect(input).toHaveValue('test input');
  });
});
