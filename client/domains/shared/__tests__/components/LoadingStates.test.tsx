/**
 * Loading States Component Tests
 * Comprehensive unit tests for loading components
 */

import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { 
  LoadingSpinner, 
  LoadingCard, 
  LoadingSkeleton,
  LoadingButton,
  LoadingOverlay 
} from '../../components/LoadingStates';

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  Loader2: ({ className }: any) => (
    <div data-testid="loader-icon" className={className}>
      Loading Icon
    </div>
  ),
}));

describe('LoadingSpinner Component', () => {
  it('renders loading spinner with default props', () => {
    render(<LoadingSpinner />);
    
    expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders with custom message', () => {
    render(<LoadingSpinner message="Please wait..." />);
    
    expect(screen.getByText('Please wait...')).toBeInTheDocument();
  });

  it('renders with different sizes', () => {
    const { rerender } = render(<LoadingSpinner size="small" />);
    expect(screen.getByTestId('loader-icon')).toHaveClass('h-4', 'w-4');
    
    rerender(<LoadingSpinner size="medium" />);
    expect(screen.getByTestId('loader-icon')).toHaveClass('h-6', 'w-6');
    
    rerender(<LoadingSpinner size="large" />);
    expect(screen.getByTestId('loader-icon')).toHaveClass('h-8', 'w-8');
  });

  it('renders without message when showMessage is false', () => {
    render(<LoadingSpinner showMessage={false} />);
    
    expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<LoadingSpinner className="custom-spinner" />);
    
    expect(container.firstChild).toHaveClass('custom-spinner');
  });

  it('centers content by default', () => {
    const { container } = render(<LoadingSpinner />);
    
    expect(container.firstChild).toHaveClass('flex', 'items-center', 'justify-center');
  });
});

describe('LoadingCard Component', () => {
  it('renders loading card with default props', () => {
    render(<LoadingCard />);
    
    expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders with custom title and description', () => {
    render(
      <LoadingCard 
        title="Loading Data" 
        description="Please wait while we fetch your information"
      />
    );
    
    expect(screen.getByText('Loading Data')).toBeInTheDocument();
    expect(screen.getByText('Please wait while we fetch your information')).toBeInTheDocument();
  });

  it('renders with different variants', () => {
    const { rerender, container } = render(<LoadingCard variant="default" />);
    expect(container.firstChild).toHaveClass('border');
    
    rerender(<LoadingCard variant="ghost" />);
    expect(container.firstChild).not.toHaveClass('border');
  });

  it('applies custom className', () => {
    const { container } = render(<LoadingCard className="custom-card" />);
    
    expect(container.firstChild).toHaveClass('custom-card');
  });

  it('renders with card styling', () => {
    const { container } = render(<LoadingCard />);
    
    expect(container.firstChild).toHaveClass('rounded-lg', 'p-6');
  });
});

describe('LoadingSkeleton Component', () => {
  it('renders skeleton with default props', () => {
    const { container } = render(<LoadingSkeleton />);
    
    expect(container.firstChild).toHaveClass('animate-pulse', 'bg-gray-200', 'rounded');
  });

  it('renders with different variants', () => {
    const { rerender, container } = render(<LoadingSkeleton variant="text" />);
    expect(container.firstChild).toHaveClass('h-4', 'w-full');
    
    rerender(<LoadingSkeleton variant="avatar" />);
    expect(container.firstChild).toHaveClass('h-10', 'w-10', 'rounded-full');
    
    rerender(<LoadingSkeleton variant="button" />);
    expect(container.firstChild).toHaveClass('h-10', 'w-20');
    
    rerender(<LoadingSkeleton variant="card" />);
    expect(container.firstChild).toHaveClass('h-32', 'w-full');
  });

  it('renders with custom dimensions', () => {
    const { container } = render(<LoadingSkeleton width="200px" height="50px" />);
    
    expect(container.firstChild).toHaveStyle({
      width: '200px',
      height: '50px',
    });
  });

  it('applies custom className', () => {
    const { container } = render(<LoadingSkeleton className="custom-skeleton" />);
    
    expect(container.firstChild).toHaveClass('custom-skeleton');
  });

  it('renders multiple lines when count is specified', () => {
    const { container } = render(<LoadingSkeleton variant="text" count={3} />);
    
    const skeletons = container.querySelectorAll('.animate-pulse');
    expect(skeletons).toHaveLength(3);
  });
});

describe('LoadingButton Component', () => {
  it('renders loading button with spinner', () => {
    render(<LoadingButton loading={true}>Save</LoadingButton>);
    
    expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
  });

  it('renders normal button when not loading', () => {
    render(<LoadingButton loading={false}>Save</LoadingButton>);
    
    expect(screen.queryByTestId('loader-icon')).not.toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
  });

  it('shows loading text when provided', () => {
    render(
      <LoadingButton loading={true} loadingText="Saving...">
        Save
      </LoadingButton>
    );
    
    expect(screen.getByText('Saving...')).toBeInTheDocument();
    expect(screen.queryByText('Save')).not.toBeInTheDocument();
  });

  it('disables button when loading', () => {
    render(<LoadingButton loading={true}>Save</LoadingButton>);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('applies button variants', () => {
    const { rerender } = render(
      <LoadingButton variant="primary">Save</LoadingButton>
    );
    expect(screen.getByRole('button')).toHaveClass('btn-primary');
    
    rerender(<LoadingButton variant="secondary">Save</LoadingButton>);
    expect(screen.getByRole('button')).toHaveClass('btn-secondary');
  });

  it('handles click events when not loading', () => {
    const onClick = vi.fn();
    render(
      <LoadingButton loading={false} onClick={onClick}>
        Save
      </LoadingButton>
    );
    
    const button = screen.getByRole('button');
    button.click();
    
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('does not handle click events when loading', () => {
    const onClick = vi.fn();
    render(
      <LoadingButton loading={true} onClick={onClick}>
        Save
      </LoadingButton>
    );
    
    const button = screen.getByRole('button');
    button.click();
    
    expect(onClick).not.toHaveBeenCalled();
  });
});

describe('LoadingOverlay Component', () => {
  it('renders overlay when visible', () => {
    render(
      <LoadingOverlay visible={true}>
        <div>Content</div>
      </LoadingOverlay>
    );
    
    expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('does not render overlay when not visible', () => {
    render(
      <LoadingOverlay visible={false}>
        <div>Content</div>
      </LoadingOverlay>
    );
    
    expect(screen.queryByTestId('loader-icon')).not.toBeInTheDocument();
    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('renders with custom message', () => {
    render(
      <LoadingOverlay visible={true} message="Processing...">
        <div>Content</div>
      </LoadingOverlay>
    );
    
    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });

  it('applies overlay styling', () => {
    const { container } = render(
      <LoadingOverlay visible={true}>
        <div>Content</div>
      </LoadingOverlay>
    );
    
    const overlay = container.querySelector('.absolute');
    expect(overlay).toHaveClass('inset-0', 'bg-white', 'bg-opacity-75');
  });

  it('renders with different opacity levels', () => {
    const { container } = render(
      <LoadingOverlay visible={true} opacity={0.9}>
        <div>Content</div>
      </LoadingOverlay>
    );
    
    const overlay = container.querySelector('.absolute');
    expect(overlay).toHaveStyle({ backgroundColor: 'rgba(255, 255, 255, 0.9)' });
  });

  it('centers loading content', () => {
    const { container } = render(
      <LoadingOverlay visible={true}>
        <div>Content</div>
      </LoadingOverlay>
    );
    
    const loadingContent = container.querySelector('.flex.items-center.justify-center');
    expect(loadingContent).toBeInTheDocument();
  });
});

describe('Loading States Integration', () => {
  it('renders skeleton while loading, then content', () => {
    const { rerender } = render(
      <div>
        {true ? <LoadingSkeleton variant="card" /> : <div>Loaded content</div>}
      </div>
    );
    
    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
    
    rerender(
      <div>
        {false ? <LoadingSkeleton variant="card" /> : <div>Loaded content</div>}
      </div>
    );
    
    expect(screen.queryByTestId('loading-skeleton')).not.toBeInTheDocument();
    expect(screen.getByText('Loaded content')).toBeInTheDocument();
  });

  it('combines loading states appropriately', () => {
    render(
      <LoadingCard>
        <LoadingSkeleton variant="text" count={3} />
      </LoadingCard>
    );
    
    expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
    const skeletons = screen.getAllByTestId('loading-skeleton');
    expect(skeletons).toHaveLength(3);
  });
});
