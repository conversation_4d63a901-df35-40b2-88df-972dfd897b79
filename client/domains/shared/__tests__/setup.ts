/**
 * Shared Components Test Setup
 * Global test setup for shared component tests
 */

import { vi } from 'vitest';
import '@testing-library/jest-dom';

// Mock React Router
vi.mock('react-router-dom', () => ({
  Link: ({ children, to, ...props }: any) => (
    <a href={to} {...props}>
      {children}
    </a>
  ),
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/' }),
  useParams: () => ({}),
  useSearchParams: () => [new URLSearchParams(), vi.fn()],
}));

// Mock toast notifications
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
  },
}));

// Mock React Hook Form
vi.mock('react-hook-form', () => ({
  useForm: () => ({
    register: vi.fn(),
    handleSubmit: vi.fn((fn) => fn),
    formState: { errors: {}, isSubmitting: false },
    setValue: vi.fn(),
    getValue: vi.fn(),
    watch: vi.fn(),
    reset: vi.fn(),
    control: {},
  }),
  useFormContext: () => ({
    register: vi.fn(),
    formState: { errors: {} },
    setValue: vi.fn(),
    getValue: vi.fn(),
    watch: vi.fn(),
    control: {},
  }),
  Controller: ({ render }: any) => render({ field: { onChange: vi.fn(), value: '' } }),
  FormProvider: ({ children }: any) => children,
}));

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(() => ({
    data: null,
    isLoading: false,
    isError: false,
    error: null,
    refetch: vi.fn(),
  })),
  useMutation: vi.fn(() => ({
    mutate: vi.fn(),
    mutateAsync: vi.fn(),
    isLoading: false,
    isError: false,
    error: null,
  })),
  useQueryClient: vi.fn(() => ({
    invalidateQueries: vi.fn(),
    setQueryData: vi.fn(),
    getQueryData: vi.fn(),
  })),
  QueryClient: vi.fn(),
  QueryClientProvider: ({ children }: any) => children,
}));

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date: Date, formatStr: string) => {
    if (formatStr === 'PPP p') return 'January 15, 2024 at 10:00 AM';
    if (formatStr === 'PPP') return 'January 15, 2024';
    if (formatStr === 'p') return '10:00 AM';
    if (formatStr === 'yyyy-MM-dd') return '2024-01-15';
    return date.toISOString();
  }),
  formatDistanceToNow: vi.fn(() => '2 hours ago'),
  isToday: vi.fn(() => false),
  isTomorrow: vi.fn(() => false),
  isYesterday: vi.fn(() => false),
  startOfDay: vi.fn((date: Date) => date),
  endOfDay: vi.fn((date: Date) => date),
  addDays: vi.fn((date: Date, days: number) => new Date(date.getTime() + days * 24 * 60 * 60 * 1000)),
  subDays: vi.fn((date: Date, days: number) => new Date(date.getTime() - days * 24 * 60 * 60 * 1000)),
}));

// Mock Zod validation
vi.mock('zod', () => ({
  z: {
    string: () => ({
      min: vi.fn().mockReturnThis(),
      max: vi.fn().mockReturnThis(),
      email: vi.fn().mockReturnThis(),
      optional: vi.fn().mockReturnThis(),
      parse: vi.fn(),
      safeParse: vi.fn(() => ({ success: true, data: {} })),
    }),
    number: () => ({
      min: vi.fn().mockReturnThis(),
      max: vi.fn().mockReturnThis(),
      optional: vi.fn().mockReturnThis(),
      parse: vi.fn(),
      safeParse: vi.fn(() => ({ success: true, data: 0 })),
    }),
    boolean: () => ({
      optional: vi.fn().mockReturnThis(),
      parse: vi.fn(),
      safeParse: vi.fn(() => ({ success: true, data: false })),
    }),
    object: vi.fn(() => ({
      parse: vi.fn(),
      safeParse: vi.fn(() => ({ success: true, data: {} })),
    })),
    array: vi.fn(() => ({
      parse: vi.fn(),
      safeParse: vi.fn(() => ({ success: true, data: [] })),
    })),
    enum: vi.fn(() => ({
      parse: vi.fn(),
      safeParse: vi.fn(() => ({ success: true, data: 'value' })),
    })),
  },
}));

// Mock file operations
global.File = class MockFile {
  name: string;
  size: number;
  type: string;
  
  constructor(bits: any[], name: string, options: any = {}) {
    this.name = name;
    this.size = bits.reduce((acc, bit) => acc + (bit.length || 0), 0);
    this.type = options.type || '';
  }
  
  text() {
    return Promise.resolve('mock file content');
  }
  
  arrayBuffer() {
    return Promise.resolve(new ArrayBuffer(0));
  }
} as any;

global.FileReader = class MockFileReader {
  result: string | ArrayBuffer | null = null;
  onload: ((event: any) => void) | null = null;
  onerror: ((event: any) => void) | null = null;
  
  readAsText(file: File) {
    setTimeout(() => {
      this.result = 'mock file content';
      if (this.onload) {
        this.onload({ target: { result: this.result } });
      }
    }, 0);
  }
  
  readAsDataURL(file: File) {
    setTimeout(() => {
      this.result = 'data:text/plain;base64,bW9jayBmaWxlIGNvbnRlbnQ=';
      if (this.onload) {
        this.onload({ target: { result: this.result } });
      }
    }, 0);
  }
} as any;

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(() => Promise.resolve()),
    readText: vi.fn(() => Promise.resolve('mock clipboard content')),
  },
});

// Mock intersection observer
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock resize observer
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock match media
global.matchMedia = vi.fn().mockImplementation((query) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

// Mock window methods
global.scrollTo = vi.fn();
global.alert = vi.fn();
global.confirm = vi.fn(() => true);
global.prompt = vi.fn(() => 'mock input');

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-object-url');
global.URL.revokeObjectURL = vi.fn();

// Mock fetch
global.fetch = vi.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob()),
  })
) as any;

// Mock console methods for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  // Suppress console errors/warnings in tests unless explicitly needed
  console.error = vi.fn();
  console.warn = vi.fn();
});

afterEach(() => {
  // Restore console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  
  // Clear all mocks
  vi.clearAllMocks();
});

// Test utilities
export const createMockEvent = (type: string, properties: any = {}) => ({
  type,
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: { value: '' },
  currentTarget: { value: '' },
  ...properties,
});

export const createMockFile = (name: string, content: string, type: string = 'text/plain') => {
  return new File([content], name, { type });
};

export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

export const mockApiResponse = (data: any, success: boolean = true) => ({
  data,
  success,
  message: success ? 'Success' : 'Error',
  errors: success ? [] : ['Mock error'],
});

export const mockApiError = (message: string = 'API Error') => {
  const error = new Error(message);
  (error as any).response = {
    status: 500,
    data: {
      success: false,
      message,
      errors: [message],
    },
  };
  return error;
};
