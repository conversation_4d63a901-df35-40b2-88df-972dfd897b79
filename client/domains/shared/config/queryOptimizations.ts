/**
 * React Query Optimizations
 * Centralized configuration for React Query performance optimizations
 */

import { QueryClient, QueryClientConfig } from "@tanstack/react-query";

// Cache time configurations (in milliseconds)
export const cacheTimeConfig = {
  // Static data that rarely changes
  static: 30 * 60 * 1000, // 30 minutes

  // Semi-static data (user preferences, settings)
  semiStatic: 15 * 60 * 1000, // 15 minutes

  // Dynamic data that changes frequently
  dynamic: 5 * 60 * 1000, // 5 minutes

  // Real-time data
  realTime: 1 * 60 * 1000, // 1 minute

  // Search results and filters
  search: 2 * 60 * 1000, // 2 minutes

  // User-specific data
  user: 10 * 60 * 1000, // 10 minutes
} as const;

// Stale time configurations
export const staleTimeConfig = {
  // Data considered fresh for longer periods
  static: 10 * 60 * 1000, // 10 minutes
  semiStatic: 5 * 60 * 1000, // 5 minutes
  dynamic: 2 * 60 * 1000, // 2 minutes
  realTime: 30 * 1000, // 30 seconds
  search: 1 * 60 * 1000, // 1 minute
  user: 3 * 60 * 1000, // 3 minutes
} as const;

// Create optimized QueryClient
export const createOptimizedQueryClient = (): QueryClient => {
  const config: QueryClientConfig = {
    defaultOptions: {
      queries: {
        // Default configurations
        staleTime: staleTimeConfig.dynamic,
        cacheTime: cacheTimeConfig.dynamic,
        retry: 3,
        retryDelay: (attemptIndex: number) =>
          Math.min(1000 * 2 ** attemptIndex, 30000),

        // Background sync
        refetchOnWindowFocus: true,
        refetchOnReconnect: true,
        refetchOnMount: true,

        // Error handling
        useErrorBoundary: false,

        // Network mode
        networkMode: "online",
      },

      mutations: {
        // Retry critical operations
        retry: 5,
        retryDelay: (attemptIndex: number) =>
          Math.min(500 * 2 ** attemptIndex, 10000),

        // Error handling
        useErrorBoundary: false,

        // Network mode
        networkMode: "online",
      },
    },
  };

  return new QueryClient(config);
};
  static: 30 * 60 * 1000, // 30 minutes

  // Semi-static data (user preferences, settings)
  semiStatic: 15 * 60 * 1000, // 15 minutes

  // Dynamic data that changes frequently
  dynamic: 5 * 60 * 1000, // 5 minutes

  // Real-time data
  realTime: 1 * 60 * 1000, // 1 minute

  // Search results and filters
  search: 2 * 60 * 1000, // 2 minutes

  // User-specific data
  user: 10 * 60 * 1000, // 10 minutes
} as const;

// Stale time configurations
export const staleTimeConfig = {
  // Data considered fresh for longer periods
  static: 10 * 60 * 1000, // 10 minutes
  semiStatic: 5 * 60 * 1000, // 5 minutes
  dynamic: 2 * 60 * 1000, // 2 minutes
  realTime: 30 * 1000, // 30 seconds
  search: 1 * 60 * 1000, // 1 minute
  user: 3 * 60 * 1000, // 3 minutes
} as const;

// Retry configurations
export const retryConfig = {
  // Default retry configuration
  default: {
    retry: 3,
    retryDelay: (attemptIndex: number) =>
      Math.min(1000 * 2 ** attemptIndex, 30000),
  },

  // Critical operations (mutations)
  critical: {
    retry: 5,
    retryDelay: (attemptIndex: number) =>
      Math.min(500 * 2 ** attemptIndex, 10000),
  },

  // Background operations
  background: {
    retry: 2,
    retryDelay: (attemptIndex: number) =>
      Math.min(2000 * 2 ** attemptIndex, 60000),
  },

  // Search operations
  search: {
    retry: 1,
    retryDelay: 1000,
  },
} as const;

// Domain-specific query configurations
export const domainQueryConfig = {
  candidates: {
    list: {
      staleTime: staleTimeConfig.dynamic,
      cacheTime: cacheTimeConfig.dynamic,
      ...retryConfig.default,
    },
    detail: {
      staleTime: staleTimeConfig.semiStatic,
      cacheTime: cacheTimeConfig.semiStatic,
      ...retryConfig.default,
    },
    search: {
      staleTime: staleTimeConfig.search,
      cacheTime: cacheTimeConfig.search,
      ...retryConfig.search,
    },
  },

  jobs: {
    list: {
      staleTime: staleTimeConfig.dynamic,
      cacheTime: cacheTimeConfig.dynamic,
      ...retryConfig.default,
    },
    detail: {
      staleTime: staleTimeConfig.semiStatic,
      cacheTime: cacheTimeConfig.semiStatic,
      ...retryConfig.default,
    },
    search: {
      staleTime: staleTimeConfig.search,
      cacheTime: cacheTimeConfig.search,
      ...retryConfig.search,
    },
  },

  interviews: {
    list: {
      staleTime: staleTimeConfig.realTime,
      cacheTime: cacheTimeConfig.realTime,
      ...retryConfig.default,
    },
    detail: {
      staleTime: staleTimeConfig.dynamic,
      cacheTime: cacheTimeConfig.dynamic,
      ...retryConfig.default,
    },
    upcoming: {
      staleTime: staleTimeConfig.realTime,
      cacheTime: cacheTimeConfig.realTime,
      ...retryConfig.default,
      refetchInterval: 2 * 60 * 1000, // 2 minutes
    },
  },

  calendar: {
    events: {
      staleTime: staleTimeConfig.dynamic,
      cacheTime: cacheTimeConfig.dynamic,
      ...retryConfig.default,
    },
    availability: {
      staleTime: staleTimeConfig.realTime,
      cacheTime: cacheTimeConfig.realTime,
      ...retryConfig.default,
    },
  },

  analytics: {
    dashboard: {
      staleTime: staleTimeConfig.dynamic,
      cacheTime: cacheTimeConfig.dynamic,
      ...retryConfig.background,
      refetchInterval: 5 * 60 * 1000, // 5 minutes
    },
    reports: {
      staleTime: staleTimeConfig.semiStatic,
      cacheTime: cacheTimeConfig.semiStatic,
      ...retryConfig.background,
    },
    metrics: {
      staleTime: staleTimeConfig.dynamic,
      cacheTime: cacheTimeConfig.dynamic,
      ...retryConfig.default,
    },
  },
} as const;

// Prefetch configurations
export const prefetchConfig = {
  // Prefetch related data when viewing details
  relatedData: {
    enabled: true,
    delay: 500, // ms
  },

  // Prefetch next page in pagination
  nextPage: {
    enabled: true,
    threshold: 0.8, // Prefetch when 80% through current page
  },

  // Prefetch on hover
  onHover: {
    enabled: true,
    delay: 300, // ms
  },

  // Prefetch on route change
  onRouteChange: {
    enabled: true,
    routes: ["/candidates", "/jobs", "/interviews", "/calendar", "/analytics"],
  },
} as const;

// Background sync configuration
export const backgroundSyncConfig = {
  // Enable background refetching
  enabled: true,

  // Refetch on window focus
  refetchOnWindowFocus: true,

  // Refetch on reconnect
  refetchOnReconnect: true,

  // Refetch on mount
  refetchOnMount: true,

  // Background refetch intervals by data type
  intervals: {
    critical: 1 * 60 * 1000, // 1 minute
    normal: 5 * 60 * 1000, // 5 minutes
    background: 15 * 60 * 1000, // 15 minutes
  },
} as const;

// Optimistic updates configuration
export const optimisticUpdatesConfig = {
  // Enable optimistic updates for mutations
  enabled: true,

  // Operations that support optimistic updates
  supportedOperations: [
    "updateCandidate",
    "updateJob",
    "updateInterview",
    "updateCalendarEvent",
    "toggleFavorite",
    "updateStatus",
  ],

  // Rollback timeout
  rollbackTimeout: 5000, // ms
} as const;

// Query deduplication configuration
export const deduplicationConfig = {
  // Enable query deduplication
  enabled: true,

  // Deduplication window
  window: 1000, // ms

  // Keys to deduplicate
  keys: ["candidates", "jobs", "interviews", "calendar-events", "analytics"],
} as const;

// Memory management configuration
export const memoryConfig = {
  // Maximum number of queries to keep in cache
  maxQueries: 100,

  // Garbage collection interval
  gcTime: 5 * 60 * 1000, // 5 minutes

  // Memory usage threshold for cleanup
  memoryThreshold: 50 * 1024 * 1024, // 50MB

  // Inactive query cleanup time
  inactiveTime: 10 * 60 * 1000, // 10 minutes
} as const;

// Performance monitoring configuration
export const performanceConfig = {
  // Enable performance monitoring
  enabled: true,

  // Metrics to track
  metrics: [
    "queryTime",
    "cacheHitRate",
    "errorRate",
    "retryCount",
    "backgroundFetches",
  ],

  // Performance thresholds
  thresholds: {
    queryTime: 2000, // ms
    errorRate: 0.05, // 5%
    cacheHitRate: 0.8, // 80%
  },

  // Logging configuration
  logging: {
    enabled: process.env.NODE_ENV === "development",
    level: "warn" as "error" | "warn" | "info" | "debug",
  },
} as const;

// Create optimized QueryClient
export const createOptimizedQueryClient = (): QueryClient => {
  const config: QueryClientConfig = {
    defaultOptions: {
      queries: {
        // Default configurations
        staleTime: staleTimeConfig.dynamic,
        cacheTime: cacheTimeConfig.dynamic,
        retry: retryConfig.default.retry,
        retryDelay: retryConfig.default.retryDelay,

        // Background sync
        refetchOnWindowFocus: backgroundSyncConfig.refetchOnWindowFocus,
        refetchOnReconnect: backgroundSyncConfig.refetchOnReconnect,
        refetchOnMount: backgroundSyncConfig.refetchOnMount,

        // Error handling
        useErrorBoundary: false,

        // Network mode
        networkMode: "online",
      },

      mutations: {
        // Retry critical operations
        retry: retryConfig.critical.retry,
        retryDelay: retryConfig.critical.retryDelay,

        // Error handling
        useErrorBoundary: false,

        // Network mode
        networkMode: "online",
      },
    },
  };

  const queryClient = new QueryClient(config);

  // Add performance monitoring
  if (performanceConfig.enabled) {
    queryClient.getQueryCache().subscribe((event) => {
      if (performanceConfig.logging.enabled) {
        console.log("Query cache event:", event);
      }

      // Track metrics
      if (typeof window !== "undefined" && (window as any).analytics) {
        (window as any).analytics.track("Query Cache Event", {
          type: event.type,
          query: event.query?.queryKey,
        });
      }
    });

    queryClient.getMutationCache().subscribe((event) => {
      if (performanceConfig.logging.enabled) {
        console.log("Mutation cache event:", event);
      }

      // Track metrics
      if (typeof window !== "undefined" && (window as any).analytics) {
        (window as any).analytics.track("Mutation Cache Event", {
          type: event.type,
          mutation: event.mutation?.options.mutationKey,
        });
      }
    });
  }

  return queryClient;
};

// Utility functions for query optimization
export const getQueryConfig = (
  domain: keyof typeof domainQueryConfig,
  type: string,
) => {
  const domainConfig = domainQueryConfig[domain];
  return (
    domainConfig?.[type as keyof typeof domainConfig] ||
    domainQueryConfig.candidates.list
  );
};

export const shouldPrefetch = (operation: string): boolean => {
  return (
    prefetchConfig.relatedData.enabled && prefetchConfig.onRouteChange.enabled
  );
};

export const getOptimisticUpdateConfig = (operation: string) => {
  return {
    enabled:
      optimisticUpdatesConfig.enabled &&
      optimisticUpdatesConfig.supportedOperations.includes(operation),
    timeout: optimisticUpdatesConfig.rollbackTimeout,
  };
};

// Export combined configuration
export const queryOptimizations = {
  cacheTime: cacheTimeConfig,
  staleTime: staleTimeConfig,
  retry: retryConfig,
  domains: domainQueryConfig,
  prefetch: prefetchConfig,
  backgroundSync: backgroundSyncConfig,
  optimisticUpdates: optimisticUpdatesConfig,
  deduplication: deduplicationConfig,
  memory: memoryConfig,
  performance: performanceConfig,
  createClient: createOptimizedQueryClient,
  getConfig: getQueryConfig,
  shouldPrefetch,
  getOptimisticConfig: getOptimisticUpdateConfig,
} as const;
