/**
 * Lazy Loading Configuration
 * Central configuration for lazy loading across all domains
 */

import { 
  candidateComponentMap, 
  preloadCandidateComponents,
  type CandidateComponentName 
} from '../../candidates/components/lazy';
import { 
  jobComponentMap, 
  preloadJobComponents,
  type JobComponentName 
} from '../../jobs/components/lazy';
import { 
  interviewComponentMap, 
  preloadInterviewComponents,
  type InterviewComponentName 
} from '../../interviews/components/lazy';
import { 
  calendarComponentMap, 
  preloadCalendarComponents,
  type CalendarComponentName 
} from '../../calendar/components/lazy';
import { 
  analyticsComponentMap, 
  preloadAnalyticsComponents,
  type AnalyticsComponentName 
} from '../../analytics/components/lazy';

// Combined component map
export const domainComponentMap = {
  candidates: candidateComponentMap,
  jobs: jobComponentMap,
  interviews: interviewComponentMap,
  calendar: calendarComponentMap,
  analytics: analyticsComponentMap,
} as const;

// Domain types
export type DomainName = keyof typeof domainComponentMap;
export type ComponentName<T extends DomainName> = T extends 'candidates'
  ? CandidateComponentName
  : T extends 'jobs'
  ? JobComponentName
  : T extends 'interviews'
  ? InterviewComponentName
  : T extends 'calendar'
  ? CalendarComponentName
  : T extends 'analytics'
  ? AnalyticsComponentName
  : never;

// Preload functions by domain
export const domainPreloaders = {
  candidates: preloadCandidateComponents,
  jobs: preloadJobComponents,
  interviews: preloadInterviewComponents,
  calendar: preloadCalendarComponents,
  analytics: preloadAnalyticsComponents,
} as const;

// Route-based preloading configuration
export const routePreloadConfig = {
  '/candidates': ['candidates'],
  '/candidates/:id': ['candidates'],
  '/jobs': ['jobs'],
  '/jobs/:id': ['jobs'],
  '/interviews': ['interviews', 'calendar'],
  '/calendar': ['calendar', 'interviews'],
  '/analytics': ['analytics'],
  '/dashboard': ['analytics', 'candidates', 'jobs', 'interviews'],
} as const;

// Priority-based loading configuration
export const loadingPriorities = {
  critical: [
    'candidates.CandidateCard',
    'jobs.JobCard',
    'interviews.InterviewCard',
    'calendar.CalendarView',
    'analytics.MetricCard',
  ],
  high: [
    'candidates.CandidateTable',
    'jobs.JobTable',
    'interviews.InterviewTable',
    'calendar.MonthView',
    'analytics.PerformanceChartWidget',
  ],
  medium: [
    'candidates.EditCandidateModal',
    'jobs.AddEditJobModal',
    'interviews.EditInterviewModal',
    'calendar.EventModal',
    'analytics.ReportBuilder',
  ],
  low: [
    'candidates.ResumeExtractionModal',
    'jobs.BulkJobActions',
    'interviews.BulkInterviewActions',
    'calendar.RecurrenceSelector',
    'analytics.ScheduledReports',
  ],
} as const;

// Performance thresholds
export const performanceConfig = {
  // Maximum time to wait for component loading (ms)
  maxLoadTime: 5000,
  
  // Retry configuration
  maxRetries: 3,
  retryDelay: 1000,
  
  // Preload timing
  preloadDelay: 100, // Delay before starting preload
  preloadOnHover: true,
  preloadOnFocus: true,
  
  // Bundle size thresholds (bytes)
  bundleSizeWarning: 100 * 1024, // 100KB
  bundleSizeError: 500 * 1024,   // 500KB
} as const;

// Lazy loading strategies
export const loadingStrategies = {
  // Load immediately when component is needed
  immediate: 'immediate',
  
  // Load when user hovers over trigger element
  onHover: 'onHover',
  
  // Load when element comes into viewport
  onVisible: 'onVisible',
  
  // Load after a delay
  delayed: 'delayed',
  
  // Load when user is idle
  onIdle: 'onIdle',
} as const;

export type LoadingStrategy = keyof typeof loadingStrategies;

// Component-specific loading strategies
export const componentLoadingStrategies: Record<string, LoadingStrategy> = {
  // Critical components - load immediately
  'candidates.CandidateCard': 'immediate',
  'jobs.JobCard': 'immediate',
  'interviews.InterviewCard': 'immediate',
  'calendar.CalendarView': 'immediate',
  'analytics.MetricCard': 'immediate',
  
  // Modals - load on hover
  'candidates.EditCandidateModal': 'onHover',
  'jobs.AddEditJobModal': 'onHover',
  'interviews.EditInterviewModal': 'onHover',
  'calendar.EventModal': 'onHover',
  
  // Heavy components - load when visible
  'analytics.ReportBuilder': 'onVisible',
  'candidates.ResumeExtractionModal': 'onVisible',
  
  // Utility components - load when idle
  'candidates.BulkActionsBar': 'onIdle',
  'jobs.BulkJobActions': 'onIdle',
  'interviews.BulkInterviewActions': 'onIdle',
};

// Error handling configuration
export const errorHandlingConfig = {
  // Show error boundary for these component types
  showErrorBoundary: ['page', 'modal', 'table'],
  
  // Retry automatically for these error types
  autoRetryErrors: ['ChunkLoadError', 'NetworkError'],
  
  // Fallback components for different error scenarios
  fallbacks: {
    ChunkLoadError: 'Please refresh the page to load the latest version.',
    NetworkError: 'Please check your internet connection and try again.',
    ComponentError: 'This component failed to load. Please try again.',
    TimeoutError: 'Component loading timed out. Please try again.',
  },
} as const;

// Monitoring and analytics
export const monitoringConfig = {
  // Track loading performance
  trackPerformance: true,
  
  // Send metrics to analytics
  sendMetrics: true,
  
  // Log errors to console in development
  logErrors: process.env.NODE_ENV === 'development',
  
  // Metrics to track
  metrics: [
    'loadTime',
    'bundleSize',
    'errorRate',
    'retryCount',
    'cacheHitRate',
  ],
} as const;

// Cache configuration
export const cacheConfig = {
  // Enable component caching
  enabled: true,
  
  // Cache duration (ms)
  duration: 30 * 60 * 1000, // 30 minutes
  
  // Maximum cache size (number of components)
  maxSize: 50,
  
  // Cache key strategy
  keyStrategy: 'componentName' as 'componentName' | 'componentPath' | 'componentHash',
} as const;

// Development configuration
export const developmentConfig = {
  // Show loading indicators in development
  showLoadingIndicators: process.env.NODE_ENV === 'development',
  
  // Log component loading in development
  logComponentLoading: process.env.NODE_ENV === 'development',
  
  // Disable lazy loading in development (for debugging)
  disableLazyLoading: false,
  
  // Show bundle size warnings
  showBundleSizeWarnings: process.env.NODE_ENV === 'development',
} as const;

// Export combined configuration
export const lazyLoadingConfig = {
  domains: domainComponentMap,
  preloaders: domainPreloaders,
  routes: routePreloadConfig,
  priorities: loadingPriorities,
  performance: performanceConfig,
  strategies: componentLoadingStrategies,
  errorHandling: errorHandlingConfig,
  monitoring: monitoringConfig,
  cache: cacheConfig,
  development: developmentConfig,
} as const;

// Utility functions
export const getDomainComponent = <T extends DomainName>(
  domain: T,
  componentName: ComponentName<T>
) => {
  return domainComponentMap[domain][componentName as keyof typeof domainComponentMap[T]];
};

export const preloadDomain = async (domain: DomainName) => {
  const preloader = domainPreloaders[domain];
  if (preloader) {
    try {
      await preloader();
      console.log(`Preloaded ${domain} components`);
    } catch (error) {
      console.error(`Failed to preload ${domain} components:`, error);
    }
  }
};

export const preloadRoute = async (route: string) => {
  const domains = routePreloadConfig[route as keyof typeof routePreloadConfig];
  if (domains) {
    await Promise.all(domains.map(domain => preloadDomain(domain)));
  }
};

export const getLoadingStrategy = (componentKey: string): LoadingStrategy => {
  return componentLoadingStrategies[componentKey] || 'immediate';
};
