# Domain API Documentation

This document provides comprehensive API documentation for all domain services in the HireFlow ATS system.

## 🏗️ API Architecture

The API layer follows a consistent pattern across all domains:
- **Services**: Handle API communication and business logic
- **Adapters**: Transform data between API and application formats
- **Types**: Define request/response interfaces
- **Error Handling**: Consistent error handling across all domains

## 🔧 Base Service

All domain services extend the `BaseService` class which provides:
- HTTP methods (GET, POST, PUT, DELETE)
- Request/response interceptors
- Error handling
- Authentication headers
- Request/response logging

```typescript
export abstract class BaseService {
  protected async get<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>
  protected async post<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>
  protected async put<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>
  protected async delete<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>
}
```

## 👥 Candidates Domain API

### CandidateService

#### `getCandidates(filters?: CandidateFilters): Promise<ApiResponse<Candidate[]>>`
Retrieves a list of candidates with optional filtering.

**Parameters:**
- `filters` (optional): Filtering criteria
  - `search?: string` - Search query for name, email, or skills
  - `status?: CandidateStatus[]` - Filter by candidate status
  - `source?: CandidateSource[]` - Filter by candidate source
  - `skills?: string[]` - Filter by required skills
  - `experience?: { min?: number; max?: number }` - Experience range
  - `dateFrom?: string` - Filter candidates from date
  - `dateTo?: string` - Filter candidates to date

**Response:**
```typescript
{
  data: Candidate[],
  meta: {
    total: number,
    page: number,
    limit: number,
    hasNext: boolean,
    hasPrev: boolean
  },
  success: boolean
}
```

**Example:**
```typescript
const candidates = await candidateService.getCandidates({
  status: ['applied', 'interview'],
  skills: ['JavaScript', 'React'],
  experience: { min: 2, max: 5 }
});
```

#### `getCandidateById(id: string): Promise<ApiResponse<Candidate>>`
Retrieves a single candidate by ID.

#### `createCandidate(data: CreateCandidateData): Promise<ApiResponse<Candidate>>`
Creates a new candidate.

**Parameters:**
```typescript
interface CreateCandidateData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  experience?: number;
  skills: string[];
  source: CandidateSource;
  resume?: File;
}
```

#### `updateCandidate(id: string, data: UpdateCandidateData): Promise<ApiResponse<Candidate>>`
Updates an existing candidate.

#### `deleteCandidate(id: string): Promise<ApiResponse<void>>`
Deletes a candidate.

#### `updateCandidateStatus(id: string, status: CandidateStatus): Promise<ApiResponse<Candidate>>`
Updates candidate status in the pipeline.

## 💼 Jobs Domain API

### JobService

#### `getJobs(filters?: JobFilters): Promise<ApiResponse<Job[]>>`
Retrieves a list of jobs with optional filtering.

**Parameters:**
- `filters` (optional): Filtering criteria
  - `search?: string` - Search query for title or description
  - `status?: JobStatus[]` - Filter by job status
  - `department?: string[]` - Filter by department
  - `location?: string[]` - Filter by location
  - `employmentType?: EmploymentType[]` - Filter by employment type
  - `salaryRange?: { min?: number; max?: number }` - Salary range
  - `dateFrom?: string` - Filter jobs from date
  - `dateTo?: string` - Filter jobs to date

#### `createJob(data: CreateJobData): Promise<ApiResponse<Job>>`
Creates a new job posting.

**Parameters:**
```typescript
interface CreateJobData {
  title: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  department: string;
  location: string;
  employmentType: EmploymentType;
  salaryRange: {
    min: number;
    max: number;
    currency: string;
  };
  benefits?: string[];
  skills: string[];
}
```

#### `updateJob(id: string, data: UpdateJobData): Promise<ApiResponse<Job>>`
Updates an existing job.

#### `deleteJob(id: string): Promise<ApiResponse<void>>`
Deletes a job posting.

#### `publishJob(id: string): Promise<ApiResponse<Job>>`
Publishes a job to external job boards.

#### `unpublishJob(id: string): Promise<ApiResponse<Job>>`
Unpublishes a job from external job boards.

## 🎤 Interviews Domain API

### InterviewService

#### `getInterviews(params?: InterviewQueryParams): Promise<ApiResponse<Interview[]>>`
Retrieves a list of interviews with optional filtering.

**Parameters:**
```typescript
interface InterviewQueryParams {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: InterviewStatus[];
  type?: InterviewType[];
  round?: InterviewRound[];
  candidateId?: string;
  jobId?: string;
  interviewerId?: string;
  page?: number;
  limit?: number;
  sort?: string;
}
```

#### `createInterview(data: CreateInterviewData): Promise<ApiResponse<Interview>>`
Schedules a new interview.

**Parameters:**
```typescript
interface CreateInterviewData {
  title: string;
  type: InterviewType;
  round: InterviewRound;
  candidateId: string;
  jobId: string;
  interviewers: string[]; // User IDs
  scheduledAt: string;
  duration: number; // in minutes
  timezone: string;
  location?: string;
  meetingUrl?: string;
  description?: string;
  agenda?: string;
}
```

#### `updateInterview(id: string, data: UpdateInterviewData): Promise<ApiResponse<Interview>>`
Updates an existing interview.

#### `deleteInterview(id: string): Promise<ApiResponse<void>>`
Deletes an interview.

#### `cancelInterview(data: CancelInterviewData): Promise<ApiResponse<Interview>>`
Cancels an interview with reason.

**Parameters:**
```typescript
interface CancelInterviewData {
  id: string;
  reason?: string;
  notifyParticipants?: boolean;
}
```

#### `completeInterview(data: CompleteInterviewData): Promise<ApiResponse<Interview>>`
Marks an interview as completed.

#### `rescheduleInterview(data: RescheduleInterviewData): Promise<ApiResponse<Interview>>`
Reschedules an interview to a new time.

**Parameters:**
```typescript
interface RescheduleInterviewData {
  id: string;
  scheduledAt: string;
  duration?: number;
  reason?: string;
  notifyParticipants?: boolean;
}
```

## 📅 Calendar Domain API

### CalendarService

#### `getCalendarEvents(params: CalendarEventParams): Promise<ApiResponse<CalendarEvent[]>>`
Retrieves calendar events for a date range.

**Parameters:**
```typescript
interface CalendarEventParams {
  start_date: string; // YYYY-MM-DD
  end_date: string;   // YYYY-MM-DD
  type?: EventType[];
  userId?: string;
}
```

#### `createCalendarEvent(data: CreateCalendarEventData): Promise<ApiResponse<CalendarEvent>>`
Creates a new calendar event.

#### `updateCalendarEvent(id: string, data: UpdateCalendarEventData): Promise<ApiResponse<CalendarEvent>>`
Updates an existing calendar event.

#### `deleteCalendarEvent(id: string): Promise<ApiResponse<void>>`
Deletes a calendar event.

#### `getCalendarStatistics(filters?: CalendarFilters): Promise<ApiResponse<CalendarStats>>`
Retrieves calendar statistics and metrics.

## 📊 Analytics Domain API

### AnalyticsService

#### `getDashboardMetrics(): Promise<ApiResponse<DashboardMetrics>>`
Retrieves key dashboard metrics.

**Response:**
```typescript
interface DashboardMetrics {
  totalCandidates: number;
  newApplications: number;
  scheduledInterviews: number;
  successfulHires: number;
  conversionRates: {
    applicationToInterview: number;
    interviewToOffer: number;
    offerToHire: number;
  };
  trends: {
    candidates: TrendData;
    interviews: TrendData;
    hires: TrendData;
  };
}
```

#### `getRecruitmentPipeline(): Promise<ApiResponse<PipelineMetrics>>`
Retrieves recruitment pipeline analytics.

#### `getSourceEffectiveness(): Promise<ApiResponse<SourceMetrics>>`
Retrieves candidate source effectiveness data.

#### `getTeamPerformance(): Promise<ApiResponse<TeamMetrics>>`
Retrieves team performance metrics.

## 🔐 Authentication

All API requests require authentication. The system supports:
- **Bearer Token**: JWT tokens for API access
- **Session-based**: Cookie-based authentication for web interface

### Headers
```typescript
{
  'Authorization': 'Bearer <jwt_token>',
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
```

## 📝 Request/Response Format

### Standard Response Format
```typescript
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasNext?: boolean;
    hasPrev?: boolean;
  };
}
```

### Error Response Format
```typescript
interface ErrorResponse {
  success: false;
  message: string;
  errors: string[];
  code?: string;
  details?: any;
}
```

## 🚨 Error Handling

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### Error Types
```typescript
enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  CONFLICT_ERROR = 'CONFLICT_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}
```

## 🔄 Pagination

List endpoints support pagination:

### Request Parameters
```typescript
interface PaginationParams {
  page?: number;     // Page number (1-based)
  limit?: number;    // Items per page (default: 25, max: 100)
  sort?: string;     // Sort field
  order?: 'asc' | 'desc'; // Sort order
}
```

### Response Meta
```typescript
interface PaginationMeta {
  total: number;     // Total number of items
  page: number;      // Current page
  limit: number;     // Items per page
  hasNext: boolean;  // Has next page
  hasPrev: boolean;  // Has previous page
  totalPages: number; // Total number of pages
}
```

## 🔍 Filtering and Search

### Search Parameters
Most list endpoints support search functionality:
```typescript
interface SearchParams {
  search?: string;   // General search query
  filters?: {        // Specific field filters
    [key: string]: any;
  };
}
```

### Date Filtering
Date fields support range filtering:
```typescript
interface DateFilter {
  dateFrom?: string; // ISO date string
  dateTo?: string;   // ISO date string
}
```

## 📈 Rate Limiting

API endpoints are rate-limited to ensure system stability:
- **Standard endpoints**: 100 requests per minute
- **Search endpoints**: 60 requests per minute
- **Upload endpoints**: 10 requests per minute

### Rate Limit Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## 🧪 Testing APIs

### Using the API Client
```typescript
// Example API test
import { candidateService } from '@/domains/candidates/services';

describe('CandidateService', () => {
  it('should fetch candidates', async () => {
    const response = await candidateService.getCandidates();
    expect(response.success).toBe(true);
    expect(Array.isArray(response.data)).toBe(true);
  });
});
```

### Mock Service Worker (MSW)
Use MSW for testing API interactions:
```typescript
// Setup MSW handlers
import { rest } from 'msw';

export const handlers = [
  rest.get('/api/candidates', (req, res, ctx) => {
    return res(
      ctx.json({
        data: mockCandidates,
        success: true,
        meta: { total: 2, page: 1, limit: 25 }
      })
    );
  }),
];
```

## 📚 Additional Resources

- [API Design Best Practices](https://restfulapi.net/)
- [HTTP Status Codes](https://httpstatuses.com/)
- [JWT Authentication](https://jwt.io/introduction/)
- [React Query Documentation](https://tanstack.com/query/latest)
