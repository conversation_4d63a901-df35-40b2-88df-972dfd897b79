# Domain Architecture Migration Guide

This guide provides step-by-step instructions for migrating existing components and pages to the new domain-driven architecture.

## 🎯 Migration Overview

The migration process involves:

1. **Assessment**: Identify components to migrate
2. **Planning**: Determine domain boundaries
3. **Implementation**: Move and refactor components
4. **Testing**: Ensure functionality is preserved
5. **Cleanup**: Remove old code and update imports

## 📋 Pre-Migration Checklist

- [ ] Understand the current codebase structure
- [ ] Identify all components that need migration
- [ ] Map components to appropriate domains
- [ ] Set up testing environment
- [ ] Create backup of current code
- [ ] Plan migration in phases

## 🗺️ Domain Mapping

### Step 1: Identify Components by Domain

Create a mapping of existing components to domains:

```
Current Components → Target Domain
├── CandidateList.tsx → candidates/components/
├── CandidateForm.tsx → candidates/components/
├── JobPosting.tsx → jobs/components/
├── InterviewScheduler.tsx → interviews/components/
├── CalendarWidget.tsx → calendar/components/
└── AnalyticsDashboard.tsx → analytics/components/
```

### Step 2: Identify Shared Components

Components used across multiple domains should go to `shared/components/`:

```
Shared Components → shared/components/
├── DataTable.tsx → shared/components/data-display/
├── Modal.tsx → shared/components/overlays/
├── FormField.tsx → shared/components/forms/
└── LoadingSpinner.tsx → shared/components/feedback/
```

## 🔄 Migration Process

### Phase 1: Set Up Domain Structure

1. **Create domain directories:**

```bash
mkdir -p domains/{domain-name}/{components,hooks,services,types,utils}
```

2. **Create index files:**

```typescript
// domains/{domain-name}/index.ts
export * from "./components";
export * from "./hooks";
export * from "./services";
export * from "./types";
export * from "./utils";
```

### Phase 2: Migrate Types

Start with types as they're the foundation:

1. **Create entity types:**

```typescript
// domains/candidates/types/entities.ts
export interface Candidate extends BaseEntity {
  firstName: string;
  lastName: string;
  email: string;
  // ... other fields
}
```

2. **Create API types:**

```typescript
// domains/candidates/types/api.ts
export interface CreateCandidateRequest {
  firstName: string;
  lastName: string;
  email: string;
}

export interface CandidateResponse {
  data: Candidate;
  success: boolean;
}
```

3. **Export from domain:**

```typescript
// domains/candidates/types/index.ts
export * from "./entities";
export * from "./api";
export * from "./forms";
```

### Phase 3: Migrate Services

1. **Create service class:**

```typescript
// domains/candidates/services/candidateService.ts
import { BaseService } from "@/core/api/BaseService";
import { Candidate, CreateCandidateRequest } from "../types";

export class CandidateService extends BaseService {
  async getCandidates(
    filters?: CandidateFilters,
  ): Promise<ApiResponse<Candidate[]>> {
    return this.get("/candidates", { params: filters });
  }

  async createCandidate(
    data: CreateCandidateRequest,
  ): Promise<ApiResponse<Candidate>> {
    return this.post("/candidates", data);
  }
}

export const candidateService = new CandidateService();
```

2. **Migrate existing API calls:**

```typescript
// Before (in component)
const fetchCandidates = async () => {
  const response = await fetch("/api/candidates");
  const data = await response.json();
  setCandidates(data);
};

// After (in service)
const candidates = await candidateService.getCandidates();
```

### Phase 4: Create Hooks

1. **Create query hooks:**

```typescript
// domains/candidates/hooks/queries/useCandidates.ts
import { useQuery } from "@tanstack/react-query";
import { candidateService } from "../../services";

export const useCandidates = (filters?: CandidateFilters) => {
  return useQuery({
    queryKey: ["candidates", filters],
    queryFn: () => candidateService.getCandidates(filters),
  });
};
```

2. **Create mutation hooks:**

```typescript
// domains/candidates/hooks/mutations/useCreateCandidate.ts
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { candidateService } from "../../services";

export const useCreateCandidate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: candidateService.createCandidate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
    },
  });
};
```

### Phase 5: Migrate Components

1. **Move component to domain:**

```bash
mv src/components/CandidateCard.tsx domains/candidates/components/
```

2. **Update component to use domain hooks:**

```typescript
// Before
import { useState, useEffect } from 'react';

const CandidateCard = ({ candidateId }) => {
  const [candidate, setCandidate] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCandidate(candidateId).then(setCandidate).finally(() => setLoading(false));
  }, [candidateId]);

  if (loading) return <div>Loading...</div>;

  return <div>{candidate.name}</div>;
};

// After
import { useCandidateById } from '../hooks';

const CandidateCard = ({ candidateId }) => {
  const { data: candidate, isLoading } = useCandidateById(candidateId);

  if (isLoading) return <div>Loading...</div>;

  return <div>{candidate?.name}</div>;
};
```

3. **Update component props and types:**

```typescript
// Before
interface Props {
  candidate: any;
  onEdit: (candidate: any) => void;
}

// After
import { Candidate } from "../types";

interface CandidateCardProps {
  candidate: Candidate;
  onEdit: (candidate: Candidate) => void;
  onDelete?: (candidateId: string) => void;
  showActions?: boolean;
}
```

### Phase 6: Update Imports

1. **Update component imports:**

```typescript
// Before
import { CandidateCard } from "../components/CandidateCard";

// After
import { CandidateCard } from "@/domains/candidates/components";
```

2. **Update hook imports:**

```typescript
// Before
import { useCandidates } from "../hooks/useCandidates";

// After
import { useCandidates } from "@/domains/candidates/hooks";
```

### Phase 7: Migrate Pages

1. **Update page to use domain components:**

```typescript
// Before
import CandidateList from "../components/CandidateList";
import CandidateForm from "../components/CandidateForm";

// After
import {
  CandidateTable,
  CandidateFormModal,
} from "@/domains/candidates/components";
import { useCandidates, useCreateCandidate } from "@/domains/candidates/hooks";
```

2. **Refactor page logic:**

```typescript
// Before - mixed concerns
const CandidatesPage = () => {
  const [candidates, setCandidates] = useState([]);
  const [loading, setLoading] = useState(true);

  const fetchCandidates = async () => {
    const response = await fetch('/api/candidates');
    const data = await response.json();
    setCandidates(data);
    setLoading(false);
  };

  useEffect(() => {
    fetchCandidates();
  }, []);

  return (
    <div>
      {loading ? <div>Loading...</div> : <CandidateList candidates={candidates} />}
    </div>
  );
};

// After - clean separation
const CandidatesPage = () => {
  const { data: candidates, isLoading } = useCandidates();
  const createCandidate = useCreateCandidate();

  const handleCreateCandidate = (data) => {
    createCandidate.mutate(data);
  };

  return (
    <Layout>
      <CandidateTable
        candidates={candidates}
        loading={isLoading}
        onCreateCandidate={handleCreateCandidate}
      />
    </Layout>
  );
};
```

## 🧪 Testing Migration

### 1. Create Tests for Migrated Components

```typescript
// domains/candidates/__tests__/components/CandidateCard.test.tsx
import { render, screen } from '@testing-library/react';
import { CandidateCard } from '../../components/CandidateCard';
import { mockCandidate } from '../__mocks__/candidate.mock';

describe('CandidateCard', () => {
  it('renders candidate information', () => {
    render(<CandidateCard candidate={mockCandidate} />);
    expect(screen.getByText(mockCandidate.name)).toBeInTheDocument();
  });
});
```

### 2. Test Hook Migration

```typescript
// domains/candidates/__tests__/hooks/useCandidates.test.ts
import { renderHook } from "@testing-library/react";
import { useCandidates } from "../../hooks/useCandidates";

describe("useCandidates", () => {
  it("fetches candidates successfully", async () => {
    const { result } = renderHook(() => useCandidates());
    // Test implementation
  });
});
```

## 🔍 Migration Checklist

### Component Migration

- [ ] Component moved to appropriate domain
- [ ] Props interface defined with proper types
- [ ] Uses domain hooks instead of direct API calls
- [ ] Follows domain naming conventions
- [ ] Exported from domain index
- [ ] Tests created and passing

### Hook Migration

- [ ] Query hooks use React Query
- [ ] Mutation hooks invalidate cache appropriately
- [ ] Error handling implemented
- [ ] Loading states managed
- [ ] Proper TypeScript types
- [ ] Tests created and passing

### Service Migration

- [ ] Extends BaseService
- [ ] Methods follow REST conventions
- [ ] Proper error handling
- [ ] TypeScript interfaces for requests/responses
- [ ] Tests created and passing

### Page Migration

- [ ] Uses domain components
- [ ] Uses domain hooks
- [ ] Clean separation of concerns
- [ ] Proper error boundaries
- [ ] Loading states handled
- [ ] Integration tests passing

## 🚨 Common Migration Issues

### 1. Circular Dependencies

**Problem**: Components importing from each other
**Solution**: Extract shared logic to utilities or parent components

### 2. Type Conflicts

**Problem**: Conflicting type definitions
**Solution**: Use consistent naming and proper namespacing

### 3. State Management

**Problem**: Complex state logic in components
**Solution**: Move to custom hooks or state management libraries

### 4. API Integration

**Problem**: Inconsistent API patterns
**Solution**: Standardize on service layer with adapters

## 📈 Migration Progress Tracking

Create a migration tracking sheet:

```
Component | Domain | Status | Assignee | Completion Date
----------|--------|--------|----------|----------------
CandidateCard | candidates | ✅ Complete | John | 2024-01-15
JobForm | jobs | 🔄 In Progress | Jane | -
InterviewModal | interviews | ⏳ Pending | - | -
```

## 🎉 Post-Migration Tasks

1. **Update Documentation**
   - Update component documentation
   - Update API documentation
   - Update development guides

2. **Performance Review**
   - Check bundle size impact
   - Review loading performance
   - Optimize if necessary

3. **Team Training**
   - Conduct architecture overview
   - Review new patterns and conventions
   - Update development workflows

4. **Cleanup**
   - Remove old unused files
   - Update import paths
   - Clean up package dependencies

## 🔄 Rollback Plan

If migration issues arise:

1. **Immediate Rollback**
   - Revert to previous commit
   - Restore old import paths
   - Re-enable old components

2. **Partial Rollback**
   - Keep migrated domains that work
   - Rollback problematic components
   - Fix issues incrementally

3. **Data Integrity**
   - Ensure no data loss
   - Verify API compatibility
   - Check user workflows

## 📚 Additional Resources

- [Domain-Driven Design Principles](https://martinfowler.com/bliki/DomainDrivenDesign.html)
- [React Query Migration Guide](https://tanstack.com/query/latest/docs/react/guides/migrating-to-react-query-4)
- [TypeScript Migration Guide](https://www.typescriptlang.org/docs/handbook/migrating-from-javascript.html)
- [Testing Library Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)

## 🎯 Success Metrics

Track migration success with these metrics:

### Code Quality

- **Type Coverage**: >95% TypeScript coverage
- **Test Coverage**: >80% code coverage
- **Bundle Size**: No significant increase
- **Performance**: No regression in load times

### Developer Experience

- **Build Time**: Faster or equivalent build times
- **Development Speed**: Reduced time to implement features
- **Bug Rate**: Decreased bug reports
- **Code Reusability**: Increased component reuse

### Maintainability

- **Code Duplication**: Reduced duplicate code
- **Dependency Management**: Cleaner dependency graph
- **Documentation**: Comprehensive domain documentation
- **Onboarding Time**: Faster new developer onboarding
