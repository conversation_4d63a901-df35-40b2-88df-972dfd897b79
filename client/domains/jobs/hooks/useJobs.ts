/**
 * useJobs Hook
 * Main hook for job data management and operations
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Job, 
  JobListItem, 
  JobSearchFilters, 
  CreateJobData, 
  UpdateJobData,
  JobStatistics,
  BulkJobOperation,
  JobUtils,
  JobTypeGuards
} from '../types';
import { JobService } from '../services';
import { toast } from 'sonner';

// Query keys for React Query
export const jobQueryKeys = {
  all: ['jobs'] as const,
  lists: () => [...jobQueryKeys.all, 'list'] as const,
  list: (filters?: JobSearchFilters) => [...jobQueryKeys.lists(), filters] as const,
  details: () => [...jobQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...jobQueryKeys.details(), id] as const,
  statistics: () => [...jobQueryKeys.all, 'statistics'] as const,
};

export interface UseJobsOptions {
  filters?: JobSearchFilters;
  enabled?: boolean;
  refetchInterval?: number;
}

export interface UseJobsResult {
  // Data
  jobs: JobListItem[];
  totalCount: number;
  statistics: JobStatistics | null;
  
  // Loading states
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  
  // Actions
  createJob: (data: CreateJobData) => Promise<Job>;
  updateJob: (id: string, data: UpdateJobData) => Promise<Job>;
  deleteJob: (id: string) => Promise<void>;
  bulkAction: (operation: BulkJobOperation) => Promise<void>;
  
  // Utilities
  refetch: () => void;
  invalidate: () => void;
  setFilters: (filters: JobSearchFilters) => void;
  clearFilters: () => void;
}

/**
 * Main jobs hook for data management
 */
export const useJobs = (options: UseJobsOptions = {}): UseJobsResult => {
  const { filters, enabled = true, refetchInterval } = options;
  const queryClient = useQueryClient();
  const [currentFilters, setCurrentFilters] = useState<JobSearchFilters>(filters || {});

  // Jobs list query
  const {
    data: jobsData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: jobQueryKeys.list(currentFilters),
    queryFn: () => JobService.searchJobs(currentFilters),
    enabled,
    refetchInterval,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Statistics query
  const { data: statistics } = useQuery({
    queryKey: jobQueryKeys.statistics(),
    queryFn: () => JobService.getStatistics(),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Create job mutation
  const createJobMutation = useMutation({
    mutationFn: (data: CreateJobData) => JobService.createJob(data),
    onSuccess: (newJob) => {
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.statistics() });
      toast.success('Job created successfully');
    },
    onError: (error) => {
      console.error('Failed to create job:', error);
      toast.error('Failed to create job');
    },
  });

  // Update job mutation
  const updateJobMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateJobData }) => 
      JobService.updateJob(id, data),
    onSuccess: (updatedJob) => {
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.detail(updatedJob.id) });
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.statistics() });
      toast.success('Job updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update job:', error);
      toast.error('Failed to update job');
    },
  });

  // Delete job mutation
  const deleteJobMutation = useMutation({
    mutationFn: (id: string) => JobService.deleteJob(id),
    onSuccess: (_, deletedId) => {
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.lists() });
      queryClient.removeQueries({ queryKey: jobQueryKeys.detail(deletedId) });
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.statistics() });
      toast.success('Job deleted successfully');
    },
    onError: (error) => {
      console.error('Failed to delete job:', error);
      toast.error('Failed to delete job');
    },
  });

  // Bulk action mutation
  const bulkActionMutation = useMutation({
    mutationFn: (operation: BulkJobOperation) => JobService.bulkAction(operation),
    onSuccess: (_, operation) => {
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.statistics() });
      toast.success(`Bulk ${operation.operation} completed successfully`);
    },
    onError: (error) => {
      console.error('Failed to perform bulk action:', error);
      toast.error('Failed to perform bulk action');
    },
  });

  // Action functions
  const createJob = useCallback(async (data: CreateJobData): Promise<Job> => {
    return createJobMutation.mutateAsync(data);
  }, [createJobMutation]);

  const updateJob = useCallback(async (id: string, data: UpdateJobData): Promise<Job> => {
    return updateJobMutation.mutateAsync({ id, data });
  }, [updateJobMutation]);

  const deleteJob = useCallback(async (id: string): Promise<void> => {
    return deleteJobMutation.mutateAsync(id);
  }, [deleteJobMutation]);

  const bulkAction = useCallback(async (operation: BulkJobOperation): Promise<void> => {
    return bulkActionMutation.mutateAsync(operation);
  }, [bulkActionMutation]);

  // Utility functions
  const invalidate = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: jobQueryKeys.all });
  }, [queryClient]);

  const setFilters = useCallback((newFilters: JobSearchFilters) => {
    setCurrentFilters(newFilters);
  }, []);

  const clearFilters = useCallback(() => {
    setCurrentFilters({});
  }, []);

  // Memoized results
  const jobs = useMemo(() => {
    if (!jobsData?.jobs) return [];
    return jobsData.jobs.map(job => JobUtils.toListItem(job));
  }, [jobsData?.jobs]);

  const totalCount = useMemo(() => {
    return jobsData?.totalCount || 0;
  }, [jobsData?.totalCount]);

  return {
    // Data
    jobs,
    totalCount,
    statistics: statistics || null,
    
    // Loading states
    isLoading: isLoading || createJobMutation.isPending || updateJobMutation.isPending || deleteJobMutation.isPending,
    isError,
    error: error as Error | null,
    
    // Actions
    createJob,
    updateJob,
    deleteJob,
    bulkAction,
    
    // Utilities
    refetch,
    invalidate,
    setFilters,
    clearFilters,
  };
};

/**
 * Hook for single job details
 */
export interface UseJobOptions {
  enabled?: boolean;
}

export interface UseJobResult {
  job: Job | null;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export const useJob = (id: string, options: UseJobOptions = {}): UseJobResult => {
  const { enabled = true } = options;

  const {
    data: job,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: jobQueryKeys.detail(id),
    queryFn: () => JobService.getJob(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    job: job || null,
    isLoading,
    isError,
    error: error as Error | null,
    refetch,
  };
};

/**
 * Hook for job statistics
 */
export interface UseJobStatisticsResult {
  statistics: JobStatistics | null;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export const useJobStatistics = (): UseJobStatisticsResult => {
  const {
    data: statistics,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: jobQueryKeys.statistics(),
    queryFn: () => JobService.getStatistics(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    statistics: statistics || null,
    isLoading,
    isError,
    error: error as Error | null,
    refetch,
  };
};
