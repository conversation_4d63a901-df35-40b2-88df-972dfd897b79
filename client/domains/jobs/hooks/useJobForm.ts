/**
 * useJobForm Hook
 * Form management hook for job creation and editing
 */

import { useState, useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Job, 
  CreateJobData, 
  UpdateJobData,
  JobStatus,
  JobPriority,
  EmploymentType,
  ExperienceLevel,
  RemoteType,
  JobUtils,
  JobTypeGuards
} from '../types';
import { toast } from 'sonner';

// Form validation schema
const jobFormSchema = z.object({
  // Basic Information
  title: z.string().min(2, 'Job title must be at least 2 characters'),
  description: z.string().min(10, 'Job description must be at least 10 characters'),
  department: z.string().min(2, 'Department is required'),
  location: z.string().min(2, 'Location is required'),
  
  // Employment Details
  remoteType: z.enum(['on-site', 'remote', 'hybrid']),
  employmentType: z.enum(['full-time', 'part-time', 'contract', 'internship', 'freelance']),
  experienceLevel: z.enum(['entry', 'junior', 'mid', 'senior', 'lead', 'executive']),
  
  // Requirements and Responsibilities
  requirements: z.string().min(10, 'Job requirements must be at least 10 characters'),
  responsibilities: z.string().optional(),
  qualifications: z.string().optional(),
  benefits: z.string().optional(),
  
  // Compensation
  salaryMin: z.number().min(0, 'Minimum salary must be positive').optional(),
  salaryMax: z.number().min(0, 'Maximum salary must be positive').optional(),
  currency: z.string().optional(),
  salaryType: z.enum(['hourly', 'monthly', 'yearly']).optional(),
  
  // Skills and Tags
  requiredSkills: z.array(z.string()).min(1, 'At least one required skill must be specified'),
  preferredSkills: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  
  // Status and Priority
  status: z.enum(['draft', 'published', 'closed', 'archived']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  
  // Hiring Information
  hiringManagerId: z.string().optional(),
  recruiterId: z.string().optional(),
  
  // Application Settings
  applicationDeadline: z.string().optional(),
  maxApplications: z.number().min(1, 'Maximum applications must be at least 1').optional(),
}).refine((data) => {
  // Custom validation: salary range
  if (data.salaryMin && data.salaryMax && data.salaryMin > data.salaryMax) {
    return false;
  }
  return true;
}, {
  message: "Minimum salary cannot be greater than maximum salary",
  path: ["salaryMax"],
});

export type JobFormData = z.infer<typeof jobFormSchema>;

export interface UseJobFormOptions {
  job?: Job | null;
  onSubmit?: (data: CreateJobData | UpdateJobData) => Promise<void>;
  onSuccess?: (job: Job) => void;
  onError?: (error: Error) => void;
}

export interface UseJobFormResult {
  // Form state
  form: ReturnType<typeof useForm<JobFormData>>;
  isSubmitting: boolean;
  isDirty: boolean;
  isValid: boolean;
  
  // Form data
  formData: JobFormData;
  
  // Actions
  handleSubmit: (data: JobFormData) => Promise<void>;
  reset: (job?: Job) => void;
  setFieldValue: <K extends keyof JobFormData>(field: K, value: JobFormData[K]) => void;
  
  // Validation
  validateField: (field: keyof JobFormData) => Promise<boolean>;
  validateForm: () => Promise<boolean>;
  
  // Utilities
  getFieldError: (field: keyof JobFormData) => string | undefined;
  hasFieldError: (field: keyof JobFormData) => boolean;
  canPublish: boolean;
  previewData: Partial<Job>;
}

/**
 * Job form management hook
 */
export const useJobForm = (options: UseJobFormOptions = {}): UseJobFormResult => {
  const { job, onSubmit, onSuccess, onError } = options;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!job;

  // Default form values
  const defaultValues: Partial<JobFormData> = useMemo(() => {
    if (job) {
      return {
        title: job.title,
        description: job.description,
        department: job.department,
        location: job.location,
        remoteType: job.remoteType,
        employmentType: job.employmentType,
        experienceLevel: job.experienceLevel,
        requirements: job.requirements,
        responsibilities: job.responsibilities || '',
        qualifications: job.qualifications || '',
        benefits: job.benefits || '',
        salaryMin: job.salaryMin || undefined,
        salaryMax: job.salaryMax || undefined,
        currency: job.currency || 'USD',
        salaryType: job.salaryType || 'yearly',
        requiredSkills: job.requiredSkills || [],
        preferredSkills: job.preferredSkills || [],
        tags: job.tags || [],
        status: job.status || 'draft',
        priority: job.priority || 'medium',
        hiringManagerId: job.hiringManagerId || '',
        recruiterId: job.recruiterId || '',
        applicationDeadline: job.applicationDeadline || '',
        maxApplications: job.maxApplications || undefined,
      };
    }
    return {
      status: 'draft',
      priority: 'medium',
      currency: 'USD',
      salaryType: 'yearly',
      remoteType: 'on-site',
      employmentType: 'full-time',
      experienceLevel: 'mid',
      requiredSkills: [],
      preferredSkills: [],
      tags: [],
    };
  }, [job]);

  // Initialize form
  const form = useForm<JobFormData>({
    resolver: zodResolver(jobFormSchema),
    defaultValues,
    mode: 'onChange',
  });

  const { 
    handleSubmit: formHandleSubmit, 
    reset: formReset, 
    setValue, 
    trigger, 
    formState: { isDirty, isValid, errors },
    watch 
  } = form;

  // Watch form data for preview
  const formData = watch();

  // Handle form submission
  const handleSubmit = useCallback(async (data: JobFormData) => {
    if (!onSubmit) return;

    setIsSubmitting(true);
    try {
      // Validate job data using type guards
      const validation = JobTypeGuards.validateJob({ ...data, id: job?.id || 'temp' } as Job);
      if (!validation.isValid) {
        toast.error(`Validation failed: ${validation.errors.join(', ')}`);
        return;
      }

      // Prepare data for submission
      const submitData = {
        ...data,
        // Convert empty strings to undefined for optional fields
        responsibilities: data.responsibilities || undefined,
        qualifications: data.qualifications || undefined,
        benefits: data.benefits || undefined,
        applicationDeadline: data.applicationDeadline || undefined,
        hiringManagerId: data.hiringManagerId || undefined,
        recruiterId: data.recruiterId || undefined,
        // Ensure arrays are not empty
        preferredSkills: data.preferredSkills?.length ? data.preferredSkills : undefined,
        tags: data.tags?.length ? data.tags : undefined,
      };

      await onSubmit(submitData);
      
      if (onSuccess) {
        onSuccess({ ...submitData, id: job?.id || 'new' } as Job);
      }
      
      toast.success(
        isEditing 
          ? 'Job updated successfully' 
          : 'Job created successfully'
      );
    } catch (error) {
      console.error('Error submitting job:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      if (onError) {
        onError(error as Error);
      }
      
      toast.error(
        isEditing 
          ? `Failed to update job: ${errorMessage}` 
          : `Failed to create job: ${errorMessage}`
      );
    } finally {
      setIsSubmitting(false);
    }
  }, [onSubmit, onSuccess, onError, job?.id, isEditing]);

  // Reset form
  const reset = useCallback((resetJob?: Job) => {
    const resetData = resetJob || job;
    if (resetData) {
      const resetValues = {
        title: resetData.title,
        description: resetData.description,
        department: resetData.department,
        location: resetData.location,
        remoteType: resetData.remoteType,
        employmentType: resetData.employmentType,
        experienceLevel: resetData.experienceLevel,
        requirements: resetData.requirements,
        responsibilities: resetData.responsibilities || '',
        qualifications: resetData.qualifications || '',
        benefits: resetData.benefits || '',
        salaryMin: resetData.salaryMin || undefined,
        salaryMax: resetData.salaryMax || undefined,
        currency: resetData.currency || 'USD',
        salaryType: resetData.salaryType || 'yearly',
        requiredSkills: resetData.requiredSkills || [],
        preferredSkills: resetData.preferredSkills || [],
        tags: resetData.tags || [],
        status: resetData.status || 'draft',
        priority: resetData.priority || 'medium',
        hiringManagerId: resetData.hiringManagerId || '',
        recruiterId: resetData.recruiterId || '',
        applicationDeadline: resetData.applicationDeadline || '',
        maxApplications: resetData.maxApplications || undefined,
      };
      formReset(resetValues);
    } else {
      formReset(defaultValues);
    }
  }, [job, formReset, defaultValues]);

  // Set field value
  const setFieldValue = useCallback(<K extends keyof JobFormData>(
    field: K, 
    value: JobFormData[K]
  ) => {
    setValue(field, value, { shouldDirty: true, shouldValidate: true });
  }, [setValue]);

  // Validate field
  const validateField = useCallback(async (field: keyof JobFormData): Promise<boolean> => {
    return await trigger(field);
  }, [trigger]);

  // Validate entire form
  const validateForm = useCallback(async (): Promise<boolean> => {
    return await trigger();
  }, [trigger]);

  // Get field error
  const getFieldError = useCallback((field: keyof JobFormData): string | undefined => {
    return errors[field]?.message;
  }, [errors]);

  // Check if field has error
  const hasFieldError = useCallback((field: keyof JobFormData): boolean => {
    return !!errors[field];
  }, [errors]);

  // Check if job can be published
  const canPublish = useMemo(() => {
    if (!formData) return false;
    return JobTypeGuards.hasRequiredFieldsForPublishing(formData as Partial<Job>);
  }, [formData]);

  // Preview data for job preview
  const previewData = useMemo((): Partial<Job> => {
    return {
      ...formData,
      id: job?.id || 'preview',
      createdAt: job?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      applicationCount: job?.applicationCount || 0,
      viewCount: job?.viewCount || 0,
      slug: JobUtils.generateSlug(formData.title || ''),
      isActive: JobUtils.isJobActive({ status: formData.status || 'draft' } as Job),
      formattedSalary: JobUtils.formatSalary(
        formData.salaryMin,
        formData.salaryMax,
        formData.currency,
        formData.salaryType
      ),
    };
  }, [formData, job]);

  return {
    // Form state
    form,
    isSubmitting,
    isDirty,
    isValid,
    
    // Form data
    formData,
    
    // Actions
    handleSubmit: formHandleSubmit(handleSubmit),
    reset,
    setFieldValue,
    
    // Validation
    validateField,
    validateForm,
    
    // Utilities
    getFieldError,
    hasFieldError,
    canPublish,
    previewData,
  };
};
