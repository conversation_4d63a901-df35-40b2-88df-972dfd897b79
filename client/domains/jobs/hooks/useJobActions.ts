/**
 * useJobActions Hook
 * Common job actions and operations
 */

import { useState, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Job, 
  JobStatus,
  BulkJobOperation,
  JobExportOptions,
  JobImportResult,
  JobUtils,
  JobTypeGuards
} from '../types';
import { JobService } from '../services';
import { jobQueryKeys } from './useJobs';
import { toast } from 'sonner';

export interface UseJobActionsOptions {
  onSuccess?: (action: string, result?: any) => void;
  onError?: (action: string, error: Error) => void;
}

export interface UseJobActionsResult {
  // Status actions
  publishJob: (id: string) => Promise<void>;
  unpublishJob: (id: string) => Promise<void>;
  closeJob: (id: string) => Promise<void>;
  archiveJob: (id: string) => Promise<void>;
  duplicateJob: (id: string) => Promise<Job>;
  
  // Bulk actions
  bulkUpdateStatus: (jobIds: string[], status: JobStatus) => Promise<void>;
  bulkAddTags: (jobIds: string[], tags: string[]) => Promise<void>;
  bulkRemoveTags: (jobIds: string[], tags: string[]) => Promise<void>;
  bulkDelete: (jobIds: string[]) => Promise<void>;
  bulkExport: (jobIds: string[], options?: JobExportOptions) => Promise<void>;
  
  // Import/Export actions
  exportJobs: (options: JobExportOptions) => Promise<void>;
  importJobs: (file: File) => Promise<JobImportResult>;
  
  // Sharing actions
  shareJob: (job: Job) => Promise<void>;
  copyJobLink: (job: Job) => Promise<void>;
  
  // Loading states
  isLoading: boolean;
  loadingActions: Set<string>;
}

/**
 * Job actions hook
 */
export const useJobActions = (options: UseJobActionsOptions = {}): UseJobActionsResult => {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();
  const [loadingActions, setLoadingActions] = useState<Set<string>>(new Set());

  // Helper to track loading state
  const withLoading = useCallback(async <T>(
    action: string,
    fn: () => Promise<T>
  ): Promise<T> => {
    setLoadingActions(prev => new Set(prev).add(action));
    try {
      const result = await fn();
      if (onSuccess) {
        onSuccess(action, result);
      }
      return result;
    } catch (error) {
      if (onError) {
        onError(action, error as Error);
      }
      throw error;
    } finally {
      setLoadingActions(prev => {
        const next = new Set(prev);
        next.delete(action);
        return next;
      });
    }
  }, [onSuccess, onError]);

  // Status change mutation
  const statusChangeMutation = useMutation({
    mutationFn: ({ id, status }: { id: string; status: JobStatus }) => 
      JobService.updateJob(id, { status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.statistics() });
    },
  });

  // Duplicate job mutation
  const duplicateJobMutation = useMutation({
    mutationFn: (id: string) => JobService.duplicateJob(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.statistics() });
    },
  });

  // Bulk action mutation
  const bulkActionMutation = useMutation({
    mutationFn: (operation: BulkJobOperation) => JobService.bulkAction(operation),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.statistics() });
    },
  });

  // Export mutation
  const exportMutation = useMutation({
    mutationFn: (options: JobExportOptions) => JobService.exportJobs(options),
  });

  // Import mutation
  const importMutation = useMutation({
    mutationFn: (file: File) => JobService.importJobs(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: jobQueryKeys.statistics() });
    },
  });

  // Status actions
  const publishJob = useCallback(async (id: string): Promise<void> => {
    return withLoading('publish', async () => {
      await statusChangeMutation.mutateAsync({ id, status: 'published' });
      toast.success('Job published successfully');
    });
  }, [statusChangeMutation, withLoading]);

  const unpublishJob = useCallback(async (id: string): Promise<void> => {
    return withLoading('unpublish', async () => {
      await statusChangeMutation.mutateAsync({ id, status: 'draft' });
      toast.success('Job unpublished successfully');
    });
  }, [statusChangeMutation, withLoading]);

  const closeJob = useCallback(async (id: string): Promise<void> => {
    return withLoading('close', async () => {
      await statusChangeMutation.mutateAsync({ id, status: 'closed' });
      toast.success('Job closed successfully');
    });
  }, [statusChangeMutation, withLoading]);

  const archiveJob = useCallback(async (id: string): Promise<void> => {
    return withLoading('archive', async () => {
      await statusChangeMutation.mutateAsync({ id, status: 'archived' });
      toast.success('Job archived successfully');
    });
  }, [statusChangeMutation, withLoading]);

  const duplicateJob = useCallback(async (id: string): Promise<Job> => {
    return withLoading('duplicate', async () => {
      const duplicatedJob = await duplicateJobMutation.mutateAsync(id);
      toast.success('Job duplicated successfully');
      return duplicatedJob;
    });
  }, [duplicateJobMutation, withLoading]);

  // Bulk actions
  const bulkUpdateStatus = useCallback(async (jobIds: string[], status: JobStatus): Promise<void> => {
    return withLoading('bulk-status', async () => {
      await bulkActionMutation.mutateAsync({
        jobIds,
        operation: 'update_status',
        data: { status },
      });
      toast.success(`${jobIds.length} jobs updated successfully`);
    });
  }, [bulkActionMutation, withLoading]);

  const bulkAddTags = useCallback(async (jobIds: string[], tags: string[]): Promise<void> => {
    return withLoading('bulk-add-tags', async () => {
      await bulkActionMutation.mutateAsync({
        jobIds,
        operation: 'add_tags',
        data: { tags },
      });
      toast.success(`Tags added to ${jobIds.length} jobs`);
    });
  }, [bulkActionMutation, withLoading]);

  const bulkRemoveTags = useCallback(async (jobIds: string[], tags: string[]): Promise<void> => {
    return withLoading('bulk-remove-tags', async () => {
      await bulkActionMutation.mutateAsync({
        jobIds,
        operation: 'remove_tags',
        data: { tags },
      });
      toast.success(`Tags removed from ${jobIds.length} jobs`);
    });
  }, [bulkActionMutation, withLoading]);

  const bulkDelete = useCallback(async (jobIds: string[]): Promise<void> => {
    return withLoading('bulk-delete', async () => {
      await bulkActionMutation.mutateAsync({
        jobIds,
        operation: 'delete',
      });
      toast.success(`${jobIds.length} jobs deleted successfully`);
    });
  }, [bulkActionMutation, withLoading]);

  const bulkExport = useCallback(async (jobIds: string[], options?: JobExportOptions): Promise<void> => {
    return withLoading('bulk-export', async () => {
      const exportOptions: JobExportOptions = {
        format: 'csv',
        fields: ['title', 'department', 'location', 'status', 'publishedAt'],
        ...options,
        filters: {
          ...options?.filters,
          // Add job IDs to filters if not already present
        },
      };
      
      await bulkActionMutation.mutateAsync({
        jobIds,
        operation: 'export',
        data: exportOptions,
      });
      toast.success(`${jobIds.length} jobs exported successfully`);
    });
  }, [bulkActionMutation, withLoading]);

  // Import/Export actions
  const exportJobs = useCallback(async (options: JobExportOptions): Promise<void> => {
    return withLoading('export', async () => {
      await exportMutation.mutateAsync(options);
      toast.success('Jobs exported successfully');
    });
  }, [exportMutation, withLoading]);

  const importJobs = useCallback(async (file: File): Promise<JobImportResult> => {
    return withLoading('import', async () => {
      const result = await importMutation.mutateAsync(file);
      toast.success(`Imported ${result.imported} jobs successfully`);
      if (result.failed > 0) {
        toast.warning(`${result.failed} jobs failed to import`);
      }
      return result;
    });
  }, [importMutation, withLoading]);

  // Sharing actions
  const shareJob = useCallback(async (job: Job): Promise<void> => {
    return withLoading('share', async () => {
      const url = JobUtils.getJobUrl(job, window.location.origin);
      
      if (navigator.share) {
        await navigator.share({
          title: job.title,
          text: `Check out this job opportunity: ${job.title} at ${job.department}`,
          url,
        });
      } else {
        // Fallback to copying to clipboard
        await navigator.clipboard.writeText(url);
        toast.success('Job link copied to clipboard');
      }
    });
  }, [withLoading]);

  const copyJobLink = useCallback(async (job: Job): Promise<void> => {
    return withLoading('copy-link', async () => {
      const url = JobUtils.getJobUrl(job, window.location.origin);
      await navigator.clipboard.writeText(url);
      toast.success('Job link copied to clipboard');
    });
  }, [withLoading]);

  const isLoading = loadingActions.size > 0;

  return {
    // Status actions
    publishJob,
    unpublishJob,
    closeJob,
    archiveJob,
    duplicateJob,
    
    // Bulk actions
    bulkUpdateStatus,
    bulkAddTags,
    bulkRemoveTags,
    bulkDelete,
    bulkExport,
    
    // Import/Export actions
    exportJobs,
    importJobs,
    
    // Sharing actions
    shareJob,
    copyJobLink,
    
    // Loading states
    isLoading,
    loadingActions,
  };
};
