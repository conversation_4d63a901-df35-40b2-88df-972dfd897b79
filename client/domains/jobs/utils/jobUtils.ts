/**
 * Job Utility Functions
 * Common utility functions for job operations
 */

import { 
  Job, 
  JobStatus, 
  JobPriority, 
  EmploymentType, 
  ExperienceLevel, 
  RemoteType,
  JobListItem 
} from '../types';

/**
 * Job Utilities Class
 * Contains static utility methods for job operations
 */
export class JobUtils {
  /**
   * Format salary range for display
   */
  static formatSalary(
    salaryMin?: number, 
    salaryMax?: number, 
    currency: string = 'USD',
    salaryType: 'hourly' | 'monthly' | 'yearly' = 'yearly'
  ): string {
    if (!salaryMin && !salaryMax) return 'Salary not specified';

    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    const period = salaryType === 'hourly' ? '/hr' : 
                   salaryType === 'monthly' ? '/mo' : 
                   '/year';

    if (salaryMin && salaryMax) {
      return `${formatter.format(salaryMin)} - ${formatter.format(salaryMax)}${period}`;
    } else if (salaryMin) {
      return `From ${formatter.format(salaryMin)}${period}`;
    } else if (salaryMax) {
      return `Up to ${formatter.format(salaryMax)}${period}`;
    }

    return 'Salary not specified';
  }

  /**
   * Get status color for UI display
   */
  static getStatusColor(status: JobStatus): string {
    const colors = {
      draft: 'bg-gray-100 text-gray-800',
      published: 'bg-green-100 text-green-800',
      closed: 'bg-red-100 text-red-800',
      archived: 'bg-yellow-100 text-yellow-800',
    };
    return colors[status] || colors.draft;
  }

  /**
   * Get priority color for UI display
   */
  static getPriorityColor(priority: JobPriority): string {
    const colors = {
      low: 'bg-blue-100 text-blue-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800',
    };
    return colors[priority] || colors.medium;
  }

  /**
   * Calculate days since job was published
   */
  static getDaysOpen(publishedAt?: string): number {
    if (!publishedAt) return 0;
    const publishDate = new Date(publishedAt);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - publishDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Format employment type for display
   */
  static formatEmploymentType(type: EmploymentType): string {
    const formatted = {
      'full-time': 'Full-time',
      'part-time': 'Part-time',
      'contract': 'Contract',
      'internship': 'Internship',
      'freelance': 'Freelance',
    };
    return formatted[type] || type;
  }

  /**
   * Format experience level for display
   */
  static formatExperienceLevel(level: ExperienceLevel): string {
    const formatted = {
      entry: 'Entry Level',
      junior: 'Junior',
      mid: 'Mid Level',
      senior: 'Senior',
      lead: 'Lead',
      executive: 'Executive',
    };
    return formatted[level] || level;
  }

  /**
   * Format remote type for display
   */
  static formatRemoteType(type: RemoteType): string {
    const formatted = {
      'on-site': 'On-site',
      'remote': 'Remote',
      'hybrid': 'Hybrid',
    };
    return formatted[type] || type;
  }

  /**
   * Generate job slug from title
   */
  static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  /**
   * Check if job is active (published and not closed)
   */
  static isJobActive(job: Job | JobListItem): boolean {
    return job.status === 'published';
  }

  /**
   * Check if job can be edited
   */
  static canEditJob(job: Job | JobListItem): boolean {
    return ['draft', 'published'].includes(job.status);
  }

  /**
   * Check if job can be deleted
   */
  static canDeleteJob(job: Job | JobListItem): boolean {
    return job.status === 'draft';
  }

  /**
   * Check if job can be published
   */
  static canPublishJob(job: Job): boolean {
    return job.status === 'draft' && 
           !!job.title?.trim() && 
           !!job.description?.trim() && 
           !!job.requirements?.trim();
  }

  /**
   * Calculate application rate
   */
  static calculateApplicationRate(applications: number, views: number): number {
    if (views === 0) return 0;
    return Math.round((applications / views) * 100 * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Get job urgency level based on various factors
   */
  static getJobUrgency(job: Job): 'low' | 'medium' | 'high' | 'critical' {
    const daysOpen = this.getDaysOpen(job.publishedAt);
    const applicationRate = job.applicationRate || 0;
    const priority = job.priority || 'medium';

    // Critical if urgent priority and open for more than 30 days with low applications
    if (priority === 'urgent' && daysOpen > 30 && applicationRate < 5) {
      return 'critical';
    }

    // High if high/urgent priority or open for more than 45 days
    if (priority === 'urgent' || priority === 'high' || daysOpen > 45) {
      return 'high';
    }

    // Medium if medium priority or open for more than 21 days
    if (priority === 'medium' || daysOpen > 21) {
      return 'medium';
    }

    return 'low';
  }

  /**
   * Validate job data
   */
  static validateJobData(job: Partial<Job>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!job.title?.trim()) {
      errors.push('Job title is required');
    }

    if (!job.description?.trim()) {
      errors.push('Job description is required');
    }

    if (!job.department?.trim()) {
      errors.push('Department is required');
    }

    if (!job.location?.trim()) {
      errors.push('Location is required');
    }

    if (!job.requirements?.trim()) {
      errors.push('Job requirements are required');
    }

    if (job.salaryMin && job.salaryMax && job.salaryMin > job.salaryMax) {
      errors.push('Minimum salary cannot be greater than maximum salary');
    }

    if (!job.requiredSkills || job.requiredSkills.length === 0) {
      errors.push('At least one required skill must be specified');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Calculate skill match percentage between job and candidate
   */
  static calculateSkillMatch(jobSkills: string[], candidateSkills: string[]): number {
    if (!jobSkills.length) return 0;

    const normalizedJobSkills = jobSkills.map(skill => skill.toLowerCase().trim());
    const normalizedCandidateSkills = candidateSkills.map(skill => skill.toLowerCase().trim());

    const matchedSkills = normalizedJobSkills.filter(skill => 
      normalizedCandidateSkills.includes(skill)
    );

    return Math.round((matchedSkills.length / normalizedJobSkills.length) * 100);
  }

  /**
   * Get job posting URL
   */
  static getJobUrl(job: Job | JobListItem, baseUrl: string = ''): string {
    const slug = job.slug || this.generateSlug(job.title);
    return `${baseUrl}/jobs/${job.id}/${slug}`;
  }

  /**
   * Format job location with remote type
   */
  static formatLocation(location: string, remoteType: RemoteType): string {
    if (remoteType === 'remote') {
      return 'Remote';
    } else if (remoteType === 'hybrid') {
      return `${location} (Hybrid)`;
    }
    return location;
  }

  /**
   * Get recommended job board posting settings
   */
  static getRecommendedJobBoards(job: Job): string[] {
    const boards: string[] = ['company-website'];

    // Add general job boards
    boards.push('linkedin', 'indeed');

    // Add tech-specific boards for tech roles
    const techKeywords = ['developer', 'engineer', 'programmer', 'software', 'tech', 'it'];
    const isTechRole = techKeywords.some(keyword => 
      job.title.toLowerCase().includes(keyword) ||
      job.description.toLowerCase().includes(keyword)
    );

    if (isTechRole) {
      boards.push('stackoverflow', 'github-jobs', 'dice');
    }

    // Add remote-specific boards for remote jobs
    if (job.remoteType === 'remote') {
      boards.push('remote-ok', 'we-work-remotely');
    }

    return boards;
  }

  /**
   * Convert job to list item format
   */
  static toListItem(job: Job): JobListItem {
    return {
      id: job.id,
      title: job.title,
      department: job.department,
      location: job.location,
      remoteType: job.remoteType,
      employmentType: job.employmentType,
      status: job.status,
      applicationCount: job.applicationCount,
      publishedAt: job.publishedAt,
      priority: job.priority,
      hiringManagerName: job.hiringManagerName,
      isActive: this.isJobActive(job),
      daysOpen: this.getDaysOpen(job.publishedAt),
      formattedSalary: this.formatSalary(job.salaryMin, job.salaryMax, job.currency),
    };
  }
}
