/**
 * Job Type Guards
 * Runtime type checking functions for job-related types
 */

import { 
  Job, 
  JobStatus, 
  JobPriority, 
  EmploymentType, 
  ExperienceLevel, 
  RemoteType,
  JobListItem,
  CreateJobData,
  UpdateJobData,
  JobSearchFilters
} from '../types';

/**
 * Job Type Guards Class
 * Contains static type guard methods for runtime type checking
 */
export class JobTypeGuards {
  /**
   * Check if value is a valid JobStatus
   */
  static isJobStatus(value: any): value is JobStatus {
    return typeof value === 'string' && 
           ['draft', 'published', 'closed', 'archived'].includes(value);
  }

  /**
   * Check if value is a valid JobPriority
   */
  static isJobPriority(value: any): value is JobPriority {
    return typeof value === 'string' && 
           ['low', 'medium', 'high', 'urgent'].includes(value);
  }

  /**
   * Check if value is a valid EmploymentType
   */
  static isEmploymentType(value: any): value is EmploymentType {
    return typeof value === 'string' && 
           ['full-time', 'part-time', 'contract', 'internship', 'freelance'].includes(value);
  }

  /**
   * Check if value is a valid ExperienceLevel
   */
  static isExperienceLevel(value: any): value is ExperienceLevel {
    return typeof value === 'string' && 
           ['entry', 'junior', 'mid', 'senior', 'lead', 'executive'].includes(value);
  }

  /**
   * Check if value is a valid RemoteType
   */
  static isRemoteType(value: any): value is RemoteType {
    return typeof value === 'string' && 
           ['on-site', 'remote', 'hybrid'].includes(value);
  }

  /**
   * Check if object is a valid Job
   */
  static isJob(obj: any): obj is Job {
    return obj &&
           typeof obj === 'object' &&
           typeof obj.id === 'string' &&
           typeof obj.title === 'string' &&
           typeof obj.description === 'string' &&
           typeof obj.department === 'string' &&
           typeof obj.location === 'string' &&
           this.isRemoteType(obj.remoteType) &&
           this.isEmploymentType(obj.employmentType) &&
           this.isExperienceLevel(obj.experienceLevel) &&
           typeof obj.requirements === 'string' &&
           Array.isArray(obj.requiredSkills) &&
           this.isJobStatus(obj.status) &&
           typeof obj.currency === 'string';
  }

  /**
   * Check if object is a valid JobListItem
   */
  static isJobListItem(obj: any): obj is JobListItem {
    return obj &&
           typeof obj === 'object' &&
           typeof obj.id === 'string' &&
           typeof obj.title === 'string' &&
           typeof obj.department === 'string' &&
           typeof obj.location === 'string' &&
           this.isRemoteType(obj.remoteType) &&
           this.isEmploymentType(obj.employmentType) &&
           this.isJobStatus(obj.status);
  }

  /**
   * Check if object is valid CreateJobData
   */
  static isCreateJobData(obj: any): obj is CreateJobData {
    return obj &&
           typeof obj === 'object' &&
           typeof obj.title === 'string' &&
           obj.title.trim().length > 0 &&
           typeof obj.description === 'string' &&
           obj.description.trim().length > 0 &&
           typeof obj.department === 'string' &&
           obj.department.trim().length > 0 &&
           typeof obj.location === 'string' &&
           obj.location.trim().length > 0 &&
           this.isRemoteType(obj.remoteType) &&
           this.isEmploymentType(obj.employmentType) &&
           this.isExperienceLevel(obj.experienceLevel) &&
           typeof obj.requirements === 'string' &&
           obj.requirements.trim().length > 0 &&
           Array.isArray(obj.requiredSkills) &&
           obj.requiredSkills.length > 0;
  }

  /**
   * Check if object is valid UpdateJobData
   */
  static isUpdateJobData(obj: any): obj is UpdateJobData {
    if (!obj || typeof obj !== 'object') return false;

    // Check optional fields if they exist
    if (obj.title !== undefined && (typeof obj.title !== 'string' || !obj.title.trim())) {
      return false;
    }
    if (obj.description !== undefined && (typeof obj.description !== 'string' || !obj.description.trim())) {
      return false;
    }
    if (obj.department !== undefined && (typeof obj.department !== 'string' || !obj.department.trim())) {
      return false;
    }
    if (obj.location !== undefined && (typeof obj.location !== 'string' || !obj.location.trim())) {
      return false;
    }
    if (obj.remoteType !== undefined && !this.isRemoteType(obj.remoteType)) {
      return false;
    }
    if (obj.employmentType !== undefined && !this.isEmploymentType(obj.employmentType)) {
      return false;
    }
    if (obj.experienceLevel !== undefined && !this.isExperienceLevel(obj.experienceLevel)) {
      return false;
    }
    if (obj.priority !== undefined && !this.isJobPriority(obj.priority)) {
      return false;
    }
    if (obj.requiredSkills !== undefined && !Array.isArray(obj.requiredSkills)) {
      return false;
    }
    if (obj.preferredSkills !== undefined && !Array.isArray(obj.preferredSkills)) {
      return false;
    }
    if (obj.tags !== undefined && !Array.isArray(obj.tags)) {
      return false;
    }

    return true;
  }

  /**
   * Check if object is valid JobSearchFilters
   */
  static isJobSearchFilters(obj: any): obj is JobSearchFilters {
    if (!obj || typeof obj !== 'object') return false;

    // Check optional fields if they exist
    if (obj.query !== undefined && typeof obj.query !== 'string') {
      return false;
    }
    if (obj.status !== undefined && (!Array.isArray(obj.status) || !obj.status.every(this.isJobStatus))) {
      return false;
    }
    if (obj.department !== undefined && (!Array.isArray(obj.department) || !obj.department.every((d: any) => typeof d === 'string'))) {
      return false;
    }
    if (obj.location !== undefined && (!Array.isArray(obj.location) || !obj.location.every((l: any) => typeof l === 'string'))) {
      return false;
    }
    if (obj.remoteType !== undefined && (!Array.isArray(obj.remoteType) || !obj.remoteType.every(this.isRemoteType))) {
      return false;
    }
    if (obj.employmentType !== undefined && (!Array.isArray(obj.employmentType) || !obj.employmentType.every(this.isEmploymentType))) {
      return false;
    }
    if (obj.experienceLevel !== undefined && (!Array.isArray(obj.experienceLevel) || !obj.experienceLevel.every(this.isExperienceLevel))) {
      return false;
    }
    if (obj.priority !== undefined && (!Array.isArray(obj.priority) || !obj.priority.every(this.isJobPriority))) {
      return false;
    }
    if (obj.requiredSkills !== undefined && (!Array.isArray(obj.requiredSkills) || !obj.requiredSkills.every((s: any) => typeof s === 'string'))) {
      return false;
    }
    if (obj.tags !== undefined && (!Array.isArray(obj.tags) || !obj.tags.every((t: any) => typeof t === 'string'))) {
      return false;
    }

    return true;
  }

  /**
   * Check if value is a valid salary range
   */
  static isValidSalaryRange(min?: number, max?: number): boolean {
    if (min === undefined && max === undefined) return true;
    if (min !== undefined && (typeof min !== 'number' || min < 0)) return false;
    if (max !== undefined && (typeof max !== 'number' || max < 0)) return false;
    if (min !== undefined && max !== undefined && min > max) return false;
    return true;
  }

  /**
   * Check if value is a valid email address
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Check if value is a valid URL
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if date string is valid
   */
  static isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  /**
   * Check if job can transition to new status
   */
  static canTransitionToStatus(currentStatus: JobStatus, newStatus: JobStatus): boolean {
    const validTransitions: Record<JobStatus, JobStatus[]> = {
      draft: ['published', 'archived'],
      published: ['closed', 'archived'],
      closed: ['published', 'archived'],
      archived: ['draft'],
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }

  /**
   * Check if job has required fields for publishing
   */
  static hasRequiredFieldsForPublishing(job: Partial<Job>): boolean {
    return !!(
      job.title?.trim() &&
      job.description?.trim() &&
      job.requirements?.trim() &&
      job.department?.trim() &&
      job.location?.trim() &&
      job.requiredSkills?.length &&
      this.isRemoteType(job.remoteType) &&
      this.isEmploymentType(job.employmentType) &&
      this.isExperienceLevel(job.experienceLevel)
    );
  }

  /**
   * Validate job data and return validation result
   */
  static validateJob(job: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.isJob(job)) {
      errors.push('Invalid job object structure');
      return { isValid: false, errors };
    }

    if (!job.title?.trim()) {
      errors.push('Job title is required');
    }

    if (!job.description?.trim()) {
      errors.push('Job description is required');
    }

    if (!job.requirements?.trim()) {
      errors.push('Job requirements are required');
    }

    if (!job.department?.trim()) {
      errors.push('Department is required');
    }

    if (!job.location?.trim()) {
      errors.push('Location is required');
    }

    if (!job.requiredSkills?.length) {
      errors.push('At least one required skill must be specified');
    }

    if (!this.isValidSalaryRange(job.salaryMin, job.salaryMax)) {
      errors.push('Invalid salary range');
    }

    if (job.applicationDeadline && !this.isValidDate(job.applicationDeadline)) {
      errors.push('Invalid application deadline date');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
