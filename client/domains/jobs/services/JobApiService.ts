/**
 * Job API Service
 * Handles all API interactions for jobs
 */

import { BaseService, ApiResponse, PaginatedResponse, QueryParams } from '@/core/api';
import { ApiClient } from '@/core/api/ApiClient';
import { Job, CreateJobData, UpdateJobData, JobSearchFilters, JobStatistics } from '../types';

export interface JobQueryParams extends QueryParams {
  status?: string[];
  department?: string[];
  location?: string[];
  remoteType?: string[];
  employmentType?: string[];
  experienceLevel?: string[];
  salaryRange?: {
    min?: number;
    max?: number;
  };
  requiredSkills?: string[];
  tags?: string[];
  priority?: string[];
  hiringManagerId?: string;
  recruiterId?: string;
}

export class JobApiService extends BaseService<Job, CreateJobData, UpdateJobData> {
  constructor(apiClient: ApiClient) {
    super(apiClient, '/jobs');
  }

  /**
   * Get jobs with advanced filtering
   */
  async getJobs(params?: JobQueryParams): Promise<PaginatedResponse<Job>> {
    const queryParams = this.buildJobQueryParams(params);
    return this.apiClient.get<Job[]>(this.baseEndpoint, queryParams);
  }

  /**
   * Get job by ID with full details
   */
  async getJobById(id: string): Promise<ApiResponse<Job>> {
    return this.getById(id);
  }

  /**
   * Create new job
   */
  async createJob(data: CreateJobData): Promise<ApiResponse<Job>> {
    return this.create(data);
  }

  /**
   * Update job
   */
  async updateJob(id: string, data: UpdateJobData): Promise<ApiResponse<Job>> {
    return this.update(id, data);
  }

  /**
   * Delete job
   */
  async deleteJob(id: string): Promise<ApiResponse<void>> {
    return this.delete(id);
  }

  /**
   * Update job status
   */
  async updateJobStatus(
    id: string, 
    status: Job['status'],
    notes?: string
  ): Promise<ApiResponse<Job>> {
    return this.apiClient.patch<Job>(`${this.baseEndpoint}/${id}/status`, {
      status,
      notes,
    });
  }

  /**
   * Publish job
   */
  async publishJob(id: string): Promise<ApiResponse<Job>> {
    return this.apiClient.post<Job>(`${this.baseEndpoint}/${id}/publish`);
  }

  /**
   * Close job
   */
  async closeJob(id: string, reason?: string): Promise<ApiResponse<Job>> {
    return this.apiClient.post<Job>(`${this.baseEndpoint}/${id}/close`, { reason });
  }

  /**
   * Archive job
   */
  async archiveJob(id: string): Promise<ApiResponse<Job>> {
    return this.apiClient.post<Job>(`${this.baseEndpoint}/${id}/archive`);
  }

  /**
   * Duplicate job
   */
  async duplicateJob(id: string, title?: string): Promise<ApiResponse<Job>> {
    return this.apiClient.post<Job>(`${this.baseEndpoint}/${id}/duplicate`, { title });
  }

  /**
   * Get job statistics
   */
  async getJobStats(filters?: Partial<JobQueryParams>): Promise<ApiResponse<JobStatistics>> {
    const queryParams = filters ? this.buildJobQueryParams(filters) : {};
    return this.apiClient.get<JobStatistics>(`${this.baseEndpoint}/stats`, queryParams);
  }

  /**
   * Search jobs by skills
   */
  async searchBySkills(skills: string[], params?: QueryParams): Promise<PaginatedResponse<Job>> {
    const queryParams = {
      ...this.buildQueryParams(params),
      skills: skills.join(','),
    };
    return this.apiClient.get<Job[]>(`${this.baseEndpoint}/search/skills`, queryParams);
  }

  /**
   * Get similar jobs
   */
  async getSimilarJobs(id: string, limit: number = 5): Promise<ApiResponse<Job[]>> {
    return this.apiClient.get<Job[]>(`${this.baseEndpoint}/${id}/similar`, { limit });
  }

  /**
   * Get job applications
   */
  async getJobApplications(id: string, params?: QueryParams): Promise<PaginatedResponse<any>> {
    const queryParams = this.buildQueryParams(params);
    return this.apiClient.get<any[]>(`${this.baseEndpoint}/${id}/applications`, queryParams);
  }

  /**
   * Get job candidates
   */
  async getJobCandidates(id: string, params?: QueryParams): Promise<PaginatedResponse<any>> {
    const queryParams = this.buildQueryParams(params);
    return this.apiClient.get<any[]>(`${this.baseEndpoint}/${id}/candidates`, queryParams);
  }

  /**
   * Add candidate to job
   */
  async addCandidateToJob(jobId: string, candidateId: string): Promise<ApiResponse<void>> {
    return this.apiClient.post<void>(`${this.baseEndpoint}/${jobId}/candidates/${candidateId}`);
  }

  /**
   * Remove candidate from job
   */
  async removeCandidateFromJob(jobId: string, candidateId: string): Promise<ApiResponse<void>> {
    return this.apiClient.delete<void>(`${this.baseEndpoint}/${jobId}/candidates/${candidateId}`);
  }

  /**
   * Bulk update job status
   */
  async bulkUpdateStatus(
    jobIds: string[],
    status: Job['status'],
    notes?: string
  ): Promise<ApiResponse<Job[]>> {
    return this.apiClient.patch<Job[]>(`${this.baseEndpoint}/bulk/status`, {
      jobIds,
      status,
      notes,
    });
  }

  /**
   * Export jobs to CSV
   */
  async exportJobs(params?: JobQueryParams): Promise<ApiResponse<{ downloadUrl: string }>> {
    const queryParams = this.buildJobQueryParams(params);
    return this.apiClient.get<{ downloadUrl: string }>(`${this.baseEndpoint}/export`, queryParams);
  }

  /**
   * Import jobs from CSV
   */
  async importJobs(file: File): Promise<ApiResponse<{ imported: number; errors: string[] }>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.apiClient.request<{ imported: number; errors: string[] }>(`${this.baseEndpoint}/import`, {
      method: 'POST',
      body: formData,
      headers: {},
    });
  }

  /**
   * Get job activity timeline
   */
  async getJobActivity(id: string): Promise<ApiResponse<any[]>> {
    return this.apiClient.get<any[]>(`${this.baseEndpoint}/${id}/activity`);
  }

  /**
   * Get job analytics
   */
  async getJobAnalytics(id: string, period: 'day' | 'week' | 'month' = 'week'): Promise<ApiResponse<any>> {
    return this.apiClient.get<any>(`${this.baseEndpoint}/${id}/analytics`, { period });
  }

  /**
   * Get job templates
   */
  async getJobTemplates(): Promise<ApiResponse<any[]>> {
    return this.apiClient.get<any[]>('/job-templates');
  }

  /**
   * Create job from template
   */
  async createJobFromTemplate(templateId: string, data?: Partial<CreateJobData>): Promise<ApiResponse<Job>> {
    return this.apiClient.post<Job>(`/job-templates/${templateId}/create-job`, data);
  }

  /**
   * Get departments
   */
  async getDepartments(): Promise<ApiResponse<string[]>> {
    return this.apiClient.get<string[]>('/departments');
  }

  /**
   * Get locations
   */
  async getLocations(): Promise<ApiResponse<string[]>> {
    return this.apiClient.get<string[]>('/locations');
  }

  /**
   * Get skills suggestions
   */
  async getSkillsSuggestions(query: string): Promise<ApiResponse<string[]>> {
    return this.apiClient.get<string[]>('/skills/suggestions', { query });
  }

  // Protected method overrides
  protected validateCreateData(data: CreateJobData): void {
    super.validateCreateData(data);
    
    if (!data.title?.trim()) {
      throw new Error('Job title is required');
    }
    
    if (!data.description?.trim()) {
      throw new Error('Job description is required');
    }
    
    if (!data.department?.trim()) {
      throw new Error('Department is required');
    }
    
    if (!data.location?.trim()) {
      throw new Error('Location is required');
    }
    
    if (!data.requirements?.trim()) {
      throw new Error('Job requirements are required');
    }
    
    if (data.salaryMin && data.salaryMax && data.salaryMin > data.salaryMax) {
      throw new Error('Minimum salary cannot be greater than maximum salary');
    }
  }

  protected validateUpdateData(data: UpdateJobData): void {
    super.validateUpdateData(data);
    
    if (data.salaryMin && data.salaryMax && data.salaryMin > data.salaryMax) {
      throw new Error('Minimum salary cannot be greater than maximum salary');
    }
  }

  private buildJobQueryParams(params?: JobQueryParams): Record<string, any> {
    if (!params) return {};

    const queryParams = this.buildQueryParams(params);

    // Add job-specific filters
    if (params.status?.length) {
      queryParams.status = params.status.join(',');
    }

    if (params.department?.length) {
      queryParams.department = params.department.join(',');
    }

    if (params.location?.length) {
      queryParams.location = params.location.join(',');
    }

    if (params.remoteType?.length) {
      queryParams.remoteType = params.remoteType.join(',');
    }

    if (params.employmentType?.length) {
      queryParams.employmentType = params.employmentType.join(',');
    }

    if (params.experienceLevel?.length) {
      queryParams.experienceLevel = params.experienceLevel.join(',');
    }

    if (params.salaryRange) {
      if (params.salaryRange.min !== undefined) {
        queryParams.salaryMin = params.salaryRange.min;
      }
      if (params.salaryRange.max !== undefined) {
        queryParams.salaryMax = params.salaryRange.max;
      }
    }

    if (params.requiredSkills?.length) {
      queryParams.requiredSkills = params.requiredSkills.join(',');
    }

    if (params.tags?.length) {
      queryParams.tags = params.tags.join(',');
    }

    if (params.priority?.length) {
      queryParams.priority = params.priority.join(',');
    }

    if (params.hiringManagerId) {
      queryParams.hiringManagerId = params.hiringManagerId;
    }

    if (params.recruiterId) {
      queryParams.recruiterId = params.recruiterId;
    }

    return queryParams;
  }
}
