/**
 * Job Business Service
 * Handles business logic and orchestrates job operations
 */

import { JobApiService, JobQueryParams } from './JobApiService';
import { 
  Job, 
  CreateJobData, 
  UpdateJobData, 
  JobSearchFilters,
  JobStatistics,
  JobStatus,
  BulkJobOperation,
  JobTemplate,
  JobAnalytics
} from '../types';
import { PaginatedResponse, ApiResponse } from '@/core/api';

export class JobBusinessService {
  constructor(private jobApiService: JobApiService) {}

  /**
   * Get jobs with business logic applied
   */
  async getJobs(
    filters?: JobSearchFilters, 
    page: number = 1, 
    limit: number = 10
  ): Promise<PaginatedResponse<Job>> {
    const params: JobQueryParams = {
      page,
      limit,
      ...this.buildQueryParams(filters),
    };

    const response = await this.jobApiService.getJobs(params);
    
    // Apply business logic transformations
    const transformedJobs = response.data.map(job => this.enrichJobData(job));
    
    return {
      ...response,
      data: transformedJobs,
    };
  }

  /**
   * Get job by ID with enriched data
   */
  async getJobById(id: string): Promise<ApiResponse<Job>> {
    const response = await this.jobApiService.getJobById(id);
    
    if (response.data) {
      response.data = this.enrichJobData(response.data);
    }
    
    return response;
  }

  /**
   * Create job with validation and business rules
   */
  async createJob(data: CreateJobData): Promise<ApiResponse<Job>> {
    // Apply business validation
    this.validateJobData(data);
    
    // Apply business rules
    const processedData = this.applyJobCreationRules(data);
    
    const response = await this.jobApiService.createJob(processedData);
    
    if (response.data) {
      response.data = this.enrichJobData(response.data);
    }
    
    return response;
  }

  /**
   * Update job with validation and business rules
   */
  async updateJob(id: string, data: UpdateJobData): Promise<ApiResponse<Job>> {
    // Apply business validation
    this.validateJobUpdateData(data);
    
    // Apply business rules
    const processedData = this.applyJobUpdateRules(data);
    
    const response = await this.jobApiService.updateJob(id, processedData);
    
    if (response.data) {
      response.data = this.enrichJobData(response.data);
    }
    
    return response;
  }

  /**
   * Update job status with business logic
   */
  async updateJobStatus(id: string, status: JobStatus, reason?: string): Promise<ApiResponse<Job>> {
    // Validate status transition
    const currentJob = await this.getJobById(id);
    if (!currentJob.data) {
      throw new Error('Job not found');
    }

    this.validateStatusTransition(currentJob.data.status, status);

    const updateData: UpdateJobData = {
      status,
      statusReason: reason,
      statusUpdatedAt: new Date().toISOString(),
    };

    return this.updateJob(id, updateData);
  }

  /**
   * Delete job with business rules
   */
  async deleteJob(id: string): Promise<void> {
    // Check if job can be deleted
    const job = await this.getJobById(id);
    if (!job.data) {
      throw new Error('Job not found');
    }

    this.validateJobDeletion(job.data);
    
    return this.jobApiService.deleteJob(id);
  }

  /**
   * Archive job
   */
  async archiveJob(id: string): Promise<ApiResponse<Job>> {
    return this.updateJobStatus(id, 'archived', 'Archived by user');
  }

  /**
   * Publish job
   */
  async publishJob(id: string): Promise<ApiResponse<Job>> {
    const job = await this.getJobById(id);
    if (!job.data) {
      throw new Error('Job not found');
    }

    // Validate job is ready for publishing
    this.validateJobForPublishing(job.data);
    
    return this.updateJobStatus(id, 'published', 'Published by user');
  }

  /**
   * Close job
   */
  async closeJob(id: string, reason?: string): Promise<ApiResponse<Job>> {
    return this.updateJobStatus(id, 'closed', reason || 'Closed by user');
  }

  /**
   * Duplicate job
   */
  async duplicateJob(id: string): Promise<ApiResponse<Job>> {
    const originalJob = await this.getJobById(id);
    if (!originalJob.data) {
      throw new Error('Job not found');
    }

    const duplicateData: CreateJobData = {
      ...originalJob.data,
      title: `${originalJob.data.title} (Copy)`,
      status: 'draft',
      publishedAt: undefined,
      closedAt: undefined,
    };

    // Remove fields that shouldn't be duplicated
    delete (duplicateData as any).id;
    delete (duplicateData as any).createdAt;
    delete (duplicateData as any).updatedAt;

    return this.createJob(duplicateData);
  }

  /**
   * Get job statistics
   */
  async getJobStatistics(filters?: JobSearchFilters): Promise<JobStatistics> {
    return this.jobApiService.getJobStatistics(filters);
  }

  /**
   * Get job analytics
   */
  async getJobAnalytics(id: string): Promise<JobAnalytics> {
    return this.jobApiService.getJobAnalytics(id);
  }

  /**
   * Perform bulk operations
   */
  async performBulkOperation(operation: BulkJobOperation): Promise<{
    successCount: number;
    failureCount: number;
    errors: string[];
  }> {
    return this.jobApiService.bulkOperation(operation);
  }

  /**
   * Search jobs with advanced filters
   */
  async searchJobs(
    query: string, 
    filters?: JobSearchFilters,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResponse<Job>> {
    const searchFilters = {
      ...filters,
      query,
    };

    return this.getJobs(searchFilters, page, limit);
  }

  /**
   * Get job templates
   */
  async getJobTemplates(): Promise<JobTemplate[]> {
    return this.jobApiService.getJobTemplates();
  }

  /**
   * Create job from template
   */
  async createJobFromTemplate(templateId: string, customData?: Partial<CreateJobData>): Promise<ApiResponse<Job>> {
    const template = await this.jobApiService.getJobTemplate(templateId);
    
    const jobData: CreateJobData = {
      ...template,
      ...customData,
      status: 'draft',
    };

    return this.createJob(jobData);
  }

  // Private helper methods

  private buildQueryParams(filters?: JobSearchFilters): Partial<JobQueryParams> {
    if (!filters) return {};

    return {
      status: filters.status,
      department: filters.department,
      location: filters.location,
      remoteType: filters.remoteType,
      employmentType: filters.employmentType,
      experienceLevel: filters.experienceLevel,
      salaryRange: filters.salaryRange,
      requiredSkills: filters.requiredSkills,
      tags: filters.tags,
      priority: filters.priority,
      hiringManagerId: filters.hiringManagerId,
      recruiterId: filters.recruiterId,
    };
  }

  private enrichJobData(job: Job): Job {
    // Add computed fields and business logic
    return {
      ...job,
      // Add any computed properties or enrichments
      isActive: job.status === 'published',
      canEdit: ['draft', 'published'].includes(job.status),
      canDelete: job.status === 'draft',
      daysOpen: job.publishedAt 
        ? Math.floor((Date.now() - new Date(job.publishedAt).getTime()) / (1000 * 60 * 60 * 24))
        : 0,
    };
  }

  private validateJobData(data: CreateJobData): void {
    if (!data.title?.trim()) {
      throw new Error('Job title is required');
    }
    if (!data.description?.trim()) {
      throw new Error('Job description is required');
    }
    if (!data.department?.trim()) {
      throw new Error('Department is required');
    }
    if (!data.location?.trim()) {
      throw new Error('Location is required');
    }
  }

  private validateJobUpdateData(data: UpdateJobData): void {
    if (data.title !== undefined && !data.title?.trim()) {
      throw new Error('Job title cannot be empty');
    }
    if (data.description !== undefined && !data.description?.trim()) {
      throw new Error('Job description cannot be empty');
    }
  }

  private applyJobCreationRules(data: CreateJobData): CreateJobData {
    return {
      ...data,
      status: data.status || 'draft',
      currency: data.currency || 'USD',
      createdAt: new Date().toISOString(),
    };
  }

  private applyJobUpdateRules(data: UpdateJobData): UpdateJobData {
    return {
      ...data,
      updatedAt: new Date().toISOString(),
    };
  }

  private validateStatusTransition(currentStatus: JobStatus, newStatus: JobStatus): void {
    const validTransitions: Record<JobStatus, JobStatus[]> = {
      draft: ['published', 'archived'],
      published: ['closed', 'archived'],
      closed: ['published', 'archived'],
      archived: ['draft'],
    };

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw new Error(`Cannot transition from ${currentStatus} to ${newStatus}`);
    }
  }

  private validateJobDeletion(job: Job): void {
    if (job.status === 'published') {
      throw new Error('Cannot delete published job. Please close or archive it first.');
    }
  }

  private validateJobForPublishing(job: Job): void {
    if (!job.title?.trim()) {
      throw new Error('Job title is required for publishing');
    }
    if (!job.description?.trim()) {
      throw new Error('Job description is required for publishing');
    }
    if (!job.requirements?.trim()) {
      throw new Error('Job requirements are required for publishing');
    }
  }
}
