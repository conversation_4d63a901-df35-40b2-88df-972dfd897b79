/**
 * Job Service - Main Service
 * Unified service that combines API and business logic
 */

import { JobApiService } from './JobApiService';
import { JobBusinessService } from './JobBusinessService';
import { apiClient } from '@/core/api/services';

// Create service instances
const jobApiService = new JobApiService(apiClient);
const jobBusinessService = new JobBusinessService(jobApiService);

/**
 * Main Job Service
 * Provides a unified interface for all job operations
 */
export class JobService {
  // Expose the business service methods
  static async getJobs(filters?: any, page?: number, limit?: number) {
    return jobBusinessService.getJobs(filters, page, limit);
  }

  static async getJobById(id: string) {
    return jobBusinessService.getJobById(id);
  }

  static async createJob(data: any) {
    return jobBusinessService.createJob(data);
  }

  static async updateJob(id: string, data: any) {
    return jobBusinessService.updateJob(id, data);
  }

  static async deleteJob(id: string) {
    return jobBusinessService.deleteJob(id);
  }

  static async updateJobStatus(id: string, status: any, reason?: string) {
    return jobBusinessService.updateJobStatus(id, status, reason);
  }

  static async archiveJob(id: string) {
    return jobBusinessService.archiveJob(id);
  }

  static async publishJob(id: string) {
    return jobBusinessService.publishJob(id);
  }

  static async closeJob(id: string, reason?: string) {
    return jobBusinessService.closeJob(id, reason);
  }

  static async duplicateJob(id: string) {
    return jobBusinessService.duplicateJob(id);
  }

  static async getJobStatistics(filters?: any) {
    return jobBusinessService.getJobStatistics(filters);
  }

  static async getJobAnalytics(id: string) {
    return jobBusinessService.getJobAnalytics(id);
  }

  static async performBulkOperation(operation: any) {
    return jobBusinessService.performBulkOperation(operation);
  }

  static async searchJobs(query: string, filters?: any, page?: number, limit?: number) {
    return jobBusinessService.searchJobs(query, filters, page, limit);
  }

  static async getJobTemplates() {
    return jobBusinessService.getJobTemplates();
  }

  static async createJobFromTemplate(templateId: string, customData?: any) {
    return jobBusinessService.createJobFromTemplate(templateId, customData);
  }

  // Direct access to services for advanced use cases
  static get api() {
    return jobApiService;
  }

  static get business() {
    return jobBusinessService;
  }
}

// Export service instances for direct use
export { jobApiService, jobBusinessService };

// Default export
export default JobService;
