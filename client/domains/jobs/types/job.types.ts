/**
 * Job Domain Types
 * Core types and interfaces for the jobs domain
 */

import { BaseEntity } from '@/core/api';

// Job status enum
export type JobStatus = 
  | 'draft'
  | 'published'
  | 'closed'
  | 'archived';

// Job priority levels
export type JobPriority = 'low' | 'medium' | 'high' | 'urgent';

// Employment types
export type EmploymentType = 'full-time' | 'part-time' | 'contract' | 'internship' | 'freelance';

// Experience levels
export type ExperienceLevel = 'entry' | 'junior' | 'mid' | 'senior' | 'lead' | 'executive';

// Remote work options
export type RemoteType = 'on-site' | 'remote' | 'hybrid';

// Core Job interface
export interface Job extends BaseEntity {
  // Basic Information
  title: string;
  description: string;
  department: string;
  location: string;
  remoteType: RemoteType;
  
  // Employment Details
  employmentType: EmploymentType;
  experienceLevel: ExperienceLevel;
  
  // Requirements and Responsibilities
  requirements: string;
  responsibilities?: string;
  qualifications?: string;
  benefits?: string;
  
  // Compensation
  salaryMin?: number;
  salaryMax?: number;
  currency: string;
  salaryType?: 'hourly' | 'monthly' | 'yearly';
  
  // Skills and Tags
  requiredSkills: string[];
  preferredSkills?: string[];
  tags?: string[];
  
  // Status and Metadata
  status: JobStatus;
  priority?: JobPriority;
  
  // Hiring Information
  hiringManagerId?: string;
  hiringManagerName?: string;
  recruiterId?: string;
  recruiterName?: string;
  
  // Application Settings
  applicationDeadline?: string;
  maxApplications?: number;
  applicationCount?: number;
  
  // SEO and External
  slug?: string;
  externalJobId?: string;
  jobBoardUrls?: string[];
  
  // Analytics
  viewCount?: number;
  applicationRate?: number;
  
  // Timestamps
  publishedAt?: string;
  closedAt?: string;
  
  // Computed fields
  isActive?: boolean;
  daysOpen?: number;
  formattedSalary?: string;
  candidateCount?: number;
}

// Create job data interface
export interface CreateJobData {
  title: string;
  description: string;
  department: string;
  location: string;
  remoteType: RemoteType;
  employmentType: EmploymentType;
  experienceLevel: ExperienceLevel;
  requirements: string;
  responsibilities?: string;
  qualifications?: string;
  benefits?: string;
  salaryMin?: number;
  salaryMax?: number;
  currency?: string;
  requiredSkills: string[];
  preferredSkills?: string[];
  tags?: string[];
  priority?: JobPriority;
  hiringManagerId?: string;
  recruiterId?: string;
  applicationDeadline?: string;
  maxApplications?: number;
}

// Update job data interface
export interface UpdateJobData {
  title?: string;
  description?: string;
  department?: string;
  location?: string;
  remoteType?: RemoteType;
  employmentType?: EmploymentType;
  experienceLevel?: ExperienceLevel;
  requirements?: string;
  responsibilities?: string;
  qualifications?: string;
  benefits?: string;
  salaryMin?: number;
  salaryMax?: number;
  currency?: string;
  requiredSkills?: string[];
  preferredSkills?: string[];
  tags?: string[];
  priority?: JobPriority;
  hiringManagerId?: string;
  recruiterId?: string;
  applicationDeadline?: string;
  maxApplications?: number;
}

// Job search filters
export interface JobSearchFilters {
  query?: string;
  status?: JobStatus[];
  department?: string[];
  location?: string[];
  remoteType?: RemoteType[];
  employmentType?: EmploymentType[];
  experienceLevel?: ExperienceLevel[];
  salaryRange?: {
    min: number;
    max: number;
  };
  requiredSkills?: string[];
  tags?: string[];
  priority?: JobPriority[];
  hiringManagerId?: string;
  recruiterId?: string;
  dateRange?: {
    from: string;
    to: string;
  };
}

// Job list item (for tables and lists)
export interface JobListItem {
  id: string;
  title: string;
  department: string;
  location: string;
  remoteType: RemoteType;
  employmentType: EmploymentType;
  status: JobStatus;
  applicationCount?: number;
  publishedAt?: string;
  priority?: JobPriority;
  hiringManagerName?: string;
  
  // Computed fields
  isActive?: boolean;
  daysOpen?: number;
  formattedSalary?: string;
}

// Job statistics
export interface JobStatistics {
  total: number;
  byStatus: Record<JobStatus, number>;
  byDepartment: Record<string, number>;
  byEmploymentType: Record<EmploymentType, number>;
  totalApplications: number;
  averageApplicationsPerJob: number;
  averageTimeToFill: number;
  topSkills: Array<{ skill: string; count: number }>;
}

// Job application interface
export interface JobApplication {
  id: string;
  jobId: string;
  candidateId: string;
  candidateName: string;
  candidateEmail: string;
  status: string;
  appliedAt: string;
  resumeUrl?: string;
  coverLetter?: string;
  customFields?: Record<string, any>;
}

// Job template interface
export interface JobTemplate {
  id: string;
  name: string;
  description?: string;
  template: Partial<CreateJobData>;
  isDefault?: boolean;
  departmentId?: string;
  createdBy: string;
  createdAt: string;
}

// Job posting settings
export interface JobPostingSettings {
  jobId: string;
  autoPublish?: boolean;
  jobBoards?: Array<{
    name: string;
    enabled: boolean;
    settings?: Record<string, any>;
  }>;
  applicationForm?: {
    fields: Array<{
      name: string;
      type: string;
      required: boolean;
      options?: string[];
    }>;
  };
  screening?: {
    questions: Array<{
      question: string;
      type: 'text' | 'multiple-choice' | 'yes-no';
      required: boolean;
      options?: string[];
    }>;
  };
}

// Job analytics
export interface JobAnalytics {
  jobId: string;
  period: 'day' | 'week' | 'month';
  views: number;
  applications: number;
  conversionRate: number;
  sourceBreakdown: Record<string, number>;
  locationBreakdown: Record<string, number>;
  deviceBreakdown: Record<string, number>;
  timeline: Array<{
    date: string;
    views: number;
    applications: number;
  }>;
}

// Bulk operations
export interface BulkJobOperation {
  jobIds: string[];
  operation: 'update_status' | 'add_tags' | 'remove_tags' | 'delete' | 'export';
  data?: any;
}

// Export options
export interface JobExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  fields: string[];
  filters?: JobSearchFilters;
  includeApplications?: boolean;
  includeAnalytics?: boolean;
}

// Import result
export interface JobImportResult {
  total: number;
  imported: number;
  failed: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
  duplicates: Array<{
    row: number;
    title: string;
    existingId: string;
  }>;
}

// Job form validation
export interface JobFormErrors {
  title?: string;
  description?: string;
  department?: string;
  location?: string;
  requirements?: string;
  salaryMin?: string;
  salaryMax?: string;
  requiredSkills?: string;
  general?: string;
}

// Job matching criteria
export interface JobMatchingCriteria {
  candidateId: string;
  requiredSkillsWeight: number;
  preferredSkillsWeight: number;
  experienceWeight: number;
  locationWeight: number;
  salaryWeight: number;
}

// Job recommendation
export interface JobRecommendation {
  job: JobListItem;
  matchScore: number;
  skillMatches: string[];
  missingSkills: string[];
  reasons: string[];
  confidence: number;
}

// Job workflow stage
export interface JobWorkflowStage {
  id: string;
  name: string;
  description?: string;
  order: number;
  isDefault?: boolean;
  actions?: Array<{
    name: string;
    type: 'email' | 'task' | 'notification';
    config: Record<string, any>;
  }>;
}

// Job workflow
export interface JobWorkflow {
  id: string;
  name: string;
  description?: string;
  stages: JobWorkflowStage[];
  isDefault?: boolean;
  departmentId?: string;
}

// Job activity log
export interface JobActivity {
  id: string;
  type: 'status_change' | 'application_received' | 'candidate_moved' | 'note_added' | 'published';
  description: string;
  details?: Record<string, any>;
  userId: string;
  userName: string;
  timestamp: string;
}
