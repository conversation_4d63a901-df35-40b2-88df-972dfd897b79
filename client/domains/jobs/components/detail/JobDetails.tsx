import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Briefcase, Award, CheckCircle } from "lucide-react";
import { Job, JobUtils } from "../../types";

export interface JobDetailsProps {
  job: Job;
}

export const JobDetails: React.FC<JobDetailsProps> = ({ job }) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="w-5 h-5" />
            Job Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm font-medium">Department</p>
            <p className="text-sm text-muted-foreground">{job.department}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Employment Type</p>
            <p className="text-sm text-muted-foreground">
              {JobUtils.formatEmploymentType(job.employmentType)}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Experience Level</p>
            <p className="text-sm text-muted-foreground">
              {JobUtils.formatExperienceLevel(job.experienceLevel)}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Remote Work</p>
            <p className="text-sm text-muted-foreground">
              {JobUtils.formatRemoteType(job.remoteType)}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Application Deadline</p>
            <p className="text-sm text-muted-foreground">
              {job.applicationDeadline &&
              !isNaN(new Date(job.applicationDeadline).getTime())
                ? new Date(job.applicationDeadline).toLocaleDateString()
                : "No deadline set"}
            </p>
          </div>
        </CardContent>
      </Card>

      {job.benefits && job.benefits.trim() && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              Benefits & Perks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm whitespace-pre-wrap">{job.benefits}</div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
