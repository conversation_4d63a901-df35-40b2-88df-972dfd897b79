import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Briefcase, Award, CheckCircle } from "lucide-react";

export interface JobDetailsProps {
  job: {
    department: string;
    type?: string;
    experienceLevel?: string;
    remote?: boolean;
    deadline?: string;
    benefits?: string[];
  };
}

export const JobDetails: React.FC<JobDetailsProps> = ({ job }) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="w-5 h-5" />
            Job Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm font-medium">Department</p>
            <p className="text-sm text-muted-foreground">{job.department}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Employment Type</p>
            <p className="text-sm text-muted-foreground">
              {job.type || "Full-time"}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Experience Level</p>
            <p className="text-sm text-muted-foreground">
              {job.experienceLevel || "Mid-level"}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Remote Work</p>
            <p className="text-sm text-muted-foreground">
              {job.remote ? "Remote friendly" : "Office required"}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Application Deadline</p>
            <p className="text-sm text-muted-foreground">
              {job.deadline && !isNaN(new Date(job.deadline).getTime())
                ? new Date(job.deadline).toLocaleDateString()
                : "No deadline set"}
            </p>
          </div>
        </CardContent>
      </Card>

      {job.benefits && job.benefits.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              Benefits & Perks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {job.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{benefit}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
