import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Users, Eye, ExternalLink } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { Candidate } from "@/domains/candidates/types";
import { CandidateUtils } from "@/domains/candidates/types";

export interface JobCandidatesProps {
  candidates: Candidate[];
  onViewAll?: () => void;
}

export const JobCandidates: React.FC<JobCandidatesProps> = ({
  candidates,
  onViewAll,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Applications ({candidates.length})
          </div>
          {onViewAll && (
            <Button size="sm" onClick={onViewAll}>
              <Eye className="w-4 h-4 mr-2" />
              View All
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {candidates && candidates.length > 0 ? (
            candidates.slice(0, 5).map((candidate) => (
              <div
                key={candidate.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {(candidate.name || "?")
                      .split(" ")
                      .map((n: string) => n[0])
                      .join("")}
                  </div>
                  <div>
                    <p className="font-medium">{candidate.name || "Unknown"}</p>
                    <p className="text-sm text-muted-foreground">
                      {candidate.position || "N/A"} •{" "}
                      {candidate.experience || "N/A"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Badge
                    className={CandidateUtils.getStatusColor(candidate.status)}
                  >
                    {candidate.status || "unknown"}
                  </Badge>
                  <Button variant="ghost" size="sm" asChild>
                    <Link to={`/candidates/detail/${candidate.id}`}>
                      <ExternalLink className="w-4 h-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            ))
          ) : (
            <p className="text-muted-foreground text-center py-8">
              No applications yet
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
