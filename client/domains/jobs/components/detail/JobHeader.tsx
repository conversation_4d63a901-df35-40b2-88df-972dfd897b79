import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Building2,
  MapPin,
  DollarSign,
  Calendar,
  Edit,
  Share2,
  Copy,
  ChevronLeft,
} from "lucide-react";
import { Job, JobUtils } from "../../types";

export interface JobHeaderProps {
  job: Job;
  showBackButton?: boolean;
  isFullPage?: boolean;
  onBack?: () => void;
  onEdit?: (job: Job) => void;
  onShare?: () => void;
  onCopyLink?: () => void;
  onDuplicate?: (job: Job) => void;
  onStatusChange?: (jobId: string, newStatus: string) => void;
}

export const JobHeader: React.FC<JobHeaderProps> = ({
  job,
  showBackButton = false,
  isFullPage = false,
  onBack,
  onEdit,
  onShare,
  onCopyLink,
  onDuplicate,
  onStatusChange,
}) => {
  return (
    <div className="flex items-start justify-between">
      <div className="flex items-start gap-4 flex-1">
        {showBackButton && onBack && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2 mt-2"
          >
            <ChevronLeft className="w-4 h-4" />
            Back
          </Button>
        )}
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <h1
              className={`font-semibold ${isFullPage ? "text-3xl" : "text-xl"}`}
            >
              {job.title}
            </h1>
            <Badge className={JobUtils.getStatusColor(job.status)}>
              {job.status}
            </Badge>
            {job.priority && (
              <Badge className={JobUtils.getPriorityColor(job.priority)}>
                {job.priority} priority
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-4 text-muted-foreground">
            <div className="flex items-center gap-1">
              <Building2 className="w-4 h-4" />
              <span>{job.department}</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="w-4 h-4" />
              <span>{job.location}</span>
            </div>
            {(job.salaryMin || job.salaryMax) && (
              <div className="flex items-center gap-1">
                <DollarSign className="w-4 h-4" />
                <span>
                  {JobUtils.formatSalary(
                    job.salaryMin,
                    job.salaryMax,
                    job.currency,
                    job.salaryType,
                  )}
                </span>
              </div>
            )}
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              <span>
                Posted{" "}
                {job.publishedAt
                  ? new Date(job.publishedAt).toLocaleDateString()
                  : "Unknown"}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {onShare && (
          <Button variant="outline" size="sm" onClick={onShare}>
            <Share2 className="w-4 h-4 mr-2" />
            Share
          </Button>
        )}
        {onCopyLink && (
          <Button variant="outline" size="sm" onClick={onCopyLink}>
            <Copy className="w-4 h-4 mr-2" />
            Copy Link
          </Button>
        )}
        {onEdit && (
          <Button variant="outline" size="sm" onClick={() => onEdit(job)}>
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>
        )}
        {onDuplicate && (
          <Button variant="outline" size="sm" onClick={() => onDuplicate(job)}>
            <Copy className="w-4 h-4 mr-2" />
            Duplicate
          </Button>
        )}
      </div>
    </div>
  );
};
