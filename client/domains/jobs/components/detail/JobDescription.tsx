import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { FileText, Target, CheckCircle } from "lucide-react";
import { Job } from "../../types";

export interface JobDescriptionProps {
  job: Job;
}

export const JobDescription: React.FC<JobDescriptionProps> = ({ job }) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Job Description
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="prose prose-sm max-w-none">
            <p className="text-muted-foreground leading-relaxed">
              {job.description}
            </p>
          </div>
        </CardContent>
      </Card>

      {job.responsibilities && job.responsibilities.trim() && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Key Responsibilities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm whitespace-pre-wrap">
              {job.responsibilities}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
