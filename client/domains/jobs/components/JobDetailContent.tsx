import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Job, mockCandidates } from "@/domains/candidates/types";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@/lib/i18n";
import {
  JobHeader,
  JobMetrics,
  JobDescription,
  JobDetails,
  JobRequirements,
  JobCandidates,
  JobAnalytics,
} from "./detail";

interface JobDetailContentProps {
  job: any; // Use any for API response data
  candidates?: any[]; // Optional candidates data from API
  onEdit?: (job: any) => void;
  onDuplicate?: (job: any) => void;
  showBackButton?: boolean;
  isFullPage?: boolean;
}

export const JobDetailContent = ({
  job,
  candidates: candidatesFromProps,
  onEdit,
  onDuplicate,
  showBackButton = false,
  isFullPage = false,
}: JobDetailContentProps) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  if (!job) return null;

  // Use candidates from props (API) or fallback to empty array
  const candidates = candidatesFromProps || [];
  const activeCount = candidates.filter((c) => c.status !== "rejected").length;
  const hiredCount = candidates.filter((c) => c.status === "hired").length;

  const handleBack = () => {
    navigate(-1);
  };

  const handleCopyJobLink = () => {
    const url = `${window.location.origin}/jobs/detail/${job.id}`;
    navigator.clipboard.writeText(url);
    toast.success("Job link copied to clipboard!");
  };

  const handleShareJob = () => {
    if (navigator.share) {
      navigator.share({
        title: job.title,
        text: `Check out this job opportunity: ${job.title} at ${job.department}`,
        url: `${window.location.origin}/jobs/detail/${job.id}`,
      });
    } else {
      handleCopyJobLink();
    }
  };

  const containerClass = isFullPage
    ? "space-y-6 p-6 max-w-6xl mx-auto"
    : "space-y-6";

  return (
    <div className={containerClass}>
      {/* Header Section */}
      <JobHeader
        job={{
          id: job.id,
          title: job.title,
          department: job.department,
          location: job.location,
          salaryRange: job.salaryRange,
          status: job.status,
          priority: job.priority,
          postedAt: job.postedAt,
        }}
        showBackButton={showBackButton}
        isFullPage={isFullPage}
        onBack={handleBack}
        onEdit={onEdit}
        onShare={handleShareJob}
        onCopyLink={handleCopyJobLink}
        onDuplicate={onDuplicate}
      />

      <Separator />

      {/* Key Metrics */}
      <JobMetrics
        metrics={{
          totalApplications: candidates.length,
          activeApplications: activeCount,
          hiredCount: hiredCount,
          daysOpen: (() => {
            if (!job.postedAt) return 0;
            const postedDate = new Date(job.postedAt);
            if (isNaN(postedDate.getTime())) return 0;
            return Math.floor(
              (Date.now() - postedDate.getTime()) / (1000 * 60 * 60 * 24),
            );
          })(),
        }}
      />

      {/* Main Content */}
      <Tabs defaultValue="description" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="description">Description</TabsTrigger>
          <TabsTrigger value="requirements">Requirements</TabsTrigger>
          <TabsTrigger value="candidates">Candidates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Description Tab */}
        <TabsContent value="description" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <JobDescription
                job={{
                  description: job.description,
                  responsibilities: job.responsibilities,
                }}
              />
            </div>
            <div>
              <JobDetails
                job={{
                  department: job.department,
                  type: job.type,
                  experienceLevel: job.experienceLevel,
                  remote: job.remote,
                  deadline: job.deadline,
                  benefits: job.benefits,
                }}
              />
            </div>
          </div>
        </TabsContent>

        {/* Requirements Tab */}
        <TabsContent value="requirements" className="space-y-6">
          <JobRequirements
            job={{
              requiredSkills: job.requiredSkills,
              preferredSkills: job.preferredSkills,
              requirements: job.requirements,
            }}
          />
        </TabsContent>

        {/* Candidates Tab */}
        <TabsContent value="candidates" className="space-y-6">
          <JobCandidates
            candidates={candidates}
            onViewAll={() => {
              console.log("View all candidates");
            }}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <JobAnalytics
            analytics={{
              conversionRate:
                candidates.length > 0
                  ? Math.round((hiredCount / candidates.length) * 100)
                  : 0,
              averageTimeToHire: 14,
              totalApplications: candidates.length,
              hiredCount: hiredCount,
              postedDate: job.postedAt,
              closingDate: job.deadline,
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default JobDetailContent;
