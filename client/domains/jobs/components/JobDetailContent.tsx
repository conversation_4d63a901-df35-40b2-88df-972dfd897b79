import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Job, JobUtils } from "../types";
import { Candidate } from "@/domains/candidates/types";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import {
  JobHeader,
  JobMetrics,
  JobDescription,
  JobDetails,
  JobRequirements,
  JobCandidates,
  JobAnalytics,
} from "./detail";

interface JobDetailContentProps {
  job: Job;
  candidates?: Candidate[];
  onEdit?: (job: Job) => void;
  onDuplicate?: (job: Job) => void;
  onStatusChange?: (jobId: string, newStatus: string) => void;
  showBackButton?: boolean;
  isFullPage?: boolean;
}

export const JobDetailContent = ({
  job,
  candidates: candidatesFromProps,
  onEdit,
  onDuplicate,
  onStatusChange,
  showBackButton = false,
  isFullPage = false,
}: JobDetailContentProps) => {
  const navigate = useNavigate();

  if (!job) return null;

  // Use candidates from props (API) or fallback to empty array
  const candidates = candidatesFromProps || [];
  const activeCount = candidates.filter((c) => c.status !== "rejected").length;
  const hiredCount = candidates.filter((c) => c.status === "hired").length;

  const handleBack = () => {
    navigate(-1);
  };

  const handleCopyJobLink = () => {
    const url = JobUtils.getJobUrl(job, window.location.origin);
    navigator.clipboard.writeText(url);
    toast.success("Job link copied to clipboard!");
  };

  const handleShareJob = () => {
    const url = JobUtils.getJobUrl(job, window.location.origin);
    if (navigator.share) {
      navigator.share({
        title: job.title,
        text: `Check out this job opportunity: ${job.title} at ${job.department}`,
        url,
      });
    } else {
      handleCopyJobLink();
    }
  };

  const containerClass = isFullPage
    ? "space-y-6 p-6 max-w-6xl mx-auto"
    : "space-y-6";

  return (
    <div className={containerClass}>
      {/* Header Section */}
      <JobHeader
        job={job}
        showBackButton={showBackButton}
        isFullPage={isFullPage}
        onBack={handleBack}
        onEdit={onEdit}
        onShare={handleShareJob}
        onCopyLink={handleCopyJobLink}
        onDuplicate={onDuplicate}
        onStatusChange={onStatusChange}
      />

      <Separator />

      {/* Key Metrics */}
      <JobMetrics
        metrics={{
          totalApplications: candidates.length,
          activeApplications: activeCount,
          hiredCount: hiredCount,
          daysOpen: JobUtils.getDaysOpen(job.publishedAt),
        }}
      />

      {/* Main Content */}
      <Tabs defaultValue="description" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="description">Description</TabsTrigger>
          <TabsTrigger value="requirements">Requirements</TabsTrigger>
          <TabsTrigger value="candidates">Candidates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Description Tab */}
        <TabsContent value="description" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <JobDescription job={job} />
            </div>
            <div>
              <JobDetails job={job} />
            </div>
          </div>
        </TabsContent>

        {/* Requirements Tab */}
        <TabsContent value="requirements" className="space-y-6">
          <JobRequirements job={job} />
        </TabsContent>

        {/* Candidates Tab */}
        <TabsContent value="candidates" className="space-y-6">
          <JobCandidates
            candidates={candidates}
            onViewAll={() => {
              console.log("View all candidates");
            }}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <JobAnalytics
            job={job}
            analytics={{
              conversionRate: JobUtils.calculateApplicationRate(
                hiredCount,
                candidates.length,
              ),
              averageTimeToHire: 14,
              totalApplications: candidates.length,
              hiredCount: hiredCount,
              postedDate: job.publishedAt,
              closingDate: job.applicationDeadline,
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default JobDetailContent;
