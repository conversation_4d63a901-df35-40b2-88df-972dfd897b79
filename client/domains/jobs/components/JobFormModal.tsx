/**
 * Job Form Modal Component
 * Modern, unified modal for creating and editing jobs using FormBuilder system
 */

import React, { useMemo } from 'react';
import { z } from 'zod';
import { Briefcase, Building2, MapPin, DollarSign, Users } from 'lucide-react';
import { FormBuilder, FieldConfig } from '@/shared/components/forms';
import { FormModal } from '@/shared/components/modals';
import { 
  Job, 
  CreateJobData, 
  UpdateJobData,
  JobStatus,
  JobPriority,
  EmploymentType,
  ExperienceLevel,
  RemoteType,
  JobUtils,
  JobTypeGuards
} from '../types';
import { toast } from 'sonner';

// Validation schema
const jobSchema = z.object({
  // Basic Information
  title: z.string().min(2, 'Job title must be at least 2 characters'),
  description: z.string().min(10, 'Job description must be at least 10 characters'),
  department: z.string().min(2, 'Department is required'),
  location: z.string().min(2, 'Location is required'),
  
  // Employment Details
  remoteType: z.enum(['on-site', 'remote', 'hybrid']),
  employmentType: z.enum(['full-time', 'part-time', 'contract', 'internship', 'freelance']),
  experienceLevel: z.enum(['entry', 'junior', 'mid', 'senior', 'lead', 'executive']),
  
  // Requirements and Responsibilities
  requirements: z.string().min(10, 'Job requirements must be at least 10 characters'),
  responsibilities: z.string().optional(),
  qualifications: z.string().optional(),
  benefits: z.string().optional(),
  
  // Compensation
  salaryMin: z.number().min(0, 'Minimum salary must be positive').optional(),
  salaryMax: z.number().min(0, 'Maximum salary must be positive').optional(),
  currency: z.string().optional(),
  salaryType: z.enum(['hourly', 'monthly', 'yearly']).optional(),
  
  // Skills and Tags
  requiredSkills: z.array(z.string()).min(1, 'At least one required skill must be specified'),
  preferredSkills: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  
  // Status and Priority
  status: z.enum(['draft', 'published', 'closed', 'archived']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  
  // Hiring Information
  hiringManagerId: z.string().optional(),
  recruiterId: z.string().optional(),
  
  // Application Settings
  applicationDeadline: z.string().optional(),
  maxApplications: z.number().min(1, 'Maximum applications must be at least 1').optional(),
}).refine((data) => {
  // Custom validation: salary range
  if (data.salaryMin && data.salaryMax && data.salaryMin > data.salaryMax) {
    return false;
  }
  return true;
}, {
  message: "Minimum salary cannot be greater than maximum salary",
  path: ["salaryMax"],
});

type JobFormData = z.infer<typeof jobSchema>;

export interface JobFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  job?: Job | null;
  onSubmit: (data: CreateJobData | UpdateJobData) => Promise<void>;
  loading?: boolean;
}

export const JobFormModal: React.FC<JobFormModalProps> = ({
  isOpen,
  onClose,
  job,
  onSubmit,
  loading = false,
}) => {
  const isEditing = !!job;

  // Form field configuration
  const fieldConfig: FieldConfig<JobFormData>[] = useMemo(() => [
    // Basic Information Section
    {
      name: 'title',
      type: 'text',
      label: 'Job Title',
      placeholder: 'Enter job title',
      required: true,
      icon: <Briefcase className="h-4 w-4" />,
      section: 'Basic Information',
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Job Description',
      placeholder: 'Describe the role, company culture, and what makes this opportunity unique',
      required: true,
      rows: 6,
      section: 'Basic Information',
    },
    {
      name: 'department',
      type: 'text',
      label: 'Department',
      placeholder: 'Enter department',
      required: true,
      icon: <Building2 className="h-4 w-4" />,
      section: 'Basic Information',
    },
    {
      name: 'location',
      type: 'text',
      label: 'Location',
      placeholder: 'Enter job location',
      required: true,
      icon: <MapPin className="h-4 w-4" />,
      section: 'Basic Information',
    },

    // Employment Details Section
    {
      name: 'remoteType',
      type: 'select',
      label: 'Remote Type',
      required: true,
      options: [
        { value: 'on-site', label: 'On-site' },
        { value: 'remote', label: 'Remote' },
        { value: 'hybrid', label: 'Hybrid' },
      ],
      section: 'Employment Details',
    },
    {
      name: 'employmentType',
      type: 'select',
      label: 'Employment Type',
      required: true,
      options: [
        { value: 'full-time', label: 'Full-time' },
        { value: 'part-time', label: 'Part-time' },
        { value: 'contract', label: 'Contract' },
        { value: 'internship', label: 'Internship' },
        { value: 'freelance', label: 'Freelance' },
      ],
      section: 'Employment Details',
    },
    {
      name: 'experienceLevel',
      type: 'select',
      label: 'Experience Level',
      required: true,
      options: [
        { value: 'entry', label: 'Entry Level' },
        { value: 'junior', label: 'Junior' },
        { value: 'mid', label: 'Mid Level' },
        { value: 'senior', label: 'Senior' },
        { value: 'lead', label: 'Lead' },
        { value: 'executive', label: 'Executive' },
      ],
      section: 'Employment Details',
    },

    // Compensation Section
    {
      name: 'salaryMin',
      type: 'number',
      label: 'Minimum Salary',
      placeholder: 'Enter minimum salary',
      min: 0,
      icon: <DollarSign className="h-4 w-4" />,
      section: 'Compensation',
    },
    {
      name: 'salaryMax',
      type: 'number',
      label: 'Maximum Salary',
      placeholder: 'Enter maximum salary',
      min: 0,
      icon: <DollarSign className="h-4 w-4" />,
      section: 'Compensation',
    },
    {
      name: 'currency',
      type: 'select',
      label: 'Currency',
      options: [
        { value: 'USD', label: 'USD ($)' },
        { value: 'EUR', label: 'EUR (€)' },
        { value: 'GBP', label: 'GBP (£)' },
        { value: 'CAD', label: 'CAD (C$)' },
        { value: 'AUD', label: 'AUD (A$)' },
      ],
      section: 'Compensation',
    },
    {
      name: 'salaryType',
      type: 'select',
      label: 'Salary Type',
      options: [
        { value: 'yearly', label: 'Yearly' },
        { value: 'monthly', label: 'Monthly' },
        { value: 'hourly', label: 'Hourly' },
      ],
      section: 'Compensation',
    },

    // Skills Section
    {
      name: 'requiredSkills',
      type: 'tags',
      label: 'Required Skills',
      placeholder: 'Add required skills (press Enter to add)',
      required: true,
      description: 'Skills that are essential for this role',
      section: 'Skills & Requirements',
    },
    {
      name: 'preferredSkills',
      type: 'tags',
      label: 'Preferred Skills',
      placeholder: 'Add preferred skills (press Enter to add)',
      description: 'Skills that would be nice to have',
      section: 'Skills & Requirements',
    },
    {
      name: 'requirements',
      type: 'textarea',
      label: 'Job Requirements',
      placeholder: 'List the key requirements for this position',
      required: true,
      rows: 4,
      section: 'Skills & Requirements',
    },

    // Additional Details Section
    {
      name: 'responsibilities',
      type: 'textarea',
      label: 'Key Responsibilities',
      placeholder: 'Describe the main responsibilities of this role',
      rows: 4,
      section: 'Additional Details',
    },
    {
      name: 'qualifications',
      type: 'textarea',
      label: 'Qualifications',
      placeholder: 'List educational and professional qualifications',
      rows: 3,
      section: 'Additional Details',
    },
    {
      name: 'benefits',
      type: 'textarea',
      label: 'Benefits & Perks',
      placeholder: 'Describe the benefits and perks offered',
      rows: 3,
      section: 'Additional Details',
    },

    // Settings Section
    {
      name: 'status',
      type: 'select',
      label: 'Status',
      options: [
        { value: 'draft', label: 'Draft' },
        { value: 'published', label: 'Published' },
        { value: 'closed', label: 'Closed' },
        { value: 'archived', label: 'Archived' },
      ],
      section: 'Settings',
    },
    {
      name: 'priority',
      type: 'select',
      label: 'Priority',
      options: [
        { value: 'low', label: 'Low' },
        { value: 'medium', label: 'Medium' },
        { value: 'high', label: 'High' },
        { value: 'urgent', label: 'Urgent' },
      ],
      section: 'Settings',
    },
    {
      name: 'applicationDeadline',
      type: 'date',
      label: 'Application Deadline',
      section: 'Settings',
    },
    {
      name: 'maxApplications',
      type: 'number',
      label: 'Maximum Applications',
      placeholder: 'Enter maximum number of applications',
      min: 1,
      icon: <Users className="h-4 w-4" />,
      section: 'Settings',
    },
    {
      name: 'tags',
      type: 'tags',
      label: 'Tags',
      placeholder: 'Add tags for categorization (press Enter to add)',
      description: 'Tags help organize and filter jobs',
      section: 'Settings',
    },
  ], []);

  // Default values for form
  const defaultValues: Partial<JobFormData> = useMemo(() => {
    if (job) {
      return {
        title: job.title,
        description: job.description,
        department: job.department,
        location: job.location,
        remoteType: job.remoteType,
        employmentType: job.employmentType,
        experienceLevel: job.experienceLevel,
        requirements: job.requirements,
        responsibilities: job.responsibilities || '',
        qualifications: job.qualifications || '',
        benefits: job.benefits || '',
        salaryMin: job.salaryMin || undefined,
        salaryMax: job.salaryMax || undefined,
        currency: job.currency || 'USD',
        salaryType: job.salaryType || 'yearly',
        requiredSkills: job.requiredSkills || [],
        preferredSkills: job.preferredSkills || [],
        tags: job.tags || [],
        status: job.status || 'draft',
        priority: job.priority || 'medium',
        hiringManagerId: job.hiringManagerId || '',
        recruiterId: job.recruiterId || '',
        applicationDeadline: job.applicationDeadline || '',
        maxApplications: job.maxApplications || undefined,
      };
    }
    return {
      status: 'draft',
      priority: 'medium',
      currency: 'USD',
      salaryType: 'yearly',
      remoteType: 'on-site',
      employmentType: 'full-time',
      experienceLevel: 'mid',
      requiredSkills: [],
      preferredSkills: [],
      tags: [],
    };
  }, [job]);

  const handleSubmit = async (data: JobFormData) => {
    try {
      // Validate job data using type guards
      const validation = JobTypeGuards.validateJob({ ...data, id: job?.id || 'temp' } as Job);
      if (!validation.isValid) {
        toast.error(`Validation failed: ${validation.errors.join(', ')}`);
        return;
      }

      // Prepare data for submission
      const submitData = {
        ...data,
        // Convert empty strings to undefined for optional fields
        responsibilities: data.responsibilities || undefined,
        qualifications: data.qualifications || undefined,
        benefits: data.benefits || undefined,
        applicationDeadline: data.applicationDeadline || undefined,
        hiringManagerId: data.hiringManagerId || undefined,
        recruiterId: data.recruiterId || undefined,
        // Ensure arrays are not empty
        preferredSkills: data.preferredSkills?.length ? data.preferredSkills : undefined,
        tags: data.tags?.length ? data.tags : undefined,
      };

      await onSubmit(submitData);
      onClose();
      toast.success(
        isEditing 
          ? 'Job updated successfully' 
          : 'Job created successfully'
      );
    } catch (error) {
      console.error('Error submitting job:', error);
      toast.error(
        isEditing 
          ? 'Failed to update job' 
          : 'Failed to create job'
      );
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? 'Edit Job' : 'Create New Job'}
      description={
        isEditing 
          ? 'Update job information and requirements' 
          : 'Create a new job posting for your organization'
      }
      icon={<Briefcase className="h-5 w-5" />}
      size="xl"
      loading={loading}
    >
      <FormBuilder
        schema={jobSchema}
        fields={fieldConfig}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
        submitText={isEditing ? 'Update Job' : 'Create Job'}
        cancelText="Cancel"
        onCancel={onClose}
        loading={loading}
        sectioned={true}
        className="space-y-6"
      />
    </FormModal>
  );
};
