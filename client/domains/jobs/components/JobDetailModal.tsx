import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Building2,
  MapPin,
  DollarSign,
  Users,
  Calendar,
  Edit,
  Share2,
  Eye,
  Copy,
  ExternalLink,
  BarChart3,
} from "lucide-react";
import { Job, mockCandidates } from "@/domains/candidates/types";
import { toast } from "sonner";
import {
  JobDescription,
  JobDetails,
  JobRequirements,
  JobCandidates,
  JobAnalytics,
} from "./detail";

interface JobDetailModalProps {
  job: Job | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (job: Job) => void;
  onDuplicate: (job: Job) => void;
}

export const JobDetailModal = ({
  job,
  isOpen,
  onClose,
  onEdit,
  onDuplicate,
}: JobDetailModalProps) => {
  if (!job) return null;

  const getStatusColor = (status: Job["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "paused":
        return "bg-yellow-100 text-yellow-800";
      case "closed":
        return "bg-red-100 text-red-800";
      case "draft":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: Job["priority"]) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const jobCandidates = mockCandidates.filter((c) => c.jobId === job.id);
  const applicationsByStatus = {
    total: jobCandidates.length,
    sourced: jobCandidates.filter((c) => c.status === "sourced").length,
    applied: jobCandidates.filter((c) => c.status === "applied").length,
    screening: jobCandidates.filter((c) => c.status === "screening").length,
    interview: jobCandidates.filter((c) => c.status === "interview").length,
    offer: jobCandidates.filter((c) => c.status === "offer").length,
    hired: jobCandidates.filter((c) => c.status === "hired").length,
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(`${window.location.origin}/jobs/${job.id}`);
    toast.success("Job link copied to clipboard!");
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: job.title,
        text: `Check out this job opportunity: ${job.title} at our company`,
        url: `${window.location.origin}/jobs/${job.id}`,
      });
    } else {
      handleCopyLink();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Building2 className="w-5 h-5 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-bold">{job.title}</h2>
              <p className="text-muted-foreground font-normal">
                {job.department} • {job.location}
              </p>
            </div>
          </DialogTitle>
          <DialogDescription>
            View detailed information about this job posting and manage
            applications.
          </DialogDescription>
        </DialogHeader>

        <div className="flex gap-2 mb-6 hidden">
          <Button onClick={() => onEdit(job)} className="ai-button gap-2">
            <Edit className="w-4 h-4" />
            Edit Job
          </Button>
          <Button
            variant="outline"
            onClick={() => onDuplicate(job)}
            className="gap-2 rounded-xl"
          >
            <Copy className="w-4 h-4" />
            Duplicate
          </Button>
          <Button
            variant="outline"
            onClick={handleShare}
            className="gap-2 rounded-xl"
          >
            <Share2 className="w-4 h-4" />
            Share
          </Button>
          <Button
            variant="outline"
            onClick={handleCopyLink}
            className="gap-2 rounded-xl"
          >
            <ExternalLink className="w-4 h-4" />
            Copy Link
          </Button>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="candidates">
              Candidates ({applicationsByStatus.total})
            </TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Status and Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Job Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(job.status)}>
                      {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                    </Badge>
                    <Badge className={getPriorityColor(job.priority)}>
                      {job.priority} priority
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{job.location}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {job.salary?.range || " "}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {job.applicantCount || 0} candidates applied
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Eye className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {job.viewCount || 0} views
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Posted {new Date(job.postedDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Team</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-sm font-medium">Hiring Manager</p>
                    <p className="text-sm text-muted-foreground">
                      {job.hiringManager}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Department</p>
                    <p className="text-sm text-muted-foreground">
                      {job.department}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Job Type</p>
                    <p className="text-sm text-muted-foreground">
                      {job.type.charAt(0).toUpperCase() + job.type.slice(1)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Closing Date</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(job.closingDate).toLocaleDateString()}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Description */}
            <JobDescription
              job={{
                description: job.description,
              }}
            />
          </TabsContent>

          <TabsContent value="details" className="space-y-6">
            <JobRequirements
              job={{
                requiredSkills: job.skills,
                preferredSkills: undefined,
                requirements: job.requirements,
              }}
            />

            <JobDetails
              job={{
                department: job.department,
                type: job.type,
                experienceLevel: undefined,
                remote: undefined,
                deadline: job.closingDate,
                benefits: job.benefits,
              }}
            />
          </TabsContent>

          <TabsContent value="candidates" className="space-y-6">
            {/* Application Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-2xl font-bold text-primary">
                    {applicationsByStatus.total}
                  </p>
                  <p className="text-sm text-muted-foreground">Total</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-2xl font-bold text-blue-600">
                    {applicationsByStatus.applied}
                  </p>
                  <p className="text-sm text-muted-foreground">Applied</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-2xl font-bold text-purple-600">
                    {applicationsByStatus.interview}
                  </p>
                  <p className="text-sm text-muted-foreground">Interview</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {applicationsByStatus.hired}
                  </p>
                  <p className="text-sm text-muted-foreground">Hired</p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Candidates */}
            <JobCandidates
              candidates={jobCandidates.map((candidate) => ({
                id: candidate.id,
                name: candidate.name,
                email: candidate.email,
                position: candidate.position,
                experience: candidate.experience,
                status: candidate.status,
                appliedDate: candidate.appliedDate,
              }))}
              onViewAll={() => {
                console.log("View all candidates for job");
              }}
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <JobAnalytics
              analytics={{
                conversionRate:
                  jobCandidates.length > 0
                    ? Math.round(
                        (applicationsByStatus.hired / jobCandidates.length) *
                          100,
                      )
                    : 0,
                averageTimeToHire: 14,
                totalApplications: jobCandidates.length,
                hiredCount: applicationsByStatus.hired,
                viewCount: job.viewCount,
                applicantCount: job.applicantCount,
                postedDate: job.postedDate,
                closingDate: job.closingDate,
              }}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
