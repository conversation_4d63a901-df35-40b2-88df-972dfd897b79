/**
 * Lazy-loaded Jobs Components
 * Lazy loading configuration for jobs domain components
 */

import React from 'react';
import { withLazyLoading, ComponentLoadingFallbacks, createRetryableLazy } from '../../shared/utils/lazyLoading';

// Lazy load main components
export const LazyJobDetailContent = withLazyLoading(
  createRetryableLazy(() => import('./JobDetailContent')),
  {
    fallback: ComponentLoadingFallbacks.page,
  }
);

export const LazyAddEditJobModal = withLazyLoading(
  createRetryableLazy(() => import('./AddEditJobModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyJobCard = withLazyLoading(
  createRetryableLazy(() => import('./JobCard')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyJobTable = withLazyLoading(
  createRetryableLazy(() => import('./JobTable')),
  {
    fallback: ComponentLoadingFallbacks.table,
  }
);

// Lazy load detail subcomponents
export const LazyJobHeader = withLazyLoading(
  createRetryableLazy(() => import('./detail/JobHeader')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyJobMetrics = withLazyLoading(
  createRetryableLazy(() => import('./detail/JobMetrics')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyJobDescription = withLazyLoading(
  createRetryableLazy(() => import('./detail/JobDescription')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyJobDetails = withLazyLoading(
  createRetryableLazy(() => import('./detail/JobDetails')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyJobRequirements = withLazyLoading(
  createRetryableLazy(() => import('./detail/JobRequirements')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyJobCandidates = withLazyLoading(
  createRetryableLazy(() => import('./detail/JobCandidates')),
  {
    fallback: ComponentLoadingFallbacks.table,
  }
);

export const LazyJobAnalytics = withLazyLoading(
  createRetryableLazy(() => import('./detail/JobAnalytics')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

// Lazy load utility components
export const LazyBulkJobActions = withLazyLoading(
  createRetryableLazy(() => import('./utils/BulkJobActions')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyDeleteJobModal = withLazyLoading(
  createRetryableLazy(() => import('./utils/DeleteJobModal')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyUserSelector = withLazyLoading(
  createRetryableLazy(() => import('./utils/UserSelector')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyJobFilters = withLazyLoading(
  createRetryableLazy(() => import('./utils/JobFilters')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyJobStatusBadge = withLazyLoading(
  createRetryableLazy(() => import('./utils/JobStatusBadge')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

// Preload critical components
export const preloadJobComponents = () => {
  return Promise.all([
    import('./JobDetailContent'),
    import('./AddEditJobModal'),
    import('./JobCard'),
    import('./JobTable'),
  ]);
};

// Component map for dynamic loading
export const jobComponentMap = {
  JobDetailContent: LazyJobDetailContent,
  AddEditJobModal: LazyAddEditJobModal,
  JobCard: LazyJobCard,
  JobTable: LazyJobTable,
  JobHeader: LazyJobHeader,
  JobMetrics: LazyJobMetrics,
  JobDescription: LazyJobDescription,
  JobDetails: LazyJobDetails,
  JobRequirements: LazyJobRequirements,
  JobCandidates: LazyJobCandidates,
  JobAnalytics: LazyJobAnalytics,
  BulkJobActions: LazyBulkJobActions,
  DeleteJobModal: LazyDeleteJobModal,
  UserSelector: LazyUserSelector,
  JobFilters: LazyJobFilters,
  JobStatusBadge: LazyJobStatusBadge,
} as const;

export type JobComponentName = keyof typeof jobComponentMap;
