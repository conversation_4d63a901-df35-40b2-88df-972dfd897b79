/**
 * useDashboard Hook
 * Hook for dashboard configuration and widget management
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { dashboardService } from '../services';
import { 
  DashboardConfig, 
  DashboardWidget, 
  WidgetData 
} from '../types';
import { toast } from 'sonner';

// Query keys for React Query
export const dashboardQueryKeys = {
  all: ['dashboard'] as const,
  config: (userId?: string) => [...dashboardQueryKeys.all, 'config', userId] as const,
  widget: (widgetId: string) => [...dashboardQueryKeys.all, 'widget', widgetId] as const,
  widgetData: (widgetId: string, config: any) => [...dashboardQueryKeys.all, 'widgetData', widgetId, config] as const,
  availableWidgets: () => [...dashboardQueryKeys.all, 'availableWidgets'] as const,
};

export interface UseDashboardOptions {
  userId?: string;
  enabled?: boolean;
  refetchInterval?: number;
}

export interface UseDashboardResult {
  // Data
  config: DashboardConfig | null;
  availableWidgets: Array<{
    type: string;
    name: string;
    description: string;
    configSchema: any;
  }> | null;
  
  // Loading states
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  
  // Actions
  updateConfig: (updates: Partial<DashboardConfig>) => Promise<DashboardConfig>;
  addWidget: (widget: Omit<DashboardWidget, 'id'>) => Promise<DashboardWidget>;
  updateWidget: (widgetId: string, updates: Partial<DashboardWidget>) => Promise<DashboardWidget>;
  removeWidget: (widgetId: string) => Promise<void>;
  exportDashboard: (dashboardId: string) => Promise<Blob>;
  importDashboard: (file: File) => Promise<DashboardConfig>;
  refreshConfig: () => void;
}

/**
 * Main dashboard hook for configuration management
 */
export const useDashboard = (options: UseDashboardOptions = {}): UseDashboardResult => {
  const { userId, enabled = true, refetchInterval } = options;
  const queryClient = useQueryClient();

  // Dashboard configuration query
  const {
    data: config,
    isLoading: isConfigLoading,
    isError: isConfigError,
    error: configError,
  } = useQuery({
    queryKey: dashboardQueryKeys.config(userId),
    queryFn: () => dashboardService.getDashboardConfig(userId),
    enabled,
    refetchInterval,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Available widgets query
  const {
    data: availableWidgets,
    isLoading: isWidgetsLoading,
    isError: isWidgetsError,
    error: widgetsError,
  } = useQuery({
    queryKey: dashboardQueryKeys.availableWidgets(),
    queryFn: () => dashboardService.getAvailableWidgets(),
    enabled,
    staleTime: 30 * 60 * 1000, // 30 minutes - rarely changes
  });

  // Update configuration mutation
  const updateConfigMutation = useMutation({
    mutationFn: (updates: Partial<DashboardConfig>) =>
      dashboardService.updateDashboardConfig(updates),
    onSuccess: (updatedConfig) => {
      queryClient.setQueryData(dashboardQueryKeys.config(userId), updatedConfig);
      toast.success('Dashboard configuration updated');
    },
    onError: (error) => {
      console.error('Failed to update dashboard config:', error);
      toast.error('Failed to update dashboard configuration');
    },
  });

  // Add widget mutation
  const addWidgetMutation = useMutation({
    mutationFn: (widget: Omit<DashboardWidget, 'id'>) =>
      dashboardService.addWidget(widget),
    onSuccess: (newWidget) => {
      // Update the config with the new widget
      if (config) {
        const updatedConfig = {
          ...config,
          widgets: [...config.widgets, newWidget],
        };
        queryClient.setQueryData(dashboardQueryKeys.config(userId), updatedConfig);
      }
      toast.success('Widget added to dashboard');
    },
    onError: (error) => {
      console.error('Failed to add widget:', error);
      toast.error('Failed to add widget');
    },
  });

  // Update widget mutation
  const updateWidgetMutation = useMutation({
    mutationFn: ({ widgetId, updates }: { widgetId: string; updates: Partial<DashboardWidget> }) =>
      dashboardService.updateWidget(widgetId, updates),
    onSuccess: (updatedWidget) => {
      // Update the config with the updated widget
      if (config) {
        const updatedConfig = {
          ...config,
          widgets: config.widgets.map(w => w.id === updatedWidget.id ? updatedWidget : w),
        };
        queryClient.setQueryData(dashboardQueryKeys.config(userId), updatedConfig);
      }
      toast.success('Widget updated');
    },
    onError: (error) => {
      console.error('Failed to update widget:', error);
      toast.error('Failed to update widget');
    },
  });

  // Remove widget mutation
  const removeWidgetMutation = useMutation({
    mutationFn: (widgetId: string) => dashboardService.removeWidget(widgetId),
    onSuccess: (_, widgetId) => {
      // Update the config by removing the widget
      if (config) {
        const updatedConfig = {
          ...config,
          widgets: config.widgets.filter(w => w.id !== widgetId),
        };
        queryClient.setQueryData(dashboardQueryKeys.config(userId), updatedConfig);
      }
      toast.success('Widget removed from dashboard');
    },
    onError: (error) => {
      console.error('Failed to remove widget:', error);
      toast.error('Failed to remove widget');
    },
  });

  // Export dashboard mutation
  const exportDashboardMutation = useMutation({
    mutationFn: (dashboardId: string) => dashboardService.exportDashboard(dashboardId),
    onSuccess: (blob) => {
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'dashboard-config.json';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast.success('Dashboard configuration exported');
    },
    onError: (error) => {
      console.error('Failed to export dashboard:', error);
      toast.error('Failed to export dashboard configuration');
    },
  });

  // Import dashboard mutation
  const importDashboardMutation = useMutation({
    mutationFn: (file: File) => dashboardService.importDashboard(file),
    onSuccess: (importedConfig) => {
      queryClient.setQueryData(dashboardQueryKeys.config(userId), importedConfig);
      toast.success('Dashboard configuration imported');
    },
    onError: (error) => {
      console.error('Failed to import dashboard:', error);
      toast.error('Failed to import dashboard configuration');
    },
  });

  // Refresh configuration
  const refreshConfig = () => {
    queryClient.invalidateQueries({ queryKey: dashboardQueryKeys.config(userId) });
  };

  // Action functions
  const updateConfig = async (updates: Partial<DashboardConfig>): Promise<DashboardConfig> => {
    return updateConfigMutation.mutateAsync(updates);
  };

  const addWidget = async (widget: Omit<DashboardWidget, 'id'>): Promise<DashboardWidget> => {
    return addWidgetMutation.mutateAsync(widget);
  };

  const updateWidget = async (widgetId: string, updates: Partial<DashboardWidget>): Promise<DashboardWidget> => {
    return updateWidgetMutation.mutateAsync({ widgetId, updates });
  };

  const removeWidget = async (widgetId: string): Promise<void> => {
    return removeWidgetMutation.mutateAsync(widgetId);
  };

  const exportDashboard = async (dashboardId: string): Promise<Blob> => {
    return exportDashboardMutation.mutateAsync(dashboardId);
  };

  const importDashboard = async (file: File): Promise<DashboardConfig> => {
    return importDashboardMutation.mutateAsync(file);
  };

  // Combine loading states
  const isLoading = isConfigLoading || isWidgetsLoading;
  
  // Combine error states
  const isError = isConfigError || isWidgetsError;
  const error = configError || widgetsError;

  return {
    // Data
    config: config || null,
    availableWidgets: availableWidgets || null,
    
    // Loading states
    isLoading,
    isError,
    error,
    
    // Actions
    updateConfig,
    addWidget,
    updateWidget,
    removeWidget,
    exportDashboard,
    importDashboard,
    refreshConfig,
  };
};

/**
 * Hook for widget data
 */
export const useWidgetData = (
  widgetId: string, 
  config: any, 
  options: { enabled?: boolean; refetchInterval?: number } = {}
) => {
  const { enabled = true, refetchInterval = 5 * 60 * 1000 } = options; // 5 minutes default

  return useQuery({
    queryKey: dashboardQueryKeys.widgetData(widgetId, config),
    queryFn: () => dashboardService.getWidgetData(widgetId, config),
    enabled: enabled && !!widgetId,
    refetchInterval,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook for available widget types
 */
export const useAvailableWidgets = (options: { enabled?: boolean } = {}) => {
  const { enabled = true } = options;

  return useQuery({
    queryKey: dashboardQueryKeys.availableWidgets(),
    queryFn: () => dashboardService.getAvailableWidgets(),
    enabled,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};
