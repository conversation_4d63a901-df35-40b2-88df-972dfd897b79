/**
 * useAnalytics Hook
 * Main hook for analytics data management
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { analyticsService } from '../services';
import { 
  DashboardMetrics, 
  AnalyticsFilters, 
  RecruitmentMetrics,
  PerformanceMetrics,
  SourceMetrics,
  TeamMetrics 
} from '../types';
import { toast } from 'sonner';

// Query keys for React Query
export const analyticsQueryKeys = {
  all: ['analytics'] as const,
  dashboard: () => [...analyticsQueryKeys.all, 'dashboard'] as const,
  recruitment: (filters?: AnalyticsFilters) => [...analyticsQueryKeys.all, 'recruitment', filters] as const,
  performance: (filters?: AnalyticsFilters) => [...analyticsQueryKeys.all, 'performance', filters] as const,
  sources: (filters?: AnalyticsFilters) => [...analyticsQueryKeys.all, 'sources', filters] as const,
  team: (filters?: AnalyticsFilters) => [...analyticsQueryKeys.all, 'team', filters] as const,
  customReport: (config: any) => [...analyticsQueryKeys.all, 'custom', config] as const,
};

export interface UseAnalyticsOptions {
  filters?: AnalyticsFilters;
  enabled?: boolean;
  refetchInterval?: number;
}

export interface UseAnalyticsResult {
  // Data
  dashboardMetrics: DashboardMetrics | null;
  recruitmentMetrics: RecruitmentMetrics | null;
  performanceMetrics: PerformanceMetrics | null;
  sourceMetrics: SourceMetrics | null;
  teamMetrics: TeamMetrics | null;
  
  // Loading states
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  
  // Actions
  refreshData: () => void;
  exportData: (format: 'csv' | 'xlsx' | 'pdf', filters?: AnalyticsFilters) => Promise<Blob>;
}

/**
 * Main analytics hook for dashboard metrics
 */
export const useAnalytics = (options: UseAnalyticsOptions = {}): UseAnalyticsResult => {
  const { filters, enabled = true, refetchInterval } = options;
  const queryClient = useQueryClient();

  // Dashboard metrics query
  const {
    data: dashboardMetrics,
    isLoading: isDashboardLoading,
    isError: isDashboardError,
    error: dashboardError,
  } = useQuery({
    queryKey: analyticsQueryKeys.dashboard(),
    queryFn: () => analyticsService.getDashboardMetrics(),
    enabled,
    refetchInterval,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Recruitment metrics query
  const {
    data: recruitmentMetrics,
    isLoading: isRecruitmentLoading,
    isError: isRecruitmentError,
    error: recruitmentError,
  } = useQuery({
    queryKey: analyticsQueryKeys.recruitment(filters),
    queryFn: () => analyticsService.getRecruitmentMetrics(filters),
    enabled,
    refetchInterval,
    staleTime: 5 * 60 * 1000,
  });

  // Performance metrics query
  const {
    data: performanceMetrics,
    isLoading: isPerformanceLoading,
    isError: isPerformanceError,
    error: performanceError,
  } = useQuery({
    queryKey: analyticsQueryKeys.performance(filters),
    queryFn: () => analyticsService.getPerformanceMetrics(filters),
    enabled,
    refetchInterval,
    staleTime: 5 * 60 * 1000,
  });

  // Source metrics query
  const {
    data: sourceMetrics,
    isLoading: isSourceLoading,
    isError: isSourceError,
    error: sourceError,
  } = useQuery({
    queryKey: analyticsQueryKeys.sources(filters),
    queryFn: () => analyticsService.getSourceMetrics(filters),
    enabled,
    refetchInterval,
    staleTime: 5 * 60 * 1000,
  });

  // Team metrics query
  const {
    data: teamMetrics,
    isLoading: isTeamLoading,
    isError: isTeamError,
    error: teamError,
  } = useQuery({
    queryKey: analyticsQueryKeys.team(filters),
    queryFn: () => analyticsService.getTeamMetrics(filters),
    enabled,
    refetchInterval,
    staleTime: 5 * 60 * 1000,
  });

  // Export data mutation
  const exportDataMutation = useMutation({
    mutationFn: ({ format, filters }: { format: 'csv' | 'xlsx' | 'pdf'; filters?: AnalyticsFilters }) =>
      analyticsService.exportData(format, filters),
    onSuccess: (blob, { format }) => {
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `analytics-report.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast.success('Analytics data exported successfully');
    },
    onError: (error) => {
      console.error('Export failed:', error);
      toast.error('Failed to export analytics data');
    },
  });

  // Refresh all data
  const refreshData = () => {
    queryClient.invalidateQueries({ queryKey: analyticsQueryKeys.all });
  };

  // Export data function
  const exportData = async (format: 'csv' | 'xlsx' | 'pdf', filters?: AnalyticsFilters): Promise<Blob> => {
    return exportDataMutation.mutateAsync({ format, filters });
  };

  // Combine loading states
  const isLoading = isDashboardLoading || isRecruitmentLoading || isPerformanceLoading || isSourceLoading || isTeamLoading;
  
  // Combine error states
  const isError = isDashboardError || isRecruitmentError || isPerformanceError || isSourceError || isTeamError;
  const error = dashboardError || recruitmentError || performanceError || sourceError || teamError;

  return {
    // Data
    dashboardMetrics: dashboardMetrics || null,
    recruitmentMetrics: recruitmentMetrics || null,
    performanceMetrics: performanceMetrics || null,
    sourceMetrics: sourceMetrics || null,
    teamMetrics: teamMetrics || null,
    
    // Loading states
    isLoading,
    isError,
    error,
    
    // Actions
    refreshData,
    exportData,
  };
};

/**
 * Hook for dashboard metrics only
 */
export const useDashboardMetrics = (options: { enabled?: boolean; refetchInterval?: number } = {}) => {
  const { enabled = true, refetchInterval } = options;

  return useQuery({
    queryKey: analyticsQueryKeys.dashboard(),
    queryFn: () => analyticsService.getDashboardMetrics(),
    enabled,
    refetchInterval,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for recruitment metrics only
 */
export const useRecruitmentMetrics = (filters?: AnalyticsFilters, options: { enabled?: boolean } = {}) => {
  const { enabled = true } = options;

  return useQuery({
    queryKey: analyticsQueryKeys.recruitment(filters),
    queryFn: () => analyticsService.getRecruitmentMetrics(filters),
    enabled,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook for performance metrics only
 */
export const usePerformanceMetrics = (filters?: AnalyticsFilters, options: { enabled?: boolean } = {}) => {
  const { enabled = true } = options;

  return useQuery({
    queryKey: analyticsQueryKeys.performance(filters),
    queryFn: () => analyticsService.getPerformanceMetrics(filters),
    enabled,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook for source metrics only
 */
export const useSourceMetrics = (filters?: AnalyticsFilters, options: { enabled?: boolean } = {}) => {
  const { enabled = true } = options;

  return useQuery({
    queryKey: analyticsQueryKeys.sources(filters),
    queryFn: () => analyticsService.getSourceMetrics(filters),
    enabled,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook for team metrics only
 */
export const useTeamMetrics = (filters?: AnalyticsFilters, options: { enabled?: boolean } = {}) => {
  const { enabled = true } = options;

  return useQuery({
    queryKey: analyticsQueryKeys.team(filters),
    queryFn: () => analyticsService.getTeamMetrics(filters),
    enabled,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook for custom reports
 */
export const useCustomReport = (config: {
  metrics: string[];
  filters: AnalyticsFilters;
  groupBy?: string;
  dateRange: { start: string; end: string };
}, options: { enabled?: boolean } = {}) => {
  const { enabled = true } = options;

  return useQuery({
    queryKey: analyticsQueryKeys.customReport(config),
    queryFn: () => analyticsService.getCustomReport(config),
    enabled: enabled && config.metrics.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes for custom reports
  });
};
