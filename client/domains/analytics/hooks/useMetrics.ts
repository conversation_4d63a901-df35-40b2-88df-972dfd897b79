/**
 * useMetrics Hook
 * Specialized hook for metrics data and calculations
 */

import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { analyticsService } from '../services';
import { 
  DashboardMetrics, 
  AnalyticsFilters,
  MetricTrend,
  MetricComparison 
} from '../types';
import { analyticsQueryKeys } from './useAnalytics';

export interface UseMetricsOptions {
  filters?: AnalyticsFilters;
  enabled?: boolean;
  refetchInterval?: number;
  compareWith?: {
    period: 'previous_month' | 'previous_quarter' | 'previous_year';
    enabled: boolean;
  };
}

export interface UseMetricsResult {
  // Raw data
  metrics: DashboardMetrics | null;
  
  // Calculated metrics
  conversionRates: {
    applicationToInterview: number;
    interviewToOffer: number;
    offerToHire: number;
    overallConversion: number;
  } | null;
  
  trends: {
    candidates: MetricTrend;
    interviews: MetricTrend;
    hires: MetricTrend;
    timeToHire: MetricTrend;
    costPerHire: MetricTrend;
  } | null;
  
  comparisons: MetricComparison[] | null;
  
  // Key insights
  insights: {
    topPerforming: string[];
    needsAttention: string[];
    recommendations: string[];
  } | null;
  
  // Loading states
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  
  // Utility functions
  formatMetric: (value: number, type: 'number' | 'percentage' | 'currency' | 'days') => string;
  getTrendIcon: (trend: 'up' | 'down' | 'stable') => string;
  getTrendColor: (trend: 'up' | 'down' | 'stable', isPositive?: boolean) => string;
}

/**
 * Main metrics hook with calculations and insights
 */
export const useMetrics = (options: UseMetricsOptions = {}): UseMetricsResult => {
  const { filters, enabled = true, refetchInterval, compareWith } = options;

  // Fetch dashboard metrics
  const {
    data: metrics,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: analyticsQueryKeys.dashboard(),
    queryFn: () => analyticsService.getDashboardMetrics(),
    enabled,
    refetchInterval,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Calculate conversion rates
  const conversionRates = useMemo(() => {
    if (!metrics) return null;

    return {
      applicationToInterview: metrics.conversionRates.applicationToInterview,
      interviewToOffer: metrics.conversionRates.interviewToOffer,
      offerToHire: metrics.conversionRates.offerToHire,
      overallConversion: 
        (metrics.conversionRates.applicationToInterview / 100) *
        (metrics.conversionRates.interviewToOffer / 100) *
        (metrics.conversionRates.offerToHire / 100) * 100,
    };
  }, [metrics]);

  // Extract trends
  const trends = useMemo(() => {
    if (!metrics) return null;

    return {
      candidates: metrics.trends.candidates,
      interviews: metrics.trends.interviews,
      hires: metrics.trends.hires,
      timeToHire: metrics.timeToHire,
      costPerHire: metrics.costPerHire,
    };
  }, [metrics]);

  // Generate insights
  const insights = useMemo(() => {
    if (!metrics || !trends) return null;

    const topPerforming: string[] = [];
    const needsAttention: string[] = [];
    const recommendations: string[] = [];

    // Analyze trends for insights
    if (trends.hires.trend === 'up' && trends.hires.change > 20) {
      topPerforming.push('Hiring velocity is significantly up');
    }

    if (trends.candidates.trend === 'up' && trends.candidates.change > 15) {
      topPerforming.push('Strong candidate pipeline growth');
    }

    if (trends.timeToHire.trend === 'down') {
      topPerforming.push('Time to hire is improving');
    }

    if (trends.costPerHire.trend === 'down') {
      topPerforming.push('Cost per hire is decreasing');
    }

    // Identify areas needing attention
    if (conversionRates.applicationToInterview < 15) {
      needsAttention.push('Low application to interview conversion rate');
      recommendations.push('Review screening criteria and job descriptions');
    }

    if (conversionRates.interviewToOffer < 30) {
      needsAttention.push('Low interview to offer conversion rate');
      recommendations.push('Analyze interview feedback and improve candidate experience');
    }

    if (trends.timeToHire.change > 10 && trends.timeToHire.trend === 'up') {
      needsAttention.push('Time to hire is increasing');
      recommendations.push('Streamline interview process and improve scheduling');
    }

    if (trends.costPerHire.change > 15 && trends.costPerHire.trend === 'up') {
      needsAttention.push('Cost per hire is rising');
      recommendations.push('Optimize sourcing channels and reduce external recruiting costs');
    }

    return {
      topPerforming,
      needsAttention,
      recommendations,
    };
  }, [metrics, trends, conversionRates]);

  // Utility functions
  const formatMetric = (value: number, type: 'number' | 'percentage' | 'currency' | 'days'): string => {
    switch (type) {
      case 'number':
        return value.toLocaleString();
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
        }).format(value);
      case 'days':
        return `${value} ${value === 1 ? 'day' : 'days'}`;
      default:
        return value.toString();
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable'): string => {
    switch (trend) {
      case 'up':
        return '↗️';
      case 'down':
        return '↘️';
      case 'stable':
        return '→';
      default:
        return '→';
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable', isPositive = true): string => {
    if (trend === 'stable') return 'text-gray-600';
    
    if (isPositive) {
      return trend === 'up' ? 'text-green-600' : 'text-red-600';
    } else {
      return trend === 'up' ? 'text-red-600' : 'text-green-600';
    }
  };

  return {
    // Raw data
    metrics: metrics || null,
    
    // Calculated metrics
    conversionRates,
    trends,
    comparisons: null, // TODO: Implement comparisons when compareWith is provided
    
    // Key insights
    insights,
    
    // Loading states
    isLoading,
    isError,
    error,
    
    // Utility functions
    formatMetric,
    getTrendIcon,
    getTrendColor,
  };
};

/**
 * Hook for specific metric tracking
 */
export const useMetricTracking = (
  metricKey: string,
  options: { 
    enabled?: boolean; 
    refetchInterval?: number;
    threshold?: { min?: number; max?: number };
  } = {}
) => {
  const { enabled = true, refetchInterval, threshold } = options;
  const { metrics, isLoading, isError, error } = useMetrics({ enabled, refetchInterval });

  const metricValue = useMemo(() => {
    if (!metrics) return null;
    
    // Extract specific metric value based on key
    switch (metricKey) {
      case 'totalCandidates':
        return metrics.totalCandidates;
      case 'newApplications':
        return metrics.newApplications;
      case 'scheduledInterviews':
        return metrics.scheduledInterviews;
      case 'successfulHires':
        return metrics.successfulHires;
      case 'timeToHire':
        return metrics.timeToHire.average;
      case 'costPerHire':
        return metrics.costPerHire.average;
      default:
        return null;
    }
  }, [metrics, metricKey]);

  const isThresholdExceeded = useMemo(() => {
    if (!metricValue || !threshold) return false;
    
    if (threshold.min !== undefined && metricValue < threshold.min) return true;
    if (threshold.max !== undefined && metricValue > threshold.max) return true;
    
    return false;
  }, [metricValue, threshold]);

  return {
    value: metricValue,
    isLoading,
    isError,
    error,
    isThresholdExceeded,
    threshold,
  };
};

/**
 * Hook for metric comparisons
 */
export const useMetricComparison = (
  currentPeriod: { start: string; end: string },
  comparisonPeriod: { start: string; end: string },
  options: { enabled?: boolean } = {}
) => {
  const { enabled = true } = options;

  // This would typically fetch data for both periods and compare
  // For now, returning a placeholder structure
  return {
    current: null,
    comparison: null,
    difference: null,
    percentageChange: null,
    isLoading: false,
    isError: false,
    error: null,
  };
};
