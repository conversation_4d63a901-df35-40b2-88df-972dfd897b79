/**
 * Lazy-loaded Analytics Components
 * Lazy loading configuration for analytics domain components
 */

import React from 'react';
import { withLazyLoading, ComponentLoadingFallbacks, createRetryableLazy } from '../../shared/utils/lazyLoading';

// Lazy load dashboard components
export const LazyMetricCard = withLazyLoading(
  createRetryableLazy(() => import('./dashboard/MetricCard')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyPerformanceChartWidget = withLazyLoading(
  createRetryableLazy(() => import('./dashboard/PerformanceChartWidget')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyRecruitmentFunnelWidget = withLazyLoading(
  createRetryableLazy(() => import('./dashboard/RecruitmentFunnelWidget')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyTopCandidatesWidget = withLazyLoading(
  createRetryableLazy(() => import('./dashboard/TopCandidatesWidget')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyInterviewScheduleWidget = withLazyLoading(
  createRetryableLazy(() => import('./dashboard/InterviewScheduleWidget')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyTaskListWidget = withLazyLoading(
  createRetryableLazy(() => import('./dashboard/TaskListWidget')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

// Lazy load chart components
export const LazyBarChart = withLazyLoading(
  createRetryableLazy(() => import('./charts/BarChart')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyLineChart = withLazyLoading(
  createRetryableLazy(() => import('./charts/LineChart')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyPieChart = withLazyLoading(
  createRetryableLazy(() => import('./charts/PieChart')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyFunnelChart = withLazyLoading(
  createRetryableLazy(() => import('./charts/FunnelChart')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

export const LazyTrendChart = withLazyLoading(
  createRetryableLazy(() => import('./charts/TrendChart')),
  {
    fallback: ComponentLoadingFallbacks.card,
  }
);

// Lazy load report components
export const LazyReportBuilder = withLazyLoading(
  createRetryableLazy(() => import('./reports/ReportBuilder')),
  {
    fallback: ComponentLoadingFallbacks.page,
  }
);

export const LazyReportViewer = withLazyLoading(
  createRetryableLazy(() => import('./reports/ReportViewer')),
  {
    fallback: ComponentLoadingFallbacks.page,
  }
);

export const LazyReportExporter = withLazyLoading(
  createRetryableLazy(() => import('./reports/ReportExporter')),
  {
    fallback: ComponentLoadingFallbacks.modal,
  }
);

export const LazyScheduledReports = withLazyLoading(
  createRetryableLazy(() => import('./reports/ScheduledReports')),
  {
    fallback: ComponentLoadingFallbacks.table,
  }
);

// Lazy load filter components
export const LazyAnalyticsFilters = withLazyLoading(
  createRetryableLazy(() => import('./filters/AnalyticsFilters')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyDateRangeFilter = withLazyLoading(
  createRetryableLazy(() => import('./filters/DateRangeFilter')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

export const LazyMetricSelector = withLazyLoading(
  createRetryableLazy(() => import('./filters/MetricSelector')),
  {
    fallback: ComponentLoadingFallbacks.form,
  }
);

// Preload critical components
export const preloadAnalyticsComponents = () => {
  return Promise.all([
    import('./dashboard/MetricCard'),
    import('./dashboard/PerformanceChartWidget'),
    import('./dashboard/RecruitmentFunnelWidget'),
    import('./charts/BarChart'),
    import('./charts/LineChart'),
  ]);
};

// Component map for dynamic loading
export const analyticsComponentMap = {
  MetricCard: LazyMetricCard,
  PerformanceChartWidget: LazyPerformanceChartWidget,
  RecruitmentFunnelWidget: LazyRecruitmentFunnelWidget,
  TopCandidatesWidget: LazyTopCandidatesWidget,
  InterviewScheduleWidget: LazyInterviewScheduleWidget,
  TaskListWidget: LazyTaskListWidget,
  BarChart: LazyBarChart,
  LineChart: LazyLineChart,
  PieChart: LazyPieChart,
  FunnelChart: LazyFunnelChart,
  TrendChart: LazyTrendChart,
  ReportBuilder: LazyReportBuilder,
  ReportViewer: LazyReportViewer,
  ReportExporter: LazyReportExporter,
  ScheduledReports: LazyScheduledReports,
  AnalyticsFilters: LazyAnalyticsFilters,
  DateRangeFilter: LazyDateRangeFilter,
  MetricSelector: LazyMetricSelector,
} as const;

export type AnalyticsComponentName = keyof typeof analyticsComponentMap;
