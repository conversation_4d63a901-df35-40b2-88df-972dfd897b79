import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface MetricCardProps {
  title: string;
  value: string;
  change?: string;
  changeType?: "increase" | "decrease" | "neutral";
  icon: LucideIcon;
  className?: string;
}

export const MetricCard = ({
  title,
  value,
  change,
  changeType = "neutral",
  icon: Icon,
  className,
}: MetricCardProps) => {
  return (
    <div className={cn("metric-card group", className)}>
      {/* AI-inspired glow effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />

      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors duration-200 relative">
              <Icon className="w-5 h-5 text-primary" />
              <div className="absolute inset-0 bg-primary/20 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground mb-1">
                {title}
              </p>
              <p className="text-2xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">
                {value}
              </p>
            </div>
          </div>
          {change && (
            <div
              className={cn(
                "px-3 py-1.5 rounded-xl text-sm font-semibold flex items-center gap-1",
                {
                  "bg-emerald-100 text-emerald-700": changeType === "increase",
                  "bg-red-100 text-red-700": changeType === "decrease",
                  "bg-gray-100 text-gray-700": changeType === "neutral",
                },
              )}
            >
              {changeType === "increase" && "↗"}
              {changeType === "decrease" && "↘"}
              {change}
            </div>
          )}
        </div>

        {/* AI-inspired bottom accent */}
        <div className="h-1 bg-gradient-to-r from-primary/20 via-primary to-emerald-500 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300" />
      </div>
    </div>
  );
};
