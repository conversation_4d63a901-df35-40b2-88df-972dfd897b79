import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";
import { mockActivities, Activity } from "@/domains/candidates/types";
import { Loader2, AlertTriangle } from "lucide-react";

const getActivityBadge = (type: Activity["type"]) => {
  switch (type) {
    case "application":
      return <Badge variant="secondary">New Application</Badge>;
    case "interview":
      return (
        <Badge className="bg-blue-100 text-blue-800">Interview Scheduled</Badge>
      );
    case "offer":
      return (
        <Badge className="bg-purple-100 text-purple-800">Offer Sent</Badge>
      );
    case "hire":
      return <Badge className="bg-success/10 text-success">Hired</Badge>;
    case "reject":
      return <Badge variant="destructive">Rejected</Badge>;
    case "status_change":
      return <Badge variant="outline">Status Updated</Badge>;
    case "note":
      return <Badge variant="secondary">Note Added</Badge>;
    default:
      return <Badge variant="secondary">Update</Badge>;
  }
};

interface RecentActivityProps {
  activities?: any[];
  isLoading?: boolean;
  hasError?: boolean;
}

export const RecentActivity = ({
  activities,
  isLoading = false,
  hasError = false,
}: RecentActivityProps) => {
  // Use API data if available, otherwise fall back to mock data
  const activitiesData =
    activities && activities.length > 0 ? activities : mockActivities;

  // Sort activities by timestamp (most recent first)
  const sortedActivities = [...activitiesData].sort(
    (a, b) =>
      new Date(b.timestamp || b.createdAt || Date.now()).getTime() -
      new Date(a.timestamp || a.createdAt || Date.now()).getTime(),
  );

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="flex items-center gap-4 p-4 rounded-lg border"
          >
            <div className="w-10 h-10 bg-muted rounded-full animate-pulse" />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-24 h-4 bg-muted rounded animate-pulse" />
                <div className="w-16 h-5 bg-muted rounded animate-pulse" />
              </div>
              <div className="w-48 h-3 bg-muted rounded animate-pulse mb-1" />
              <div className="w-32 h-3 bg-muted rounded animate-pulse" />
            </div>
            <div className="w-16 h-4 bg-muted rounded animate-pulse" />
          </div>
        ))}
      </div>
    );
  }

  // Error state
  if (hasError) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <AlertTriangle className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">
            Không thể tải hoạt động gần đây
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {sortedActivities.slice(0, 6).map((activity, index) => (
        <div
          key={
            activity.id ||
            `activity-${index}-${activity.candidateName || "unknown"}`
          }
          className="flex items-center gap-4 p-4 rounded-lg border hover:bg-muted/30 transition-colors"
        >
          <Avatar className="h-10 w-10">
            <AvatarImage src={activity.candidateAvatar} />
            <AvatarFallback>
              {activity.candidateInitials ||
                (activity.candidateName
                  ? activity.candidateName
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                  : "U")}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <p className="font-medium">{activity.candidateName}</p>
              {getActivityBadge(activity.type)}
            </div>
            <p className="text-sm text-muted-foreground">
              {activity.description}
            </p>
            <p className="text-xs text-muted-foreground">{activity.jobTitle}</p>
          </div>

          <div className="text-right">
            <p className="text-xs text-muted-foreground">
              {formatDistanceToNow(
                new Date(
                  activity.timestamp || activity.createdAt || Date.now(),
                ),
                {
                  addSuffix: true,
                },
              )}
            </p>
            <p className="text-xs text-muted-foreground">
              {activity.performer || "Hệ thống"}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};
