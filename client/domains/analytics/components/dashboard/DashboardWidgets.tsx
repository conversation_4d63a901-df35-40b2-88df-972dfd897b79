import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import {
  TrendingUp,
  TrendingDown,
  Users,
  Briefcase,
  Clock,
  Target,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  Calendar,
  CheckCircle,
  AlertCircle,
  Eye,
} from "lucide-react";
import { Link } from "react-router-dom";

// Performance Chart Widget
export const PerformanceChartWidget = ({
  title,
  data,
  trend,
  period = "7 days",
}: {
  title: string;
  data: Array<{ label: string; value: number; change?: number }>;
  trend: "up" | "down" | "neutral";
  period?: string;
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case "up":
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case "down":
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <MoreHorizontal className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case "up":
        return "text-green-600 bg-green-50";
      case "down":
        return "text-red-600 bg-red-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">{title}</CardTitle>
          <div
            className={`flex items-center gap-1 px-2 py-1 rounded-md ${getTrendColor()}`}
          >
            {getTrendIcon()}
            <span className="text-xs font-medium">{period}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {data.map((item, index) => (
          <div key={index} className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{item.label}</span>
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold">{item.value}</span>
                {item.change !== undefined && (
                  <span
                    className={`text-xs flex items-center gap-1 ${
                      item.change > 0
                        ? "text-green-600"
                        : item.change < 0
                          ? "text-red-600"
                          : "text-gray-600"
                    }`}
                  >
                    {item.change > 0 && <ArrowUpRight className="w-3 h-3" />}
                    {item.change < 0 && <ArrowDownRight className="w-3 h-3" />}
                    {Math.abs(item.change)}%
                  </span>
                )}
              </div>
            </div>
            <Progress
              value={(item.value / Math.max(...data.map((d) => d.value))) * 100}
              className="h-2"
            />
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

// Recruitment Funnel Widget
export const RecruitmentFunnelWidget = ({
  stages,
}: {
  stages: Array<{
    name: string;
    count: number;
    percentage: number;
    color: string;
  }>;
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="w-5 h-5" />
          Phễu Tuyển Dụng
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {stages.map((stage, index) => (
            <div key={index} className="relative">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">{stage.name}</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-bold">{stage.count}</span>
                  <span className="text-xs text-muted-foreground">
                    ({stage.percentage.toFixed(1)}%)
                  </span>
                </div>
              </div>
              <div className="relative">
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full ${stage.color} transition-all duration-500`}
                    style={{ width: `${stage.percentage}%` }}
                  />
                </div>
                {index < stages.length - 1 && (
                  <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-white border-2 border-gray-300 rounded-full" />
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Top Candidates Widget
export const TopCandidatesWidget = ({
  candidates,
}: {
  candidates: Array<{
    id: string;
    name: string;
    position: string;
    score: number;
    status: string;
    avatar?: string;
  }>;
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "interview":
        return "bg-blue-100 text-blue-800";
      case "offer":
        return "bg-purple-100 text-purple-800";
      case "hired":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Top Ứng Viên
          </CardTitle>
          <Button variant="ghost" size="sm" asChild>
            <Link to="/candidates">
              Xem tất cả
              <ArrowUpRight className="w-4 h-4 ml-1" />
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {candidates.map((candidate, index) => (
            <div
              key={candidate.id}
              className="flex items-center gap-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors group"
            >
              <div className="flex items-center gap-3 flex-1">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-bold text-primary">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm">{candidate.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {candidate.position}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-right">
                  <p className="text-sm font-bold">{candidate.score}%</p>
                  <Badge
                    variant="secondary"
                    className={getStatusColor(candidate.status)}
                  >
                    {candidate.status}
                  </Badge>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  asChild
                >
                  <Link to={`/candidates/detail/${candidate.id}`}>
                    <Eye className="w-4 h-4" />
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Interview Schedule Widget
export const InterviewScheduleWidget = ({
  interviews,
}: {
  interviews: Array<{
    id: string;
    candidateName: string;
    jobTitle: string;
    time: string;
    date: string;
    type: string;
    status: string;
  }>;
}) => {
  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "video":
        return "🎥";
      case "phone":
        return "📞";
      case "onsite":
        return "🏢";
      default:
        return "💬";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Lịch Phỏng Vấn Hôm Nay
          </CardTitle>
          <Button variant="ghost" size="sm" asChild>
            <Link to="/calendar">
              Xem lịch
              <ArrowUpRight className="w-4 h-4 ml-1" />
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {interviews.length > 0 ? (
          <div className="space-y-3">
            {interviews.map((interview) => (
              <div
                key={interview.id}
                className="flex items-center gap-3 p-3 rounded-lg border"
              >
                <div className="text-xl">{getTypeIcon(interview.type)}</div>
                <div className="flex-1">
                  <p className="font-medium text-sm">
                    {interview.candidateName}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {interview.jobTitle}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{interview.time}</p>
                  <Badge
                    variant="secondary"
                    className={getStatusColor(interview.status)}
                  >
                    {interview.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">Không có phỏng vấn nào hôm nay</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Task List Widget
export const TaskListWidget = ({
  tasks,
}: {
  tasks: Array<{
    id: string;
    title: string;
    description: string;
    priority: "high" | "medium" | "low";
    completed: boolean;
    dueDate?: string;
  }>;
}) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="w-5 h-5" />
          Nhiệm Vụ Hôm Nay
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {tasks.map((task) => (
            <div
              key={task.id}
              className={`flex items-start gap-3 p-3 rounded-lg border transition-all ${
                task.completed ? "bg-muted/50 opacity-60" : "hover:bg-muted/30"
              }`}
            >
              <div className="mt-1">
                {task.completed ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <div className="w-4 h-4 border-2 border-gray-300 rounded-full" />
                )}
              </div>
              <div className="flex-1">
                <p
                  className={`text-sm font-medium ${
                    task.completed ? "line-through" : ""
                  }`}
                >
                  {task.title}
                </p>
                <p className="text-xs text-muted-foreground">
                  {task.description}
                </p>
                {task.dueDate && (
                  <p className="text-xs text-muted-foreground mt-1">
                    <Clock className="w-3 h-3 inline mr-1" />
                    {new Date(task.dueDate).toLocaleDateString()}
                  </p>
                )}
              </div>
              <Badge
                variant="secondary"
                className={getPriorityColor(task.priority)}
              >
                {task.priority}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
