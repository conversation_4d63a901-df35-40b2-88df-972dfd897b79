/**
 * Dashboard Service
 * Handles dashboard-specific data and widget management
 */

import { BaseService } from '@/core/api/BaseService';
import { 
  DashboardConfig, 
  DashboardWidget, 
  WidgetData,
  DashboardMetrics 
} from '../types';

export class DashboardService extends BaseService {
  /**
   * Get dashboard configuration for user
   */
  async getDashboardConfig(userId?: string): Promise<DashboardConfig> {
    // Mock implementation - replace with actual API call
    return {
      id: 'dashboard-1',
      userId: userId || 'current-user',
      name: 'Default Dashboard',
      layout: 'grid',
      widgets: [
        {
          id: 'metrics-overview',
          type: 'metrics',
          title: 'Key Metrics',
          position: { x: 0, y: 0, w: 12, h: 4 },
          config: {
            metrics: ['totalCandidates', 'newApplications', 'scheduledInterviews', 'successfulHires'],
            showTrends: true,
          },
        },
        {
          id: 'recruitment-funnel',
          type: 'funnel',
          title: 'Recruitment Funnel',
          position: { x: 0, y: 4, w: 6, h: 6 },
          config: {
            stages: ['applied', 'screening', 'interview', 'offer', 'hired'],
            showConversion: true,
          },
        },
        {
          id: 'source-performance',
          type: 'chart',
          title: 'Source Performance',
          position: { x: 6, y: 4, w: 6, h: 6 },
          config: {
            chartType: 'bar',
            metric: 'hires',
            groupBy: 'source',
          },
        },
        {
          id: 'recent-activity',
          type: 'activity',
          title: 'Recent Activity',
          position: { x: 0, y: 10, w: 8, h: 6 },
          config: {
            limit: 10,
            types: ['interview', 'hire', 'application'],
          },
        },
        {
          id: 'quick-actions',
          type: 'actions',
          title: 'Quick Actions',
          position: { x: 8, y: 10, w: 4, h: 6 },
          config: {
            actions: ['schedule-interview', 'add-candidate', 'post-job'],
          },
        },
      ],
      settings: {
        refreshInterval: 300000, // 5 minutes
        theme: 'light',
        autoRefresh: true,
      },
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z',
    };
  }

  /**
   * Update dashboard configuration
   */
  async updateDashboardConfig(config: Partial<DashboardConfig>): Promise<DashboardConfig> {
    // Mock implementation - replace with actual API call
    return {
      ...await this.getDashboardConfig(),
      ...config,
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * Get widget data
   */
  async getWidgetData(widgetId: string, config: any): Promise<WidgetData> {
    // Mock implementation - replace with actual API call
    switch (widgetId) {
      case 'metrics-overview':
        return {
          type: 'metrics',
          data: {
            totalCandidates: { value: 1247, change: 7.9, trend: 'up' },
            newApplications: { value: 89, change: 12.5, trend: 'up' },
            scheduledInterviews: { value: 23, change: -5.2, trend: 'down' },
            successfulHires: { value: 12, change: 50.0, trend: 'up' },
          },
          lastUpdated: new Date().toISOString(),
        };

      case 'recruitment-funnel':
        return {
          type: 'funnel',
          data: [
            { stage: 'Applied', count: 1247, percentage: 100 },
            { stage: 'Screening', count: 623, percentage: 50 },
            { stage: 'Interview', count: 231, percentage: 18.5 },
            { stage: 'Offer', count: 23, percentage: 1.8 },
            { stage: 'Hired', count: 18, percentage: 1.4 },
          ],
          lastUpdated: new Date().toISOString(),
        };

      case 'source-performance':
        return {
          type: 'chart',
          data: {
            labels: ['LinkedIn', 'Indeed', 'Referral', 'Website'],
            datasets: [
              {
                label: 'Hires',
                data: [12, 4, 8, 3],
                backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],
              },
            ],
          },
          lastUpdated: new Date().toISOString(),
        };

      case 'recent-activity':
        return {
          type: 'activity',
          data: [
            {
              id: '1',
              type: 'interview',
              title: 'Interview scheduled with John Doe',
              timestamp: '2024-01-15T10:00:00Z',
              user: 'Jane Smith',
            },
            {
              id: '2',
              type: 'hire',
              title: 'Sarah Wilson hired for Frontend Developer',
              timestamp: '2024-01-15T09:30:00Z',
              user: 'Bob Johnson',
            },
            {
              id: '3',
              type: 'application',
              title: 'New application for Backend Developer',
              timestamp: '2024-01-15T09:15:00Z',
              user: 'System',
            },
          ],
          lastUpdated: new Date().toISOString(),
        };

      case 'quick-actions':
        return {
          type: 'actions',
          data: [
            {
              id: 'schedule-interview',
              title: 'Schedule Interview',
              icon: 'calendar',
              url: '/interviews/new',
              count: 5, // pending interviews to schedule
            },
            {
              id: 'add-candidate',
              title: 'Add Candidate',
              icon: 'user-plus',
              url: '/candidates/new',
            },
            {
              id: 'post-job',
              title: 'Post Job',
              icon: 'briefcase',
              url: '/jobs/new',
            },
          ],
          lastUpdated: new Date().toISOString(),
        };

      default:
        return {
          type: 'unknown',
          data: null,
          lastUpdated: new Date().toISOString(),
        };
    }
  }

  /**
   * Add widget to dashboard
   */
  async addWidget(widget: Omit<DashboardWidget, 'id'>): Promise<DashboardWidget> {
    // Mock implementation - replace with actual API call
    return {
      ...widget,
      id: `widget-${Date.now()}`,
    };
  }

  /**
   * Update widget configuration
   */
  async updateWidget(widgetId: string, updates: Partial<DashboardWidget>): Promise<DashboardWidget> {
    // Mock implementation - replace with actual API call
    const config = await this.getDashboardConfig();
    const widget = config.widgets.find(w => w.id === widgetId);
    
    if (!widget) {
      throw new Error(`Widget ${widgetId} not found`);
    }

    return {
      ...widget,
      ...updates,
    };
  }

  /**
   * Remove widget from dashboard
   */
  async removeWidget(widgetId: string): Promise<void> {
    // Mock implementation - replace with actual API call
    console.log(`Removing widget ${widgetId}`);
  }

  /**
   * Get available widget types
   */
  async getAvailableWidgets(): Promise<Array<{
    type: string;
    name: string;
    description: string;
    configSchema: any;
  }>> {
    // Mock implementation - replace with actual API call
    return [
      {
        type: 'metrics',
        name: 'Metrics Overview',
        description: 'Display key recruitment metrics with trends',
        configSchema: {
          metrics: { type: 'array', items: { type: 'string' } },
          showTrends: { type: 'boolean' },
        },
      },
      {
        type: 'funnel',
        name: 'Recruitment Funnel',
        description: 'Visualize candidate progression through stages',
        configSchema: {
          stages: { type: 'array', items: { type: 'string' } },
          showConversion: { type: 'boolean' },
        },
      },
      {
        type: 'chart',
        name: 'Chart Widget',
        description: 'Display data in various chart formats',
        configSchema: {
          chartType: { type: 'string', enum: ['bar', 'line', 'pie', 'doughnut'] },
          metric: { type: 'string' },
          groupBy: { type: 'string' },
        },
      },
      {
        type: 'activity',
        name: 'Recent Activity',
        description: 'Show recent system activities and events',
        configSchema: {
          limit: { type: 'number', minimum: 1, maximum: 50 },
          types: { type: 'array', items: { type: 'string' } },
        },
      },
      {
        type: 'actions',
        name: 'Quick Actions',
        description: 'Provide quick access to common actions',
        configSchema: {
          actions: { type: 'array', items: { type: 'string' } },
        },
      },
    ];
  }

  /**
   * Export dashboard configuration
   */
  async exportDashboard(dashboardId: string): Promise<Blob> {
    // Mock implementation - replace with actual API call
    const config = await this.getDashboardConfig();
    const data = JSON.stringify(config, null, 2);
    return new Blob([data], { type: 'application/json' });
  }

  /**
   * Import dashboard configuration
   */
  async importDashboard(file: File): Promise<DashboardConfig> {
    // Mock implementation - replace with actual API call
    const text = await file.text();
    const config = JSON.parse(text);
    return this.updateDashboardConfig(config);
  }
}

export const dashboardService = new DashboardService();
