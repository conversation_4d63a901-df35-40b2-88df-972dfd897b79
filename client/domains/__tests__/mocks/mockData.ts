/**
 * Mock Data Utilities
 * Centralized mock data for domain tests
 */

import { CalendarEvent } from '../../calendar/types';
import { Interview } from '../../interviews/types';
import { Candidate } from '../../candidates/types';
import { Job } from '../../jobs/types';

// Calendar Mock Data
export const mockCalendarEvents: CalendarEvent[] = [
  {
    id: '1',
    title: 'Interview with <PERSON>',
    description: 'Technical interview for Frontend Developer position',
    type: 'interview',
    status: 'scheduled',
    priority: 'high',
    startDate: '2024-01-15T10:00:00Z',
    endDate: '2024-01-15T11:00:00Z',
    allDay: false,
    timezone: 'UTC',
    location: 'Conference Room A',
    isVirtual: false,
    organizerId: 'user1',
    organizerName: '<PERSON>',
    organizerEmail: '<EMAIL>',
    attendees: [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        role: 'required',
        status: 'pending',
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        role: 'organizer',
        status: 'accepted',
      },
    ],
    interviewId: 'interview1',
    candidateId: 'candidate1',
    jobId: 'job1',
    color: '#3b82f6',
    tags: ['technical', 'frontend'],
    isRecurring: false,
    reminders: [
      {
        type: 'email',
        minutes: 60,
      },
    ],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: '2',
    title: 'Team Meeting',
    description: 'Weekly team sync meeting',
    type: 'meeting',
    status: 'scheduled',
    priority: 'medium',
    startDate: '2024-01-16T14:00:00Z',
    endDate: '2024-01-16T15:00:00Z',
    allDay: false,
    timezone: 'UTC',
    isVirtual: true,
    meetingUrl: 'https://meet.example.com/123',
    organizerId: 'user2',
    organizerName: 'Bob Wilson',
    organizerEmail: '<EMAIL>',
    attendees: [
      {
        name: 'Team Members',
        email: '<EMAIL>',
        role: 'required',
        status: 'accepted',
      },
    ],
    color: '#10b981',
    tags: ['meeting', 'team'],
    isRecurring: true,
    reminders: [],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
];

// Interview Mock Data
export const mockInterviews: Interview[] = [
  {
    id: '1',
    title: 'Technical Interview',
    description: 'Frontend developer technical assessment',
    type: 'technical',
    round: 'first',
    status: 'scheduled',
    candidateId: 'candidate1',
    candidateName: 'John Doe',
    candidateEmail: '<EMAIL>',
    jobId: 'job1',
    jobTitle: 'Frontend Developer',
    scheduledAt: '2024-01-15T10:00:00Z',
    duration: 60,
    timezone: 'UTC',
    location: 'Conference Room A',
    interviewers: [
      {
        userId: 'user1',
        name: 'Jane Smith',
        email: '<EMAIL>',
        isPrimary: true,
        confirmed: true,
      },
      {
        userId: 'user2',
        name: 'Bob Wilson',
        email: '<EMAIL>',
        isPrimary: false,
        confirmed: true,
      },
    ],
    feedback: [],
    notes: 'Technical assessment focusing on React and TypeScript skills',
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: '2',
    title: 'HR Interview',
    description: 'Cultural fit and behavioral assessment',
    type: 'hr',
    round: 'second',
    status: 'completed',
    candidateId: 'candidate2',
    candidateName: 'Jane Wilson',
    candidateEmail: '<EMAIL>',
    jobId: 'job2',
    jobTitle: 'Backend Developer',
    scheduledAt: '2024-01-14T14:00:00Z',
    duration: 45,
    timezone: 'UTC',
    meetingUrl: 'https://meet.example.com/456',
    interviewers: [
      {
        userId: 'user3',
        name: 'Alice Johnson',
        email: '<EMAIL>',
        isPrimary: true,
        confirmed: true,
      },
    ],
    feedback: [
      {
        interviewerId: 'user3',
        rating: 4,
        comments: 'Good cultural fit, strong communication skills',
        createdAt: '2024-01-14T15:00:00Z',
      },
    ],
    notes: 'Behavioral questions and company culture discussion',
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-14T15:00:00Z',
  },
  {
    id: '3',
    title: 'Final Interview',
    description: 'Final round with leadership team',
    type: 'final',
    round: 'final',
    status: 'cancelled',
    candidateId: 'candidate3',
    candidateName: 'Mike Brown',
    candidateEmail: '<EMAIL>',
    jobId: 'job1',
    jobTitle: 'Frontend Developer',
    scheduledAt: '2024-01-17T16:00:00Z',
    duration: 90,
    timezone: 'UTC',
    location: 'Executive Conference Room',
    interviewers: [
      {
        userId: 'user4',
        name: 'David Lee',
        email: '<EMAIL>',
        isPrimary: true,
        confirmed: false,
      },
    ],
    feedback: [],
    notes: 'Cancelled due to candidate accepting another offer',
    createdAt: '2024-01-12T00:00:00Z',
    updatedAt: '2024-01-16T00:00:00Z',
  },
];

// Candidate Mock Data
export const mockCandidates: Candidate[] = [
  {
    id: 'candidate1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '******-0123',
    title: 'Frontend Developer',
    experience: 3,
    skills: ['React', 'TypeScript', 'JavaScript', 'CSS', 'HTML'],
    status: 'interview',
    source: 'linkedin',
    appliedAt: '2024-01-05T00:00:00Z',
    resumeUrl: 'https://example.com/resumes/john-doe.pdf',
    portfolioUrl: 'https://johndoe.dev',
    linkedinUrl: 'https://linkedin.com/in/johndoe',
    githubUrl: 'https://github.com/johndoe',
    location: 'San Francisco, CA',
    expectedSalary: {
      min: 80000,
      max: 100000,
      currency: 'USD',
    },
    availability: 'immediate',
    notes: 'Strong React skills, good portfolio',
    tags: ['frontend', 'react', 'typescript'],
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: 'candidate2',
    firstName: 'Jane',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phone: '******-0124',
    title: 'Backend Developer',
    experience: 5,
    skills: ['Node.js', 'Python', 'PostgreSQL', 'Docker', 'AWS'],
    status: 'offer',
    source: 'referral',
    appliedAt: '2024-01-03T00:00:00Z',
    resumeUrl: 'https://example.com/resumes/jane-wilson.pdf',
    location: 'New York, NY',
    expectedSalary: {
      min: 100000,
      max: 120000,
      currency: 'USD',
    },
    availability: '2_weeks',
    notes: 'Excellent backend experience, strong system design skills',
    tags: ['backend', 'nodejs', 'python'],
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-14T00:00:00Z',
  },
];

// Job Mock Data
export const mockJobs: Job[] = [
  {
    id: 'job1',
    title: 'Frontend Developer',
    description: 'We are looking for a skilled Frontend Developer to join our team...',
    requirements: [
      '3+ years of React experience',
      'Strong TypeScript skills',
      'Experience with modern CSS frameworks',
      'Knowledge of testing frameworks',
    ],
    responsibilities: [
      'Develop user-facing features using React',
      'Collaborate with design team on UI/UX',
      'Write clean, maintainable code',
      'Participate in code reviews',
    ],
    department: 'Engineering',
    location: 'San Francisco, CA',
    employmentType: 'full-time',
    salaryRange: {
      min: 80000,
      max: 120000,
      currency: 'USD',
    },
    benefits: [
      'Health insurance',
      'Dental insurance',
      '401k matching',
      'Flexible PTO',
      'Remote work options',
    ],
    skills: ['React', 'TypeScript', 'JavaScript', 'CSS', 'HTML'],
    status: 'active',
    priority: 'high',
    openings: 2,
    applicationDeadline: '2024-02-15T23:59:59Z',
    startDate: '2024-03-01T00:00:00Z',
    hiringManagerId: 'user1',
    hiringManagerName: 'Jane Smith',
    tags: ['frontend', 'react', 'typescript'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: 'job2',
    title: 'Backend Developer',
    description: 'Join our backend team to build scalable server-side applications...',
    requirements: [
      '4+ years of backend development experience',
      'Strong knowledge of Node.js or Python',
      'Database design and optimization skills',
      'Experience with cloud platforms (AWS/GCP)',
    ],
    responsibilities: [
      'Design and implement APIs',
      'Optimize database performance',
      'Ensure system scalability and reliability',
      'Mentor junior developers',
    ],
    department: 'Engineering',
    location: 'Remote',
    employmentType: 'full-time',
    salaryRange: {
      min: 100000,
      max: 140000,
      currency: 'USD',
    },
    benefits: [
      'Health insurance',
      'Dental insurance',
      '401k matching',
      'Unlimited PTO',
      'Home office stipend',
    ],
    skills: ['Node.js', 'Python', 'PostgreSQL', 'Docker', 'AWS'],
    status: 'active',
    priority: 'medium',
    openings: 1,
    applicationDeadline: '2024-02-28T23:59:59Z',
    startDate: '2024-03-15T00:00:00Z',
    hiringManagerId: 'user2',
    hiringManagerName: 'Bob Wilson',
    tags: ['backend', 'nodejs', 'python'],
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z',
  },
];

// Factory functions for creating test data
export const createMockCalendarEvent = (overrides: Partial<CalendarEvent> = {}): CalendarEvent => ({
  ...mockCalendarEvents[0],
  ...overrides,
});

export const createMockInterview = (overrides: Partial<Interview> = {}): Interview => ({
  ...mockInterviews[0],
  ...overrides,
});

export const createMockCandidate = (overrides: Partial<Candidate> = {}): Candidate => ({
  ...mockCandidates[0],
  ...overrides,
});

export const createMockJob = (overrides: Partial<Job> = {}): Job => ({
  ...mockJobs[0],
  ...overrides,
});

// Test utilities
export const createQueryClientWrapper = () => {
  const { QueryClient, QueryClientProvider } = require('@tanstack/react-query');
  
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

// Mock API responses
export const createMockApiResponse = <T>(data: T, success = true) => ({
  data,
  success,
  message: success ? 'Success' : 'Error',
});

export const createMockApiError = (message = 'API Error') => {
  const error = new Error(message);
  (error as any).response = {
    status: 500,
    data: {
      success: false,
      message,
      errors: [message],
    },
  };
  return error;
};
