/**
 * Test Setup Configuration
 * Global test setup for domain tests
 */

import { vi } from 'vitest';
import '@testing-library/jest-dom';

// Mock React Router
vi.mock('react-router-dom', () => ({
  Link: ({ children, to, ...props }: any) => (
    <a href={to} {...props}>
      {children}
    </a>
  ),
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/' }),
  useParams: () => ({}),
}));

// Mock toast notifications
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
}));

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date: Date, formatStr: string) => {
    if (formatStr === 'PPP p') return 'January 15, 2024 at 10:00 AM';
    if (formatStr === 'PPP') return 'January 15, 2024';
    if (formatStr === 'p') return '10:00 AM';
    if (formatStr === 'yyyy-MM-dd') return '2024-01-15';
    return date.toISOString();
  }),
  formatDistanceToNow: vi.fn(() => '2 hours ago'),
  isToday: vi.fn(() => false),
  isTomorrow: vi.fn(() => false),
  isYesterday: vi.fn(() => false),
  startOfDay: vi.fn((date: Date) => date),
  endOfDay: vi.fn((date: Date) => date),
  addDays: vi.fn((date: Date, days: number) => new Date(date.getTime() + days * 24 * 60 * 60 * 1000)),
  subDays: vi.fn((date: Date, days: number) => new Date(date.getTime() - days * 24 * 60 * 60 * 1000)),
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => {
  const createMockIcon = (name: string) => {
    const MockIcon = (props: any) => (
      <span data-testid={`${name.toLowerCase()}-icon`} {...props}>
        {name}
      </span>
    );
    MockIcon.displayName = name;
    return MockIcon;
  };

  return {
    Calendar: createMockIcon('Calendar'),
    Clock: createMockIcon('Clock'),
    MapPin: createMockIcon('MapPin'),
    Video: createMockIcon('Video'),
    User: createMockIcon('User'),
    Users: createMockIcon('Users'),
    Edit: createMockIcon('Edit'),
    Trash2: createMockIcon('Trash2'),
    MoreVertical: createMockIcon('MoreVertical'),
    Plus: createMockIcon('Plus'),
    Search: createMockIcon('Search'),
    Filter: createMockIcon('Filter'),
    Download: createMockIcon('Download'),
    RefreshCw: createMockIcon('RefreshCw'),
    ChevronLeft: createMockIcon('ChevronLeft'),
    ChevronRight: createMockIcon('ChevronRight'),
    ChevronDown: createMockIcon('ChevronDown'),
    ChevronUp: createMockIcon('ChevronUp'),
    X: createMockIcon('X'),
    Check: createMockIcon('Check'),
    AlertTriangle: createMockIcon('AlertTriangle'),
    Info: createMockIcon('Info'),
    CheckCircle: createMockIcon('CheckCircle'),
    XCircle: createMockIcon('XCircle'),
    Loader2: createMockIcon('Loader2'),
    Eye: createMockIcon('Eye'),
    EyeOff: createMockIcon('EyeOff'),
    Settings: createMockIcon('Settings'),
    MoreHorizontal: createMockIcon('MoreHorizontal'),
    ArrowLeft: createMockIcon('ArrowLeft'),
    ArrowRight: createMockIcon('ArrowRight'),
    ArrowUp: createMockIcon('ArrowUp'),
    ArrowDown: createMockIcon('ArrowDown'),
    TrendingUp: createMockIcon('TrendingUp'),
    TrendingDown: createMockIcon('TrendingDown'),
    Building2: createMockIcon('Building2'),
    Briefcase: createMockIcon('Briefcase'),
    Mail: createMockIcon('Mail'),
    Phone: createMockIcon('Phone'),
    Star: createMockIcon('Star'),
    Heart: createMockIcon('Heart'),
    Bookmark: createMockIcon('Bookmark'),
    Share: createMockIcon('Share'),
    Copy: createMockIcon('Copy'),
    ExternalLink: createMockIcon('ExternalLink'),
    Link: createMockIcon('Link'),
    Unlink: createMockIcon('Unlink'),
    Save: createMockIcon('Save'),
    Upload: createMockIcon('Upload'),
    FileText: createMockIcon('FileText'),
    File: createMockIcon('File'),
    Folder: createMockIcon('Folder'),
    Image: createMockIcon('Image'),
    Paperclip: createMockIcon('Paperclip'),
    Tag: createMockIcon('Tag'),
    Hash: createMockIcon('Hash'),
    AtSign: createMockIcon('AtSign'),
    Globe: createMockIcon('Globe'),
    Wifi: createMockIcon('Wifi'),
    WifiOff: createMockIcon('WifiOff'),
    Battery: createMockIcon('Battery'),
    BatteryLow: createMockIcon('BatteryLow'),
    Volume2: createMockIcon('Volume2'),
    VolumeX: createMockIcon('VolumeX'),
    Play: createMockIcon('Play'),
    Pause: createMockIcon('Pause'),
    Stop: createMockIcon('Stop'),
    SkipBack: createMockIcon('SkipBack'),
    SkipForward: createMockIcon('SkipForward'),
    Repeat: createMockIcon('Repeat'),
    Shuffle: createMockIcon('Shuffle'),
    Maximize: createMockIcon('Maximize'),
    Minimize: createMockIcon('Minimize'),
    Maximize2: createMockIcon('Maximize2'),
    Minimize2: createMockIcon('Minimize2'),
    RotateCcw: createMockIcon('RotateCcw'),
    RotateCw: createMockIcon('RotateCw'),
    ZoomIn: createMockIcon('ZoomIn'),
    ZoomOut: createMockIcon('ZoomOut'),
    Move: createMockIcon('Move'),
    MousePointer: createMockIcon('MousePointer'),
    Hand: createMockIcon('Hand'),
    Grab: createMockIcon('Grab'),
    Target: createMockIcon('Target'),
    Crosshair: createMockIcon('Crosshair'),
    Navigation: createMockIcon('Navigation'),
    Compass: createMockIcon('Compass'),
    Map: createMockIcon('Map'),
    Home: createMockIcon('Home'),
    Building: createMockIcon('Building'),
    Store: createMockIcon('Store'),
    ShoppingCart: createMockIcon('ShoppingCart'),
    ShoppingBag: createMockIcon('ShoppingBag'),
    CreditCard: createMockIcon('CreditCard'),
    DollarSign: createMockIcon('DollarSign'),
    Euro: createMockIcon('Euro'),
    PoundSterling: createMockIcon('PoundSterling'),
    Yen: createMockIcon('Yen'),
    Bitcoin: createMockIcon('Bitcoin'),
    Award: createMockIcon('Award'),
    Trophy: createMockIcon('Trophy'),
    Medal: createMockIcon('Medal'),
    Gift: createMockIcon('Gift'),
    PartyPopper: createMockIcon('PartyPopper'),
    Cake: createMockIcon('Cake'),
    Coffee: createMockIcon('Coffee'),
    Pizza: createMockIcon('Pizza'),
    Utensils: createMockIcon('Utensils'),
    Car: createMockIcon('Car'),
    Truck: createMockIcon('Truck'),
    Plane: createMockIcon('Plane'),
    Train: createMockIcon('Train'),
    Bus: createMockIcon('Bus'),
    Bike: createMockIcon('Bike'),
    Ship: createMockIcon('Ship'),
    Anchor: createMockIcon('Anchor'),
    Zap: createMockIcon('Zap'),
    Flame: createMockIcon('Flame'),
    Sun: createMockIcon('Sun'),
    Moon: createMockIcon('Moon'),
    Cloud: createMockIcon('Cloud'),
    CloudRain: createMockIcon('CloudRain'),
    CloudSnow: createMockIcon('CloudSnow'),
    Umbrella: createMockIcon('Umbrella'),
    Thermometer: createMockIcon('Thermometer'),
    Activity: createMockIcon('Activity'),
    BarChart: createMockIcon('BarChart'),
    BarChart2: createMockIcon('BarChart2'),
    BarChart3: createMockIcon('BarChart3'),
    LineChart: createMockIcon('LineChart'),
    PieChart: createMockIcon('PieChart'),
    TrendingFlat: createMockIcon('TrendingFlat'),
    Database: createMockIcon('Database'),
    Server: createMockIcon('Server'),
    HardDrive: createMockIcon('HardDrive'),
    Cpu: createMockIcon('Cpu'),
    Monitor: createMockIcon('Monitor'),
    Smartphone: createMockIcon('Smartphone'),
    Tablet: createMockIcon('Tablet'),
    Laptop: createMockIcon('Laptop'),
    Keyboard: createMockIcon('Keyboard'),
    Mouse: createMockIcon('Mouse'),
    Headphones: createMockIcon('Headphones'),
    Mic: createMockIcon('Mic'),
    MicOff: createMockIcon('MicOff'),
    Camera: createMockIcon('Camera'),
    CameraOff: createMockIcon('CameraOff'),
    Printer: createMockIcon('Printer'),
    Scanner: createMockIcon('Scanner'),
    Gamepad2: createMockIcon('Gamepad2'),
    Joystick: createMockIcon('Joystick'),
    Radio: createMockIcon('Radio'),
    Tv: createMockIcon('Tv'),
    Watch: createMockIcon('Watch'),
    Glasses: createMockIcon('Glasses'),
    Shirt: createMockIcon('Shirt'),
    Crown: createMockIcon('Crown'),
    Gem: createMockIcon('Gem'),
    Key: createMockIcon('Key'),
    Lock: createMockIcon('Lock'),
    Unlock: createMockIcon('Unlock'),
    Shield: createMockIcon('Shield'),
    ShieldCheck: createMockIcon('ShieldCheck'),
    ShieldAlert: createMockIcon('ShieldAlert'),
    ShieldX: createMockIcon('ShieldX'),
    Bug: createMockIcon('Bug'),
    Code: createMockIcon('Code'),
    Code2: createMockIcon('Code2'),
    Terminal: createMockIcon('Terminal'),
    GitBranch: createMockIcon('GitBranch'),
    GitCommit: createMockIcon('GitCommit'),
    GitMerge: createMockIcon('GitMerge'),
    GitPullRequest: createMockIcon('GitPullRequest'),
    Github: createMockIcon('Github'),
    Gitlab: createMockIcon('Gitlab'),
    MessageSquare: createMockIcon('MessageSquare'),
    MessageCircle: createMockIcon('MessageCircle'),
    Send: createMockIcon('Send'),
    Reply: createMockIcon('Reply'),
    ReplyAll: createMockIcon('ReplyAll'),
    Forward: createMockIcon('Forward'),
    Inbox: createMockIcon('Inbox'),
    Outbox: createMockIcon('Outbox'),
    Archive: createMockIcon('Archive'),
    Trash: createMockIcon('Trash'),
    Flag: createMockIcon('Flag'),
    Bell: createMockIcon('Bell'),
    BellOff: createMockIcon('BellOff'),
    BellRing: createMockIcon('BellRing'),
    AlarmClock: createMockIcon('AlarmClock'),
    Timer: createMockIcon('Timer'),
    Stopwatch: createMockIcon('Stopwatch'),
    Hourglass: createMockIcon('Hourglass'),
    CalendarDays: createMockIcon('CalendarDays'),
    CalendarCheck: createMockIcon('CalendarCheck'),
    CalendarX: createMockIcon('CalendarX'),
    CalendarPlus: createMockIcon('CalendarPlus'),
    CalendarMinus: createMockIcon('CalendarMinus'),
    CalendarRange: createMockIcon('CalendarRange'),
    CalendarClock: createMockIcon('CalendarClock'),
    CalendarHeart: createMockIcon('CalendarHeart'),
    CalendarSearch: createMockIcon('CalendarSearch'),
  };
});

// Global test utilities
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

global.matchMedia = vi.fn().mockImplementation((query) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock window.scrollTo
global.scrollTo = vi.fn();

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.sessionStorage = sessionStorageMock;
