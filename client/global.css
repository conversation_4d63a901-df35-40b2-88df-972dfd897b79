@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * HireFlow AI - Modern Green Theme
   * Inspired by contemporary AI website builders with green primary colors
   */
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* Modern Green Primary Colors */
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;

    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 142.1 76.2% 36.3%;
    --accent-foreground: 355.7 100% 97.3%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;

    --warning: 32.1 94.6% 43.7%;
    --warning-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142.1 76.2% 36.3%;

    --radius: 0.75rem;

    /* Sidebar with green theme */
    --sidebar-background: 240 10% 3.9%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 142.1 76.2% 36.3%;
    --sidebar-primary-foreground: 355.7 100% 97.3%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 142.1 76.2% 36.3%;

    /* AI-inspired gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(142.1 76.2% 36.3%) 0%,
      hsl(158.1 64.4% 51.6%) 100%
    );
    --gradient-secondary: linear-gradient(
      135deg,
      hsl(142.1 76.2% 36.3% / 0.1) 0%,
      hsl(158.1 64.4% 51.6% / 0.1) 100%
    );
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 142.1 76.2% 36.3%;
    --accent-foreground: 355.7 100% 97.3%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;

    --warning: 32.1 94.6% 43.7%;
    --warning-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142.1 76.2% 36.3%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 142.1 76.2% 36.3%;
    --sidebar-primary-foreground: 355.7 100% 97.3%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 142.1 76.2% 36.3%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(142.1 76.2% 36.3%) 0%,
      hsl(158.1 64.4% 41.6%) 100%
    );
    --gradient-secondary: linear-gradient(
      135deg,
      hsl(142.1 76.2% 36.3% / 0.15) 0%,
      hsl(158.1 64.4% 41.6% / 0.15) 100%
    );
  }
}

@layer base {
  * {
    @apply border-border;
    transition:
      background-color 0.3s ease,
      border-color 0.3s ease,
      color 0.3s ease;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
    transition:
      background-color 0.3s ease,
      color 0.3s ease;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold;
  }
}

@layer components {
  .metric-card {
    @apply bg-card border border-border rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 relative overflow-hidden;
  }

  .metric-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
  }

  .sidebar-nav-item {
    @apply flex items-center gap-3 rounded-xl px-4 py-3 text-sidebar-foreground transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground relative;
  }

  .sidebar-nav-item.active {
    @apply bg-sidebar-primary text-sidebar-primary-foreground shadow-lg;
  }

  .sidebar-nav-item.active::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: hsl(var(--sidebar-primary-foreground));
    border-radius: 0 2px 2px 0;
  }

  .ai-gradient-bg {
    background: var(--gradient-primary);
  }

  .ai-gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .glass-card {
    @apply bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl;
  }

  .modern-shadow {
    box-shadow:
      0 1px 3px 0 rgb(0 0 0 / 0.1),
      0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .modern-shadow-lg {
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 0.1),
      0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  .ai-button {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl px-6 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98];
  }

  .ai-button-outline {
    @apply border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground rounded-xl px-6 py-3 font-medium transition-all duration-200;
  }

  /* Dark mode specific styles */
  .dark .metric-card {
    @apply bg-card/80 backdrop-blur-sm;
  }

  .dark .glass-card {
    @apply bg-card/60 backdrop-blur-md border-border/30;
  }

  .dark .modern-shadow {
    box-shadow:
      0 1px 3px 0 rgb(0 0 0 / 0.3),
      0 1px 2px -1px rgb(0 0 0 / 0.3);
  }

  .dark .modern-shadow-lg {
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 0.3),
      0 4px 6px -4px rgb(0 0 0 / 0.3);
  }

  /* Theme transition helpers */
  .theme-transition {
    @apply transition-all duration-300 ease-in-out;
  }

  .theme-transition-fast {
    @apply transition-all duration-150 ease-in-out;
  }

  /* Dark mode glow effects */
  .dark .ai-button {
    @apply shadow-lg shadow-primary/25;
  }

  .dark .ai-button:hover {
    @apply shadow-xl shadow-primary/30;
  }

  .dark .metric-card:hover {
    @apply shadow-lg shadow-primary/10;
  }

  .dark .sidebar-nav-item.active {
    @apply shadow-lg shadow-primary/25;
  }

  /* Dark mode shimmer effect */
  @keyframes shimmer-dark {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .dark .animate-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    background-size: 200px 100%;
    animation: shimmer-dark 2s infinite;
  }
}

/* Modern scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.5);
}

.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.6);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* Animation utilities */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-green {
  0%,
  100% {
    box-shadow: 0 0 0 0 hsl(var(--primary) / 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px hsl(var(--primary) / 0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out;
}

.animate-pulse-green {
  animation: pulse-green 2s infinite;
}

/* Modern focus styles */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Enhanced Notification Styles */
.toaster {
  --normal-bg: hsl(var(--background));
  --normal-border: hsl(var(--border));
  --normal-text: hsl(var(--foreground));
  --success-bg: 240 253 244;
  --success-border: 34 197 94;
  --success-text: 21 128 61;
  --error-bg: 254 242 242;
  --error-border: 239 68 68;
  --error-text: 153 27 27;
  --warning-bg: 254 252 232;
  --warning-border: 245 158 11;
  --warning-text: 146 64 14;
  --info-bg: 239 246 255;
  --info-border: 59 130 246;
  --info-text: 30 64 175;
}

.dark .toaster {
  --success-bg: 6 78 59;
  --success-text: 110 231 183;
  --error-bg: 127 29 29;
  --error-text: 252 165 165;
  --warning-bg: 120 53 15;
  --warning-text: 251 191 36;
  --info-bg: 30 58 138;
  --info-text: 147 197 253;
}

/* Enhanced toast animations */
@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Toast container positioning */
.toaster group {
  top: 1rem;
  right: 1rem;
  max-width: 420px;
}

/* Custom toast border effects */
.group.toast {
  position: relative;
  overflow: visible;
}

.group.toast::before {
  content: "";
  position: absolute;
  left: -2px;
  top: -2px;
  bottom: -2px;
  width: 6px;
  border-radius: 3px;
  opacity: 0.8;
}

/* Toast type specific styling */
[data-type="success"]::before {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

[data-type="error"]::before {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

[data-type="warning"]::before {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

[data-type="info"]::before {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

/* Enhanced toast content */
.group.toast {
  backdrop-filter: blur(8px);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.group.toast:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Dark mode enhancements */
.dark .group.toast {
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .group.toast:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}
