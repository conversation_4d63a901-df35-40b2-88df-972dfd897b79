// User profiles, team members, and organization data for HireFlow ATS

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  initials: string;
  title: string;
  department: string;
  role: "admin" | "manager" | "recruiter" | "interviewer" | "viewer";
  permissions: string[];
  phone?: string;
  location: string;
  timezone: string;
  language: string;
  isActive: boolean;
  lastLogin: string;
  joinedDate: string;
  bio?: string;
  skills: string[];
  preferences: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    weeklyReports: boolean;
    interviewReminders: boolean;
    candidateUpdates: boolean;
    systemAlerts: boolean;
    theme: "light" | "dark" | "system";
    compactView: boolean;
  };
  socialLinks: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
}

export interface TeamMember extends UserProfile {
  reportsTo?: string;
  directReports: string[];
  currentProjects: string[];
  performanceRating: number;
  interviewsCompleted: number;
  candidatesHired: number;
}

export interface Organization {
  id: string;
  name: string;
  logo?: string;
  website: string;
  industry: string;
  size: string;
  founded: string;
  headquarters: string;
  description: string;
  contactInfo: {
    email: string;
    phone: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
  };
  settings: {
    allowPublicApplications: boolean;
    requireVisaSponsorship: boolean;
    defaultWorkLocation: "remote" | "onsite" | "hybrid";
    applicationDeadline: number; // days
    interviewProcess: string[];
    emailTemplates: {
      welcome: string;
      rejection: string;
      interview: string;
      offer: string;
    };
    integrations: {
      slack: boolean;
      teams: boolean;
      zoom: boolean;
      calendar: boolean;
    };
  };
  branding: {
    primaryColor: string;
    secondaryColor: string;
    logoUrl?: string;
    faviconUrl?: string;
  };
}

// Mock Data
export const mockUserProfile: UserProfile = {
  id: "user-1",
  email: "<EMAIL>",
  firstName: "Hùng",
  lastName: "Nguyễn Văn",
  initials: "NH",
  title: "Quản lý Tuyển dụng Nhân tài Senior",
  department: "Nhân sự",
  role: "admin",
  permissions: [
    "manage_candidates",
    "manage_jobs",
    "manage_interviews",
    "manage_team",
    "view_analytics",
    "admin_settings",
  ],
  phone: "+84 (90) 123-4567",
  location: "Thành phố Hồ Chí Minh",
  timezone: "Asia/Ho_Chi_Minh",
  language: "Vietnamese",
  isActive: true,
  lastLogin: "2024-01-22T09:30:00Z",
  joinedDate: "2022-03-15",
  bio: "Chuyên gia tuyển dụng nhân tài có kinh nghiệm với 8+ năm trong tuyển dụng công nghệ. Đam mê xây dựng các đội ngũ đa dạng, hiệu suất cao và tạo ra trải nghiệm ứng viên xuất sắc.",
  skills: [
    "Tuyển dụng Kỹ thuật",
    "Tìm kiếm Điều hành",
    "Đa dạng & Hòa nhập",
    "Thương hiệu Nhà tuyển dụng",
    "Phân tích",
  ],
  preferences: {
    emailNotifications: true,
    pushNotifications: true,
    weeklyReports: true,
    interviewReminders: true,
    candidateUpdates: true,
    systemAlerts: false,
    theme: "system",
    compactView: false,
  },
  socialLinks: {
    linkedin: "https://linkedin.com/in/nguyenvanhung",
    twitter: "https://twitter.com/nguyenvanhung",
  },
};

export const mockTeamMembers: TeamMember[] = [
  {
    ...mockUserProfile,
    id: "user-1",
    reportsTo: undefined,
    directReports: ["user-2", "user-3", "user-4"],
    currentProjects: ["Kế hoạch Tuyển dụng Q1", "Mở rộng Đội Kỹ thuật"],
    performanceRating: 4.8,
    interviewsCompleted: 127,
    candidatesHired: 23,
  },
  {
    id: "user-2",
    email: "<EMAIL>",
    firstName: "Mai",
    lastName: "Trần Thị",
    initials: "TM",
    title: "Nhà tuyển dụng Senior",
    department: "Nhân sự",
    role: "recruiter",
    permissions: ["manage_candidates", "manage_interviews", "view_analytics"],
    phone: "+84 (91) 234-5678",
    location: "Hà Nội",
    timezone: "Asia/Ho_Chi_Minh",
    language: "Vietnamese",
    isActive: true,
    lastLogin: "2024-01-22T11:15:00Z",
    joinedDate: "2021-08-20",
    bio: "Nhà tuyển dụng quản lý sản phẩm chuyên về các vị trí cấp cao.",
    skills: [
      "Tuyển dụng Sản phẩm",
      "Quản lý Bên liên quan",
      "Tối ưu hóa Quy trình",
    ],
    preferences: {
      emailNotifications: true,
      pushNotifications: true,
      weeklyReports: false,
      interviewReminders: true,
      candidateUpdates: true,
      systemAlerts: true,
      theme: "light",
      compactView: true,
    },
    socialLinks: {
      linkedin: "https://linkedin.com/in/tranthimai",
    },
    reportsTo: "user-1",
    directReports: [],
    currentProjects: [
      "Tuyển dụng Đội Sản phẩm",
      "Chương trình Đào tạo Quản lý",
    ],
    performanceRating: 4.6,
    interviewsCompleted: 89,
    candidatesHired: 18,
  },
  {
    id: "user-3",
    email: "<EMAIL>",
    firstName: "Minh",
    lastName: "Phạm Văn",
    initials: "PM",
    title: "Nhà tuyển dụng Kỹ thuật",
    department: "Nhân sự",
    role: "recruiter",
    permissions: ["manage_candidates", "manage_interviews"],
    phone: "+84 (92) 345-6789",
    location: "Đà Nẵng",
    timezone: "Asia/Ho_Chi_Minh",
    language: "Vietnamese",
    isActive: true,
    lastLogin: "2024-01-22T08:45:00Z",
    joinedDate: "2020-05-10",
    bio: "Nhà tuyển dụng kỹ thuật với kiến thức kỹ thuật sâu và mạng lưới rộng.",
    skills: ["Tuyển dụng Kỹ thuật", "Đánh giá Kỹ thuật", "Phỏng vấn Coding"],
    preferences: {
      emailNotifications: true,
      pushNotifications: false,
      weeklyReports: true,
      interviewReminders: true,
      candidateUpdates: false,
      systemAlerts: true,
      theme: "dark",
      compactView: false,
    },
    socialLinks: {
      linkedin: "https://linkedin.com/in/phamvanminh",
      github: "https://github.com/phamvanminh",
    },
    reportsTo: "user-1",
    directReports: [],
    currentProjects: ["Vị trí Kỹ sư Senior", "Tuyển dụng Sinh viên"],
    performanceRating: 4.9,
    interviewsCompleted: 156,
    candidatesHired: 31,
  },
  {
    id: "user-4",
    email: "<EMAIL>",
    firstName: "Hoa",
    lastName: "Lê Thị",
    initials: "LH",
    title: "Chuyên viên HR",
    department: "Nhân sự",
    role: "interviewer",
    permissions: ["manage_interviews", "view_analytics"],
    phone: "+84 (93) 456-7890",
    location: "Cần Thơ",
    timezone: "Asia/Ho_Chi_Minh",
    language: "Vietnamese",
    isActive: true,
    lastLogin: "2024-01-21T16:30:00Z",
    joinedDate: "2019-11-03",
    bio: "Chuyên viên HR tập trung vào trải nghiệm ứng viên và đánh giá văn hóa.",
    skills: ["Đánh giá Văn hóa", "Onboarding", "Quan hệ Nhân viên"],
    preferences: {
      emailNotifications: true,
      pushNotifications: true,
      weeklyReports: true,
      interviewReminders: true,
      candidateUpdates: true,
      systemAlerts: false,
      theme: "light",
      compactView: false,
    },
    socialLinks: {
      linkedin: "https://linkedin.com/in/lethihoa",
    },
    reportsTo: "user-1",
    directReports: [],
    currentProjects: ["Quy trình Onboarding", "Phỏng vấn Văn hóa"],
    performanceRating: 4.7,
    interviewsCompleted: 234,
    candidatesHired: 45,
  },
  {
    id: "user-5",
    email: "<EMAIL>",
    firstName: "Đức",
    lastName: "Bùi Văn",
    initials: "BD",
    title: "Điều phối viên Tuyển dụng",
    department: "Nhân sự",
    role: "viewer",
    permissions: ["view_analytics"],
    phone: "+84 (94) 567-8901",
    location: "Làm việc từ xa",
    timezone: "Asia/Ho_Chi_Minh",
    language: "Vietnamese",
    isActive: true,
    lastLogin: "2024-01-22T07:20:00Z",
    joinedDate: "2023-02-01",
    bio: "Điều phối viên tuyển dụng quản lý logistics phỏng vấn và giao tiếp ứng viên.",
    skills: ["Lập lịch", "Giao tiếp", "Quản lý Quy trình"],
    preferences: {
      emailNotifications: true,
      pushNotifications: true,
      weeklyReports: false,
      interviewReminders: true,
      candidateUpdates: false,
      systemAlerts: true,
      theme: "system",
      compactView: true,
    },
    socialLinks: {},
    reportsTo: "user-2",
    directReports: [],
    currentProjects: ["Lập lịch Phỏng vấn", "Khảo sát Trải nghiệm Ứng viên"],
    performanceRating: 4.4,
    interviewsCompleted: 0,
    candidatesHired: 0,
  },
];

export const mockOrganization: Organization = {
  id: "org-1",
  name: "HireFlow Technologies Vietnam",
  website: "https://hireflow.vn",
  industry: "Công nghệ",
  size: "51-200 nhân viên",
  founded: "2020",
  headquarters: "Thành phố Hồ Chí Minh",
  description:
    "HireFlow là nền tảng tuyển dụng được hỗ trợ bởi AI tiên tiến giúp các công ty tìm kiếm, đánh giá và tuyển dụng nhân tài tốt nhất nhanh hơn. Sứ mệnh của chúng tôi là cách mạng hóa quy trình tuyển dụng thông qua tự động hóa thông minh và thông tin dựa trên dữ liệu.",
  contactInfo: {
    email: "<EMAIL>",
    phone: "+84 (28) 123-HIRE",
    address: {
      street: "123 Đường Đổi mới, Tầng 4",
      city: "Thành phố Hồ Chí Minh",
      state: "TP.HCM",
      zipCode: "700000",
      country: "Việt Nam",
    },
  },
  settings: {
    allowPublicApplications: true,
    requireVisaSponsorship: false,
    defaultWorkLocation: "hybrid",
    applicationDeadline: 30,
    interviewProcess: [
      "Đánh giá Hồ sơ",
      "Sàng lọc Điện thoại",
      "Phỏng vấn Kỹ thuật",
      "Phỏng vấn Văn hóa",
      "Phỏng vấn Cuối cùng",
      "Kiểm tra Tham chiếu",
    ],
    emailTemplates: {
      welcome:
        "Chào mừng đến với HireFlow! Chúng tôi rất vui mừng được xem xét hồ sơ của bạn...",
      rejection:
        "Cảm ơn bạn đã quan tâm đến HireFlow. Mặc dù chúng tôi sẽ không tiếp tục...",
      interview: "Chúc mừng! Chúng tôi muốn mời bạn tham gia phỏng vấn...",
      offer:
        "Chúng tôi rất vui mừng được gửi đến bạn lời đề nghị gia nhập đội ngũ...",
    },
    integrations: {
      slack: true,
      teams: false,
      zoom: true,
      calendar: true,
    },
  },
  branding: {
    primaryColor: "#10b981",
    secondaryColor: "#065f46",
    logoUrl: "/logo.png",
    faviconUrl: "/favicon.ico",
  },
};

// Helper functions
export const getUserRole = (roleKey: string) => {
  const roles = {
    admin: "Quản trị viên",
    manager: "Quản lý",
    recruiter: "Nhà tuyển dụng",
    interviewer: "Người phỏng vấn",
    viewer: "Người xem",
  };
  return roles[roleKey as keyof typeof roles] || roleKey;
};

export const getPermissionLabel = (permission: string) => {
  const labels = {
    manage_candidates: "Quản lý Ứng viên",
    manage_jobs: "Quản lý Việc làm",
    manage_interviews: "Quản lý Phỏng vấn",
    manage_team: "Quản lý Đội ngũ",
    view_analytics: "Xem Phân tích",
    admin_settings: "Cài đặt Quản trị",
  };
  return labels[permission as keyof typeof labels] || permission;
};
