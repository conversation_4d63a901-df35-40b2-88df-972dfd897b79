// User profiles, team members, and organization data for HireFlow ATS

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  initials: string;
  title: string;
  department: string;
  role: "admin" | "manager" | "recruiter" | "interviewer" | "viewer";
  permissions: string[];
  phone?: string;
  location: string;
  timezone: string;
  language: string;
  isActive: boolean;
  lastLogin: string;
  joinedDate: string;
  bio?: string;
  skills: string[];
  preferences: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    weeklyReports: boolean;
    interviewReminders: boolean;
    candidateUpdates: boolean;
    systemAlerts: boolean;
    theme: "light" | "dark" | "system";
    compactView: boolean;
  };
  socialLinks: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
}

export interface TeamMember extends UserProfile {
  reportsTo?: string;
  directReports: string[];
  currentProjects: string[];
  performanceRating: number;
  interviewsCompleted: number;
  candidatesHired: number;
}

export interface Organization {
  id: string;
  name: string;
  logo?: string;
  website: string;
  industry: string;
  size: string;
  founded: string;
  headquarters: string;
  description: string;
  contactInfo: {
    email: string;
    phone: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
  };
  settings: {
    allowPublicApplications: boolean;
    requireVisaSponsorship: boolean;
    defaultWorkLocation: "remote" | "onsite" | "hybrid";
    applicationDeadline: number; // days
    interviewProcess: string[];
    emailTemplates: {
      welcome: string;
      rejection: string;
      interview: string;
      offer: string;
    };
    integrations: {
      slack: boolean;
      teams: boolean;
      zoom: boolean;
      calendar: boolean;
    };
  };
  branding: {
    primaryColor: string;
    secondaryColor: string;
    logoUrl?: string;
    faviconUrl?: string;
  };
}

// Mock Data

export const mockOrganization: Organization = {
  id: "org-1",
  name: "HireFlow Technologies Vietnam",
  website: "https://hireflow.vn",
  industry: "Công nghệ",
  size: "51-200 nhân viên",
  founded: "2020",
  headquarters: "Thành phố Hồ Chí Minh",
  description:
    "HireFlow là nền tảng tuyển dụng được hỗ trợ bởi AI tiên tiến giúp các công ty tìm kiếm, đánh giá và tuyển dụng nhân tài tốt nhất nhanh hơn. Sứ mệnh của chúng tôi là cách mạng hóa quy trình tuyển dụng thông qua tự động hóa thông minh và thông tin dựa trên dữ liệu.",
  contactInfo: {
    email: "<EMAIL>",
    phone: "+84 (28) 123-HIRE",
    address: {
      street: "123 Đường Đổi mới, Tầng 4",
      city: "Thành phố Hồ Chí Minh",
      state: "TP.HCM",
      zipCode: "700000",
      country: "Việt Nam",
    },
  },
  settings: {
    allowPublicApplications: true,
    requireVisaSponsorship: false,
    defaultWorkLocation: "hybrid",
    applicationDeadline: 30,
    interviewProcess: [
      "Đánh giá Hồ sơ",
      "Sàng lọc Điện thoại",
      "Phỏng vấn Kỹ thuật",
      "Phỏng vấn Văn hóa",
      "Phỏng vấn Cuối cùng",
      "Kiểm tra Tham chiếu",
    ],
    emailTemplates: {
      welcome:
        "Chào mừng đến với HireFlow! Chúng tôi rất vui mừng được xem xét hồ sơ của bạn...",
      rejection:
        "Cảm ơn bạn đã quan tâm đến HireFlow. Mặc dù chúng tôi sẽ không tiếp tục...",
      interview: "Chúc mừng! Chúng tôi muốn mời bạn tham gia phỏng vấn...",
      offer:
        "Chúng tôi rất vui mừng được gửi đến bạn lời đề nghị gia nhập đội ngũ...",
    },
    integrations: {
      slack: true,
      teams: false,
      zoom: true,
      calendar: true,
    },
  },
  branding: {
    primaryColor: "#10b981",
    secondaryColor: "#065f46",
    logoUrl: "/logo.png",
    faviconUrl: "/favicon.ico",
  },
};

// Helper functions
export const getUserRole = (roleKey: string) => {
  const roles = {
    admin: "Quản trị viên",
    manager: "Quản lý",
    recruiter: "Nhà tuyển dụng",
    interviewer: "Người phỏng vấn",
    viewer: "Người xem",
  };
  return roles[roleKey as keyof typeof roles] || roleKey;
};

export const getPermissionLabel = (permission: string) => {
  const labels = {
    manage_candidates: "Quản lý Ứng viên",
    manage_jobs: "Quản lý Việc làm",
    manage_interviews: "Quản lý Phỏng vấn",
    manage_team: "Quản lý Đội ngũ",
    view_analytics: "Xem Phân tích",
    admin_settings: "Cài đặt Quản trị",
  };
  return labels[permission as keyof typeof labels] || permission;
};
