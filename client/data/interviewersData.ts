// Interviewer data structure and mock data for HireFlow ATS

export interface Interviewer {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  initials: string;
  title: string;
  department: string;
  expertise: string[];
  availability: {
    monday: boolean;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturday: boolean;
    sunday: boolean;
  };
  timeSlots: string[];
  location: string;
  maxInterviewsPerDay: number;
  isActive: boolean;
  joinedDate: string;
  totalInterviews: number;
  rating: number;
  preferredTypes: ("video" | "phone" | "in-person")[];
  languages: string[];
  notes?: string;
}

export const mockInterviewers: Interviewer[] = [
  {
    id: "int-1",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+84 (90) 123-0001",
    initials: "NH",
    title: "<PERSON><PERSON>ề<PERSON>",
    department: "<PERSON><PERSON><PERSON>",
    expertise: [
      "React",
      "Node.js",
      "TypeScript",
      "Thiế<PERSON> <PERSON><PERSON>ố<PERSON>",
      "<PERSON><PERSON><PERSON> tr<PERSON>",
    ],
    availability: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["09:00", "10:30", "14:00", "15:30"],
    location: "Thành phố Hồ Chí Minh",
    maxInterviewsPerDay: 4,
    isActive: true,
    joinedDate: "2022-03-15",
    totalInterviews: 127,
    rating: 4.8,
    preferredTypes: ["video", "in-person"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
    notes: "Xuất sắc trong đánh giá kỹ thuật và phù hợp văn hóa.",
  },
  {
    id: "int-2",
    name: "Trần Thị Mai",
    email: "<EMAIL>",
    phone: "+84 (91) 123-0002",
    initials: "TM",
    title: "Quản lý Sản phẩm",
    department: "Sản phẩm",
    expertise: [
      "Chiến lược Sản phẩm",
      "Nghiên cứu Người dùng",
      "Agile",
      "Lập kế hoạch Lộ trình",
      "Phân tích",
    ],
    availability: {
      monday: true,
      tuesday: true,
      wednesday: false,
      thursday: true,
      friday: true,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["10:00", "11:30", "13:00", "16:00"],
    location: "Hà Nội",
    maxInterviewsPerDay: 3,
    isActive: true,
    joinedDate: "2021-08-20",
    totalInterviews: 89,
    rating: 4.9,
    preferredTypes: ["video", "phone"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
  },
  {
    id: "int-3",
    name: "Phạm Văn Minh",
    email: "<EMAIL>",
    phone: "+84 (92) 123-0003",
    initials: "PM",
    title: "Trưởng nhóm Kỹ thuật",
    department: "Kỹ thuật",
    expertise: [
      "Lãnh đạo Đội nhóm",
      "Kiến trúc Hệ thống",
      "Python",
      "AWS",
      "DevOps",
    ],
    availability: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: false,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["09:30", "11:00", "14:30", "16:00"],
    location: "Đà Nẵng",
    maxInterviewsPerDay: 3,
    isActive: true,
    joinedDate: "2020-05-10",
    totalInterviews: 156,
    rating: 4.7,
    preferredTypes: ["video", "in-person"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
    notes: "Giỏi đánh giá ứng viên cấp cao và tiềm năng lãnh đạo.",
  },
  {
    id: "int-4",
    name: "Lê Thị Hoa",
    email: "<EMAIL>",
    phone: "+84 (93) 123-0004",
    initials: "LH",
    title: "Quản lý HR",
    department: "Nhân sự",
    expertise: [
      "Phù hợp Văn hóa",
      "Phỏng vấn Hành vi",
      "Lương thưởng",
      "Onboarding",
      "Đa dạng",
    ],
    availability: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["09:00", "10:30", "13:30", "15:00", "16:30"],
    location: "Cần Thơ",
    maxInterviewsPerDay: 5,
    isActive: true,
    joinedDate: "2019-11-03",
    totalInterviews: 234,
    rating: 4.9,
    preferredTypes: ["video", "phone", "in-person"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
  },
  {
    id: "int-5",
    name: "Hoàng Văn Tuấn",
    email: "<EMAIL>",
    phone: "+84 (94) 123-0005",
    initials: "HT",
    title: "Giám đốc Công nghệ",
    department: "Điều hành",
    expertise: [
      "Tầm nhìn Công nghệ",
      "Kế hoạch Chiến lược",
      "AI/ML",
      "Blockchain",
      "Lãnh đạo",
    ],
    availability: {
      monday: false,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: false,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["10:00", "15:00"],
    location: "Thành phố Hồ Chí Minh",
    maxInterviewsPerDay: 2,
    isActive: true,
    joinedDate: "2018-01-15",
    totalInterviews: 78,
    rating: 5.0,
    preferredTypes: ["video", "in-person"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
    notes: "Chỉ phỏng vấn lãnh đạo cấp cao và vai trò kỹ thuật chủ chốt.",
  },
  {
    id: "int-6",
    name: "Vũ Thị Lan",
    email: "<EMAIL>",
    phone: "+84 (95) 123-0006",
    initials: "VL",
    title: "Trưởng nhóm Thiết kế",
    department: "Thiết kế",
    expertise: [
      "Thiết kế UX",
      "Hệ thống Thiết kế",
      "Nghiên cứu Người dùng",
      "Prototyping",
      "Thiết kế Hình ảnh",
    ],
    availability: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: false,
      friday: true,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["10:00", "11:30", "14:00", "15:30"],
    location: "Hải Phòng",
    maxInterviewsPerDay: 4,
    isActive: true,
    joinedDate: "2021-02-28",
    totalInterviews: 92,
    rating: 4.8,
    preferredTypes: ["video", "in-person"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
  },
  {
    id: "int-7",
    name: "Đỗ Văn Sơn",
    email: "<EMAIL>",
    phone: "+84 (96) 123-0007",
    initials: "DS",
    title: "Giám đốc Kinh doanh",
    department: "Kinh doanh",
    expertise: [
      "Chiến lược Kinh doanh",
      "Quan hệ Khách hàng",
      "Phát triển Kinh doanh",
      "Đàm phán",
      "Phân tích Thị trường",
    ],
    availability: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["09:00", "11:00", "13:00", "15:00", "17:00"],
    location: "Nha Trang",
    maxInterviewsPerDay: 4,
    isActive: true,
    joinedDate: "2020-09-12",
    totalInterviews: 145,
    rating: 4.6,
    preferredTypes: ["phone", "video", "in-person"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
  },
  {
    id: "int-8",
    name: "Ngô Thị Nga",
    email: "<EMAIL>",
    phone: "+84 (97) 123-0008",
    initials: "NN",
    title: "Quản lý Khoa học Dữ liệu",
    department: "Khoa học Dữ liệu",
    expertise: [
      "Machine Learning",
      "Thống kê",
      "Python",
      "Phân tích Dữ liệu",
      "Lãnh đạo Đội nhóm",
    ],
    availability: {
      monday: true,
      tuesday: false,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["09:30", "11:00", "14:30", "16:00"],
    location: "Huế",
    maxInterviewsPerDay: 3,
    isActive: true,
    joinedDate: "2021-06-07",
    totalInterviews: 67,
    rating: 4.7,
    preferredTypes: ["video"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
  },
  {
    id: "int-9",
    name: "Bùi Văn Đức",
    email: "<EMAIL>",
    phone: "+84 (98) 123-0009",
    initials: "BD",
    title: "Kỹ sư DevOps",
    department: "Kỹ thuật",
    expertise: ["Docker", "Kubernetes", "AWS", "CI/CD", "Hạ tầng"],
    availability: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["08:00", "09:30", "13:30", "15:00"],
    location: "L��m việc từ xa",
    maxInterviewsPerDay: 3,
    isActive: false,
    joinedDate: "2022-01-10",
    totalInterviews: 43,
    rating: 4.5,
    preferredTypes: ["video"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
    notes: "Hiện đang nghỉ phép, sẽ trở lại tháng tới.",
  },
  {
    id: "int-10",
    name: "Đinh Thị Thu",
    email: "<EMAIL>",
    phone: "+84 (99) 123-0010",
    initials: "DT",
    title: "Quản lý Marketing",
    department: "Marketing",
    expertise: [
      "Marketing Số",
      "Chiến lược Nội dung",
      "Phân tích",
      "Quản lý Thương hiệu",
      "Mạng xã hội",
    ],
    availability: {
      monday: true,
      tuesday: true,
      wednesday: false,
      thursday: true,
      friday: true,
      saturday: false,
      sunday: false,
    },
    timeSlots: ["10:00", "11:30", "14:00", "16:30"],
    location: "Quy Nhon",
    maxInterviewsPerDay: 4,
    isActive: true,
    joinedDate: "2021-11-15",
    totalInterviews: 58,
    rating: 4.6,
    preferredTypes: ["video", "phone"],
    languages: ["Tiếng Việt", "Tiếng Anh"],
  },
];

// Helper functions for interviewer management
export const getActiveInterviewers = () =>
  mockInterviewers.filter((interviewer) => interviewer.isActive);

export const getInterviewersByDepartment = (department: string) =>
  mockInterviewers.filter(
    (interviewer) =>
      interviewer.department.toLowerCase() === department.toLowerCase(),
  );

export const getAvailableInterviewers = (
  day: keyof Interviewer["availability"],
) =>
  mockInterviewers.filter(
    (interviewer) => interviewer.isActive && interviewer.availability[day],
  );

export const getDepartments = () => [
  ...new Set(mockInterviewers.map((interviewer) => interviewer.department)),
];

export const getExpertiseAreas = () => [
  ...new Set(mockInterviewers.flatMap((interviewer) => interviewer.expertise)),
];
