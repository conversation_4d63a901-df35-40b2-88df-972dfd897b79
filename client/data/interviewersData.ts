// Interviewer data structure and mock data for HireFlow ATS

export interface Interviewer {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  initials: string;
  title: string;
  department: string;
  expertise: string[];
  availability: {
    monday: boolean;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturday: boolean;
    sunday: boolean;
  };
  timeSlots: string[];
  location: string;
  maxInterviewsPerDay: number;
  isActive: boolean;
  joinedDate: string;
  totalInterviews: number;
  rating: number;
  preferredTypes: ("video" | "phone" | "in-person")[];
  languages: string[];
  notes?: string;
}

export const mockInterviewers: Interviewer[] = [];

// Helper functions for interviewer management
export const getActiveInterviewers = () =>
  mockInterviewers.filter((interviewer) => interviewer.isActive);

export const getInterviewersByDepartment = (department: string) =>
  mockInterviewers.filter(
    (interviewer) =>
      interviewer.department.toLowerCase() === department.toLowerCase(),
  );

export const getAvailableInterviewers = (
  day: keyof Interviewer["availability"],
) =>
  mockInterviewers.filter(
    (interviewer) => interviewer.isActive && interviewer.availability[day],
  );

export const getDepartments = () => [
  ...new Set(mockInterviewers.map((interviewer) => interviewer.department)),
];

export const getExpertiseAreas = () => [
  ...new Set(mockInterviewers.flatMap((interviewer) => interviewer.expertise)),
];
