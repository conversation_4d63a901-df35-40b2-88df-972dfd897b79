# Unified CandidateModal Implementation

## Overview

The `EditCandidateModal` component has been successfully transformed into a unified `CandidateModal` that supports both adding new candidates and editing existing ones, eliminating the need for a separate `AddCandidateModal` component.

## Key Changes

### 1. Component Interface Updates

**New Props:**
- `mode?: "edit" | "add"` - Determines the modal behavior (defaults to "edit" for backwards compatibility)
- `onCreate?: (candidate: Omit<UiCandidate, "id">) => Promise<void>` - Handler for creating new candidates
- `onUpdate?` and `candidate?` - Made optional to support add mode

### 2. Unified Functionality

**Mode-Specific Behavior:**
- **Edit Mode**: Loads existing candidate data, shows "Edit Candidate" title, calls `onUpdate`
- **Add Mode**: Shows empty form, "Add New Candidate" title, calls `onCreate`

**Shared Features:**
- Form validation (with additional required fields in add mode)
- AI-powered resume extraction
- Skills and tags management
- File upload support
- Comprehensive form fields

### 3. Enhanced Features

**AI Toggle (Add Mode Only):**
- Visual toggle for AI-powered resume parsing
- Enhanced file upload interface with drag-and-drop styling
- Demo mode for resume extraction in add mode

**Adaptive Validation:**
- Phone number required in add mode, optional in edit mode
- Dynamic form validation based on mode

**Improved UX:**
- Mode-appropriate icons (Edit vs. User)
- Context-sensitive button text
- Tab structure adapts to mode (4 tabs in add mode, 3 in edit mode)

### 4. Tab Structure

**Edit Mode (3 tabs):**
1. General Information - Basic and professional info combined
2. Documents - Resume and profile links
3. Additional - Notes and extra information

**Add Mode (4 tabs):**
1. Basic Info - Name, email, phone, location
2. Professional - Position, experience, salary, skills
3. Documents - Resume upload and profile links
4. Additional - Notes and tags

### 5. Resume Extraction

**Dual Extraction Logic:**
- **Edit Mode**: Real API extraction using candidate ID
- **Add Mode**: Demo mode with mock data (prompts to save first for real extraction)

### 6. Backwards Compatibility

**Alias Exports:**
```typescript
export const CandidateModal = ({ ... }) => { ... };
export const EditCandidateModal = CandidateModal; // Backwards compatibility
export const AddCandidateModal = CandidateModal;  // Replacement alias
```

### 7. Translation Support

**Added Translation Keys:**
- Modal-specific keys (addCandidate, editCandidate, updateCandidate)
- Form field labels and placeholders
- Validation messages
- Mode-specific descriptions

## Usage Examples

### Adding a New Candidate
```tsx
<CandidateModal
  mode="add"
  isOpen={isAddModalOpen}
  onClose={() => setIsAddModalOpen(false)}
  onCreate={handleCreateCandidate}
/>
```

### Editing an Existing Candidate
```tsx
<CandidateModal
  mode="edit"
  isOpen={isEditModalOpen}
  onClose={() => setIsEditModalOpen(false)}
  onUpdate={handleUpdateCandidate}
  candidate={selectedCandidate}
/>
```

### Backwards Compatibility
```tsx
// Still works with existing code
<EditCandidateModal
  isOpen={isEditModalOpen}
  onClose={() => setIsEditModalOpen(false)}
  onUpdate={handleUpdateCandidate}
  candidate={selectedCandidate}
/>
```

## Implementation Details

### Form Validation Differences

**Add Mode:**
- Name: Required
- Email: Required with format validation
- Phone: Required
- Position: Required

**Edit Mode:**
- Name: Required
- Email: Required with format validation
- Phone: Optional
- Position: Required

### State Management

**Shared State:**
- Form data via `react-hook-form`
- Selected skills and tags arrays
- Resume file handling
- AI extraction state

**Mode-Specific State:**
- `isAiMode` - Only used in add mode
- Form default values - Applied only in edit mode

### File Structure

**Modified Files:**
- `client/components/candidates/EditCandidateModal.tsx` - Main unified component
- `client/pages/Candidates.tsx` - Updated to use new component
- `client/lib/translations/en.ts` - Added missing translation keys
- `client/lib/translations/vi.ts` - Added Vietnamese translations

**Files Ready for Removal:**
- `client/components/candidates/AddCandidateModal.tsx` - Can be safely removed

## Benefits

1. **Code Deduplication**: Eliminated ~800 lines of duplicate code
2. **Maintenance**: Single component to maintain instead of two
3. **Consistency**: Unified UX across add and edit operations
4. **Feature Parity**: Both modes have access to all features
5. **Flexibility**: Easy to extend with new features for both modes
6. **Type Safety**: Comprehensive TypeScript interfaces

## Testing Recommendations

1. **Add Mode Testing:**
   - Verify empty form initialization
   - Test required field validation
   - Check AI toggle functionality
   - Test file upload and demo extraction

2. **Edit Mode Testing:**
   - Verify candidate data loading
   - Test form updates and submission
   - Check real API extraction (if candidate has resume)
   - Verify backwards compatibility

3. **Shared Functionality:**
   - Skills and tags management
   - Form validation across both modes
   - Translation key usage
   - Modal open/close behavior

## Next Steps

1. **Optional**: Remove the old `AddCandidateModal.tsx` file
2. **Testing**: Thoroughly test both modes in development
3. **Documentation**: Update any component documentation
4. **Migration**: Update other files that might import `AddCandidateModal`

## Migration Path

For any existing code using `AddCandidateModal`:

**Before:**
```tsx
import { AddCandidateModal } from "@/components/candidates/AddCandidateModal";

<AddCandidateModal
  isOpen={isOpen}
  onClose={onClose}
  onAdd={onAdd}
/>
```

**After:**
```tsx
import { CandidateModal } from "@/components/candidates/EditCandidateModal";
// OR use the alias for drop-in replacement
import { AddCandidateModal } from "@/components/candidates/EditCandidateModal";

<CandidateModal
  mode="add"
  isOpen={isOpen}
  onClose={onClose}
  onCreate={onAdd} // Note: prop name changed from onAdd to onCreate
/>
```
