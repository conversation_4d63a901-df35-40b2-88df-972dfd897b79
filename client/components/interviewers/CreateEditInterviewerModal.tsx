import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Check,
  ChevronsUpDown,
  X,
  Plus,
  User,
  Clock,
  MapPin,
  Building2,
  Mail,
  Phone,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  InterviewerFormData,
  InterviewerValidationErrors,
  DEPARTMENTS,
  COMMON_EXPERTISE,
  TIME_SLOTS,
  TIMEZONES,
  DAYS_OF_WEEK,
  Interviewer,
  TimeSlot,
} from "@/lib/types/interviewer";
import { useInterviewerUsers } from "@/hooks/useApi";

interface CreateEditInterviewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: InterviewerFormData) => Promise<void>;
  interviewer?: Interviewer | null;
  isLoading?: boolean;
}
export function CreateEditInterviewerModal({
  isOpen,
  onClose,
  onSubmit,
  interviewer,
  isLoading = false,
}: CreateEditInterviewerModalProps) {
  const { data: users = [], isLoading: usersLoading } = useInterviewerUsers();

  const [formData, setFormData] = useState<InterviewerFormData>({
    user_id: "",
    department: "",
    expertise: [],
    location: "",
    max_interviews_per_day: 3,
    timezone: "Asia/Ho_Chi_Minh",
    is_active: true,
    availability: null,
    time_slots: [],
  });

  const [validationErrors, setValidationErrors] =
    useState<InterviewerValidationErrors>({});
  const [userSelectOpen, setUserSelectOpen] = useState(false);
  const [expertiseOpen, setExpertiseOpen] = useState(false);
  const [expertiseInput, setExpertiseInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Populate form when editing
  useEffect(() => {
    if (interviewer) {
      setFormData({
        user_id: interviewer.user_id,
        department: interviewer.department || "",
        expertise: interviewer.expertise || [],
        location: interviewer.location || "",
        max_interviews_per_day: interviewer.max_interviews_per_day || 3,
        timezone: interviewer.timezone || "Asia/Ho_Chi_Minh",
        is_active: interviewer.is_active,
        availability: null,
        time_slots: interviewer.time_slots || [],
      });
    } else {
      // Reset form for new interviewer
      setFormData({
        user_id: "",
        department: "",
        expertise: [],
        location: "",
        max_interviews_per_day: 3,
        timezone: "Asia/Ho_Chi_Minh",
        is_active: true,
        availability: null,
        time_slots: [],
      });
    }
    setValidationErrors({});
  }, [interviewer, isOpen]);

  const selectedUser = Array.isArray(users)
    ? users.find((user) => user.id === Number(formData.user_id))
    : null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmitting) return;

    setIsSubmitting(true);
    setValidationErrors({});

    try {
      await onSubmit(formData);
      onClose();
    } catch (error: any) {
      if (error.errors) {
        setValidationErrors(error.errors);
      }
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addExpertise = (skill: string) => {
    if (!formData.expertise.includes(skill)) {
      setFormData((prev) => ({
        ...prev,
        expertise: [...prev.expertise, skill],
      }));
    }
    setExpertiseInput("");
    setExpertiseOpen(false);
  };

  const removeExpertise = (skill: string) => {
    setFormData((prev) => ({
      ...prev,
      expertise: prev.expertise.filter((s) => s !== skill),
    }));
  };

  const addTimeSlot = (day: string, startTime: string, endTime: string) => {
    if (!startTime || !endTime) return;

    const newSlot: TimeSlot = {
      start_time: startTime,
      end_time: endTime,
    };

    setFormData((prev) => ({
      ...prev,
    }));
  };

  const removeTimeSlot = (day: string, index: number) => {
    setFormData((prev) => ({
      ...prev,
    }));
  };

  const groupedUsers = Array.isArray(users)
    ? users.reduce(
        (acc, user) => {
          const group =
            user.role === "Hiring Manager" ? "Hiring Managers" : "Recruiters";
          if (!acc[group]) acc[group] = [];
          acc[group].push(user);
          return acc;
        },
        {} as Record<string, typeof users>,
      )
    : {};

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {interviewer
              ? "Chỉnh sửa phỏng vấn viên"
              : "Thêm phỏng vấn viên mới"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">Thông tin cơ bản</TabsTrigger>
              <TabsTrigger value="availability">Lịch trình</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              {/* User Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Người dùng <span className="text-red-500">*</span>
                </Label>
                <Popover open={userSelectOpen} onOpenChange={setUserSelectOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={userSelectOpen}
                      className={cn(
                        "w-full justify-between",
                        !selectedUser && "text-muted-foreground",
                      )}
                    >
                      {selectedUser ? (
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={selectedUser.avatar} />
                            <AvatarFallback className="text-xs">
                              {selectedUser.initials}
                            </AvatarFallback>
                          </Avatar>
                          <span className="truncate">{selectedUser.name}</span>
                          <Badge
                            variant="secondary"
                            className={cn(
                              "ml-auto text-xs",
                              selectedUser.role === "hiring_manager"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-green-100 text-green-800",
                            )}
                          >
                            {selectedUser.role === "hiring_manager"
                              ? "HM"
                              : "Recruiter"}
                          </Badge>
                        </div>
                      ) : (
                        "Chọn người dùng..."
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0 pointer-events-auto">
                    <Command>
                      <CommandInput placeholder="Tìm kiếm người dùng..." />
                      <CommandList>
                        <CommandEmpty>
                          {usersLoading
                            ? "Đang tải..."
                            : "Không tìm thấy người dùng."}
                        </CommandEmpty>
                        {Object.entries(groupedUsers).map(
                          ([groupName, groupUsers]) => (
                            <CommandGroup key={groupName} heading={groupName}>
                              {Array.isArray(groupUsers) &&
                                groupUsers.map((user) => (
                                  <CommandItem
                                    key={user.id}
                                    value={`${user.name} ${user.email}`}
                                    onSelect={() => {
                                      setFormData((prev) => ({
                                        ...prev,
                                        user_id: user.id,
                                      }));
                                      setUserSelectOpen(false);
                                    }}
                                    className="flex items-center space-x-3"
                                  >
                                    <Avatar className="h-6 w-6">
                                      <AvatarImage src={user.avatar} />
                                      <AvatarFallback
                                        className={cn(
                                          "text-xs",
                                          user.role === "hiring_manager"
                                            ? "bg-blue-100 text-blue-800"
                                            : "bg-green-100 text-green-800",
                                        )}
                                      >
                                        {user.initials}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1">
                                      <div className="flex items-center justify-between">
                                        <span className="font-medium">
                                          {user.name}
                                        </span>
                                        <Badge
                                          variant="secondary"
                                          className={cn(
                                            "text-xs",
                                            user.role === "hiring_manager"
                                              ? "bg-blue-100 text-blue-800"
                                              : "bg-green-100 text-green-800",
                                          )}
                                        >
                                          {user.role === "hiring_manager"
                                            ? "HM"
                                            : "Recruiter"}
                                        </Badge>
                                      </div>
                                      <div className="text-sm text-muted-foreground flex items-center space-x-2">
                                        <Mail className="w-3 h-3" />
                                        <span>{user.email}</span>
                                      </div>
                                    </div>
                                    <Check
                                      className={cn(
                                        "ml-auto h-4 w-4",
                                        formData.user_id === user.id
                                          ? "opacity-100"
                                          : "opacity-0",
                                      )}
                                    />
                                  </CommandItem>
                                ))}
                            </CommandGroup>
                          ),
                        )}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {validationErrors.user_id && (
                  <p className="text-sm text-red-500">
                    {validationErrors.user_id[0]}
                  </p>
                )}
              </div>

              {/* Department */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Phòng ban</Label>
                <Select
                  value={formData.department}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, department: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn phòng ban" />
                  </SelectTrigger>
                  <SelectContent>
                    {DEPARTMENTS.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.department && (
                  <p className="text-sm text-red-500">
                    {validationErrors.department[0]}
                  </p>
                )}
              </div>

              {/* Location */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Địa điểm</Label>
                <Input
                  value={formData.location}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      location: e.target.value,
                    }))
                  }
                  placeholder="Nhập địa điểm làm việc"
                />
                {validationErrors.location && (
                  <p className="text-sm text-red-500">
                    {validationErrors.location[0]}
                  </p>
                )}
              </div>

              {/* Max Interviews Per Day */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Số buổi phỏng vấn tối đa mỗi ngày
                </Label>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={formData.max_interviews_per_day}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      max_interviews_per_day: parseInt(e.target.value) || 1,
                    }))
                  }
                />
                {validationErrors.max_interviews_per_day && (
                  <p className="text-sm text-red-500">
                    {validationErrors.max_interviews_per_day[0]}
                  </p>
                )}
              </div>

              {/* Timezone */}
              <div className="space-y-2 hidden">
                <Label className="text-sm font-medium">Múi giờ</Label>
                <Select
                  value={formData.timezone}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, timezone: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {TIMEZONES.map((tz) => (
                      <SelectItem key={tz} value={tz}>
                        {tz}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.timezone && (
                  <p className="text-sm text-red-500">
                    {validationErrors.timezone[0]}
                  </p>
                )}
              </div>

              {/* Active Status */}
              <div className="flex items-center justify-between space-x-2">
                <Label className="text-sm font-medium">
                  Trạng thái hoạt động
                </Label>
                <Switch
                  checked={formData.is_active}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, is_active: checked }))
                  }
                />
              </div>

              {/* Expertise Section - Consolidated into Basic Tab */}
              <div className="border-t pt-4 space-y-4">
                <h3 className="text-lg font-semibold">Chuyên môn</h3>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Thêm chuyên môn</Label>
                  <Popover open={expertiseOpen} onOpenChange={setExpertiseOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        className="w-full justify-between"
                      >
                        Thêm chuyên môn...
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0 pointer-events-auto">
                      <Command>
                        <CommandInput
                          placeholder="Tìm kiếm hoặc nhập chuyên môn..."
                          value={expertiseInput}
                          onValueChange={setExpertiseInput}
                        />
                        <CommandList>
                          {expertiseInput && (
                            <CommandGroup>
                              <CommandItem
                                onSelect={() => addExpertise(expertiseInput)}
                              >
                                <Plus className="mr-2 h-4 w-4" />
                                Thêm "{expertiseInput}"
                              </CommandItem>
                            </CommandGroup>
                          )}
                          <CommandGroup heading="Chuyên môn phổ biến">
                            {COMMON_EXPERTISE.filter(
                              (skill) =>
                                !formData.expertise.includes(skill) &&
                                skill
                                  .toLowerCase()
                                  .includes(expertiseInput.toLowerCase()),
                            ).map((skill) => (
                              <CommandItem
                                key={skill}
                                onSelect={() => addExpertise(skill)}
                              >
                                {skill}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  {validationErrors.expertise && (
                    <p className="text-sm text-red-500">
                      {validationErrors.expertise[0]}
                    </p>
                  )}
                </div>

                {/* Selected Expertise */}
                {formData.expertise.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      Chuyên môn đã chọn ({formData.expertise.length})
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      {Array.isArray(formData.expertise) &&
                        formData.expertise.map((skill, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="gap-1"
                          >
                            {skill}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-auto p-0 text-muted-foreground hover:text-foreground"
                              onClick={() => removeExpertise(skill)}
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </Badge>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="availability" className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Lịch trình làm việc
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Thiết lập khung giờ có thể phỏng vấn cho từng ngày trong
                    tuần
                  </p>
                </div>

                {DAYS_OF_WEEK.map((day) => (
                  <Card key={day.key}>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">{day.label}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Select
                          onValueChange={(startTime) => {
                            const endTimeSelect = document.querySelector(
                              `[data-end-time="${day.key}"]`,
                            ) as HTMLSelectElement;
                            const endTime = endTimeSelect?.value;
                            if (endTime && startTime < endTime) {
                              addTimeSlot(day.key, startTime, endTime);
                            }
                          }}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue placeholder="Từ" />
                          </SelectTrigger>
                          <SelectContent>
                            {TIME_SLOTS.map((time) => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <span className="text-sm text-muted-foreground">
                          đến
                        </span>

                        <Select>
                          <SelectTrigger
                            className="w-32"
                            data-end-time={day.key}
                          >
                            <SelectValue placeholder="Đến" />
                          </SelectTrigger>
                          <SelectContent>
                            {TIME_SLOTS.map((time) => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // This is handled by the first Select's onValueChange
                          }}
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting || isLoading}>
              {isSubmitting
                ? "Đang xử lý..."
                : interviewer
                  ? "Cập nhật"
                  : "Tạo mới"}
            </Button>
          </div>

          {validationErrors.form && (
            <div className="text-sm text-red-500 text-center">
              {validationErrors.form[0]}
            </div>
          )}
        </form>
      </DialogContent>
    </Dialog>
  );
}
