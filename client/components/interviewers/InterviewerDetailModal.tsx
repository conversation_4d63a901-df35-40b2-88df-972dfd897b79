import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Building2,
  Clock,
  Calendar,
  Star,
  TrendingUp,
  Users,
  CheckCircle,
  UserCheck,
  UserX,
  Edit,
  BarChart3,
  Award,
  Target,
  Globe,
} from "lucide-react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { Interviewer, DAYS_OF_WEEK } from "@/lib/types/interviewer";
import { useInterviews } from "@/domains/candidates/hooks";

interface InterviewerDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  interviewer: Interviewer | null;
  onEdit?: () => void;
}

export function InterviewerDetailModal({
  isOpen,
  onClose,
  interviewer,
  onEdit,
}: InterviewerDetailModalProps) {
  // Get interviewer's recent interviews (hook must be called before any conditional returns)
  const { data: interviewsData } = useInterviews({
    interviewer_id: interviewer?.id || 0,
    per_page: 10,
    sort: "date",
  });

  const recentInterviews = interviewsData?.data || [];

  if (!interviewer) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="w-12 h-12">
                <AvatarImage src={interviewer.user.avatar} />
                <AvatarFallback className="text-lg">
                  {interviewer.user.initials}
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-xl font-semibold">{interviewer.name}</h2>
                <p className="text-muted-foreground">
                  {interviewer.user.title} • {interviewer.department}
                </p>
              </div>
            </div>
            {onEdit && (
              <Button onClick={onEdit} className="gap-2">
                <Edit className="w-4 h-4" />
                Chỉnh sửa
              </Button>
            )}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="schedule">Lịch trình</TabsTrigger>
            <TabsTrigger value="performance">Hiệu suất</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Status and Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <User className="w-4 h-4" />
                    Thông tin cơ bản
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Trạng thái
                    </span>
                    <Badge
                      variant={interviewer.is_active ? "default" : "secondary"}
                      className={
                        interviewer.is_active
                          ? "bg-green-100 text-green-800"
                          : ""
                      }
                    >
                      {interviewer.is_active ? (
                        <span key="active" className="flex items-center">
                          <UserCheck className="w-3 h-3 mr-1" />
                          Hoạt động
                        </span>
                      ) : (
                        <span key="inactive" className="flex items-center">
                          <UserX className="w-3 h-3 mr-1" />
                          Không hoạt động
                        </span>
                      )}
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="w-4 h-4 text-muted-foreground" />
                      <span>{interviewer.email}</span>
                    </div>
                    {interviewer.user.phone && (
                      <div
                        key="phone"
                        className="flex items-center gap-2 text-sm"
                      >
                        <Phone className="w-4 h-4 text-muted-foreground" />
                        <span>{interviewer.user.phone}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-sm">
                      <Building2 className="w-4 h-4 text-muted-foreground" />
                      <span>{interviewer.department}</span>
                    </div>
                    {interviewer.location && (
                      <div
                        key="location"
                        className="flex items-center gap-2 text-sm"
                      >
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <span>{interviewer.location}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-sm hidden">
                      <Globe className="w-4 h-4 text-muted-foreground" />
                      <span>{interviewer.timezone}</span>
                    </div>
                  </div>

                  <div className="pt-2 border-t">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Tham gia từ</span>
                      <span>
                        {format(
                          new Date(interviewer.created_at),
                          "dd/MM/yyyy",
                          {
                            locale: vi,
                          },
                        )}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <BarChart3 className="w-4 h-4" />
                    Thống kê nhanh
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-blue-600">
                        {interviewer.statistics?.total_interviews || 0}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Tổng phỏng vấn
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-green-600">
                        {interviewer.statistics?.upcoming_interviews || 0}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Sắp tới
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-yellow-600">
                        {interviewer.statistics?.average_rating?.toFixed(1) ||
                          "N/A"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Đánh giá TB
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-purple-600">
                        {interviewer.max_interviews_per_day}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Tối đa/ngày
                      </div>
                    </div>
                  </div>

                  {interviewer.statistics && (
                    <div className="pt-2 border-t">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">
                          Tỷ lệ hoàn thành
                        </span>
                        <span className="font-medium">
                          {interviewer.statistics.total_interviews > 0
                            ? Math.round(
                                (interviewer.statistics.completed_interviews /
                                  interviewer.statistics.total_interviews) *
                                  100,
                              )
                            : 0}
                          %
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Recent Interviews */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  Phỏng vấn gần đây
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recentInterviews.length === 0 ? (
                  <div className="text-center py-8">
                    <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Chưa có phỏng vấn nào
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {Array.isArray(recentInterviews) &&
                      recentInterviews
                        .slice(0, 5)
                        .map((interview: any, index: number) => (
                          <div
                            key={interview?.id || `interview-${index}`}
                            className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                                <Calendar className="w-4 h-4 text-primary" />
                              </div>
                              <div>
                                <div className="font-medium">
                                  {interview.candidate?.name || "N/A"}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {interview.job_posting?.title || "N/A"}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {format(
                                    new Date(
                                      interview.date || interview.scheduled_at,
                                    ),
                                    "dd/MM/yyyy HH:mm",
                                    { locale: vi },
                                  )}
                                </div>
                              </div>
                            </div>
                            <Badge
                              variant={
                                interview.status === "completed"
                                  ? "default"
                                  : interview.status === "scheduled"
                                    ? "secondary"
                                    : "outline"
                              }
                            >
                              {interview.status === "completed"
                                ? "Hoàn thành"
                                : interview.status === "scheduled"
                                  ? "Đã lên lịch"
                                  : interview.status}
                            </Badge>
                          </div>
                        ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Expertise Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Award className="w-4 h-4" />
                  Chuyên môn (
                  {Array.isArray(interviewer.expertise)
                    ? interviewer.expertise.length
                    : 0}
                  )
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!Array.isArray(interviewer.expertise) ||
                interviewer.expertise.length === 0 ? (
                  <div className="text-center py-6">
                    <Award className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-muted-foreground text-sm">
                      Chưa có thông tin chuyên môn
                    </p>
                  </div>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {Array.isArray(interviewer.expertise) &&
                      interviewer.expertise.map((skill, index) => (
                        <Badge
                          key={`expertise-${index}-${skill}`}
                          variant="secondary"
                          className="text-sm"
                        >
                          {skill}
                        </Badge>
                      ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Lịch trình làm việc
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {DAYS_OF_WEEK.map((day) => {
                  const daySchedule =
                    interviewer.availability[
                      day.key as keyof typeof interviewer.availability
                    ] || [];

                  return (
                    <div
                      key={day.key}
                      className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                    >
                      <div className="font-medium">{day.label}</div>
                      <div className="flex flex-wrap gap-2">
                        {daySchedule.length === 0 ? (
                          <Badge variant="outline" className="text-xs">
                            Không có lịch
                          </Badge>
                        ) : (
                          Array.isArray(daySchedule) &&
                          daySchedule.map((slot, index) => (
                            <Badge
                              key={`${day.key}-slot-${index}-${slot.start_time}-${slot.end_time}`}
                              variant="secondary"
                              className="text-xs"
                            >
                              {slot.start_time} - {slot.end_time}
                            </Badge>
                          ))
                        )}
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            {/* Available Time Slots */}
            {Array.isArray(interviewer.time_slots) &&
              interviewer.time_slots.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Target className="w-4 h-4" />
                      Khung giờ có thể phỏng vấn (
                      {interviewer.time_slots.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {Array.isArray(interviewer.time_slots) &&
                        interviewer.time_slots.map((slot, index) => (
                          <Badge
                            key={`timeslot-${index}-${slot}`}
                            variant="outline"
                            className="text-sm"
                          >
                            {slot}
                          </Badge>
                        ))}
                    </div>
                  </CardContent>
                </Card>
              )}
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            {interviewer.statistics ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-blue-600" />
                        Tổng phỏng vấn
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-blue-600">
                        {interviewer.statistics.total_interviews}
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        Đã thực hiện
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        Hoàn thành
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-green-600">
                        {interviewer.statistics.completed_interviews}
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        Thành công
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-600" />
                        Đánh giá
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-yellow-600">
                        {interviewer.statistics.average_rating.toFixed(1)}
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        / 5.0 điểm
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">
                      Phân tích hiệu su��t
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Tỷ lệ hoàn thành</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className="h-full bg-green-600"
                              style={{
                                width: `${Math.round(
                                  (interviewer.statistics.completed_interviews /
                                    interviewer.statistics.total_interviews) *
                                    100,
                                )}%`,
                              }}
                            />
                          </div>
                          <span className="text-sm font-medium">
                            {Math.round(
                              (interviewer.statistics.completed_interviews /
                                interviewer.statistics.total_interviews) *
                                100,
                            )}
                            %
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm">Mức độ hài lòng</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className="h-full bg-yellow-600"
                              style={{
                                width: `${(interviewer.statistics.average_rating / 5) * 100}%`,
                              }}
                            />
                          </div>
                          <span className="text-sm font-medium">
                            {(
                              (interviewer.statistics.average_rating / 5) *
                              100
                            ).toFixed(0)}
                            %
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm">Hiệu suất tháng này</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className="h-full bg-blue-600"
                              style={{
                                width: `${Math.min(
                                  (interviewer.statistics.upcoming_interviews /
                                    interviewer.max_interviews_per_day) *
                                    100,
                                  100,
                                )}%`,
                              }}
                            />
                          </div>
                          <span className="text-sm font-medium">
                            {interviewer.statistics.upcoming_interviews}/
                            {interviewer.max_interviews_per_day}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    Chưa có dữ li��u hiệu suất
                  </h3>
                  <p className="text-muted-foreground">
                    Dữ liệu hiệu suất sẽ được hiển thị sau khi có phỏng vấn đầu
                    tiên
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
