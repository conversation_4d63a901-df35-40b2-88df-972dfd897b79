import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>, Monitor, Palette } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/components/theme-provider";
import { useTranslation } from "@/lib/i18n";
import { cn } from "@/lib/utils";

export function FloatingThemeToggle() {
  const { theme, setTheme } = useTheme();
  const { language } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const checkTheme = () => {
      if (theme === "dark") {
        setIsDarkMode(true);
      } else if (theme === "system") {
        const systemIsDark = window.matchMedia(
          "(prefers-color-scheme: dark)",
        ).matches;
        setIsDarkMode(systemIsDark);
      } else {
        setIsDarkMode(false);
      }
    };

    checkTheme();

    if (theme === "system") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      mediaQuery.addEventListener("change", checkTheme);
      return () => mediaQuery.removeEventListener("change", checkTheme);
    }
  }, [theme]);

  const themes = [
    {
      name: "light",
      icon: Sun,
      label: language === "vi" ? "Sáng" : "Light",
    },
    {
      name: "dark",
      icon: Moon,
      label: language === "vi" ? "Tối" : "Dark",
    },
    {
      name: "system",
      icon: Monitor,
      label: language === "vi" ? "Hệ thống" : "System",
    },
  ];

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className="relative">
        {/* Theme Options */}
        <div
          className={cn(
            "absolute bottom-16 right-0 flex flex-col gap-2 transition-all duration-300 ease-out",
            isExpanded
              ? "opacity-100 scale-100 translate-y-0"
              : "opacity-0 scale-75 translate-y-4 pointer-events-none",
          )}
        >
          {themes.map((themeOption, index) => {
            const Icon = themeOption.icon;
            const isActive = theme === themeOption.name;

            return (
              <Button
                key={themeOption.name}
                variant={isActive ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setTheme(themeOption.name as any);
                  setIsExpanded(false);
                }}
                className={cn(
                  "theme-transition w-auto px-3 py-2 gap-2 backdrop-blur-md",
                  "transform transition-all duration-300 ease-out",
                  `delay-[${index * 50}ms]`,
                  isActive && "scale-105 shadow-lg",
                )}
                style={{
                  transitionDelay: isExpanded
                    ? `${index * 50}ms`
                    : `${(2 - index) * 50}ms`,
                }}
              >
                <Icon className="h-4 w-4" />
                <span className="text-sm">{themeOption.label}</span>
              </Button>
            );
          })}
        </div>

        {/* Main Toggle Button */}
        <Button
          onClick={() => setIsExpanded(!isExpanded)}
          size="sm"
          className={cn(
            "theme-transition w-12 h-12 rounded-full shadow-lg backdrop-blur-md",
            "hover:scale-110 active:scale-95",
            "border-2 transition-all duration-300",
            isDarkMode
              ? "bg-primary border-primary/50 hover:border-primary"
              : "bg-background border-border hover:border-primary/50",
            isExpanded && "rotate-180",
          )}
          title={language === "vi" ? "Chuyển đổi giao diện" : "Toggle theme"}
        >
          {isExpanded ? (
            <Palette className="h-5 w-5" />
          ) : theme === "light" ? (
            <Sun className="h-5 w-5" />
          ) : theme === "dark" ? (
            <Moon className="h-5 w-5" />
          ) : (
            <Monitor className="h-5 w-5" />
          )}
        </Button>
      </div>
    </div>
  );
}
