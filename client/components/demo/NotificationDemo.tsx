import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useNotifications } from "@/hooks/use-notifications";
import { useTranslation } from "@/lib/i18n";
import { <PERSON>Circle, AlertTriangle, XCircle, Info, Bell } from "lucide-react";

export const NotificationDemo = () => {
  const notifications = useNotifications();
  const { t } = useTranslation();

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          Demo Thông báo Vietnamese / Vietnamese Notifications Demo
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {/* Success Notifications */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-green-600 flex items-center gap-1">
              <CheckCircle className="w-4 h-4" />
              Thành công
            </h4>
            <div className="space-y-1">
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.success.saved()}
              >
                Đã lưu
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.success.created()}
              >
                Đã tạo
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.success.updated()}
              >
                Đã cập nhật
              </Button>
            </div>
          </div>

          {/* Error Notifications */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-red-600 flex items-center gap-1">
              <XCircle className="w-4 h-4" />
              Lỗi
            </h4>
            <div className="space-y-1">
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.error.failed()}
              >
                Thất bại
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.error.network()}
              >
                Lỗi mạng
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.error.validation()}
              >
                Không hợp lệ
              </Button>
            </div>
          </div>

          {/* Warning Notifications */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-yellow-600 flex items-center gap-1">
              <AlertTriangle className="w-4 h-4" />
              Cảnh báo
            </h4>
            <div className="space-y-1">
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.warning.unsaved()}
              >
                Chưa lưu
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.warning.duplicate()}
              >
                Trùng lặp
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.warning.limit()}
              >
                Đạt giới hạn
              </Button>
            </div>
          </div>

          {/* Info Notifications */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-blue-600 flex items-center gap-1">
              <Info className="w-4 h-4" />
              Thông tin
            </h4>
            <div className="space-y-1">
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.info.loading()}
              >
                Đang tải
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.info.processing()}
              >
                Đang xử lý
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="w-full text-xs"
                onClick={() => notifications.info.syncing()}
              >
                Đang đồng bộ
              </Button>
            </div>
          </div>
        </div>

        {/* Custom notification example */}
        <div className="mt-4 pt-4 border-t">
          <Button
            variant="default"
            className="w-full"
            onClick={() => {
              notifications.showSuccess(
                "created",
                "Ứng viên mới đã được thêm thành công vào hệ thống!",
              );
            }}
          >
            Thông báo tùy chỉnh / Custom Notification
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
