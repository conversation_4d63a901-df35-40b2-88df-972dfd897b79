import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  usePageTitle, 
  useSimplePageTitle, 
  useContextPageTitle 
} from "@/hooks/usePageTitle";
import { useTranslation } from "@/lib/i18n";

/**
 * Demo component to showcase dynamic page title functionality
 * This component can be used for testing and demonstration purposes
 */
export const PageTitleDemo: React.FC = () => {
  const { t, language, setLanguage } = useTranslation();
  const [currentDemo, setCurrentDemo] = useState<string>("candidates");
  const [contextValue, setContextValue] = useState<string>("John Doe");

  // Example usage of different page title hooks
  const demoTitles = {
    candidates: () => useSimplePageTitle("pageTitle.candidates.list"),
    candidateDetail: () => useContextPageTitle(
      "pageTitle.candidates.detail", 
      "name", 
      contextValue
    ),
    dashboard: () => useSimplePageTitle("pageTitle.dashboard"),
    jobs: () => useSimplePageTitle("pageTitle.jobs.list"),
    jobDetail: () => useContextPageTitle(
      "pageTitle.jobs.detail", 
      "title", 
      "Frontend Developer"
    ),
    messages: () => useSimplePageTitle("pageTitle.messages.inbox"),
    analytics: () => useSimplePageTitle("pageTitle.analytics"),
  };

  // Set the current demo title
  React.useEffect(() => {
    const titleFunction = demoTitles[currentDemo as keyof typeof demoTitles];
    if (titleFunction) {
      titleFunction();
    }
  }, [currentDemo, contextValue, language]);

  const handleLanguageToggle = () => {
    setLanguage(language === 'vi' ? 'en' : 'vi');
  };

  return (
    <div className="p-6 space-y-6 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Dynamic Page Title Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Language Toggle */}
          <div className="flex items-center gap-4">
            <span className="font-medium">Current Language:</span>
            <Badge variant="outline">{language === 'vi' ? 'Vietnamese' : 'English'}</Badge>
            <Button onClick={handleLanguageToggle} variant="outline" size="sm">
              Switch to {language === 'vi' ? 'English' : 'Vietnamese'}
            </Button>
          </div>

          {/* Current Page Title Display */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600 mb-1">Current Page Title:</p>
            <p className="text-lg font-semibold">{document.title}</p>
          </div>

          {/* Demo Controls */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Try Different Page Titles:</h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {Object.keys(demoTitles).map((titleKey) => (
                <Button
                  key={titleKey}
                  variant={currentDemo === titleKey ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentDemo(titleKey)}
                  className="capitalize"
                >
                  {titleKey.replace(/([A-Z])/g, ' $1').trim()}
                </Button>
              ))}
            </div>
          </div>

          {/* Context Value Input */}
          {(currentDemo === 'candidateDetail') && (
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Candidate Name (for context):
              </label>
              <input
                type="text"
                value={contextValue}
                onChange={(e) => setContextValue(e.target.value)}
                className="w-full p-2 border rounded-md"
                placeholder="Enter candidate name..."
              />
            </div>
          )}

          {/* Translation Examples */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Translation Examples:</h3>
            <div className="grid gap-2 text-sm">
              <div className="flex justify-between">
                <span className="font-medium">Dashboard:</span>
                <span>{language === 'vi' ? 'Trang chủ' : 'Dashboard'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Candidates:</span>
                <span>{language === 'vi' ? 'Ứng viên' : 'Candidates'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Job Postings:</span>
                <span>{language === 'vi' ? 'Tin tuyển dụng' : 'Job Postings'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Analytics:</span>
                <span>{language === 'vi' ? 'Phân tích & Báo cáo' : 'Analytics & Reports'}</span>
              </div>
            </div>
          </div>

          {/* Implementation Notes */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">Implementation Notes:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Page titles update automatically when language changes</li>
              <li>• Context variables like {`{name}`} and {`{title}`} are dynamically replaced</li>
              <li>• SEO-friendly titles follow pattern: "[Context] - [Feature] - HireFlow ATS"</li>
              <li>• Meta descriptions are automatically generated for better SEO</li>
              <li>• Works with React Router navigation</li>
            </ul>
          </div>

          {/* Hook Usage Examples */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Hook Usage Examples:</h3>
            
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-gray-100 rounded">
                <div className="font-mono text-green-600 mb-1">// Simple page title</div>
                <div className="font-mono">useSimplePageTitle("pageTitle.dashboard");</div>
              </div>
              
              <div className="p-3 bg-gray-100 rounded">
                <div className="font-mono text-green-600 mb-1">// Page title with context</div>
                <div className="font-mono">
                  useContextPageTitle("pageTitle.candidates.detail", "name", candidateName);
                </div>
              </div>
              
              <div className="p-3 bg-gray-100 rounded">
                <div className="font-mono text-green-600 mb-1">// Advanced usage with multiple context</div>
                <div className="font-mono">
                  {`usePageTitle({
  titleKey: "pageTitle.jobs.detail",
  context: { title: jobTitle },
  additionalContext: "Active Position"
});`}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PageTitleDemo;
