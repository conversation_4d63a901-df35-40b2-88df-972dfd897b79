import { forwardRef } from "react";
import { cn } from "@/lib/utils";
import { FormActions, FormActionsProps } from "./form-actions";

interface FormContainerProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode;
  actions?: FormActionsProps;
  actionsClassName?: string;
}

const FormContainer = forwardRef<HTMLFormElement, FormContainerProps>(
  ({ children, actions, actionsClassName, className, ...props }, ref) => {
    return (
      <form
        ref={ref}
        className={cn("space-y-6", className)}
        {...props}
      >
        {children}
        {actions && (
          <FormActions
            {...actions}
            className={cn(actions.className, actionsClassName)}
          />
        )}
      </form>
    );
  }
);

FormContainer.displayName = "FormContainer";

export { FormContainer, type FormContainerProps };
