import { Button, ButtonProps } from "./button";
import { SubmitButton, SubmitButtonProps } from "./submit-button";
import { cn } from "@/lib/utils";

interface FormActionsProps {
  onCancel?: () => void;
  cancelText?: string;
  submitText?: string;
  submitLoadingText?: string;
  isSubmitting?: boolean;
  disableCancelWhileSubmitting?: boolean;
  className?: string;
  cancelButtonProps?: Omit<ButtonProps, "onClick" | "disabled">;
  submitButtonProps?: Omit<SubmitButtonProps, "isLoading" | "loadingText">;
}

export const FormActions = ({
  onCancel,
  cancelText = "Cancel",
  submitText = "Submit",
  submitLoadingText = "Submitting...",
  isSubmitting = false,
  disableCancelWhileSubmitting = true,
  className,
  cancelButtonProps,
  submitButtonProps,
}: FormActionsProps) => {
  return (
    <div className={cn("flex gap-3 pt-6 border-t", className)}>
      {onCancel && (
        <Button
          type="button"
          variant="outline"
          disabled={disableCancelWhileSubmitting && isSubmitting}
          onClick={onCancel}
          className="flex-1 rounded-xl"
          {...cancelButtonProps}
        >
          {cancelText}
        </Button>
      )}
      <SubmitButton
        isLoading={isSubmitting}
        loadingText={submitLoadingText}
        className="flex-1 ai-button"
        {...submitButtonProps}
      >
        {submitText}
      </SubmitButton>
    </div>
  );
};

export type { FormActionsProps };
