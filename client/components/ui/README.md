# Reusable Form Components

This directory contains reusable UI components for forms that provide consistent loading states and user feedback.

## Components

### SubmitButton

A submit button with built-in loading state.

```tsx
import { SubmitButton } from "@/components/ui/submit-button";

<SubmitButton 
  isLoading={isSubmitting}
  loadingText="Saving..."
>
  Save Changes
</SubmitButton>
```

### LoadingButton

A more flexible button with loading states for any action.

```tsx
import { LoadingButton } from "@/components/ui/loading-button";
import { Save } from "lucide-react";

<LoadingButton 
  loading={isLoading}
  loadingText="Saving..."
  icon={<Save className="w-4 h-4" />}
  onClick={handleSave}
>
  Save Document
</LoadingButton>
```

### FormActions

Complete form action bar with cancel and submit buttons.

```tsx
import { FormActions } from "@/components/ui/form-actions";

<FormActions
  onCancel={handleClose}
  cancelText="Cancel"
  submitText="Create User"
  submitLoadingText="Creating..."
  isSubmitting={isSubmitting}
  disableCancelWhileSubmitting={true}
/>
```

### FormContainer

Wrapper for forms with integrated actions.

```tsx
import { FormContainer } from "@/components/ui/form-container";

<FormContainer 
  onSubmit={handleSubmit(onSubmit)}
  actions={{
    onCancel: handleClose,
    cancelText: "Cancel",
    submitText: "Submit",
    submitLoadingText: "Submitting...",
    isSubmitting: isSubmitting,
  }}
>
  {/* Form fields */}
  <Input {...register("name")} />
  <Input {...register("email")} />
</FormContainer>
```

## Usage Examples

### Basic Form with Loading State

```tsx
const [isSubmitting, setIsSubmitting] = useState(false);

const onSubmit = async (data) => {
  setIsSubmitting(true);
  try {
    await api.createUser(data);
    onSuccess();
  } catch (error) {
    console.error(error);
  } finally {
    setIsSubmitting(false);
  }
};

return (
  <form onSubmit={handleSubmit(onSubmit)}>
    {/* form fields */}
    <FormActions
      onCancel={onClose}
      submitText="Create User"
      submitLoadingText="Creating..."
      isSubmitting={isSubmitting}
    />
  </form>
);
```

### Custom Loading Button

```tsx
<LoadingButton
  loading={isDeleting}
  loadingText="Deleting..."
  variant="destructive"
  onClick={handleDelete}
>
  Delete Item
</LoadingButton>
```

## Features

- ✅ Prevents double submission
- ✅ Visual loading feedback
- ✅ Consistent styling across forms
- ✅ Accessible button states
- ✅ Customizable text and styles
- ✅ TypeScript support
