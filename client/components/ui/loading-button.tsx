import { forwardRef } from "react";
import { Loader2 } from "lucide-react";
import { Button, ButtonProps } from "./button";
import { cn } from "@/lib/utils";

interface LoadingButtonProps extends ButtonProps {
  loading?: boolean;
  loadingText?: string;
  icon?: React.ReactNode;
  loadingIcon?: React.ReactNode;
}

const LoadingButton = forwardRef<HTMLButtonElement, LoadingButtonProps>(
  (
    {
      children,
      loading = false,
      loadingText,
      icon,
      loadingIcon = <Loader2 className="w-4 h-4 animate-spin" />,
      disabled,
      className,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading;
    const displayText = loading && loadingText ? loadingText : children;
    const displayIcon = loading ? loadingIcon : icon;

    return (
      <Button
        ref={ref}
        disabled={isDisabled}
        className={cn("relative", className)}
        {...props}
      >
        {displayIcon && (
          <span className={cn("mr-2", loading && "animate-pulse")}>
            {displayIcon}
          </span>
        )}
        {displayText}
      </Button>
    );
  }
);

LoadingButton.displayName = "LoadingButton";

export { LoadingButton, type LoadingButtonProps };
