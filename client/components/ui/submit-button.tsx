import { forwardRef } from "react";
import { Loader2 } from "lucide-react";
import { Button, ButtonProps } from "./button";
import { cn } from "@/lib/utils";

interface SubmitButtonProps extends Omit<ButtonProps, "type" | "disabled"> {
  isLoading?: boolean;
  loadingText?: string;
  disableWhileLoading?: boolean;
}

const SubmitButton = forwardRef<HTMLButtonElement, SubmitButtonProps>(
  (
    {
      children,
      isLoading = false,
      loadingText = "Loading...",
      disableWhileLoading = true,
      className,
      ...props
    },
    ref
  ) => {
    return (
      <Button
        ref={ref}
        type="submit"
        disabled={disableWhileLoading && isLoading}
        className={cn("relative", className)}
        {...props}
      >
        {isLoading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            {loadingText}
          </>
        ) : (
          children
        )}
      </Button>
    );
  }
);

SubmitButton.displayName = "SubmitButton";

export { SubmitButton, type SubmitButtonProps };
