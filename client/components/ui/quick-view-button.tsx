import React from "react";
import { Button } from "@/components/ui/button";
import { ExternalLink, User, Briefcase } from "lucide-react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";

interface QuickViewButtonProps {
  type: "candidate" | "job";
  id: string;
  name?: string;
  variant?: "default" | "outline" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  showIcon?: boolean;
  showText?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const QuickViewButton: React.FC<QuickViewButtonProps> = ({
  type,
  id,
  name,
  variant = "outline",
  size = "sm",
  showIcon = true,
  showText = true,
  className,
  children,
}) => {
  const getPath = () => {
    return type === "candidate"
      ? `/candidates/detail/${id}`
      : `/jobs/detail/${id}`;
  };

  const getIcon = () => {
    if (!showIcon) return null;
    return type === "candidate" ? (
      <User className="w-3 h-3" />
    ) : (
      <Briefcase className="w-3 h-3" />
    );
  };

  const getText = () => {
    if (children) return children;
    if (!showText) return null;
    if (name) return name;
    return type === "candidate" ? "View Candidate" : "View Job";
  };

  const iconOnly = !showText && !children;

  return (
    <Button
      variant={variant}
      size={size}
      asChild
      className={cn(iconOnly ? "px-2" : "px-3", "h-7 text-xs gap-1", className)}
    >
      <Link to={getPath()}>
        {getIcon()}
        {!iconOnly && <span>{getText()}</span>}
        {!iconOnly && showIcon && (
          <ExternalLink className="w-3 h-3 ml-1 opacity-60" />
        )}
      </Link>
    </Button>
  );
};

export default QuickViewButton;
