import React, { useState, useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Calendar,
  Clock,
  MapPin,
  User,
  Video,
  Phone,
  Users,
  Search,
  Filter,
  MoreVertical,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCcw,
  Building,
} from "lucide-react";
import { format, isToday, isTomorrow, isYesterday } from "date-fns";
import { vi } from "date-fns/locale";
import { Interview } from "@/data/mockData";
import { useTranslation } from "@/lib/i18n";
import { cn } from "@/lib/utils";

interface InterviewListViewProps {
  interviews: Interview[];
  onInterviewClick: (interview: Interview) => void;
  onStatusChange?: (
    interviewId: string,
    newStatus: Interview["status"],
  ) => void;
  isLoading?: boolean;
}

export const InterviewListView: React.FC<InterviewListViewProps> = ({
  interviews,
  onInterviewClick,
  onStatusChange,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<
    "date" | "candidate" | "interviewer" | "status"
  >("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");

  // Filter and sort interviews
  const filteredAndSortedInterviews = useMemo(() => {
    let filtered = interviews;

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (interview) =>
          interview.candidateName.toLowerCase().includes(searchLower) ||
          interview.interviewer.toLowerCase().includes(searchLower) ||
          interview.jobTitle.toLowerCase().includes(searchLower) ||
          interview.notes.toLowerCase().includes(searchLower),
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(
        (interview) => interview.status === statusFilter,
      );
    }

    // Apply type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter((interview) => interview.type === typeFilter);
    }

    // Sort interviews
    filtered.sort((a, b) => {
      let compareValue = 0;

      switch (sortBy) {
        case "date":
          const dateA = new Date(`${a.date} ${a.time}`);
          const dateB = new Date(`${b.date} ${b.time}`);
          compareValue = dateA.getTime() - dateB.getTime();
          break;
        case "candidate":
          compareValue = a.candidateName.localeCompare(b.candidateName);
          break;
        case "interviewer":
          compareValue = a.interviewer.localeCompare(b.interviewer);
          break;
        case "status":
          compareValue = a.status.localeCompare(b.status);
          break;
      }

      return sortOrder === "asc" ? compareValue : -compareValue;
    });

    return filtered;
  }, [interviews, searchTerm, sortBy, sortOrder, statusFilter, typeFilter]);

  // Group interviews by date
  const groupedInterviews = useMemo(() => {
    const groups: Record<string, Interview[]> = {};

    filteredAndSortedInterviews.forEach((interview) => {
      const date = interview.date;
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(interview);
    });

    return groups;
  }, [filteredAndSortedInterviews]);

  const getStatusIcon = (status: Interview["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-emerald-600" />;
      case "cancelled":
        return <XCircle className="w-4 h-4 text-red-600" />;
      case "rescheduled":
        return <RotateCcw className="w-4 h-4 text-orange-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-blue-600" />;
    }
  };

  const getStatusLabel = (status: Interview["status"]) => {
    switch (status) {
      case "scheduled":
        return "Đã lên lịch";
      case "completed":
        return "Hoàn thành";
      case "cancelled":
        return "Đã hủy";
      case "rescheduled":
        return "Dời lịch";
      default:
        return status;
    }
  };

  const getTypeIcon = (type: Interview["type"]) => {
    switch (type) {
      case "video":
        return <Video className="w-4 h-4 text-purple-600" />;
      case "phone":
        return <Phone className="w-4 h-4 text-green-600" />;
      case "in-person":
        return <Users className="w-4 h-4 text-blue-600" />;
      default:
        return <Calendar className="w-4 h-4" />;
    }
  };

  const getDateLabel = (dateString: string) => {
    const date = new Date(dateString);

    if (isToday(date)) {
      return "Hôm nay";
    } else if (isTomorrow(date)) {
      return "Ngày mai";
    } else if (isYesterday(date)) {
      return "Hôm qua";
    }

    return format(date, "EEEE, dd/MM/yyyy", { locale: vi });
  };

  const toggleSort = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(newSortBy);
      setSortOrder("asc");
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-muted h-10 w-10"></div>
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-muted rounded w-1/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Tìm kiếm ứng viên, người phỏng vấn, vị trí..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 rounded-xl"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px] rounded-xl">
              <SelectValue placeholder="Trạng thái" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả</SelectItem>
              <SelectItem value="scheduled">Đã lên lịch</SelectItem>
              <SelectItem value="completed">Hoàn thành</SelectItem>
              <SelectItem value="cancelled">Đã hủy</SelectItem>
              <SelectItem value="rescheduled">Dời lịch</SelectItem>
            </SelectContent>
          </Select>

          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[120px] rounded-xl">
              <SelectValue placeholder="Loại" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả</SelectItem>
              <SelectItem value="video">Video</SelectItem>
              <SelectItem value="phone">Điện thoại</SelectItem>
              <SelectItem value="in-person">Trực tiếp</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={`${sortBy}-${sortOrder}`}
            onValueChange={(value) => {
              const [newSortBy, newSortOrder] = value.split("-") as [
                typeof sortBy,
                typeof sortOrder,
              ];
              setSortBy(newSortBy);
              setSortOrder(newSortOrder);
            }}
          >
            <SelectTrigger className="w-[140px] rounded-xl">
              <SelectValue placeholder="Sắp xếp" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date-asc">Ngày (cũ→mới)</SelectItem>
              <SelectItem value="date-desc">Ngày (mới→cũ)</SelectItem>
              <SelectItem value="candidate-asc">Ứng viên (A→Z)</SelectItem>
              <SelectItem value="candidate-desc">Ứng viên (Z→A)</SelectItem>
              <SelectItem value="interviewer-asc">Người PV (A→Z)</SelectItem>
              <SelectItem value="interviewer-desc">Người PV (Z→A)</SelectItem>
              <SelectItem value="status-asc">Trạng thái (A→Z)</SelectItem>
              <SelectItem value="status-desc">Trạng thái (Z→A)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          Hiển thị {filteredAndSortedInterviews.length} / {interviews.length}{" "}
          cuộc phỏng vấn
        </span>
        {(searchTerm || statusFilter !== "all" || typeFilter !== "all") && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSearchTerm("");
              setStatusFilter("all");
              setTypeFilter("all");
            }}
            className="h-auto p-1 text-xs"
          >
            Xóa bộ lọc
          </Button>
        )}
      </div>

      {/* Interview List */}
      <div className="space-y-6">
        {Object.keys(groupedInterviews).length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Không có cuộc phỏng vấn nào
              </h3>
              <p className="text-muted-foreground">
                {searchTerm || statusFilter !== "all" || typeFilter !== "all"
                  ? "Không tìm thấy cuộc phỏng vấn phù hợp với bộ lọc"
                  : "Chưa có lịch phỏng vấn nào được tạo"}
              </p>
            </CardContent>
          </Card>
        ) : (
          Object.entries(groupedInterviews)
            .sort(([dateA], [dateB]) => dateA.localeCompare(dateB))
            .map(([date, dayInterviews]) => (
              <div key={date} className="space-y-3">
                {/* Date Header */}
                <div className="flex items-center gap-3">
                  <h3 className="text-lg font-semibold text-foreground">
                    {getDateLabel(date)}
                  </h3>
                  <Badge variant="outline" className="text-xs">
                    {dayInterviews.length} cuộc
                  </Badge>
                  <div className="flex-1 h-px bg-border"></div>
                </div>

                {/* Day's Interviews */}
                <div className="space-y-3">
                  {dayInterviews.map((interview) => (
                    <Card
                      key={interview.id}
                      className={cn(
                        "",
                        interview.status === "completed" &&
                          "border-l-emerald-500 ",
                        interview.status === "cancelled" && "border-l-red-500 ",
                        interview.status === "rescheduled" &&
                          "border-l-orange-500 ",
                        interview.status === "scheduled" &&
                          "border-l-blue-500 ",
                      )}
                      onClick={() => onInterviewClick(interview)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-4 flex-1">
                            {/* Time and Type */}
                            <div className="flex flex-col items-center gap-1 min-w-[60px]">
                              <span className="text-lg font-semibold text-foreground">
                                {interview.time}
                              </span>
                              <div className="flex items-center gap-1">
                                {getTypeIcon(interview.type)}
                                <span className="text-xs text-muted-foreground">
                                  {interview.duration}min
                                </span>
                              </div>
                            </div>

                            {/* Main Content */}
                            <div className="flex-1 space-y-2">
                              {/* Candidate and Job */}
                              <div className="flex items-center gap-3">
                                <Avatar className="h-8 w-8">
                                  <AvatarFallback className="text-xs bg-primary/10 text-primary">
                                    {interview.candidateName
                                      .split(" ")
                                      .map((n) => n[0])
                                      .join("")
                                      .slice(0, 2)}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <h4 className="font-semibold text-foreground">
                                    {interview.candidateName}
                                  </h4>
                                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                                    <Building className="w-3 h-3" />
                                    {interview.jobTitle}
                                  </p>
                                </div>
                              </div>

                              {/* Interviewer and Location */}
                              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                <div className="flex items-center gap-1">
                                  <User className="w-3 h-3" />
                                  {interview.interviewer}
                                </div>
                                {interview.location && (
                                  <div className="flex items-center gap-1">
                                    <MapPin className="w-3 h-3" />
                                    {interview.location}
                                  </div>
                                )}
                              </div>

                              {/* Notes Preview */}
                              {interview.notes && (
                                <p className="text-sm text-muted-foreground line-clamp-1">
                                  {interview.notes}
                                </p>
                              )}
                            </div>
                          </div>

                          {/* Status and Actions */}
                          <div className="flex items-start gap-2">
                            <Badge
                              variant={
                                interview.status === "completed"
                                  ? "default"
                                  : interview.status === "cancelled"
                                    ? "destructive"
                                    : interview.status === "rescheduled"
                                      ? "secondary"
                                      : "outline"
                              }
                              className="flex items-center gap-1"
                            >
                              {getStatusIcon(interview.status)}
                              {getStatusLabel(interview.status)}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))
        )}
      </div>
    </div>
  );
};
