import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar as CalendarIcon, 
  Clock, 
  CheckCircle, 
  Video, 
  BarChart3, 
  Zap,
  TrendingUp,
  Users
} from 'lucide-react';

interface CalendarStats {
  total: number;
  today: number;
  scheduled: number;
  completed: number;
  cancelled: number;
  rescheduled: number;
  video: number;
  phone: number;
  inPerson: number;
  completionRate: string;
}

interface CalendarSummaryCardProps {
  stats: CalendarStats;
  className?: string;
}

export const CalendarSummaryCard: React.FC<CalendarSummaryCardProps> = ({ 
  stats, 
  className = "" 
}) => {
  const summaryItems = [
    {
      icon: CalendarIcon,
      label: "Tháng này",
      value: stats.total,
      color: "primary",
      bg: "bg-gradient-to-br from-primary/5 to-emerald-500/5",
      border: "border-primary/20"
    },
    {
      icon: Clock,
      label: "Hôm nay",
      value: stats.today,
      color: "blue-600",
      bg: "bg-gradient-to-br from-blue-500/5 to-indigo-500/5",
      border: "border-blue-500/20"
    },
    {
      icon: CheckCircle,
      label: "<PERSON>àn thành",
      value: stats.completed,
      color: "emerald-600",
      bg: "bg-gradient-to-br from-emerald-500/5 to-green-500/5",
      border: "border-emerald-500/20"
    },
    {
      icon: Video,
      label: "Video",
      value: stats.video,
      color: "purple-600",
      bg: "bg-gradient-to-br from-purple-500/5 to-pink-500/5",
      border: "border-purple-500/20"
    },
    {
      icon: BarChart3,
      label: "Tỷ lệ HT",
      value: `${stats.completionRate}%`,
      color: "orange-600",
      bg: "bg-gradient-to-br from-orange-500/5 to-red-500/5",
      border: "border-orange-500/20"
    },
    {
      icon: Zap,
      label: "Đã hủy",
      value: stats.cancelled,
      color: "red-600",
      bg: "bg-gradient-to-br from-red-500/5 to-pink-500/5",
      border: "border-red-500/20"
    }
  ];

  return (
    <div className={`grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 ${className}`}>
      {summaryItems.map((item, index) => {
        const Icon = item.icon;
        return (
          <Card key={index} className={`${item.border} ${item.bg} hover:shadow-md transition-shadow`}>
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center gap-2 sm:gap-3">
                <div className={`p-1.5 sm:p-2 rounded-lg ${item.color === 'primary' ? 'bg-primary/20' : `bg-${item.color}/20`}`}>
                  <Icon className={`h-3 w-3 sm:h-4 sm:w-4 ${item.color === 'primary' ? 'text-primary' : `text-${item.color}`}`} />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm sm:text-lg font-bold truncate">
                    {item.value}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {item.label}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default CalendarSummaryCard;
