import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  ChevronLeft,
  ChevronRight,
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Calendar as CalendarIcon,
  CheckCircle,
  XCircle,
  AlertCircle,
  MoreHorizontal,
} from "lucide-react";
import { Interview } from "@/data/mockData";

interface MonthlyCalendarProps {
  interviews: Interview[];
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  onInterviewClick: (interview: Interview) => void;
}

const getStatusIcon = (status: Interview["status"]) => {
  switch (status) {
    case "completed":
      return <CheckCircle className="w-3 h-3 text-green-600" />;
    case "cancelled":
      return <XCircle className="w-3 h-3 text-red-600" />;
    case "rescheduled":
      return <AlertCircle className="w-3 h-3 text-yellow-600" />;
    default:
      return <Clock className="w-3 h-3 text-blue-600" />;
  }
};

const getTypeIcon = (type: Interview["type"]) => {
  switch (type) {
    case "video":
      return <Video className="w-3 h-3" />;
    case "phone":
      return <Phone className="w-3 h-3" />;
    case "in-person":
      return <MapPin className="w-3 h-3" />;
    default:
      return <User className="w-3 h-3" />;
  }
};

const getStatusColor = (status: Interview["status"]) => {
  switch (status) {
    case "completed":
      return "bg-green-100 text-green-800 border-green-200";
    case "cancelled":
      return "bg-red-100 text-red-800 border-red-200";
    case "rescheduled":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    default:
      return "bg-blue-100 text-blue-800 border-blue-200";
  }
};

export const MonthlyCalendar = ({
  interviews,
  selectedDate,
  onDateChange,
  onInterviewClick,
}: MonthlyCalendarProps) => {
  console.log("=== MonthlyCalendar received interviews ===");
  console.log("Interviews count:", interviews.length);
  console.log("Sample interview:", interviews[0]);
  console.log("=== End MonthlyCalendar debug ===");

  const [currentMonth, setCurrentMonth] = useState(new Date());

  const firstDayOfMonth = new Date(
    currentMonth.getFullYear(),
    currentMonth.getMonth(),
    1,
  );
  const lastDayOfMonth = new Date(
    currentMonth.getFullYear(),
    currentMonth.getMonth() + 1,
    0,
  );
  const firstDayOfWeek = firstDayOfMonth.getDay();
  const daysInMonth = lastDayOfMonth.getDate();

  const previousMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1),
    );
  };

  const nextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1),
    );
  };

  const goToToday = () => {
    const today = new Date();
    setCurrentMonth(today);
    onDateChange(today);
  };

  const getInterviewsForDate = (date: Date) => {
    const dateStr = date.toISOString().split("T")[0];
    const matchingInterviews = interviews.filter((interview) => {
      const matches = interview.date === dateStr;
      if (interviews.length > 0) {
        console.log(
          `Date ${dateStr}: interview.date=${interview.date} matches=${matches}`,
        );
      }
      return matches;
    });
    if (matchingInterviews.length > 0) {
      console.log(
        `Found ${matchingInterviews.length} interviews for ${dateStr}`,
      );
    }
    return matchingInterviews;
  };

  const generateCalendarDays = () => {
    const days = [];
    const today = new Date();
    const todayStr = today.toISOString().split("T")[0];

    // Previous month's trailing days
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(firstDayOfMonth);
      date.setDate(date.getDate() - (i + 1));
      days.push({
        date,
        isCurrentMonth: false,
        interviews: [],
      });
    }

    // Current month's days
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(
        currentMonth.getFullYear(),
        currentMonth.getMonth(),
        day,
      );
      const dayInterviews = getInterviewsForDate(date);

      days.push({
        date,
        isCurrentMonth: true,
        interviews: dayInterviews,
        isToday: date.toISOString().split("T")[0] === todayStr,
        isSelected:
          date.toISOString().split("T")[0] ===
          selectedDate.toISOString().split("T")[0],
      });
    }

    // Next month's leading days
    const remainingCells = 42 - days.length; // 6 rows × 7 days
    for (let day = 1; day <= remainingCells; day++) {
      const date = new Date(
        currentMonth.getFullYear(),
        currentMonth.getMonth() + 1,
        day,
      );
      days.push({
        date,
        isCurrentMonth: false,
        interviews: [],
      });
    }

    return days;
  };

  const calendarDays = generateCalendarDays();
  const weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const InterviewPopover = ({
    interviews,
    date,
  }: {
    interviews: Interview[];
    date: Date;
  }) => (
    <Popover>
      <PopoverTrigger asChild>
        <div className="cursor-pointer">
          {interviews.length > 2 ? (
            <div className="flex items-center gap-1 mt-1">
              <div className="text-xs text-muted-foreground">
                {interviews.length} interviews
              </div>
              <MoreHorizontal className="w-3 h-3 text-muted-foreground" />
            </div>
          ) : (
            <div className="space-y-1 mt-1">
              {interviews.slice(0, 2).map((interview) => (
                <div
                  key={interview.id}
                  className={`text-xs px-2 py-1 rounded border ${getStatusColor(interview.status)} truncate cursor-pointer hover:opacity-80 transition-opacity relative`}
                  onClick={() => onInterviewClick(interview)}
                >
                  {/* Priority indicator */}
                  {interview.jobPriority === "urgent" && (
                    <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-red-500 rounded-full"></div>
                  )}
                  {interview.jobPriority === "high" && (
                    <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-orange-500 rounded-full"></div>
                  )}

                  <div className="flex items-center gap-1">
                    {getStatusIcon(interview.status)}
                    {getTypeIcon(interview.type)}
                    <span className="truncate font-medium">
                      {interview.time}
                    </span>
                    <span className="truncate">{interview.candidateName}</span>
                  </div>
                  {interview.jobTitle &&
                    interview.jobTitle !== interview.candidateName && (
                      <div className="text-xs text-muted-foreground truncate mt-0.5 pl-4">
                        {interview.jobTitle}
                      </div>
                    )}
                </div>
              ))}
            </div>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="p-4 border-b">
          <h4 className="font-semibold flex items-center gap-2">
            <CalendarIcon className="w-4 h-4" />
            {date.toLocaleDateString("en-US", {
              weekday: "long",
              month: "long",
              day: "numeric",
            })}
          </h4>
          <p className="text-sm text-muted-foreground">
            {interviews.length} interview(s) scheduled
          </p>
        </div>
        <div className="max-h-64 overflow-y-auto">
          {interviews.map((interview) => (
            <div
              key={interview.id}
              className="p-3 border-b last:border-b-0 hover:bg-muted/50 cursor-pointer transition-colors"
              onClick={() => onInterviewClick(interview)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1 flex-wrap">
                    <span className="font-medium text-sm">
                      {interview.candidateName}
                    </span>
                    <Badge
                      variant="secondary"
                      className={`text-xs ${getStatusColor(interview.status)}`}
                    >
                      {interview.status}
                    </Badge>
                    {interview.jobPriority &&
                      interview.jobPriority !== "medium" && (
                        <Badge
                          variant={
                            interview.jobPriority === "urgent"
                              ? "destructive"
                              : interview.jobPriority === "high"
                                ? "default"
                                : "outline"
                          }
                          className="text-xs h-4 px-1"
                        >
                          {interview.jobPriority}
                        </Badge>
                      )}
                    {interview.round && interview.round > 1 && (
                      <Badge variant="outline" className="text-xs h-4 px-1">
                        Round {interview.round}
                      </Badge>
                    )}
                  </div>
                  {interview.jobTitle && (
                    <div className="text-xs text-muted-foreground mb-1">
                      <span className="font-medium">{interview.jobTitle}</span>
                      {interview.jobDepartment && (
                        <span className="ml-1">
                          • {interview.jobDepartment}
                        </span>
                      )}
                      {interview.candidateRating > 0 && (
                        <span className="ml-1 text-yellow-600">
                          ⭐ {interview.candidateRating}/5
                        </span>
                      )}
                    </div>
                  )}
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span className="font-medium">{interview.time}</span>
                      <span>({interview.duration}min)</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {getTypeIcon(interview.type)}
                      <span className="capitalize">{interview.type}</span>
                      {interview.interviewType && (
                        <span className="ml-1 capitalize text-xs bg-muted px-1 rounded">
                          {interview.interviewType}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      <span className="truncate">{interview.interviewer}</span>
                      {interview.interviewerDepartment && (
                        <span className="text-xs">
                          ({interview.interviewerDepartment})
                        </span>
                      )}
                    </div>
                    {interview.meetingLink && (
                      <div className="flex items-center gap-1 text-blue-600">
                        <Video className="w-3 h-3" />
                        <span>Meeting Link Available</span>
                      </div>
                    )}
                    {interview.candidateRating > 0 && (
                      <div className="flex items-center gap-1">
                        <span className="text-yellow-500">⭐</span>
                        <span>{interview.candidateRating}/5</span>
                        {interview.candidateAiScore > 0 && (
                          <span className="ml-1 text-xs">
                            AI: {interview.candidateAiScore}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div className="ml-2">{getStatusIcon(interview.status)}</div>
              </div>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );

  return (
    <div className="space-y-4">
      {/* Calendar Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold">
            {currentMonth.toLocaleDateString("en-US", {
              month: "long",
              year: "numeric",
            })}
          </h2>
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={previousMonth}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={nextMonth}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <Button variant="outline" size="sm" onClick={goToToday}>
          Today
        </Button>
      </div>

      {/* Calendar Grid */}
      <Card>
        <CardContent className="p-0">
          <div className="grid grid-cols-7 border-b">
            {weekDays.map((day) => (
              <div
                key={day}
                className="p-3 text-center text-sm font-medium text-muted-foreground border-r last:border-r-0"
              >
                {day}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-7">
            {calendarDays.map((day, index) => (
              <div
                key={index}
                className={`min-h-[120px] p-2 border-r border-b last:border-r-0 ${
                  day.isCurrentMonth
                    ? "bg-background"
                    : "bg-muted/30 text-muted-foreground"
                } ${
                  day.isToday
                    ? "bg-primary/5 border-primary/20"
                    : "hover:bg-muted/50"
                } ${
                  day.isSelected ? "ring-2 ring-primary ring-offset-1" : ""
                } transition-colors cursor-pointer`}
                onClick={() => day.isCurrentMonth && onDateChange(day.date)}
              >
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span
                      className={`text-sm ${
                        day.isToday
                          ? "font-bold text-primary bg-primary/10 w-6 h-6 rounded-full flex items-center justify-center"
                          : day.isCurrentMonth
                            ? "font-medium"
                            : "text-muted-foreground"
                      }`}
                    >
                      {day.date.getDate()}
                    </span>
                    {day.interviews.length > 0 && (
                      <Badge
                        variant="secondary"
                        className="text-xs h-5 px-1.5 bg-primary/10 text-primary"
                      >
                        {day.interviews.length}
                      </Badge>
                    )}
                  </div>

                  {day.interviews.length > 0 && (
                    <InterviewPopover
                      interviews={day.interviews}
                      date={day.date}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
        <div className="flex items-center gap-1">
          <CheckCircle className="w-3 h-3 text-green-600" />
          <span>Completed</span>
        </div>
        <div className="flex items-center gap-1">
          <Clock className="w-3 h-3 text-blue-600" />
          <span>Scheduled</span>
        </div>
        <div className="flex items-center gap-1">
          <AlertCircle className="w-3 h-3 text-yellow-600" />
          <span>Rescheduled</span>
        </div>
        <div className="flex items-center gap-1">
          <XCircle className="w-3 h-3 text-red-600" />
          <span>Cancelled</span>
        </div>
        <div className="ml-auto flex items-center gap-2">
          <Video className="w-3 h-3" />
          <Phone className="w-3 h-3" />
          <MapPin className="w-3 h-3" />
          <span>Video / Phone / In-person</span>
        </div>
      </div>
    </div>
  );
};
