import { useState, useEffect, use<PERSON>emo } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  CalendarIcon,
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Briefcase,
  Check,
  ChevronsUpDown,
  Wand2,
  Send,
  Plus,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Building2,
  Star,
  Users,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  useCandidates,
  useJobs,
  useActiveInterviewersDropdown,
  useScheduleInterview,
  useCheckInterviewerAvailability,
} from "@/hooks/useApi";
import {
  SchedulingFormData,
  InterviewCreateRequest,
  INTERVIEW_TYPES,
  INTERVIEW_CATEGORIES,
  DURATION_OPTIONS,
  TIME_SLOTS,
  ValidationErrors,
} from "@/lib/types/interview";

interface EnhancedScheduleInterviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  selectedDate?: Date;
  preSelectedCandidateId?: string;
  preSelectedJobId?: string;
  preSelectedInterviewerId?: string;
}

export function EnhancedScheduleInterviewModal({
  isOpen,
  onClose,
  onSuccess,
  selectedDate,
  preSelectedCandidateId,
  preSelectedJobId,
  preSelectedInterviewerId,
}: EnhancedScheduleInterviewModalProps) {
  const [formData, setFormData] = useState<SchedulingFormData>({
    candidate_id: preSelectedCandidateId || "",
    job_posting_id: preSelectedJobId || "",
    interviewer_id: preSelectedInterviewerId || "",
    date: selectedDate || new Date(),
    time: "",
    duration: 60,
    type: "video",
    interview_type: "technical",
    location: "",
    meeting_link: "",
    meeting_password: "",
    notes: "",
    agenda: [],
    round: 1,
  });

  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {},
  );
  const [candidateOpen, setCandidateOpen] = useState(false);
  const [jobOpen, setJobOpen] = useState(false);
  const [interviewerOpen, setInterviewerOpen] = useState(false);
  const [datePickerOpen, setDatePickerOpen] = useState(false);
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);
  const [availabilityStatus, setAvailabilityStatus] = useState<{
    checked: boolean;
    available: boolean;
    message: string;
  }>({ checked: false, available: false, message: "" });

  // API hooks
  const { data: candidatesData, isLoading: loadingCandidates } = useCandidates({
    per_page: 100,
    status: "interview", // Only candidates in interview stage
    sort: "name",
  });

  const { data: jobsData, isLoading: loadingJobs } = useJobs({
    per_page: 100,
    status: "active", // Only active jobs
    sort: "title",
  });

  const { data: interviewersData, isLoading: loadingInterviewers } =
    useActiveInterviewersDropdown();

  const scheduleInterviewMutation = useScheduleInterview();
  const checkAvailabilityMutation = useCheckInterviewerAvailability();

  // Transform API data to options
  const candidateOptions = useMemo(() => {
    if (!candidatesData?.data) return [];
    return candidatesData.data.map((candidate: any) => ({
      id: candidate.id.toString(),
      name: candidate.name,
      email: candidate.email,
      position: candidate.position,
      status: candidate.status,
      initials: candidate.name
        .split(" ")
        .map((n: string) => n[0])
        .join(""),
      rating: candidate.rating,
      ai_score: candidate.ai_score,
      job_posting_id: candidate.job_posting_id,
      job_title: candidate.job_posting?.title,
    }));
  }, [candidatesData]);

  const jobOptions = useMemo(() => {
    if (!jobsData?.data) return [];
    return jobsData.data.map((job: any) => ({
      id: job.id.toString(),
      title: job.title,
      department: job.department,
      location: job.location,
      status: job.status,
      priority: job.priority,
      hiring_manager: job.hiring_manager_name,
      recruiter: job.recruiter_name,
    }));
  }, [jobsData]);

  const interviewerOptions = useMemo(() => {
    if (!interviewersData) return [];
    return interviewersData.map((interviewer: any) => ({
      id: interviewer.value.toString(),
      name: interviewer.label.split(" (")[0], // Extract name from label
      email: "", // Not provided in dropdown format
      department: interviewer.department,
      expertise: interviewer.expertise || [],
      title: "Interviewer",
      initials:
        interviewer.initials ||
        interviewer.label
          .split(" ")
          .map((n: string) => n[0])
          .join(""),
      is_active: interviewer.isActive,
      max_interviews_per_day: interviewer.maxPerDay,
      upcoming_interviews: interviewer.upcomingInterviews || 0,
      availability_slots: [],
      avatar: interviewer.avatar,
    }));
  }, [interviewersData]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        candidate_id: preSelectedCandidateId || "",
        job_posting_id: preSelectedJobId || "",
        interviewer_id: preSelectedInterviewerId || "",
        date: selectedDate || new Date(),
        time: "",
        duration: 60,
        type: "video",
        interview_type: "technical",
        location: "",
        meeting_link: "",
        meeting_password: "",
        notes: "",
        agenda: [],
        round: 1,
      });
      setValidationErrors({});
      setAvailabilityStatus({ checked: false, available: false, message: "" });
    }
  }, [
    isOpen,
    selectedDate,
    preSelectedCandidateId,
    preSelectedJobId,
    preSelectedInterviewerId,
  ]);

  // Auto-suggest location for in-person interviews
  useEffect(() => {
    if (formData.type === "in-person" && !formData.location) {
      setFormData((prev) => ({
        ...prev,
        location: "Phòng họp A, Tầng 5",
      }));
    }
  }, [formData.type]);

  // Check interviewer availability when all required fields are filled
  useEffect(() => {
    if (
      formData.interviewer_id &&
      formData.date &&
      formData.time &&
      formData.duration
    ) {
      checkAvailability();
    }
  }, [
    formData.interviewer_id,
    formData.date,
    formData.time,
    formData.duration,
  ]);

  const checkAvailability = async () => {
    if (!formData.interviewer_id || !formData.time) return;

    try {
      const result = await checkAvailabilityMutation.mutateAsync({
        interviewer_id: formData.interviewer_id,
        date: format(formData.date, "yyyy-MM-dd"),
        time: formData.time,
        duration: formData.duration,
      });

      if (result.status === "success") {
        setAvailabilityStatus({
          checked: true,
          available: result.data.is_available,
          message: result.data.is_available
            ? "Người phỏng vấn có thể tham gia"
            : "Người phỏng vấn không có thời gian",
        });
      }
    } catch (error) {
      setAvailabilityStatus({
        checked: true,
        available: false,
        message: "Không thể kiểm tra lịch trống",
      });
    }
  };

  const validateForm = (): boolean => {
    const errors: ValidationErrors = {};

    if (!formData.candidate_id) {
      errors.candidate_id = ["Vui lòng chọn ứng viên"];
    }

    if (!formData.job_posting_id) {
      errors.job_posting_id = ["Vui lòng chọn vị trí công việc"];
    }

    if (!formData.interviewer_id) {
      errors.interviewer_id = ["Vui lòng chọn người phỏng vấn"];
    }

    if (!formData.time) {
      errors.time = ["Vui lòng chọn thời gian"];
    }

    if (formData.type === "in-person" && !formData.location) {
      errors.location = ["Vui lòng nhập địa điểm"];
    }

    if (!availabilityStatus.available && availabilityStatus.checked) {
      errors.form = ["Người phỏng vấn không có thời gian trong khung giờ này"];
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Vui lòng kiểm tra lại thông tin");
      return;
    }

    const interviewData: InterviewCreateRequest = {
      candidate_id: parseInt(formData.candidate_id),
      job_posting_id: parseInt(formData.job_posting_id),
      interviewer_id: parseInt(formData.interviewer_id),
      date: format(formData.date, "yyyy-MM-dd"),
      time: formData.time,
      duration: formData.duration,
      type: formData.type,
      interview_type: formData.interview_type,
      location: formData.type === "in-person" ? formData.location : undefined,
      meeting_link:
        formData.type === "video" ? formData.meeting_link : undefined,
      meeting_password:
        formData.type === "video" ? formData.meeting_password : undefined,
      notes: formData.notes || undefined,
      agenda: formData.agenda.length > 0 ? formData.agenda : undefined,
      round: formData.round,
      status: "scheduled",
    };

    console.log("Submitting interview data:", interviewData);

    try {
      await scheduleInterviewMutation.mutateAsync(interviewData);
      toast.success("Đã lên lịch phỏng vấn thành công!");
      onSuccess?.();
      onClose();
    } catch (error: any) {
      if (error.status === 422 && error.response?.errors) {
        setValidationErrors(error.response.errors);
      }
      toast.error("Có lỗi xảy ra khi lên lịch phỏng vấn");
    }
  };

  const selectedCandidate = candidateOptions.find(
    (c) => c.id === formData.candidate_id,
  );
  const selectedJob = jobOptions.find((j) => j.id === formData.job_posting_id);
  const selectedInterviewer = interviewerOptions.find(
    (i) => i.id === formData.interviewer_id,
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CalendarIcon className="w-5 h-5 text-primary" />
            Lên lịch phỏng vấn
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Validation Error Alert */}
          {validationErrors.form && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{validationErrors.form[0]}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Candidate Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Ứng viên <span className="text-red-500">*</span>
                </Label>
                <Popover open={candidateOpen} onOpenChange={setCandidateOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={candidateOpen}
                      className={cn(
                        "w-full justify-between rounded-xl",
                        validationErrors.candidate_id && "border-red-500",
                      )}
                    >
                      {selectedCandidate ? (
                        <div className="flex items-center gap-3">
                          <Avatar className="w-6 h-6">
                            <AvatarFallback className="text-xs bg-primary/10">
                              {selectedCandidate.initials}
                            </AvatarFallback>
                          </Avatar>
                          <div className="text-left">
                            <div className="font-medium">
                              {selectedCandidate.name}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {selectedCandidate.position}
                            </div>
                          </div>
                        </div>
                      ) : (
                        "Chọn ứng viên..."
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[400px] p-0">
                    <Command>
                      <CommandInput placeholder="Tìm kiếm ứng viên..." />
                      <CommandList>
                        {loadingCandidates ? (
                          <div className="p-4 text-center">
                            <Loader2 className="w-4 h-4 animate-spin mx-auto" />
                            <p className="text-sm text-muted-foreground mt-2">
                              Đang tải...
                            </p>
                          </div>
                        ) : candidateOptions.length === 0 ? (
                          <CommandEmpty>
                            Không tìm thấy ứng viên nào.
                          </CommandEmpty>
                        ) : (
                          <CommandGroup>
                            {candidateOptions.map((candidate) => (
                              <CommandItem
                                key={candidate.id}
                                value={`${candidate.name} ${candidate.position}`.toLowerCase()}
                                onSelect={() => {
                                  setFormData((prev) => ({
                                    ...prev,
                                    candidate_id: candidate.id,
                                    job_posting_id:
                                      candidate.job_posting_id ||
                                      prev.job_posting_id,
                                  }));
                                  setCandidateOpen(false);
                                }}
                              >
                                <div className="flex items-center gap-3 w-full">
                                  <Avatar className="w-8 h-8">
                                    <AvatarFallback className="text-xs bg-primary/10">
                                      {candidate.initials}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1">
                                    <div className="font-medium">
                                      {candidate.name}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {candidate.position}
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {candidate.rating > 0 && (
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        <Star className="w-3 h-3 mr-1" />
                                        {candidate.rating}
                                      </Badge>
                                    )}
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {candidate.status}
                                    </Badge>
                                  </div>
                                  <Check
                                    className={cn(
                                      "ml-2 h-4 w-4",
                                      formData.candidate_id === candidate.id
                                        ? "opacity-100"
                                        : "opacity-0",
                                    )}
                                  />
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        )}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {validationErrors.candidate_id && (
                  <p className="text-sm text-red-500">
                    {validationErrors.candidate_id[0]}
                  </p>
                )}
              </div>

              {/* Job Position Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Vị trí công việc <span className="text-red-500">*</span>
                </Label>
                <Popover open={jobOpen} onOpenChange={setJobOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={jobOpen}
                      className={cn(
                        "w-full justify-between rounded-xl",
                        validationErrors.job_posting_id && "border-red-500",
                      )}
                    >
                      {selectedJob ? (
                        <div className="flex items-center gap-3">
                          <Building2 className="w-4 h-4 text-primary" />
                          <div className="text-left">
                            <div className="font-medium">
                              {selectedJob.title}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {selectedJob.department} • {selectedJob.location}
                            </div>
                          </div>
                        </div>
                      ) : (
                        "Chọn vị trí công việc..."
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[400px] p-0">
                    <Command>
                      <CommandInput placeholder="Tìm kiếm công việc..." />
                      <CommandList>
                        {loadingJobs ? (
                          <div className="p-4 text-center">
                            <Loader2 className="w-4 h-4 animate-spin mx-auto" />
                            <p className="text-sm text-muted-foreground mt-2">
                              Đang tải...
                            </p>
                          </div>
                        ) : jobOptions.length === 0 ? (
                          <CommandEmpty>
                            Không tìm thấy công việc nào.
                          </CommandEmpty>
                        ) : (
                          <CommandGroup>
                            {jobOptions.map((job) => (
                              <CommandItem
                                key={job.id}
                                value={`${job.title} ${job.department}`.toLowerCase()}
                                onSelect={() => {
                                  setFormData((prev) => ({
                                    ...prev,
                                    job_posting_id: job.id,
                                  }));
                                  setJobOpen(false);
                                }}
                              >
                                <div className="flex items-center gap-3 w-full">
                                  <Building2 className="w-5 h-5 text-primary" />
                                  <div className="flex-1">
                                    <div className="font-medium">
                                      {job.title}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {job.department} • {job.location}
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Badge
                                      variant={
                                        job.priority === "urgent"
                                          ? "destructive"
                                          : job.priority === "high"
                                            ? "default"
                                            : "outline"
                                      }
                                      className="text-xs"
                                    >
                                      {job.priority}
                                    </Badge>
                                  </div>
                                  <Check
                                    className={cn(
                                      "ml-2 h-4 w-4",
                                      formData.job_posting_id === job.id
                                        ? "opacity-100"
                                        : "opacity-0",
                                    )}
                                  />
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        )}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {validationErrors.job_posting_id && (
                  <p className="text-sm text-red-500">
                    {validationErrors.job_posting_id[0]}
                  </p>
                )}
              </div>

              {/* Interviewer Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Người phỏng vấn <span className="text-red-500">*</span>
                </Label>
                <Popover
                  open={interviewerOpen}
                  onOpenChange={setInterviewerOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      className={cn(
                        "w-full justify-between rounded-xl",
                        validationErrors.interviewer_id && "border-red-500",
                      )}
                    >
                      {selectedInterviewer ? (
                        <div className="flex items-center gap-3">
                          <Avatar className="w-6 h-6">
                            <AvatarFallback className="text-xs bg-primary/10">
                              {selectedInterviewer.initials}
                            </AvatarFallback>
                          </Avatar>
                          <div className="text-left">
                            <div className="font-medium">
                              {selectedInterviewer.name}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {selectedInterviewer.title} •{" "}
                              {selectedInterviewer.department}
                            </div>
                          </div>
                        </div>
                      ) : (
                        "Chọn người phỏng vấn..."
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[400px] p-0">
                    <Command>
                      <CommandInput placeholder="Tìm kiếm người phỏng vấn..." />
                      <CommandList>
                        {loadingInterviewers ? (
                          <div className="p-4 text-center">
                            <Loader2 className="w-4 h-4 animate-spin mx-auto" />
                            <p className="text-sm text-muted-foreground mt-2">
                              Đang tải...
                            </p>
                          </div>
                        ) : interviewerOptions.length === 0 ? (
                          <CommandEmpty>
                            Không tìm thấy người phỏng vấn nào.
                          </CommandEmpty>
                        ) : (
                          <CommandGroup>
                            {interviewerOptions.map((interviewer) => (
                              <CommandItem
                                key={interviewer.id}
                                value={`${interviewer.name} ${interviewer.department}`.toLowerCase()}
                                onSelect={() => {
                                  setFormData((prev) => ({
                                    ...prev,
                                    interviewer_id: interviewer.id.toString(),
                                  }));
                                  setInterviewerOpen(false);
                                }}
                              >
                                <div className="flex items-center gap-3 w-full">
                                  <Avatar className="w-8 h-8">
                                    <AvatarFallback className="text-xs bg-primary/10">
                                      {interviewer.initials}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1">
                                    <div className="font-medium">
                                      {interviewer.name}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {interviewer.title} •{" "}
                                      {interviewer.department}
                                    </div>
                                    {Array.isArray(interviewer.expertise) &&
                                      interviewer.expertise.length > 0 && (
                                        <div className="flex flex-wrap gap-1 mt-1">
                                          {interviewer.expertise
                                            .slice(0, 3)
                                            .map((skill, index) => (
                                              <Badge
                                                key={index}
                                                variant="outline"
                                                className="text-xs h-4 px-1"
                                              >
                                                {skill}
                                              </Badge>
                                            ))}
                                        </div>
                                      )}
                                  </div>
                                  <div className="flex flex-col items-end gap-1">
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      <Users className="w-3 h-3 mr-1" />
                                      {interviewer.upcoming_interviews}
                                    </Badge>
                                  </div>
                                  <Check
                                    className={cn(
                                      "ml-2 h-4 w-4",
                                      formData.interviewer_id ===
                                        interviewer.id.toString()
                                        ? "opacity-100"
                                        : "opacity-0",
                                    )}
                                  />
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        )}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {validationErrors.interviewer_id && (
                  <p className="text-sm text-red-500">
                    {validationErrors.interviewer_id[0]}
                  </p>
                )}
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Date and Time */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Ngày <span className="text-red-500">*</span>
                  </Label>
                  <Popover
                    open={datePickerOpen}
                    onOpenChange={setDatePickerOpen}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal rounded-xl",
                          !formData.date && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.date
                          ? format(formData.date, "dd/MM/yyyy")
                          : "Chọn ngày"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={formData.date}
                        onSelect={(date) => {
                          if (date) {
                            setFormData((prev) => ({ ...prev, date }));
                            setDatePickerOpen(false);
                          }
                        }}
                        disabled={(date) => {
                          const today = new Date();
                          today.setHours(0, 0, 0, 0);
                          return date < today;
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Thời gian <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.time}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, time: value }))
                    }
                  >
                    <SelectTrigger
                      className={cn(
                        "rounded-xl",
                        validationErrors.time && "border-red-500",
                      )}
                    >
                      <SelectValue placeholder="Chọn giờ" />
                    </SelectTrigger>
                    <SelectContent>
                      {TIME_SLOTS.map((time) => (
                        <SelectItem key={time} value={time}>
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4" />
                            {time}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {validationErrors.time && (
                    <p className="text-sm text-red-500">
                      {validationErrors.time[0]}
                    </p>
                  )}
                </div>
              </div>

              {/* Duration and Interview Type */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Thời lượng</Label>
                  <Select
                    value={formData.duration.toString()}
                    onValueChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        duration: parseInt(value),
                      }))
                    }
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {DURATION_OPTIONS.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Loại phỏng vấn</Label>
                  <Select
                    value={formData.interview_type}
                    onValueChange={(value: any) =>
                      setFormData((prev) => ({
                        ...prev,
                        interview_type: value,
                      }))
                    }
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {INTERVIEW_CATEGORIES.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Interview Method */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Hình thức <span className="text-red-500">*</span>
                </Label>
                <div className="grid grid-cols-3 gap-3">
                  {INTERVIEW_TYPES.map((type) => {
                    const isSelected = formData.type === type.value;
                    return (
                      <Button
                        key={type.value}
                        type="button"
                        variant={isSelected ? "default" : "outline"}
                        className={cn(
                          "h-20 flex-col gap-2 rounded-xl",
                          isSelected && "bg-primary text-primary-foreground",
                        )}
                        onClick={() =>
                          setFormData((prev) => ({ ...prev, type: type.value }))
                        }
                      >
                        {type.icon === "Video" && <Video className="w-5 h-5" />}
                        {type.icon === "Phone" && <Phone className="w-5 h-5" />}
                        {type.icon === "MapPin" && (
                          <MapPin className="w-5 h-5" />
                        )}
                        <span className="text-sm">{type.label}</span>
                      </Button>
                    );
                  })}
                </div>
              </div>

              {/* Availability Status */}
              {availabilityStatus.checked && (
                <Alert
                  variant={
                    availabilityStatus.available ? "default" : "destructive"
                  }
                >
                  {availabilityStatus.available ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertTriangle className="h-4 w-4" />
                  )}
                  <AlertDescription>
                    {availabilityStatus.message}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          {/* Location/Meeting Link */}
          {formData.type === "in-person" && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                Địa điểm <span className="text-red-500">*</span>
              </Label>
              <Input
                placeholder="Phòng họp, địa chỉ, v.v."
                value={formData.location}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, location: e.target.value }))
                }
                className={cn(
                  "rounded-xl",
                  validationErrors.location && "border-red-500",
                )}
              />
              {validationErrors.location && (
                <p className="text-sm text-red-500">
                  {validationErrors.location[0]}
                </p>
              )}
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Ghi chú</Label>
            <Textarea
              placeholder="Ghi chú hoặc nội dung phỏng vấn..."
              value={formData.notes}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, notes: e.target.value }))
              }
              className="rounded-xl min-h-[80px]"
            />
          </div>

          {/* Actions */}
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                toast.success("Lời mời lịch đã được gửi!");
              }}
              className="gap-2 rounded-xl"
            >
              <Send className="w-4 h-4" />
              Gửi lời mời
            </Button>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="rounded-xl"
              >
                Hủy
              </Button>
              <Button
                type="submit"
                disabled={scheduleInterviewMutation.isPending}
                className="bg-primary text-primary-foreground hover:bg-primary/90 gap-2 rounded-xl"
              >
                {scheduleInterviewMutation.isPending ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Plus className="w-4 h-4" />
                )}
                Lên lịch phỏng vấn
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
