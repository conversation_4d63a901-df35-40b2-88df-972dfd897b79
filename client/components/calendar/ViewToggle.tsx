import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Grid3X3, List, Download, Calendar } from "lucide-react";
import { cn } from "@/lib/utils";

type ViewMode = "calendar" | "list";

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  totalInterviews: number;
  filteredInterviews: number;
  onExport?: () => void;
  className?: string;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  totalInterviews,
  filteredInterviews,
  onExport,
  className,
}) => {
  return (
    <div className={cn("flex items-center justify-between", className)}>
      {/* View Toggle Buttons */}
      <div className="flex items-center bg-muted/50 rounded-xl p-1">
        <Button
          variant={viewMode === "calendar" ? "default" : "ghost"}
          size="sm"
          onClick={() => onViewModeChange("calendar")}
          className={cn(
            "flex items-center gap-2 rounded-lg transition-all",
            viewMode === "calendar"
              ? "dark:bg-background dark:shadow-sm"
              : "hover:bg-background/50",
          )}
        >
          <Grid3X3 className="w-4 h-4" />
          <span className="hidden sm:inline">Lịch theo tháng</span>
          <span className="sm:hidden">Lịch</span>
        </Button>

        <Button
          variant={viewMode === "list" ? "default" : "ghost"}
          size="sm"
          onClick={() => onViewModeChange("list")}
          className={cn(
            "flex items-center gap-2 rounded-lg transition-all",
            viewMode === "list"
              ? "dark:shadow-sm dark:bg-background"
              : "hover:bg-background/50",
          )}
        >
          <List className="w-4 h-4" />
          <span className="hidden sm:inline">Danh sách</span>
          <span className="sm:hidden">DS</span>
        </Button>
      </div>

      {/* Statistics and Export */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Badge variant="outline" className="text-xs">
            <Calendar className="w-3 h-3 mr-1" />
            {filteredInterviews}/{totalInterviews}
          </Badge>

          {viewMode === "list" && (
            <span className="hidden sm:inline">cuộc phỏng vấn</span>
          )}
        </div>

        {onExport && (
          <Button
            variant="outline"
            size="sm"
            onClick={onExport}
            className="gap-2 rounded-xl"
          >
            <Download className="w-4 h-4" />
            <span className="hidden sm:inline">Xuất</span>
          </Button>
        )}
      </div>
    </div>
  );
};

export default ViewToggle;
