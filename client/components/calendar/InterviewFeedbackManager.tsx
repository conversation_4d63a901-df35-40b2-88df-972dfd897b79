import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { Badge } from "../ui/badge";
import { Alert, AlertDescription } from "../ui/alert";
import { Separator } from "../ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import {
  Star,
  Plus,
  Eye,
  Edit,
  Trash2,
  MoreVertical,
  ThumbsUp,
  ThumbsDown,
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  User,
  Calendar,
  Target,
  MessageSquare,
  TrendingUp,
  Loader2,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { safeFormatDistanceToNow } from "@/lib/utils";
import { useToast } from "../../hooks/use-toast";
import {
  useInterviewFeedbackByInterview,
  useSubmitInterviewFeedback,
  useDeleteInterviewFeedback,
} from "../../hooks/useApi";
import {
  InterviewFeedback,
  FeedbackFormData,
  getRatingLabel,
  getRatingColor,
  getScoreColor,
  getScoreLabel,
  feedbackUtils,
} from "../../lib/types/interviewFeedback";
import { InterviewFeedbackForm } from "./InterviewFeedbackForm";
import { InterviewFeedbackModal } from "./InterviewFeedbackModal";

interface InterviewFeedbackManagerProps {
  interviewId: number;
  interviewerId: number;
  interview: {
    id: number;
    candidate_name: string;
    job_title: string;
    date: string;
    time: string;
    status: string;
  };
  className?: string;
  compact?: boolean;
  autoExpandOnComplete?: boolean;
}

export const InterviewFeedbackManager: React.FC<
  InterviewFeedbackManagerProps
> = ({
  interviewId,
  interviewerId,
  interview,
  className = "",
  compact = false,
  autoExpandOnComplete = true,
}) => {
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [selectedFeedbackId, setSelectedFeedbackId] = useState<number | null>(
    null,
  );
  const [expanded, setExpanded] = useState(!compact);
  const { toast } = useToast();

  // API hooks
  const {
    data: feedbackData,
    isLoading: isLoadingFeedback,
    error: feedbackError,
    refetch: refetchFeedback,
  } = useInterviewFeedbackByInterview(interviewId, "interviewer.user");

  const submitFeedbackMutation = useSubmitInterviewFeedback();
  const deleteFeedbackMutation = useDeleteInterviewFeedback();

  const feedbackList = feedbackData?.data || [];
  const userFeedback = feedbackList.find(
    (f) => f.interviewer_id === interviewerId,
  );
  const otherFeedbacks = feedbackList.filter(
    (f) => f.interviewer_id !== interviewerId,
  );

  const handleSubmitFeedback = async (data: FeedbackFormData) => {
    try {
      const feedbackData = {
        interview_id: interviewId,
        interviewer_id: interviewerId,
        ...data,
      };

      await submitFeedbackMutation.mutateAsync(feedbackData);
      toast({
        title: "Success",
        description: "Feedback submitted successfully!",
      });
      setShowFeedbackForm(false);
      refetchFeedback();
    } catch (error: any) {
      console.error("Error submitting feedback:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to submit feedback",
        variant: "destructive",
      });
    }
  };

  const handleDeleteFeedback = async (feedbackId: number) => {
    if (!confirm("Are you sure you want to delete this feedback?")) {
      return;
    }

    try {
      await deleteFeedbackMutation.mutateAsync(feedbackId.toString());
      toast({
        title: "Success",
        description: "Feedback deleted successfully",
      });
      refetchFeedback();
    } catch (error: any) {
      console.error("Error deleting feedback:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete feedback",
        variant: "destructive",
      });
    }
  };

  const handleViewFeedback = (feedbackId: number) => {
    setSelectedFeedbackId(feedbackId);
    setShowFeedbackModal(true);
  };

  const handleEditFeedback = (feedbackId: number) => {
    setSelectedFeedbackId(feedbackId);
    setShowFeedbackModal(true);
  };

  const FeedbackSummaryCard: React.FC<{ feedback: InterviewFeedback }> = ({
    feedback,
  }) => {
    const formattedFeedback = feedbackUtils.formatFeedbackForDisplay(feedback);
    const isComplete = feedbackUtils.isFeedbackComplete(feedback);

    return (
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">
              {feedback.interviewer?.name || "Unknown Interviewer"}
            </CardTitle>
            <div className="flex items-center gap-2">
              {isComplete ? (
                <Badge variant="default" className="text-xs">
                  Complete
                </Badge>
              ) : (
                <Badge variant="outline" className="text-xs">
                  Incomplete
                </Badge>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() => handleViewFeedback(feedback.id)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </DropdownMenuItem>
                  {feedback.interviewer_id === interviewerId && (
                    <>
                      <DropdownMenuItem
                        onClick={() => handleEditFeedback(feedback.id)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteFeedback(feedback.id)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
                    <CardDescription className="text-xs">
            {safeFormatDistanceToNow(feedback.created_at, {
              addSuffix: true,
            })}
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          {/* Rating */}
          {feedback.rating && (
            <div className="flex items-center gap-2">
              <div className="flex">
                {Array.from({ length: 5 }, (_, i) => (
                  <Star
                    key={i}
                    className={`w-3 h-3 ${
                      i < feedback.rating!
                        ? "text-yellow-400 fill-current"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs text-muted-foreground">
                {feedback.rating}/5
              </span>
            </div>
          )}

          {/* Recommendation */}
          {feedback.recommend !== null && (
            <div className="flex items-center gap-2">
              {feedback.recommend ? (
                <>
                  <ThumbsUp className="w-3 h-3 text-green-600" />
                  <span className="text-xs text-green-600">Recommended</span>
                </>
              ) : (
                <>
                  <ThumbsDown className="w-3 h-3 text-red-600" />
                  <span className="text-xs text-red-600">Not Recommended</span>
                </>
              )}
            </div>
          )}

          {/* Detailed Scores */}
          {formattedFeedback.has_detailed_scores && (
            <div className="space-y-1">
              <div className="text-xs font-medium">Scores:</div>
              <div className="grid grid-cols-3 gap-2 text-xs">
                {feedback.technical_score !== null && (
                  <div className="text-center">
                    <div className="font-medium">Technical</div>
                    <div className={getScoreColor(feedback.technical_score)}>
                      {feedback.technical_score}%
                    </div>
                  </div>
                )}
                {feedback.communication_score !== null && (
                  <div className="text-center">
                    <div className="font-medium">Communication</div>
                    <div
                      className={getScoreColor(feedback.communication_score)}
                    >
                      {feedback.communication_score}%
                    </div>
                  </div>
                )}
                {feedback.cultural_fit_score !== null && (
                  <div className="text-center">
                    <div className="font-medium">Cultural</div>
                    <div className={getScoreColor(feedback.cultural_fit_score)}>
                      {feedback.cultural_fit_score}%
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Comments Preview */}
          {feedback.comments && (
            <div>
              <p className="text-xs text-muted-foreground line-clamp-2">
                {feedback.comments}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const LoadingState = () => (
    <div className="flex items-center justify-center py-4">
      <Loader2 className="w-4 h-4 animate-spin mr-2" />
      <span className="text-sm text-muted-foreground">Loading feedback...</span>
    </div>
  );

  const EmptyState = () => (
    <div className="text-center py-6">
      <MessageSquare className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
      <p className="text-sm text-muted-foreground mb-3">No feedback yet</p>
      {interview.status === "completed" && (
        <Button
          size="sm"
          onClick={() => setShowFeedbackForm(true)}
          className="gap-2"
        >
          <Plus className="w-4 h-4" />
          Submit Feedback
        </Button>
      )}
    </div>
  );

  const ActionButtons = () => {
    if (interview.status !== "completed") {
      return (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Feedback can only be submitted after the interview is completed.
          </AlertDescription>
        </Alert>
      );
    }

    if (userFeedback) {
      return (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewFeedback(userFeedback.id)}
            className="gap-2"
          >
            <Eye className="w-4 h-4" />
            View Your Feedback
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditFeedback(userFeedback.id)}
            className="gap-2"
          >
            <Edit className="w-4 h-4" />
            Edit
          </Button>
        </div>
      );
    }

    return (
      <Button
        onClick={() => setShowFeedbackForm(true)}
        className="gap-2"
        disabled={submitFeedbackMutation.isPending}
      >
        {submitFeedbackMutation.isPending ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Plus className="w-4 h-4" />
        )}
        Submit Feedback
      </Button>
    );
  };

  if (showFeedbackForm) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-500" />
            Submit Interview Feedback
          </CardTitle>
          <CardDescription>
            Provide detailed feedback for this interview
          </CardDescription>
        </CardHeader>
        <CardContent>
          <InterviewFeedbackForm
            interview={{
              id: interview.id,
              candidate_name: interview.candidate_name,
              job_title: interview.job_title,
              date: interview.date,
              time: interview.time,
              interviewer_id: interviewerId,
            }}
            existingFeedback={userFeedback}
            onSubmit={handleSubmitFeedback}
            onCancel={() => setShowFeedbackForm(false)}
            isLoading={submitFeedbackMutation.isPending}
            isEditMode={!!userFeedback}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-500" />
              Interview Feedback
              {feedbackList.length > 0 && (
                <Badge variant="outline">{feedbackList.length}</Badge>
              )}
            </CardTitle>
            {compact && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? "Collapse" : "Expand"}
              </Button>
            )}
          </div>
          <CardDescription>
            Feedback from interviewers for this session
          </CardDescription>
        </CardHeader>

        {(!compact || expanded) && (
          <CardContent className="space-y-4">
            {isLoadingFeedback ? (
              <LoadingState />
            ) : feedbackError ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Failed to load feedback. Please try again.
                </AlertDescription>
              </Alert>
            ) : feedbackList.length === 0 ? (
              <EmptyState />
            ) : (
              <div className="space-y-4">
                {/* User's Feedback */}
                {userFeedback && (
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      <User className="w-4 h-4" />
                      Your Feedback
                    </h4>
                    <FeedbackSummaryCard feedback={userFeedback} />
                  </div>
                )}

                {/* Other Feedbacks */}
                {otherFeedbacks.length > 0 && (
                  <div>
                    {userFeedback && <Separator />}
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      Other Feedback ({otherFeedbacks.length})
                    </h4>
                    <div className="space-y-2">
                      {otherFeedbacks.map((feedback) => (
                        <FeedbackSummaryCard
                          key={feedback.id}
                          feedback={feedback}
                        />
                      ))}
                    </div>
                  </div>
                )}

                <Separator />
              </div>
            )}

            <ActionButtons />
          </CardContent>
        )}
      </Card>

      {/* Feedback Modal */}
      {showFeedbackModal && selectedFeedbackId && (
        <InterviewFeedbackModal
          isOpen={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          feedbackId={selectedFeedbackId}
          mode="view"
          onFeedbackUpdated={refetchFeedback}
          onFeedbackDeleted={() => {
            refetchFeedback();
            setShowFeedbackModal(false);
          }}
        />
      )}
    </TooltipProvider>
  );
};

export default InterviewFeedbackManager;
