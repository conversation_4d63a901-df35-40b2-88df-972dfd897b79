import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  CalendarIcon,
  Clock,
  Video,
  Phone,
  MapPin,
  User,
  Briefcase,
  Check,
  ChevronsUpDown,
  Plus,
  X,
  Loader2,
} from "lucide-react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { Interview } from "@/data/mockData";
import {
  useUpdateInterview,
  useCandidates,
  useJobs,
  useInterviewers,
} from "@/hooks/useApi";
import {
  INTERVIEW_TYPES,
  INTERVIEW_CATEGORIES,
  DURATION_OPTIONS,
  TIME_SLOTS,
  SchedulingFormData,
  ValidationErrors,
} from "@/lib/types/interview";

interface EditInterviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  interview: Interview | null;
  onSuccess?: () => void;
}
export function EditInterviewModal({
  isOpen,
  onClose,
  interview,
  onSuccess,
}: EditInterviewModalProps) {
  const updateInterviewMutation = useUpdateInterview();
  const { data: candidatesResponse } = useCandidates();
  const { data: jobsResponse } = useJobs();
  const { data: interviewersResponse } = useInterviewers();

  // Safely extract data arrays with proper fallbacks
  const candidates = candidatesResponse?.data || [];
  const jobs = jobsResponse?.data || [];
  const interviewers = interviewersResponse?.data || [];

  const [formData, setFormData] = useState<SchedulingFormData>({
    candidate_id: "",
    job_posting_id: "",
    interviewer_id: "",
    date: new Date(),
    time: "",
    duration: 60,
    type: "video",
    interview_type: "screening",
    location: "",
    meeting_link: "",
    meeting_password: "",
    notes: "",
    agenda: [],
    round: 1,
  });

  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {},
  );
  const [candidateOpen, setCandidateOpen] = useState(false);
  const [jobOpen, setJobOpen] = useState(false);
  const [interviewerOpen, setInterviewerOpen] = useState(false);
  const [dateOpen, setDateOpen] = useState(false);
  const [agendaInput, setAgendaInput] = useState("");

  // Populate form when interview changes
  useEffect(() => {
    if (interview && isOpen) {
      // Populating form with interview data

      setFormData({
        candidate_id: String(interview.candidateId), // Ensure string type
        job_posting_id: String(interview.jobId || ""),
        interviewer_id: String(interview.interviewerId || ""),
        date: new Date(interview.date),
        time: interview.time,
        duration: interview.duration || 60,
        type: interview.type,
        interview_type: (interview.interviewType as any) || "screening",
        location: interview.location || "",
        meeting_link: interview.meetingLink || "",
        meeting_password: interview.meetingPassword || "",
        notes: interview.notes || "",
        agenda: interview.agenda || [],
        round: interview.round || 1,
      });
      setValidationErrors({});
    }
  }, [interview, isOpen]);

  // Calculate selected values (must be before useEffect that references them)
  const selectedCandidate = Array.isArray(candidates)
    ? candidates.find(
        (c) =>
          c.id === formData.candidate_id ||
          c.id === String(formData.candidate_id) ||
          String(c.id) === String(formData.candidate_id),
      )
    : null;

  const selectedJob = Array.isArray(jobs)
    ? jobs.find(
        (j) =>
          j.id === formData.job_posting_id ||
          j.id === String(formData.job_posting_id) ||
          String(j.id) === String(formData.job_posting_id),
      )
    : null;

  const selectedInterviewer = Array.isArray(interviewers)
    ? interviewers.find(
        (i) =>
          i.id === formData.interviewer_id ||
          i.id === String(formData.interviewer_id) ||
          String(i.id) === String(formData.interviewer_id),
      )
    : null;

  // Re-populate form when candidates data becomes available
  useEffect(() => {
    if (interview && isOpen && candidates.length > 0 && !selectedCandidate) {
      // Re-checking candidate selection after data load

      // Re-trigger form population to ensure proper data binding
      setFormData((prev) => ({
        ...prev,
        candidate_id: String(interview.candidateId),
      }));
    }
  }, [candidates, interview, isOpen, selectedCandidate, formData.candidate_id]);

  // Improved candidate selection with type handling

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!interview) return;

    setValidationErrors({});

    try {
      // Validate required fields
      if (!formData.candidate_id) {
        setValidationErrors({ candidate_id: ["Vui lòng chọn ứng viên"] });
        toast.error("Vui lòng chọn ứng viên");
        return;
      }

      if (!formData.job_posting_id) {
        setValidationErrors({
          job_posting_id: ["Vui lòng chọn vị trí tuyển dụng"],
        });
        toast.error("Vui lòng chọn vị trí tuyển dụng");
        return;
      }

      if (!formData.interviewer_id) {
        setValidationErrors({
          interviewer_id: ["Vui lòng chọn phỏng vấn viên"],
        });
        toast.error("Vui lòng chọn phỏng vấn viên");
        return;
      }

      if (!formData.time) {
        setValidationErrors({ time: ["Vui lòng chọn giờ phỏng vấn"] });
        toast.error("Vui lòng chọn giờ phỏng vấn");
        return;
      }

      // Format the data for the API
      const updateData = {
        candidate_id: Number(formData.candidate_id),
        job_posting_id: Number(formData.job_posting_id),
        interviewer_id: Number(formData.interviewer_id),
        date: format(formData.date, "yyyy-MM-dd"),
        time: formData.time,
        duration: formData.duration,
        type: formData.type,
        interview_type: formData.interview_type,
        location: formData.location || "",
        meeting_link: formData.meeting_link || "",
        meeting_password: formData.meeting_password || "",
        notes: formData.notes || "",
        agenda: formData.agenda || [],
        round: formData.round,
      };

      // Updating interview with validated data

      await updateInterviewMutation.mutateAsync({
        id: String(interview.id), // Ensure string type for ID
        data: updateData,
      });

      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Failed to update interview:", error);
      console.error("Error details:", {
        message: error.message,
        validationErrors: error.validationErrors,
        statusCode: error.statusCode,
        originalError: error,
      });

      if (error.validationErrors) {
        setValidationErrors(error.validationErrors);
        // Show specific validation errors
        const errorMessages = Object.entries(error.validationErrors)
          .map(
            ([field, messages]) =>
              `${field}: ${Array.isArray(messages) ? messages.join(", ") : messages}`,
          )
          .join("\n");
        toast.error(`Lỗi xác thực:\n${errorMessages}`);
      } else {
        toast.error(
          `Có lỗi xảy ra khi cập nhật phỏng vấn: ${error.message || "Lỗi không xác định"}`,
        );
      }
    }
  };

  const addAgendaItem = () => {
    if (agendaInput.trim()) {
      setFormData((prev) => ({
        ...prev,
        agenda: [...prev.agenda, agendaInput.trim()],
      }));
      setAgendaInput("");
    }
  };

  const removeAgendaItem = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      agenda: prev.agenda.filter((_, i) => i !== index),
    }));
  };

  if (!interview) return null;

  const isSubmitting = updateInterviewMutation.isPending;
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Chỉnh sửa phỏng vấn</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              {/* Candidate Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Ứng viên <span className="text-red-500">*</span>
                </Label>
                <Popover open={candidateOpen} onOpenChange={setCandidateOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={candidateOpen}
                      className={cn(
                        "w-full justify-between",
                        !selectedCandidate && "text-muted-foreground",
                      )}
                    >
                      {selectedCandidate ? (
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={selectedCandidate.avatar} />
                            <AvatarFallback className="text-xs">
                              {selectedCandidate.initials}
                            </AvatarFallback>
                          </Avatar>
                          <span className="truncate">
                            {selectedCandidate.name}
                          </span>
                        </div>
                      ) : formData.candidate_id && interview?.candidateName ? (
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {interview.candidateName
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <span className="truncate text-muted-foreground">
                            {interview.candidateName} (đang tải...)
                          </span>
                        </div>
                      ) : (
                        "Chọn ứng viên..."
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Tìm kiếm ứng viên..." />
                      <CommandList>
                        <CommandEmpty>Không tìm thấy ứng viên.</CommandEmpty>
                        <CommandGroup>
                          {Array.isArray(candidates) &&
                            candidates.map((candidate) => (
                              <CommandItem
                                key={candidate.id}
                                value={`${candidate.name} ${candidate.email}`}
                                onSelect={() => {
                                  setFormData((prev) => ({
                                    ...prev,
                                    candidate_id: candidate.id,
                                  }));
                                  setCandidateOpen(false);
                                }}
                                className="flex items-center space-x-3"
                              >
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={candidate.avatar} />
                                  <AvatarFallback className="text-xs">
                                    {candidate.initials}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex-1">
                                  <div className="font-medium">
                                    {candidate.name}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {candidate.position}
                                  </div>
                                </div>
                                <Check
                                  className={cn(
                                    "ml-auto h-4 w-4",
                                    formData.candidate_id === candidate.id
                                      ? "opacity-100"
                                      : "opacity-0",
                                  )}
                                />
                              </CommandItem>
                            ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {validationErrors.candidate_id && (
                  <p className="text-sm text-red-500">
                    {validationErrors.candidate_id[0]}
                  </p>
                )}
              </div>

              {/* Job Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Vị trí tuyển dụng <span className="text-red-500">*</span>
                </Label>
                <Popover open={jobOpen} onOpenChange={setJobOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={jobOpen}
                      className={cn(
                        "w-full justify-between",
                        !selectedJob && "text-muted-foreground",
                      )}
                    >
                      {selectedJob ? (
                        <div className="flex items-center space-x-2">
                          <Briefcase className="h-4 w-4" />
                          <span className="truncate">{selectedJob.title}</span>
                        </div>
                      ) : (
                        "Chọn vị trí tuyển dụng..."
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Tìm kiếm vị trí..." />
                      <CommandList>
                        <CommandEmpty>
                          Không tìm thấy vị trí tuyển dụng.
                        </CommandEmpty>
                        <CommandGroup>
                          {Array.isArray(jobs) &&
                            jobs.map((job) => (
                              <CommandItem
                                key={job.id}
                                value={`${job.title} ${job.department}`}
                                onSelect={() => {
                                  setFormData((prev) => ({
                                    ...prev,
                                    job_posting_id: job.id,
                                  }));
                                  setJobOpen(false);
                                }}
                              >
                                <div className="flex-1">
                                  <div className="font-medium">{job.title}</div>
                                  <div className="text-sm text-muted-foreground">
                                    {job.department} • {job.location}
                                  </div>
                                </div>
                                <Check
                                  className={cn(
                                    "ml-auto h-4 w-4",
                                    formData.job_posting_id === job.id
                                      ? "opacity-100"
                                      : "opacity-0",
                                  )}
                                />
                              </CommandItem>
                            ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {validationErrors.job_posting_id && (
                  <p className="text-sm text-red-500">
                    {validationErrors.job_posting_id[0]}
                  </p>
                )}
              </div>

              {/* Interviewer Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Phỏng vấn viên <span className="text-red-500">*</span>
                </Label>
                <Popover
                  open={interviewerOpen}
                  onOpenChange={setInterviewerOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={interviewerOpen}
                      className={cn(
                        "w-full justify-between",
                        !selectedInterviewer && "text-muted-foreground",
                      )}
                    >
                      {selectedInterviewer ? (
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-6 w-6">
                            <AvatarImage
                              src={selectedInterviewer.user?.avatar}
                            />
                            <AvatarFallback className="text-xs">
                              {selectedInterviewer.name
                                ?.substring(0, 2)
                                .toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span className="truncate">
                            {selectedInterviewer.name}
                          </span>
                        </div>
                      ) : (
                        "Chọn phỏng vấn viên..."
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Tìm kiếm phỏng vấn viên..." />
                      <CommandList>
                        <CommandEmpty>
                          Không tìm thấy phỏng vấn viên.
                        </CommandEmpty>
                        <CommandGroup>
                          {Array.isArray(interviewers) &&
                            interviewers.map((interviewer) => (
                              <CommandItem
                                key={interviewer.id}
                                value={`${interviewer.name} ${interviewer.email}`}
                                onSelect={() => {
                                  setFormData((prev) => ({
                                    ...prev,
                                    interviewer_id: interviewer.id.toString(),
                                  }));
                                  setInterviewerOpen(false);
                                }}
                                className="flex items-center space-x-3"
                              >
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={interviewer.user?.avatar} />
                                  <AvatarFallback className="text-xs">
                                    {interviewer.name
                                      ?.substring(0, 2)
                                      .toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex-1">
                                  <div className="font-medium">
                                    {interviewer.name}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {interviewer.department}
                                  </div>
                                </div>
                                <Check
                                  className={cn(
                                    "ml-auto h-4 w-4",
                                    formData.interviewer_id ===
                                      interviewer.id.toString()
                                      ? "opacity-100"
                                      : "opacity-0",
                                  )}
                                />
                              </CommandItem>
                            ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {validationErrors.interviewer_id && (
                  <p className="text-sm text-red-500">
                    {validationErrors.interviewer_id[0]}
                  </p>
                )}
              </div>

              {/* Date and Time */}
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Ngày <span className="text-red-500">*</span>
                  </Label>
                  <Popover open={dateOpen} onOpenChange={setDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.date && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.date ? (
                          format(formData.date, "dd/MM/yyyy", { locale: vi })
                        ) : (
                          <span>Chọn ngày</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={formData.date}
                        onSelect={(date) => {
                          if (date) {
                            setFormData((prev) => ({ ...prev, date }));
                            setDateOpen(false);
                          }
                        }}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  {validationErrors.date && (
                    <p className="text-sm text-red-500">
                      {validationErrors.date[0]}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Giờ <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.time}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, time: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn giờ" />
                    </SelectTrigger>
                    <SelectContent>
                      {TIME_SLOTS.map((time) => (
                        <SelectItem key={time} value={time}>
                          <div className="flex items-center space-x-2">
                            <Clock className="w-4 h-4" />
                            <span>{time}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {validationErrors.time && (
                    <p className="text-sm text-red-500">
                      {validationErrors.time[0]}
                    </p>
                  )}
                </div>
              </div>

              {/* Duration */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Thời lượng</Label>
                <Select
                  value={formData.duration.toString()}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      duration: parseInt(value),
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {DURATION_OPTIONS.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {/* Interview Type */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Hình thức phỏng vấn
                </Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: any) =>
                    setFormData((prev) => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {INTERVIEW_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center space-x-2">
                          {type.value === "video" && (
                            <Video className="w-4 h-4" />
                          )}
                          {type.value === "phone" && (
                            <Phone className="w-4 h-4" />
                          )}
                          {type.value === "in-person" && (
                            <MapPin className="w-4 h-4" />
                          )}
                          <span>{type.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Interview Category */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Loại phỏng vấn</Label>
                <Select
                  value={formData.interview_type}
                  onValueChange={(value: any) =>
                    setFormData((prev) => ({ ...prev, interview_type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {INTERVIEW_CATEGORIES.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Location/Meeting Link */}
              {formData.type === "in-person" ? (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Địa điểm</Label>
                  <Input
                    value={formData.location}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        location: e.target.value,
                      }))
                    }
                    placeholder="Nhập địa điểm phỏng vấn"
                  />
                </div>
              ) : (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Link cuộc họp</Label>
                  <Input
                    value={formData.meeting_link}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        meeting_link: e.target.value,
                      }))
                    }
                    placeholder="https://meet.google.com/xxx-xxxx-xxx"
                  />
                </div>
              )}

              {/* Meeting Password */}
              {formData.type !== "in-person" && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Mật khẩu cuộc họp
                  </Label>
                  <Input
                    value={formData.meeting_password}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        meeting_password: e.target.value,
                      }))
                    }
                    placeholder="Mật khẩu (nếu có)"
                  />
                </div>
              )}

              {/* Round */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Vòng phỏng vấn</Label>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={formData.round}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      round: parseInt(e.target.value) || 1,
                    }))
                  }
                />
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Ghi chú</Label>
                <Textarea
                  value={formData.notes}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      notes: e.target.value,
                    }))
                  }
                  placeholder="Ghi chú bổ sung về cuộc phỏng vấn..."
                  rows={3}
                />
              </div>

              {/* Agenda */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Chương trình phỏng vấn
                </Label>
                <div className="flex space-x-2">
                  <Input
                    value={agendaInput}
                    onChange={(e) => setAgendaInput(e.target.value)}
                    placeholder="Thêm mục chương trình..."
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        addAgendaItem();
                      }
                    }}
                  />
                  <Button
                    type="button"
                    size="sm"
                    onClick={addAgendaItem}
                    disabled={!agendaInput.trim()}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                {formData.agenda.length > 0 && (
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {formData.agenda.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between bg-muted p-2 rounded"
                      >
                        <span className="text-sm">{item}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAgendaItem(index)}
                          className="h-auto p-1"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Đang cập nhật...
                </>
              ) : (
                "Cập nhật phỏng vấn"
              )}
            </Button>
          </div>

          {validationErrors.form && (
            <div className="text-sm text-red-500 text-center">
              {validationErrors.form[0]}
            </div>
          )}
        </form>
      </DialogContent>
    </Dialog>
  );
}
