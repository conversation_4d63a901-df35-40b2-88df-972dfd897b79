import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  Star,
  Plus,
  Edit,
  Trash2,
  Building,
  Languages,
  Video,
  Users,
  Save,
  X,
} from "lucide-react";
import { Interviewer, getDepartments } from "@/data/interviewersData";
import { toast } from "sonner";

interface InterviewerManagementProps {
  isOpen: boolean;
  onClose: () => void;
  interviewer?: Interviewer;
  onSave: (interviewer: Omit<Interviewer, "id"> | Interviewer) => void;
  onDelete?: (interviewerId: string) => void;
  mode: "add" | "edit";
}

const availabilityDays = [
  { key: "monday", label: "Monday" },
  { key: "tuesday", label: "Tuesday" },
  { key: "wednesday", label: "Wednesday" },
  { key: "thursday", label: "Thursday" },
  { key: "friday", label: "Friday" },
  { key: "saturday", label: "Saturday" },
  { key: "sunday", label: "Sunday" },
] as const;

const timeSlots = [
  "08:00",
  "08:30",
  "09:00",
  "09:30",
  "10:00",
  "10:30",
  "11:00",
  "11:30",
  "12:00",
  "12:30",
  "13:00",
  "13:30",
  "14:00",
  "14:30",
  "15:00",
  "15:30",
  "16:00",
  "16:30",
  "17:00",
  "17:30",
  "18:00",
];

const interviewTypes = [
  { value: "video", label: "Video", icon: Video },
  { value: "phone", label: "Phone", icon: Phone },
  { value: "in-person", label: "In-Person", icon: Users },
] as const;

const commonLanguages = [
  "English",
  "Spanish",
  "French",
  "German",
  "Italian",
  "Portuguese",
  "Mandarin",
  "Japanese",
  "Korean",
  "Arabic",
  "Hindi",
  "Russian",
];

export function InterviewerManagement({
  isOpen,
  onClose,
  interviewer,
  onSave,
  onDelete,
  mode,
}: InterviewerManagementProps) {
  const [formData, setFormData] = useState<Omit<Interviewer, "id">>(() => ({
    name: interviewer?.name || "",
    email: interviewer?.email || "",
    phone: interviewer?.phone || "",
    initials: interviewer?.initials || "",
    title: interviewer?.title || "",
    department: interviewer?.department || "",
    expertise: interviewer?.expertise || [],
    availability: interviewer?.availability || {
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
      sunday: false,
    },
    timeSlots: interviewer?.timeSlots || [],
    location: interviewer?.location || "",
    maxInterviewsPerDay: interviewer?.maxInterviewsPerDay || 3,
    isActive: interviewer?.isActive ?? true,
    joinedDate:
      interviewer?.joinedDate || new Date().toISOString().split("T")[0],
    totalInterviews: interviewer?.totalInterviews || 0,
    rating: interviewer?.rating || 0,
    preferredTypes: interviewer?.preferredTypes || [],
    languages: interviewer?.languages || [],
    notes: interviewer?.notes || "",
  }));

  const [newExpertise, setNewExpertise] = useState("");
  const [newLanguage, setNewLanguage] = useState("");

  // Auto-generate initials when name changes
  const handleNameChange = (name: string) => {
    const initials = name
      .split(" ")
      .map((n) => n.charAt(0))
      .join("")
      .toUpperCase();
    setFormData((prev) => ({ ...prev, name, initials }));
  };

  const addExpertise = () => {
    if (
      newExpertise.trim() &&
      !formData.expertise.includes(newExpertise.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        expertise: [...prev.expertise, newExpertise.trim()],
      }));
      setNewExpertise("");
    }
  };

  const removeExpertise = (skill: string) => {
    setFormData((prev) => ({
      ...prev,
      expertise: prev.expertise.filter((s) => s !== skill),
    }));
  };

  const addLanguage = () => {
    if (newLanguage && !formData.languages.includes(newLanguage)) {
      setFormData((prev) => ({
        ...prev,
        languages: [...prev.languages, newLanguage],
      }));
      setNewLanguage("");
    }
  };

  const removeLanguage = (language: string) => {
    setFormData((prev) => ({
      ...prev,
      languages: prev.languages.filter((l) => l !== language),
    }));
  };

  const toggleTimeSlot = (timeSlot: string) => {
    setFormData((prev) => ({
      ...prev,
      timeSlots: prev.timeSlots.includes(timeSlot)
        ? prev.timeSlots.filter((t) => t !== timeSlot)
        : [...prev.timeSlots, timeSlot].sort(),
    }));
  };

  const toggleInterviewType = (type: "video" | "phone" | "in-person") => {
    setFormData((prev) => ({
      ...prev,
      preferredTypes: prev.preferredTypes.includes(type)
        ? prev.preferredTypes.filter((t) => t !== type)
        : [...prev.preferredTypes, type],
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.name ||
      !formData.email ||
      !formData.title ||
      !formData.department
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (mode === "edit" && interviewer) {
      onSave({ ...formData, id: interviewer.id });
    } else {
      onSave(formData);
    }

    onClose();
    toast.success(
      mode === "edit"
        ? "Interviewer updated successfully!"
        : "Interviewer added successfully!",
    );
  };

  const handleDelete = () => {
    if (interviewer && onDelete) {
      onDelete(interviewer.id);
      onClose();
      toast.success("Interviewer deleted successfully!");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="w-5 h-5 text-primary" />
            {mode === "edit" ? "Edit Interviewer" : "Add New Interviewer"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Full Name *</Label>
              <Input
                placeholder="John Smith"
                value={formData.name}
                onChange={(e) => handleNameChange(e.target.value)}
                className="rounded-xl"
                required
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Initials</Label>
              <Input
                placeholder="JS"
                value={formData.initials}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    initials: e.target.value.toUpperCase(),
                  }))
                }
                className="rounded-xl"
                maxLength={3}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Email *</Label>
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, email: e.target.value }))
                }
                className="rounded-xl"
                required
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Phone</Label>
              <Input
                placeholder="+****************"
                value={formData.phone}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, phone: e.target.value }))
                }
                className="rounded-xl"
              />
            </div>
          </div>

          {/* Professional Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Job Title *</Label>
              <Input
                placeholder="Senior Software Engineer"
                value={formData.title}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, title: e.target.value }))
                }
                className="rounded-xl"
                required
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Department *</Label>
              <Select
                value={formData.department}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, department: value }))
                }
              >
                <SelectTrigger className="rounded-xl">
                  <SelectValue placeholder="Select department" />
                </SelectTrigger>
                <SelectContent>
                  {getDepartments().map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium">Location</Label>
            <Input
              placeholder="San Francisco, CA or Remote"
              value={formData.location}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, location: e.target.value }))
              }
              className="rounded-xl"
            />
          </div>

          {/* Expertise Areas */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Areas of Expertise</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Add expertise area..."
                value={newExpertise}
                onChange={(e) => setNewExpertise(e.target.value)}
                onKeyPress={(e) =>
                  e.key === "Enter" && (e.preventDefault(), addExpertise())
                }
                className="rounded-xl"
              />
              <Button
                type="button"
                onClick={addExpertise}
                className="rounded-xl"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.expertise.map((skill) => (
                <Badge
                  key={skill}
                  variant="secondary"
                  className="gap-1 rounded-full"
                >
                  {skill}
                  <X
                    className="w-3 h-3 cursor-pointer"
                    onClick={() => removeExpertise(skill)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Languages */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Languages</Label>
            <div className="flex gap-2">
              <Select value={newLanguage} onValueChange={setNewLanguage}>
                <SelectTrigger className="rounded-xl">
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {commonLanguages
                    .filter((lang) => !formData.languages.includes(lang))
                    .map((lang) => (
                      <SelectItem key={lang} value={lang}>
                        {lang}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <Button
                type="button"
                onClick={addLanguage}
                disabled={!newLanguage}
                className="rounded-xl"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.languages.map((language) => (
                <Badge
                  key={language}
                  variant="outline"
                  className="gap-1 rounded-full"
                >
                  <Languages className="w-3 h-3" />
                  {language}
                  <X
                    className="w-3 h-3 cursor-pointer"
                    onClick={() => removeLanguage(language)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Availability */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Weekly Availability</Label>
            <div className="grid grid-cols-4 gap-3">
              {availabilityDays.map((day) => (
                <div key={day.key} className="flex items-center space-x-2">
                  <Checkbox
                    id={day.key}
                    checked={formData.availability[day.key]}
                    onCheckedChange={(checked) =>
                      setFormData((prev) => ({
                        ...prev,
                        availability: {
                          ...prev.availability,
                          [day.key]: !!checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor={day.key} className="text-sm">
                    {day.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Time Slots */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Preferred Time Slots</Label>
            <div className="grid grid-cols-6 gap-2">
              {timeSlots.map((time) => (
                <Button
                  key={time}
                  type="button"
                  variant={
                    formData.timeSlots.includes(time) ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => toggleTimeSlot(time)}
                  className="rounded-xl"
                >
                  {time}
                </Button>
              ))}
            </div>
          </div>

          {/* Interview Preferences */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              Preferred Interview Types
            </Label>
            <div className="grid grid-cols-3 gap-3">
              {interviewTypes.map((type) => {
                const Icon = type.icon;
                const isSelected = formData.preferredTypes.includes(type.value);
                return (
                  <Button
                    key={type.value}
                    type="button"
                    variant={isSelected ? "default" : "outline"}
                    className="h-16 flex-col gap-2 rounded-xl"
                    onClick={() => toggleInterviewType(type.value)}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{type.label}</span>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Settings */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                Max Interviews Per Day
              </Label>
              <Input
                type="number"
                min="1"
                max="10"
                value={formData.maxInterviewsPerDay}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    maxInterviewsPerDay: parseInt(e.target.value) || 3,
                  }))
                }
                className="rounded-xl"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Status</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Switch
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, isActive: checked }))
                  }
                />
                <Label className="text-sm">
                  {formData.isActive ? "Active" : "Inactive"}
                </Label>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Notes</Label>
            <Textarea
              placeholder="Additional notes about the interviewer..."
              value={formData.notes}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, notes: e.target.value }))
              }
              className="rounded-xl min-h-[80px]"
            />
          </div>

          {/* Actions */}
          <div className="flex justify-between pt-4">
            <div>
              {mode === "edit" && onDelete && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={handleDelete}
                  className="gap-2 rounded-xl"
                >
                  <Trash2 className="w-4 h-4" />
                  Delete
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="rounded-xl"
              >
                Cancel
              </Button>
              <Button type="submit" className="ai-button gap-2">
                <Save className="w-4 h-4" />
                {mode === "edit" ? "Update" : "Add"} Interviewer
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
