import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Download,
  Archive,
  Trash2,
  Play,
  Pause,
  Copy,
  MoreHorizontal,
  X,
  FileText,
  Eye,
} from "lucide-react";

interface BulkJobActionsProps {
  selectedCount: number;
  onClearSelection: () => void;
  onBulkAction: (action: string) => void;
}

export const BulkJobActions = ({
  selectedCount,
  onClearSelection,
  onBulkAction,
}: BulkJobActionsProps) => {
  if (selectedCount === 0) return null;

  return (
    <div className="bg-primary text-primary-foreground rounded-xl p-4 mb-6 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <Badge
          variant="secondary"
          className="bg-primary-foreground text-primary"
        >
          {selectedCount} selected
        </Badge>
        <span className="text-sm">Bulk actions available</span>
      </div>

      <div className="flex items-center gap-2">
        <Button
          size="sm"
          variant="secondary"
          onClick={() => onBulkAction("export")}
          className="gap-2"
        >
          <Download className="h-4 w-4" />
          Export
        </Button>

        <Button
          size="sm"
          variant="secondary"
          onClick={() => onBulkAction("duplicate")}
          className="gap-2"
        >
          <Copy className="h-4 w-4" />
          Duplicate
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="sm" variant="secondary" className="gap-2">
              <MoreHorizontal className="h-4 w-4" />
              More Actions
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="rounded-xl">
            <DropdownMenuLabel>Status Changes</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onBulkAction("activate")}>
              <Play className="mr-2 h-4 w-4" />
              Activate Jobs
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkAction("pause")}>
              <Pause className="mr-2 h-4 w-4" />
              Pause Jobs
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkAction("close")}>
              <Archive className="mr-2 h-4 w-4" />
              Close Jobs
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>Data Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onBulkAction("export-detailed")}>
              <FileText className="mr-2 h-4 w-4" />
              Detailed Export
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkAction("preview")}>
              <Eye className="mr-2 h-4 w-4" />
              Preview Reports
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onBulkAction("delete")}
              className="text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Jobs
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          size="sm"
          variant="secondary"
          onClick={onClearSelection}
          className="gap-2"
        >
          <X className="h-4 w-4" />
          Clear
        </Button>
      </div>
    </div>
  );
};
