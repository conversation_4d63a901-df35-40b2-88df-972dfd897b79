import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Loader2, User, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { apiService } from "@/lib/api";

export interface UserOption {
  id: number;
  name: string;
  email: string;
  department?: string;
  title?: string;
  avatar?: string;
  is_active?: boolean;
}

interface UserSelectorProps {
  label: string;
  placeholder: string;
  value?: number;
  onValueChange: (value: number | undefined) => void;
  userType: "hiring-managers" | "recruiters";
  disabled?: boolean;
  error?: string;
}

export const UserSelector = ({
  label,
  placeholder,
  value,
  onValueChange,
  userType,
  disabled = false,
  error,
}: UserSelectorProps) => {
  const [users, setUsers] = useState<UserOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [fetchError, setFetchError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoading(true);
        setFetchError(null);

        const response =
          userType === "hiring-managers"
            ? await apiService.getHiringManagers()
            : await apiService.getRecruiters();

        if (response.status === "success") {
          setUsers(response.data || []);
        } else {
          setFetchError("Failed to load users");
        }
      } catch (error) {
        console.error(`Error fetching ${userType}:`, error);
        setFetchError(`Unable to load ${userType.replace("-", " ")}`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, [userType]);

  const selectedUser = users.find((user) => user.id === value);

  const getInitials = (name: string): string => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (fetchError) {
    return (
      <div className="space-y-2">
        <Label htmlFor={`${userType}-selector`}>{label}</Label>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{fetchError}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={`${userType}-selector`}>{label}</Label>
      <Select
        value={value?.toString() || ""}
        onValueChange={(val) => onValueChange(val ? parseInt(val) : undefined)}
        disabled={disabled || isLoading}
      >
        <SelectTrigger className={error ? "border-destructive" : ""}>
          {isLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading...</span>
            </div>
          ) : selectedUser ? (
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={selectedUser.avatar} />
                <AvatarFallback className="text-xs">
                  {getInitials(selectedUser.name)}
                </AvatarFallback>
              </Avatar>
              <span>{selectedUser.name}</span>
              {selectedUser.department && (
                <Badge variant="secondary" className="text-xs">
                  {selectedUser.department}
                </Badge>
              )}
            </div>
          ) : (
            <SelectValue placeholder={placeholder} />
          )}
        </SelectTrigger>
        <SelectContent>
          {users.length === 0 && !isLoading ? (
            <div className="p-4 text-center text-muted-foreground">
              <User className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No {userType.replace("-", " ")} available</p>
            </div>
          ) : (
            users.map((user) => (
              <SelectItem
                key={user.id}
                value={user.id.toString()}
                disabled={user.is_active === false}
              >
                <div className="flex items-center gap-3 w-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.avatar} />
                    <AvatarFallback className="text-sm">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium truncate">{user.name}</span>
                      {user.is_active === false && (
                        <Badge variant="outline" className="text-xs">
                          Inactive
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span className="truncate">{user.email}</span>
                      {user.department && (
                        <>
                          <span>•</span>
                          <Badge variant="secondary" className="text-xs">
                            {user.department}
                          </Badge>
                        </>
                      )}
                    </div>
                    {user.title && (
                      <p className="text-xs text-muted-foreground truncate">
                        {user.title}
                      </p>
                    )}
                  </div>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
};
