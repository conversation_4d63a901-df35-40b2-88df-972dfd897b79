import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, Trash2 } from "lucide-react";
import { Job } from "@/domains/candidates/types";

interface DeleteJobModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  job: Job | null;
}

export const DeleteJobModal = ({
  isOpen,
  onClose,
  onConfirm,
  job,
}: DeleteJobModalProps) => {
  if (!job) return null;

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-destructive">
            <div className="p-2 bg-destructive/10 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-destructive" />
            </div>
            Delete Job
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the job
            posting and remove all associated data.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="p-4 bg-muted/50 rounded-lg border border-border">
            <h4 className="font-semibold">{job.title}</h4>
            <p className="text-sm text-muted-foreground">
              {job.department} • {job.location}
            </p>
            <p className="text-sm text-muted-foreground">
              {job.applicantCount} candidates will lose their application status
            </p>
          </div>

          <div className="bg-destructive/5 border border-destructive/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-destructive mt-0.5" />
              <div>
                <h4 className="font-semibold text-destructive">
                  Warning: This action is irreversible
                </h4>
                <ul className="text-sm text-destructive/80 mt-2 space-y-1">
                  <li>• Job posting will be permanently deleted</li>
                  <li>• All candidate applications will be archived</li>
                  <li>• Interview schedules will be cancelled</li>
                  <li>• Analytics data will be preserved for reporting</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1 rounded-xl"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirm}
              className="flex-1 rounded-xl gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Delete Job
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
