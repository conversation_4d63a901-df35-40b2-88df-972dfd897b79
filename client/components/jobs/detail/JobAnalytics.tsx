import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { BarChart3 } from "lucide-react";

export interface JobAnalyticsProps {
  analytics: {
    conversionRate: number;
    averageTimeToHire: number;
    totalApplications: number;
    hiredCount: number;
    viewCount?: number;
    applicantCount?: number;
    postedDate?: string;
    closingDate?: string;
  };
}

export const JobAnalytics: React.FC<JobAnalyticsProps> = ({ analytics }) => {
  const calculateDaysActive = () => {
    if (!analytics.postedDate) return 0;
    const postedDate = new Date(analytics.postedDate);
    if (isNaN(postedDate.getTime())) return 0;
    return Math.floor(
      (Date.now() - postedDate.getTime()) / (1000 * 60 * 60 * 24),
    );
  };

  const calculateDaysToClose = () => {
    if (!analytics.closingDate) return "No deadline";
    const closingDate = new Date(analytics.closingDate);
    if (isNaN(closingDate.getTime())) return "No deadline";
    const daysRemaining = Math.floor(
      (closingDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24),
    );
    return daysRemaining > 0 ? `${daysRemaining} days` : "Expired";
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm">Conversion Rate</span>
              <span className="text-sm font-medium">
                {analytics.conversionRate}%
              </span>
            </div>
            {analytics.viewCount && analytics.applicantCount && (
              <div className="flex justify-between">
                <span className="text-sm">Views to Applications</span>
                <span className="text-sm font-medium">
                  {(
                    (analytics.applicantCount / analytics.viewCount) *
                    100
                  ).toFixed(1)}
                  %
                </span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-sm">Application Rate</span>
              <span className="text-sm font-medium">
                {(
                  analytics.totalApplications /
                  Math.max(calculateDaysActive(), 1)
                ).toFixed(1)}{" "}
                per day
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Days Active</span>
              <span className="text-sm font-medium">
                {calculateDaysActive()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Time to Close</span>
              <span className="text-sm font-medium">
                {calculateDaysToClose()}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Application Sources</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm">LinkedIn</span>
              <span className="text-sm font-medium">45%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Company Website</span>
              <span className="text-sm font-medium">30%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Indeed</span>
              <span className="text-sm font-medium">15%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Referrals</span>
              <span className="text-sm font-medium">10%</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Job Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium">Conversion Rate</p>
                <p className="text-2xl font-bold text-green-600">
                  {analytics.conversionRate}%
                </p>
              </div>
              <div>
                <p className="text-sm font-medium">Average Time to Hire</p>
                <p className="text-2xl font-bold text-blue-600">
                  {analytics.averageTimeToHire} days
                </p>
              </div>
            </div>
            <div className="pt-4">
              <p className="text-sm text-muted-foreground">
                Analytics data will be more comprehensive as more candidates
                apply and progress through the hiring pipeline.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
