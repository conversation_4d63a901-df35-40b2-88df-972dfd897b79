import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { BarChart3 } from "lucide-react";
import { useTranslation } from "@/lib/i18n";

export interface JobAnalyticsProps {
  analytics: {
    conversionRate: number;
    averageTimeToHire: number;
    totalApplications: number;
    hiredCount: number;
    viewCount?: number;
    applicantCount?: number;
    postedDate?: string;
    closingDate?: string;
  };
}

export const JobAnalytics: React.FC<JobAnalyticsProps> = ({ analytics }) => {
  const { t } = useTranslation();

  const calculateDaysActive = () => {
    if (!analytics.postedDate) return 0;
    const postedDate = new Date(analytics.postedDate);
    if (isNaN(postedDate.getTime())) return 0;
    return Math.floor(
      (Date.now() - postedDate.getTime()) / (1000 * 60 * 60 * 24),
    );
  };

  const calculateDaysToClose = () => {
    if (!analytics.closingDate) return "<PERSON><PERSON><PERSON>ng có hạn chót";
    const closingDate = new Date(analytics.closingDate);
    if (isNaN(closingDate.getTime())) return "Không có hạn chót";
    const daysRemaining = Math.floor(
      (closingDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24),
    );
    return daysRemaining > 0 ? `${daysRemaining} ngày` : "Đã hết hạn";
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Chỉ số hiệu suất
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm">Tỷ lệ chuyển đổi</span>
              <span className="text-sm font-medium">
                {analytics.conversionRate}%
              </span>
            </div>
            {analytics.viewCount && analytics.applicantCount && (
              <div className="flex justify-between">
                <span className="text-sm">Tỷ lệ xem thành ứng tuyển</span>
                <span className="text-sm font-medium">
                  {(
                    (analytics.applicantCount / analytics.viewCount) *
                    100
                  ).toFixed(1)}
                  %
                </span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-sm">Tỷ lệ ứng tuyển</span>
              <span className="text-sm font-medium">
                {(
                  analytics.totalApplications /
                  Math.max(calculateDaysActive(), 1)
                ).toFixed(1)}{" "}
                mỗi ngày
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Số ngày hoạt động</span>
              <span className="text-sm font-medium">
                {calculateDaysActive()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Thời gian đóng</span>
              <span className="text-sm font-medium">
                {calculateDaysToClose()}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Nguồn ứng tuyển</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm">LinkedIn</span>
              <span className="text-sm font-medium">45%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Website công ty</span>
              <span className="text-sm font-medium">30%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Indeed</span>
              <span className="text-sm font-medium">15%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Giới thiệu</span>
              <span className="text-sm font-medium">10%</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Hiệu suất công việc</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium">Tỷ lệ chuyển đổi</p>
                <p className="text-2xl font-bold text-green-600">
                  {analytics.conversionRate}%
                </p>
              </div>
              <div>
                <p className="text-sm font-medium">Thời gian tuyển dụng trung bình</p>
                <p className="text-2xl font-bold text-blue-600">
                  {analytics.averageTimeToHire} ngày
                </p>
              </div>
            </div>
            <div className="pt-4">
              <p className="text-sm text-muted-foreground">
                Dữ liệu phân tích sẽ đầy đủ hơn khi có nhiều ứng viên ứng tuyển
                và tiến triển qua quy trình tuyển dụng.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
