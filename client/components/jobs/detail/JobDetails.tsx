import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Briefcase, Award, CheckCircle } from "lucide-react";
import { useTranslation } from "@/lib/i18n";

export interface JobDetailsProps {
  job: {
    department: string;
    type?: string;
    experienceLevel?: string;
    remote?: boolean;
    deadline?: string;
    benefits?: string[];
  };
}

export const JobDetails: React.FC<JobDetailsProps> = ({ job }) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="w-5 h-5" />
            {t.jobs.jobDetails}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm font-medium">{t.jobs.department}</p>
            <p className="text-sm text-muted-foreground">{job.department}</p>
          </div>
          <div>
            <p className="text-sm font-medium"><PERSON><PERSON><PERSON> công việc</p>
            <p className="text-sm text-muted-foreground">
              {job.type === 'full-time' ? t.jobs.fullTime :
               job.type === 'part-time' ? t.jobs.partTime :
               job.type === 'contract' ? t.jobs.contract :
               job.type || t.jobs.fullTime}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Cấp độ kinh nghiệm</p>
            <p className="text-sm text-muted-foreground">
              {job.experienceLevel || "Trung cấp"}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Làm việc từ xa</p>
            <p className="text-sm text-muted-foreground">
              {job.remote ? "Hỗ trợ làm việc từ xa" : "Yêu cầu làm tại văn phòng"}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Hạn nộp đơn</p>
            <p className="text-sm text-muted-foreground">
              {job.deadline && !isNaN(new Date(job.deadline).getTime())
                ? new Date(job.deadline).toLocaleDateString()
                : "Không có hạn chót"}
            </p>
          </div>
        </CardContent>
      </Card>

      {job.benefits && job.benefits.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              {t.jobs.benefits}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {job.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{benefit}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
