import { Button } from "@/components/ui/button";
import {
  Plus,
  Calendar,
  MessageSquare,
  Upload,
  Users,
  Building2,
  Sparkles,
  Zap,
  Edit,
} from "lucide-react";

const actions = [
  {
    title: "Add Candidate",
    description: "Manually add a new candidate to the system",
    icon: Plus,
    variant: "default" as const,
    featured: true,
  },
  {
    title: "Edit Candidate",
    description: "Update existing candidate information",
    icon: Edit,
    variant: "outline" as const,
  },
  {
    title: "AI Smart Search",
    description: "Find candidates using AI-powered search",
    icon: Sparkles,
    variant: "default" as const,
    featured: true,
  },
  {
    title: "Schedule Interview",
    description: "Set up a new interview with a candidate",
    icon: Calendar,
    variant: "outline" as const,
  },
  {
    title: "Send Message",
    description: "Send email to candidates or team members",
    icon: MessageSquare,
    variant: "outline" as const,
  },
  {
    title: "Import Resumes",
    description: "Bulk upload candidate resumes with AI parsing",
    icon: Upload,
    variant: "outline" as const,
  },
  {
    title: "Create Job",
    description: "Post a new job opening with AI suggestions",
    icon: Building2,
    variant: "outline" as const,
  },
  {
    title: "Invite Team",
    description: "Add team members to collaborate",
    icon: Users,
    variant: "outline" as const,
  },
  {
    title: "AI Insights",
    description: "Get AI-powered hiring recommendations",
    icon: Zap,
    variant: "outline" as const,
  },
];

export const QuickActions = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {actions.map((action) => {
        const Icon = action.icon;
        const isFeatured = action.featured;

        return (
          <Button
            key={action.title}
            variant={action.variant}
            className={cn(
              "h-auto p-6 flex flex-col items-start gap-3 text-left group relative overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl",
              {
                "ai-button": isFeatured,
                "hover:border-primary/50": !isFeatured,
              },
            )}
          >
            {/* Background glow for featured items */}
            {isFeatured && (
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-emerald-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            )}

            <div className="relative z-10 w-full">
              <div className="flex items-center gap-3 w-full mb-2">
                <div
                  className={cn("p-2 rounded-lg transition-all duration-200", {
                    "bg-primary-foreground/20": isFeatured,
                    "bg-primary/10 group-hover:bg-primary/20": !isFeatured,
                  })}
                >
                  <Icon
                    className={cn(
                      "w-4 h-4 transition-transform group-hover:scale-110",
                      {
                        "text-primary-foreground": isFeatured,
                        "text-primary": !isFeatured,
                      },
                    )}
                  />
                </div>
                <span
                  className={cn("font-semibold", {
                    "text-primary-foreground": isFeatured,
                  })}
                >
                  {action.title}
                </span>
                {isFeatured && (
                  <div className="ml-auto">
                    <Sparkles className="w-3 h-3 text-primary-foreground/60" />
                  </div>
                )}
              </div>
              <p
                className={cn("text-xs opacity-80 leading-relaxed", {
                  "text-primary-foreground": isFeatured,
                  "text-muted-foreground": !isFeatured,
                })}
              >
                {action.description}
              </p>
            </div>
          </Button>
        );
      })}
    </div>
  );
};

function cn(...classes: (string | undefined | boolean)[]): string {
  return classes.filter(Boolean).join(" ");
}
