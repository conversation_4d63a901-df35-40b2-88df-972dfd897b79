import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Plus,
  X,
  AlertCircle,
  CheckCircle,
  Variable,
  Lightbulb,
} from "lucide-react";
import { toast } from "sonner";

export interface TemplateVariableEditorProps {
  variables: string[];
  onAddVariable: (variable: string) => void;
  onRemoveVariable: (variable: string) => void;
  extractedVariables?: string[];
  className?: string;
  disabled?: boolean;
}

export const TemplateVariableEditor: React.FC<TemplateVariableEditorProps> = ({
  variables,
  onAddVariable,
  onRemoveVariable,
  extractedVariables = [],
  className,
  disabled = false,
}) => {
  const [newVariable, setNewVariable] = useState("");
  const [error, setError] = useState<string | null>(null);

  // Common Vietnamese variable suggestions
  const commonVariables = [
    { name: "candidate_name", label: "Tên ứng viên", description: "Họ tên đầy đủ của ứng viên" },
    { name: "job_title", label: "Vị trí công việc", description: "Tên vị trí tuyển dụng" },
    { name: "company_name", label: "Tên công ty", description: "Tên công ty tuyển dụng" },
    { name: "recruiter_name", label: "Tên nhà tuyển dụng", description: "Người phụ trách tuyển dụng" },
    { name: "recruiter_email", label: "Email nhà tuyển dụng", description: "Email liên hệ" },
    { name: "recruiter_phone", label: "SĐT nhà tuyển dụng", description: "Số điện thoại liên hệ" },
    { name: "interview_date", label: "Ngày phỏng vấn", description: "Ngày diễn ra phỏng vấn" },
    { name: "interview_time", label: "Giờ phỏng vấn", description: "Thời gian phỏng vấn" },
    { name: "interview_location", label: "Địa điểm phỏng vấn", description: "Nơi phỏng vấn" },
    { name: "interview_type", label: "Hình thức phỏng vấn", description: "Trực tiếp/Online" },
    { name: "department", label: "Phòng ban", description: "Phòng ban tuyển dụng" },
    { name: "salary", label: "Mức lương", description: "Mức lương đề xuất" },
    { name: "start_date", label: "Ngày bắt đầu", description: "Ngày bắt đầu làm việc" },
    { name: "offer_deadline", label: "Hạn trả lời", description: "Hạn phản hồi offer" },
  ];

  const validateVariable = (variable: string): string | null => {
    if (!variable.trim()) {
      return "Tên biến không được để trống";
    }

    if (variable.trim().length < 2) {
      return "Tên biến phải có ít nhất 2 ký tự";
    }

    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(variable.trim())) {
      return "Tên biến chỉ được chứa chữ cái, số và dấu gạch dưới, bắt đầu bằng chữ cái";
    }

    if (variables.includes(variable.trim())) {
      return "Biến này đã tồn tại";
    }

    return null;
  };

  const handleAddVariable = () => {
    const trimmedVariable = newVariable.trim();
    const validationError = validateVariable(trimmedVariable);

    if (validationError) {
      setError(validationError);
      return;
    }

    onAddVariable(trimmedVariable);
    setNewVariable("");
    setError(null);
    toast.success(`Đã thêm biến "${trimmedVariable}"`);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddVariable();
    }
  };

  const handleSuggestionAdd = (variableName: string) => {
    if (!variables.includes(variableName)) {
      onAddVariable(variableName);
      toast.success(`Đã thêm biến "${variableName}"`);
    }
  };

  const handleRemoveVariable = (variable: string) => {
    onRemoveVariable(variable);
    toast.success(`Đã xóa biến "${variable}"`);
  };

  // Get unused common variables as suggestions
  const suggestions = commonVariables.filter(
    common => !variables.includes(common.name) && !extractedVariables.includes(common.name)
  );

  // Variables that are extracted but not manually added
  const autoDetectedVariables = extractedVariables.filter(
    extracted => !variables.includes(extracted)
  );

  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="text-sm flex items-center gap-2">
            <Variable className="w-4 h-4" />
            Quản lý biến Template
          </CardTitle>
          <CardDescription className="text-xs">
            Thêm và quản lý các biến động trong template
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add New Variable */}
          <div className="space-y-2">
            <div className="flex gap-2">
              <Input
                value={newVariable}
                onChange={(e) => {
                  setNewVariable(e.target.value);
                  setError(null);
                }}
                onKeyPress={handleKeyPress}
                placeholder="Nhập tên biến..."
                disabled={disabled}
                className={`flex-1 text-sm ${error ? "border-destructive" : ""}`}
              />
              <Button
                onClick={handleAddVariable}
                disabled={disabled || !newVariable.trim()}
                size="sm"
                className="gap-1"
              >
                <Plus className="w-4 h-4" />
                Thêm
              </Button>
            </div>
            {error && (
              <p className="text-sm text-destructive">{error}</p>
            )}
          </div>

          {/* Auto-detected Variables */}
          {autoDetectedVariables.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium">Biến tự động phát hiện</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {autoDetectedVariables.map((variable) => (
                  <Badge
                    key={variable}
                    variant="secondary"
                    className="text-xs cursor-pointer hover:bg-green-100"
                    onClick={() => handleSuggestionAdd(variable)}
                  >
                    <Plus className="w-3 h-3 mr-1" />
                    {variable}
                  </Badge>
                ))}
              </div>
              <p className="text-xs text-muted-foreground">
                Click để thêm vào danh sách biến
              </p>
            </div>
          )}

          {/* Current Variables */}
          {variables.length > 0 && (
            <div className="space-y-2">
              <span className="text-sm font-medium">Biến hiện tại</span>
              <div className="flex flex-wrap gap-2">
                {variables.map((variable) => (
                  <Badge
                    key={variable}
                    variant="default"
                    className="text-xs flex items-center gap-1"
                  >
                    {`{{${variable}}}`}
                    {!disabled && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 ml-1 hover:bg-transparent"
                        onClick={() => handleRemoveVariable(variable)}
                      >
                        <X className="w-3 h-3 hover:text-red-500" />
                      </Button>
                    )}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Variable Suggestions */}
          {suggestions.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Lightbulb className="w-4 h-4 text-yellow-600" />
                <span className="text-sm font-medium">Gợi ý biến thường dùng</span>
              </div>
              <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
                {suggestions.slice(0, 6).map((suggestion) => (
                  <div
                    key={suggestion.name}
                    className="p-2 border rounded cursor-pointer hover:bg-muted transition-colors"
                    onClick={() => handleSuggestionAdd(suggestion.name)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-sm">{suggestion.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {suggestion.description}
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs ml-2">
                        {`{{${suggestion.name}}}`}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
              {suggestions.length > 6 && (
                <p className="text-xs text-muted-foreground">
                  Và {suggestions.length - 6} gợi ý khác...
                </p>
              )}
            </div>
          )}

          {/* Help Text */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              <div className="space-y-1">
                <p><strong>Cách sử dụng biến:</strong></p>
                <ul className="list-disc list-inside space-y-1 ml-2">
                  <li>Sử dụng cú pháp {`{{tên_biến}}`} trong nội dung template</li>
                  <li>Tên biến chỉ chứa chữ cái, số và dấu gạch dưới</li>
                  <li>Biến được tự động phát hiện từ nội dung template</li>
                  <li>Click vào gợi ý để thêm biến nhanh</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
};
