import React from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Heart,
  MessageCircle,
  Bell,
  X,
  UserCheck,
  Mail,
  MessageSquare,
  Clock,
  CheckCircle,
  XCircle,
  Gift,
} from "lucide-react";

export interface TemplateCategorySelectorProps {
  value: string;
  onValueChange: (value: string) => void;
  categories?: Record<string, string>;
  error?: string;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  showIcons?: boolean;
  showDescriptions?: boolean;
}

export const TemplateCategorySelector: React.FC<TemplateCategorySelectorProps> = ({
  value,
  onValueChange,
  categories,
  error,
  disabled = false,
  placeholder = "Chọn danh mục...",
  className,
  showIcons = true,
  showDescriptions = false,
}) => {
  // Default Vietnamese categories
  const defaultCategories: Record<string, string> = {
    interview: "Phỏng vấn",
    offer: "<PERSON><PERSON> nghị công việc",
    feedback: "<PERSON><PERSON><PERSON> hồi",
    reminder: "Nhắc nhở",
    rejection: "Từ chối",
    welcome: "Chào mừng",
  };

  // Category descriptions in Vietnamese
  const categoryDescriptions: Record<string, string> = {
    interview: "Template cho việc mời phỏng vấn, thông báo lịch hẹn",
    offer: "Template gửi đề nghị công việc, thương lượng lương",
    feedback: "Template phản hồi kết quả phỏng vấn, đánh giá",
    reminder: "Template nhắc nhở lịch hẹn, deadline",
    rejection: "Template từ chối ứng viên một cách lịch sự",
    welcome: "Template chào mừng ứng viên mới gia nhập",
  };

  // Category icons
  const categoryIcons: Record<string, React.ReactNode> = {
    interview: <Users className="w-4 h-4 text-blue-600" />,
    offer: <Gift className="w-4 h-4 text-green-600" />,
    feedback: <MessageCircle className="w-4 h-4 text-purple-600" />,
    reminder: <Bell className="w-4 h-4 text-orange-600" />,
    rejection: <XCircle className="w-4 h-4 text-red-600" />,
    welcome: <UserCheck className="w-4 h-4 text-emerald-600" />,
  };

  // Category colors for badges
  const categoryColors: Record<string, string> = {
    interview: "bg-blue-100 text-blue-800 border-blue-200",
    offer: "bg-green-100 text-green-800 border-green-200",
    feedback: "bg-purple-100 text-purple-800 border-purple-200",
    reminder: "bg-orange-100 text-orange-800 border-orange-200",
    rejection: "bg-red-100 text-red-800 border-red-200",
    welcome: "bg-emerald-100 text-emerald-800 border-emerald-200",
  };

  const currentCategories = categories || defaultCategories;

  const getCategoryIcon = (categoryKey: string) => {
    return showIcons ? categoryIcons[categoryKey] : null;
  };

  const getCategoryColor = (categoryKey: string) => {
    return categoryColors[categoryKey] || "bg-gray-100 text-gray-800 border-gray-200";
  };

  const getCategoryDescription = (categoryKey: string) => {
    return showDescriptions ? categoryDescriptions[categoryKey] : null;
  };

  return (
    <div className={className}>
      <Select 
        value={value} 
        onValueChange={onValueChange}
        disabled={disabled}
      >
        <SelectTrigger className={error ? "border-destructive" : ""}>
          <SelectValue placeholder={placeholder}>
            {value && (
              <div className="flex items-center gap-2">
                {getCategoryIcon(value)}
                <span>{currentCategories[value] || value}</span>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {Object.entries(currentCategories).map(([key, label]) => (
            <SelectItem key={key} value={key}>
              <div className="flex items-center gap-2 w-full">
                {getCategoryIcon(key)}
                <div className="flex-1">
                  <div className="font-medium">{label}</div>
                  {showDescriptions && getCategoryDescription(key) && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {getCategoryDescription(key)}
                    </div>
                  )}
                </div>
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getCategoryColor(key)}`}
                >
                  {key}
                </Badge>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {error && (
        <p className="text-sm text-destructive mt-1">{error}</p>
      )}
      
      {/* Category Info */}
      {value && !error && showDescriptions && (
        <div className="mt-2 p-2 bg-muted/50 rounded border">
          <div className="flex items-start gap-2">
            {getCategoryIcon(value)}
            <div className="flex-1">
              <p className="text-sm font-medium">
                {currentCategories[value]}
              </p>
              <p className="text-xs text-muted-foreground">
                {getCategoryDescription(value)}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Utility functions for external use
export const getCategoryDisplayName = (category: string): string => {
  const categories: Record<string, string> = {
    interview: "Phỏng vấn",
    offer: "Đề nghị công việc",
    feedback: "Phản hồi",
    reminder: "Nhắc nhở",
    rejection: "Từ chối",
    welcome: "Chào mừng",
  };
  
  return categories[category] || category;
};

export const getCategoryIcon = (category: string): React.ReactNode => {
  const icons: Record<string, React.ReactNode> = {
    interview: <Users className="w-4 h-4 text-blue-600" />,
    offer: <Gift className="w-4 h-4 text-green-600" />,
    feedback: <MessageCircle className="w-4 h-4 text-purple-600" />,
    reminder: <Bell className="w-4 h-4 text-orange-600" />,
    rejection: <XCircle className="w-4 h-4 text-red-600" />,
    welcome: <UserCheck className="w-4 h-4 text-emerald-600" />,
  };
  
  return icons[category] || <MessageSquare className="w-4 h-4 text-gray-600" />;
};

export const getCategoryColor = (category: string): string => {
  const colors: Record<string, string> = {
    interview: "bg-blue-100 text-blue-800 border-blue-200",
    offer: "bg-green-100 text-green-800 border-green-200",
    feedback: "bg-purple-100 text-purple-800 border-purple-200",
    reminder: "bg-orange-100 text-orange-800 border-orange-200",
    rejection: "bg-red-100 text-red-800 border-red-200",
    welcome: "bg-emerald-100 text-emerald-800 border-emerald-200",
  };
  
  return colors[category] || "bg-gray-100 text-gray-800 border-gray-200";
};

// Sample categories for development/testing
export const sampleCategories: Record<string, string> = {
  interview: "Phỏng vấn",
  offer: "Đề nghị công việc",
  feedback: "Phản hồi",
  reminder: "Nhắc nhở",
  rejection: "Từ chối",
  welcome: "Chào mừng",
  followup: "Theo dõi",
  general: "Tổng quát",
};

// Template examples for each category
export const categoryTemplateExamples: Record<string, { subject: string; content: string }> = {
  interview: {
    subject: "Lời mời phỏng vấn - Vị trí {{job_title}} tại {{company_name}}",
    content: `Kính gửi {{candidate_name}},

Chúng tôi rất vui mừng thông báo rằng hồ sơ của bạn cho vị trí {{job_title}} đã được chọn để tham gia vòng phỏng vấn.

📅 Thông tin phỏng vấn:
• Thời gian: {{interview_date}} lúc {{interview_time}}
• Địa điểm: {{interview_location}}
• Hình thức: {{interview_type}}

Vui lòng xác nhận sự tham gia của bạn bằng cách trả lời email này.

Trân trọng,
{{recruiter_name}}
{{company_name}}`
  },
  offer: {
    subject: "Đề nghị công việc - Vị trí {{job_title}} tại {{company_name}}",
    content: `Kính gửi {{candidate_name}},

Chúng tôi rất vui mừng gửi đến bạn đề nghị chính thức cho vị trí {{job_title}} tại {{company_name}}.

💼 Chi tiết đề nghị:
• Vị trí: {{job_title}}
• Phòng ban: {{department}}
• Mức lương: {{salary}}
• Ngày bắt đầu: {{start_date}}

Vui lòng phản hồi trước ngày {{offer_deadline}}.

Chúc mừng!
{{hiring_manager_name}}
{{company_name}}`
  },
  feedback: {
    subject: "Phản hồi k���t quả phỏng vấn - {{job_title}}",
    content: `Kính gửi {{candidate_name}},

Cảm ơn bạn đã dành thời gian tham gia phỏng vấn cho vị trí {{job_title}} tại {{company_name}}.

Chúng tôi đã xem xét kỹ lưỡng và đánh giá cao những kinh nghiệm và kỹ năng mà bạn đã chia sẻ.

{{feedback_content}}

Chúng tôi sẽ tiếp tục theo dõi hồ sơ của bạn cho các cơ hội phù hợp trong tương lai.

Trân trọng,
{{recruiter_name}}
{{company_name}}`
  },
  reminder: {
    subject: "Nhắc nhở: {{reminder_type}} - {{job_title}}",
    content: `Kính gửi {{candidate_name}},

Đây là email nhắc nhở về {{reminder_type}} liên quan đến vị trí {{job_title}}.

📌 Chi tiết:
{{reminder_details}}

Vui lòng liên hệ nếu bạn có bất kỳ câu hỏi nào.

Trân trọng,
{{recruiter_name}}
{{company_name}}`
  },
  rejection: {
    subject: "Cập nhật về đơn ứng tuyển - {{job_title}}",
    content: `Kính gửi {{candidate_name}},

Cảm ơn bạn đã quan tâm đến vị trí {{job_title}} tại {{company_name}} và dành thời gian tham gia quy trình tuyển dụng.

Sau khi xem xét kỹ lưỡng, chúng tôi quyết định tiếp tục với những ứng viên khác có kinh nghiệm phù hợp hơn với yêu cầu hiện tại.

Chúng tôi rất ấn tượng với hồ sơ của bạn và mong muốn có cơ hội hợp tác trong tương lai.

Chúc bạn thành công!
{{recruiter_name}}
{{company_name}}`
  },
  welcome: {
    subject: "Chào mừng bạn gia nhập {{company_name}}!",
    content: `Chào {{candidate_name}},

Chào mừng bạn gia nhập đại gia đình {{company_name}}! 🎉

Chúng tôi rất vui mừng có bạn trong vai trò {{job_title}} tại {{department}}.

📋 Thông tin cần biết:
• Ngày bắt đầu: {{start_date}}
• Báo cáo với: {{manager_name}}
• Địa điểm làm việc: {{office_location}}

Một số điều cần chuẩn bị trước ngày đầu tiên sẽ được gửi riêng.

Chào mừng bạn!
{{hr_team_name}}
{{company_name}}`
  },
};
