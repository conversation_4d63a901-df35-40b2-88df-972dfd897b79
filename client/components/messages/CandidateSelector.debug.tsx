import React, { useState } from "react";
import { CandidateSelector, CandidateOption } from "./CandidateSelector";

/**
 * Debug component to test CandidateSelector interactions
 * This can be temporarily used to verify hover and selection work correctly
 */
export const CandidateSelectorDebug: React.FC = () => {
  const [selectedCandidate1, setSelectedCandidate1] = useState<CandidateOption | null>(null);
  const [selectedCandidate2, setSelectedCandidate2] = useState<CandidateOption | null>(null);

  return (
    <div className="p-6 space-y-8 max-w-2xl">
      <h2 className="text-2xl font-bold">CandidateSelector Debug</h2>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Test 1: Select Variant</h3>
        <CandidateSelector
          label="Select Candidate (Select Variant)"
          placeholder="Choose a candidate..."
          value={selectedCandidate1?.id}
          onValueChange={(candidate) => {
            console.log("Selected candidate 1:", candidate);
            setSelectedCandidate1(candidate);
          }}
          variant="select"
        />
        {selectedCandidate1 && (
          <div className="p-3 bg-green-50 rounded">
            <p><strong>Selected:</strong> {selectedCandidate1.name}</p>
            <p><strong>Email:</strong> {selectedCandidate1.email}</p>
          </div>
        )}
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Test 2: Combobox Variant</h3>
        <CandidateSelector
          label="Select Candidate (Combobox Variant)"
          placeholder="Search and choose..."
          value={selectedCandidate2?.id}
          onValueChange={(candidate) => {
            console.log("Selected candidate 2:", candidate);
            setSelectedCandidate2(candidate);
          }}
          variant="combobox"
          showSearch={true}
        />
        {selectedCandidate2 && (
          <div className="p-3 bg-blue-50 rounded">
            <p><strong>Selected:</strong> {selectedCandidate2.name}</p>
            <p><strong>Email:</strong> {selectedCandidate2.email}</p>
          </div>
        )}
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Debug Info</h3>
        <div className="text-sm text-gray-600">
          <p>• Check browser console for selection logs</p>
          <p>• Hover should work on dropdown items</p>
          <p>• Click should select items and close dropdown</p>
          <p>• Search should filter items in combobox variant</p>
        </div>
      </div>
    </div>
  );
};

export default CandidateSelectorDebug;
