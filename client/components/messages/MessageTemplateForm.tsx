import React, { useState, useEffect, use<PERSON>emo } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Save,
  X,
  Eye,
  FileText,
  AlertCircle,
  Plus,
  Minus,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>cle,
} from "lucide-react";
import { toast } from "sonner";
import {
  messageTemplateService,
  MessageTemplate,
  CreateTemplateData,
  UpdateTemplateData,
} from "@/lib/services/messageTemplateService";
import { TemplateCategorySelector } from "./TemplateCategorySelector";
import { TemplateVariableEditor } from "./TemplateVariableEditor";
import { MessageTemplatePreview } from "./MessageTemplatePreview";

export interface MessageTemplateFormProps {
  isOpen: boolean;
  onClose: () => void;
  template?: MessageTemplate | null;
  mode: "create" | "edit";
  onSave?: (template: MessageTemplate) => void;
  initialData?: Partial<CreateTemplateData>;
}

interface FormData {
  name: string;
  subject: string;
  content: string;
  category: string;
  type: string;
  language: string;
  is_active: boolean;
  variables: string[];
}

interface ValidationErrors {
  name?: string;
  subject?: string;
  content?: string;
  category?: string;
  type?: string;
}

export const MessageTemplateForm: React.FC<MessageTemplateFormProps> = ({
  isOpen,
  onClose,
  template,
  mode,
  onSave,
  initialData,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    subject: "",
    content: "",
    category: "",
    type: "email",
    language: "vi",
    is_active: true,
    variables: [],
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [loading, setSaving] = useState(false);
  const [categories, setCategories] = useState<Record<string, string>>({});
  const [types, setTypes] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState("basic");
  const [previewData, setPreviewData] = useState<Record<string, any>>({});

  // Extract variables from content automatically
  const extractedVariables = useMemo(() => {
    return messageTemplateService.extractVariables(
      formData.content + " " + formData.subject,
    );
  }, [formData.content, formData.subject]);

  // Vietnamese validation messages
  const validateForm = (): ValidationErrors => {
    const newErrors: ValidationErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Tên template là bắt buộc";
    } else if (formData.name.trim().length < 3) {
      newErrors.name = "Tên template phải có ít nhất 3 ký tự";
    }

    if (!formData.content.trim()) {
      newErrors.content = "Nội dung template là bắt buộc";
    } else if (formData.content.trim().length < 10) {
      newErrors.content = "Nội dung template phải có ít nhất 10 ký tự";
    }

    if (!formData.category) {
      newErrors.category = "Vui lòng chọn danh mục";
    }

    if (!formData.type) {
      newErrors.type = "Vui lòng chọn loại template";
    }

    if (formData.type === "email" && !formData.subject.trim()) {
      newErrors.subject = "Tiêu đề email là bắt buộc";
    }

    return newErrors;
  };

  // Load initial data
  useEffect(() => {
    if (template && mode === "edit") {
      setFormData({
        name: template.name,
        subject: template.subject || "",
        content: template.content,
        category: template.category,
        type: template.type,
        language: template.language,
        is_active: template.is_active,
        variables: template.variables || [],
      });
    } else if (initialData) {
      setFormData((prev) => ({
        ...prev,
        ...initialData,
      }));
    } else {
      // Reset form for create mode
      setFormData({
        name: "",
        subject: "",
        content: "",
        category: "",
        type: "email",
        language: "vi",
        is_active: true,
        variables: [],
      });
    }
  }, [template, mode, initialData, isOpen]);

  // Load categories and types
  useEffect(() => {
    const loadData = async () => {
      try {
        const [categoriesRes, typesRes] = await Promise.all([
          messageTemplateService.getCategories(),
          messageTemplateService.getTypes(),
        ]);

        if (categoriesRes.status === "success" || categoriesRes.data) {
          setCategories(categoriesRes.data || categoriesRes);
        }

        if (typesRes.status === "success" || typesRes.data) {
          setTypes(typesRes.data || typesRes);
        }
      } catch (error) {
        console.error("Error loading form data:", error);
      }
    };

    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  // Update extracted variables
  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      variables: extractedVariables,
    }));
  }, [extractedVariables]);

  const handleSubmit = async () => {
    const validationErrors = validateForm();
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length > 0) {
      toast.error("Vui lòng kiểm tra và sửa các lỗi trong form");
      return;
    }

    setSaving(true);

    try {
      const templateData = {
        name: formData.name.trim(),
        subject:
          formData.type === "email" ? formData.subject.trim() : undefined,
        content: formData.content.trim(),
        category: formData.category,
        type: formData.type,
        language: formData.language,
        is_active: formData.is_active,
        variables: formData.variables,
      };

      let response;
      if (mode === "edit" && template) {
        response = await messageTemplateService.updateTemplate(
          template.id,
          templateData as UpdateTemplateData,
        );
      } else {
        response = await messageTemplateService.createTemplate(
          templateData as CreateTemplateData,
        );
      }

      if (response.status === "success") {
        toast.success(
          mode === "edit"
            ? "Template đã được cập nhật thành công"
            : "Template đã được tạo thành công",
        );

        if (onSave && response.data) {
          onSave(response.data);
        }

        onClose();
      } else {
        throw new Error(response.message || "Không thể lưu template");
      }
    } catch (error) {
      console.error("Error saving template:", error);
      toast.error(
        mode === "edit"
          ? "Không thể cập nhật template"
          : "Không thể tạo template",
      );
    } finally {
      setSaving(false);
    }
  };

  const handleVariableAdd = (variable: string) => {
    if (variable && !formData.variables.includes(variable)) {
      setFormData((prev) => ({
        ...prev,
        variables: [...prev.variables, variable],
      }));
    }
  };

  const handleVariableRemove = (variable: string) => {
    setFormData((prev) => ({
      ...prev,
      variables: prev.variables.filter((v) => v !== variable),
    }));
  };

  const handlePreviewDataChange = (data: Record<string, any>) => {
    setPreviewData(data);
  };

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById(
      "template-content",
    ) as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const before = text.substring(0, start);
      const after = text.substring(end, text.length);
      const newText = before + `{{${variable}}}` + after;

      setFormData((prev) => ({ ...prev, content: newText }));

      // Set cursor position after the inserted variable
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(
          start + variable.length + 4,
          start + variable.length + 4,
        );
      }, 10);
    }
  };

  const commonVariables = [
    {
      name: "candidate_name",
      label: "Tên ứng viên",
      description: "Tên đầy đủ của ứng viên",
    },
    {
      name: "job_title",
      label: "V�� trí công việc",
      description: "Tên vị trí ứng tuyển",
    },
    {
      name: "company_name",
      label: "Tên công ty",
      description: "Tên công ty tuyển dụng",
    },
    {
      name: "recruiter_name",
      label: "Tên nhà tuyển dụng",
      description: "Tên người phụ trách tuyển dụng",
    },
    {
      name: "interview_date",
      label: "Ngày phỏng vấn",
      description: "Ngày diễn ra phỏng vấn",
    },
    {
      name: "interview_time",
      label: "Giờ phỏng vấn",
      description: "Thời gian phỏng vấn",
    },
    {
      name: "interview_location",
      label: "Địa điểm phỏng vấn",
      description: "Nơi diễn ra phỏng vấn",
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            {mode === "edit" ? "Chỉnh sửa Template" : "Tạo Template Mới"}
          </DialogTitle>
          <DialogDescription>
            {mode === "edit"
              ? "Cập nhật thông tin và nội dung template"
              : "Tạo template tin nhắn mới cho việc tuyển dụng"}
          </DialogDescription>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">Thông tin cơ bản</TabsTrigger>
            <TabsTrigger value="content">Nội dung & Biến</TabsTrigger>
            <TabsTrigger value="preview">Xem trước</TabsTrigger>
          </TabsList>

          {/* Basic Information Tab */}
          <TabsContent value="basic" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Tên Template *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, name: e.target.value }))
                    }
                    placeholder="Nhập tên template..."
                    className={errors.name ? "border-destructive" : ""}
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.name}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="category">Danh mục *</Label>
                  <TemplateCategorySelector
                    value={formData.category}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, category: value }))
                    }
                    categories={categories}
                    error={errors.category}
                  />
                </div>

                <div>
                  <Label htmlFor="type">Loại Template *</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, type: value }))
                    }
                  >
                    <SelectTrigger
                      className={errors.type ? "border-destructive" : ""}
                    >
                      <SelectValue placeholder="Chọn loại template..." />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(types).map(([key, value]) => (
                        <SelectItem key={key} value={key}>
                          {value}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.type && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.type}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="language">Ngôn ngữ</Label>
                  <Select
                    value={formData.language}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, language: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="vi">Tiếng Việt</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                {formData.type === "email" && (
                  <div>
                    <Label htmlFor="subject">Tiêu đề Email *</Label>
                    <Input
                      id="subject"
                      value={formData.subject}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          subject: e.target.value,
                        }))
                      }
                      placeholder="Nhập tiêu đề email..."
                      className={errors.subject ? "border-destructive" : ""}
                    />
                    {errors.subject && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.subject}
                      </p>
                    )}
                    <p className="text-sm text-muted-foreground mt-1">
                      Có thể sử dụng biến như{" "}
                      {`{{candidate_name}}, {{job_title}}`}
                    </p>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) =>
                      setFormData((prev) => ({ ...prev, is_active: checked }))
                    }
                  />
                  <Label htmlFor="is_active">Template hoạt động</Label>
                </div>

                {extractedVariables.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">
                        Biến được phát hiện
                      </CardTitle>
                      <CardDescription className="text-xs">
                        Các biến được tự động phát hiện từ nội dung
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {extractedVariables.map((variable) => (
                          <Badge
                            key={variable}
                            variant="secondary"
                            className="text-xs"
                          >
                            {`{{${variable}}}`}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Content & Variables Tab */}
          <TabsContent value="content" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-4">
                <div>
                  <Label htmlFor="template-content">Nội dung Template *</Label>
                  <Textarea
                    id="template-content"
                    value={formData.content}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        content: e.target.value,
                      }))
                    }
                    placeholder="Nhập nội dung template... Sử dụng {{tên_biến}} để thêm biến động"
                    rows={15}
                    className={`font-mono text-sm resize-none ${errors.content ? "border-destructive" : ""}`}
                  />
                  {errors.content && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.content}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Sử dụng cú pháp {`{{tên_biến}}`} để chèn nội dung động
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Biến thường dùng</CardTitle>
                    <CardDescription className="text-xs">
                      Click để chèn vào nội dung
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {commonVariables.map((variable) => (
                      <div
                        key={variable.name}
                        className="p-2 border rounded cursor-pointer hover:bg-muted transition-colors"
                        onClick={() => insertVariable(variable.name)}
                      >
                        <div className="font-medium text-sm">
                          {variable.label}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {variable.description}
                        </div>
                        <Badge variant="outline" className="text-xs mt-1">
                          {`{{${variable.name}}}`}
                        </Badge>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                <TemplateVariableEditor
                  variables={formData.variables}
                  onAddVariable={handleVariableAdd}
                  onRemoveVariable={handleVariableRemove}
                  extractedVariables={extractedVariables}
                />
              </div>
            </div>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-6">
            <MessageTemplatePreview
              template={
                {
                  id: template?.id || 0,
                  name: formData.name,
                  subject: formData.subject,
                  content: formData.content,
                  variables: formData.variables,
                  category: formData.category,
                  type: formData.type,
                } as MessageTemplate
              }
              previewData={previewData}
              onPreviewDataChange={handlePreviewDataChange}
            />
          </TabsContent>
        </Tabs>

        {/* Form Actions */}
        <div className="flex justify-between pt-6 border-t">
          <div>
            {Object.keys(errors).length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Vui lòng kiểm tra và sửa các lỗi trong form trước khi lưu.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              <X className="w-4 h-4 mr-2" />
              Hủy
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={loading}
              className="gap-2"
            >
              {loading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {mode === "edit" ? "Cập nhật" : "Tạo"} Template
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
