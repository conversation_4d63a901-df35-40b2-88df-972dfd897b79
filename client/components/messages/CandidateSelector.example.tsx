import React, { useState } from "react";
import { CandidateSelector, CandidateOption } from "./CandidateSelector";

/**
 * Example usage of the CandidateSelector component
 * This demonstrates various configurations and integration patterns
 */
export const CandidateSelectorExamples: React.FC = () => {
  const [selectedCandidate1, setSelectedCandidate1] = useState<CandidateOption | null>(null);
  const [selectedCandidate2, setSelectedCandidate2] = useState<CandidateOption | null>(null);
  const [selectedCandidate3, setSelectedCandidate3] = useState<CandidateOption | null>(null);

  return (
    <div className="space-y-8 p-6 max-w-2xl">
      <h2 className="text-2xl font-bold">CandidateSelector Examples</h2>
      
      {/* Basic combobox usage */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">1. Basic Combobox (Default)</h3>
        <CandidateSelector
          label="Select Candidate"
          placeholder="Choose a candidate..."
          value={selectedCandidate1?.id}
          onValueChange={setSelectedCandidate1}
        />
        {selectedCandidate1 && (
          <div className="p-3 bg-gray-100 rounded-lg">
            <p><strong>Selected:</strong> {selectedCandidate1.name}</p>
            <p><strong>Email:</strong> {selectedCandidate1.email}</p>
            <p><strong>Status:</strong> {selectedCandidate1.status}</p>
          </div>
        )}
      </div>

      {/* Select variant */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">2. Select Variant</h3>
        <CandidateSelector
          label="Pick Recipient"
          placeholder="Select from dropdown..."
          value={selectedCandidate2?.id}
          onValueChange={setSelectedCandidate2}
          variant="select"
          includeActiveOnly={true}
        />
        {selectedCandidate2 && (
          <div className="p-3 bg-blue-50 rounded-lg">
            <p><strong>Selected:</strong> {selectedCandidate2.name}</p>
            <p><strong>Position:</strong> {selectedCandidate2.position}</p>
          </div>
        )}
      </div>

      {/* Advanced configuration */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">3. Advanced Configuration</h3>
        <CandidateSelector
          label="Message Recipient"
          placeholder="Find candidate by name, email, or position..."
          value={selectedCandidate3?.id}
          onValueChange={setSelectedCandidate3}
          variant="combobox"
          showSearch={true}
          includeActiveOnly={false}
          includeJobInfo={true}
          className="border-2 border-blue-200"
        />
        {selectedCandidate3 && (
          <div className="p-3 bg-green-50 rounded-lg">
            <p><strong>Selected:</strong> {selectedCandidate3.name}</p>
            <p><strong>Email:</strong> {selectedCandidate3.email}</p>
            <p><strong>Job Title:</strong> {selectedCandidate3.jobTitle}</p>
            <p><strong>Location:</strong> {selectedCandidate3.location}</p>
          </div>
        )}
      </div>

      {/* Error state example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">4. Error State</h3>
        <CandidateSelector
          label="Required Field"
          placeholder="This field has an error..."
          value=""
          onValueChange={() => {}}
          error="Please select a candidate"
        />
      </div>

      {/* Disabled state example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">5. Disabled State</h3>
        <CandidateSelector
          label="Disabled Selector"
          placeholder="This is disabled..."
          value=""
          onValueChange={() => {}}
          disabled={true}
        />
      </div>

      {/* Integration example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">6. Integration Example (Form-like)</h3>
        <form className="space-y-4">
          <CandidateSelector
            label="Send message to"
            placeholder="Select candidate for messaging..."
            value={selectedCandidate1?.id}
            onValueChange={(candidate) => {
              setSelectedCandidate1(candidate);
              // Here you might update form data or trigger other actions
              console.log("Candidate selected for messaging:", candidate);
            }}
          />
          
          <div>
            <label className="block text-sm font-medium mb-2">Message Type</label>
            <select className="w-full p-2 border rounded-lg">
              <option value="email">Email</option>
              <option value="sms">SMS</option>
              <option value="note">Internal Note</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Message</label>
            <textarea 
              className="w-full p-2 border rounded-lg"
              rows={4}
              placeholder="Type your message here..."
            />
          </div>
          
          <button 
            type="button"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            disabled={!selectedCandidate1}
          >
            Send Message
          </button>
        </form>
      </div>
    </div>
  );
};

export default CandidateSelectorExamples;
