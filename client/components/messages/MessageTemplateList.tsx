import React, { useState, useEffect, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Send,
  Copy,
  Trash2,
  <PERSON>,
  Refresh<PERSON><PERSON>,
  Filter,
  FileText,
  Mail,
  MessageSquare,
} from "lucide-react";
import { toast } from "sonner";
import { safeFormatDistanceToNow } from "@/lib/utils";
import {
  messageTemplateService,
  MessageTemplate,
  TemplateListParams,
} from "@/lib/services/messageTemplateService";

export interface MessageTemplateListProps {
  onTemplateSelect?: (template: MessageTemplate) => void;
  onTemplateEdit?: (template: MessageTemplate) => void;
  onTemplateCreate?: () => void;
  onTemplatePreview?: (template: MessageTemplate) => void;
  showActions?: boolean;
  showCreateButton?: boolean;
  selectable?: boolean;
  className?: string;
}

export const MessageTemplateList: React.FC<MessageTemplateListProps> = ({
  onTemplateSelect,
  onTemplateEdit,
  onTemplateCreate,
  onTemplatePreview,
  showActions = true,
  showCreateButton = true,
  selectable = false,
  className,
}) => {
  const [templates, setTemplates] = useState<MessageTemplate[]>([]);
  const [categories, setCategories] = useState<Record<string, string>>({});
  const [types, setTypes] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCategory, setFilterCategory] = useState<string>("all");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("-created_at");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] =
    useState<MessageTemplate | null>(null);
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0,
  });

  const fetchTemplates = async (params: TemplateListParams = {}) => {
    try {
      setLoading(true);
      setError(null);

      const response = await messageTemplateService.getTemplates({
        page: pagination.current_page,
        per_page: pagination.per_page,
        sort: sortBy,
        ...params,
      });

      if (response.status === "success" || response.data) {
        const data = response.data || response;
        setTemplates(data.data || data);
        if (data.meta) {
          setPagination(data.meta);
        }
      } else {
        throw new Error(response.message || "Failed to fetch templates");
      }
    } catch (err) {
      console.error("Error fetching templates:", err);
      setError(err instanceof Error ? err.message : "Failed to load templates");
      toast.error("Không thể tải danh sách template");
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await messageTemplateService.getCategories();
      if (response.status === "success" || response.data) {
        setCategories(response.data || response);
      }
    } catch (err) {
      console.error("Error fetching categories:", err);
    }
  };

  const fetchTypes = async () => {
    try {
      const response = await messageTemplateService.getTypes();
      if (response.status === "success" || response.data) {
        setTypes(response.data || response);
      }
    } catch (err) {
      console.error("Error fetching types:", err);
    }
  };

  useEffect(() => {
    fetchTemplates();
    fetchCategories();
    fetchTypes();
  }, [pagination.current_page, sortBy]);

  // Apply filters
  const filteredTemplates = useMemo(() => {
    return templates.filter((template) => {
      const matchesSearch =
        searchTerm === "" ||
        template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.content.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory =
        filterCategory === "all" || template.category === filterCategory;
      const matchesType = filterType === "all" || template.type === filterType;
      const matchesStatus =
        filterStatus === "all" ||
        (filterStatus === "active" && template.is_active) ||
        (filterStatus === "inactive" && !template.is_active);

      return matchesSearch && matchesCategory && matchesType && matchesStatus;
    });
  }, [templates, searchTerm, filterCategory, filterType, filterStatus]);

  const handleSearch = () => {
    const params: TemplateListParams = {};

    if (searchTerm) params.search = searchTerm;
    if (filterCategory !== "all") params.category = filterCategory;
    if (filterType !== "all") params.type = filterType;
    if (filterStatus === "active") params.is_active = true;
    if (filterStatus === "inactive") params.is_active = false;

    fetchTemplates(params);
  };

  const handleRefresh = () => {
    fetchTemplates();
  };

  const handleTemplateUse = (template: MessageTemplate) => {
    if (onTemplateSelect) {
      setTimeout(() => {
        onTemplateSelect(template);
      }, 100);
    } else {
      toast.success(`Template "${template.name}" đã được ch��n`);
    }
  };

  const handleTemplateEdit = (template: MessageTemplate) => {
    if (onTemplateEdit) {
      //set delay run function openTemplateEdit
      setTimeout(() => {
        onTemplateEdit(template);
      }, 100);
      //onTemplateEdit(template);
    }
  };

  const handleTemplatePreview = (template: MessageTemplate) => {
    if (onTemplatePreview) {
      setTimeout(() => {
        onTemplatePreview(template);
      }, 100);
    }
  };

  const handleTemplateCopy = async (template: MessageTemplate) => {
    try {
      await navigator.clipboard.writeText(template.content);
      toast.success("Nội dung template đã được sao chép");
    } catch (err) {
      toast.error("Không thể sao chép nội dung");
    }
  };

  const handleTemplateDuplicate = async (template: MessageTemplate) => {
    try {
      const response = await messageTemplateService.duplicateTemplate(
        template.id,
      );
      if (response.status === "success") {
        toast.success("Template đã được sao chép thành công");
        fetchTemplates();
      }
    } catch (err) {
      toast.error("Không thể sao chép template");
    }
  };

  const confirmDelete = (template: MessageTemplate) => {
    setTemplateToDelete(template);
    setDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!templateToDelete) return;

    try {
      await messageTemplateService.deleteTemplate(templateToDelete.id);
      toast.success("Template đã được xóa thành công");
      setTemplates(templates.filter((t) => t.id !== templateToDelete.id));
      setDeleteDialogOpen(false);
      setTemplateToDelete(null);
    } catch (err) {
      toast.error("Không thể xóa template");
    }
  };

  const getTemplateIcon = (type: string) => {
    switch (type) {
      case "email":
        return <Mail className="w-4 h-4 text-blue-600" />;
      case "sms":
        return <MessageSquare className="w-4 h-4 text-green-600" />;
      default:
        return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      interview: "bg-blue-100 text-blue-800 border-blue-200",
      offer: "bg-green-100 text-green-800 border-green-200",
      feedback: "bg-yellow-100 text-yellow-800 border-yellow-200",
      reminder: "bg-orange-100 text-orange-800 border-orange-200",
      rejection: "bg-red-100 text-red-800 border-red-200",
      welcome: "bg-purple-100 text-purple-800 border-purple-200",
    };
    return colors[category] || "bg-gray-100 text-gray-800 border-gray-200";
  };

  if (error) {
    return (
      <Card className="p-8 text-center">
        <div className="flex flex-col items-center gap-4">
          <FileText className="w-12 h-12 text-muted-foreground" />
          <div>
            <h3 className="text-lg font-semibold text-destructive">
              Lỗi tải dữ liệu
            </h3>
            <p className="text-muted-foreground">{error}</p>
          </div>
          <Button onClick={handleRefresh} variant="outline" className="gap-2">
            <RefreshCw className="w-4 h-4" />
            Thử lại
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Message Templates</h2>
          <p className="text-muted-foreground">
            Quản lý các template tin nhắn cho việc tuyển dụng
          </p>
        </div>
        {showCreateButton && (
          <Button onClick={onTemplateCreate} className="gap-2">
            <Plus className="w-4 h-4" />
            Tạo Template Mới
          </Button>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Tìm kiếm template..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSearch()}
            className="pl-10 rounded-xl"
          />
        </div>

        <Select value={filterCategory} onValueChange={setFilterCategory}>
          <SelectTrigger className="w-[180px] rounded-xl">
            <SelectValue placeholder="Tất cả danh mục" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả danh mục</SelectItem>
            {Object.entries(categories).map(([key, value]) => (
              <SelectItem key={key} value={key}>
                {value}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-[150px] rounded-xl">
            <SelectValue placeholder="Tất cả loại" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả loại</SelectItem>
            {Object.entries(types).map(([key, value]) => (
              <SelectItem key={key} value={key}>
                {value}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-[150px] rounded-xl">
            <SelectValue placeholder="Trạng thái" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả</SelectItem>
            <SelectItem value="active">Hoạt động</SelectItem>
            <SelectItem value="inactive">Không hoạt động</SelectItem>
          </SelectContent>
        </Select>

        <Button
          onClick={handleSearch}
          variant="outline"
          className="gap-2 rounded-xl"
        >
          <Filter className="w-4 h-4" />
          Lọc
        </Button>

        <Button
          onClick={handleRefresh}
          variant="outline"
          className="gap-2 rounded-xl"
        >
          <RefreshCw className="w-4 h-4" />
          Làm mới
        </Button>
      </div>

      {/* Templates Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <Card
              key={template.id}
              className={`hover:shadow-md transition-shadow ${
                selectable ? "cursor-pointer hover:border-primary" : ""
              }`}
              onClick={
                selectable ? () => handleTemplateUse(template) : undefined
              }
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-2 flex-1 min-w-0">
                    {getTemplateIcon(template.type)}
                    <div className="min-w-0 flex-1">
                      <CardTitle className="text-sm truncate">
                        {template.name}
                      </CardTitle>
                      <CardDescription className="text-xs">
                        {template.category_name ||
                          messageTemplateService.getCategoryDisplayName(
                            template.category,
                          )}{" "}
                        • Cập nhật{" "}
                        {safeFormatDistanceToNow(template.updated_at, {
                          addSuffix: true,
                        })}
                      </CardDescription>
                    </div>
                  </div>
                  {showActions && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Hành động</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() => handleTemplateUse(template)}
                        >
                          <Send className="mr-2 h-4 w-4" />
                          Sử dụng Template
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleTemplateEdit(template)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleTemplateDuplicate(template)}
                        >
                          <Copy className="mr-2 h-4 w-4" />
                          Nhân bản Template
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => confirmDelete(template)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                <div>
                  <p className="text-sm font-medium mb-1 line-clamp-1">
                    {template.subject || "Không có tiêu đề"}
                  </p>
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {template.content.substring(0, 150)}...
                  </p>
                </div>

                <div className="flex items-center justify-between flex-wrap gap-2">
                  <div className="flex items-center gap-2">
                    <Badge className={getCategoryColor(template.category)}>
                      {template.category_name ||
                        messageTemplateService.getCategoryDisplayName(
                          template.category,
                        )}
                    </Badge>
                    <Badge
                      variant={template.is_active ? "default" : "secondary"}
                    >
                      {template.is_active ? "Hoạt động" : "Không hoạt động"}
                    </Badge>
                  </div>

                  <div className="text-xs text-muted-foreground">
                    {template.variables.length} biến
                  </div>
                </div>

                {template.variables.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {template.variables.slice(0, 3).map((variable) => (
                      <Badge
                        key={variable}
                        variant="outline"
                        className="text-xs"
                      >
                        {variable}
                      </Badge>
                    ))}
                    {template.variables.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{template.variables.length - 3}
                      </Badge>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {filteredTemplates.length === 0 && !loading && (
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center gap-4">
            <FileText className="w-12 h-12 text-muted-foreground" />
            <div>
              <h3 className="text-lg font-semibold">Không tìm thấy template</h3>
              <p className="text-muted-foreground">
                {searchTerm ||
                filterCategory !== "all" ||
                filterType !== "all" ||
                filterStatus !== "all"
                  ? "Thử điều chỉnh bộ lọc tìm kiếm."
                  : "Chưa có template nào được tạo."}
              </p>
            </div>
            {showCreateButton && onTemplateCreate && (
              <Button onClick={onTemplateCreate} className="gap-2">
                <Plus className="w-4 h-4" />
                Tạo Template Đầu Tiên
              </Button>
            )}
          </div>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa template</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa template "{templateToDelete?.name}"?
              Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive hover:bg-destructive/90"
            >
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
