import { useState, useEffect, useMemo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  User,
  AlertCircle,
  ChevronsUpDown,
  Check,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { useCandidates } from "@/hooks/useApi";
import { UiCandidate } from "@/lib/adapters/types";
import { useTranslation } from "@/lib/i18n";

export interface CandidateOption {
  id: string;
  name: string;
  email: string;
  phone?: string;
  position?: string;
  avatar?: string;
  initials: string;
  status: string;
  location?: string;
  jobTitle?: string;
}

interface CandidateSelectorProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onValueChange: (candidate: CandidateOption | null) => void;
  disabled?: boolean;
  error?: string;
  className?: string;
  variant?: "select" | "combobox";
  showSearch?: boolean;
  includeActiveOnly?: boolean;
  includeJobInfo?: boolean;
}

export const CandidateSelector = ({
  label = "Candidate",
  placeholder = "Select candidate...",
  value,
  onValueChange,
  disabled = false,
  error,
  className,
  variant = "combobox",
  showSearch = true,
  includeActiveOnly = true,
  includeJobInfo = true,
}: CandidateSelectorProps) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  // Fetch candidates with appropriate parameters
  const {
    data: candidatesResponse,
    isLoading,
    error: fetchError,
  } = useCandidates({
    per_page: 100,
    include: includeJobInfo ? "jobPosting" : undefined,
    filter: includeActiveOnly
      ? { status: ["active", "interview", "offer"] }
      : undefined,
    sort: "name",
  });

  // Convert API candidates to selector options
  const candidateOptions = useMemo((): CandidateOption[] => {
    if (!candidatesResponse?.data) return [];

    return candidatesResponse.data.map(
      (candidate: UiCandidate): CandidateOption => ({
        id: candidate.id,
        name: candidate.name || "No name",
        email: candidate.email || "",
        phone: candidate.phone,
        position: candidate.position,
        avatar: candidate.avatar,
        initials: candidate.initials,
        status: candidate.status,
        location: candidate.location,
        jobTitle: candidate.jobPosting?.title || candidate.position,
      }),
    );
  }, [candidatesResponse]);

  // Filter candidates based on search term
  const filteredCandidates = useMemo(() => {
    if (!searchTerm.trim()) return candidateOptions;

    const searchLower = searchTerm.toLowerCase();
    return candidateOptions.filter(
      (candidate) =>
        candidate.name.toLowerCase().includes(searchLower) ||
        candidate.email.toLowerCase().includes(searchLower) ||
        (candidate.position &&
          candidate.position.toLowerCase().includes(searchLower)) ||
        (candidate.jobTitle &&
          candidate.jobTitle.toLowerCase().includes(searchLower)) ||
        (candidate.location &&
          candidate.location.toLowerCase().includes(searchLower)),
    );
  }, [candidateOptions, searchTerm]);

  // Get selected candidate
  const selectedCandidate = candidateOptions.find(
    (candidate) => candidate.id === value,
  );

  // Helper function to get status color
  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case "active":
      case "sourced":
        return "bg-blue-100 text-blue-800";
      case "applied":
        return "bg-green-100 text-green-800";
      case "screening":
        return "bg-yellow-100 text-yellow-800";
      case "interview":
        return "bg-purple-100 text-purple-800";
      case "offer":
        return "bg-orange-100 text-orange-800";
      case "hired":
        return "bg-emerald-100 text-emerald-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Error handling
  if (fetchError) {
    return (
      <div className={cn("space-y-2", className)}>
        {label && <Label htmlFor="candidate-selector">{label}</Label>}
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load candidates. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Combobox variant (with search)
  return (
    <div className={cn("space-y-2", className) + ""}>
      {label && <Label htmlFor="candidate-selector">{label}</Label>}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isOpen}
            className={cn(
              "w-full justify-between",
              error && "border-destructive",
              disabled && "cursor-not-allowed opacity-50",
            )}
            disabled={disabled || isLoading}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Loading candidates...</span>
              </div>
            ) : selectedCandidate ? (
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <Avatar className="h-5 w-5">
                  <AvatarImage src={selectedCandidate.avatar} />
                  <AvatarFallback className="text-xs">
                    {selectedCandidate.initials}
                  </AvatarFallback>
                </Avatar>
                <span className="truncate">{selectedCandidate.name}</span>
                <Badge
                  variant="outline"
                  className={cn(
                    "text-xs",
                    getStatusColor(selectedCandidate.status),
                  )}
                >
                  {selectedCandidate.status}
                </Badge>
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-[400px] p-0 z-50 pointer-events-auto"
          align="start"
          sideOffset={4}
        >
          <Command>
            {showSearch && (
              <CommandInput
                placeholder="Search candidates by name, email, or position..."
                value={searchTerm}
                onValueChange={setSearchTerm}
              />
            )}
            <CommandList>
              <CommandEmpty>
                {searchTerm
                  ? `No candidates found for "${searchTerm}"`
                  : "No candidates available"}
              </CommandEmpty>
              <CommandGroup heading={`${filteredCandidates.length} candidates`}>
                {filteredCandidates.map((candidate) => (
                  <CommandItem
                    key={candidate.id}
                    value={`${candidate.name} ${candidate.email} ${candidate.position || ""}`}
                    onSelect={() => {
                      onValueChange(candidate);
                      setSearchTerm("");
                      setIsOpen(false);
                    }}
                    disabled={
                      candidate.status === "rejected" || !candidate.email
                    }
                    className={cn(
                      "hover:bg-accent focus:bg-accent aria-selected:bg-accent",
                      (candidate.status === "rejected" || !candidate.email) &&
                        "opacity-50 cursor-not-allowed hover:bg-transparent",
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8 shrink-0">
                        <AvatarImage src={candidate.avatar} />
                        <AvatarFallback className="text-xs bg-primary/10">
                          {candidate.initials}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium truncate">
                            {candidate.name}
                          </span>
                          <Badge
                            variant="outline"
                            className={cn(
                              "text-xs",
                              getStatusColor(candidate.status),
                            )}
                          >
                            {candidate.status}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground truncate">
                          {candidate.email || "No email"}
                        </div>
                        {(candidate.jobTitle || candidate.position) && (
                          <div className="text-xs text-muted-foreground truncate">
                            {candidate.jobTitle || candidate.position}
                          </div>
                        )}
                        {candidate.location && (
                          <div className="text-xs text-muted-foreground truncate">
                            📍 {candidate.location}
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col items-end gap-1 shrink-0">
                        {!candidate.email && (
                          <Badge variant="destructive" className="text-xs">
                            No email
                          </Badge>
                        )}
                        <Check
                          className={cn(
                            "h-4 w-4 ",
                            selectedCandidate?.id === candidate.id
                              ? "opacity-100"
                              : "opacity-0",
                          )}
                        />
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
};

export default CandidateSelector;
