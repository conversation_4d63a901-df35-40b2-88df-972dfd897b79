import React, { useState, useEffect, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  RefreshCw,
  Eye,
  Mail,
  MessageSquare,
  AlertCircle,
  CheckCircle,
  Copy,
  Send,
} from "lucide-react";
import { toast } from "sonner";
import { messageTemplateService, MessageTemplate, TemplatePreview } from "@/lib/services/messageTemplateService";

export interface MessageTemplatePreviewProps {
  template: MessageTemplate;
  previewData?: Record<string, any>;
  onPreviewDataChange?: (data: Record<string, any>) => void;
  className?: string;
  showDataEditor?: boolean;
  showActions?: boolean;
}

interface PreviewState {
  loading: boolean;
  preview: TemplatePreview | null;
  error: string | null;
}

export const MessageTemplatePreview: React.FC<MessageTemplatePreviewProps> = ({
  template,
  previewData = {},
  onPreviewDataChange,
  className,
  showDataEditor = true,
  showActions = true,
}) => {
  const [state, setState] = useState<PreviewState>({
    loading: false,
    preview: null,
    error: null,
  });

  const [localPreviewData, setLocalPreviewData] = useState<Record<string, any>>({});
  const [activeTab, setActiveTab] = useState<string>("preview");

  // Sample Vietnamese data for common variables
  const sampleData: Record<string, any> = {
    candidate_name: "Nguyễn Văn Minh",
    job_title: "Lập trình viên Frontend Senior",
    company_name: "Công ty TNHH Công nghệ ABC",
    recruiter_name: "Trần Thị Hương",
    recruiter_email: "<EMAIL>",
    recruiter_phone: "0123456789",
    interview_date: "25/01/2025",
    interview_time: "14:00",
    interview_location: "Tầng 8, Tòa nhà XYZ, 123 Đường ABC, Quận 1, TP.HCM",
    interview_type: "Phỏng vấn trực tiếp",
    duration: "90 phút",
    position: "Frontend Developer",
    department: "Phòng Công nghệ Thông tin",
    salary: "25,000,000 - 35,000,000 VNĐ",
    start_date: "01/02/2025",
    offer_deadline: "30/01/2025",
    hiring_manager_name: "Lê Văn Tú",
    hiring_manager_title: "Trưởng phòng IT",
    email: "<EMAIL>",
    phone: "0987654321",
    website: "https://abc.com.vn",
    address: "123 Đường ABC, Quận 1, TP.HCM",
  };

  // Merge sample data with preview data and local data
  const currentPreviewData = useMemo(() => {
    return { ...sampleData, ...previewData, ...localPreviewData };
  }, [previewData, localPreviewData]);

  // Generate preview
  const generatePreview = async () => {
    if (!template.id || template.id === 0) {
      // For templates that haven't been saved yet, generate local preview
      generateLocalPreview();
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await messageTemplateService.previewTemplate(
        template.id,
        currentPreviewData
      );

      if (response.status === "success" && response.data) {
        setState(prev => ({
          ...prev,
          preview: response.data,
          loading: false,
        }));
      } else {
        throw new Error(response.message || "Không thể tạo preview");
      }
    } catch (error) {
      console.error("Error generating preview:", error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Lỗi tạo preview",
        loading: false,
      }));
      
      // Fallback to local preview
      generateLocalPreview();
    }
  };

  // Generate local preview (for unsaved templates or fallback)
  const generateLocalPreview = () => {
    try {
      let previewSubject = template.subject || "";
      let previewContent = template.content || "";

      // Simple variable substitution
      Object.entries(currentPreviewData).forEach(([key, value]) => {
        const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, "g");
        previewSubject = previewSubject.replace(regex, String(value));
        previewContent = previewContent.replace(regex, String(value));
      });

      const localPreview: TemplatePreview = {
        template: {
          id: template.id,
          name: template.name,
          category: template.category,
          type: template.type,
        },
        preview: {
          subject: previewSubject,
          content: previewContent,
        },
        variables_used: currentPreviewData,
        missing_variables: template.variables?.filter(
          variable => !(variable in currentPreviewData)
        ) || [],
        available_variables: template.variables || [],
      };

      setState(prev => ({
        ...prev,
        preview: localPreview,
        loading: false,
        error: null,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: "Không thể tạo preview cục bộ",
        loading: false,
      }));
    }
  };

  // Auto-generate preview when template or data changes
  useEffect(() => {
    if (template) {
      generatePreview();
    }
  }, [template, currentPreviewData]);

  // Initialize preview data with template variables
  useEffect(() => {
    if (template.variables && template.variables.length > 0) {
      const initialData: Record<string, any> = {};
      
      template.variables.forEach(variable => {
        if (!(variable in currentPreviewData)) {
          initialData[variable] = sampleData[variable] || `{{${variable}}}`;
        }
      });

      if (Object.keys(initialData).length > 0) {
        setLocalPreviewData(prev => ({ ...prev, ...initialData }));
      }
    }
  }, [template.variables]);

  const handleDataChange = (variable: string, value: string) => {
    const newData = { ...localPreviewData, [variable]: value };
    setLocalPreviewData(newData);
    
    if (onPreviewDataChange) {
      onPreviewDataChange({ ...currentPreviewData, ...newData });
    }
  };

  const copyPreviewContent = async () => {
    if (state.preview) {
      try {
        const content = template.type === "email" 
          ? `${state.preview.preview.subject}\n\n${state.preview.preview.content}`
          : state.preview.preview.content;
        
        await navigator.clipboard.writeText(content);
        toast.success("Nội dung preview đã được sao chép");
      } catch (error) {
        toast.error("Không thể sao chép nội dung");
      }
    }
  };

  const getPreviewIcon = () => {
    return template.type === "email" 
      ? <Mail className="w-4 h-4 text-blue-600" />
      : <MessageSquare className="w-4 h-4 text-green-600" />;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {getPreviewIcon()}
          <h3 className="text-lg font-semibold">Xem trước Template</h3>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={generatePreview}
            disabled={state.loading}
            variant="outline"
            size="sm"
            className="gap-2"
          >
            {state.loading ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Eye className="w-4 h-4" />
            )}
            Làm mới Preview
          </Button>
          {showActions && state.preview && (
            <Button
              onClick={copyPreviewContent}
              variant="outline"
              size="sm"
              className="gap-2"
            >
              <Copy className="w-4 h-4" />
              Sao chép
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="preview">Xem trước</TabsTrigger>
          {showDataEditor && (
            <TabsTrigger value="data">Dữ liệu mẫu</TabsTrigger>
          )}
        </TabsList>

        {/* Preview Tab */}
        <TabsContent value="preview" className="space-y-4">
          {state.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          {state.loading && (
            <Card>
              <CardContent className="p-8 text-center">
                <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">Đang tạo preview...</p>
              </CardContent>
            </Card>
          )}

          {state.preview && !state.loading && (
            <div className="space-y-4">
              {/* Missing Variables Warning */}
              {state.preview.missing_variables.length > 0 && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p>Một số biến chưa có dữ liệu:</p>
                      <div className="flex flex-wrap gap-1">
                        {state.preview.missing_variables.map(variable => (
                          <Badge key={variable} variant="outline" className="text-xs">
                            {`{{${variable}}}`}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Email Preview */}
              {template.type === "email" && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      Email Preview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Subject */}
                    <div>
                      <Label className="text-xs text-muted-foreground">Tiêu đề:</Label>
                      <div className="p-3 bg-muted/50 rounded border">
                        <p className="font-medium">{state.preview.preview.subject}</p>
                      </div>
                    </div>

                    {/* Content */}
                    <div>
                      <Label className="text-xs text-muted-foreground">Nội dung:</Label>
                      <div className="p-4 bg-background border rounded-lg">
                        <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                          {state.preview.preview.content}
                        </pre>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* SMS Preview */}
              {template.type === "sms" && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <MessageSquare className="w-4 h-4" />
                      SMS Preview
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="max-w-sm mx-auto">
                      <div className="bg-blue-500 text-white p-3 rounded-2xl rounded-bl-sm">
                        <p className="text-sm leading-relaxed">
                          {state.preview.preview.content}
                        </p>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1 text-right">
                        {state.preview.preview.content.length} ký tự
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Variables Used */}
              {Object.keys(state.preview.variables_used).length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Biến đã sử dụng</CardTitle>
                    <CardDescription className="text-xs">
                      Các biến và giá trị tương ứng trong preview
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {Object.entries(state.preview.variables_used).map(([key, value]) => (
                        <div key={key} className="flex items-center gap-2 p-2 bg-muted/50 rounded">
                          <Badge variant="outline" className="text-xs">
                            {`{{${key}}}`}
                          </Badge>
                          <span className="text-sm truncate flex-1">
                            {String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        {/* Data Editor Tab */}
        {showDataEditor && (
          <TabsContent value="data" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Chỉnh sửa dữ liệu mẫu</CardTitle>
                <CardDescription className="text-xs">
                  Thay đổi giá trị của các biến để xem kết quả preview khác nhau
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {template.variables && template.variables.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {template.variables.map((variable) => (
                      <div key={variable} className="space-y-2">
                        <Label htmlFor={`var-${variable}`} className="text-sm">
                          {variable}
                        </Label>
                        <Input
                          id={`var-${variable}`}
                          value={currentPreviewData[variable] || ""}
                          onChange={(e) => handleDataChange(variable, e.target.value)}
                          placeholder={`Nhập giá trị cho ${variable}`}
                          className="text-sm"
                        />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <AlertCircle className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Template chưa có biến nào để chỉnh sửa
                    </p>
                  </div>
                )}

                {template.variables && template.variables.length > 0 && (
                  <div className="pt-4 border-t">
                    <Button
                      onClick={() => {
                        // Reset to sample data
                        const resetData: Record<string, any> = {};
                        template.variables.forEach(variable => {
                          resetData[variable] = sampleData[variable] || "";
                        });
                        setLocalPreviewData(resetData);
                        toast.success("Đã reset về dữ liệu mẫu");
                      }}
                      variant="outline"
                      size="sm"
                      className="gap-2"
                    >
                      <RefreshCw className="w-4 h-4" />
                      Reset về mẫu
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};
