import { useState, useMemo, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MessageSquare,
  Plus,
  Search,
  Send,
  Mail,
  FileText,
  CheckCircle,
  MoreHorizontal,
  Reply,
  Forward,
  Archive,
  Trash2,
  Star,
  RefreshCw,
  Loader2,
  AlertCircle,
} from "lucide-react";
import {
  Message,
  messageService,
  MessageListParams,
} from "@/lib/services/messageService";
import { toast } from "sonner";
import { cn, safeFormatDistanceToNow } from "@/lib/utils";
import { useTranslation } from "@/lib/i18n";

interface MessageListProps {
  type?: "inbox" | "sent" | "drafts" | "all";
  onMessageSelect?: (message: Message) => void;
  onComposeClick?: () => void;
  onReplyClick?: (message: Message) => void;
  onForwardClick?: (message: Message) => void;
  showActions?: boolean;
  candidateId?: number;
  jobPostingId?: number;
  className?: string;
}

export default function MessageList({
  type = "all",
  onMessageSelect,
  onComposeClick,
  onReplyClick,
  onForwardClick,
  showActions = true,
  candidateId,
  jobPostingId,
  className,
}: MessageListProps) {
  const { t } = useTranslation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterCategory, setFilterCategory] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalMessages, setTotalMessages] = useState(0);

  // Load messages
  useEffect(() => {
    loadMessages();
  }, [
    type,
    currentPage,
    searchTerm,
    filterType,
    filterStatus,
    filterCategory,
    candidateId,
    jobPostingId,
  ]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: MessageListParams = {
        page: currentPage,
        per_page: 15,
        include: "candidate,template",
      };

      // Add filters based on type
      if (type === "sent") {
        // We'll need to implement this logic based on the current user
        // For now, we'll use a status filter
        params.status = "sent,delivered,read";
      } else if (type === "drafts") {
        params.status = "draft";
      } else if (type === "inbox") {
        // Logic for received messages - this might need adjustment based on your data model
        params.status = "sent,delivered,read";
      }

      // Add search and filters
      if (searchTerm) {
        params.search = searchTerm;
      }
      if (filterType !== "all") {
        params.type = filterType;
      }
      if (filterStatus !== "all") {
        params.status = filterStatus;
      }
      if (filterCategory !== "all") {
        params.category = filterCategory;
      }
      if (candidateId) {
        params.candidate_id = candidateId;
      }
      if (jobPostingId) {
        params.job_posting_id = jobPostingId;
      }

      // Use authenticated API
      const response = await messageService.getMessages(params);

      if (response.data) {
        setMessages(response.data);
        setTotalPages(response.meta?.last_page || 1);
        setTotalMessages(response.meta?.total || 0);
      }
    } catch (err) {
      console.error("Error loading messages:", err);
      setError("Không thể tải danh sách tin nhắn. Vui lòng thử lại.");
      toast.error("Lỗi khi tải tin nhắn");
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setCurrentPage(1);
    loadMessages();
  };

  const handleMessageAction = async (action: string, message: Message) => {
    try {
      switch (action) {
        case "reply":
          onReplyClick?.(message);
          break;
        case "forward":
          onForwardClick?.(message);
          break;
        case "archive":
          // Implement archive functionality
          toast.success("Tin nhắn đã được lưu trữ");
          break;
        case "delete":
          if (window.confirm("Bạn có chắc chắn muốn xóa tin nhắn này?")) {
            await messageService.deleteMessage(message.id);
            toast.success("Tin nhắn đã được xóa");
            loadMessages();
          }
          break;
        case "star":
          // Implement star functionality
          toast.success("Tin nhắn đã được đánh dấu");
          break;
      }
    } catch (err) {
      console.error("Error performing action:", err);
      toast.error("Không thể thực hiện hành động này");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "sent":
        return <Send className="h-4 w-4 text-blue-600" />;
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "read":
        return <CheckCircle className="h-4 w-4 text-emerald-600" />;
      case "draft":
        return <FileText className="h-4 w-4 text-gray-600" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Mail className="h-4 w-4" />;
    }
  };

  const getTypeDisplayName = (messageType: string) => {
    return messageService.getTypeDisplayName(messageType);
  };

  const getStatusDisplayName = (status: string) => {
    return messageService.getStatusDisplayName(status);
  };

  const getStatusColor = (status: string) => {
    return messageService.getStatusColor(status);
  };

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        {/* Filters Skeleton */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <div className="h-10 bg-muted rounded-xl animate-pulse" />
          </div>
          <div className="h-10 w-[150px] bg-muted rounded-xl animate-pulse" />
          <div className="h-10 w-[150px] bg-muted rounded-xl animate-pulse" />
          <div className="h-10 w-[120px] bg-muted rounded-xl animate-pulse" />
        </div>

        {/* Messages Skeleton */}
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 bg-muted rounded-full" />
                    <div className="min-w-0 flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4" />
                      <div className="h-3 bg-muted rounded w-1/2" />
                    </div>
                  </div>
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center gap-4">
            <AlertCircle className="w-12 h-12 text-destructive" />
            <div>
              <h3 className="text-lg font-semibold">Lỗi tải dữ liệu</h3>
              <p className="text-muted-foreground">{error}</p>
            </div>
            <Button onClick={handleRefresh} className="gap-2">
              <RefreshCw className="w-4 h-4" />
              Thử lại
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Tìm kiếm tin nhắn..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rounded-xl"
          />
        </div>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-[150px] rounded-xl">
            <SelectValue placeholder="Tất cả loại" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả loại</SelectItem>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="sms">SMS</SelectItem>
            <SelectItem value="note">Ghi chú</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-[150px] rounded-xl">
            <SelectValue placeholder="Tất cả trạng thái" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả trạng thái</SelectItem>
            <SelectItem value="draft">Bản nháp</SelectItem>
            <SelectItem value="queued">Đang chờ</SelectItem>
            <SelectItem value="sent">Đã gửi</SelectItem>
            <SelectItem value="delivered">Đã nhận</SelectItem>
            <SelectItem value="read">Đã đọc</SelectItem>
            <SelectItem value="failed">Gửi lỗi</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterCategory} onValueChange={setFilterCategory}>
          <SelectTrigger className="w-[150px] rounded-xl">
            <SelectValue placeholder="Tất cả danh mục" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả danh mục</SelectItem>
            <SelectItem value="interview">Phỏng vấn</SelectItem>
            <SelectItem value="offer">Đ�� nghị</SelectItem>
            <SelectItem value="feedback">Phản hồi</SelectItem>
            <SelectItem value="reminder">Nhắc nhở</SelectItem>
            <SelectItem value="rejection">Từ chối</SelectItem>
            <SelectItem value="general">Tổng quát</SelectItem>
          </SelectContent>
        </Select>
        <Button
          variant="outline"
          className="gap-2 rounded-xl"
          onClick={handleRefresh}
        >
          <RefreshCw className="w-4 h-4" />
          Làm mới
        </Button>
      </div>

      {/* Message Count */}
      {totalMessages > 0 && (
        <div className="text-sm text-muted-foreground">
          Hiển thị {messages.length} trong tổng số {totalMessages} tin nhắn
        </div>
      )}

      {/* Messages */}
      <div className="space-y-4">
        {messages.map((message) => (
          <Card
            key={message.id}
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => onMessageSelect?.(message)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>
                      {message.candidate?.name
                        ?.split(" ")
                        .map((n) => n[0])
                        .join("") || "?"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <CardTitle className="text-sm truncate">
                        {message.subject || "Không có tiêu đề"}
                      </CardTitle>
                      <Badge className={getStatusColor(message.status)}>
                        {getStatusDisplayName(message.status)}
                      </Badge>
                      {message.priority > 7 && (
                        <Badge variant="destructive" className="text-xs">
                          Cao
                        </Badge>
                      )}
                    </div>
                    <CardDescription className="text-xs">
                      {message.candidate ? (
                        <>
                          {type === "sent" ? "Tới" : "Từ"}:{" "}
                          {message.candidate.name}
                          {message.candidate.email &&
                            ` (${message.candidate.email})`}
                        </>
                      ) : (
                        <>
                          {message.to_name ||
                            message.from_name ||
                            "Không rõ người gửi/nhận"}
                        </>
                      )}{" "}
                      •{" "}
                      {safeFormatDistanceToNow(message.created_at, {
                        addSuffix: true,
                      })}
                    </CardDescription>
                  </div>
                </div>
                {showActions && (
                  <div className="flex items-center justify-end gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMessageAction("reply", message);
                      }}
                    >
                      <Reply className="mr-2 h-4 w-4" />
                      Trả lời
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-destructive"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMessageAction("delete", message);
                      }}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Xóa
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                {message.content}
              </p>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(message.status)}
                  <Badge variant="outline" className="text-xs">
                    {getTypeDisplayName(message.type)}
                  </Badge>
                  {message.template && (
                    <Badge variant="secondary" className="text-xs">
                      Template: {message.template.name}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {message.replies_count > 0 && (
                    <Badge variant="outline" className="text-xs">
                      {message.replies_count} trả lời
                    </Badge>
                  )}
                  {message.is_scheduled && (
                    <Badge variant="outline" className="text-xs">
                      Đã lên lịch
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {messages.length === 0 && (
          <Card className="p-8 text-center">
            <div className="flex flex-col items-center gap-4">
              <Mail className="w-12 h-12 text-muted-foreground" />
              <div>
                <h3 className="text-lg font-semibold">
                  Không tìm thấy tin nhắn
                </h3>
                <p className="text-muted-foreground">
                  {searchTerm
                    ? "Thử điều chỉnh từ khóa tìm kiếm."
                    : "Chưa có tin nhắn nào."}
                </p>
              </div>
              {onComposeClick && (
                <Button className="gap-2" onClick={onComposeClick}>
                  <Plus className="w-4 h-4" />
                  Soạn tin nhắn
                </Button>
              )}
            </div>
          </Card>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage <= 1}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            Trước
          </Button>
          <span className="text-sm text-muted-foreground">
            Trang {currentPage} / {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage >= totalPages}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Sau
          </Button>
        </div>
      )}
    </div>
  );
}
