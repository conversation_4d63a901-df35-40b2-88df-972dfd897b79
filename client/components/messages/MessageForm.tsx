import { useState, useEffect, useRef, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";


import {
  Send,
  Save,
  X,
  Paperclip,
  Image,
  File,
  Loader2,
  Eye,
  AtSign,
  ExternalLink,
  AlertCircle,
} from "lucide-react";
import {
  Message,
  messageService,
  CreateMessageData,
} from "@/lib/services/messageService";
import {
  MessageTemplate,
  messageTemplateService,
} from "@/lib/services/messageTemplateService";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useTranslation } from "@/lib/i18n";
import { useCandidates } from "@/hooks/useApi";
import { apiService } from "@/lib/api";
import { CandidateSelector, CandidateOption } from "./CandidateSelector";

interface MessageFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
}

interface MessageFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSend?: (message: CreateMessageData) => void;
  mode?: "compose" | "reply" | "forward" | "edit";
  replyToMessage?: Message;
  forwardMessage?: Message;
  editMessage?: Message;
  selectedTemplate?: MessageTemplate;
  selectedCandidates?: Candidate[];
  className?: string;
}

export default function MessageForm({
  isOpen,
  onClose,
  onSend,
  mode = "compose",
  replyToMessage,
  forwardMessage,
  editMessage,
  selectedTemplate,
}: MessageFormProps) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<MessageTemplate[]>([]);
  const [selectedCandidate, setSelectedCandidate] =
    useState<CandidateOption | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Form state
  const [formData, setFormData] = useState({
    type: "email" as "email" | "sms" | "note",
    category: "general",
    to_email: "",
    to_name: "",
    subject: "",
    content: "",
    priority: 5,
    scheduled_at: "",
    template_id: "",
    attachments: [] as MessageFile[],
  });

  const [templateData, setTemplateData] = useState<Record<string, any>>({});
  const [previewMode, setPreviewMode] = useState(false);
  const [previewContent, setPreviewContent] = useState({
    subject: "",
    content: "",
  });

  // Initialize form based on mode
  useEffect(() => {
    if (mode === "reply" && replyToMessage) {
      setFormData((prev) => ({
        ...prev,
        type: replyToMessage.type,
        category: replyToMessage.category,
        to_email:
          replyToMessage.from_email || replyToMessage.candidate?.email || "",
        to_name:
          replyToMessage.from_name || replyToMessage.candidate?.name || "",
        subject: replyToMessage.subject?.startsWith("Re:")
          ? replyToMessage.subject
          : `Re: ${replyToMessage.subject || "Không có tiêu đề"}`,
        priority: Math.min((replyToMessage.priority || 5) + 1, 10),
      }));
    } else if (mode === "forward" && forwardMessage) {
      setFormData((prev) => ({
        ...prev,
        type: forwardMessage.type,
        category: forwardMessage.category,
        subject: forwardMessage.subject?.startsWith("Fwd:")
          ? forwardMessage.subject
          : `Fwd: ${forwardMessage.subject || "Không có tiêu đề"}`,
        content: `\n\n--- Forwarded Message ---\nFrom: ${forwardMessage.from_name || "Unknown"}\nTo: ${forwardMessage.to_name || "Unknown"}\nSubject: ${forwardMessage.subject || "No Subject"}\nDate: ${new Date(forwardMessage.created_at).toLocaleString()}\n\n${forwardMessage.content}`,
      }));
    } else if (mode === "edit" && editMessage) {
      setFormData((prev) => ({
        ...prev,
        type: editMessage.type,
        category: editMessage.category,
        to_email: editMessage.to_email || "",
        to_name: editMessage.to_name || "",
        subject: editMessage.subject || "",
        content: editMessage.content || "",
        priority: editMessage.priority || 5,
        scheduled_at: editMessage.scheduled_at || "",
        template_id: editMessage.template?.id?.toString() || "",
      }));

      // Set selected candidate if available
      if (editMessage.candidate) {
        const candidate: CandidateOption = {
          id: editMessage.candidate.id.toString(),
          name: editMessage.candidate.name,
          email: editMessage.candidate.email,
          phone: editMessage.candidate.phone,
          initials: editMessage.candidate.name
            .split(" ")
            .map((n) => n[0])
            .join(""),
          position: editMessage.candidate.position,
          status: editMessage.candidate.status || "active",
        };
        setSelectedCandidate(candidate);
      }
    } else if (selectedTemplate) {
      setFormData((prev) => ({
        ...prev,
        type: selectedTemplate.type,
        category: selectedTemplate.category,
        subject: selectedTemplate.subject || "",
        content: selectedTemplate.content,
        template_id: selectedTemplate.id.toString(),
      }));
    }
  }, [mode, replyToMessage, forwardMessage, editMessage, selectedTemplate]);

  // Load templates
  useEffect(() => {
    if (isOpen) {
      loadTemplates();
    }
  }, [isOpen]);

  const loadTemplates = async () => {
    try {
      const response = await messageTemplateService.getTemplates({
        is_active: true,
        per_page: 50,
      });
      if (response.data) {
        setTemplates(response.data);
      }
    } catch (err) {
      console.error("Error loading templates:", err);
    }
  };

  const handleTemplateSelect = async (template: MessageTemplate) => {
    setFormData((prev) => ({
      ...prev,
      type: template.type,
      category: template.category,
      subject: template.subject || "",
      content: template.content,
      template_id: template.id.toString(),
    }));

    // Extract default template data
    const variables = messageTemplateService.extractVariables(template.content);
    const defaultData: Record<string, any> = {};

    variables.forEach((variable) => {
      switch (variable) {
        case "candidate_name":
          defaultData[variable] = selectedCandidate?.name || "[Tên ứng viên]";
          break;
        case "candidate_email":
          defaultData[variable] =
            selectedCandidate?.email || "[Email ứng viên]";
          break;
        case "candidate_phone":
          defaultData[variable] = selectedCandidate?.phone || "[Số điện thoại]";
          break;
        case "job_title":
        case "position":
          defaultData[variable] =
            selectedCandidate?.position || "[Vị trí ứng tuyển]";
          break;
        case "candidate_location":
          defaultData[variable] = selectedCandidate?.location || "[Vị trí]";
          break;
        case "company_name":
          defaultData[variable] =
            "Công ty Cổ phần Giải pháp công nghệ Trực tuyến GTEL";
          break;
        case "recruiter_name":
          defaultData[variable] = "Recruiter";
          break;
        case "interview_date":
          defaultData[variable] = "[Ngày phỏng vấn]";
          break;
        case "interview_time":
          defaultData[variable] = "[Giờ phỏng vấn]";
          break;
        case "interview_type":
          defaultData[variable] = "[Loại phỏng vấn]";
          break;
        case "interviewer_name":
          defaultData[variable] = "[Người phỏng vấn]";
          break;

        default:
          defaultData[variable] = `{{${variable}}}`;
      }
    });

    setTemplateData(defaultData);
    toast.success(`Template "${template.name}" đã được áp dụng`);
  };

  const handlePreview = async () => {
    if (!formData.template_id) {
      setPreviewContent({
        subject: formData.subject,
        content: formData.content,
      });
      setPreviewMode(true);
      return;
    }

    try {
      const response = await messageTemplateService.previewTemplate(
        parseInt(formData.template_id),
        templateData,
      );

      if (response.status === "success") {
        setPreviewContent(response.data.preview);
        setPreviewMode(true);
      }
    } catch (err) {
      console.error("Error generating preview:", err);
      toast.error("Không thể tạo preview");
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles: MessageFile[] = Array.from(files).map((file) => ({
        id: `file-${Date.now()}-${Math.random()}`,
        name: file.name,
        size: file.size,
        type: file.type,
        url: URL.createObjectURL(file),
      }));
      setFormData((prev) => ({
        ...prev,
        attachments: [...prev.attachments, ...newFiles],
      }));
    }
  };

  const removeAttachment = (fileId: string) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((f) => f.id !== fileId),
    }));
  };

  const generateGmailUrl = () => {
    const gmailBaseUrl = "https://mail.google.com/mail/u/0/?fs=1";
    const params = new URLSearchParams();

    if (formData.to_email || selectedCandidate?.email) {
      params.append("to", formData.to_email || selectedCandidate?.email || "");
    }
    if (formData.subject) {
      params.append("su", formData.subject);
    }
    if (formData.content) {
      params.append("body", formData.content);
    }
    params.append("tf", "cm");

    return `${gmailBaseUrl}&${params.toString()}`;
  };

  const handleGmailQuickSend = () => {
    if (!formData.to_email && !selectedCandidate?.email) {
      toast.error("Vui lòng chọn người nhận trước khi gửi qua Gmail");
      return;
    }

    const gmailUrl = generateGmailUrl();
    window.open(gmailUrl, "_blank");
    toast.success("Mở Gmail để gửi tin nhắn");
  };

  const validateForm = () => {
    // Validate email format
    if (!formData.to_email.trim()) {
      toast.error("Vui lòng nhập địa chỉ email người nhận");
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.to_email)) {
      toast.error("Địa chỉ email không hợp lệ");
      return false;
    }

    // Check content
    if (!formData.content.trim()) {
      toast.error("Vui lòng nhập nội dung tin nhắn");
      return false;
    }

    // Check subject for email type
    if (formData.type === "email" && !formData.subject.trim()) {
      toast.error("Vui lòng nhập tiêu đề cho email");
      return false;
    }

    return true;
  };

  const handleSend = async () => {
    // Validation
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      const messageData: CreateMessageData = {
        type: formData.type,
        category: formData.category,
        candidate_id: selectedCandidate
          ? parseInt(selectedCandidate.id)
          : undefined,
        to_email: formData.to_email || selectedCandidate?.email,
        to_name: formData.to_name || selectedCandidate?.name,
        subject: formData.subject,
        content: formData.content,
        priority: formData.priority,
        scheduled_at: formData.scheduled_at || undefined,
        template_data:
          Object.keys(templateData).length > 0 ? templateData : undefined,
      };

      if (formData.template_id) {
        messageData.template_id = parseInt(formData.template_id);
      }

      if (mode === "edit" && editMessage) {
        // Update existing draft message
        const response = await messageService.updateMessage(editMessage.id, {
          ...messageData,
          status: formData.scheduled_at ? "queued" : "sent",
        });
        if (response.status === "success") {
          toast.success("Tin nhắn đã ��ược cập nhật thành công!");
          handleClose();
        }
      } else {
        if (onSend) {
          onSend(messageData);
        } else {
          const response = await messageService.sendMessage(messageData);
          if (response.status === "success") {
            toast.success("Tin nhắn đã được gửi thành công!");
            handleClose();
          }
        }
      }
    } catch (err) {
      console.error("Error sending message:", err);
      toast.error("Không thể gửi tin nhắn. Vui lòng thử lại.");
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDraft = async () => {
    try {
      setLoading(true);

      const draftData: CreateMessageData = {
        type: formData.type,
        category: formData.category,
        candidate_id: selectedCandidate
          ? parseInt(selectedCandidate.id)
          : undefined,
        to_email: formData.to_email || selectedCandidate?.email,
        to_name: formData.to_name || selectedCandidate?.name,
        subject: formData.subject,
        content: formData.content,
        priority: formData.priority,
        status: "draft",
        template_data:
          Object.keys(templateData).length > 0 ? templateData : undefined,
      };

      if (formData.template_id) {
        draftData.template_id = parseInt(formData.template_id);
      }

      if (mode === "edit" && editMessage) {
        // Update existing draft
        const response = await messageService.updateMessage(
          editMessage.id,
          draftData,
        );
        if (response.status === "success") {
          toast.success("Bản nháp đã được cập nhật!");
        }
      } else {
        // Create new draft
        const response = await messageService.sendMessage(draftData);
        if (response.status === "success") {
          toast.success("Bản nháp đã được lưu!");
        }
      }
    } catch (err) {
      console.error("Error saving draft:", err);
      toast.error("Không thể lưu bản nháp. Vui lòng thử lại.");
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      type: "email",
      category: "general",
      to_email: "",
      to_name: "",
      subject: "",
      content: "",
      priority: 5,
      scheduled_at: "",
      template_id: "",
      attachments: [],
    });
    setTemplateData({});
    setSelectedCandidate(null);
    setPreviewMode(false);
    onClose();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="w-5 h-5" />
            {mode === "reply"
              ? "Trả lời tin nhắn"
              : mode === "forward"
                ? "Chuyển tiếp tin nhắn"
                : mode === "edit"
                  ? "Chỉnh sửa tin nhắn"
                  : "Soạn tin nhắn mới"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
            {/* Message Form */}
            <div className="space-y-4">
              {/* Recipients, Email, and Type */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pointer-events-auto">
                <CandidateSelector
                  label="Người nhận"
                  placeholder="Chọn người nhận..."
                  value={selectedCandidate?.id}
                  onValueChange={(candidate) => {
                    setSelectedCandidate(candidate);
                    setFormData((prev) => ({
                      ...prev,
                      to_email: candidate?.email || "",
                      to_name: candidate?.name || "",
                    }));
                  }}
                  disabled={loading}
                  variant="combobox"
                  showSearch={true}
                  includeActiveOnly={false}
                  includeJobInfo={true}
                  className="space-y-2"
                />

                <div className="space-y-2">
                  <Label>Email *</Label>
                  <Input
                    type="email"
                    placeholder="Nhập địa chỉ email..."
                    value={formData.to_email}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        to_email: e.target.value,
                      }))
                    }
                    className="rounded-xl"
                  />
                </div>
              </div>

              {/* Message Type, Category, and Template */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">

                <div className="space-y-2">
                  <Label>Loại tin nhắn</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value: "email" | "sms" | "note") =>
                      setFormData((prev) => ({ ...prev, type: value }))
                    }
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="sms">SMS</SelectItem>
                      <SelectItem value="note">Ghi chú</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Danh mục</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, category: value }))
                    }
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">Tổng quát</SelectItem>
                      <SelectItem value="interview">Phỏng vấn</SelectItem>
                      <SelectItem value="offer">Đề nghị</SelectItem>
                      <SelectItem value="feedback">Phản hồi</SelectItem>
                      <SelectItem value="reminder">Nhắc nhở</SelectItem>
                      <SelectItem value="rejection">Từ chối</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Template</Label>
                  <Select
                    value={formData.template_id}
                    onValueChange={(value) => {
                      if (value && value !== "none") {
                        const template = templates.find(t => t.id.toString() === value);
                        if (template) {
                          handleTemplateSelect(template);
                        }
                      } else {
                        setFormData((prev) => ({ ...prev, template_id: "" }));
                        setTemplateData({});
                      }
                    }}
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Chọn template..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Không sử dụng template</SelectItem>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id.toString()}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Subject */}
              <div className="space-y-2">
                <Label>Tiêu đề *</Label>
                <Input
                  placeholder="Nhập tiêu đề..."
                  value={formData.subject}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      subject: e.target.value,
                    }))
                  }
                  className="rounded-xl"
                />
              </div>

              {/* Content */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Nội dung *</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handlePreview}
                    className="gap-2"
                  >
                    <Eye className="w-4 h-4" />
                    Preview
                  </Button>
                </div>
                <Textarea
                  placeholder="Nhập nội dung tin nhắn..."
                  value={formData.content}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      content: e.target.value,
                    }))
                  }
                  rows={12}
                  className="rounded-xl resize-none"
                />
              </div>

              {/* Template Variables */}
              {formData.template_id && Object.keys(templateData).length > 0 && (
                <div className="space-y-2">
                  <Label>Dữ liệu template</Label>
                  <div className="grid grid-cols-2 gap-4 p-4 border rounded-xl bg-muted/50">
                    {Object.entries(templateData).map(([key, value]) => (
                      <div key={key} className="space-y-1">
                        <Label className="text-xs">{key}</Label>
                        <Input
                          value={value}
                          onChange={(e) =>
                            setTemplateData((prev) => ({
                              ...prev,
                              [key]: e.target.value,
                            }))
                          }
                          className="text-sm"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Attachments */}
              {formData.type === "email" && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Tệp đính kèm</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="gap-2 rounded-xl"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Paperclip className="w-4 h-4" />
                      Thêm tệp
                    </Button>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    className="hidden"
                    onChange={handleFileUpload}
                  />
                  {formData.attachments.length > 0 && (
                    <div className="space-y-2">
                      {formData.attachments.map((file) => (
                        <div
                          key={file.id}
                          className="flex items-center justify-between p-2 border rounded-lg"
                        >
                          <div className="flex items-center gap-2">
                            {file.type.startsWith("image/") ? (
                              <Image className="w-4 h-4 text-blue-500" />
                            ) : (
                              <File className="w-4 h-4 text-gray-500" />
                            )}
                            <span className="text-sm font-medium">
                              {file.name}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              ({formatFileSize(file.size)})
                            </span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeAttachment(file.id)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Advanced Options */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Độ ưu tiên</Label>
                  <Select
                    value={formData.priority.toString()}
                    onValueChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        priority: parseInt(value),
                      }))
                    }
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Rất thấp (1)</SelectItem>
                      <SelectItem value="3">Thấp (3)</SelectItem>
                      <SelectItem value="5">Trung bình (5)</SelectItem>
                      <SelectItem value="7">Cao (7)</SelectItem>
                      <SelectItem value="10">Rất cao (10)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Lên lịch gửi</Label>
                  <Input
                    type="datetime-local"
                    value={formData.scheduled_at}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        scheduled_at: e.target.value,
                      }))
                    }
                    className="rounded-xl"
                  />
                </div>
              </div>
            </div>
        </div>

        {/* Preview Modal */}
        {previewMode && (
          <Dialog open={previewMode} onOpenChange={setPreviewMode}>
            <DialogContent className="max-w-4xl">
              <DialogHeader>
                <DialogTitle>Preview tin nhắn</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Tiêu đề:</Label>
                  <p className="mt-1 p-2 bg-muted rounded">
                    {previewContent.subject}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Nội dung:</Label>
                  <div className="mt-1 p-4 bg-muted rounded">
                    <pre className="whitespace-pre-wrap font-sans text-sm">
                      {previewContent.content}
                    </pre>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Actions */}
        <div className="flex justify-between pt-4">
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              className="gap-2 rounded-xl"
              onClick={handleSaveDraft}
              disabled={loading}
            >
              <Save className="w-4 h-4" />
              {mode === "edit" ? "Cập nhật nháp" : "Lưu nháp"}
            </Button>
            {formData.type === "email" && (
              <Button
                type="button"
                variant="outline"
                className="gap-2 rounded-xl text-blue-600 border-blue-200 hover:bg-blue-50"
                onClick={handleGmailQuickSend}
                disabled={loading}
              >
                <AtSign className="w-4 h-4" />
                <ExternalLink className="w-3 h-3" />
                Gửi qua Gmail
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
              className="rounded-xl"
            >
              Hủy
            </Button>
            <Button
              type="button"
              className="ai-button gap-2"
              onClick={handleSend}
              disabled={loading || !formData.content.trim()}
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
              {mode === "edit"
                ? formData.scheduled_at
                  ? "Lên lịch gửi"
                  : "Gửi tin nhắn"
                : formData.scheduled_at
                  ? "Lên lịch gửi"
                  : "Gửi tin nhắn"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
