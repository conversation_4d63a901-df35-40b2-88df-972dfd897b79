import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { CandidateSelector } from "./CandidateSelector";

// Mock the useTranslation hook
vi.mock("@/lib/i18n", () => ({
  useTranslation: () => ({
    t: {
      common: {
        search: "Search",
        loading: "Loading",
        error: "Error",
      },
    },
  }),
}));

// Mock the useCandidates hook
vi.mock("@/hooks/useApi", () => ({
  useCandidates: () => ({
    data: {
      data: [
        {
          id: "1",
          name: "Test Candidate",
          email: "<EMAIL>",
          initials: "TC",
          status: "active",
          position: "Developer",
        },
      ],
    },
    isLoading: false,
    error: null,
  }),
}));

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
};

const renderWithQueryClient = (component: React.ReactElement) => {
  const testQueryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={testQueryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe("CandidateSelector", () => {
  it("renders with default props", () => {
    const mockOnValueChange = vi.fn();
    
    renderWithQueryClient(
      <CandidateSelector onValueChange={mockOnValueChange} />
    );

    expect(screen.getByText("Candidate")).toBeInTheDocument();
    expect(screen.getByText("Select candidate...")).toBeInTheDocument();
  });

  it("renders with custom label and placeholder", () => {
    const mockOnValueChange = vi.fn();
    
    renderWithQueryClient(
      <CandidateSelector
        label="Choose Recipient"
        placeholder="Pick a candidate..."
        onValueChange={mockOnValueChange}
      />
    );

    expect(screen.getByText("Choose Recipient")).toBeInTheDocument();
    expect(screen.getByText("Pick a candidate...")).toBeInTheDocument();
  });

  it("renders error state when error prop is provided", () => {
    const mockOnValueChange = vi.fn();
    
    renderWithQueryClient(
      <CandidateSelector
        error="This field is required"
        onValueChange={mockOnValueChange}
      />
    );

    expect(screen.getByText("This field is required")).toBeInTheDocument();
  });

  it("can be disabled", () => {
    const mockOnValueChange = vi.fn();
    
    renderWithQueryClient(
      <CandidateSelector
        disabled={true}
        onValueChange={mockOnValueChange}
      />
    );

    const button = screen.getByRole("combobox");
    expect(button).toBeDisabled();
  });

  it("supports select variant", () => {
    const mockOnValueChange = vi.fn();
    
    renderWithQueryClient(
      <CandidateSelector
        variant="select"
        onValueChange={mockOnValueChange}
      />
    );

    // Should render a select trigger instead of combobox
    expect(screen.getByRole("combobox")).toBeInTheDocument();
  });
});
