import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import {
  User,
  Mail,
  Lock,
  Phone,
  Building,
  Briefcase,
  UserCheck,
  Loader2,
  Save,
  X,
} from "lucide-react";
import { useAuth } from "@/lib/auth";
import {
  User as UserType,
  CreateUserData,
  UpdateUserData,
  ROLE_DISPLAY_NAMES,
  USER_ROLES,
  DEPARTMENTS,
} from "@/lib/types/userManagement";
import { useUserRoles } from "@/hooks/useUserManagement";
import { toast } from "sonner";

interface UserFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateUserData | UpdateUserData) => void;
  user?: UserType | null;
  mode: "create" | "edit";
  isLoading?: boolean;
  currentUser?: UserType | null;
}

export default function UserForm({
  isOpen,
  onClose,
  onSubmit,
  user,
  mode,
  isLoading = false,
  currentUser,
}: UserFormProps) {
  const { user: authUser } = useAuth();
  const { data: rolesData } = useUserRoles();
  const [formData, setFormData] = useState<CreateUserData | UpdateUserData>({
    name: "",
    email: "",
    password: "",
    password_confirmation: "",
    role: "recruiter",
    department: "",
    title: "",
    phone: "",
    is_active: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Check if current user is admin
  const isAdmin = authUser?.role === "admin" || currentUser?.role === "admin";

  // Check if editing own profile
  const isEditingSelf = mode === "edit" && user?.id === authUser?.id;

  useEffect(() => {
    if (mode === "edit" && user) {
      setFormData({
        name: user.name || "",
        email: user.email || "",
        password: "",
        password_confirmation: "",
        role: user.role || "recruiter",
        department: user.department || "",
        title: user.title || "",
        phone: user.phone || "",
        is_active: user.is_active ?? true,
      });
    } else if (mode === "create") {
      setFormData({
        name: "",
        email: "",
        password: "",
        password_confirmation: "",
        role: "recruiter",
        department: "",
        title: "",
        phone: "",
        is_active: true,
      });
    }
    setErrors({});
  }, [mode, user, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = "Tên là bắt buộc";
    }

    if (!formData.email?.trim()) {
      newErrors.email = "Email là bắt buộc";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Email không hợp lệ";
    }

    if (mode === "create" || formData.password) {
      if (!formData.password) {
        newErrors.password = "Mật khẩu là bắt buộc";
      } else if (formData.password.length < 8) {
        newErrors.password = "Mật khẩu phải có ít nhất 8 ký tự";
      }

      if (formData.password !== formData.password_confirmation) {
        newErrors.password_confirmation = "Xác nhận mật khẩu không khớp";
      }
    }

    if (!formData.role) {
      newErrors.role = "Vai trò là bắt buộc";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Remove empty password fields for updates
    const submitData = { ...formData };
    if (mode === "edit" && !submitData.password) {
      delete submitData.password;
      delete submitData.password_confirmation;
    }

    onSubmit(submitData);
  };

  const handleClose = () => {
    setFormData({
      name: "",
      email: "",
      password: "",
      password_confirmation: "",
      role: "recruiter",
      department: "",
      title: "",
      phone: "",
      is_active: true,
    });
    setErrors({});
    onClose();
  };

  // Get available roles from API or fallback to constants
  const availableRoles =
    rolesData?.data ||
    Object.entries(ROLE_DISPLAY_NAMES).map(([key, value]) => ({
      name: key,
      display_name: value,
    }));

  // Fields that non-admin users can edit about themselves
  const canEditField = (field: string): boolean => {
    if (isAdmin) return true;
    if (!isEditingSelf) return false;

    // Self-edit allowed fields
    return ["name", "phone", "password", "password_confirmation"].includes(
      field,
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            {mode === "create"
              ? "Thêm người dùng mới"
              : "Chỉnh sửa thông tin người dùng"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Thông tin cơ bản</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Họ và tên *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  placeholder="Nhập họ và tên"
                  disabled={!canEditField("name")}
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  placeholder="<EMAIL>"
                  disabled={!canEditField("email")}
                  className={errors.email ? "border-red-500" : ""}
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="password">
                  Mật khẩu{" "}
                  {mode === "create" ? "*" : "(để trống nếu không thay đổi)"}
                </Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                  placeholder="Nhập mật khẩu"
                  disabled={!canEditField("password")}
                  className={errors.password ? "border-red-500" : ""}
                />
                {errors.password && (
                  <p className="text-sm text-red-500">{errors.password}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password_confirmation">Xác nhận mật khẩu</Label>
                <Input
                  id="password_confirmation"
                  type="password"
                  value={formData.password_confirmation}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      password_confirmation: e.target.value,
                    })
                  }
                  placeholder="Xác nhận mật khẩu"
                  disabled={!canEditField("password_confirmation")}
                  className={
                    errors.password_confirmation ? "border-red-500" : ""
                  }
                />
                {errors.password_confirmation && (
                  <p className="text-sm text-red-500">
                    {errors.password_confirmation}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Số điện thoại</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) =>
                  setFormData({ ...formData, phone: e.target.value })
                }
                placeholder="0123456789"
                disabled={!canEditField("phone")}
              />
            </div>
          </div>

          {/* Role and Department */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Vai trò và phòng ban</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="role">Vai trò *</Label>
                <Select
                  value={formData.role}
                  onValueChange={(value) =>
                    setFormData({ ...formData, role: value })
                  }
                  disabled={!canEditField("role")}
                >
                  <SelectTrigger
                    className={errors.role ? "border-red-500" : ""}
                  >
                    <SelectValue placeholder="Chọn vai trò" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRoles.map((role) => (
                      <SelectItem key={role.name} value={role.name}>
                        {role.display_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.role && (
                  <p className="text-sm text-red-500">{errors.role}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="department">Phòng ban</Label>
                <Select
                  value={formData.department}
                  onValueChange={(value) =>
                    setFormData({ ...formData, department: value })
                  }
                  disabled={!canEditField("department")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn phòng ban" />
                  </SelectTrigger>
                  <SelectContent>
                    {DEPARTMENTS.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Chức vụ</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) =>
                  setFormData({ ...formData, title: e.target.value })
                }
                placeholder="Ví dụ: Senior Recruiter"
                disabled={!canEditField("title")}
              />
            </div>

            {isAdmin && (
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) =>
                    setFormData({ ...formData, is_active: checked })
                  }
                />
                <Label htmlFor="is_active">Tài khoản hoạt động</Label>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              <X className="w-4 h-4 mr-2" />
              Hủy
            </Button>
            <Button type="submit" disabled={isLoading} className="ai-button">
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {mode === "create" ? "Tạo người dùng" : "Cập nhật"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
