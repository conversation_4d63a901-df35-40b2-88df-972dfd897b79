import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  User,
  Mail,
  Phone,
  Building,
  Calendar,
  Clock,
  Shield,
  Users,
  Target,
  Activity,
  Edit,
  UserX,
  UserCheck,
  MoreVertical,
  Award,
  TrendingUp,
  Crown,
} from "lucide-react";
import { User as UserType, ROLE_DISPLAY_NAMES } from "@/lib/types/userManagement";
import { useAuth } from "@/lib/auth";
import { cn, safeFormatDistanceToNow } from "@/lib/utils";

interface UserDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserType | null;
  onEdit?: (user: UserType) => void;
  onToggleStatus?: (user: UserType) => void;
  onDeactivate?: (user: UserType) => void;
}

export default function UserDetailModal({
  isOpen,
  onClose,
  user,
  onEdit,
  onToggleStatus,
  onDeactivate,
}: UserDetailModalProps) {
  const { user: authUser } = useAuth();

  if (!user) return null;

  // Check permissions
  const isAdmin = authUser?.role === "admin";
  const canEdit = isAdmin || user.id === authUser?.id;
  const canDeactivate = isAdmin && user.id !== authUser?.id;

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "admin":
        return Crown;
      case "hiring_manager":
        return Shield;
      case "recruiter":
        return Users;
      case "interviewer":
        return UserCheck;
      default:
        return User;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "admin":
        return "text-amber-600 bg-amber-100 border-amber-200";
      case "hiring_manager":
        return "text-blue-600 bg-blue-100 border-blue-200";
      case "recruiter":
        return "text-green-600 bg-green-100 border-green-200";
      case "interviewer":
        return "text-purple-600 bg-purple-100 border-purple-200";
      default:
        return "text-gray-600 bg-gray-100 border-gray-200";
    }
  };

  const RoleIcon = getRoleIcon(user.role);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Chi tiết người dùng
            </DialogTitle>
            {(canEdit || canDeactivate) && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {canEdit && (
                    <DropdownMenuItem onClick={() => onEdit?.(user)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Chỉnh sửa
                    </DropdownMenuItem>
                  )}
                  {canDeactivate && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => onToggleStatus?.(user)}
                        className={user.is_active ? "text-orange-600" : "text-green-600"}
                      >
                        {user.is_active ? (
                          <>
                            <UserX className="mr-2 h-4 w-4" />
                            Vô hiệu hóa
                          </>
                        ) : (
                          <>
                            <UserCheck className="mr-2 h-4 w-4" />
                            Kích hoạt
                          </>
                        )}
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* User Profile Header */}
          <div className="flex items-start gap-4">
            <Avatar className="w-20 h-20">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="bg-primary/10 text-primary font-semibold text-xl">
                {user.initials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h2 className="text-2xl font-bold">{user.name}</h2>
                <Badge
                  className={cn("text-sm", getRoleColor(user.role))}
                  variant="outline"
                >
                  <RoleIcon className="w-4 h-4 mr-1" />
                  {user.role_display_name || ROLE_DISPLAY_NAMES[user.role as keyof typeof ROLE_DISPLAY_NAMES]}
                </Badge>
                <Badge
                  variant={user.is_active ? "default" : "secondary"}
                  className="text-sm"
                >
                  {user.is_active ? "Hoạt động" : "Không hoạt động"}
                </Badge>
              </div>
              <p className="text-lg text-muted-foreground mb-3">{user.title}</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-muted-foreground" />
                  <span>{user.email}</span>
                </div>
                {user.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-muted-foreground" />
                    <span>{user.phone}</span>
                  </div>
                )}
                {user.department && (
                  <div className="flex items-center gap-2">
                    <Building className="w-4 h-4 text-muted-foreground" />
                    <span>{user.department}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <span>Tham gia: {new Date(user.created_at).toLocaleDateString("vi-VN")}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Account Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Thông tin tài khoản</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-1">ID người dùng</h4>
                <p className="text-sm">{user.id}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-1">Trạng thái phỏng vấn viên</h4>
                <p className="text-sm">
                  {user.is_interviewer ? "Có" : "Không"}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-1">Đăng nhập cuối</h4>
                <p className="text-sm">
                  {user.last_login_at 
                    ? safeFormatDistanceToNow(user.last_login_at, { addSuffix: true })
                    : "Chưa đăng nhập"
                  }
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-1">Ngày cập nhật</h4>
                <p className="text-sm">
                  {safeFormatDistanceToNow(user.updated_at, { addSuffix: true })}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          {user.statistics && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Thống kê hoạt động</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-blue-600">
                      {user.statistics.created_candidates_count}
                    </p>
                    <p className="text-xs text-muted-foreground">Ứng viên tạo</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <Target className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-green-600">
                      {user.statistics.assigned_candidates_count}
                    </p>
                    <p className="text-xs text-muted-foreground">Ứng viên được phân</p>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <Activity className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-purple-600">
                      {user.statistics.created_interviews_count}
                    </p>
                    <p className="text-xs text-muted-foreground">Cuộc phỏng vấn</p>
                  </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <Award className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-orange-600">
                      {user.statistics.created_job_postings_count}
                    </p>
                    <p className="text-xs text-muted-foreground">Tin tuyển dụng tạo</p>
                  </div>
                  <div className="text-center p-3 bg-emerald-50 rounded-lg">
                    <TrendingUp className="w-8 h-8 text-emerald-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-emerald-600">
                      {user.statistics.managed_job_postings_count}
                    </p>
                    <p className="text-xs text-muted-foreground">Tin quản lý</p>
                  </div>
                  <div className="text-center p-3 bg-pink-50 rounded-lg">
                    <Shield className="w-8 h-8 text-pink-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-pink-600">
                      {user.statistics.recruited_job_postings_count}
                    </p>
                    <p className="text-xs text-muted-foreground">Tin tuyển dụng</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Permissions */}
          {user.permissions && user.permissions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quyền hệ thống</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {user.permissions.map((permission) => (
                    <Badge key={permission} variant="secondary" className="text-xs">
                      {permission}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              Đóng
            </Button>
            {canEdit && (
              <Button onClick={() => onEdit?.(user)} className="ai-button">
                <Edit className="w-4 h-4 mr-2" />
                Chỉnh sửa
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
