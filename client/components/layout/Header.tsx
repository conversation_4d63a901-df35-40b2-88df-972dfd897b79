import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import { useTranslation } from "@/lib/i18n";
import { useTheme } from "@/components/theme-provider";
import { useAuth } from "@/lib/auth";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Search,
  Bell,
  Sparkles,
  Settings,
  User,
  Users,
  LogOut,
  Moon,
  Sun,
  Monitor,
  Calendar,
  MessageSquare,
} from "lucide-react";
import { toast } from "sonner";

export const Header = () => {
  const { language, setLanguage } = useTranslation();
  const { theme, setTheme } = useTheme();
  const {
    user: authUser,
    logout: authLogout,
    isLoading: authLoading,
  } = useAuth();
  const { profile: currentUserData, isLoading: userDataLoading } =
    useCurrentUser();
  const [isNotificationModalOpen, setIsNotificationModalOpen] = useState(false);

  // Combine auth user and current user data, with preference for currentUserData
  const user = currentUserData || authUser;
  const isLoadingUser = authLoading || userDataLoading;

  // Generate initials from user name
  const getInitials = (name?: string) => {
    if (!name) return "U";
    return name
      .trim()
      .split(" ")
      .filter((n) => n.length > 0)
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Get avatar URL with fallback
  const getAvatarUrl = (user: any) => {
    return user?.avatar || user?.avatar_url || undefined;
  };

  // Get user name (handle both User and UserProfile types)
  const getUserName = (user: any) => {
    return user?.fullName || user?.name || "User";
  };

  // Get user plan (fallback to 'free' if not specified)
  const getUserPlan = () => {
    // In a real app, plan might be determined by user role or a separate API call
    if (user?.role === "admin") return "enterprise";
    if (user?.role === "manager") return "pro";
    return "free"; // Default plan since plan property doesn't exist
  };

  // Get user role display name
  const getRoleDisplayName = (role?: string) => {
    if (!role) return "";
    return role.charAt(0).toUpperCase() + role.slice(1);
  };

  // Mock notifications count (in real app, this would come from API)
  const notificationsCount = 3;

  // Show minimal header if user data failed to load but we have auth token
  const hasAuthToken = !!localStorage.getItem("auth_token");

  if (!isLoadingUser && !user && !hasAuthToken) {
    return null;
  }

  const handleLogout = () => {
    authLogout();
    toast.success(
      language === "vi" ? "Đăng xuất thành công" : "Logged out successfully",
    );
  };

  const handleThemeChange = (newTheme: "light" | "dark" | "system") => {
    console.log(user);
    setTheme(newTheme);
    const themeNames = {
      light: language === "vi" ? "sáng" : "light",
      dark: language === "vi" ? "tối" : "dark",
      system: language === "vi" ? "hệ thống" : "system",
    };
    toast.success(
      language === "vi"
        ? `Đã chuyển sang chế độ ${themeNames[newTheme]}`
        : `Theme changed to ${themeNames[newTheme]}`,
    );
  };

  const notifications = [
    {
      id: "1",
      title: "New interview scheduled",
      description: "Sarah Chen - Frontend Developer position",
      time: "5 minutes ago",
      type: "interview",
      unread: true,
    },
    {
      id: "2",
      title: "Application received",
      description: "New candidate applied for Backend Engineer",
      time: "1 hour ago",
      type: "application",
      unread: true,
    },
    {
      id: "3",
      title: "Interview feedback submitted",
      description: "Feedback received for Michael Rodriguez",
      time: "2 hours ago",
      type: "feedback",
      unread: false,
    },
  ];

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "interview":
        return <Calendar className="w-4 h-4 text-blue-500" />;
      case "application":
        return <User className="w-4 h-4 text-green-500" />;
      case "feedback":
        return <MessageSquare className="w-4 h-4 text-purple-500" />;
      default:
        return <Bell className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <header className="h-16 bg-background/95 backdrop-blur-sm border-b border-border flex items-center justify-between px-6 relative">
      {/* AI Glow Effect */}
      <div className="absolute top-0 left-0 right-0 h-full bg-gradient-to-r from-transparent via-primary/5 to-transparent pointer-events-none" />

      {/* Search */}
      <div className="flex-1 max-w-lg relative z-10">
        <div className="relative group hidden">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4 group-focus-within:text-primary transition-colors" />
          <Input
            placeholder={
              language === "vi"
                ? "Tìm kiếm với AI... (Thử 'React developers ở TP.HCM')"
                : "Search with AI... (Try 'React developers in SF')"
            }
            className="pl-10 bg-muted/50 border-border/50 focus:border-primary/50 focus:bg-background transition-all duration-200 rounded-xl"
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="flex items-center gap-1 px-2 py-1 bg-primary/10 rounded-md">
              <Sparkles className="w-3 h-3 text-primary" />
              <span className="text-xs font-medium text-primary">AI</span>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-4 relative z-10">
        {/* Notifications */}
        <div className="relative">
          <Button
            variant="outline"
            size="sm"
            className="rounded-xl border-border/50 hover:border-primary/50 transition-colors"
            onClick={() => setIsNotificationModalOpen(true)}
          >
            <Bell className="w-4 h-4" />
          </Button>
          {notificationsCount > 0 && (
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
              <span className="text-xs font-bold text-primary-foreground">
                {notificationsCount}
              </span>
            </div>
          )}
        </div>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="relative h-8 w-8 rounded-full hover:bg-muted transition-colors"
              disabled={isLoadingUser}
            >
              <Avatar className="h-8 w-8 border-2 border-primary/20">
                <AvatarImage src={getAvatarUrl(user)} alt="@user" />
                <AvatarFallback className="bg-primary/10 text-primary font-medium">
                  {isLoadingUser ? "⟳" : getInitials(getUserName(user))}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-72 rounded-xl border-border/50"
            align="end"
            forceMount
          >
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-2">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={getAvatarUrl(user)} alt="@user" />
                    <AvatarFallback className="bg-primary/10 text-primary font-medium">
                      {isLoadingUser ? "..." : getInitials(getUserName(user))}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm font-medium leading-none">
                      {isLoadingUser ? "Loading..." : getUserName(user)}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground mt-1">
                      {isLoadingUser ? "..." : user?.email || "No email"}
                    </p>
                    {!isLoadingUser && (user?.role || user?.department) && (
                      <p className="text-xs leading-none text-muted-foreground mt-1">
                        {getRoleDisplayName(user?.role)}
                        {user?.role && user?.department && " • "}
                        {user?.department}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />

            <DropdownMenuSeparator />

            {/* Navigation */}
            <DropdownMenuItem className="rounded-lg" asChild>
              <Link to="/profile" className="cursor-pointer">
                <User className="mr-2 h-4 w-4" />
                Hồ sơ
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem className="rounded-lg" asChild>
              <Link to="/team" className="cursor-pointer">
                <Users className="mr-2 h-4 w-4" />
                Nhóm
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem className="rounded-lg" asChild>
              <Link to="/settings" className="cursor-pointer">
                <Settings className="mr-2 h-4 w-4" />
                Cài đặt
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            {/* Language Selector */}
            <div className="px-2 py-1">
              <p className="text-xs font-medium text-muted-foreground mb-2 px-2">
                Language
              </p>
              <div className="grid grid-cols-2 gap-1">
                <Button
                  variant={language === "vi" ? "default" : "ghost"}
                  size="sm"
                  className="rounded-lg h-8 text-xs"
                  onClick={() => setLanguage("vi")}
                >
                  🇻🇳 Tiếng Việt
                </Button>
                <Button
                  variant={language === "en" ? "default" : "ghost"}
                  size="sm"
                  className="rounded-lg h-8 text-xs"
                  onClick={() => setLanguage("en")}
                >
                  🇺🇸 English
                </Button>
              </div>
            </div>

            <DropdownMenuSeparator />

            {/* Theme Selector */}
            <div className="px-2 py-1">
              <p className="text-xs font-medium text-muted-foreground mb-2 px-2">
                {language === "vi" ? "Giao diện" : "Theme"}
              </p>
              <div className="grid grid-cols-3 gap-1">
                <Button
                  variant={theme === "light" ? "default" : "ghost"}
                  size="sm"
                  className="rounded-lg h-8 flex-col gap-0 p-1"
                  onClick={() => handleThemeChange("light")}
                  title={language === "vi" ? "Chế độ sáng" : "Light mode"}
                >
                  <Sun className="w-3 h-3" />
                  <span className="text-[10px]">
                    {language === "vi" ? "Sáng" : "Light"}
                  </span>
                </Button>
                <Button
                  variant={theme === "dark" ? "default" : "ghost"}
                  size="sm"
                  className="rounded-lg h-8 flex-col gap-0 p-1"
                  onClick={() => handleThemeChange("dark")}
                  title={language === "vi" ? "Chế độ tối" : "Dark mode"}
                >
                  <Moon className="w-3 h-3" />
                  <span className="text-[10px]">
                    {language === "vi" ? "Tối" : "Dark"}
                  </span>
                </Button>
                <Button
                  variant={theme === "system" ? "default" : "ghost"}
                  size="sm"
                  className="rounded-lg h-8 flex-col gap-0 p-1"
                  onClick={() => handleThemeChange("system")}
                  title={language === "vi" ? "Theo hệ thống" : "System theme"}
                >
                  <Monitor className="w-3 h-3" />
                  <span className="text-[10px]">
                    {language === "vi" ? "Auto" : "Auto"}
                  </span>
                </Button>
              </div>
            </div>

            <DropdownMenuSeparator />
            <DropdownMenuSeparator />

            <DropdownMenuItem
              className="rounded-lg text-destructive focus:text-destructive"
              onClick={handleLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Notifications Modal */}
      <Dialog
        open={isNotificationModalOpen}
        onOpenChange={setIsNotificationModalOpen}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Notifications
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-3 rounded-lg border ${
                  notification.unread
                    ? "bg-primary/5 border-primary/20"
                    : "bg-muted/50 border-border"
                }`}
              >
                <div className="flex items-start gap-3">
                  <div className="p-1 rounded">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm font-medium">
                        {notification.title}
                      </p>
                      {notification.unread && (
                        <div className="w-2 h-2 bg-primary rounded-full" />
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground mb-1">
                      {notification.description}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {notification.time}
                    </p>
                  </div>
                </div>
              </div>
            ))}
            <div className="flex justify-center pt-2">
              <Button variant="outline" size="sm" className="rounded-xl">
                View All Notifications
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </header>
  );
};
