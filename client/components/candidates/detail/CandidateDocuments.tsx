import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  FileText,
  Download,
  ExternalLink,
  Eye,
  Upload,
  Calendar,
} from "lucide-react";
import { PDFViewer } from "../PDFViewer";

export interface Document {
  id: string;
  name: string;
  type: "resume" | "cover_letter" | "certificate" | "portfolio" | "other";
  url?: string;
  uploadDate: string;
  size?: string;
}

export interface CandidateDocumentsProps {
  candidate: {
    resumeUrl?: string;
    appliedDate?: string;
  };
  documents?: Document[];
  onDownload?: (documentId: string) => void;
  onView?: (documentId: string) => void;
  onUpload?: () => void;
}

export const CandidateDocuments: React.FC<CandidateDocumentsProps> = ({
  candidate,
  documents = [],
  onDownload,
  onView,
  onUpload,
}) => {
  const getDocumentIcon = (type: Document["type"]) => {
    switch (type) {
      case "resume":
        return <FileText className="w-5 h-5 text-blue-600" />;
      case "cover_letter":
        return <FileText className="w-5 h-5 text-green-600" />;
      case "certificate":
        return <FileText className="w-5 h-5 text-purple-600" />;
      case "portfolio":
        return <FileText className="w-5 h-5 text-orange-600" />;
      default:
        return <FileText className="w-5 h-5 text-gray-600" />;
    }
  };

  const formatFileSize = (bytes?: string | number) => {
    if (!bytes) return "";
    const size = typeof bytes === "string" ? parseInt(bytes) : bytes;
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Tài liệu & CV
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Resume PDF Viewer */}
        {candidate.resumeUrl && (
          <div>
            <h4 className="font-medium mb-3">CV</h4>
            <PDFViewer
              fileName="CV.pdf"
              fileUrl={candidate.resumeUrl}
              uploadDate={candidate.appliedDate || new Date().toISOString()}
              onDownload={() => {
                if (candidate.resumeUrl) {
                  window.open(candidate.resumeUrl, "_blank");
                }
              }}
            />
          </div>
        )}

        {/* Additional Documents */}
        {documents.length > 0 && (
          <div>
            <h4 className="font-medium mb-3">Tài liệu bổ sung</h4>
            <div className="space-y-3">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    {getDocumentIcon(doc.type)}
                    <div>
                      <p className="font-medium">{doc.name}</p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Calendar className="w-3 h-3" />
                        <span>
                          Đã tải lên{" "}
                          {new Date(doc.uploadDate).toLocaleDateString()}
                        </span>
                        {doc.size && <span>• {formatFileSize(doc.size)}</span>}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {onView && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onView(doc.id)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    )}
                    {onDownload && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDownload(doc.id)}
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Tải xuống
                      </Button>
                    )}
                    {doc.url && (
                      <Button variant="default" size="sm" asChild>
                        <a
                          href={doc.url}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Xem
                        </a>
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty state */}
        {!candidate.resumeUrl && documents.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">Chưa có tài liệu nào được tải lên</p>
            <p className="text-xs">
              Tài liệu sẽ xuất hiện ở đây khi được tải lên
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
