import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { GraduationCap, Briefcase } from "lucide-react";

export interface CandidateProfessionalInfoProps {
  candidate: {
    experience?: string;
    education?: string;
    workHistory?: string;
    source?: string;
    salaryExpectation?: {
      min?: string;
      max?: string;
      currency?: string;
      range?: string;
    };
  };
}

export const CandidateProfessionalInfo: React.FC<
  CandidateProfessionalInfoProps
> = ({ candidate }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GraduationCap className="w-5 h-5" />
          Thông tin nghề nghiệp
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Experience Level */}
        {candidate.experience && (
          <div>
            <h4 className="font-medium mb-3"><PERSON><PERSON><PERSON> độ kinh nghiệm</h4>
            <p className="text-sm text-muted-foreground">
              {candidate.experience} kinh nghiệm liên quan
            </p>
          </div>
        )}

        {/* Education */}
        {candidate.education && (
          <div>
            <h4 className="font-medium mb-3">Học vấn</h4>
            <p className="text-sm text-muted-foreground whitespace-pre-line">
              {candidate.education}
            </p>
          </div>
        )}

        {/* Work History */}
        {candidate.workHistory && (
          <div>
            <h4 className="font-medium mb-3">Lịch sử công việc</h4>
            <p className="text-sm text-muted-foreground whitespace-pre-line">
              {candidate.workHistory}
            </p>
          </div>
        )}

        {/* Salary Expectation */}
        {candidate.salaryExpectation && (
          <div>
            <h4 className="font-medium mb-3">Mong muốn lương</h4>
            <p className="text-sm text-muted-foreground">
              {candidate.salaryExpectation.range ||
                `${candidate.salaryExpectation.min?.toLocaleString()} - ${candidate.salaryExpectation.max?.toLocaleString()} ${candidate.salaryExpectation.currency || "VND"}`}
            </p>
          </div>
        )}

        {/* Source */}
        {candidate.source && (
          <div>
            <h4 className="font-medium mb-3">Nguồn ứng tuyển</h4>
            <p className="text-sm text-muted-foreground">{candidate.source}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
