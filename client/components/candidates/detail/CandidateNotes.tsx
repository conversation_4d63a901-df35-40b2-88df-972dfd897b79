import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MessageSquare, Star, Trash2, Edit, Plus } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { safeFormatDistanceToNow } from "@/lib/utils";

export interface Note {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    initials: string;
  };
  createdAt: string;
  updatedAt?: string;
  type?: "note" | "feedback" | "interview";
  rating?: number;
}

export interface CandidateNotesProps {
  notes?: Note[];
  onAddNote?: (content: string) => void;
  onEditNote?: (noteId: string, content: string) => void;
  onDeleteNote?: (noteId: string) => void;
  disabled?: boolean;
}

export const CandidateNotes: React.FC<CandidateNotesProps> = ({
  notes = [],
  onAddNote,
  onEditNote,
  onDeleteNote,
  disabled = false,
}) => {
  const [newNote, setNewNote] = useState("");
  const [editingNote, setEditingNote] = useState<string | null>(null);
  const [editContent, setEditContent] = useState("");

  const handleSaveNote = () => {
    if (newNote.trim() && onAddNote) {
      onAddNote(newNote.trim());
      setNewNote("");
    }
  };

  const handleEditNote = (note: Note) => {
    setEditingNote(note.id);
    setEditContent(note.content);
  };

  const handleSaveEdit = (noteId: string) => {
    if (editContent.trim() && onEditNote) {
      onEditNote(noteId, editContent.trim());
      setEditingNote(null);
      setEditContent("");
    }
  };

  const handleCancelEdit = () => {
    setEditingNote(null);
    setEditContent("");
  };

  const getNoteTypeIcon = (type?: string) => {
    switch (type) {
      case "feedback":
        return <Star className="w-4 h-4 text-yellow-600" />;
      case "interview":
        return <MessageSquare className="w-4 h-4 text-blue-600" />;
      default:
        return <MessageSquare className="w-4 h-4 text-gray-600" />;
    }
  };

  const renderRating = (rating?: number) => {
    if (!rating) return null;

    return (
      <div className="flex items-center gap-1">
        {Array.from({ length: 5 }, (_, i) => (
          <Star
            key={i}
            className={`w-3 h-3 ${
              i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
            }`}
          />
        ))}
        <span className="text-xs text-muted-foreground ml-1">{rating}/5</span>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          Ghi chú & Phản hồi
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Add new note */}
        {!disabled && (
          <div className="space-y-3 p-4 border rounded-lg bg-muted/20">
            <label className="text-sm font-medium">Thêm ghi chú</label>
            <Textarea
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              placeholder="Thêm ghi chú của bạn về ứng viên này..."
              rows={3}
            />
            <Button
              size="sm"
              onClick={handleSaveNote}
              disabled={!newNote.trim()}
            >
              <Plus className="w-4 h-4 mr-2" />
              Lưu ghi chú
            </Button>
          </div>
        )}

        {/* Notes list */}
        {notes.length > 0 ? (
          <div className="space-y-3">
            <h4 className="font-medium">Ghi chú & Phản hồi trước đó</h4>
            {notes.map((note) => (
              <div
                key={note.id}
                className="p-4 border rounded-lg bg-card hover:bg-muted/20 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={note.author.avatar} />
                      <AvatarFallback className="text-xs">
                        {note.author.initials}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {note.author.name}
                      </span>
                      {getNoteTypeIcon(note.type)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">
                      {safeFormatDistanceToNow(note.createdAt, {
                        addSuffix: true,
                      })}
                    </span>
                    {!disabled && onEditNote && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditNote(note)}
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                    )}
                    {!disabled && onDeleteNote && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDeleteNote(note.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                </div>

                {editingNote === note.id ? (
                  <div className="space-y-2">
                    <Textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      rows={3}
                    />
                    <div className="flex gap-2">
                      <Button size="sm" onClick={() => handleSaveEdit(note.id)}>
                        Lưu
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancelEdit}
                      >
                        Hủy
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className="text-sm whitespace-pre-line mb-2">
                      {note.content}
                    </p>
                    {renderRating(note.rating)}
                    {note.updatedAt && note.updatedAt !== note.createdAt && (
                      <p className="text-xs text-muted-foreground">
                        Chỉnh sửa lần cuối{" "}
                        {safeFormatDistanceToNow(note.updatedAt, {
                          addSuffix: true,
                        })}
                      </p>
                    )}
                  </>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">Chưa có ghi chú nào</p>
            <p className="text-xs">
              Ghi chú và phản hồi sẽ xuất hiện ở đây khi được thêm vào
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
