import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Filter, X, RotateCcw } from "lucide-react";

interface FilterOptions {
  experience: string[];
  salaryRange: { min: number; max: number };
  skills: string[];
  sources: string[];
  locations: string[];
  appliedDateRange: { from: string; to: string };
  rating: { min: number; max: number };
}

interface AdvancedFiltersProps {
  onFiltersChange: (filters: Partial<FilterOptions>) => void;
  activeFiltersCount: number;
}

const allSkills = [
  "React",
  "TypeScript",
  "Next.js",
  "GraphQL",
  "TailwindCSS",
  "Product Strategy",
  "Agile",
  "Data Analysis",
  "User Research",
  "Figma",
  "Prototyping",
  "Design Systems",
  "Node.js",
  "Python",
  "PostgreSQL",
  "AWS",
  "Docker",
  "Machine Learning",
  "SQL",
  "TensorFlow",
  "R",
];

const allSources = [
  "LinkedIn",
  "Company Website",
  "Referral",
  "Indeed",
  "AngelList",
  "Glassdoor",
];
const allLocations = [
  "San Francisco, CA",
  "New York, NY",
  "Austin, TX",
  "Seattle, WA",
  "Boston, MA",
  "Remote",
];

export const AdvancedFilters = ({
  onFiltersChange,
  activeFiltersCount,
}: AdvancedFiltersProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [tempFilters, setTempFilters] = useState<Partial<FilterOptions>>({});

  const handleApplyFilters = () => {
    onFiltersChange(tempFilters);
    setIsOpen(false);
  };

  const handleClearFilters = () => {
    setTempFilters({});
    onFiltersChange({});
  };

  const handleSkillToggle = (skill: string) => {
    const currentSkills = tempFilters.skills || [];
    const newSkills = currentSkills.includes(skill)
      ? currentSkills.filter((s) => s !== skill)
      : [...currentSkills, skill];

    setTempFilters({ ...tempFilters, skills: newSkills });
  };

  const handleSourceToggle = (source: string) => {
    const currentSources = tempFilters.sources || [];
    const newSources = currentSources.includes(source)
      ? currentSources.filter((s) => s !== source)
      : [...currentSources, source];

    setTempFilters({ ...tempFilters, sources: newSources });
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Filter className="h-4 w-4" />
          Advanced Filters
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-1">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="start">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Advanced Filters</CardTitle>
                <CardDescription>
                  Refine your candidate search with detailed criteria
                </CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Experience Level */}
            <div className="space-y-2">
              <Label>Experience Level</Label>
              <Select
                value={tempFilters.experience?.[0] || ""}
                onValueChange={(value) =>
                  setTempFilters({
                    ...tempFilters,
                    experience: value ? [value] : [],
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select experience level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="entry">Entry Level (0-2 years)</SelectItem>
                  <SelectItem value="mid">Mid Level (3-5 years)</SelectItem>
                  <SelectItem value="senior">
                    Senior Level (5+ years)
                  </SelectItem>
                  <SelectItem value="lead">
                    Lead/Principal (8+ years)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Salary Range */}
            <div className="space-y-2">
              <Label>Salary Range (USD)</Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Input
                    type="number"
                    placeholder="Min"
                    value={tempFilters.salaryRange?.min || ""}
                    onChange={(e) =>
                      setTempFilters({
                        ...tempFilters,
                        salaryRange: {
                          ...tempFilters.salaryRange,
                          min: parseInt(e.target.value) || 0,
                          max: tempFilters.salaryRange?.max || 0,
                        },
                      })
                    }
                  />
                </div>
                <div>
                  <Input
                    type="number"
                    placeholder="Max"
                    value={tempFilters.salaryRange?.max || ""}
                    onChange={(e) =>
                      setTempFilters({
                        ...tempFilters,
                        salaryRange: {
                          min: tempFilters.salaryRange?.min || 0,
                          max: parseInt(e.target.value) || 0,
                        },
                      })
                    }
                  />
                </div>
              </div>
            </div>

            {/* Rating */}
            <div className="space-y-2">
              <Label>Minimum Rating</Label>
              <Select
                value={tempFilters.rating?.min?.toString() || ""}
                onValueChange={(value) =>
                  setTempFilters({
                    ...tempFilters,
                    rating: { min: parseInt(value), max: 5 },
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select minimum rating" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 Star & Above</SelectItem>
                  <SelectItem value="2">2 Stars & Above</SelectItem>
                  <SelectItem value="3">3 Stars & Above</SelectItem>
                  <SelectItem value="4">4 Stars & Above</SelectItem>
                  <SelectItem value="5">5 Stars Only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Skills */}
            <div className="space-y-2">
              <Label>Required Skills</Label>
              <div className="max-h-32 overflow-y-auto space-y-2">
                {allSkills.map((skill) => (
                  <div key={skill} className="flex items-center space-x-2">
                    <Checkbox
                      id={skill}
                      checked={tempFilters.skills?.includes(skill) || false}
                      onCheckedChange={() => handleSkillToggle(skill)}
                    />
                    <Label
                      htmlFor={skill}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {skill}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Sources */}
            <div className="space-y-2">
              <Label>Application Source</Label>
              <div className="space-y-2">
                {allSources.map((source) => (
                  <div key={source} className="flex items-center space-x-2">
                    <Checkbox
                      id={source}
                      checked={tempFilters.sources?.includes(source) || false}
                      onCheckedChange={() => handleSourceToggle(source)}
                    />
                    <Label
                      htmlFor={source}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {source}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Date Range */}
            <div className="space-y-2">
              <Label>Application Date Range</Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Input
                    type="date"
                    value={tempFilters.appliedDateRange?.from || ""}
                    onChange={(e) =>
                      setTempFilters({
                        ...tempFilters,
                        appliedDateRange: {
                          ...tempFilters.appliedDateRange,
                          from: e.target.value,
                          to: tempFilters.appliedDateRange?.to || "",
                        },
                      })
                    }
                  />
                </div>
                <div>
                  <Input
                    type="date"
                    value={tempFilters.appliedDateRange?.to || ""}
                    onChange={(e) =>
                      setTempFilters({
                        ...tempFilters,
                        appliedDateRange: {
                          from: tempFilters.appliedDateRange?.from || "",
                          to: e.target.value,
                        },
                      })
                    }
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearFilters}
                className="flex-1 gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Clear All
              </Button>
              <Button size="sm" onClick={handleApplyFilters} className="flex-1">
                Apply Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
};
