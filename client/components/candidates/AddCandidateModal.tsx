import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  GraduationCap,
  Link,
  Upload,
  X,
  Plus,
  Sparkles,
  Loader2,
  FileText,
} from "lucide-react";
import { Candidate } from "@/data/mockData";
import { toast } from "sonner";
import { ValidationError, ApiError } from "@/lib/api";
import { useJobs } from "@/hooks/useApi";
import { jobAdapters } from "@/lib/adapters";
import { ResumeExtractionModal } from "./ResumeExtractionModal";
import { ResumeExtractionService } from "@/lib/services/resumeExtractionService";
import {
  ExtractedInformation,
  ExtractedFieldSelection,
} from "@/lib/types/resumeExtraction";

interface AddCandidateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (candidate: Omit<Candidate, "id">) => Promise<void> | void;
}

interface CandidateFormData {
  name: string;
  email: string;
  phone: string;
  location: string;
  position: string;
  experience: string;
  skills: string[];
  source: string;
  jobId: string;
  salaryExpectationMin: number;
  salaryExpectationMax: number;
  salaryCurrency: string;
  linkedinUrl: string;
  githubUrl: string;
  portfolioUrl: string; // v2.0.1: added portfolio URL
  notes: string;
  // v2.0.1: TEXT fields for simplified structure
  education: string;
  workHistory: string;
  tags: string[]; // v2.0.1: simplified tags array
  resumeFile?: File;
}

const skillSuggestions = [
  "React",
  "TypeScript",
  "JavaScript",
  "Node.js",
  "Python",
  "Java",
  "AWS",
  "Docker",
  "Kubernetes",
  "GraphQL",
  "SQL",
  "MongoDB",
  "UI/UX Design",
  "Product Management",
  "Agile",
  "Scrum",
  "Data Analysis",
  "Machine Learning",
  "DevOps",
  "Leadership",
];

const sourceSuggestions = [
  "LinkedIn",
  "Company Website",
  "Referral",
  "Indeed",
  "AngelList",
  "Glassdoor",
  "GitHub",
  "Stack Overflow",
  "Conference",
  "Cold Outreach",
];

// v2.0.1: Tag suggestions for candidate categorization
const tagSuggestions = [
  "high-potential",
  "team-player",
  "technical-expert",
  "leadership",
  "senior-level",
  "junior-level",
  "remote-ready",
  "local-only",
  "flexible",
  "immediate-start",
  "notice-period",
  "visa-required",
  "bilingual",
  "startup-experience",
  "enterprise-experience",
];

export const AddCandidateModal = ({
  isOpen,
  onClose,
  onAdd,
}: AddCandidateModalProps) => {
  const [currentTab, setCurrentTab] = useState("basic");
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [customSkill, setCustomSkill] = useState("");
    const [selectedTags, setSelectedTags] = useState<string[]>([]); // v2.0.1: tags state
  const [customTag, setCustomTag] = useState("");
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [isAiMode, setIsAiMode] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractedData, setExtractedData] = useState<ExtractedInformation | null>(null);
  const [showExtractionModal, setShowExtractionModal] = useState(false);
  const [resumeUrl, setResumeUrl] = useState<string>("");

  // Fetch active jobs from API
  const { data: jobsData } = useJobs({
    status: "active",
    per_page: 100, // Get enough jobs for selection
  });

  // Transform API data to get active jobs
  const activeJobs = jobsData
    ? jobAdapters.fromPaginatedApi(jobsData).jobs
    : [];

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<CandidateFormData>({
    defaultValues: {
      source: "LinkedIn",
      experience: "3-5 years",
      salaryCurrency: "VND", // v2.0.1: default currency
    },
  });

    const handleClose = () => {
    reset();
    setSelectedSkills([]);
    setSelectedTags([]); // v2.0.1: reset tags
    setResumeFile(null);
    setIsAiMode(false);
    setIsExtracting(false);
    setExtractedData(null);
    setShowExtractionModal(false);
    setResumeUrl("");
    onClose();
  };

  const handleSkillAdd = (skill: string) => {
    if (skill && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
    }
    setCustomSkill("");
  };

  const handleSkillRemove = (skill: string) => {
    setSelectedSkills(selectedSkills.filter((s) => s !== skill));
  };

  // v2.0.1: Tag handlers
  const handleTagAdd = (tag: string) => {
    if (tag && !selectedTags.includes(tag)) {
      setSelectedTags([...selectedTags, tag]);
    }
    setCustomTag("");
  };

  const handleTagRemove = (tag: string) => {
    setSelectedTags(selectedTags.filter((t) => t !== tag));
  };

    const handleResumeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setResumeFile(file);
      // Create a temporary URL for the uploaded file
      const fileUrl = URL.createObjectURL(file);
      setResumeUrl(fileUrl);

      // Simulate AI parsing
      if (isAiMode) {
        toast.info("AI is parsing the resume...");
        setTimeout(() => {
          // Mock AI-parsed data
          setValue("name", "Sarah Johnson");
          setValue("email", "<EMAIL>");
          setValue("phone", "+****************");
          setValue("location", "San Francisco, CA");
          setValue("position", "Senior React Developer");
          setValue("experience", "5+ years");
          setSelectedSkills(["React", "TypeScript", "Node.js", "AWS"]);
          toast.success("Resume parsed successfully with AI!");
        }, 2000);
      }
    }
  };

  const handleExtractFromResume = async () => {
    if (!resumeUrl && !resumeFile) {
      toast.error("Please upload a resume first");
      return;
    }

    setIsExtracting(true);
    try {
            // Note: API extraction requires a candidate ID, so this uses demo data
      // Real extraction will be available after saving the candidate
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API delay

      // Mock extracted data for demonstration
      const mockExtractedData: ExtractedInformation = {
        name: "Sarah Johnson",
        email: "<EMAIL>",
        phone: "+****************",
        address: "San Francisco, CA",
        skills: ["React", "TypeScript", "Node.js", "AWS", "JavaScript", "Python"],
        experience: [
          {
            company: "Tech Corp",
            position: "Senior Frontend Developer",
            duration: "2021-2024",
            description: "Led development of user-facing features using React and TypeScript"
          },
          {
            company: "StartupXYZ",
            position: "Frontend Developer",
            duration: "2019-2021",
            description: "Built responsive web applications and implemented modern UI/UX designs"
          }
        ],
        education: [
          {
            institution: "Stanford University",
            degree: "Bachelor of Science in Computer Science",
            graduation_year: "2019"
          }
        ]
      };

      setExtractedData(mockExtractedData);
      setShowExtractionModal(true);
      toast.success("Resume information extracted successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to extract resume information");
    } finally {
      setIsExtracting(false);
    }
  };

  const handleApplyExtractedFields = (selectedFields: ExtractedFieldSelection) => {
    if (!extractedData) return;

    // Apply selected fields to the form
    if (selectedFields.name && extractedData.name) {
      setValue("name", extractedData.name);
    }
    if (selectedFields.email && extractedData.email) {
      setValue("email", extractedData.email);
    }
    if (selectedFields.phone && extractedData.phone) {
      setValue("phone", extractedData.phone);
    }
    if (selectedFields.address && extractedData.address) {
      setValue("location", extractedData.address);
    }
    if (selectedFields.skills && extractedData.skills) {
      const formattedSkills = ResumeExtractionService.formatSkills(extractedData.skills);
      setSelectedSkills(prev => {
        const combined = [...prev, ...formattedSkills];
        return Array.from(new Set(combined)); // Remove duplicates
      });
    }
    if (selectedFields.experience && extractedData.experience) {
      const formattedExperience = ResumeExtractionService.formatExperience(extractedData.experience);
      setValue("workHistory", formattedExperience);
    }
    if (selectedFields.education && extractedData.education) {
      const formattedEducation = ResumeExtractionService.formatEducation(extractedData.education);
      setValue("education", formattedEducation);
    }

    const appliedFields = Object.entries(selectedFields)
      .filter(([_, selected]) => selected)
      .map(([field, _]) => field);

    toast.success(`Applied ${appliedFields.length} fields from resume extraction`);
  };

  const onSubmit = async (data: CandidateFormData) => {
    try {
      const newCandidate: Omit<Candidate, "id"> = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        location: data.location,
        position: data.position,
        experience: data.experience,
        skills: selectedSkills, // Use selected skills from state
        source: data.source,
        jobId: data.jobId === "none" ? undefined : data.jobId || undefined, // v2.0.1: job assignment is optional
        salaryExpectationMin: data.salaryExpectationMin,
        salaryExpectationMax: data.salaryExpectationMax,
        salaryCurrency: data.salaryCurrency,
        linkedinUrl: data.linkedinUrl,
        githubUrl: data.githubUrl,
        portfolioUrl: data.portfolioUrl, // v2.0.1: added portfolio URL
        notes: data.notes,
        // v2.0.1: new simplified TEXT fields
        education: data.education,
        workHistory: data.workHistory,
        tags: selectedTags, // v2.0.1: simplified tags array
        initials: data.name
          .split(" ")
          .map((n) => n[0])
          .join("")
          .toUpperCase(),
        status: "sourced",
        appliedDate: new Date().toISOString().split("T")[0],
      };

      await onAdd(newCandidate);
      // Only close modal if no error occurred
      // The parent component will handle closing the modal on success
    } catch (error) {
      console.error("Form submission error:", error);

      // Show appropriate error message to user based on error type
      if (error instanceof ValidationError) {
        // Show validation errors with Vietnamese messages
        const formattedErrors = error.getFormattedErrors();
        toast.error(`Lỗi xác thực: ${error.message}`, {
          description: formattedErrors,
          duration: 5000,
        });
      } else if (error instanceof ApiError) {
        // Show API errors with status code
        toast.error(`Lỗi API (${error.statusCode}): ${error.message}`, {
          duration: 5000,
        });
      } else if (error instanceof Error) {
        // Handle generic errors
        const message = error.message || "Đã xảy ra lỗi không xác định";
        toast.error(`Lỗi: ${message}`, {
          duration: 5000,
        });
      } else {
        // Handle any other error types
        toast.error("Đã xảy ra lỗi không xác định. Vui lòng thử lại.", {
          duration: 5000,
        });
      }

      // Modal stays open so user can fix the issue and retry
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <User className="w-5 h-5 text-primary" />
            </div>
            Add New Candidate
          </DialogTitle>
          <DialogDescription>
            Create a new candidate profile manually or use AI to parse resume
            information automatically.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* AI Toggle */}
          <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-emerald-500/5">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Sparkles className="w-5 h-5 text-primary" />
                  <div>
                    <h4 className="font-semibold">AI-Powered Resume Parsing</h4>
                    <p className="text-sm text-muted-foreground">
                      Automatically extract candidate information from resume
                    </p>
                  </div>
                </div>
                <Checkbox
                  checked={isAiMode}
                  onCheckedChange={(checked) => setIsAiMode(checked === true)}
                  className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
              </div>
            </CardContent>
          </Card>

          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="professional">Professional</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="additional">Additional</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    {...register("name", { required: "Name is required" })}
                    placeholder="Enter full name"
                    className="rounded-xl"
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register("email", {
                      required: "Email is required",
                      pattern: {
                        value: /^\S+@\S+$/,
                        message: "Invalid email format",
                      },
                    })}
                    placeholder="Enter email address"
                    className="rounded-xl"
                  />
                  {errors.email && (
                    <p className="text-sm text-destructive">
                      {errors.email.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    {...register("phone", {
                      required: "Phone number is required",
                    })}
                    placeholder="+****************"
                    className="rounded-xl"
                  />
                  {errors.phone && (
                    <p className="text-sm text-destructive">
                      {errors.phone.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    {...register("location")}
                    placeholder="City, State/Country"
                    className="rounded-xl"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="professional" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="position">Position/Role *</Label>
                  <Input
                    id="position"
                    {...register("position", {
                      required: "Position is required",
                    })}
                    placeholder="e.g., Senior Frontend Developer"
                    className="rounded-xl"
                  />
                  {errors.position && (
                    <p className="text-sm text-destructive">
                      {errors.position.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="experience">Experience Level</Label>
                  <Select
                    value={watch("experience")}
                    onValueChange={(value) => setValue("experience", value)}
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Select experience level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0-1 years">
                        Entry Level (0-1 years)
                      </SelectItem>
                      <SelectItem value="2-3 years">
                        Junior (2-3 years)
                      </SelectItem>
                      <SelectItem value="3-5 years">
                        Mid-level (3-5 years)
                      </SelectItem>
                      <SelectItem value="5+ years">
                        Senior (5+ years)
                      </SelectItem>
                      <SelectItem value="8+ years">
                        Lead/Principal (8+ years)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* v2.0.1: Separate salary expectation fields */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="salaryExpectationMin">
                      Min Salary (VND)
                    </Label>
                    <Input
                      id="salaryExpectationMin"
                      type="number"
                      {...register("salaryExpectationMin", {
                        valueAsNumber: true,
                        min: { value: 0, message: "Salary must be positive" },
                      })}
                      placeholder="e.g., 18000000"
                      className="rounded-xl"
                    />
                    {errors.salaryExpectationMin && (
                      <span className="text-sm text-red-500">
                        {errors.salaryExpectationMin.message}
                      </span>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="salaryExpectationMax">
                      Max Salary (VND)
                    </Label>
                    <Input
                      id="salaryExpectationMax"
                      type="number"
                      {...register("salaryExpectationMax", {
                        valueAsNumber: true,
                        min: { value: 0, message: "Salary must be positive" },
                        validate: (value) => {
                          const minSalary = watch("salaryExpectationMin");
                          if (minSalary && value && value < minSalary) {
                            return "Max salary must be greater than min salary";
                          }
                          return true;
                        },
                      })}
                      placeholder="e.g., 28000000"
                      className="rounded-xl"
                    />
                    {errors.salaryExpectationMax && (
                      <span className="text-sm text-red-500">
                        {errors.salaryExpectationMax.message}
                      </span>
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="salaryCurrency">Currency</Label>
                  <Select
                    value={watch("salaryCurrency")}
                    onValueChange={(value) => setValue("salaryCurrency", value)}
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="VND">VND (Vietnamese Dong)</SelectItem>
                      <SelectItem value="USD">USD (US Dollar)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="source">Source</Label>
                  <Select
                    value={watch("source")}
                    onValueChange={(value) => setValue("source", value)}
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Select source" />
                    </SelectTrigger>
                    <SelectContent>
                      {sourceSuggestions.map((source) => (
                        <SelectItem key={source} value={source}>
                          {source}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobId">Applying for Position</Label>
                <Select
                  value={watch("jobId")}
                  onValueChange={(value) => setValue("jobId", value)}
                >
                  <SelectTrigger className="rounded-xl">
                    <SelectValue placeholder="Select job position" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No specific position</SelectItem>
                    {activeJobs.map((job) => (
                      <SelectItem key={job.id} value={job.id}>
                        {job.title} - {job.department}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                <Label>Skills</Label>
                <div className="flex flex-wrap gap-2 mb-3">
                  {selectedSkills.map((skill) => (
                    <Badge
                      key={skill}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {skill}
                      <button
                        type="button"
                        onClick={() => handleSkillRemove(skill)}
                        className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={customSkill}
                    onChange={(e) => setCustomSkill(e.target.value)}
                    placeholder="Add a skill"
                    className="rounded-xl"
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        handleSkillAdd(customSkill);
                      }
                    }}
                  />
                  <Button
                    type="button"
                    onClick={() => handleSkillAdd(customSkill)}
                    className="rounded-xl"
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {skillSuggestions
                    .filter((skill) => !selectedSkills.includes(skill))
                    .slice(0, 10)
                    .map((skill) => (
                      <Button
                        key={skill}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleSkillAdd(skill)}
                        className="text-xs rounded-lg"
                      >
                        {skill}
                      </Button>
                    ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
                            <Card className="border-dashed border-2 border-muted-foreground/25 hover:border-primary/50 transition-colors">
                <CardContent className="p-8">
                  <div className="text-center space-y-4">
                    <Upload className="w-12 h-12 text-muted-foreground mx-auto" />
                    <div>
                      <h3 className="font-semibold text-lg">Upload Resume</h3>
                      <p className="text-muted-foreground">
                        {isAiMode
                          ? "AI will automatically extract information from the resume"
                          : "Upload PDF, DOC, or DOCX files"}
                      </p>
                    </div>
                    <div className="space-y-3">
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx"
                        onChange={handleResumeUpload}
                        className="hidden"
                        id="resume-upload"
                      />
                      <label htmlFor="resume-upload">
                        <Button
                          type="button"
                          variant="outline"
                          className="cursor-pointer rounded-xl"
                          asChild
                        >
                          <span>
                            {isAiMode && <Sparkles className="w-4 h-4 mr-2" />}
                            Choose File
                          </span>
                        </Button>
                      </label>
                      {resumeFile && (
                        <p className="text-sm text-primary">
                          ✓ {resumeFile.name} uploaded
                        </p>
                      )}
                    </div>

                    {/* Extract from Resume Button */}
                    {resumeFile && (
                      <div className="pt-4 border-t border-dashed border-muted-foreground/25">
                        <Button
                          type="button"
                          onClick={handleExtractFromResume}
                          disabled={isExtracting}
                          className="ai-button"
                        >
                          {isExtracting ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Extracting...
                            </>
                          ) : (
                            <>
                              <FileText className="w-4 h-4 mr-2" />
                              Extract from Resume
                            </>
                          )}
                        </Button>
                                                <p className="text-xs text-muted-foreground mt-2">
                          Demo: AI will extract and suggest form fields from your resume<br/>
                          <span className="text-primary">For real extraction, save candidate first then edit</span>
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="linkedinUrl">LinkedIn Profile</Label>
                  <Input
                    id="linkedinUrl"
                    {...register("linkedinUrl")}
                    placeholder="https://linkedin.com/in/username"
                    className="rounded-xl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="githubUrl">GitHub Profile</Label>
                  <Input
                    id="githubUrl"
                    {...register("githubUrl")}
                    placeholder="https://github.com/username"
                    className="rounded-xl"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="additional" className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  {...register("notes")}
                  placeholder="Add any additional notes about this candidate..."
                  rows={4}
                  className="rounded-xl"
                />
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1 rounded-xl"
            >
              Cancel
            </Button>
            <Button type="submit" className="flex-1 ai-button">
              Add Candidate
            </Button>
          </div>
                </form>

        {/* Resume Extraction Modal */}
        <ResumeExtractionModal
          isOpen={showExtractionModal}
          onClose={() => setShowExtractionModal(false)}
          extractedData={extractedData}
          onApplyFields={handleApplyExtractedFields}
          isLoading={isExtracting}
        />
      </DialogContent>
    </Dialog>
  );
};
