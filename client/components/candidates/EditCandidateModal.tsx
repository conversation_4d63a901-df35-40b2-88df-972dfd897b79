import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  GraduationCap,
  Link,
  Upload,
  X,
  Plus,
  Sparkles,
  Edit,
  Loader2,
  FileText,
} from "lucide-react";
import { Candidate } from "@/data/mockData";
import { UiCandidate } from "@/lib/adapters/types";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";
import { useJobs } from "@/hooks/useApi";
import { jobAdapters } from "@/lib/adapters";
import { ResumeExtractionModal } from "./ResumeExtractionModal";
import { ResumeExtractionService } from "@/lib/services/resumeExtractionService";
import {
  ExtractedInformation,
  ExtractedFieldSelection,
} from "@/lib/types/resumeExtraction";

interface EditCandidateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (candidate: UiCandidate) => Promise<void>;
  candidate: UiCandidate | null;
}

interface CandidateFormData {
  name: string;
  email: string;
  phone: string;
  location: string;
  position: string;
  experience: string;
  skills: string[];
  source: string;
  jobId: string;
  salaryExpectationMin: number;
  salaryExpectationMax: number;
  salaryCurrency: string;
  linkedinUrl: string;
  githubUrl: string;
  portfolioUrl: string;
  notes: string;
  education: string;
  workHistory: string;
  tags: string[];
  resumeUrl?: string;
}

const skillSuggestions = [
  "React",
  "TypeScript",
  "JavaScript",
  "Node.js",
  "Python",
  "Java",
  "AWS",
  "Docker",
  "Kubernetes",
  "GraphQL",
  "SQL",
  "MongoDB",
  "UI/UX Design",
  "Product Management",
  "Agile",
  "Scrum",
  "Data Analysis",
  "Machine Learning",
  "DevOps",
  "Leadership",
];

const sourceSuggestions = [
  "LinkedIn",
  "Company Website",
  "Referral",
  "Indeed",
  "AngelList",
  "Glassdoor",
  "GitHub",
  "Stack Overflow",
  "Conference",
  "Cold Outreach",
];

// v2.0.1: Tag suggestions for candidate categorization
const tagSuggestions = [
  "high-potential",
  "team-player",
  "technical-expert",
  "leadership",
  "senior-level",
  "junior-level",
  "remote-ready",
  "local-only",
  "flexible",
  "immediate-start",
  "notice-period",
  "visa-required",
  "bilingual",
  "startup-experience",
  "enterprise-experience",
];

export const EditCandidateModal = ({
  isOpen,
  onClose,
  onUpdate,
  candidate,
}: EditCandidateModalProps) => {
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState("general");
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [customSkill, setCustomSkill] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]); // v2.0.1: tags state
  const [customTag, setCustomTag] = useState("");
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractedData, setExtractedData] =
    useState<ExtractedInformation | null>(null);
  const [showExtractionModal, setShowExtractionModal] = useState(false);

  // Fetch active jobs from API
  const { data: jobsData } = useJobs({
    status: "active",
    per_page: 100, // Get enough jobs for selection
  });

  // Transform API data to get active jobs
  const activeJobs = jobsData
    ? jobAdapters.fromPaginatedApi(jobsData).jobs
    : [];

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<CandidateFormData>({
    defaultValues: {
      source: "LinkedIn",
      experience: "3-5 years",
      salaryCurrency: "VND", // v2.0.1: default currency
    },
  });

  // Load candidate data when modal opens
  useEffect(() => {
    if (candidate && isOpen) {
      setValue("name", candidate.name || "");
      setValue("email", candidate.email || "");
      setValue("phone", candidate.phone || "");
      setValue("location", candidate.location || "");
      setValue("position", candidate.position || "");
      setValue("experience", candidate.experience || "3-5 years");
      setValue("source", candidate.source || "LinkedIn");
      setValue("jobId", candidate.jobId || "none");

      // v2.0.1: Load salary fields with fallback logic
      const minSalary = candidate.salaryExpectationMin || 0;
      const maxSalary = candidate.salaryExpectationMax || 0;
      const currency = candidate.salaryCurrency || "VND";

      setValue("salaryExpectationMin", minSalary);
      setValue("salaryExpectationMax", maxSalary);
      setValue("salaryCurrency", currency);
      setValue("linkedinUrl", candidate.linkedinUrl || "");
      setValue("resumeUrl", candidate.resumeUrl || "");
      setValue("githubUrl", candidate.githubUrl || "");
      setValue("portfolioUrl", candidate.portfolioUrl || ""); // v2.0.1: portfolio URL
      setValue("notes", candidate.notes || "");
      // v2.0.1: TEXT fields
      setValue("education", candidate.education || "");
      setValue("workHistory", candidate.workHistory || "");
      setSelectedSkills(candidate.skills || []);
      setSelectedTags(candidate.tags || []); // v2.0.1: tags
    }
  }, [candidate, isOpen, setValue]);

  const handleClose = () => {
    reset();
    setSelectedSkills([]);
    setSelectedTags([]); // v2.0.1: reset tags
    setResumeFile(null);
    setCurrentTab("general");
    setIsExtracting(false);
    setExtractedData(null);
    setShowExtractionModal(false);
    onClose();
  };

  const handleSkillAdd = (skill: string) => {
    if (skill && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
    }
    setCustomSkill("");
  };

  const handleSkillRemove = (skill: string) => {
    setSelectedSkills(selectedSkills.filter((s) => s !== skill));
  };

  // v2.0.1: Tag handlers
  const handleTagAdd = (tag: string) => {
    if (tag && !selectedTags.includes(tag)) {
      setSelectedTags([...selectedTags, tag]);
    }
    setCustomTag("");
  };

  const handleTagRemove = (tag: string) => {
    setSelectedTags(selectedTags.filter((t) => t !== tag));
  };

  const handleResumeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setResumeFile(file);
      toast.info(t.toast?.success?.uploaded || "Resume uploaded successfully");
    }
  };

  const handleExtractFromResume = async () => {
    if (!candidate) return;

    const resumeUrl = watch("resumeUrl");
    if (!resumeUrl && !resumeFile) {
      toast.error("Please provide a resume URL or upload a resume file first");
      return;
    }

    if (!ResumeExtractionService.canExtractResume({ resumeUrl })) {
      toast.error("No resume found to extract from");
      return;
    }

    setIsExtracting(true);
    try {
      // Call the actual API to extract resume information
      const candidateId =
        typeof candidate.id === "string"
          ? parseInt(candidate.id)
          : candidate.id;

      if (!candidateId || isNaN(candidateId)) {
        throw new Error("Invalid candidate ID. Cannot extract resume.");
      }

      const response = await ResumeExtractionService.extractResume(candidateId);

      // Extract the information from the API response
      const extractedInfo = response.data.extracted_information;

      if (!extractedInfo) {
        throw new Error("No information could be extracted from the resume.");
      }

      setExtractedData(extractedInfo);
      setShowExtractionModal(true);
      toast.success(
        response.message || "Resume information extracted successfully!",
      );
    } catch (error: any) {
      toast.error(error.message || "Failed to extract resume information");
    } finally {
      setIsExtracting(false);
    }
  };

  const handleApplyExtractedFields = (
    selectedFields: ExtractedFieldSelection,
  ) => {
    if (!extractedData) return;

    // Apply selected fields to the form
    if (selectedFields.name && extractedData.name) {
      setValue("name", extractedData.name);
    }
    if (selectedFields.email && extractedData.email) {
      setValue("email", extractedData.email);
    }
    if (selectedFields.phone && extractedData.phone) {
      setValue("phone", extractedData.phone);
    }
    if (selectedFields.address && extractedData.address) {
      setValue("location", extractedData.address);
    }
    if (selectedFields.skills && extractedData.skills) {
      const formattedSkills = ResumeExtractionService.formatSkills(
        extractedData.skills,
      );
      setSelectedSkills((prev) => {
        const combined = [...prev, ...formattedSkills];
        return Array.from(new Set(combined)); // Remove duplicates
      });
    }
    if (selectedFields.experience && extractedData.experience) {
      const formattedExperience = ResumeExtractionService.formatExperience(
        extractedData.experience,
      );
      setValue("workHistory", formattedExperience);
    }
    if (selectedFields.education && extractedData.education) {
      const formattedEducation = ResumeExtractionService.formatEducation(
        extractedData.education,
      );
      setValue("education", formattedEducation);
    }

    const appliedFields = Object.entries(selectedFields)
      .filter(([_, selected]) => selected)
      .map(([field, _]) => field);

    toast.success(
      `Applied ${appliedFields.length} fields from resume extraction`,
    );
  };

  const onSubmit = async (data: CandidateFormData) => {
    if (!candidate) return;

    try {
      const updatedCandidate: UiCandidate = {
        ...candidate,
        name: data.name,
        email: data.email,
        phone: data.phone,
        location: data.location,
        position: data.position,
        experience: data.experience,
        skills: selectedSkills, // Use selected skills from state
        source: data.source,
        jobId: data.jobId === "none" ? undefined : data.jobId || undefined, // v2.0.1: job assignment is optional
        salaryExpectationMin: data.salaryExpectationMin,
        salaryExpectationMax: data.salaryExpectationMax,
        salaryCurrency: data.salaryCurrency,
        linkedinUrl: data.linkedinUrl,
        resumeUrl: data.resumeUrl,
        githubUrl: data.githubUrl,
        portfolioUrl: data.portfolioUrl, // v2.0.1: added portfolio URL
        notes: data.notes,
        // v2.0.1: new simplified TEXT fields
        education: data.education,
        workHistory: data.workHistory,
        tags: selectedTags, // v2.0.1: simplified tags array
        initials: data.name
          .split(" ")
          .map((n) => n[0])
          .join("")
          .toUpperCase(),
      };

      await onUpdate(updatedCandidate);
      // Only close modal if no error occurred
      // The parent component will handle closing the modal on success
    } catch (error) {
      // Error will be handled by the parent component's error handling
      console.error("Form submission error:", error);
    }
  };

  if (!candidate) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Edit className="w-5 h-5 text-primary" />
            </div>
            {t.candidates?.editCandidate || "Edit Candidate"}: {candidate.name}
          </DialogTitle>
          <DialogDescription>
            {t.candidates?.updateCandidateProfile ||
              "Update candidate profile information and preferences."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">
                {t.candidates?.generalInformation || "General Information"}
              </TabsTrigger>
              <TabsTrigger value="documents">
                {t.candidates?.documents || "Documents"}
              </TabsTrigger>
              <TabsTrigger value="additional">
                {t.candidates?.notes || "Additional"}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-6">
              {/* Basic Information Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    {t.candidates?.basicInformation || "Basic Information"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name">
                        {t.candidates?.fullName || "Full Name"} *
                      </Label>
                      <Input
                        id="name"
                        {...register("name", {
                          required:
                            t.candidates?.nameRequired || "Name is required",
                        })}
                        placeholder={
                          t.candidates?.enterFullName || "Enter full name"
                        }
                        className="rounded-xl"
                      />
                      {errors.name && (
                        <p className="text-sm text-destructive">
                          {errors.name.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">
                        {t.candidates?.emailAddress || "Email Address"} *
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        {...register("email", {
                          required:
                            t.candidates?.emailRequired || "Email is required",
                          pattern: {
                            value: /^\S+@\S+$/,
                            message:
                              t.candidates?.invalidEmailFormat ||
                              "Invalid email format",
                          },
                        })}
                        placeholder={
                          t.candidates?.enterEmailAddress ||
                          "Enter email address"
                        }
                        className="rounded-xl"
                      />
                      {errors.email && (
                        <p className="text-sm text-destructive">
                          {errors.email.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">
                        {t.candidates?.phoneNumber || "Phone Number"}
                      </Label>
                      <Input
                        id="phone"
                        {...register("phone")}
                        placeholder="+****************"
                        className="rounded-xl"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="location">
                        {t.candidates?.location || "Location"}
                      </Label>
                      <Input
                        id="location"
                        {...register("location")}
                        placeholder={
                          t.candidates?.cityStateCountry ||
                          "City, State/Country"
                        }
                        className="rounded-xl"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Professional Information Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Briefcase className="w-5 h-5" />
                    {t.candidates?.professionalInformation ||
                      "Professional Information"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="position">
                        {t.candidates?.positionRole || "Position/Role"} *
                      </Label>
                      <Input
                        id="position"
                        {...register("position", {
                          required:
                            t.candidates?.positionRequired ||
                            "Position is required",
                        })}
                        placeholder={
                          t.candidates?.positionPlaceholder ||
                          "e.g., Senior Frontend Developer"
                        }
                        className="rounded-xl"
                      />
                      {errors.position && (
                        <p className="text-sm text-destructive">
                          {errors.position.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="experience">
                        {t.candidates?.experienceLevel || "Experience Level"}
                      </Label>
                      <Select
                        value={watch("experience")}
                        onValueChange={(value) => setValue("experience", value)}
                      >
                        <SelectTrigger className="rounded-xl">
                          <SelectValue
                            placeholder={
                              t.candidates?.selectExperienceLevel ||
                              "Select experience level"
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0-1 years">
                            {t.candidates?.entryLevel ||
                              "Entry Level (0-1 years)"}
                          </SelectItem>
                          <SelectItem value="2-3 years">
                            {t.candidates?.junior || "Junior (2-3 years)"}
                          </SelectItem>
                          <SelectItem value="3-5 years">
                            {t.candidates?.midLevel || "Mid-level (3-5 years)"}
                          </SelectItem>
                          <SelectItem value="5+ years">
                            {t.candidates?.senior || "Senior (5+ years)"}
                          </SelectItem>
                          <SelectItem value="8+ years">
                            {t.candidates?.leadPrincipal ||
                              "Lead/Principal (8+ years)"}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* v2.0.1: Separate salary expectation fields */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="salaryExpectationMin">
                          {t.candidates?.minSalary || "Min Salary (VND)"}
                        </Label>
                        <Input
                          id="salaryExpectationMin"
                          type="number"
                          {...register("salaryExpectationMin", {
                            valueAsNumber: true,
                            min: {
                              value: 0,
                              message: "Salary must be positive",
                            },
                          })}
                          placeholder="e.g., 18000000"
                          className="rounded-xl"
                        />
                        {errors.salaryExpectationMin && (
                          <span className="text-sm text-red-500">
                            {errors.salaryExpectationMin.message}
                          </span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="salaryExpectationMax">
                          {t.candidates?.maxSalary || "Max Salary (VND)"}
                        </Label>
                        <Input
                          id="salaryExpectationMax"
                          type="number"
                          {...register("salaryExpectationMax", {
                            valueAsNumber: true,
                            min: {
                              value: 0,
                              message: "Salary must be positive",
                            },
                            validate: (value) => {
                              const minSalary = watch("salaryExpectationMin");
                              if (minSalary && value && value < minSalary) {
                                return "Max salary must be greater than min salary";
                              }
                              return true;
                            },
                          })}
                          placeholder="e.g., 28000000"
                          className="rounded-xl"
                        />
                        {errors.salaryExpectationMax && (
                          <span className="text-sm text-red-500">
                            {errors.salaryExpectationMax.message}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="salaryCurrency">
                        {t.candidates?.currency || "Currency"}
                      </Label>
                      <Select
                        value={watch("salaryCurrency")}
                        onValueChange={(value) =>
                          setValue("salaryCurrency", value)
                        }
                      >
                        <SelectTrigger className="rounded-xl">
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="VND">
                            VND (Vietnamese Dong)
                          </SelectItem>
                          <SelectItem value="USD">USD (US Dollar)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="source">
                        {t.candidates?.source || "Source"}
                      </Label>
                      <Select
                        value={watch("source")}
                        onValueChange={(value) => setValue("source", value)}
                      >
                        <SelectTrigger className="rounded-xl">
                          <SelectValue
                            placeholder={
                              t.candidates?.selectSource || "Select source"
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {sourceSuggestions.map((source) => (
                            <SelectItem key={source} value={source}>
                              {source}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="jobId">
                      {t.candidates?.applyingForPosition ||
                        "Applying for Position"}
                    </Label>
                    <Select
                      value={watch("jobId")}
                      onValueChange={(value) => setValue("jobId", value)}
                    >
                      <SelectTrigger className="rounded-xl">
                        <SelectValue
                          placeholder={
                            t.candidates?.selectJobPosition ||
                            "Select job position"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">
                          {t.candidates?.noSpecificPosition ||
                            "No specific position"}
                        </SelectItem>
                        {activeJobs.map((job) => (
                          <SelectItem key={job.id} value={job.id}>
                            {job.title} - {job.department}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-4">
                    <Label>{t.candidates?.skills || "Skills"}</Label>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {selectedSkills.map((skill) => (
                        <Badge
                          key={skill}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {skill}
                          <button
                            type="button"
                            onClick={() => handleSkillRemove(skill)}
                            className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        value={customSkill}
                        onChange={(e) => setCustomSkill(e.target.value)}
                        placeholder={t.candidates?.addSkill || "Add a skill"}
                        className="rounded-xl"
                        onKeyPress={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            handleSkillAdd(customSkill);
                          }
                        }}
                      />
                      <Button
                        type="button"
                        onClick={() => handleSkillAdd(customSkill)}
                        className="rounded-xl"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {skillSuggestions
                        .filter((skill) => !selectedSkills.includes(skill))
                        .slice(0, 10)
                        .map((skill) => (
                          <Button
                            key={skill}
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleSkillAdd(skill)}
                            className="text-xs rounded-lg"
                          >
                            {skill}
                          </Button>
                        ))}
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Label htmlFor="education">Bằng cấp / chứng chỉ</Label>
                      <Textarea
                        id="education"
                        {...register("education")}
                        placeholder={
                          "Bằng cấp, Cao đẳng, Đại học, Sau đại học..."
                        }
                        rows={4}
                        className="rounded-xl"
                      />
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Label htmlFor="notes">Quá trình làm việc</Label>
                      <Textarea
                        id="workHistory"
                        {...register("workHistory")}
                        placeholder={"Thêm quá trình làm việc của ứng viên..."}
                        rows={4}
                        className="rounded-xl"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
              <Card className="border-dashed border-2 border-muted-foreground/25 hover:border-primary/50 transition-colors">
                <CardContent className="p-8">
                  <div className="text-center space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="resumeUrl">Link drive Resume</Label>
                      <Input
                        id="resumeUrl"
                        {...register("resumeUrl")}
                        placeholder="https://drive.google.com/file/d/11-vtfXyUK6jehaLc1j84PpnU29ddnagX/preview"
                        className="rounded-xl"
                      />
                    </div>

                    {/* Extract from Resume Button */}
                    {(watch("resumeUrl") || resumeFile) && (
                      <div className="pt-4 border-t border-dashed border-muted-foreground/25">
                        <Button
                          type="button"
                          onClick={handleExtractFromResume}
                          disabled={isExtracting}
                          className="ai-button"
                        >
                          {isExtracting ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Extracting...
                            </>
                          ) : (
                            <>
                              <FileText className="w-4 h-4 mr-2" />
                              Extract from Resume
                            </>
                          )}
                        </Button>
                        <p className="text-xs text-muted-foreground mt-2">
                          AI will extract and suggest form fields from the
                          resume
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="linkedinUrl">
                    {t.candidates?.linkedinProfile || "LinkedIn Profile"}
                  </Label>
                  <Input
                    id="linkedinUrl"
                    {...register("linkedinUrl")}
                    placeholder="https://linkedin.com/in/username"
                    className="rounded-xl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="githubUrl">
                    {t.candidates?.githubProfile || "GitHub Profile"}
                  </Label>
                  <Input
                    id="githubUrl"
                    {...register("githubUrl")}
                    placeholder="https://github.com/username"
                    className="rounded-xl"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="additional" className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="notes">{t.candidates?.notes || "Notes"}</Label>
                <Textarea
                  id="notes"
                  {...register("notes")}
                  placeholder={
                    t.candidates?.addNotes ||
                    "Add any additional notes about this candidate..."
                  }
                  rows={4}
                  className="rounded-xl"
                />
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1 rounded-xl"
            >
              {t.common?.cancel || "Cancel"}
            </Button>
            <Button type="submit" className="flex-1 ai-button">
              {t.candidates?.updateCandidate || "Update Candidate"}
            </Button>
          </div>
        </form>

        {/* Resume Extraction Modal */}
        <ResumeExtractionModal
          isOpen={showExtractionModal}
          onClose={() => setShowExtractionModal(false)}
          extractedData={extractedData}
          onApplyFields={handleApplyExtractedFields}
          isLoading={isExtracting}
        />
      </DialogContent>
    </Dialog>
  );
};
