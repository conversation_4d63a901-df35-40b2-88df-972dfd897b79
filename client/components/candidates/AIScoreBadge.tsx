import { Badge } from "@/components/ui/badge";
import { <PERSON>, Sparkles } from "lucide-react";
import { Candidate } from "@/data/mockData";

interface AIScoreBadgeProps {
  candidate: Candidate;
  size?: "sm" | "md";
}

export const AIScoreBadge = ({ candidate, size = "sm" }: AIScoreBadgeProps) => {
  // Return early if no candidate data
  if (!candidate) {
    return null;
  }

  // Mock AI score calculation based on candidate data
  const calculateAIScore = (candidate: Candidate): number => {
    let score = 60; // Base score

    // Add points for skills
    if (
      candidate.skills &&
      Array.isArray(candidate.skills) &&
      candidate.skills.length > 0
    ) {
      score += Math.min(25, candidate.skills.length * 3);
    }

    // Add points for experience length
    if (candidate.experience && typeof candidate.experience === "string") {
      score += Math.min(15, candidate.experience.length / 10);
    }

    // Add points for education (v2.0.1: now TEXT field)
    if (
      candidate.education &&
      typeof candidate.education === "string" &&
      candidate.education.trim().length > 0
    ) {
      score += 10;
    }

    // Random variation to make it more realistic
    score += Math.floor(Math.random() * 10) - 5;

    return Math.min(100, Math.max(0, score));
  };

  const aiScore = calculateAIScore(candidate);

  const getScoreColor = (score: number) => {
    if (score >= 85)
      return "bg-green-500/10 text-green-700 border-green-500/20";
    if (score >= 70) return "bg-blue-500/10 text-blue-700 border-blue-500/20";
    if (score >= 55)
      return "bg-yellow-500/10 text-yellow-700 border-yellow-500/20";
    return "bg-gray-500/10 text-gray-700 border-gray-500/20";
  };

  const getIcon = (score: number) => {
    if (score >= 85) return <Sparkles className="w-3 h-3" />;
    return <Brain className="w-3 h-3" />;
  };

  if (size === "md") {
    return (
      <Badge
        variant="outline"
        className={`${getScoreColor(aiScore)} flex items-center gap-1.5 px-2 py-1`}
      >
        {getIcon(aiScore)}
        <span className="font-semibold">AI: {aiScore}%</span>
      </Badge>
    );
  }

  return (
    <Badge
      variant="outline"
      className={`${getScoreColor(aiScore)} flex items-center gap-1 px-1.5 py-0.5 text-xs`}
    >
      {getIcon(aiScore)}
      <span className="font-medium">{aiScore}%</span>
    </Badge>
  );
};
