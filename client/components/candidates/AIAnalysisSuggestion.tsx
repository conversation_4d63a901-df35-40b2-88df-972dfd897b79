import { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON>, 
  Sparkles, 
  X, 
  ArrowRight,
  Zap
} from "lucide-react";

interface AIAnalysisSuggestionProps {
  candidateName: string;
  onGenerateAnalysis: () => void;
  onDismiss?: () => void;
  jobPostingId?: number;
}

export const AIAnalysisSuggestion = ({
  candidateName,
  onGenerateAnalysis,
  onDismiss,
  jobPostingId,
}: AIAnalysisSuggestionProps) => {
  const [isDismissed, setIsDismissed] = useState(false);

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  if (isDismissed) return null;

  return (
    <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-emerald-500/5 mb-6">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
              <Brain className="w-5 h-5 text-primary" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-semibold">AI Analysis Available</h4>
                <Badge variant="secondary" className="text-xs">
                  <Sparkles className="w-3 h-3 mr-1" />
                  Powered by AI
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Generate comprehensive insights for {candidateName} including skills assessment, 
                strengths analysis, and personalized recommendations.
                {jobPostingId && " Job-specific matching will be included."}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={onGenerateAnalysis}
              className="flex items-center gap-2"
            >
              <Zap className="w-4 h-4" />
              Generate AI Analysis
              <ArrowRight className="w-4 h-4" />
            </Button>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
