import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Candidate, mockJobs } from "@/data/mockData";
import { formatDistanceToNow } from "date-fns";
import { safeFormatDistanceToNow } from "@/lib/utils";
import { AICandidateSummary } from "./AICandidateSummary";

import {
  CandidateContactInfo,
  CandidateStatusActions,
  CandidateProfessionalInfo,
  CandidateSkills,
  CandidateDocuments,
  CandidateNotes,
} from "./detail";
import { useUpdateCandidateStatus } from "@/hooks/useApi";
import { useToast } from "@/hooks/use-toast";

interface CandidateDetailModalProps {
  candidate: Candidate | null;
  isOpen: boolean;
  onClose: () => void;
  onStatusChange?: (
    candidateId: string,
    newStatus: Candidate["status"],
  ) => void;
  onEdit?: (candidate: Candidate) => void;
}

export const CandidateDetailModal = ({
  candidate,
  isOpen,
  onClose,
  onStatusChange,
  onEdit,
}: CandidateDetailModalProps) => {
  const [rating, setRating] = useState(candidate?.rating || 0);
  const { toast } = useToast();
  const updateStatusMutation = useUpdateCandidateStatus();

  if (!candidate) return null;

  const job = mockJobs.find((j) => j.id === candidate.jobId);

  const handleStatusChange = async (newStatus: string) => {
    try {
      await updateStatusMutation.mutateAsync({
        id: candidate.id?.toString() || candidate.id,
        status: newStatus,
        notes: `Trạng thái đã được thay đổi thành ${newStatus} từ modal chi tiết ứng viên`,
      });

      toast({
        title: "Đã cập nhật trạng thái",
        description: `Trạng thái ứng viên đã được thay đổi thành ${newStatus}`,
      });

      // Call the callback if provided (for backward compatibility)
      if (onStatusChange) {
        onStatusChange(candidate.id, newStatus as Candidate["status"]);
      }
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật trạng thái ứng viên",
        variant: "destructive",
      });
      console.error("Lỗi cập nhật trạng thái:", error);
    }
  };

  const getValidDate = (dateString: string) => {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? new Date() : date;
  };

  const baseDate = getValidDate(candidate.appliedDate);
  const timeline = [];
  //   {
  //     event: "Đã nộp hồ sơ",
  //     date: candidate.appliedDate,
  //     type: "application",
  //   },
  //   {
  //     event: "Đã xem xét CV",
  //     date: new Date(baseDate.getTime() + 24 * 60 * 60 * 1000).toISOString(),
  //     type: "review",
  //   },
  //   {
  //     event: "Phỏng vấn qua điện thoại",
  //     date: new Date(
  //       baseDate.getTime() + 3 * 24 * 60 * 60 * 1000,
  //     ).toISOString(),
  //     type: "screening",
  //   },
  // ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={candidate.avatar} />
              <AvatarFallback>{candidate.initials}</AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-bold">{candidate.name}</h2>
              <p className="text-muted-foreground">{candidate.position}</p>
            </div>
          </DialogTitle>
          <DialogDescription>
            Xem và quản lý thông tin ứng viên, trạng thái và tương tác
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="ai-summary" className="mt-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="ai-summary">Tóm tắt AI</TabsTrigger>
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="activity">Hoạt động</TabsTrigger>
            <TabsTrigger value="documents">Tài liệu</TabsTrigger>
            <TabsTrigger value="feedback">Phản hồi</TabsTrigger>
          </TabsList>

          <TabsContent value="ai-summary" className="space-y-6">
            <AICandidateSummary
              candidate={candidate}
              position={candidate.position}
              jobPostingId={
                candidate.jobId ? parseInt(candidate.jobId) : undefined
              }
              jobDescription="Tìm kiếm một lập trình viên có kỹ năng với kinh nghiệm về React, TypeScript và các công nghệ web hiện đại. Phải có kỹ năng giải quyết vấn đề tốt và khả năng làm việc trong môi trường agile."
            />
          </TabsContent>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Contact Information */}
              <CandidateContactInfo
                candidate={{
                  email: candidate.email,
                  phone: candidate.phone,
                  location: candidate.location,
                  appliedDate: candidate.appliedDate,
                  linkedin: candidate.linkedinUrl,
                  github: candidate.githubUrl,
                  portfolio: candidate.portfolioUrl,
                }}
              />

              {/* Status and Actions */}
              <CandidateStatusActions
                candidate={{
                  id: candidate.id,
                  status: candidate.status,
                  rating: candidate.rating || rating,
                }}
                jobInfo={
                  job
                    ? {
                        title: job.title,
                        department: job.department,
                        location: job.location,
                      }
                    : undefined
                }
                onStatusChange={(candidateId, newStatus) => {
                  handleStatusChange(newStatus);
                }}
                onEdit={onEdit}
                disabled={updateStatusMutation.isPending}
              />
            </div>

            {/* Professional Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <CandidateProfessionalInfo
                candidate={{
                  experience: candidate.experience,
                  education: candidate.education,
                  workHistory: candidate.workHistory,
                  source: candidate.source,
                  salaryExpectation: candidate.salaryExpectation,
                }}
              />

              <CandidateSkills
                candidate={{
                  skills: candidate.skills,
                  tags: candidate.tags,
                  linkedinUrl: candidate.linkedinUrl,
                  githubUrl: candidate.githubUrl,
                  portfolioUrl: candidate.portfolioUrl,
                  resumeUrl: candidate.resumeUrl,
                }}
              />
            </div>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Dòng thời gian hoạt động</CardTitle>
                <CardDescription>
                  Theo dõi tương tác và tiến trình của ứng viên
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {timeline.map((item, index) => (
                    <div key={index} className="flex gap-4">
                      <div className="flex flex-col items-center">
                        <div className="w-3 h-3 bg-primary rounded-full"></div>
                        {index < timeline.length - 1 && (
                          <div className="w-px h-8 bg-border"></div>
                        )}
                      </div>
                      <div className="flex-1 pb-4">
                        <p className="font-medium">{item.event}</p>
                        <p className="text-sm text-muted-foreground">
                          {safeFormatDistanceToNow(item.date, {
                            addSuffix: true,
                          })}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <CandidateDocuments
              candidate={{
                resumeUrl: candidate.resumeUrl,
                appliedDate: candidate.appliedDate,
              }}
              documents={[
                {
                  id: "cover-letter",
                  name: "Thư xin việc.pdf",
                  type: "cover_letter",
                  uploadDate: candidate.appliedDate,
                  size: "245 KB",
                },
              ]}
              onDownload={(documentId) => {
                console.log("Đang tải tài liệu:", documentId);
              }}
              onView={(documentId) => {
                console.log("Đang xem tài liệu:", documentId);
              }}
              onUpload={() => {
                console.log("Tải lên tài liệu mới");
              }}
            />
          </TabsContent>

          <TabsContent value="feedback" className="space-y-4">
            <CandidateNotes
              notes={[
                {
                  id: "1",
                  content: "Mẫu ghi chú.",
                  author: {
                    id: "john-smith",
                    name: "Nguyễn Văn Minh",
                    initials: "NM",
                  },
                  createdAt: new Date(
                    Date.now() - 2 * 24 * 60 * 60 * 1000,
                  ).toISOString(),
                  type: "feedback",
                  rating: 4,
                },
              ]}
              onAddNote={(content) => {
                console.log("Đang thêm ghi chú:", content);
                toast({
                  title: "Đã thêm ghi chú",
                  description: "Ghi chú đã được lưu thành công",
                });
              }}
              onEditNote={(noteId, content) => {
                console.log("Đang chỉnh sửa ghi chú:", noteId, content);
                toast({
                  title: "Đã cập nhật ghi chú",
                  description: "Ghi chú đã được cập nhật thành công",
                });
              }}
              onDeleteNote={(noteId) => {
                console.log("Đang xóa ghi chú:", noteId);
                toast({
                  title: "Đã xóa ghi chú",
                  description: "Ghi chú đã được xóa thành công",
                });
              }}
              disabled={updateStatusMutation.isPending}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
