import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  GraduationCap,
  Code,
  Check,
  X,
} from "lucide-react";
import {
  ExtractedInformation,
  ExtractedFieldSelection,
} from "@/lib/types/resumeExtraction";
import { ResumeExtractionService } from "@/lib/services/resumeExtractionService";

interface ResumeExtractionModalProps {
  isOpen: boolean;
  onClose: () => void;
  extractedData: ExtractedInformation | null;
  onApplyFields: (selectedFields: ExtractedFieldSelection) => void;
  isLoading?: boolean;
}

const fieldLabels = {
  name: "Full Name",
  email: "Email Address",
  phone: "Phone Number",
  address: "Address/Location", 
  skills: "Skills",
  experience: "Work Experience",
  education: "Education",
};

const fieldIcons = {
  name: User,
  email: Mail,
  phone: Phone,
  address: MapPin,
  skills: Code,
  experience: Briefcase,
  education: GraduationCap,
};

export const ResumeExtractionModal = ({
  isOpen,
  onClose,
  extractedData,
  onApplyFields,
  isLoading = false,
}: ResumeExtractionModalProps) => {
  const [selectedFields, setSelectedFields] = useState<ExtractedFieldSelection>({
    name: true,
    email: true,
    phone: true,
    address: true,
    skills: true,
    experience: true,
    education: true,
  });

  const handleFieldToggle = (field: keyof ExtractedFieldSelection) => {
    setSelectedFields(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleSelectAll = () => {
    const allSelected = Object.values(selectedFields).every(Boolean);
    const newValue = !allSelected;
    setSelectedFields({
      name: newValue,
      email: newValue,
      phone: newValue,
      address: newValue,
      skills: newValue,
      experience: newValue,
      education: newValue,
    });
  };

  const handleApply = () => {
    onApplyFields(selectedFields);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  if (!extractedData) {
    return null;
  }

  const availableFields = Object.entries(extractedData).filter(([key, value]) => {
    if (key === "skills") return Array.isArray(value) && value.length > 0;
    if (key === "experience") return Array.isArray(value) && value.length > 0;
    if (key === "education") return Array.isArray(value) && value.length > 0;
    return value && String(value).trim().length > 0;
  });

  const selectedCount = Object.values(selectedFields).filter(Boolean).length;
  const allSelected = Object.values(selectedFields).every(Boolean);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Briefcase className="w-5 h-5 text-primary" />
            </div>
            Resume Information Extracted
          </DialogTitle>
          <DialogDescription>
            Review the extracted information and select which fields to populate in your form.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Selection Summary */}
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-primary" />
                  <span className="font-medium">
                    {selectedCount} of {availableFields.length} fields selected
                  </span>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="text-xs"
                >
                  {allSelected ? "Deselect All" : "Select All"}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Extracted Fields */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Extracted Information</h3>
            <div className="grid gap-4">
              {availableFields.map(([field, value]) => {
                const fieldKey = field as keyof ExtractedFieldSelection;
                const Icon = fieldIcons[fieldKey];
                const isSelected = selectedFields[fieldKey];
                
                return (
                  <Card 
                    key={field}
                    className={`transition-all duration-200 ${
                      isSelected 
                        ? "border-primary/50 bg-primary/5" 
                        : "border-border hover:border-primary/30"
                    }`}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Icon className="w-4 h-4 text-primary" />
                          <CardTitle className="text-base">
                            {fieldLabels[fieldKey]}
                          </CardTitle>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`field-${field}`}
                            checked={isSelected}
                            onCheckedChange={() => handleFieldToggle(fieldKey)}
                            className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                          />
                          <Label 
                            htmlFor={`field-${field}`}
                            className="text-sm cursor-pointer"
                          >
                            Include
                          </Label>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="text-sm text-muted-foreground">
                        {fieldKey === "skills" && Array.isArray(value) ? (
                          <div className="flex flex-wrap gap-1">
                            {ResumeExtractionService.formatSkills(value).map((skill, idx) => (
                              <Badge key={idx} variant="secondary" className="text-xs">
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        ) : fieldKey === "experience" && Array.isArray(value) ? (
                          <div className="space-y-2">
                            {value.slice(0, 2).map((exp, idx) => (
                              <div key={idx} className="p-2 bg-muted/50 rounded-md">
                                <div className="font-medium text-foreground">
                                  {exp.position} {exp.company && `at ${exp.company}`}
                                </div>
                                {exp.duration && (
                                  <div className="text-xs text-muted-foreground">
                                    {exp.duration}
                                  </div>
                                )}
                              </div>
                            ))}
                            {value.length > 2 && (
                              <div className="text-xs text-muted-foreground">
                                +{value.length - 2} more positions
                              </div>
                            )}
                          </div>
                        ) : fieldKey === "education" && Array.isArray(value) ? (
                          <div className="space-y-2">
                            {value.map((edu, idx) => (
                              <div key={idx} className="p-2 bg-muted/50 rounded-md">
                                <div className="font-medium text-foreground">
                                  {edu.degree}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {edu.institution} {edu.graduation_year && `(${edu.graduation_year})`}
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <span className="break-words">{String(value)}</span>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>

        <Separator />

        <DialogFooter className="gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="flex-1 sm:flex-none"
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleApply}
            disabled={selectedCount === 0 || isLoading}
            className="flex-1 sm:flex-none"
          >
            <Check className="w-4 h-4 mr-2" />
            Apply Selected ({selectedCount})
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
