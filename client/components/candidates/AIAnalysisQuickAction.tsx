import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Loader2,
  CheckCircle2,
  AlertCircle
} from "lucide-react";
import { useAIAnalysis } from "@/hooks/useAIAnalysis";
import { toast } from "sonner";

interface AIAnalysisQuickActionProps {
  candidateId: number | string | undefined;
  candidateName?: string;
  jobPostingId?: number;
  size?: "sm" | "default" | "lg";
  variant?: "default" | "outline" | "ghost";
  showBadge?: boolean;
}

export const AIAnalysisQuickAction = ({
  candidateId,
  candidateName = "candidate",
  jobPostingId,
  size = "sm",
  variant = "outline",
  showBadge = true,
}: AIAnalysisQuickActionProps) => {
  const {
    analysis,
    isGenerating,
    isProcessing,
    needsGeneration,
    error,
    generateAnalysis,
  } = useAIAnalysis({
    candidateId,
    jobPostingId,
    autoLoad: true,
  });

  const handleGenerateAnalysis = async () => {
    try {
      const response = await generateAnalysis();
      toast.success(`AI analysis generated for ${candidateName}!`);
    } catch (error: any) {
      toast.error(`Failed to generate analysis: ${error.message}`);
    }
  };

  // If analysis exists and is completed
  if (analysis?.status === "completed") {
    return showBadge ? (
      <Badge variant="secondary" className="flex items-center gap-1">
        <CheckCircle2 className="w-3 h-3 text-green-500" />
        AI Analyzed
      </Badge>
    ) : null;
  }

  // If analysis is processing
  if (isProcessing) {
    return (
      <Button
        size={size}
        variant={variant}
        disabled
        className="flex items-center gap-2"
      >
        <Loader2 className="w-4 h-4 animate-spin" />
        {size !== "sm" && "Processing..."}
      </Button>
    );
  }

  // If analysis failed
  if (analysis?.status === "failed") {
    return (
      <Button
        size={size}
        variant="destructive"
        onClick={handleGenerateAnalysis}
        disabled={isGenerating}
        className="flex items-center gap-2"
      >
        <AlertCircle className="w-4 h-4" />
        {size !== "sm" && "Retry Analysis"}
      </Button>
    );
  }

  // If no analysis exists
  if (needsGeneration) {
    return (
      <Button
        size={size}
        variant={variant}
        onClick={handleGenerateAnalysis}
        disabled={isGenerating}
        className="flex items-center gap-2"
      >
        {isGenerating ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Brain className="w-4 h-4" />
        )}
        {size !== "sm" && (isGenerating ? "Generating..." : "AI Analysis")}
      </Button>
    );
  }

  return null;
};
