import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useTranslation } from "@/lib/i18n";
import { toast } from "sonner";
import {
  Brain,
  Target,
  TrendingUp,
  Clock,
  Award,
  AlertCircle,
  CheckCircle2,
  Sparkles,
  FileText,
  Users,
  Briefcase,
  Loader2,
  RefreshCw,
  Zap,
  BarChart3,
  TrendingDown,
  CheckSquare,
} from "lucide-react";
import { Candidate } from "@/data/mockData";
import { useAIAnalysis } from "@/hooks/useAIAnalysis";

interface AICandidateSummaryProps {
  candidate: Candidate;
  jobDescription?: string;
  position?: string;
  jobPostingId?: number;
}

export const AICandidateSummary = ({
  candidate,
  jobDescription,
  position,
  jobPostingId,
}: AICandidateSummaryProps) => {
  const { t } = useTranslation();

  const {
    analysis,
    isLoading,
    isGenerating,
    isProcessing,
    needsGeneration,
    error,
    generateAnalysis,
    retryAnalysis,
    refreshAnalysis,
  } = useAIAnalysis({
    candidateId: candidate.id,
    jobPostingId,
    autoLoad: true,
  });

  const handleGenerateAnalysis = async () => {
    try {
      const response = await generateAnalysis();
      toast.success(response?.message || "AI analysis generated successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to generate AI analysis");
    }
  };

  const handleRetryAnalysis = async () => {
    try {
      const response = await retryAnalysis();
      toast.success(response?.message || "AI analysis generated successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to generate AI analysis");
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-green-100 dark:bg-green-900/20";
    if (score >= 60) return "bg-yellow-100 dark:bg-yellow-900/20";
    return "bg-red-100 dark:bg-red-900/20";
  };

  const getProgressColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Show loading state
  if (isLoading) {
    return (
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-blue-500/5">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center space-y-4">
            <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto" />
            <p className="text-muted-foreground">Loading AI analysis...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show generate analysis prompt if no analysis exists
  if (!analysis && !isProcessing) {
    return (
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-blue-500/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-primary" />
            AI Candidate Analysis
            <Badge variant="secondary" className="ml-auto">
              <Sparkles className="w-3 h-3 mr-1" />
              AI Powered
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <Zap className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">
                Generate AI Analysis
              </h3>
              <p className="text-muted-foreground mb-4">
                Nhận thông tin chi tiết toàn diện được hỗ trợ bởi AI về ứng
                viên, bao gồm điểm mạnh, điểm yếu, đánh giá kỹ năng và đề xuất.
                {jobPostingId &&
                  "  Phân tích kết hợp công việc cụ thể sẽ được bao gồm nếu bạn đang xem xét ứng viên cho một công việc cụ thể."}
              </p>
            </div>

            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">{error}</span>
                </div>
              </div>
            )}

            <div className="flex gap-3 justify-center">
              <Button
                onClick={handleGenerateAnalysis}
                disabled={isGenerating}
                className="flex items-center gap-2"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Generating Analysis...
                  </>
                ) : (
                  <>
                    <Brain className="w-4 h-4" />
                    Generate AI Analysis
                  </>
                )}
              </Button>

              {error && (
                <Button
                  variant="outline"
                  onClick={handleRetryAnalysis}
                  disabled={isGenerating}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Retry
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show processing state
  if (isProcessing) {
    return (
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-blue-500/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-primary" />
            AI Analysis in Progress
            <Badge variant="secondary" className="ml-auto">
              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
              {analysis?.status === "pending" ? "Pending" : "Processing"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <Loader2 className="w-8 h-8 text-primary animate-spin" />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">
                Analyzing Candidate Profile
              </h3>
              <p className="text-muted-foreground">
                Our AI is processing the candidate's information and generating
                comprehensive insights. This usually takes 30-60 seconds.
              </p>
              {analysis?.processing?.started_at && (
                <p className="text-xs text-muted-foreground mt-2">
                  Started at:{" "}
                  {new Date(
                    analysis.processing.started_at,
                  ).toLocaleTimeString()}
                </p>
              )}
            </div>

            <Button
              variant="outline"
              onClick={refreshAnalysis}
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show error state
  if (analysis?.status === "failed") {
    return (
      <Card className="border-red-200 bg-red-50 dark:bg-red-900/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="w-5 h-5" />
            Analysis Failed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              The AI analysis could not be completed. Please try generating a
              new analysis.
            </p>
            <Button
              onClick={handleGenerateAnalysis}
              disabled={isGenerating}
              className="flex items-center gap-2"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4" />
                  Retry Analysis
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show completed analysis
  if (!analysis) return null;

  return (
    <div className="space-y-6">
      {/* AI Analysis Header */}
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-blue-500/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-primary" />
            AI Candidate Analysis
            <Badge variant="secondary" className="ml-auto">
              <CheckCircle2 className="w-3 h-3 mr-1 text-green-500" />
              Completed
            </Badge>
          </CardTitle>
          {analysis.summary && (
            <p className="text-sm text-muted-foreground mt-2">
              {analysis.summary}
            </p>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Overall Score */}
            <div className="text-center">
              <div
                className={`text-3xl font-bold mb-2 ${getScoreColor(analysis.scores.overall)}`}
              >
                {analysis.scores.overall}%
              </div>
              <p className="text-sm text-muted-foreground">Overall Score</p>
            </div>

            {/* Skills Breakdown */}
            <div className="col-span-3 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Technical Skills</span>
                <span
                  className={`text-sm font-bold ${getScoreColor(analysis.scores.skills)}`}
                >
                  {analysis.scores.skills}%
                </span>
              </div>
              <Progress value={analysis.scores.skills} className="h-2" />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Experience</span>
                <span
                  className={`text-sm font-bold ${getScoreColor(analysis.scores.experience)}`}
                >
                  {analysis.scores.experience}%
                </span>
              </div>
              <Progress value={analysis.scores.experience} className="h-2" />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Education</span>
                <span
                  className={`text-sm font-bold ${getScoreColor(analysis.scores.education)}`}
                >
                  {analysis.scores.education}%
                </span>
              </div>
              <Progress value={analysis.scores.education} className="h-2" />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Cultural Fit</span>
                <span
                  className={`text-sm font-bold ${getScoreColor(analysis.scores.cultural_fit)}`}
                >
                  {analysis.scores.cultural_fit}%
                </span>
              </div>
              <Progress value={analysis.scores.cultural_fit} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Job Matching Section (if available) */}
      {analysis.jobMatching && (
        <Card className="border-blue-200 bg-blue-50 dark:bg-blue-900/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5 text-blue-500" />
              Job Matching Analysis
              <Badge variant="secondary" className="ml-auto">
                {analysis.jobMatching.matchPercentage}% Match
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {analysis.jobMatching.keyAlignments.length > 0 && (
              <div>
                <h4 className="font-medium text-green-600 mb-2 flex items-center gap-2">
                  <CheckSquare className="w-4 h-4" />
                  Key Alignments
                </h4>
                <div className="space-y-1">
                  {analysis.jobMatching.keyAlignments.map(
                    (alignment, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2 text-sm"
                      >
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                        {alignment}
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}

            {analysis.jobMatching.missingRequirements.length > 0 && (
              <div>
                <h4 className="font-medium text-orange-600 mb-2 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  Missing Requirements
                </h4>
                <div className="space-y-1">
                  {analysis.jobMatching.missingRequirements.map(
                    (req, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2 text-sm"
                      >
                        <div className="w-2 h-2 bg-orange-500 rounded-full" />
                        {req}
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}

            {analysis.jobMatching.matchingCriteria.length > 0 && (
              <div>
                <h4 className="font-medium text-blue-600 mb-2 flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Matching Criteria
                </h4>
                <div className="space-y-1">
                  {analysis.jobMatching.matchingCriteria.map(
                    (criteria, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2 text-sm"
                      >
                        <div className="w-2 h-2 bg-blue-500 rounded-full" />
                        {criteria}
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Strengths */}
        {analysis.strengths.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle2 className="w-5 h-5 text-green-500" />
                Strengths
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {analysis.strengths.map((strength, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-sm">{strength}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Weaknesses */}
        {analysis.weaknesses.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingDown className="w-5 h-5 text-red-500" />
                Areas for Improvement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {analysis.weaknesses.map((weakness, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full" />
                    <span className="text-sm">{weakness}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Improvement Areas */}
        {analysis.improvementAreas.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-orange-500" />
                Development Opportunities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {analysis.improvementAreas.map((area, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full" />
                    <span className="text-sm">{area}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* AI Recommendations */}
        {analysis.recommendations.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5 text-blue-500" />
                AI Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {analysis.recommendations.map((rec, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <Award className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{rec}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button
          onClick={handleGenerateAnalysis}
          disabled={isGenerating}
          variant="outline"
          className="flex items-center gap-2"
        >
          {isGenerating ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Regenerating...
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4" />
              Regenerate Analysis
            </>
          )}
        </Button>
        <Button variant="outline" className="flex items-center gap-2">
          <FileText className="w-4 h-4" />
          Export Report
        </Button>
        <Button variant="outline" className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          Compare Candidates
        </Button>
      </div>

      {/* Analysis metadata */}
      {analysis.createdAt && (
        <div className="text-xs text-muted-foreground">
          Analysis completed on {new Date(analysis.createdAt).toLocaleString()}
          {analysis.processing?.duration_seconds &&
            ` • Processing time: ${analysis.processing.duration_seconds}s`}
        </div>
      )}
    </div>
  );
};
