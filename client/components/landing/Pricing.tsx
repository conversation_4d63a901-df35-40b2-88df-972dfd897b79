import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "@/lib/i18n";
import { Check, Star } from "lucide-react";

export const Pricing = () => {
  const { t } = useTranslation();

  const plans = [
    {
      name: t.landing.pricing.free.name,
      price: t.landing.pricing.free.price,
      description: t.landing.pricing.free.description,
      features: t.landing.pricing.free.features,
      cta: t.landing.pricing.free.cta,
      popular: false,
      variant: "outline" as const,
    },
    {
      name: t.landing.pricing.pro.name,
      price: t.landing.pricing.pro.price,
      description: t.landing.pricing.pro.description,
      features: t.landing.pricing.pro.features,
      cta: t.landing.pricing.pro.cta,
      popular: true,
      variant: "default" as const,
    },
    {
      name: t.landing.pricing.enterprise.name,
      price: t.landing.pricing.enterprise.price,
      description: t.landing.pricing.enterprise.description,
      features: t.landing.pricing.enterprise.features,
      cta: t.landing.pricing.enterprise.cta,
      popular: false,
      variant: "outline" as const,
    },
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            {t.landing.pricing.title}
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
            {t.landing.pricing.subtitle}
          </p>

          {/* Billing toggle */}
          <div className="inline-flex items-center bg-muted rounded-lg p-1">
            <button className="px-4 py-2 rounded-md bg-background text-foreground font-medium transition-all">
              {t.landing.pricing.monthly}
            </button>
            <button className="px-4 py-2 rounded-md text-muted-foreground font-medium transition-all hover:text-foreground">
              {t.landing.pricing.yearly}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={`relative group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 ${
                plan.popular
                  ? "border-primary/50 bg-gradient-to-br from-primary/5 to-primary/10"
                  : "border-0 bg-card/50"
              }`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 bg-primary text-primary-foreground">
                  <Star className="w-3 h-3 mr-1" />
                  {t.landing.pricing.pro.popular}
                </Badge>
              )}

              <CardHeader className="text-center pb-4">
                <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                <div className="mb-2">
                  <span className="text-4xl font-bold">{plan.price}</span>
                  {plan.price !== t.landing.pricing.enterprise.price &&
                    plan.price !== "0đ" &&
                    plan.price !== "$0" && (
                      <span className="text-muted-foreground">/tháng</span>
                    )}
                </div>
                <p className="text-muted-foreground">{plan.description}</p>
              </CardHeader>

              <CardContent>
                <Button
                  className="w-full mb-6"
                  variant={plan.variant}
                  size="lg"
                >
                  {plan.cta}
                </Button>

                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
