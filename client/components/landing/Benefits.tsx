import { Card, CardContent } from "@/components/ui/card";
import { useTranslation } from "@/lib/i18n";
import { TrendingUp, Award, Heart } from "lucide-react";

export const Benefits = () => {
  const { t } = useTranslation();

  const benefits = [
    {
      icon: TrendingUp,
      title: t.landing.benefits.efficiency.title,
      description: t.landing.benefits.efficiency.description,
      color: "text-green-500",
      bgColor: "bg-green-500/10",
    },
    {
      icon: Award,
      title: t.landing.benefits.quality.title,
      description: t.landing.benefits.quality.description,
      color: "text-blue-500",
      bgColor: "bg-blue-500/10",
    },
    {
      icon: Heart,
      title: t.landing.benefits.experience.title,
      description: t.landing.benefits.experience.description,
      color: "text-red-500",
      bgColor: "bg-red-500/10",
    },
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            {t.landing.benefits.title}
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            {t.landing.benefits.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => {
            const Icon = benefit.icon;
            return (
              <Card
                key={index}
                className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 text-center border-0 bg-gradient-to-br from-card to-card/50"
              >
                <CardContent className="p-8">
                  <div
                    className={`${benefit.bgColor} ${benefit.color} w-20 h-20 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300`}
                  >
                    <Icon className="w-10 h-10" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 group-hover:text-primary transition-colors">
                    {benefit.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};
