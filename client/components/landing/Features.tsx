import { Card, CardContent } from "@/components/ui/card";
import { useTranslation } from "@/lib/i18n";
import {
  Users,
  GitBranch,
  Calendar,
  Mail,
  BarChart3,
  MessageSquare,
} from "lucide-react";

export const Features = () => {
  const { t } = useTranslation();

  const features = [
    {
      icon: Users,
      title: t.landing.features.candidateManagement.title,
      description: t.landing.features.candidateManagement.description,
      color: "text-blue-500",
      bgColor: "bg-blue-500/10",
    },
    {
      icon: GitBranch,
      title: t.landing.features.pipeline.title,
      description: t.landing.features.pipeline.description,
      color: "text-purple-500",
      bgColor: "bg-purple-500/10",
    },
    {
      icon: Calendar,
      title: t.landing.features.interviews.title,
      description: t.landing.features.interviews.description,
      color: "text-green-500",
      bgColor: "bg-green-500/10",
    },
    {
      icon: Mail,
      title: t.landing.features.automation.title,
      description: t.landing.features.automation.description,
      color: "text-orange-500",
      bgColor: "bg-orange-500/10",
    },
    {
      icon: BarChart3,
      title: t.landing.features.analytics.title,
      description: t.landing.features.analytics.description,
      color: "text-red-500",
      bgColor: "bg-red-500/10",
    },
    {
      icon: MessageSquare,
      title: t.landing.features.collaboration.title,
      description: t.landing.features.collaboration.description,
      color: "text-cyan-500",
      bgColor: "bg-cyan-500/10",
    },
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            {t.landing.features.title}
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            {t.landing.features.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card
                key={index}
                className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-0 bg-card/50 backdrop-blur-sm"
              >
                <CardContent className="p-8">
                  <div
                    className={`${feature.bgColor} ${feature.color} w-16 h-16 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <Icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4 group-hover:text-primary transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};
