import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "@/lib/i18n";
import { ArrowRight, Calendar, User } from "lucide-react";

export const Blog = () => {
  const { t } = useTranslation();

  // Mock blog posts data
  const blogPosts = [
    {
      title: "AI trong tuyển dụng: Xu hướng 2024",
      excerpt:
        "Khám phá cách AI đang thay đổi cách thức tuyển dụng và những lợi ích mà nó mang lại cho HR.",
      author: "Nguyễn Văn A",
      date: "15 Tháng 3, 2024",
      category: "AI & Technology",
      image:
        "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=250&fit=crop",
    },
    {
      title: "10 câu hỏi phỏng vấn hiệu qu<PERSON> nhất",
      excerpt:
        "<PERSON><PERSON> sách các câu hỏi phỏng vấn giúp bạn đánh giá ứng viên một cách toàn diện và chính xác.",
      author: "Trần Thị B",
      date: "12 Tháng 3, 2024",
      category: "Interview Tips",
      image:
        "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=250&fit=crop",
    },
    {
      title: "Xây dựng thương hiệu nhà tuyển dụng",
      excerpt:
        "Hướng dẫn chi tiết cách xây dựng thương hiệu nhà tuyển dụng để thu hút ứng viên chất lượng.",
      author: "Lê Văn C",
      date: "10 Tháng 3, 2024",
      category: "Employer Branding",
      image:
        "https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=250&fit=crop",
    },
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            {t.landing.blog.title}
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            {t.landing.blog.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {blogPosts.map((post, index) => (
            <Card
              key={index}
              className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-0 bg-card/50 backdrop-blur-sm overflow-hidden"
            >
              <div className="aspect-video bg-muted overflow-hidden">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <CardContent className="p-6">
                <Badge variant="secondary" className="mb-3">
                  {post.category}
                </Badge>
                <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors line-clamp-2">
                  {post.title}
                </h3>
                <p className="text-muted-foreground mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    {post.author}
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    {post.date}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  className="p-0 h-auto font-semibold group/btn"
                >
                  {t.landing.blog.readMore}
                  <ArrowRight className="w-4 h-4 ml-1 group-hover/btn:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button variant="outline" size="lg">
            {t.landing.blog.viewAll}
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
};
