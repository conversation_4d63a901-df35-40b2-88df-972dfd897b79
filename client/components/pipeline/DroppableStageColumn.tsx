import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  GitBranch,
  MoreHorizontal,
  Plus,
  Settings,
  Filter,
  SortAsc,
  Mail,
  Users,
} from "lucide-react";
import { DraggableCandidateCard } from "./DraggableCandidateCard";
import { Candidate } from "@/domains/candidates/types";

interface DroppableStageColumnProps {
  stage: {
    id: string;
    name: string;
    color: string;
    description?: string;
  };
  candidates: Candidate[];
  onDrop: (candidateId: string, newStatus: Candidate["status"]) => void;
  onStatusChange: (candidateId: string, newStatus: Candidate["status"]) => void;
  onSendEmail: (candidate: Candidate) => void;
  onSendBulkEmail: (candidates: Candidate[]) => void;
  onViewDetails: (candidate: Candidate) => void;
  onAddCandidate: (status: Candidate["status"]) => void;
}

export const DroppableStageColumn = ({
  stage,
  candidates,
  onDrop,
  onStatusChange,
  onSendEmail,
  onSendBulkEmail,
  onViewDetails,
  onAddCandidate,
}: DroppableStageColumnProps) => {
  const [dragOver, setDragOver] = useState(false);
  const [sortBy, setSortBy] = useState<"date" | "name" | "rating">("date");

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    const candidateId = e.dataTransfer.getData("text/plain");
    if (candidateId) {
      onDrop(candidateId, stage.id as Candidate["status"]);
    }
  };

  const sortedCandidates = [...candidates].sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name);
      case "rating":
        return (b.rating || 0) - (a.rating || 0);
      case "date":
      default:
                const dateA = new Date(a.appliedDate);
        const dateB = new Date(b.appliedDate);
        const timeA = isNaN(dateA.getTime()) ? 0 : dateA.getTime();
        const timeB = isNaN(dateB.getTime()) ? 0 : dateB.getTime();
        return timeB - timeA;
    }
  });

  const getStageIcon = (stageId: string) => {
    switch (stageId) {
      case "sourced":
        return <Users className="h-4 w-4" />;
      case "applied":
        return <GitBranch className="h-4 w-4" />;
      case "screening":
        return <Filter className="h-4 w-4" />;
      case "interview":
        return <Users className="h-4 w-4" />;
      case "offer":
        return <Mail className="h-4 w-4" />;
      case "hired":
        return <Users className="h-4 w-4" />;
      case "rejected":
        return <Users className="h-4 w-4" />;
      default:
        return <GitBranch className="h-4 w-4" />;
    }
  };

  return (
    <div className="flex-1 min-w-[280px]">
      {/* Stage Header */}
      <div
        className={`${stage.color} rounded-xl p-4 mb-4 transition-all duration-200 ${
          dragOver
            ? "ring-2 ring-primary ring-offset-2 transform scale-105"
            : ""
        }`}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="dark:text-slate-800">{getStageIcon(stage.id)}</div>
            <h3 className="font-semibold text-sm dark:text-slate-800">
              {stage.name}
            </h3>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant="secondary"
              className="text-xs bg-white/20 dark:bg-slate-800/20 dark:text-slate-800 border-white/30 dark:border-slate-800/30"
            >
              {candidates.length}
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 dark:text-slate-800 hover:bg-white/20 dark:hover:bg-slate-800/20"
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="rounded-xl">
                <DropdownMenuLabel>Stage Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() =>
                    onAddCandidate(stage.id as Candidate["status"])
                  }
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Candidate
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onSendBulkEmail(candidates)}>
                  <Mail className="mr-2 h-4 w-4" />
                  Email All ({candidates.length})
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setSortBy("date")}>
                  <SortAsc className="mr-2 h-4 w-4" />
                  Sort by Date
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSortBy("name")}>
                  <SortAsc className="mr-2 h-4 w-4" />
                  Sort by Name
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSortBy("rating")}>
                  <SortAsc className="mr-2 h-4 w-4" />
                  Sort by Rating
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  Configure Stage
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        {stage.description && (
          <p className="text-xs opacity-80 dark:text-slate-800">
            {stage.description}
          </p>
        )}
      </div>

      {/* Drop Zone */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`min-h-[400px] transition-all duration-200 ${
          dragOver
            ? "bg-primary/5 border-2 border-dashed border-primary rounded-xl p-2"
            : ""
        }`}
      >
        <div className="space-y-3 max-h-[calc(100vh-300px)] overflow-y-auto pr-2">
          {sortedCandidates.map((candidate) => (
            <DraggableCandidateCard
              key={candidate.id}
              candidate={candidate}
              onStatusChange={onStatusChange}
              onSendEmail={onSendEmail}
              onViewDetails={onViewDetails}
            />
          ))}

          {/* Empty State */}
          {candidates.length === 0 && (
            <div className="text-center py-12 text-muted-foreground">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-3">
                {getStageIcon(stage.id)}
              </div>
              <p className="text-sm mb-3">No candidates in this stage</p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAddCandidate(stage.id as Candidate["status"])}
                className="text-xs rounded-lg"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Candidate
              </Button>
            </div>
          )}

          {/* Drop indicator when dragging */}
          {dragOver && (
            <div className="border-2 border-dashed border-primary rounded-lg p-4 text-center text-primary text-sm">
              Drop candidate here to move to {stage.name}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
