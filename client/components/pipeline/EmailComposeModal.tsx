import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Mail,
  Send,
  Users,
  FileText,
  Sparkles,
  Clock,
  Calendar,
  Building2,
  X,
} from "lucide-react";
import { Candidate } from "@/data/mockData";
import { toast } from "sonner";

interface EmailComposeModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidates: Candidate[];
  isBulk?: boolean;
  selectedTemplate?: string;
}

const emailTemplates = [
  {
    id: "interview-invitation",
    name: "Interview Invitation",
    subject: "Interview Invitation - {{position}} Position",
    content: `Hi {{candidateName}},

Thank you for your interest in the {{position}} position at HireFlow. We were impressed with your background and would like to invite you for an interview.

Interview Details:
- Date: {{interviewDate}}
- Time: {{interviewTime}}
- Type: {{interviewType}}
- Duration: 1 hour

Please confirm your availability by replying to this email.

Best regards,
{{senderName}}`,
    category: "Interview",
    tags: ["interview", "invitation", "scheduling"],
  },
  {
    id: "status-update",
    name: "Application Status Update",
    subject: "Update on Your Application - {{position}}",
    content: `Hi {{candidateName}},

I wanted to provide you with an update on your application for the {{position}} role.

We have moved you to the next stage of our recruitment process. We're excited about your potential fit for our team and will be in touch soon with next steps.

If you have any questions, please don't hesitate to reach out.

Best regards,
{{senderName}}`,
    category: "Status",
    tags: ["update", "progress", "status"],
  },
  {
    id: "rejection-polite",
    name: "Polite Rejection",
    subject: "Thank you for your interest - {{position}}",
    content: `Hi {{candidateName}},

Thank you for taking the time to apply for the {{position}} position and for interviewing with our team.

After careful consideration, we have decided to move forward with another candidate whose experience more closely aligns with our current needs.

We were impressed with your background and encourage you to apply for future opportunities that match your skills.

Best wishes for your job search.

Best regards,
{{senderName}}`,
    category: "Rejection",
    tags: ["rejection", "polite", "future"],
  },
  {
    id: "offer-letter",
    name: "Job Offer",
    subject: "Job Offer - {{position}} Position",
    content: `Hi {{candidateName}},

We are delighted to extend an offer for the {{position}} position at HireFlow!

Offer Details:
- Position: {{position}}
- Start Date: {{startDate}}
- Salary: {{salary}}
- Benefits: Comprehensive package including health, dental, and vision

Please review the attached offer letter and let us know your decision by {{deadline}}.

Congratulations! We're excited about the possibility of you joining our team.

Best regards,
{{senderName}}`,
    category: "Offer",
    tags: ["offer", "congratulations", "decision"],
  },
  {
    id: "follow-up",
    name: "Follow-up Check-in",
    subject: "Following up on your application - {{position}}",
    content: `Hi {{candidateName}},

I hope this email finds you well. I wanted to follow up on your application for the {{position}} role.

We're currently reviewing applications and wanted to let you know that yours is being given careful consideration. We expect to have updates within the next week.

Thank you for your patience, and we'll be in touch soon.

Best regards,
{{senderName}}`,
    category: "Follow-up",
    tags: ["follow-up", "patience", "consideration"],
  },
];

export const EmailComposeModal = ({
  isOpen,
  onClose,
  candidates,
  isBulk = false,
  selectedTemplate,
}: EmailComposeModalProps) => {
  const [currentTab, setCurrentTab] = useState("compose");
  const [selectedTemplateId, setSelectedTemplateId] = useState(
    selectedTemplate || "",
  );
  const [subject, setSubject] = useState("");
  const [content, setContent] = useState("");
  const [senderName, setSenderName] = useState("HR Team");
  const [scheduleDateTime, setScheduleDateTime] = useState("");
  const [isScheduled, setIsScheduled] = useState(false);

  const handleTemplateSelect = (templateId: string) => {
    const template = emailTemplates.find((t) => t.id === templateId);
    if (template) {
      setSelectedTemplateId(templateId);
      setSubject(template.subject);
      setContent(template.content);
    }
  };

  const handleSendEmail = () => {
    const emailData = {
      recipients: candidates.map((c) => c.email),
      subject,
      content,
      senderName,
      isScheduled,
      scheduleDateTime,
      candidateCount: candidates.length,
    };

    // Simulate sending email
    if (isScheduled) {
      toast.success(
        `Email scheduled for ${new Date(scheduleDateTime).toLocaleString()} to ${candidates.length} ${
          candidates.length === 1 ? "candidate" : "candidates"
        }!`,
      );
    } else {
      toast.success(
        `Email sent to ${candidates.length} ${
          candidates.length === 1 ? "candidate" : "candidates"
        }!`,
      );
    }

    onClose();
  };

  const previewContent = content
    .replace(/{{candidateName}}/g, candidates[0]?.name || "[Candidate Name]")
    .replace(/{{position}}/g, candidates[0]?.position || "[Position]")
    .replace(/{{senderName}}/g, senderName)
    .replace(/{{interviewDate}}/g, "[Interview Date]")
    .replace(/{{interviewTime}}/g, "[Interview Time]")
    .replace(/{{interviewType}}/g, "[Interview Type]")
    .replace(/{{startDate}}/g, "[Start Date]")
    .replace(/{{salary}}/g, "[Salary]")
    .replace(/{{deadline}}/g, "[Deadline]");

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Mail className="w-5 h-5 text-primary" />
            </div>
            {isBulk ? "Send Bulk Email" : "Send Email"}
          </DialogTitle>
          <DialogDescription>
            {isBulk
              ? `Compose and send email to ${candidates.length} candidates`
              : `Send personalized email to ${candidates[0]?.name}`}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={currentTab} onValueChange={setCurrentTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="compose">Compose</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="compose" className="space-y-6">
            {/* Recipients */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Users className="w-4 h-4" />
                  Recipients ({candidates.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {candidates.map((candidate) => (
                    <div
                      key={candidate.id}
                      className="flex items-center gap-2 bg-muted p-2 rounded-lg"
                    >
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={candidate.avatar} />
                        <AvatarFallback className="text-xs">
                          {candidate.initials}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{candidate.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {candidate.email}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Email Form */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sender">From</Label>
                  <Input
                    id="sender"
                    value={senderName}
                    onChange={(e) => setSenderName(e.target.value)}
                    placeholder="Your name"
                    className="rounded-xl"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="template">Quick Template</Label>
                  <Select
                    value={selectedTemplateId}
                    onValueChange={handleTemplateSelect}
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Choose template..." />
                    </SelectTrigger>
                    <SelectContent>
                      {emailTemplates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject">Subject</Label>
                <Input
                  id="subject"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  placeholder="Email subject..."
                  className="rounded-xl"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Message</Label>
                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Type your message..."
                  rows={8}
                  className="rounded-xl resize-none"
                />
              </div>

              {/* Schedule Options */}
              <Card className="border-primary/20 bg-primary/5">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-primary" />
                      <span className="font-medium">Schedule Email</span>
                    </div>
                    <Button
                      variant={isScheduled ? "default" : "outline"}
                      size="sm"
                      onClick={() => setIsScheduled(!isScheduled)}
                      className="rounded-lg"
                    >
                      {isScheduled ? "Scheduled" : "Send Now"}
                    </Button>
                  </div>
                  {isScheduled && (
                    <div className="space-y-2">
                      <Label htmlFor="scheduleDateTime">Send Date & Time</Label>
                      <Input
                        id="scheduleDateTime"
                        type="datetime-local"
                        value={scheduleDateTime}
                        onChange={(e) => setScheduleDateTime(e.target.value)}
                        className="rounded-xl"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="templates" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {emailTemplates.map((template) => (
                <Card
                  key={template.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedTemplateId === template.id
                      ? "ring-2 ring-primary border-primary"
                      : ""
                  }`}
                  onClick={() => handleTemplateSelect(template.id)}
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-base">
                          {template.name}
                        </CardTitle>
                        <Badge variant="secondary" className="mt-1">
                          {template.category}
                        </Badge>
                      </div>
                      {selectedTemplateId === template.id && (
                        <div className="p-1 bg-primary rounded-full">
                          <Sparkles className="w-3 h-3 text-primary-foreground" />
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-2">
                      {template.subject}
                    </p>
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {template.content.split("\n")[0]}...
                    </p>
                    <div className="flex flex-wrap gap-1 mt-3">
                      {template.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Email Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border rounded-lg p-4 bg-muted/50">
                  <div className="space-y-2 border-b pb-3 mb-3">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">From:</span>
                      <span>{senderName}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">To:</span>
                      <span>
                        {candidates.length === 1
                          ? candidates[0].email
                          : `${candidates.length} recipients`}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">Subject:</span>
                      <span>{subject || "[No subject]"}</span>
                    </div>
                    {isScheduled && scheduleDateTime && (
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">Scheduled:</span>
                        <span>
                          {new Date(scheduleDateTime).toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="whitespace-pre-wrap text-sm">
                    {previewContent || "[No content]"}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex gap-3 pt-6 border-t">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1 rounded-xl"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSendEmail}
            className="flex-1 ai-button gap-2"
            disabled={!subject || !content}
          >
            {isScheduled ? (
              <>
                <Calendar className="w-4 h-4" />
                Schedule Email
              </>
            ) : (
              <>
                <Send className="w-4 h-4" />
                Send {isBulk ? `to ${candidates.length}` : "Email"}
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
