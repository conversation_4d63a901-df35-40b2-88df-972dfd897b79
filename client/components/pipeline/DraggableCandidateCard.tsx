import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Star,
  MoreHorizontal,
  ArrowRight,
  X,
  MessageSquare,
  FileText,
  ExternalLink,
} from "lucide-react";
import { Candidate, mockJobs } from "@/data/mockData";

interface DraggableCandidateCardProps {
  candidate: Candidate;
  onStatusChange: (candidateId: string, newStatus: Candidate["status"]) => void;
  onSendEmail: (candidate: Candidate) => void;
  onViewDetails: (candidate: Candidate) => void;
  isDragging?: boolean;
}

export const DraggableCandidateCard = ({
  candidate,
  onStatusChange,
  onSendEmail,
  onViewDetails,
  isDragging = false,
}: DraggableCandidateCardProps) => {
  const [draggedOver, setDraggedOver] = useState(false);

  const getJobTitle = (jobId: string) => {
    const job = mockJobs.find((j) => j.id === jobId);
    return job ? job.title : "Unknown Position";
  };

  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData(
      "text/plain",
      candidate.id?.toString() || candidate.id,
    );
    e.dataTransfer.effectAllowed = "move";
  };

  const handleDragEnd = () => {
    setDraggedOver(false);
  };

  const statusColors = {
    sourced: "bg-gray-500",
    applied: "bg-blue-500",
    screening: "bg-yellow-500",
    interview: "bg-purple-500",
    offer: "bg-orange-500",
    hired: "bg-green-500",
    rejected: "bg-red-500",
  };

  return (
    <Card
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      className={`mb-3 cursor-move hover:shadow-md transition-all duration-200 group relative overflow-hidden ${
        isDragging ? "opacity-50 transform rotate-2 scale-95" : ""
      } ${draggedOver ? "ring-2 ring-primary" : ""}`}
    >
      {/* Status indicator */}
      <div
        className={`absolute top-0 left-0 right-0 h-1 ${statusColors[candidate.status]}`}
      />

      {/* AI Enhancement Indicator */}
      {candidate.rating && candidate.rating >= 4.5 && (
        <div className="absolute top-2 right-2 z-10">
          <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
        </div>
      )}

      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2 flex-1">
            <Avatar className="h-8 w-8">
              <AvatarImage src={candidate.avatar} />
              <AvatarFallback className="text-xs bg-primary/10 text-primary">
                {candidate.initials ||
                  candidate.name?.slice(0, 2)?.toUpperCase() ||
                  "??"}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <CardTitle
                className="text-sm cursor-pointer hover:text-primary transition-colors"
                onClick={() => onViewDetails(candidate)}
              >
                {candidate.name}
              </CardTitle>
              <p className="text-xs text-muted-foreground truncate">
                {candidate.position}
              </p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-6 w-6 p-0">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="rounded-xl">
              <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => {
                  setTimeout(() => {
                    onViewDetails(candidate);
                  }, 100);
                }}
              >
                <User className="mr-2 h-4 w-4" />
                View Profile
              </DropdownMenuItem>

              <DropdownMenuItem
                className="text-red-600"
                onClick={() =>
                  onStatusChange(
                    candidate.id?.toString() || candidate.id,
                    "rejected",
                  )
                }
              >
                <X className="mr-2 h-4 w-4" />
                Reject
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-2">
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <MapPin className="h-3 w-3" />
          <span className="truncate">{candidate.location}</span>
        </div>

        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Calendar className="h-3 w-3" />
          <span>
            Applied{" "}
            {candidate.appliedDate && candidate.appliedDate.trim() !== ""
              ? (() => {
                  const date = new Date(candidate.appliedDate);
                  return isNaN(date.getTime())
                    ? "Recently"
                    : date.toLocaleDateString();
                })()
              : "Recently"}
          </span>
        </div>

        {candidate.rating && (
          <div className="flex items-center gap-1">
            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            <span className="text-xs font-medium">{candidate.rating}</span>
          </div>
        )}

        <div className="flex flex-wrap gap-1">
          {candidate.skills && candidate.skills.length > 0 ? (
            <>
              {candidate.skills.slice(0, 2).map((skill) => (
                <Badge
                  key={skill}
                  variant="secondary"
                  className="text-xs px-1 py-0"
                >
                  {skill}
                </Badge>
              ))}
              {candidate.skills.length > 2 && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  +{candidate.skills.length - 2}
                </Badge>
              )}
            </>
          ) : (
            <Badge variant="secondary" className="text-xs px-1 py-0">
              No skills listed
            </Badge>
          )}
        </div>

        <div className="text-xs text-muted-foreground truncate">
          {getJobTitle(candidate.jobId || "")}
        </div>

        {/* Quick Actions */}
        <div className="flex gap-1 pt-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            size="sm"
            variant="outline"
            className="h-6 text-xs flex-1 rounded-md"
            onClick={() => onSendEmail(candidate)}
          >
            <Mail className="h-3 w-3 mr-1" />
            Email
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="h-6 text-xs flex-1 rounded-md"
          >
            <Calendar className="h-3 w-3 mr-1" />
            Schedule
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
