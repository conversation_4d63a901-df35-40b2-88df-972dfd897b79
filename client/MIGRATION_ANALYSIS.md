# Legacy Codebase Migration Analysis

## 📊 Current Legacy Structure Analysis

### Discovered Legacy Components

#### Candidates Domain Components
- `components/candidates/CandidateDetailModal.tsx` - Candidate detail modal
- `components/candidates/CandidateForm.tsx` - Candidate form component
- `components/candidates/CandidateModal.tsx` - Generic candidate modal
- `components/candidates/CandidateTable.tsx` - Candidate listing table
- `components/candidates/detail/` - Candidate detail components
- `components/candidates/AIScoreBadge.tsx` - AI score display
- `components/candidates/AICandidateSummary.tsx` - AI-generated summary
- `components/candidates/CandidateActivityTimeline.tsx` - Activity timeline
- `components/candidates/AdvancedFilters.tsx` - Advanced filtering
- `components/candidates/BulkActionsBar.tsx` - Bulk operations

#### Jobs Domain Components
- `components/jobs/JobCard.tsx` - Job card component
- `components/jobs/JobForm.tsx` - Job creation/editing form
- `components/jobs/JobModal.tsx` - Job modal dialogs
- `components/jobs/JobTable.tsx` - Job listing table
- `components/jobs/detail/` - Job detail components

#### Interviews Domain Components
- `components/interviews/InterviewCard.tsx` - Interview card
- `components/interviews/InterviewForm.tsx` - Interview scheduling form
- `components/interviews/InterviewModal.tsx` - Interview modals
- `components/interviews/InterviewTable.tsx` - Interview listing
- `components/interviews/detail/` - Interview detail components

#### Calendar Domain Components
- `components/calendar/CalendarView.tsx` - Calendar display
- `components/calendar/SchedulingModal.tsx` - Scheduling interface

#### Analytics Domain Components
- `components/analytics/Dashboard.tsx` - Analytics dashboard
- `components/analytics/ReportsView.tsx` - Reports interface

#### Shared Components (Already in correct location)
- `components/ui/` - UI primitives (buttons, inputs, etc.)
- `components/layout/` - Layout components

### Legacy Hooks Analysis
- `hooks/useApi.ts` - Generic API hook (needs refactoring to domain services)
- `hooks/useCandidates.ts` - Candidate-specific hooks
- `hooks/useJobs.ts` - Job-specific hooks
- `hooks/useInterviews.ts` - Interview-specific hooks
- `hooks/useAnalytics.ts` - Analytics hooks

### Legacy Services Analysis
- `lib/api.ts` - Generic API client (needs migration to core/api)
- `lib/candidateService.ts` - Candidate API service
- `lib/jobService.ts` - Job API service
- `lib/interviewService.ts` - Interview API service

### Legacy Types Analysis
- `data/mockData.ts` - Contains legacy type definitions
- `lib/types/` - Various type definitions that need domain migration

## 🎯 Migration Strategy

### Phase 1: Type Migration
1. Extract types from `data/mockData.ts`
2. Migrate types from `lib/types/` to appropriate domains
3. Update type definitions to match new architecture

### Phase 2: Service Migration
1. Migrate `lib/api.ts` to `core/api/ApiClient.ts` (already done)
2. Refactor domain services to extend BaseService
3. Update service interfaces

### Phase 3: Hook Migration
1. Move domain-specific hooks to respective domains
2. Refactor to use new service layer
3. Update hook interfaces

### Phase 4: Component Migration
1. Move components to appropriate domains
2. Refactor large components into smaller, focused ones
3. Apply compound component patterns
4. Update import statements

### Phase 5: Integration & Testing
1. Update all import paths
2. Migrate test files
3. Validate functionality
4. Update documentation

## 📋 Migration Checklist

### Candidates Domain
- [ ] Migrate CandidateDetailModal → domains/candidates/components/
- [ ] Migrate CandidateForm → domains/candidates/components/
- [ ] Migrate CandidateModal → domains/candidates/components/
- [ ] Migrate CandidateTable → domains/candidates/components/ (already done)
- [ ] Migrate detail components → domains/candidates/components/detail/
- [ ] Migrate utility components (AIScoreBadge, etc.)
- [ ] Migrate candidate hooks → domains/candidates/hooks/
- [ ] Migrate candidate services → domains/candidates/services/
- [ ] Update candidate types → domains/candidates/types/

### Jobs Domain
- [ ] Migrate JobCard → domains/jobs/components/
- [ ] Migrate JobForm → domains/jobs/components/
- [ ] Migrate JobModal → domains/jobs/components/
- [ ] Migrate JobTable → domains/jobs/components/
- [ ] Migrate detail components → domains/jobs/components/detail/
- [ ] Migrate job hooks → domains/jobs/hooks/
- [ ] Migrate job services → domains/jobs/services/
- [ ] Update job types → domains/jobs/types/

### Interviews Domain
- [ ] Migrate interview components → domains/interviews/components/
- [ ] Migrate interview hooks → domains/interviews/hooks/
- [ ] Migrate interview services → domains/interviews/services/
- [ ] Update interview types → domains/interviews/types/

### Calendar Domain
- [ ] Migrate calendar components → domains/calendar/components/
- [ ] Migrate calendar hooks → domains/calendar/hooks/
- [ ] Migrate calendar services → domains/calendar/services/
- [ ] Create calendar types → domains/calendar/types/

### Analytics Domain
- [ ] Migrate analytics components → domains/analytics/components/
- [ ] Migrate analytics hooks → domains/analytics/hooks/
- [ ] Migrate analytics services → domains/analytics/services/
- [ ] Create analytics types → domains/analytics/types/

## 🔍 Dependencies Analysis

### Cross-Domain Dependencies
- Candidates ↔ Jobs: Candidate applications to jobs
- Candidates ↔ Interviews: Interview scheduling for candidates
- Interviews ↔ Calendar: Calendar integration for scheduling
- All domains → Analytics: Data for reporting

### Shared Dependencies
- All domains use shared UI components
- All domains use core API infrastructure
- All domains use shared validation utilities

## ⚠️ Migration Risks & Mitigation

### Risks
1. **Breaking Changes**: Import path changes may break existing functionality
2. **Circular Dependencies**: Cross-domain imports may create cycles
3. **Test Coverage**: Tests may break during migration
4. **Performance**: Bundle size may increase temporarily

### Mitigation Strategies
1. **Gradual Migration**: Migrate one domain at a time
2. **Backward Compatibility**: Keep legacy imports during transition
3. **Comprehensive Testing**: Update tests alongside migration
4. **Bundle Analysis**: Monitor bundle size changes

## 📈 Expected Benefits

### Post-Migration Benefits
1. **Better Organization**: Clear domain boundaries
2. **Improved Maintainability**: Easier to find and modify code
3. **Enhanced Testing**: Domain-specific test strategies
4. **Better Performance**: Code splitting by domain
5. **Clearer Dependencies**: Explicit import relationships
6. **Easier Onboarding**: New developers can focus on specific domains

### Metrics to Track
- Bundle size changes
- Build time improvements
- Developer productivity metrics
- Code maintainability scores
