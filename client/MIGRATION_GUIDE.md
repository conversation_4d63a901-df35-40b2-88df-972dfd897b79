# Migration Guide: Legacy to Domain-Driven Architecture

This guide helps you migrate from the legacy structure to the new domain-driven architecture.

## 🎯 Overview

We're transitioning from a feature-based structure to a domain-driven design (DDD) that better reflects business domains and improves maintainability.

### Before (Legacy)
```
src/
├── components/
│   ├── candidates/
│   ├── jobs/
│   └── shared/
├── hooks/
├── services/
├── types/
└── utils/
```

### After (Domain-Driven)
```
client/
├── domains/
│   ├── candidates/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── types/
│   │   └── utils/
│   └── jobs/
├── shared/
│   ├── components/
│   └── utils/
└── core/
```

## 🚀 Migration Process

### Phase 1: Preparation (Week 1)
1. **Set up new structure** ✅
2. **Create migration tools** ✅
3. **Implement base services** ✅
4. **Create shared components** ✅

### Phase 2: Domain Migration (Weeks 2-7)
1. **Candidates Domain** ✅
2. **Jobs Domain** ✅
3. **Interviews Domain** ✅
4. **Calendar Domain** ✅
5. **Analytics Domain** ✅

### Phase 3: Integration (Weeks 8-10)
1. **Component Integration** ✅
2. **Testing Setup** ✅
3. **Documentation** ✅

## 🛠️ Migration Tools

### Automated Migration Scripts

#### 1. Component Migration
```bash
npm run migrate:component -- --component=CandidateList --domain=candidates
```

This script:
- Moves component files to the correct domain
- Updates import statements
- Creates barrel exports
- Moves associated test files

#### 2. Import Updates
```bash
npm run migrate:imports
```

This script:
- Scans all files for import statements
- Updates paths to new domain structure
- Fixes relative imports
- Updates test imports

#### 3. Validation
```bash
npm run migrate:validate
```

This script:
- Checks for broken imports
- Validates file structure
- Reports missing files
- Suggests fixes

### Manual Migration Steps

#### Step 1: Identify Domain
Determine which domain your component belongs to:
- **Candidates**: Candidate management, profiles, applications
- **Jobs**: Job postings, requirements, management
- **Interviews**: Scheduling, feedback, evaluation
- **Calendar**: Calendar integration, availability
- **Analytics**: Reports, metrics, dashboards

#### Step 2: Move Component
```bash
# Example: Moving CandidateCard
mkdir -p domains/candidates/components
mv src/components/candidates/CandidateCard.tsx domains/candidates/components/
mv src/components/candidates/CandidateCard.test.tsx __tests__/domains/candidates/components/
```

#### Step 3: Update Imports
```typescript
// Before
import { CandidateCard } from '../../../components/candidates/CandidateCard';

// After
import { CandidateCard } from '@/domains/candidates/components';
```

#### Step 4: Update Barrel Exports
```typescript
// domains/candidates/components/index.ts
export * from './CandidateCard';
export * from './CandidateTable';
export * from './CandidateForm';
```

## 📋 Migration Checklist

### For Each Component
- [ ] Move to correct domain folder
- [ ] Update import statements
- [ ] Add to barrel exports
- [ ] Move test files
- [ ] Update test imports
- [ ] Verify functionality

### For Each Domain
- [ ] Create folder structure
- [ ] Set up barrel exports
- [ ] Migrate components
- [ ] Migrate hooks
- [ ] Migrate services
- [ ] Migrate types
- [ ] Update documentation

## 🔍 Common Migration Patterns

### 1. Component Migration
```typescript
// Before: src/components/candidates/CandidateCard.tsx
import { Candidate } from '../../types/candidate';
import { Button } from '../ui/Button';

// After: domains/candidates/components/CandidateCard.tsx
import { Candidate } from '../types';
import { Button } from '@/shared/components/ui';
```

### 2. Hook Migration
```typescript
// Before: src/hooks/useCandidates.ts
import { candidateService } from '../services/candidateService';

// After: domains/candidates/hooks/useCandidates.ts
import { CandidateApiService } from '../services';
```

### 3. Service Migration
```typescript
// Before: src/services/candidateService.ts
import { ApiClient } from './apiClient';

// After: domains/candidates/services/CandidateApiService.ts
import { BaseService } from '@/core/api';
```

## 🚨 Common Issues and Solutions

### Issue 1: Circular Dependencies
**Problem**: Domain A imports from Domain B, which imports from Domain A.

**Solution**: 
- Move shared logic to `shared/` or `core/`
- Use dependency injection
- Create domain events for communication

### Issue 2: Shared Components in Wrong Domain
**Problem**: Component used by multiple domains is in a specific domain.

**Solution**:
- Move to `shared/components/`
- Create domain-specific wrappers if needed
- Use composition over inheritance

### Issue 3: Import Path Confusion
**Problem**: Complex relative import paths.

**Solution**:
- Use absolute imports with `@/` prefix
- Set up proper TypeScript path mapping
- Use barrel exports consistently

### Issue 4: Test File Organization
**Problem**: Test files scattered across different locations.

**Solution**:
- Mirror domain structure in `__tests__/`
- Use consistent naming conventions
- Group related tests together

## 📊 Migration Progress Tracking

### Completed Domains ✅
- [x] **Core Infrastructure** - API client, base services, providers
- [x] **Shared Components** - UI components, forms, layouts, modals
- [x] **Candidates Domain** - Complete migration with components, hooks, services
- [x] **Jobs Domain** - Types and services migrated
- [x] **Interviews Domain** - Types and structure created
- [x] **Calendar Domain** - Structure created
- [x] **Analytics Domain** - Structure created

### Remaining Work
- [ ] Complete Jobs domain components and hooks
- [ ] Complete Interviews domain implementation
- [ ] Complete Calendar domain implementation
- [ ] Complete Analytics domain implementation
- [ ] Legacy code cleanup
- [ ] Performance optimization

## 🎯 Best Practices

### Domain Boundaries
- Keep domains independent
- Use events for cross-domain communication
- Share common logic through `shared/` or `core/`

### Import Organization
```typescript
// 1. External libraries
import React from 'react';
import { useQuery } from '@tanstack/react-query';

// 2. Core/shared imports
import { ApiClient } from '@/core/api';
import { Button } from '@/shared/components/ui';

// 3. Domain imports (relative)
import { CandidateService } from '../services';
import { Candidate } from '../types';
```

### File Naming
- Use PascalCase for components: `CandidateCard.tsx`
- Use camelCase for hooks: `useCandidates.ts`
- Use PascalCase for services: `CandidateApiService.ts`
- Use camelCase for utilities: `candidateUtils.ts`

### Testing Strategy
- Mirror domain structure in tests
- Test domain logic independently
- Use integration tests for cross-domain features
- Mock external dependencies

## 🔄 Rollback Strategy

If issues arise during migration:

1. **Immediate Rollback**
   ```bash
   git revert <migration-commit>
   ```

2. **Partial Rollback**
   - Revert specific domain migrations
   - Keep shared components
   - Maintain core infrastructure

3. **Gradual Migration**
   - Migrate one domain at a time
   - Keep legacy and new structure in parallel
   - Use feature flags for gradual rollout

## 📞 Support

For migration issues:
1. Check this guide first
2. Run validation scripts
3. Check existing domain implementations
4. Ask team for help with complex cases

## 🎉 Post-Migration Benefits

After completing the migration:
- **Better Organization**: Clear domain boundaries
- **Improved Maintainability**: Easier to find and modify code
- **Enhanced Testing**: Domain-specific test strategies
- **Better Performance**: Code splitting by domain
- **Clearer Dependencies**: Explicit import relationships
- **Easier Onboarding**: New developers can focus on specific domains
