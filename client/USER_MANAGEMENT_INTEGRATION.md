# User Management API Integration

## Overview

This document describes the comprehensive integration of User Management API into the HireFlow ATS frontend. The implementation provides full CRUD operations for user management with role-based access control, Vietnamese localization, and modern UI/UX.

## Implementation Summary

### 🔧 **API Integration**

1. **API Service Methods** (`client/lib/api.ts`)
   - `getUsers()` - List users with pagination and filtering
   - `createUser()` - Create new user
   - `getUser()` - Get user details
   - `updateUser()` - Update user information
   - `updateUserRole()` - Update user role specifically
   - `deactivateUser()` - Deactivate/activate user
   - `getUserRoles()` - Get available roles
   - `getUserStatistics()` - Get user statistics

2. **React Query Hooks** (`client/hooks/useUserManagement.ts`)
   - `useUsers()` - List users with caching
   - `useUser()` - Get single user
   - `useUserRoles()` - Get available roles
   - `useUserStatistics()` - Get statistics
   - `useCreateUser()` - Create user mutation
   - `useUpdateUser()` - Update user mutation
   - `useUpdateUserRole()` - Update role mutation
   - `useDeactivateUser()` - Deactivate user mutation

### 🎯 **TypeScript Types** (`client/lib/types/userManagement.ts`)

```typescript
interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  role_display_name: string;
  department: string;
  title: string;
  phone: string;
  avatar?: string;
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
  roles?: Role[];
  permissions?: string[];
  initials: string;
  is_interviewer: boolean;
  statistics?: UserStatistics;
}
```

### 🎨 **Components**

#### 1. **TeamManagement** (`client/pages/TeamManagement.tsx`)
- Main page component with comprehensive user management
- Real-time filtering and search
- Pagination support
- Role-based UI rendering
- Statistics dashboard
- Export functionality

#### 2. **UserForm** (`client/components/team/UserForm.tsx`)
- Modal form for creating/editing users
- Validation with Vietnamese error messages
- Role-based field restrictions
- Password handling for new/existing users
- Department and role selection

#### 3. **UserDetailModal** (`client/components/team/UserDetailModal.tsx`)
- Detailed user information display
- Activity statistics
- Permissions overview
- Role-based action buttons
- Professional UI layout

### 🔐 **Security Implementation**

#### Role-Based Access Control
```typescript
// Admin-only features
const isAdmin = authUser?.role === "admin";

// Self-edit permissions
const isEditingSelf = mode === "edit" && user?.id === authUser?.id;

// Field-level permissions
const canEditField = (field: string): boolean => {
  if (isAdmin) return true;
  if (!isEditingSelf) return false;
  return ["name", "phone", "password", "password_confirmation"].includes(field);
};
```

#### API Error Handling
- 401 (Unauthorized) - Redirect to login
- 403 (Forbidden) - Show permission denied message
- 422 (Validation) - Show field-specific errors
- 500 (Server Error) - Show generic error with retry option

### 🌍 **Vietnamese Localization**

#### Complete Vietnamese Translation Support
- All UI text in Vietnamese
- Error messages in Vietnamese
- Role names in Vietnamese
- Form validation messages
- Status indicators
- Success/error notifications

#### Role Display Names
```typescript
const ROLE_DISPLAY_NAMES = {
  admin: 'Quản trị viên',
  recruiter: 'Nhà tuyển dụng',
  hiring_manager: 'Quản lý tuyển dụng',
  interviewer: 'Người phỏng vấn',
};
```

### 📊 **Features Implemented**

#### 1. **User List Management**
- ✅ Paginated user list
- ✅ Advanced filtering (role, department, status, name, email)
- ✅ Sorting options (name, email, role, department, dates)
- ✅ Search functionality
- ✅ Real-time updates
- ✅ Export functionality

#### 2. **User Creation** (Admin Only)
- ✅ Comprehensive form validation
- ✅ Role and department selection
- ✅ Password requirements
- ✅ Vietnamese error messages
- ✅ Real-time validation feedback

#### 3. **User Updates**
- ✅ Admin can update all users
- ✅ Users can update own profile (limited fields)
- ✅ Role updates with separate API call
- ✅ Password change functionality
- ✅ Account activation/deactivation

#### 4. **User Details**
- ✅ Comprehensive user information display
- ✅ Activity statistics
- ✅ Permission overview
- ✅ Contact information
- ✅ Account status and history

#### 5. **Security & Permissions**
- ✅ Role-based UI rendering
- ✅ API endpoint protection
- ✅ Field-level edit permissions
- ✅ Self-edit restrictions
- ✅ Admin-only functions

### 🎛️ **UI/UX Features**

#### Modern Design System
- Consistent with existing HireFlow design
- Professional color scheme
- Smooth animations and transitions
- Responsive layout for mobile/desktop
- Loading states and skeletons

#### User Experience
- Intuitive navigation
- Clear visual hierarchy
- Helpful tooltips and labels
- Error prevention and recovery
- Success feedback

#### Interactive Elements
- Real-time search and filtering
- Pagination controls
- Action dropdowns
- Modal forms
- Confirmation dialogs

### 📈 **Statistics Dashboard**

#### User Statistics Display
- Total users count
- Active/inactive breakdown
- Role distribution
- Recent login metrics
- Never logged in users

#### Visual Cards
```typescript
// Statistics cards with color coding
<Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
  <CardContent className="p-4">
    <div className="flex items-center gap-3">
      <div className="p-2 bg-primary/20 rounded-xl">
        <Users className="h-4 w-4 text-primary" />
      </div>
      <div>
        <p className="text-lg font-bold">{statistics.total_users}</p>
        <p className="text-xs text-muted-foreground">Tổng số</p>
      </div>
    </div>
  </CardContent>
</Card>
```

### ⚙️ **Configuration**

#### API Endpoints
```typescript
const endpoints = {
  list: 'GET /api/v1/users',
  create: 'POST /api/v1/users',
  read: 'GET /api/v1/users/{id}',
  update: 'PUT /api/v1/users/{id}',
  updateRole: 'PUT /api/v1/users/{id}/roles',
  deactivate: 'DELETE /api/v1/users/{id}',
  roles: 'GET /api/v1/users/roles',
  statistics: 'GET /api/v1/users/statistics',
};
```

#### Query Parameters
- `page` - Page number
- `per_page` - Items per page (max 50)
- `filter[role]` - Filter by role
- `filter[department]` - Filter by department
- `filter[is_active]` - Filter by status
- `filter[name]` - Search by name
- `filter[email]` - Search by email
- `sort` - Sort field
- `include` - Include relationships

### 🔄 **State Management**

#### React Query Integration
- Automatic caching and invalidation
- Background refetching
- Optimistic updates
- Error boundary handling
- Loading state management

#### Local State
```typescript
const [userListParams, setUserListParams] = useState<UserListParams>({
  page: 1,
  per_page: 15,
  include: "roles,permissions",
});
```

### 🧪 **Error Handling**

#### Comprehensive Error Scenarios
- Network connectivity issues
- Authentication failures
- Authorization errors
- Validation failures
- Server errors
- Timeout handling

#### User-Friendly Error Messages
```typescript
const errorMessages = {
  unauthorized: "Bạn cần đăng nhập để truy cập tài nguyên này",
  forbidden: "Chỉ quản trị viên mới có quyền truy cập tài nguyên này",
  validation: "Dữ liệu không hợp lệ",
  network: "Lỗi kết nối mạng",
  server: "Lỗi máy chủ nội bộ",
};
```

### 📱 **Responsive Design**

#### Mobile-First Approach
- Responsive table layouts
- Mobile-friendly forms
- Touch-optimized interactions
- Collapsible filters
- Swipe gestures

#### Breakpoint Strategy
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

### 🚀 **Performance Optimizations**

#### Frontend Optimizations
- React Query caching
- Debounced search input
- Optimistic updates
- Lazy loading
- Memoized components

#### API Optimizations
- Pagination for large datasets
- Selective field inclusion
- Efficient filtering
- Proper indexing support
- Response compression

### 🔧 **Development Tools**

#### TypeScript Support
- Strict type checking
- Interface definitions
- Enum constants
- Type guards
- Generic utilities

#### Development Experience
- Hot reload support
- Error boundaries
- Console logging
- Debug information
- Performance monitoring

### 📋 **Testing Strategy**

#### Component Testing
- Unit tests for hooks
- Component integration tests
- Mock API responses
- Error scenario testing
- Accessibility testing

#### E2E Testing
- User workflow testing
- Permission verification
- Form validation testing
- Error handling verification
- Performance testing

### 🔮 **Future Enhancements**

#### Planned Features
1. **Advanced Permissions**
   - Granular permission management
   - Custom role creation
   - Department-specific permissions
   - Time-based access control

2. **Enhanced UI/UX**
   - Bulk user operations
   - Advanced search filters
   - User import/export
   - Activity audit logs

3. **Integration Features**
   - LDAP/AD integration
   - SSO support
   - API key management
   - Webhook notifications

4. **Analytics & Reporting**
   - User activity reports
   - Login analytics
   - Permission usage statistics
   - Security audit reports

### 📚 **Usage Examples**

#### Creating a New User
```typescript
const createUser = useCreateUser();

const handleCreateUser = async (userData: CreateUserData) => {
  try {
    await createUser.mutateAsync(userData);
    toast.success("Người dùng đã được tạo thành công!");
  } catch (error) {
    // Error handling done in hook
  }
};
```

#### Filtering Users
```typescript
const [filters, setFilters] = useState({
  role: "recruiter",
  department: "HR",
  is_active: true,
});

const { data: users } = useUsers({
  filter: filters,
  sort: "name",
  per_page: 20,
});
```

#### Role-Based UI Rendering
```typescript
const isAdmin = authUser?.role === "admin";

return (
  <div>
    {isAdmin && (
      <Button onClick={handleCreateUser}>
        Thêm người dùng
      </Button>
    )}
  </div>
);
```

## Conclusion

The User Management API integration provides a complete, secure, and user-friendly solution for managing users in the HireFlow ATS system. With comprehensive role-based access control, Vietnamese localization, and modern UI/UX, it delivers a professional user management experience while maintaining security and performance standards.

The implementation follows React and TypeScript best practices, provides excellent error handling, and includes comprehensive documentation for future maintenance and enhancements.
