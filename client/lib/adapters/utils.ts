// Shared utility functions for data adapters

export function getInitials(name: string): string {
  if (!name) return "";
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
}

export function formatSalary(
  min?: number,
  max?: number,
  currency = "VND",
): string {
  if (!min && !max) return "";

  const formatNumber = (num: number) => {
    if (currency === "VND") {
      return `${(num / 1000000).toFixed(0)} triệu VND`;
    }
    return `$${(num / 1000).toFixed(0)}K`;
  };

  if (min && max) {
    return `${formatNumber(min)}-${formatNumber(max)}`;
  }
  return formatNumber(min || max || 0);
}

export function calculateMockAiScore(candidate: any): number {
  let score = 60;
  if (candidate.skills?.length)
    score += Math.min(25, candidate.skills.length * 3);
  if (candidate.experience) score += 15;
  if (candidate.education?.length) score += 10;
  return Math.min(100, Math.max(0, score + Math.floor(Math.random() * 10) - 5));
}

export function getStageColor(stage: string): string {
  const colors: Record<string, string> = {
    sourced: "bg-blue-500",
    applied: "bg-primary",
    screening: "bg-warning",
    interview: "bg-purple-500",
    offer: "bg-orange-500",
    hired: "bg-success",
    rejected: "bg-gray-500",
  };
  return colors[stage] || "bg-gray-500";
}

// Pagination interface
export interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Common pagination adapter
export function adaptPagination(apiResponse: any): PaginationData {
  return {
    currentPage: apiResponse?.meta?.current_page || 1,
    totalPages: apiResponse?.meta?.last_page || 1,
    totalItems: apiResponse?.meta?.total || 0,
    itemsPerPage: apiResponse?.meta?.per_page || 10,
    hasNext: !!apiResponse?.links?.next,
    hasPrev: !!apiResponse?.links?.prev,
  };
}

// Type guards for data validation
export function isValidArray(value: unknown): value is Array<any> {
  return Array.isArray(value);
}

export function isValidString(value: unknown): value is string {
  return typeof value === "string";
}

export function isValidNumber(value: unknown): value is number {
  return typeof value === "number" && !isNaN(value);
}

// Safe data extraction helpers
export function safeString(value: unknown, defaultValue = ""): string {
  if (isValidString(value)) return value;
  if (isValidNumber(value)) return value.toString();
  return defaultValue;
}

export function safeNumber(value: unknown, defaultValue = 0): number {
  return isValidNumber(value) ? value : defaultValue;
}

export function safeArray(
  value: unknown,
  defaultValue: Array<any> = [],
): Array<any> {
  return isValidArray(value) ? value : defaultValue;
}

// Date formatting helper
export function formatApiDate(dateString?: string): string {
  if (!dateString) return "";
  try {
    return dateString.substring(0, 10); // Extract YYYY-MM-DD from ISO string
  } catch {
    return "";
  }
}
