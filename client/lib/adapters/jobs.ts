// Job data adapters for API v2.0.1

import {
  ApiJob,
  UiJob,
  JobListResponse,
  PaginatedApiResponse,
  JobFilters,
} from "./types";
import {
  adaptPagination,
  safeString,
  safeNumber,
  safeArray,
  formatApiDate,
} from "./utils";

export const jobAdapters = {
  // Convert API job to UI format
  fromApi: (apiJob: ApiJob): UiJob => ({
    id: apiJob.id.toString(),
    title: safeString(apiJob.title),
    department: safeString(apiJob.department),
    location: safeString(apiJob.location),
    type: safeString(apiJob.type, "full-time"),
    workLocation: safeString(apiJob.work_location, "hybrid"),
    salary: apiJob.salary,
    description: safeString(apiJob.description),
    requirements: safeArray(apiJob.requirements),
    responsibilities: safeArray(apiJob.responsibilities),
    benefits: safeArray(apiJob.benefits),
    skills: safeArray(apiJob.skills),
    status: safeString(apiJob.status, "draft"),
    postedDate: formatApiDate(apiJob.posted_date || apiJob.created_at),
    closingDate: formatApiDate(apiJob.closing_date),
    applicantCount: safeNumber(apiJob.applicant_count),
    viewCount: safeNumber(apiJob.view_count),
    hiringManager:
      apiJob.hiring_manager?.name || safeString(apiJob.hiring_manager_name),
    hiringManagerId: safeNumber(apiJob.hiring_manager_id),
    recruiter: apiJob.recruiter?.name || safeString(apiJob.recruiter_name),
    recruiterId: safeNumber(apiJob.recruiter_id),
    priority: safeString(apiJob.priority, "medium"),
    experienceLevel: safeString(apiJob.experience_level),
    educationRequired: safeString(apiJob.education_required),
    companyCulture: safeString(apiJob.company_culture),
    positions: safeNumber(apiJob.positions, 1),
  }),

  // Convert UI job to API format
  toApi: (uiJob: Partial<UiJob>): Partial<ApiJob> => ({
    title: safeString(uiJob.title),
    department: safeString(uiJob.department),
    location: safeString(uiJob.location),
    type: safeString(uiJob.type, "full-time"),
    work_location: safeString(uiJob.workLocation, "hybrid"),
    salary_min: uiJob.salary?.min,
    salary_max: uiJob.salary?.max,
    currency: safeString(uiJob.salary?.currency, "VND"),
    description: safeString(uiJob.description),
    education_required: safeString(uiJob.educationRequired),
    company_culture: safeString(uiJob.companyCulture),
    status: safeString(uiJob.status, "draft"),
    priority: safeString(uiJob.priority, "medium"),
    experience_level: safeString(uiJob.experienceLevel),
    hiring_manager_id: safeNumber(uiJob.hiringManagerId),
    recruiter_id: safeNumber(uiJob.recruiterId),
    closing_date: safeString(uiJob.closingDate),
    positions: safeNumber(uiJob.positions, 1),
    requirements: safeArray(uiJob.requirements),
    responsibilities: safeArray(uiJob.responsibilities),
    benefits: safeArray(uiJob.benefits),
    skills: safeArray(uiJob.skills),
  }),

  // Convert paginated API response to UI format
  fromPaginatedApi: (
    apiResponse: PaginatedApiResponse<ApiJob>,
  ): JobListResponse => ({
    jobs: safeArray(apiResponse?.data).map(jobAdapters.fromApi),
    pagination: adaptPagination(apiResponse),
  }),
};

// Filter adapters for API query parameters
export const jobFilterAdapters = {
  // Convert UI filters to API parameters (v2.0.1: Direct parameter mapping)
  toApiParams: (uiFilters: JobFilters): Record<string, any> => {
    const apiParams: Record<string, any> = {};

    if (uiFilters.search) apiParams["search"] = uiFilters.search;
    if (uiFilters.status) apiParams["status"] = uiFilters.status;
    if (uiFilters.department) apiParams["department"] = uiFilters.department;
    if (uiFilters.location) apiParams["location"] = uiFilters.location;
    if (uiFilters.type) apiParams["type"] = uiFilters.type;
    if (uiFilters.workLocation)
      apiParams["work_location"] = uiFilters.workLocation;
    if (uiFilters.experienceLevel)
      apiParams["experience_level"] = uiFilters.experienceLevel;
    if (uiFilters.priority) apiParams["priority"] = uiFilters.priority;
    if (uiFilters.hiringManagerId)
      apiParams["hiring_manager_id"] = parseInt(uiFilters.hiringManagerId);
    if (uiFilters.recruiterId)
      apiParams["recruiter_id"] = parseInt(uiFilters.recruiterId);

    // v2.0.1: Skills as arrays
    if (safeArray(uiFilters.skills).length > 0) {
      apiParams["skills"] = uiFilters.skills;
    }

    // Sorting parameters
    if (uiFilters.sort) apiParams["sort"] = uiFilters.sort;
    if (uiFilters.direction) apiParams["direction"] = uiFilters.direction;

    return apiParams;
  },
};

export default jobAdapters;
