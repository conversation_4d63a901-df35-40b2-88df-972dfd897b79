// Dashboard data adapters for API v2.0.1

import { ApiDashboardData, UiDashboardData } from "./types";
import {
  getInitials,
  getStageColor,
  safeString,
  safeNumber,
  safeArray,
  formatApiDate,
} from "./utils";

export const dashboardAdapters = {
  // Convert API dashboard data to UI format
  fromApi: (apiData: ApiDashboardData): UiDashboardData => ({
    overview: {
      totalCandidates: safeNumber(apiData?.summary_cards?.total_candidates),
      newApplications: safeNumber(
        apiData?.summary_cards?.new_candidates_this_month,
      ),
      scheduledInterviews: safeNumber(
        apiData?.summary_cards?.interviews_this_week,
      ),
      successfulHires: safeNumber(apiData?.summary_cards?.total_hires),
      trends: {
        candidatesGrowth: 12.5, // API doesn't provide this yet
        applicationsGrowth: 8.3,
        interviewsGrowth: -2.1,
        hiresGrowth: 15.6,
      },
    },
    recentActivity: safeArray(apiData?.recent_activities).map((activity) => ({
      id: safeString(activity.id),
      type: safeString(activity.type),
      candidateId: activity.candidate_id
        ? activity.candidate_id.toString()
        : undefined,
      candidateName: safeString(activity.candidate_name),
      candidateInitials: getInitials(activity.candidate_name || ""),
      candidateAvatar: safeString(activity.candidate_avatar),
      jobTitle: safeString(activity.job_title),
      timestamp: formatApiDate(activity.created_at || activity.timestamp),
      description: safeString(activity.description),
      performer: safeString(activity.performer || activity.user_name),
      priority: safeString(activity.priority, "medium"),
    })),
    pipelineOverview: {
      stages: Object.entries(
        apiData?.recruitment_pipeline?.by_status || {},
      ).map(([stage, count]) => ({
        stage,
        count: count as number,
        percentage:
          ((count as number) /
            (apiData?.recruitment_pipeline?.total_candidates || 1)) *
          100,
        color: getStageColor(stage),
      })),
      maxCount: Math.max(
        ...Object.values(apiData?.recruitment_pipeline?.by_status || {}).map(
          Number,
        ),
        0,
      ),
    },
    upcomingInterviews: safeArray(apiData?.upcoming_interviews).map(
      (interview) => ({
        id: safeString(interview.id),
        candidateName: safeString(interview.candidate_name),
        candidateAvatar: safeString(interview.candidate_avatar),
        jobTitle: safeString(interview.job_title),
        date: safeString(interview.date),
        time: safeString(interview.time),
        type: safeString(interview.type),
        interviewer: safeString(interview.interviewer_name),
      }),
    ),
    topJobs: safeArray(apiData?.top_performing_jobs).map((job) => ({
      id: safeString(job.id),
      title: safeString(job.title),
      department: safeString(job.department),
      applicantCount: safeNumber(job.candidates_count),
      status: safeString(job.status),
      priority: safeString(job.priority),
    })),
    recentHires: [], // Will be populated from another API call
  }),
};

export default dashboardAdapters;
