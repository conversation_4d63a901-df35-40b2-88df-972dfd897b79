// Candidate data adapters for API v2.0.1

import {
  ApiCandidate,
  UiCandidate,
  CandidateListResponse,
  PaginatedApiResponse,
  CandidateFilters,
} from "./types";
import {
  getInitials,
  formatSalary,
  calculateMockAiScore,
  adaptPagination,
  safeString,
  safeNumber,
  safeArray,
  formatApiDate,
} from "./utils";

export const candidateAdapters = {
  // Convert API candidate to UI format
  fromApi: (apiCandidate: ApiCandidate): UiCandidate => ({
    id: apiCandidate.id.toString(),
    name: safeString(apiCandidate.name),
    email: safeString(apiCandidate.email),
    phone: safeString(apiCandidate.phone),
    initials: getInitials(apiCandidate.name),
    position: safeString(apiCandidate.position),
    experience: safeString(apiCandidate.experience),
    // v2.0.1: skills are now simple string arrays
    skills: safeArray(apiCandidate.skills),
    status: safeString(apiCandidate.status),
    appliedDate: formatApiDate(apiCandidate.applied_date),
    source: safeString(apiCandidate.source),
    location: safeString(apiCandidate.location),
    salary: formatSalary(
      apiCandidate.salary_expectation_min,
      apiCandidate.salary_expectation_max,
      apiCandidate.salary_currency,
    ),
    rating: safeNumber(apiCandidate.rating),
    interviewDate: formatApiDate(apiCandidate.interview_date),
    jobId: apiCandidate.job_posting_id?.toString(),
    linkedinUrl: safeString(apiCandidate.social_links.linkedin),
    githubUrl: safeString(apiCandidate.social_links.github),
    portfolioUrl: safeString(apiCandidate.social_links.portfolio),
    avatar: safeString(apiCandidate.avatar || apiCandidate.avatar_url),
    resumeUrl: safeString(apiCandidate.resume_url),
    notes: safeString(apiCandidate.notes),
    aiScore: apiCandidate.ai_score || calculateMockAiScore(apiCandidate),
    // v2.0.1: tags are now simple string arrays
    tags: safeArray(apiCandidate.tags),
    // v2.0.1: education and work_history are now TEXT fields
    education: safeString(apiCandidate.education),
    workHistory: safeString(apiCandidate.work_history),
    // Additional fields from API
    salaryExpectationMin: safeNumber(apiCandidate.salary_expectation_min),
    salaryExpectationMax: safeNumber(apiCandidate.salary_expectation_max),
    salaryCurrency: safeString(apiCandidate.salary_currency, "VND"),
    salaryExpectation: apiCandidate.salary_expectation_range,
    jobPosting: apiCandidate.job_posting,
    createdBy: apiCandidate.created_by,
    assignedTo: apiCandidate.assigned_to,
    createdAt: safeString(apiCandidate.created_at),
    updatedAt: safeString(apiCandidate.updated_at),
  }),

  // Convert UI candidate to API format
  toApi: (uiCandidate: Partial<UiCandidate>): Partial<ApiCandidate> => ({
    name: safeString(uiCandidate.name),
    email: safeString(uiCandidate.email),
    phone: safeString(uiCandidate.phone),
    position: safeString(uiCandidate.position),
    experience: safeString(uiCandidate.experience),
    status: safeString(uiCandidate.status),
    applied_date: safeString(uiCandidate.appliedDate),
    source: safeString(uiCandidate.source),
    location: safeString(uiCandidate.location),
    salary_expectation_min: safeNumber(uiCandidate.salaryExpectationMin),
    salary_expectation_max: safeNumber(uiCandidate.salaryExpectationMax),
    salary_currency: safeString(uiCandidate.salaryCurrency, "VND"),
    // v2.0.1: job_posting_id is now optional
    job_posting_id: uiCandidate.jobId ? parseInt(uiCandidate.jobId) : null,
    linkedin_url: safeString(uiCandidate.linkedinUrl),
    resume_url: safeString(uiCandidate.resumeUrl),
    github_url: safeString(uiCandidate.githubUrl),
    portfolio_url: safeString(uiCandidate.portfolioUrl),
    notes: safeString(uiCandidate.notes),
    // v2.0.1: education and work_history are now TEXT fields
    education: safeString(uiCandidate.education),
    work_history: safeString(uiCandidate.workHistory),
    // v2.0.1: skills are now simple string arrays
    skills: safeArray(uiCandidate.skills),
    // v2.0.1: tags are now simple string arrays
    tags: safeArray(uiCandidate.tags),
  }),

  // Convert paginated API response to UI format
  fromPaginatedApi: (
    apiResponse: PaginatedApiResponse<ApiCandidate>,
  ): CandidateListResponse => ({
    candidates: safeArray(apiResponse?.data).map(candidateAdapters.fromApi),
    pagination: adaptPagination(apiResponse),
  }),
};

// Filter adapters for API query parameters
export const candidateFilterAdapters = {
  // Convert UI filters to API parameters (v2.0.1: Direct parameter mapping)
  toApiParams: (uiFilters: CandidateFilters): Record<string, any> => {
    const apiParams: Record<string, any> = {};

    if (uiFilters.search) apiParams["search"] = uiFilters.search;
    if (uiFilters.status) apiParams["status"] = uiFilters.status;
    if (uiFilters.experience) apiParams["experience"] = uiFilters.experience;
    if (uiFilters.location) apiParams["location"] = uiFilters.location;
    if (uiFilters.source) apiParams["source"] = uiFilters.source;
    if (uiFilters.jobId)
      apiParams["job_posting_id"] = parseInt(uiFilters.jobId);

    // v2.0.1: Skills and tags as arrays
    if (safeArray(uiFilters.skills).length > 0) {
      apiParams["skills"] = uiFilters.skills;
    }
    if (safeArray(uiFilters.tags).length > 0) {
      apiParams["tags"] = uiFilters.tags;
    }

    // Sorting parameters
    if (uiFilters.sort) apiParams["sort"] = uiFilters.sort;
    if (uiFilters.direction) apiParams["direction"] = uiFilters.direction;

    return apiParams;
  },
};

export default candidateAdapters;
