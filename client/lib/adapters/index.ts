// Main adapter exports for API v2.0.1

// Export all adapters
export { candidateAdapters, candidateFilterAdapters } from "./candidates";
export { jobAdapters, jobFilterAdapters } from "./jobs";
export { interviewAdapters, interviewFilterAdapters } from "./interviews";
export { dashboardAdapters } from "./dashboard";

// Export types
export * from "./types";

// Export utilities
export * from "./utils";

// Legacy exports for backward compatibility
import { candidateAdapters, candidateFilterAdapters } from "./candidates";
import { jobAdapters, jobFilterAdapters } from "./jobs";
import { interviewAdapters, interviewFilterAdapters } from "./interviews";
import { dashboardAdapters } from "./dashboard";

// Combined filter adapters for backward compatibility
export const filterAdapters = {
  candidateFilters: candidateFilterAdapters.toApiParams,
  jobFilters: jobFilterAdapters.toApiParams,
  interviewFilters: interviewFilterAdapters.toApiParams,
};

// Default export for backward compatibility
export default {
  candidateAdapters,
  jobAdapters,
  interviewAdapters,
  dashboardAdapters,
  filterAdapters,
};
