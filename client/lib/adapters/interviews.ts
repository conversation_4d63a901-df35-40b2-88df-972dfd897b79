// Interview data adapters for API v2.0.1

import {
  ApiInterview,
  UiInterview,
  InterviewListResponse,
  PaginatedApiResponse,
  InterviewFilters,
} from "./types";
import {
  adaptPagination,
  safeString,
  safeNumber,
  safeArray,
  formatApiDate,
} from "./utils";

export const interviewAdapters = {
  // Convert API interview to UI format
  fromApi: (apiInterview: ApiInterview): UiInterview => {
    // Handle scheduled_at from Laravel API
    let date = safeString(apiInterview.date);
    let time = safeString(apiInterview.time, "09:00");

    if (apiInterview.scheduled_at) {
      const scheduledDate = new Date(apiInterview.scheduled_at);
      date = scheduledDate.toISOString().split("T")[0];
      time = scheduledDate.toLocaleTimeString("en-GB", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      });
    }
    
    return {
      id: apiInterview.id.toString(),
      candidateId: apiInterview.candidate_id?.toString(),
      candidateName:
        apiInterview.candidate?.name || safeString(apiInterview.candidate_name),
      candidateEmail:
        apiInterview.candidate?.email ||
        safeString(apiInterview.candidate_email),
      candidateAvatar: safeString(apiInterview.candidate?.avatar_url),
      jobId: apiInterview.job_posting_id?.toString(),
      jobTitle:
        apiInterview.job_posting?.title || safeString(apiInterview.job_title),
      date,
      time,
      duration: safeNumber(apiInterview.duration, 60),
      type: safeString(apiInterview.type, "video"),
      interviewer:
        apiInterview.interviewer?.name ||
        safeString(apiInterview.interviewer_name),
      interviewerId: apiInterview.interviewer_id?.toString(),
      interviewerEmail:
        apiInterview.interviewer?.email ||
        safeString(apiInterview.interviewer_email),
      status: safeString(apiInterview.status, "scheduled"),
      meetingLink: safeString(apiInterview.meeting_link),
      meetingPassword: safeString(apiInterview.meeting_password),
      location: safeString(apiInterview.location),
      notes: safeString(apiInterview.notes),
      agenda: safeArray(apiInterview.agenda),
      feedback: apiInterview.feedback,
      tags: safeArray(apiInterview.tags),
      round: safeNumber(apiInterview.round, 1),
      interviewType: safeString(apiInterview.interview_type, "technical"),
    };
  },

  // Convert UI interview to API format
  toApi: (uiInterview: Partial<UiInterview>): Partial<ApiInterview> => {
    const apiData: Partial<ApiInterview> = {
      candidate_id: uiInterview.candidateId
        ? parseInt(uiInterview.candidateId)
        : undefined,
      job_posting_id: uiInterview.jobId
        ? parseInt(uiInterview.jobId)
        : undefined,
      interviewer_id: uiInterview.interviewerId
        ? parseInt(uiInterview.interviewerId)
        : undefined,
      duration: safeNumber(uiInterview.duration, 60),
      type: safeString(uiInterview.type, "video"),
      interview_type: safeString(uiInterview.interviewType, "technical"),
      meeting_link: safeString(uiInterview.meetingLink),
      meeting_password: safeString(uiInterview.meetingPassword),
      location: safeString(uiInterview.location),
      notes: safeString(uiInterview.notes),
      agenda: safeArray(uiInterview.agenda),
      tags: safeArray(uiInterview.tags),
      round: safeNumber(uiInterview.round, 1),
      status: safeString(uiInterview.status, "scheduled"),
    };

    // Create scheduled_at for Laravel API (preferred format)
    if (uiInterview.date && uiInterview.time) {
      apiData.scheduled_at = `${uiInterview.date}T${uiInterview.time}:00`;
    }

    // Keep separate date/time for backwards compatibility
    apiData.date = safeString(uiInterview.date);
    apiData.time = safeString(uiInterview.time);

    return apiData;
  },

  // Convert paginated API response to UI format
  fromPaginatedApi: (
    apiResponse: PaginatedApiResponse<ApiInterview>,
  ): InterviewListResponse => ({
    interviews: safeArray(apiResponse?.data).map(interviewAdapters.fromApi),
    pagination: adaptPagination(apiResponse),
  }),
};

// Filter adapters for API query parameters
export const interviewFilterAdapters = {
  // Convert UI filters to API parameters
  toApiParams: (uiFilters: InterviewFilters): Record<string, any> => {
    const apiParams: Record<string, any> = {};

    if (uiFilters.status) apiParams["status"] = uiFilters.status;
    if (uiFilters.type) apiParams["type"] = uiFilters.type;
    if (uiFilters.candidateId)
      apiParams["candidate_id"] = parseInt(uiFilters.candidateId);
    if (uiFilters.interviewerId)
      apiParams["interviewer_id"] = parseInt(uiFilters.interviewerId);
    if (uiFilters.dateFrom) apiParams["date_from"] = uiFilters.dateFrom;
    if (uiFilters.dateTo) apiParams["date_to"] = uiFilters.dateTo;

    return apiParams;
  },
};

export default interviewAdapters;
