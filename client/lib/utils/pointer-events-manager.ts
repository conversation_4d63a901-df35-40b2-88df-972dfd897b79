/**
 * Pointer Events Manager
 * 
 * This utility helps manage body pointer events for dialogs and modals
 * to prevent the common issue where pointer-events: none is not cleared
 * after dialogs close.
 */

interface PointerEventsState {
  isLocked: boolean;
  lockCount: number;
  originalValue: string;
}

class PointerEventsManager {
  private state: PointerEventsState = {
    isLocked: false,
    lockCount: 0,
    originalValue: '',
  };

  private restoreTimeouts: Set<NodeJS.Timeout> = new Set();

  /**
   * Lock pointer events (typically when opening a modal)
   */
  public lock(): void {
    if (this.state.lockCount === 0) {
      // Store original value before first lock
      const currentStyle = getComputedStyle(document.body).pointerEvents;
      this.state.originalValue = currentStyle === 'auto' ? '' : currentStyle;
      
      // Apply pointer-events: none
      document.body.style.pointerEvents = 'none';
      this.state.isLocked = true;
    }
    
    this.state.lockCount++;
    
    if (process.env.NODE_ENV === 'development') {
      console.debug('PointerEventsManager: Locked (count:', this.state.lockCount, ')');
    }
  }

  /**
   * Unlock pointer events (typically when closing a modal)
   */
  public unlock(): void {
    if (this.state.lockCount > 0) {
      this.state.lockCount--;
    }

    if (this.state.lockCount === 0 && this.state.isLocked) {
      this.restore();
    }

    if (process.env.NODE_ENV === 'development') {
      console.debug('PointerEventsManager: Unlocked (count:', this.state.lockCount, ')');
    }
  }

  /**
   * Force restore pointer events regardless of lock count
   */
  public forceRestore(): void {
    this.state.lockCount = 0;
    this.restore();
    
    if (process.env.NODE_ENV === 'development') {
      console.debug('PointerEventsManager: Force restored');
    }
  }

  /**
   * Restore pointer events with a timeout as backup
   */
  public restoreWithTimeout(timeout: number = 100): void {
    const timeoutId = setTimeout(() => {
      this.forceRestore();
      this.restoreTimeouts.delete(timeoutId);
    }, timeout);
    
    this.restoreTimeouts.add(timeoutId);
  }

  /**
   * Get current state
   */
  public getState(): Readonly<PointerEventsState> {
    return { ...this.state };
  }

  /**
   * Check if body currently has pointer-events: none
   */
  public isBodyLocked(): boolean {
    const currentStyle = getComputedStyle(document.body).pointerEvents;
    return currentStyle === 'none';
  }

  /**
   * Emergency cleanup - removes any stuck pointer-events styles
   */
  public emergencyCleanup(): void {
    // Clear all timeouts
    this.restoreTimeouts.forEach(timeout => clearTimeout(timeout));
    this.restoreTimeouts.clear();
    
    // Force restore
    this.forceRestore();
    
    console.warn('PointerEventsManager: Emergency cleanup performed');
  }

  private restore(): void {
    if (this.state.originalValue) {
      document.body.style.pointerEvents = this.state.originalValue;
    } else {
      document.body.style.removeProperty('pointer-events');
    }
    
    this.state.isLocked = false;
    this.state.originalValue = '';
  }
}

// Create singleton instance
export const pointerEventsManager = new PointerEventsManager();

/**
 * React hook for managing pointer events in dialogs
 */
export function usePointerEventsLock() {
  const React = require('react');
  
  const lock = React.useCallback(() => {
    pointerEventsManager.lock();
  }, []);

  const unlock = React.useCallback(() => {
    pointerEventsManager.unlock();
  }, []);

  const forceRestore = React.useCallback(() => {
    pointerEventsManager.forceRestore();
  }, []);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      unlock();
    };
  }, [unlock]);

  return { lock, unlock, forceRestore };
}

/**
 * Global error handler for pointer events issues
 */
if (typeof window !== 'undefined') {
  // Set up error recovery
  window.addEventListener('error', (event) => {
    if (pointerEventsManager.isBodyLocked()) {
      console.warn('Global error detected with locked pointer events, attempting recovery');
      pointerEventsManager.emergencyCleanup();
    }
  });

  // Set up page visibility change handler
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible' && pointerEventsManager.isBodyLocked()) {
      console.warn('Page became visible with locked pointer events, attempting recovery');
      pointerEventsManager.restoreWithTimeout(1000);
    }
  });

  // Development mode: expose manager to window for debugging
  if (process.env.NODE_ENV === 'development') {
    (window as any).pointerEventsManager = pointerEventsManager;
  }
}

export default pointerEventsManager;
