// API Service Layer for HireFlow Backend Integration

import { config } from "./config";

export interface ApiResponse<T> {
  status: "success" | "error";
  message: string;
  data: T;
  errors?: Record<string, string[]>;
}

export class ValidationError extends Error {
  public readonly validationErrors: Record<string, string[]>;
  public readonly statusCode: number;

  constructor(
    message: string,
    validationErrors: Record<string, string[]>,
    statusCode: number = 422,
  ) {
    super(message);
    this.name = "ValidationError";
    this.validationErrors = validationErrors;
    this.statusCode = statusCode;
  }

  getFormattedErrors(): string {
    const errorMessages: string[] = [];

    // Vietnamese field name mapping
    const fieldNames: Record<string, string> = {
      title: "Tên công việc",
      name: "<PERSON><PERSON> tên",
      email: "Email",
      phone: "Số điện thoại",
      position: "Vị trí",
      department: "Phòng ban",
      description: "<PERSON>ô tả",
      location: "<PERSON>ị trí",
      salary: "<PERSON><PERSON><PERSON> l<PERSON>ơ<PERSON>",
      form: "<PERSON>iểu mẫu",
      status: "Trạng thái",
      type: "Lo���i",
    };

    Object.entries(this.validationErrors).forEach(([field, messages]) => {
      const fieldDisplayName = fieldNames[field] || field;

      if (Array.isArray(messages)) {
        messages.forEach((message) => {
          // Only add field name if it's not already in the message
          if (
            message.toLowerCase().includes(field.toLowerCase()) ||
            field === "form"
          ) {
            errorMessages.push(message);
          } else {
            errorMessages.push(`${fieldDisplayName}: ${message}`);
          }
        });
      } else {
        // Handle single message
        const message = String(messages);
        if (
          message.toLowerCase().includes(field.toLowerCase()) ||
          field === "form"
        ) {
          errorMessages.push(message);
        } else {
          errorMessages.push(`${fieldDisplayName}: ${message}`);
        }
      }
    });

    return errorMessages.join("\n");
  }
}

export class ApiError extends Error {
  public readonly statusCode: number;
  public readonly response?: any;

  constructor(message: string, statusCode: number, response?: any) {
    super(message);
    this.name = "ApiError";
    this.statusCode = statusCode;
    this.response = response;
  }
}

export interface PaginatedResponse<T> {
  interviews: any;
  status: "success" | "error";
  data: T[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface ApiError {
  status: "error";
  message: string;
  errors?: Record<string, string[]>;
}

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = `${config.apiBaseUrl}/api/v1`;
    // Safe localStorage access to prevent SSR issues
    this.token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      Accept: "application/json",
      "Content-Type": "application/json",
    };

    if (this.token) {
      headers["Authorization"] = `Bearer ${this.token}`;
    }

    return headers;
  }

  private getFileUploadHeaders(): HeadersInit {
    const headers: HeadersInit = {
      Accept: "application/json",
    };

    if (this.token) {
      headers["Authorization"] = `Bearer ${this.token}`;
    }

    return headers;
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== "undefined") {
      localStorage.setItem("auth_token", token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token");
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        ...options,
        headers: {
          ...this.getHeaders(),
          ...options.headers,
        },
      });

      let data: any = null;

      // Only try to read the response body if there's content
      if (response.status !== 204) {
        try {
          let responseToRead = response;

          // Check if the response body has already been consumed
          if (response.bodyUsed) {
            console.warn(
              "Response body already consumed, providing fallback error structure",
            );
            // Provide appropriate fallback data structure without trying to read body
            if (!response.ok) {
              if (response.status === 422) {
                data = {
                  status: "error",
                  message: "Lỗi xác thực dữ liệu",
                  data: null,
                  errors: {
                    form: [
                      "Dữ liệu nhập vào không hợp lệ. Vui lòng kiểm tra lại.",
                    ],
                  },
                };
              } else if (response.status === 500) {
                data = {
                  status: "error",
                  message: "Lỗi máy chủ nội bộ",
                  data: null,
                };
              } else {
                data = {
                  status: "error",
                  message: `Lỗi máy chủ (${response.status})`,
                  data: null,
                };
              }
            } else {
              data = null;
            }
          } else {
            // Clone the response to protect against external tools reading the body
            try {
              responseToRead = response.clone();
            } catch (cloneError) {
              console.warn(
                "Cannot clone response, using original:",
                cloneError,
              );
            }

            // Read response as text (this consumes the body)
            const text = await responseToRead.text();

            if (text.trim()) {
              // Try to parse as JSON
              try {
                data = JSON.parse(text);
              } catch (jsonError) {
                console.warn("Failed to parse JSON response:", jsonError);
                // If not JSON, treat the text as the error message
                data = { message: text };
              }
            } else {
              data = null;
            }
          }
        } catch (bodyError) {
          console.error("Failed to read response body:", bodyError);

          // Check if this is the specific "body already read" error
          const isBodyAlreadyRead =
            bodyError.message &&
            (bodyError.message.includes("body stream already read") ||
              bodyError.message.includes("Response body is already used"));

          if (isBodyAlreadyRead) {
            console.warn(
              "Response body was consumed by external source (analytics, dev tools, etc.)",
            );
          }

          // Provide fallback error data based on status code
          if (!response.ok) {
            if (response.status === 422) {
              data = {
                status: "error",
                message: "Lỗi xác thực dữ liệu",
                data: null,
                errors: {
                  form: [
                    "Dữ liệu nhập vào không hợp lệ. Vui lòng ki���m tra lại.",
                  ],
                },
              };
            } else if (response.status === 500) {
              data = {
                status: "error",
                message: "Lỗi máy chủ nội bộ",
                data: null,
              };
            } else {
              data = {
                status: "error",
                message: `HTTP ${response.status}: ${response.statusText}`,
              };
            }
          } else {
            data = null;
          }
        }
      }

      if (!response.ok) {
        // Extract error message following the priority order: message -> error -> fallback
        const extractErrorMessage = (data: any, fallbackMessage: string) => {
          return data?.message || data?.error || fallbackMessage;
        };

        // Handle validation errors (422)
        if (response.status === 422) {
          const mainMessage = extractErrorMessage(data, "Validation failed");

          if (data?.errors && typeof data.errors === "object") {
            // Structured validation errors with field-specific messages
            throw new ValidationError(
              mainMessage,
              data.errors,
              response.status,
            );
          } else {
            // Simple validation error without structured errors
            const errorFields = { form: [mainMessage] };
            throw new ValidationError(
              mainMessage,
              errorFields,
              response.status,
            );
          }
        }

        // Handle 500 errors specifically
        if (response.status === 500) {
          const errorMessage = extractErrorMessage(
            data,
            "Internal server error",
          );
          throw new ApiError(errorMessage, response.status, data);
        }

        // Handle other API errors
        const errorMessage = extractErrorMessage(
          data,
          `HTTP ${response.status}: ${response.statusText}`,
        );

        throw new ApiError(errorMessage, response.status, data);
      }

      return data || { status: "success", message: "Success", data: null as T };
    } catch (error) {
      console.error("API Error:", error);

      // If it's already a ValidationError or ApiError, just re-throw
      if (error instanceof ValidationError || error instanceof ApiError) {
        throw error;
      }

      // Handle network errors or other fetch failures
      if (error instanceof TypeError && error.message.includes("fetch")) {
        throw new ApiError(
          "Kết nối mạng thất bại. Vui lòng kiểm tra kết n��i internet.",
          0,
          error,
        );
      }

      // Handle other unexpected errors
      throw new ApiError(
        error?.message || "Đã xảy ra lỗi không xác định",
        0,
        error,
      );
    }
  }

  // Authentication
  async login(
    email: string,
    password: string,
  ): Promise<
    ApiResponse<{
      user: any;
      token: string;
      token_type: string;
    }>
  > {
    const response = await this.request<{
      user: any;
      token: string;
      token_type: string;
    }>("/auth/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });

    if (response.status === "success") {
      this.setToken(response.data.token);
    }

    return response;
  }

  async logout(): Promise<ApiResponse<any>> {
    const response = await this.request<any>("/auth/logout", {
      method: "POST",
    });

    this.clearToken();
    return response;
  }

  async getCurrentUser(): Promise<ApiResponse<{ user: any }>> {
    return this.request<{ user: any }>("/auth/me");
  }

  // Candidates
  async getCandidates(
    params: {
      page?: number;
      per_page?: number;
      search?: string;
      status?: string;
      experience?: string;
      location?: string;
      source?: string;
      skills?: string[];
      tags?: string[];
      job_posting_id?: number;
      sort?: string;
      direction?: string;
      include?: string;
      filter?: Record<string, any>;
    } = {},
  ): Promise<PaginatedResponse<any>> {
    const queryString = new URLSearchParams();

    // Basic pagination and sorting
    if (params.page) queryString.append("page", params.page.toString());
    if (params.per_page)
      queryString.append("per_page", params.per_page.toString());
    if (params.sort) queryString.append("sort", params.sort);
    if (params.direction) queryString.append("direction", params.direction);
    if (params.include) queryString.append("include", params.include);

    // v2.0.1: Direct query parameters
    if (params.search) queryString.append("search", params.search);
    if (params.status) queryString.append("status", params.status);
    if (params.experience) queryString.append("experience", params.experience);
    if (params.location) queryString.append("location", params.location);
    if (params.source) queryString.append("source", params.source);
    if (params.job_posting_id)
      queryString.append("job_posting_id", params.job_posting_id.toString());

    // v2.0.1: Array parameters for skills and tags
    if (params.skills && params.skills.length > 0) {
      params.skills.forEach((skill) => {
        queryString.append("skills[]", skill);
      });
    }
    if (params.tags && params.tags.length > 0) {
      params.tags.forEach((tag) => {
        queryString.append("tags[]", tag);
      });
    }

    // Legacy filter support for backward compatibility
    if (params.filter) {
      Object.entries(params.filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (!queryString.has(key)) {
            queryString.append(key, value.toString());
          }
        }
      });
    }

    const response = await fetch(`${this.baseURL}/candidates?${queryString}`, {
      headers: this.getHeaders(),
    });

    return response.json();
  }

  async getCandidate(id: string, include?: string): Promise<ApiResponse<any>> {
    const queryString = include ? `?include=${include}` : "";
    return this.request<any>(`/candidates/${id}${queryString}`);
  }

  async createCandidate(candidateData: any): Promise<ApiResponse<any>> {
    return this.request<any>("/candidates", {
      method: "POST",
      body: JSON.stringify(candidateData),
    });
  }

  async updateCandidate(
    id: string,
    candidateData: any,
  ): Promise<ApiResponse<any>> {
    return this.request<any>(`/candidates/${id}`, {
      method: "PUT",
      body: JSON.stringify(candidateData),
    });
  }

  async updateCandidateStatus(
    id: string,
    status: string,
    notes?: string,
  ): Promise<ApiResponse<any>> {
    return this.request<any>(`/candidates/${id}/status`, {
      method: "PATCH",
      body: JSON.stringify({ status, notes }),
      headers: this.getHeaders(),
    });
  }

  async uploadResume(
    candidateId: string,
    file: File,
  ): Promise<ApiResponse<any>> {
    const formData = new FormData();
    formData.append("resume", file);

    const response = await fetch(
      `${this.baseURL}/candidates/${candidateId}/resume`,
      {
        method: "POST",
        headers: this.getFileUploadHeaders(),
        body: formData,
      },
    );

    return response.json();
  }

  async triggerAIAnalysis(candidateId: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/candidates/${candidateId}/ai-analysis`, {
      method: "POST",
    });
  }

  async deleteCandidate(id: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/candidates/${id}`, {
      method: "DELETE",
    });
  }

  // Jobs
  async getJobs(
    params: {
      page?: number;
      per_page?: number;
      search?: string;
      status?: string;
      department?: string;
      location?: string;
      type?: string;
      work_location?: string;
      experience_level?: string;
      priority?: string;
      hiring_manager_id?: number;
      recruiter_id?: number;
      skills?: string[];
      sort?: string;
      direction?: string;
      include?: string;
      filter?: Record<string, any>;
    } = {},
  ): Promise<PaginatedResponse<any>> {
    const queryString = new URLSearchParams();

    // Basic pagination and sorting
    if (params.page) queryString.append("page", params.page.toString());
    if (params.per_page)
      queryString.append("per_page", params.per_page.toString());
    if (params.sort) queryString.append("sort", params.sort);
    if (params.direction) queryString.append("direction", params.direction);
    if (params.include) queryString.append("include", params.include);

    // v2.0.1: Direct query parameters
    if (params.search) queryString.append("search", params.search);
    if (params.status) queryString.append("status", params.status);
    if (params.department) queryString.append("department", params.department);
    if (params.location) queryString.append("location", params.location);
    if (params.type) queryString.append("type", params.type);
    if (params.work_location)
      queryString.append("work_location", params.work_location);
    if (params.experience_level)
      queryString.append("experience_level", params.experience_level);
    if (params.priority) queryString.append("priority", params.priority);
    if (params.hiring_manager_id)
      queryString.append(
        "hiring_manager_id",
        params.hiring_manager_id.toString(),
      );
    if (params.recruiter_id)
      queryString.append("recruiter_id", params.recruiter_id.toString());

    // v2.0.1: Array parameters for skills
    if (params.skills && params.skills.length > 0) {
      params.skills.forEach((skill) => {
        queryString.append("skills[]", skill);
      });
    }

    // Legacy filter support for backward compatibility
    if (params.filter) {
      Object.entries(params.filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (!queryString.has(key)) {
            queryString.append(key, value.toString());
          }
        }
      });
    }

    const response = await fetch(`${this.baseURL}/jobs?${queryString}`, {
      headers: this.getHeaders(),
    });

    return response.json();
  }

  async getJob(id: string, include?: string): Promise<ApiResponse<any>> {
    const queryString = include ? `?include=${include}` : "";
    return this.request<any>(`/jobs/${id}${queryString}`);
  }

  async createJob(jobData: any): Promise<ApiResponse<any>> {
    return this.request<any>("/jobs", {
      method: "POST",
      body: JSON.stringify(jobData),
    });
  }

  async updateJob(id: string, jobData: any): Promise<ApiResponse<any>> {
    return this.request<any>(`/jobs/${id}`, {
      method: "PUT",
      body: JSON.stringify(jobData),
    });
  }

  async deleteJob(id: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/jobs/${id}`, {
      method: "DELETE",
    });
  }

  async getJobAnalytics(id: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/jobs/${id}/analytics`);
  }

  async getJobCandidates(
    id: string,
    params: {
      page?: number;
      per_page?: number;
      filter?: Record<string, any>;
      sort?: string;
      include?: string;
    } = {},
  ): Promise<PaginatedResponse<any>> {
    const queryString = new URLSearchParams();

    if (params.page) queryString.append("page", params.page.toString());
    if (params.per_page)
      queryString.append("per_page", params.per_page.toString());
    if (params.sort) queryString.append("sort", params.sort);
    if (params.include) queryString.append("include", params.include);

    if (params.filter) {
      Object.entries(params.filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryString.append(`filter[${key}]`, value.toString());
        }
      });
    }

    const response = await fetch(
      `${this.baseURL}/jobs/${id}/candidates?${queryString}`,
      {
        headers: this.getHeaders(),
      },
    );

    return response.json();
  }

  async updateJobStatus(
    id: string,
    status: string,
    notes?: string,
  ): Promise<ApiResponse<any>> {
    return this.request<any>(`/jobs/${id}/status`, {
      method: "PATCH",
      body: JSON.stringify({ status, notes }),
    });
  }

  async bulkJobActions(
    action: string,
    jobIds: string[],
  ): Promise<ApiResponse<any>> {
    return this.request<any>("/jobs/bulk-action", {
      method: "POST",
      body: JSON.stringify({ action, job_ids: jobIds }),
    });
  }

  // v2.0.1: Hiring Managers and Recruiters
  async getHiringManagers(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>("/jobs/hiring-managers");
  }

  async getRecruiters(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>("/jobs/recruiters");
  }

  // Interviewers API methods
  async getInterviewers(params?: {
    department?: string;
    is_active?: boolean;
    expertise?: string;
    include_stats?: boolean;
    include_interviews?: boolean;
  }): Promise<ApiResponse<any[]>> {
    const queryString = new URLSearchParams();

    if (params?.department) queryString.append("department", params.department);
    if (params?.is_active !== undefined)
      queryString.append("is_active", params.is_active.toString());
    if (params?.expertise) queryString.append("expertise", params.expertise);
    if (params?.include_stats !== undefined)
      queryString.append("include_stats", params.include_stats.toString());
    if (params?.include_interviews !== undefined)
      queryString.append(
        "include_interviews",
        params.include_interviews.toString(),
      );

    return this.request<any[]>(`/interviewers?${queryString}`);
  }

  async getInterviewer(
    id: string,
    params?: {
      include_interviews?: boolean;
      include_stats?: boolean;
    },
  ): Promise<ApiResponse<any>> {
    const queryString = new URLSearchParams();

    if (params?.include_interviews !== undefined) {
      queryString.append(
        "include_interviews",
        params.include_interviews.toString(),
      );
    }
    if (params?.include_stats !== undefined) {
      queryString.append("include_stats", params.include_stats.toString());
    }

    return this.request<any>(`/interviewers/${id}?${queryString}`);
  }

  async createInterviewer(interviewerData: any): Promise<ApiResponse<any>> {
    return this.request<any>("/interviewers", {
      method: "POST",
      body: JSON.stringify(interviewerData),
    });
  }

  async updateInterviewer(
    id: string,
    interviewerData: any,
  ): Promise<ApiResponse<any>> {
    return this.request<any>(`/interviewers/${id}`, {
      method: "PATCH",
      body: JSON.stringify(interviewerData),
    });
  }

  async deleteInterviewer(id: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/interviewers/${id}`, {
      method: "DELETE",
    });
  }

  // Interviews
  async getInterviews(
    params: {
      page?: number;
      per_page?: number;
      filter?: Record<string, any>;
      sort?: string;
      status?: string;
      type?: string;
      candidate_id?: number;
      interviewer_id?: number;
      date_from?: string;
      date_to?: string;
    } = {},
  ): Promise<PaginatedResponse<any>> {
    const queryString = new URLSearchParams();

    if (params.page) queryString.append("page", params.page.toString());
    if (params.per_page)
      queryString.append("per_page", params.per_page.toString());
    if (params.sort) queryString.append("sort", params.sort);

    // Direct query parameters for Laravel API
    if (params.status) queryString.append("status", params.status);
    if (params.type) queryString.append("type", params.type);
    if (params.candidate_id)
      queryString.append("candidate_id", params.candidate_id.toString());
    if (params.interviewer_id)
      queryString.append("interviewer_id", params.interviewer_id.toString());
    if (params.date_from) queryString.append("date_from", params.date_from);
    if (params.date_to) queryString.append("date_to", params.date_to);

    // Legacy filter support
    if (params.filter) {
      Object.entries(params.filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryString.append(key, value.toString());
        }
      });
    }

    const url = `/interviews?${queryString}`;

    return this.request<any[]>(url);
  }

  async scheduleInterview(interviewData: any): Promise<ApiResponse<any>> {
    return this.request<any>("/interviews", {
      method: "POST",
      body: JSON.stringify(interviewData),
    });
  }

  async checkInterviewerAvailability(params: {
    interviewer_id: string;
    date: string;
    time: string;
    duration: number;
  }): Promise<ApiResponse<any>> {
    return this.request<any>("/interviews/availability/check", {
      method: "POST",
      body: JSON.stringify(params),
    });
  }

  async getInterview(id: string, include?: string): Promise<ApiResponse<any>> {
    const queryString = include ? `?include=${include}` : "";
    return this.request<any>(`/interviews/${id}${queryString}`);
  }

  async updateInterview(
    id: string,
    interviewData: any,
  ): Promise<ApiResponse<any>> {
    return this.request<any>(`/interviews/${id}`, {
      method: "PUT",
      body: JSON.stringify(interviewData),
    });
  }

  async deleteInterview(id: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/interviews/${id}`, {
      method: "DELETE",
    });
  }

  async updateInterviewStatus(
    id: string,
    status: string,
    notes?: string,
  ): Promise<ApiResponse<any>> {
    return this.request<any>(`/interviews/${id}/status`, {
      method: "PATCH",
      body: JSON.stringify({ status, notes }),
    });
  }

  async sendInterviewReminders(params: {
    interview_ids: string[];
    hours_before: number;
  }): Promise<ApiResponse<any>> {
    return this.request<any>("/interviews/reminders/send", {
      method: "POST",
      body: JSON.stringify(params),
    });
  }

  async getCalendarEvents(params: {
    start_date: string;
    end_date: string;
    interviewer_id?: string;
  }): Promise<ApiResponse<any>> {
    const queryString = new URLSearchParams();
    queryString.append("start_date", params.start_date);
    queryString.append("end_date", params.end_date);
    if (params.interviewer_id) {
      queryString.append("interviewer_id", params.interviewer_id);
    }

    return this.request<any>(`/interviews/calendar/events?${queryString}`);
  }

  async getInterviewFeedback(
    params: {
      page?: number;
      per_page?: number;
      filter?: Record<string, any>;
      sort?: string;
      include?: string;
    } = {},
  ): Promise<PaginatedResponse<any>> {
    const queryString = new URLSearchParams();

    if (params.page) queryString.append("page", params.page.toString());
    if (params.per_page)
      queryString.append("per_page", params.per_page.toString());
    if (params.sort) queryString.append("sort", params.sort);
    if (params.include) queryString.append("include", params.include);

    if (params.filter) {
      Object.entries(params.filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryString.append(`filter[${key}]`, value.toString());
        }
      });
    }

    const response = await fetch(
      `${this.baseURL}/interview-feedback?${queryString}`,
      {
        headers: this.getHeaders(),
      },
    );

    return response.json();
  }

  async submitInterviewFeedback(feedbackData: any): Promise<ApiResponse<any>> {
    return this.request<any>("/interview-feedback", {
      method: "POST",
      body: JSON.stringify(feedbackData),
    });
  }

  async getInterviewFeedbackDetails(
    id: string,
    include?: string,
  ): Promise<ApiResponse<any>> {
    const queryString = include ? `?include=${include}` : "";
    return this.request<any>(`/interview-feedback/${id}${queryString}`);
  }

  async updateInterviewFeedback(
    id: string,
    feedbackData: any,
  ): Promise<ApiResponse<any>> {
    return this.request<any>(`/interview-feedback/${id}`, {
      method: "PUT",
      body: JSON.stringify(feedbackData),
    });
  }

  async deleteInterviewFeedback(id: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/interview-feedback/${id}`, {
      method: "DELETE",
    });
  }

  // Enhanced interview feedback methods with proper typing
  async getFeedbackByInterview(
    interviewId: number,
    include?: string,
  ): Promise<PaginatedResponse<any>> {
    const params = { filter: { interview_id: interviewId } };
    if (include) params.include = include;
    return this.getInterviewFeedback(params);
  }

  async getFeedbackByInterviewer(
    interviewerId: number,
    params: {
      page?: number;
      per_page?: number;
      include?: string;
    } = {},
  ): Promise<PaginatedResponse<any>> {
    const requestParams = {
      ...params,
      filter: { interviewer_id: interviewerId },
    };
    return this.getInterviewFeedback(requestParams);
  }

  async patchInterviewFeedback(
    id: string,
    feedbackData: any,
  ): Promise<ApiResponse<any>> {
    return this.request<any>(`/interview-feedback/${id}`, {
      method: "PATCH",
      body: JSON.stringify(feedbackData),
    });
  }

  // Dashboard
  async getDashboardOverview(
    dateFrom?: string,
    dateTo?: string,
  ): Promise<ApiResponse<any>> {
    const queryString = new URLSearchParams();
    if (dateFrom) queryString.append("date_from", dateFrom);
    if (dateTo) queryString.append("date_to", dateTo);

    return this.request<any>(`/dashboard/overview?${queryString}`);
  }

  async getRecruitmentPipeline(): Promise<ApiResponse<any>> {
    return this.request<any>("/dashboard/recruitment-pipeline");
  }

  async getSourceEffectiveness(): Promise<ApiResponse<any>> {
    return this.request<any>("/dashboard/source-effectiveness");
  }

  async getTeamPerformance(): Promise<ApiResponse<any>> {
    return this.request<any>("/dashboard/team-performance");
  }

  // File Upload
  async uploadFile(params: {
    file: File;
    type: "contract" | "certificate" | "portfolio" | "other";
    entity_type: "candidate" | "job" | "interview" | "user";
    entity_id: number;
    description?: string;
  }): Promise<ApiResponse<any>> {
    const formData = new FormData();
    formData.append("file", params.file);
    formData.append("type", params.type);
    formData.append("entity_type", params.entity_type);
    formData.append("entity_id", params.entity_id.toString());
    if (params.description) formData.append("description", params.description);

    const response = await fetch(`${this.baseURL}/files/upload`, {
      method: "POST",
      headers: this.getFileUploadHeaders(),
      body: formData,
    });

    return response.json();
  }

    // Export
  async exportAnalytics(params: {
    type: "pipeline" | "sources" | "team" | "comprehensive";
    format: "pdf" | "excel" | "csv";
    date_from?: string;
    date_to?: string;
  }): Promise<ApiResponse<any>> {
    return this.request<any>("/dashboard/export", {
      method: "POST",
      body: JSON.stringify(params),
    });
  }

  // Candidate Analysis
  async generateCandidateAnalysis(params: {
    candidate_id: number;
    job_posting_id?: number;
  }): Promise<ApiResponse<any>> {
    return this.request<any>("/candidate-analysis/generate-analysis", {
      method: "POST",
      body: JSON.stringify(params),
    });
  }

  async getCandidateAnalyses(params: {
    candidate_id?: number;
    job_posting_id?: number;
    analysis_type?: string;
    status?: string;
    page?: number;
    per_page?: number;
  } = {}): Promise<ApiResponse<any>> {
    const queryString = new URLSearchParams();

    if (params.candidate_id) queryString.append("candidate_id", params.candidate_id.toString());
    if (params.job_posting_id) queryString.append("job_posting_id", params.job_posting_id.toString());
    if (params.analysis_type) queryString.append("analysis_type", params.analysis_type);
    if (params.status) queryString.append("status", params.status);
    if (params.page) queryString.append("page", params.page.toString());
    if (params.per_page) queryString.append("per_page", params.per_page.toString());

    return this.request<any>(`/candidate-analysis/analyses?${queryString}`);
  }

  async getCandidateAnalysis(id: number): Promise<ApiResponse<any>> {
    return this.request<any>(`/candidate-analysis/analyses/${id}`);
  }

  async getCandidateSummary(candidateId: number): Promise<ApiResponse<any>> {
    return this.request<any>(`/candidate-analysis/candidate/${candidateId}/summary`);
  }

  // User Management API Methods
  async getUsers(params?: {
    page?: number;
    per_page?: number;
    filter?: {
      role?: string;
      department?: string;
      is_active?: boolean;
      name?: string;
      email?: string;
    };
    sort?: string;
    include?: string;
  }): Promise<ApiResponse<any>> {
    const queryString = new URLSearchParams();

    if (params?.page) queryString.append("page", params.page.toString());
    if (params?.per_page) queryString.append("per_page", params.per_page.toString());
    if (params?.sort) queryString.append("sort", params.sort);
    if (params?.include) queryString.append("include", params.include);

    // Add filters
    if (params?.filter) {
      Object.entries(params.filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryString.append(`filter[${key}]`, value.toString());
        }
      });
    }

    return this.request<any>(`/users?${queryString}`);
  }

  async createUser(userData: {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    role: string;
    department?: string;
    title?: string;
    phone?: string;
    is_active?: boolean;
  }): Promise<ApiResponse<any>> {
    return this.request<any>("/users", {
      method: "POST",
      body: JSON.stringify(userData),
    });
  }

  async getUser(id: number, include?: string): Promise<ApiResponse<any>> {
    const queryString = include ? `?include=${include}` : "";
    return this.request<any>(`/users/${id}${queryString}`);
  }

  async updateUser(id: number, userData: {
    name?: string;
    email?: string;
    password?: string;
    password_confirmation?: string;
    role?: string;
    department?: string;
    title?: string;
    phone?: string;
    is_active?: boolean;
  }): Promise<ApiResponse<any>> {
    return this.request<any>(`/users/${id}`, {
      method: "PUT",
      body: JSON.stringify(userData),
    });
  }

  async updateUserRole(id: number, role: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/users/${id}/roles`, {
      method: "PUT",
      body: JSON.stringify({ role }),
    });
  }

  async deactivateUser(id: number): Promise<ApiResponse<any>> {
    return this.request<any>(`/users/${id}`, {
      method: "DELETE",
    });
  }

  async getUserRoles(): Promise<ApiResponse<any>> {
    return this.request<any>("/users/roles");
  }

  async getUserStatistics(): Promise<ApiResponse<any>> {
    return this.request<any>("/users/statistics");
  }
}

export const apiService = new ApiService();
export default apiService;
