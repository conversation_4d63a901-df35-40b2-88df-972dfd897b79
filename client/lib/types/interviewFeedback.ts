// Interview Feedback Types based on HireFlow API v2.0.1

export type NextRoundType =
  | "screening"
  | "technical"
  | "case-study"
  | "portfolio"
  | "cultural"
  | "final"
  | "offer"
  | "reject";

// Core Interview Feedback Interface
export interface InterviewFeedback {
  id: number;
  interview_id: number;
  interviewer_id: number;
  rating: number | null; // 0-5 scale
  comments: string | null;
  recommend: boolean | null;
  strengths: string[] | null; // Array of strength descriptions
  concerns: string[] | null; // Array of concern descriptions
  next_round_recommendation: NextRoundType | null;
  technical_score: number | null; // 0-100 scale
  communication_score: number | null; // 0-100 scale
  cultural_fit_score: number | null; // 0-100 scale
  overall_score: number; // Computed average of scores
  created_at: string; // ISO 8601 format
  updated_at: string; // ISO 8601 format

  // Relationships (when included)
  interview?: InterviewSummary;
  interviewer?: InterviewerSummary;
}

// Summary interfaces for relationships
export interface InterviewSummary {
  id: number;
  date: string; // YYYY-MM-DD format
  time: string; // HH:mm format
  type: string;
  interview_type: string;
  round: number;
  candidate?: {
    id: number;
    name: string;
    email: string;
    position: string;
    rating?: number;
    ai_score?: number;
  };
  job_posting?: {
    id: number;
    title: string;
    department: string;
    location: string;
    priority: string;
  };
}

export interface InterviewerSummary {
  id: number;
  name: string;
  email: string;
  department: string;
  user?: {
    id: number;
    name: string;
    email: string;
    phone: string;
    avatar?: string;
    initials: string;
    title: string;
    is_active: boolean;
  };
}

// API Response Types
export interface ApiResponse<T> {
  status: "success" | "error";
  message?: string;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

// Request Types
export interface InterviewFeedbackCreateRequest {
  interview_id: number;
  interviewer_id: number;
  rating?: number | null;
  comments?: string | null;
  recommend?: boolean | null;
  strengths?: string[] | null;
  concerns?: string[] | null;
  next_round_recommendation?: NextRoundType | null;
  technical_score?: number | null;
  communication_score?: number | null;
  cultural_fit_score?: number | null;
}

export interface InterviewFeedbackUpdateRequest {
  rating?: number | null;
  comments?: string | null;
  recommend?: boolean | null;
  strengths?: string[] | null;
  concerns?: string[] | null;
  next_round_recommendation?: NextRoundType | null;
  technical_score?: number | null;
  communication_score?: number | null;
  cultural_fit_score?: number | null;
}

// Filter and Query Types
export interface InterviewFeedbackFilters {
  interview_id?: number;
  interviewer_id?: number;
  recommend?: boolean;
  next_round_recommendation?: NextRoundType;
  rating_min?: number;
  technical_score_min?: number;
  communication_score_min?: number;
  cultural_fit_score_min?: number;
}

export interface InterviewFeedbackListParams {
  page?: number;
  per_page?: number;
  sort?: string;
  include?: string;
  filters?: InterviewFeedbackFilters;
}

// UI Form Types
export interface FeedbackFormData {
  rating: number | null;
  comments: string;
  recommend: boolean | null;
  strengths: string[];
  concerns: string[];
  next_round_recommendation: NextRoundType | null;
  technical_score: number | null;
  communication_score: number | null;
  cultural_fit_score: number | null;
}

// Validation Types
export interface FeedbackValidationErrors {
  interview_id?: string[];
  interviewer_id?: string[];
  rating?: string[];
  comments?: string[];
  recommend?: string[];
  strengths?: string[];
  concerns?: string[];
  next_round_recommendation?: string[];
  technical_score?: string[];
  communication_score?: string[];
  cultural_fit_score?: string[];
  form?: string[];
}

// UI State Types
export interface FeedbackUIState {
  isLoading: boolean;
  isSaving: boolean;
  isDeleting: boolean;
  errors: FeedbackValidationErrors | null;
  showForm: boolean;
  editMode: boolean;
}

// Activity Timeline Types
export interface FeedbackActivity {
  id: string;
  type: "feedback_created" | "feedback_updated" | "feedback_deleted";
  title: string;
  description: string;
  timestamp: string;
  user: {
    id: number;
    name: string;
    avatar?: string;
    initials: string;
  };
  metadata: {
    feedback_id: number;
    interview_id: number;
    rating?: number;
    recommend?: boolean;
    changes?: string[];
  };
}

// Constants for UI components
export const NEXT_ROUND_OPTIONS = [
  { value: "screening", label: "Screening Interview" },
  { value: "technical", label: "Technical Interview" },
  { value: "case-study", label: "Case Study Interview" },
  { value: "portfolio", label: "Portfolio Review" },
  { value: "cultural", label: "Cultural Fit Interview" },
  { value: "final", label: "Final Interview" },
  { value: "offer", label: "Proceed to Offer" },
  { value: "reject", label: "Reject Candidate" },
] as const;

export const RATING_LABELS = [
  { value: 1, label: "Poor", color: "text-red-500" },
  { value: 2, label: "Below Average", color: "text-orange-500" },
  { value: 3, label: "Average", color: "text-yellow-500" },
  { value: 4, label: "Good", color: "text-blue-500" },
  { value: 5, label: "Excellent", color: "text-green-500" },
] as const;

export const SCORE_RANGES = {
  POOR: { min: 0, max: 40, label: "Poor", color: "text-red-500" },
  BELOW_AVERAGE: {
    min: 41,
    max: 60,
    label: "Below Average",
    color: "text-orange-500",
  },
  AVERAGE: { min: 61, max: 70, label: "Average", color: "text-yellow-500" },
  GOOD: { min: 71, max: 85, label: "Good", color: "text-blue-500" },
  EXCELLENT: { min: 86, max: 100, label: "Excellent", color: "text-green-500" },
} as const;

// Helper functions for score evaluation
export const getScoreLabel = (score: number | null): string => {
  if (score === null || score === undefined) return "Not Rated";

  for (const range of Object.values(SCORE_RANGES)) {
    if (score >= range.min && score <= range.max) {
      return range.label;
    }
  }
  return "Invalid Score";
};

export const getScoreColor = (score: number | null): string => {
  if (score === null || score === undefined) return "text-gray-500";

  for (const range of Object.values(SCORE_RANGES)) {
    if (score >= range.min && score <= range.max) {
      return range.color;
    }
  }
  return "text-gray-500";
};

export const getRatingLabel = (rating: number | null): string => {
  if (rating === null || rating === undefined) return "Not Rated";

  const ratingLabel = RATING_LABELS.find((r) => r.value === Math.round(rating));
  return ratingLabel?.label || "Invalid Rating";
};

export const getRatingColor = (rating: number | null): string => {
  if (rating === null || rating === undefined) return "text-gray-500";

  const ratingLabel = RATING_LABELS.find((r) => r.value === Math.round(rating));
  return ratingLabel?.color || "text-gray-500";
};

// Validation helpers
export const validateFeedbackData = (
  data: Partial<FeedbackFormData>,
): FeedbackValidationErrors => {
  const errors: FeedbackValidationErrors = {};

  if (data.rating !== null && data.rating !== undefined) {
    if (data.rating < 0 || data.rating > 5) {
      errors.rating = ["Rating must be between 0 and 5"];
    }
  }

  if (data.technical_score !== null && data.technical_score !== undefined) {
    if (data.technical_score < 0 || data.technical_score > 100) {
      errors.technical_score = ["Technical score must be between 0 and 100"];
    }
  }

  if (
    data.communication_score !== null &&
    data.communication_score !== undefined
  ) {
    if (data.communication_score < 0 || data.communication_score > 100) {
      errors.communication_score = [
        "Communication score must be between 0 and 100",
      ];
    }
  }

  if (
    data.cultural_fit_score !== null &&
    data.cultural_fit_score !== undefined
  ) {
    if (data.cultural_fit_score < 0 || data.cultural_fit_score > 100) {
      errors.cultural_fit_score = [
        "Cultural fit score must be between 0 and 100",
      ];
    }
  }

  if (data.strengths && Array.isArray(data.strengths)) {
    const invalidStrengths = data.strengths.filter(
      (strength) => typeof strength !== "string" || strength.length > 255,
    );
    if (invalidStrengths.length > 0) {
      errors.strengths = [
        "Each strength must be a string with maximum 255 characters",
      ];
    }
  }

  if (data.concerns && Array.isArray(data.concerns)) {
    const invalidConcerns = data.concerns.filter(
      (concern) => typeof concern !== "string" || concern.length > 255,
    );
    if (invalidConcerns.length > 0) {
      errors.concerns = [
        "Each concern must be a string with maximum 255 characters",
      ];
    }
  }

  return errors;
};

// Helper to calculate overall score
export const calculateOverallScore = (
  technical_score: number | null,
  communication_score: number | null,
  cultural_fit_score: number | null,
): number => {
  const scores = [
    technical_score,
    communication_score,
    cultural_fit_score,
  ].filter((score): score is number => score !== null && score !== undefined);

  if (scores.length === 0) return 0;

  const sum = scores.reduce((acc, score) => acc + score, 0);
  return Math.round((sum / scores.length) * 100) / 100; // Round to 2 decimal places
};

// Feedback utilities object
export const feedbackUtils = {
  // Format feedback data for display purposes
  formatFeedbackForDisplay: (feedback: InterviewFeedback) => {
    return {
      ...feedback,
      ratingLabel: getRatingLabel(feedback.rating),
      ratingColor: getRatingColor(feedback.rating),
      technicalScoreLabel: getScoreLabel(feedback.technical_score),
      technicalScoreColor: getScoreColor(feedback.technical_score),
      communicationScoreLabel: getScoreLabel(feedback.communication_score),
      communicationScoreColor: getScoreColor(feedback.communication_score),
      culturalFitScoreLabel: getScoreLabel(feedback.cultural_fit_score),
      culturalFitScoreColor: getScoreColor(feedback.cultural_fit_score),
      overallScoreLabel: getScoreLabel(feedback.overall_score),
      overallScoreColor: getScoreColor(feedback.overall_score),
      formattedCreatedAt: new Date(feedback.created_at).toLocaleDateString(),
      formattedUpdatedAt: new Date(feedback.updated_at).toLocaleDateString(),
    };
  },

  // Check if feedback is complete (has all required fields)
  isFeedbackComplete: (feedback: InterviewFeedback): boolean => {
    return !!(
      feedback.rating !== null &&
      feedback.comments &&
      feedback.recommend !== null &&
      (feedback.technical_score !== null ||
        feedback.communication_score !== null ||
        feedback.cultural_fit_score !== null)
    );
  },

  // Generate a summary of the feedback
  generateFeedbackSummary: (feedback: InterviewFeedback): string => {
    const parts: string[] = [];

    if (feedback.rating !== null) {
      parts.push(`Rating: ${getRatingLabel(feedback.rating)}`);
    }

    if (feedback.recommend !== null) {
      parts.push(`Recommendation: ${feedback.recommend ? "Recommend" : "Do not recommend"}`);
    }

    if (feedback.next_round_recommendation) {
      const nextRound = NEXT_ROUND_OPTIONS.find(
        (option) => option.value === feedback.next_round_recommendation
      );
      if (nextRound) {
        parts.push(`Next: ${nextRound.label}`);
      }
    }

    if (feedback.overall_score > 0) {
      parts.push(`Overall: ${getScoreLabel(feedback.overall_score)}`);
    }

    return parts.length > 0 ? parts.join(" • ") : "No feedback provided";
  },
};
