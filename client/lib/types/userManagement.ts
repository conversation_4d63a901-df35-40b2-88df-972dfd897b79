// User Management Types for HireFlow ATS

export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  role_display_name: string;
  department: string;
  title: string;
  phone: string;
  avatar?: string;
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
  roles?: Role[];
  permissions?: string[];
  initials: string;
  is_interviewer: boolean;
  statistics?: UserStatistics;
}

export interface Role {
  id: number;
  name: string;
  display_name: string;
}

export interface UserStatistics {
  created_candidates_count: number;
  assigned_candidates_count: number;
  created_job_postings_count: number;
  managed_job_postings_count: number;
  recruited_job_postings_count: number;
  created_interviews_count: number;
}

export interface UserListResponse {
  data: User[];
  meta: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  links: {
    first: string;
    last: string;
    prev?: string;
    next?: string;
  };
}

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  role: string;
  department?: string;
  title?: string;
  phone?: string;
  is_active?: boolean;
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  password?: string;
  password_confirmation?: string;
  role?: string;
  department?: string;
  title?: string;
  phone?: string;
  is_active?: boolean;
}

export interface UserFilters {
  role?: string;
  department?: string;
  is_active?: boolean;
  name?: string;
  email?: string;
}

export interface UserListParams {
  page?: number;
  per_page?: number;
  filter?: UserFilters;
  sort?: string;
  include?: string;
}

export interface TeamStatistics {
  total_users: number;
  active_users: number;
  inactive_users: number;
  by_role: {
    admin: number;
    recruiter: number;
    hiring_manager: number;
    interviewer: number;
  };
  recent_logins: number;
  never_logged_in: number;
}

// Role constants
export const USER_ROLES = {
  admin: 'admin',
  recruiter: 'recruiter',
  hiring_manager: 'hiring_manager',
  interviewer: 'interviewer',
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];

// Vietnamese role display names
export const ROLE_DISPLAY_NAMES: Record<UserRole, string> = {
  admin: 'Quản trị viên',
  recruiter: 'Nhà tuyển dụng',
  hiring_manager: 'Quản lý tuyển dụng',
  interviewer: 'Người phỏng vấn',
};

// Sort options
export const SORT_OPTIONS = [
  { value: 'name', label: 'Tên' },
  { value: 'email', label: 'Email' },
  { value: 'role', label: 'Vai trò' },
  { value: 'department', label: 'Phòng ban' },
  { value: 'created_at', label: 'Ngày tạo' },
  { value: 'last_login_at', label: 'Đăng nhập cuối' },
];

// Common departments
export const DEPARTMENTS = [
  'HR',
  'IT',
  'Marketing',
  'Sales',
  'Finance',
  'Operations',
  'Engineering',
  'Design',
  'Product',
  'Legal',
];
