// TypeScript interfaces for interviewer management based on API documentation

export interface InterviewerUser {
  id: number;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  initials: string;
  title: string;
  is_active: boolean;
}

export interface InterviewerStatistics {
  total_interviews: number;
  completed_interviews: number;
  scheduled_interviews: number;
  upcoming_interviews: number;
  average_rating: number;
}

export interface TimeSlot {
  start_time: string;
  end_time: string;
}

export interface InterviewerAvailability {
  [key: string]: TimeSlot[];
}

export interface Interviewer {
  id: number;
  user_id: number;
  name: string;
  email: string;
  department: string;
  expertise: string[];
  expertise_list: string;
  location: string;
  max_interviews_per_day: number;
  availability: InterviewerAvailability;
  time_slots: string[];
  timezone: string;
  is_active: boolean;
  user: InterviewerUser;
  statistics?: InterviewerStatistics;
  created_at: string;
  updated_at: string;
}

export interface InterviewerFilters {
  department?: string;
  is_active?: boolean;
  expertise?: string;
  include_stats?: boolean;
  include_interviews?: boolean;
}

export interface InterviewerCreateRequest {
  user_id: number;
  department?: string;
  expertise?: string[];
  location?: string;
  max_interviews_per_day?: number;
  availability?: InterviewerAvailability;
  time_slots?: string[];
  timezone?: string;
  is_active?: boolean;
}

export interface InterviewerUpdateRequest {
  user_id?: number;
  department?: string;
  expertise?: string[];
  location?: string;
  max_interviews_per_day?: number;
  availability?: InterviewerAvailability;
  time_slots?: string[];
  timezone?: string;
  is_active?: boolean;
}

export interface InterviewerResponse {
  data: Interviewer[];
}

export interface SingleInterviewerResponse {
  status: string;
  data: Interviewer;
}

export interface InterviewerDropdownOption {
  value: number;
  label: string;
  department: string;
  expertise: string[];
  maxPerDay: number;
  isActive: boolean;
  avatar?: string;
  initials: string;
}

// Form validation types
export interface InterviewerFormData {
  user_id: number | string;
  department: string;
  expertise: string[];
  location: string;
  max_interviews_per_day: number;
  timezone: string;
  is_active: boolean;
  availability: null;
  time_slots: string[];
}

export interface InterviewerValidationErrors {
  user_id?: string[];
  department?: string[];
  expertise?: string[];
  location?: string[];
  max_interviews_per_day?: string[];
  timezone?: string[];
  is_active?: string[];
  availability?: string[];
  time_slots?: string[];
  form?: string[];
}

// Constants
export const DAYS_OF_WEEK = [
  { key: "monday", label: "Thứ 2" },
  { key: "tuesday", label: "Thứ 3" },
  { key: "wednesday", label: "Thứ 4" },
  { key: "thursday", label: "Thứ 5" },
  { key: "friday", label: "Thứ 6" },
  { key: "saturday", label: "Thứ 7" },
  { key: "sunday", label: "Chủ nhật" },
] as const;

export const DEPARTMENTS = [
  "Phòng Phần mềm",
  "Phòng Kinh doanh",
  "Phòng Hành chính nhân sự",
  "Phòng Tài chính kế toán",
  "Phòng Marketing",
  "Phòng Vận hành",
  "Phòng Giải pháp kỹ thuật",
  "VP Hà Nội",
] as const;

export const COMMON_EXPERTISE = [
  "JavaScript",
  "TypeScript",
  "React",
  "Vue.js",
  "Angular",
  "Node.js",
  "Python",
  "Java",
  "C#",
  "PHP",
  "Go",
  "Rust",
  "System Design",
  "Database Design",
  "Frontend Architecture",
  "Backend Architecture",
  "DevOps",
  "AWS",
  "Azure",
  "GCP",
  "Docker",
  "Kubernetes",
  "CI/CD",
  "Product Strategy",
  "User Research",
  "UX Design",
  "UI Design",
  "Agile",
  "Project Management",
  "Team Leadership",
  "Cultural Fit",
  "Behavioral Interview",
  "Technical Assessment",
] as const;

export const TIME_SLOTS = [
  "08:00",
  "08:30",
  "09:00",
  "09:30",
  "10:00",
  "10:30",
  "11:00",
  "11:30",
  "12:00",
  "12:30",
  "13:00",
  "13:30",
  "14:00",
  "14:30",
  "15:00",
  "15:30",
  "16:00",
  "16:30",
  "17:00",
  "17:30",
  "18:00",
  "18:30",
  "19:00",
  "19:30",
] as const;

export const TIMEZONES = [
  "Asia/Ho_Chi_Minh",
  "Asia/Bangkok",
  "Asia/Singapore",
  "Asia/Seoul",
  "Asia/Tokyo",
  "UTC",
] as const;
