export interface ExtractedExperience {
  company: string;
  position: string;
  duration: string;
  description: string;
}

export interface ExtractedEducation {
  institution: string;
  degree: string;
  graduation_year: string;
}

export interface ExtractedInformation {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  skills?: string[];
  experience?: ExtractedExperience[];
  education?: ExtractedEducation[];
}

export interface ProcessingInfo {
  started_at: string;
  completed_at: string;
  duration_seconds: number;
}

export interface ResumeExtractionData {
  id: number;
  candidate_id: number;
  analysis_type: "resume_extraction";
  status: "pending" | "processing" | "completed" | "failed";
  extracted_information: ExtractedInformation;
  processing: ProcessingInfo;
  created_at: string;
}

export interface ResumeExtractionResponse {
  status: "success" | "error";
  message: string;
  data: ResumeExtractionData;
}

export interface ResumeExtractionRequest {
  candidate_id: number;
}

export interface ExtractedFieldSelection {
  name: boolean;
  email: boolean;
  phone: boolean;
  address: boolean;
  skills: boolean;
  experience: boolean;
  education: boolean;
}

export interface ResumeExtractionError {
  status: "error";
  message: string;
  errors?: Record<string, string[]>;
}
