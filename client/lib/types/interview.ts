// Interview scheduling types based on HireFlow API v2.0.1

export interface Interviewer {
  id: number;
  user_id: number;
  name: string;
  email: string;
  department: string;
  expertise: string[];
  expertise_list: string;
  location: string;
  max_interviews_per_day: number;
  availability: {
    [key: string]: Array<{
      start_time: string;
      end_time: string;
    }>;
  };
  time_slots: string[];
  timezone: string;
  is_active: boolean;
  user: {
    id: number;
    name: string;
    email: string;
    phone: string;
    avatar?: string;
    initials: string;
    title: string;
    is_active: boolean;
  };
  statistics?: {
    total_interviews: number;
    completed_interviews: number;
    scheduled_interviews: number;
    upcoming_interviews: number;
    average_rating: number;
  };
  created_at: string;
  updated_at: string;
}

export interface InterviewerResponse {
  data: Interviewer[];
}

export interface SingleInterviewerResponse {
  status: string;
  data: Interviewer;
}

export interface InterviewCreateRequest {
  candidate_id: number;
  job_posting_id: number;
  interviewer_id: number;
  date: string; // YYYY-MM-DD format
  time: string; // HH:MM format
  duration: number; // minutes
  type: "video" | "phone" | "in-person";
  interview_type?:
    | "screening"
    | "technical"
    | "case-study"
    | "portfolio"
    | "cultural"
    | "final";
  location?: string;
  meeting_link?: string;
  meeting_password?: string;
  notes?: string;
  agenda?: string[];
  round?: number;
  status?: "scheduled" | "completed" | "cancelled" | "rescheduled" | "no-show";
}

export interface InterviewCreateResponse {
  status: "success" | "error";
  message: string;
  data?: {
    id: number;
    candidate_id: number;
    job_posting_id: number;
    interviewer_id: number;
    scheduled_at: string;
    duration: number;
    type: string;
    interview_type: string;
    location?: string;
    meeting_link?: string;
    meeting_password?: string;
    notes?: string;
    agenda?: string[];
    round: number;
    status: string;
    reminder_sent: boolean;
    created_at: string;
    updated_at: string;
    // Related data
    candidate?: {
      id: number;
      name: string;
      email: string;
      position: string;
      rating?: number;
      ai_score?: number;
    };
    job_posting?: {
      id: number;
      title: string;
      department: string;
      location: string;
      priority: string;
    };
    interviewer?: Interviewer;
  };
  errors?: Record<string, string[]>;
}

export interface AvailabilityCheckRequest {
  interviewer_id: string;
  date: string;
  time: string;
  duration: number;
}

export interface AvailabilityCheckResponse {
  status: "success" | "error";
  data: {
    interviewer_id: number;
    interviewer_name: string;
    date: string;
    is_available: boolean;
    remaining_capacity: number;
    existing_interviews: Array<{
      start_time: string;
      end_time: string;
      candidate_name?: string;
    }>;
    conflicts?: Array<{
      start_time: string;
      end_time: string;
      reason: string;
    }>;
  };
  message?: string;
}

export interface CandidateOption {
  id: string;
  name: string;
  email: string;
  position: string;
  status: string;
  initials: string;
  avatar?: string;
  rating?: number;
  ai_score?: number;
  job_posting_id?: number;
  job_title?: string;
}

export interface JobPostingOption {
  id: string;
  title: string;
  department: string;
  location: string;
  status: string;
  priority: "low" | "medium" | "high" | "urgent";
  hiring_manager?: string;
  recruiter?: string;
}

export interface InterviewerOption {
  id: string;
  name: string;
  email: string;
  department: string;
  expertise: string[];
  title: string;
  avatar?: string;
  initials: string;
  is_active: boolean;
  max_interviews_per_day: number;
  upcoming_interviews?: number;
  availability_slots?: string[];
}

export interface SchedulingFormData {
  candidate_id: string;
  job_posting_id: string;
  interviewer_id: string;
  date: Date;
  time: string;
  duration: number;
  type: "video" | "phone" | "in-person";
  interview_type:
    | "screening"
    | "technical"
    | "case-study"
    | "portfolio"
    | "cultural"
    | "final";
  location: string;
  meeting_link: string;
  meeting_password: string;
  notes: string;
  agenda: string[];
  round: number;
}

export interface ValidationErrors {
  candidate_id?: string[];
  job_posting_id?: string[];
  interviewer_id?: string[];
  date?: string[];
  time?: string[];
  duration?: string[];
  type?: string[];
  location?: string[];
  meeting_link?: string[];
  form?: string[];
}

// Constants for form validation and UI
export const INTERVIEW_TYPES = [
  { value: "video", label: "Video Call", icon: "Video" },
  { value: "phone", label: "Phone Call", icon: "Phone" },
  { value: "in-person", label: "In-Person", icon: "MapPin" },
] as const;

export const INTERVIEW_CATEGORIES = [
  { value: "screening", label: "Screening" },
  { value: "technical", label: "Technical" },
  { value: "case-study", label: "Case Study" },
  { value: "portfolio", label: "Portfolio Review" },
  { value: "cultural", label: "Cultural Fit" },
  { value: "final", label: "Final Interview" },
] as const;

export const DURATION_OPTIONS = [
  { value: 15, label: "15 minutes" },
  { value: 30, label: "30 minutes" },
  { value: 45, label: "45 minutes" },
  { value: 60, label: "1 hour" },
  { value: 90, label: "1.5 hours" },
  { value: 120, label: "2 hours" },
] as const;

export const TIME_SLOTS = [
  "08:00",
  "08:30",
  "09:00",
  "09:30",
  "10:00",
  "10:30",
  "11:00",
  "11:30",
  "12:00",
  "12:30",
  "13:00",
  "13:30",
  "14:00",
  "14:30",
  "15:00",
  "15:30",
  "16:00",
  "16:30",
  "17:00",
  "17:30",
  "18:00",
] as const;
