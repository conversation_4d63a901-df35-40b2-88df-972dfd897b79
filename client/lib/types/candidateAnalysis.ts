export interface AIAnalysisRequest {
  candidate_id: number;
  job_posting_id?: number;
}

export interface AIAnalysisData {
  summary: string;
  strengths: string[];
  weaknesses: string[];
  improvement_areas: string[];
  recommendations: string[];
}

export interface AnalysisScores {
  overall: number;
  skills: number;
  experience: number;
  education: number;
  cultural_fit: number;
  average: number;
}

export interface JobMatchDetails {
  match_percentage: number;
  key_alignments: string[];
}

export interface JobMatching {
  match_details: JobMatchDetails;
  missing_requirements: string[];
  matching_criteria: string[];
}

export interface CandidateAnalysisData {
  id: number;
  candidate_id: number;
  job_posting_id?: number;
  analysis_type: "ai_analysis" | "job_matching";
  status: "pending" | "processing" | "completed" | "failed";
  ai_analysis: AIAnalysisData;
  scores: AnalysisScores;
  job_matching?: JobMatching;
  processing?: {
    started_at: string;
    completed_at?: string;
    duration_seconds?: number;
  };
  created_at: string;
}

export interface CandidateAnalysisResponse {
  status: "success" | "error";
  message: string;
  data: CandidateAnalysisData;
}

export interface AnalysisError {
  status: "error";
  message: string;
  errors?: Record<string, string[]>;
}
