import { useUpdateCandidateStatus } from "@/hooks/useApi";
import { toast } from "sonner";

/**
 * Status update contexts for generating appropriate notes
 */
export type StatusUpdateContext =
  | "pipeline" // From pipeline drag & drop
  | "modal" // From candidate detail modal
  | "bulk" // From bulk actions
  | "manual" // Manual status change
  | "automation"; // From automated rules

/**
 * Generate contextual notes for status updates
 */
export const generateStatusUpdateNotes = (
  newStatus: string,
  context: StatusUpdateContext,
  additionalInfo?: string,
): string => {
  const baseMessage = `Status changed to ${newStatus}`;

  const contextMessages = {
    pipeline: `${baseMessage} via pipeline drag & drop`,
    modal: `${baseMessage} via candidate detail modal`,
    bulk: `${baseMessage} via bulk action`,
    manual: `${baseMessage} manually`,
    automation: `${baseMessage} via automation rules`,
  };

  const contextMessage = contextMessages[context] || baseMessage;

  return additionalInfo
    ? `${contextMessage}. ${additionalInfo}`
    : contextMessage;
};

/**
 * Update candidate status with proper error handling and notifications
 */
export const updateCandidateStatus = async (
  mutation: ReturnType<typeof useUpdateCandidateStatus>,
  candidateId: string,
  candidateName: string,
  newStatus: string,
  context: StatusUpdateContext = "manual",
  additionalNotes?: string,
): Promise<boolean> => {
  try {
    const notes = generateStatusUpdateNotes(
      newStatus,
      context,
      additionalNotes,
    );

    await mutation.mutateAsync({
      id: candidateId,
      status: newStatus,
      notes,
    });

    toast.success(
      `Đã cập nhật trạng thái của ${candidateName} thành ${newStatus}`,
    );

    return true;
  } catch (error) {
    toast.error(`Không thể cập nhật trạng thái cho ${candidateName}`);
    console.error("Status update error:", error);
    return false;
  }
};

/**
 * Update multiple candidates' status with bulk operation
 */
export const updateMultipleCandidatesStatus = async (
  mutation: ReturnType<typeof useUpdateCandidateStatus>,
  candidates: Array<{ id: string; name: string }>,
  newStatus: string,
  additionalNotes?: string,
): Promise<{ success: number; failed: number }> => {
  const notes = generateStatusUpdateNotes(newStatus, "bulk", additionalNotes);
  let success = 0;
  let failed = 0;

  const results = await Promise.allSettled(
    candidates.map((candidate) =>
      mutation.mutateAsync({
        id: candidate.id,
        status: newStatus,
        notes,
      }),
    ),
  );

  results.forEach((result, index) => {
    if (result.status === "fulfilled") {
      success++;
    } else {
      failed++;
      console.error(
        `Failed to update ${candidates[index].name}:`,
        result.reason,
      );
    }
  });

  if (success > 0) {
    toast.success(
      `Đã cập nhật trạng thái cho ${success} ứng viên thành ${newStatus}`,
    );
  }

  if (failed > 0) {
    toast.error(`Không thể cập nhật ${failed} ứng viên`);
  }

  return { success, failed };
};

/**
 * Status display utilities
 */
export const getStatusDisplayName = (status: string): string => {
  const statusNames: Record<string, string> = {
    sourced: "Tiếp cận",
    applied: "Đã ứng tuyển",
    screening: "Sàng lọc",
    interview: "Phỏng vấn",
    offer: "Đề nghị",
    hired: "Đã tuyển",
    rejected: "Từ chối",
  };

  return statusNames[status] || status;
};

export const getStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    sourced: "bg-gray-100 text-gray-800 border-gray-200",
    applied: "bg-blue-100 text-blue-800 border-blue-200",
    screening: "bg-yellow-100 text-yellow-800 border-yellow-200",
    interview: "bg-purple-100 text-purple-800 border-purple-200",
    offer: "bg-orange-100 text-orange-800 border-orange-200",
    hired: "bg-green-100 text-green-800 border-green-200",
    rejected: "bg-red-100 text-red-800 border-red-200",
  };

  return statusColors[status] || "bg-gray-100 text-gray-800 border-gray-200";
};
