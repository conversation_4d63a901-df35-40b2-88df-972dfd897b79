import React, { createContext, useContext, useEffect, useState } from "react";
import { apiService } from "./api";

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  department: string;
  permissions: string[];
  is_interviewer: boolean;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  isLoading: boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(
    typeof window !== "undefined" ? localStorage.getItem("auth_token") : null,
  );
  const [isLoading, setIsLoading] = useState(true);

  const login = async (email: string, password: string) => {
    try {
      const response = await apiService.login(email, password);
      if (response.status === "success") {
        setUser(response.data.user);
        setToken(response.data.token);
        apiService.setToken(response.data.token);
      } else {
        throw new Error(response.message || "Login failed");
      }
    } catch (error: any) {
      console.error("Login error:", error);
      // Re-throw with a cleaner error message
      const errorMessage = error.message || "Login failed. Please try again.";
      throw new Error(errorMessage);
    }
  };

  const logout = () => {
    apiService.logout().finally(() => {
      setUser(null);
      setToken(null);
      apiService.clearToken();
    });
  };

  const hasPermission = (permission: string) => {
    if (!user) return false;
    return user.permissions.includes(permission) || user.role === "admin";
  };

  useEffect(() => {
    const initAuth = async () => {
      if (token) {
        try {
          apiService.setToken(token);
          const response = await apiService.getCurrentUser();
          if (response.status === "success") {
            setUser(response.data.user);
          } else {
            // Token expired or invalid
            setToken(null);
            apiService.clearToken();
          }
        } catch (error) {
          console.error("Auth init error:", error);
          setToken(null);
          apiService.clearToken();
        }
      }
      setIsLoading(false);
    };

    initAuth();
  }, [token]);

  // Sync token changes with ApiService
  useEffect(() => {
    if (token) {
      apiService.setToken(token);
    } else {
      apiService.clearToken();
    }
  }, [token]);

  const value: AuthContextType = {
    user,
    token,
    login,
    logout,
    isAuthenticated: !!user,
    isLoading,
    hasPermission,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Protected Route Component
export const ProtectedRoute: React.FC<{
  children: React.ReactNode;
  permission?: string;
}> = ({ children, permission }) => {
  const { isAuthenticated, hasPermission, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginRedirect />;
  }

  if (permission && !hasPermission(permission)) {
    return <UnauthorizedPage />;
  }

  return <>{children}</>;
};

const LoginRedirect = () => {
  useEffect(() => {
    window.location.href = "/login";
  }, []);

  return (
    <div className="flex h-screen items-center justify-center">
      <div>Redirecting to login...</div>
    </div>
  );
};

const UnauthorizedPage = () => (
  <div className="flex h-screen items-center justify-center">
    <div className="text-center">
      <h1 className="text-2xl font-bold text-red-600">Unauthorized</h1>
      <p>You don't have permission to access this page.</p>
    </div>
  </div>
);
