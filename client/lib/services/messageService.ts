import { apiService } from "@/lib/api";
import { config } from "@/lib/config";

export interface Message {
  id: number;
  type: "email" | "sms" | "note";
  type_name: string;
  category:
    | "interview"
    | "offer"
    | "feedback"
    | "reminder"
    | "rejection"
    | "general";
  category_name: string;
  candidate_id: number;
  job_posting_id?: number;
  template_id?: number;
  parent_message_id?: number;
  thread_id?: string;
  to_email?: string;
  to_phone?: string;
  to_name?: string;
  from_email?: string;
  from_name?: string;
  subject?: string;
  content: string;
  status: "draft" | "queued" | "sent" | "delivered" | "read" | "failed";
  status_name: string;
  status_color: string;
  priority: number;
  is_scheduled: boolean;
  is_due: boolean;
  delivery_time?: number;
  read_time?: number;
  scheduled_at?: string;
  sent_at?: string;
  delivered_at?: string;
  read_at?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
  external_id?: string;
  candidate?: {
    id: number;
    name: string;
    email: string;
    phone?: string;
    position?: string;
  };
  template?: {
    id: number;
    name: string;
    category: string;
    type: string;
    version: number;
  };
  replies_count: number;
}

export interface MessageThread {
  thread_id: string;
  total_messages: number;
  messages: Message[];
}

export interface MessageStatistics {
  total_messages: number;
  by_status: Record<string, number>;
  by_type: Record<string, number>;
  by_category: Record<string, number>;
  success_rate: number;
  average_delivery_time: number;
}

export interface CreateMessageData {
  type: "email" | "sms" | "note";
  category: string;
  candidate_id: number;
  job_posting_id?: number;
  template_id?: number;
  to_email?: string;
  to_phone?: string;
  to_name?: string;
  from_email?: string;
  from_name?: string;
  subject?: string;
  content: string;
  priority?: number;
  scheduled_at?: string;
  metadata?: Record<string, any>;
  template_data?: Record<string, any>;
}

export interface UpdateMessageData {
  status?: string;
  external_id?: string;
  metadata?: Record<string, any>;
}

export interface ReplyMessageData {
  type: "email" | "sms" | "note";
  category: string;
  to_email?: string;
  to_phone?: string;
  to_name?: string;
  subject?: string;
  content: string;
  priority?: number;
  metadata?: Record<string, any>;
}

export interface BulkSendData {
  template_id: number;
  candidate_ids: number[];
  job_posting_id?: number;
  scheduled_at?: string;
  priority?: number;
  metadata?: Record<string, any>;
}

export interface BulkSendResult {
  total_sent: number;
  total_failed: number;
  success: Array<{
    id: number;
    candidate_id: number;
    status: string;
    scheduled_at?: string;
  }>;
  failed: Array<{
    candidate_id: number;
    candidate_name: string;
    error: string;
  }>;
}

export interface MessageListParams {
  page?: number;
  per_page?: number;
  candidate_id?: number;
  job_posting_id?: number;
  type?: string;
  category?: string;
  status?: string;
  template_id?: number;
  thread_id?: string;
  priority_min?: number;
  priority_max?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
  sort?: string;
  include?: string;
}

export interface MessageListResponse {
  data: Message[];
  links: {
    first: string;
    last: string;
    prev?: string;
    next?: string;
  };
  meta: {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
    from: number;
    to: number;
  };
}

export const messageService = {
  // Get messages list with filtering and pagination
  async getMessages(
    params: MessageListParams = {},
  ): Promise<MessageListResponse> {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });

    const response = await apiService.request(
      `/messages?${queryParams.toString()}`,
    );
    return response;
  },

  // Get single message
  async getMessage(id: number): Promise<{ status: string; data: Message }> {
    const response = await apiService.request(`/messages/${id}`);
    return response;
  },

  // Send new message
  async sendMessage(
    data: CreateMessageData,
  ): Promise<{ status: string; message: string; data: Message }> {
    const response = await apiService.request("/messages", {
      method: "POST",
      body: JSON.stringify(data),
    });
    return response;
  },

  // Update message status
  async updateMessage(
    id: number,
    data: UpdateMessageData,
  ): Promise<{ status: string; message: string; data: Partial<Message> }> {
    const response = await apiService.request(`/messages/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
    return response;
  },

  // Delete message
  async deleteMessage(
    id: number,
  ): Promise<{ status: string; message: string }> {
    const response = await apiService.request(`/messages/${id}`, {
      method: "DELETE",
    });
    return response;
  },

  // Send bulk messages
  async sendBulkMessages(
    data: BulkSendData,
  ): Promise<{ status: string; message: string; data: BulkSendResult }> {
    const response = await apiService.request("/messages/bulk-send", {
      method: "POST",
      body: JSON.stringify(data),
    });
    return response;
  },

  // Get message thread
  async getMessageThread(
    id: number,
  ): Promise<{ status: string; data: MessageThread }> {
    const response = await apiService.request(`/messages/${id}/thread`);
    return response;
  },

  // Reply to message
  async replyToMessage(
    id: number,
    data: ReplyMessageData,
  ): Promise<{ status: string; message: string; data: Message }> {
    const response = await apiService.request(`/messages/${id}/reply`, {
      method: "POST",
      body: JSON.stringify(data),
    });
    return response;
  },

  // Get message statistics
  async getMessageStatistics(params?: {
    date_from?: string;
    date_to?: string;
  }): Promise<{ status: string; data: MessageStatistics }> {
    try {
      const queryParams = new URLSearchParams();

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value));
          }
        });
      }

      const response = await apiService.request(
        `/messages/statistics?${queryParams.toString()}`,
      );
      return response;
    } catch (error) {
      // Enhance error message for common backend issues
      if (error instanceof Error && error.message.includes('diffInSeconds')) {
        throw new Error('Backend database has timestamp data integrity issues. Please check the messages table for null timestamp fields.');
      }
      throw error;
    }
  },

  // Utility functions for Vietnamese display
  getStatusDisplayName(status: string): string {
    const statuses: Record<string, string> = {
      draft: "Bản nháp",
      queued: "Đang chờ",
      sent: "Đã gửi",
      delivered: "Đã nhận",
      read: "Đã đọc",
      failed: "Gửi lỗi",
    };

    return statuses[status] || status;
  },

  getTypeDisplayName(type: string): string {
    const types: Record<string, string> = {
      email: "Email",
      sms: "SMS",
      note: "Ghi chú",
    };

    return types[type] || type;
  },

  getCategoryDisplayName(category: string): string {
    const categories: Record<string, string> = {
      interview: "Phỏng vấn",
      offer: "Đề nghị công việc",
      feedback: "Phản hồi",
      reminder: "Nhắc nhở",
      rejection: "Từ chối",
      general: "Tổng quát",
    };

    return categories[category] || category;
  },

  getStatusColor(status: string): string {
    const colors: Record<string, string> = {
      draft: "bg-gray-100 text-gray-800 border-gray-200",
      queued: "bg-yellow-100 text-yellow-800 border-yellow-200",
      sent: "bg-blue-100 text-blue-800 border-blue-200",
      delivered: "bg-green-100 text-green-800 border-green-200",
      read: "bg-emerald-100 text-emerald-800 border-emerald-200",
      failed: "bg-red-100 text-red-800 border-red-200",
    };

    return colors[status] || "bg-gray-100 text-gray-800 border-gray-200";
  },

  getPriorityDisplayName(priority: number): string {
    if (priority >= 8) return "Rất cao";
    if (priority >= 6) return "Cao";
    if (priority >= 4) return "Trung bình";
    if (priority >= 2) return "Thấp";
    return "Rất thấp";
  },

  getPriorityColor(priority: number): string {
    if (priority >= 8) return "bg-red-100 text-red-800 border-red-200";
    if (priority >= 6) return "bg-orange-100 text-orange-800 border-orange-200";
    if (priority >= 4) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    if (priority >= 2) return "bg-blue-100 text-blue-800 border-blue-200";
    return "bg-gray-100 text-gray-800 border-gray-200";
  },
};
