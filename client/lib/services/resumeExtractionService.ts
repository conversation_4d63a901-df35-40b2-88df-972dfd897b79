import { apiService } from "@/lib/api";
import {
  ResumeExtractionRequest,
  ResumeExtractionResponse,
  ResumeExtractionError,
} from "@/lib/types/resumeExtraction";

export class ResumeExtractionService {
    private static readonly BASE_URL = "/candidate-analysis";

  /**
   * Extract resume information for a candidate
   */
  static async extractResume(
    candidateId: number,
  ): Promise<ResumeExtractionResponse> {
        try {
      const response = await apiService.request<ResumeExtractionData>(
        `${this.BASE_URL}/extract-resume`,
        {
          method: "POST",
          body: JSON.stringify({
            candidate_id: candidateId,
          } as ResumeExtractionRequest),
        }
      );

            // The API service returns: { status: "success", message: "...", data: {...} }
      if (response.status === "error") {
        throw new Error(response.message || "Resume extraction failed");
      }

      // Return the proper response structure
      const extractionResponse: ResumeExtractionResponse = {
        status: "success",
        message: response.message || "Resume extraction completed successfully",
        data: response.data as ResumeExtractionData,
      };

      return extractionResponse;
    } catch (error: any) {
            // Handle API errors from our ApiService error classes
      if (error.statusCode && error.message) {
        throw new Error(error.message || "Failed to extract resume");
      }
      
      // Handle network or other errors
      throw new Error(
        error.message || "Network error occurred while extracting resume",
      );
    }
  }

  /**
   * Check if candidate has a resume URL to extract from
   */
  static canExtractResume(candidate: { resumeUrl?: string | null }): boolean {
    return Boolean(candidate.resumeUrl);
  }

  /**
   * Format skills array from extracted data
   */
  static formatSkills(skills?: string[]): string[] {
    if (!skills || !Array.isArray(skills)) return [];
    return skills.filter(skill => skill && skill.trim().length > 0);
  }

  /**
   * Format experience text from extracted data
   */
  static formatExperience(experience?: any[]): string {
    if (!experience || !Array.isArray(experience)) return "";
    
    return experience
      .map(exp => {
        const parts = [];
        if (exp.position) parts.push(exp.position);
        if (exp.company) parts.push(`at ${exp.company}`);
        if (exp.duration) parts.push(`(${exp.duration})`);
        if (exp.description) parts.push(`\n${exp.description}`);
        return parts.join(" ");
      })
      .join("\n\n");
  }

  /**
   * Format education text from extracted data
   */
  static formatEducation(education?: any[]): string {
    if (!education || !Array.isArray(education)) return "";
    
    return education
      .map(edu => {
        const parts = [];
        if (edu.degree) parts.push(edu.degree);
        if (edu.institution) parts.push(`from ${edu.institution}`);
        if (edu.graduation_year) parts.push(`(${edu.graduation_year})`);
        return parts.join(" ");
      })
      .join("\n");
  }
}
