import { apiService } from "@/lib/api";
import { config } from "@/lib/config";

export interface MessageTemplate {
  id: number;
  name: string;
  subject: string | null;
  content: string;
  variables: string[];
  category:
    | "interview"
    | "offer"
    | "feedback"
    | "reminder"
    | "rejection"
    | "welcome";
  category_name: string;
  type: "email" | "sms";
  type_name: string;
  language: string;
  version: number;
  is_active: boolean;
  parent_template_id: number | null;
  available_variables: string[];
  extracted_variables: string[];
  is_latest_version: boolean;
  messages_count: number;
  created_at: string;
  updated_at: string;
  created_by?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface TemplatePreview {
  template: {
    id: number;
    name: string;
    category: string;
    type: string;
  };
  preview: {
    subject: string;
    content: string;
  };
  variables_used: Record<string, any>;
  missing_variables: string[];
  available_variables: string[];
}

export interface CreateTemplateData {
  name: string;
  subject?: string;
  content: string;
  variables?: string[];
  category: string;
  type: string;
  language?: string;
  is_active?: boolean;
}

export interface UpdateTemplateData {
  name?: string;
  subject?: string;
  content?: string;
  variables?: string[];
  category?: string;
  type?: string;
  language?: string;
  is_active?: boolean;
}

export interface TemplateListParams {
  page?: number;
  per_page?: number;
  category?: string;
  type?: string;
  language?: string;
  is_active?: boolean;
  search?: string;
  sort?: string;
  include?: string;
}

export const messageTemplateService = {
  async getTemplates(params: TemplateListParams = {}) {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });

    const response = await apiService.request(
      `/message-templates?${queryParams.toString()}`,
    );
    return response;
  },

  async getTemplate(id: number) {
    const response = await apiService.request(`/message-templates/${id}`);
    return response;
  },

  async createTemplate(data: CreateTemplateData) {
    const response = await apiService.request("/message-templates", {
      method: "POST",
      body: JSON.stringify(data),
    });
    return response;
  },

  async updateTemplate(id: number, data: UpdateTemplateData) {
    const response = await apiService.request(`/message-templates/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
    return response;
  },

  async deleteTemplate(id: number) {
    const response = await apiService.request(`/message-templates/${id}`, {
      method: "DELETE",
    });
    return response;
  },

  async previewTemplate(id: number, templateData: Record<string, any>) {
    const response = await apiService.request(
      `/message-templates/${id}/preview`,
      {
        method: "POST",
        body: JSON.stringify({ data: templateData }),
      },
    );
    return response;
  },

  async duplicateTemplate(id: number) {
    const response = await apiService.request(
      `/message-templates/${id}/duplicate`,
      {
        method: "POST",
      },
    );
    return response;
  },

  async getCategories() {
    const response = await apiService.request("/message-templates/categories");
    return response;
  },

  async getTypes() {
    const response = await apiService.request("/message-templates/types");
    return response;
  },

  // Extract variables from template content
  extractVariables(content: string): string[] {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const matches = [];
    let match;

    while ((match = variableRegex.exec(content)) !== null) {
      const variable = match[1].trim();
      if (!matches.includes(variable)) {
        matches.push(variable);
      }
    }

    return matches;
  },

  // Vietnamese template categories mapping
  getCategoryDisplayName(category: string): string {
    const categories: Record<string, string> = {
      interview: "Phỏng vấn",
      offer: "Đề nghị công việc",
      feedback: "Phản hồi",
      reminder: "Nhắc nhở",
      rejection: "Từ chối",
      welcome: "Chào mừng",
    };

    return categories[category] || category;
  },

  // Vietnamese template types mapping
  getTypeDisplayName(type: string): string {
    const types: Record<string, string> = {
      email: "Email",
      sms: "SMS",
    };

    return types[type] || type;
  },
};
