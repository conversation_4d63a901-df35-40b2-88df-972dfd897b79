import { Translations } from "../types";

export const viTranslations: Translations = {
  common: {
    save: "<PERSON><PERSON><PERSON>",
    cancel: "<PERSON>ủ<PERSON>",
    delete: "Xóa",
    edit: "Chỉnh sửa",
    add: "Thêm",
    search: "Tì<PERSON> kiếm",
    filter: "<PERSON>ọ<PERSON>",
    export: "Xuất",
    import: "<PERSON>hập",
    loading: "Đang tải...",
    error: "Lỗi",
    success: "Thành công",
    warning: "Cảnh báo",
    info: "Thông tin",
    confirm: "Xác nhận",
    yes: "C<PERSON>",
    no: "Không",
    close: "Đóng",
    back: "<PERSON>uay lại",
    next: "Tiế<PERSON> theo",
    previous: "Trước",
    submit: "<PERSON><PERSON><PERSON>",
    reset: "Đặt lại",
    clear: "Xóa",
    view: "Xem",
    download: "Tải xuống",
    upload: "Tải lên",
    refresh: "<PERSON>àm mới",
    settings: "Cài đặt",
    help: "<PERSON><PERSON><PERSON> giúp",
    logout: "<PERSON><PERSON><PERSON> xuất",
    profile: "<PERSON><PERSON> sơ",
    name: "<PERSON><PERSON><PERSON>",
    email: "<PERSON><PERSON>",
    phone: "<PERSON><PERSON><PERSON><PERSON> thoại",
    address: "<PERSON><PERSON><PERSON> chỉ",
    status: "<PERSON>rạng thái",
    date: "Ngày",
    time: "Thời gian",
    actions: "Hành động",
    total: "Tổng",
    active: "Hoạt động",
    inactive: "Không hoạt động",
    enabled: "Bật",
    disabled: "Tắt",
    theme: "Giao diện",
    themeLight: "Sáng",
    themeDark: "Tối",
    themeSystem: "Hệ thống",
    themeToggle: "Chuyển đổi giao diện",
    required: "Bắt buộc",
    optional: "Tùy chọn",
    all: "Tất cả",
    none: "Không có",
    select: "Chọn",
    selectAll: "Chọn tất cả",
    deselectAll: "Bỏ chọn tất cả",
    today: "Hôm nay",
    yesterday: "Hôm qua",
    tomorrow: "Ngày mai",
    thisWeek: "Tuần này",
    thisMonth: "Tháng này",
    thisYear: "Năm này",
    language: "Ngôn ngữ",
  },

  // Navigation
  nav: {
    dashboard: "Bảng điều khiển",
    candidates: "Ứng viên",
    jobs: "Việc làm",
    pipeline: "Quy trình",
    calendar: "Lịch",
    interviewers: "Người phỏng vấn",
    messages: "Tin nhắn",
    analytics: "Phân tích",
    profile: "Hồ sơ",
    team: "Đ��i nhóm",
    settings: "Cài đặt",
  },

  // Candidates
  candidates: {
    title: "Ứng viên",
    subtitle: "Quản lý và theo dõi ứng viên",
    viewDetails: "Xem chi tiết",
    sendEmail: "Gửi email",
    scheduleInterview: "Lên lịch phỏng vấn",
    updateStatus: "Cập nhật trạng thái",
    addNote: "Thêm ghi chú",
    exportProfile: "Xuất hồ sơ",
    aiSummary: "Phân tích AI",
    aiScore: "Điểm AI",
    overallMatch: "Độ phù hợp tổng thể",
    technicalSkills: "Kỹ năng kỹ thuật",
    softSkills: "Kỹ năng mềm",
    experience: "Kinh nghiệm",
    strengths: "Điểm mạnh",
    improvements: "Điểm cần cải thiện",
    recommendations: "Khuyến nghị AI",
    riskFactors: "Yếu tố rủi ro",
    nextActions: "Hành động tiếp theo",
    generateReport: "Tạo báo cáo chi tiết",
    compareWith: "So sánh với ứng viên khác",
    matchToJobs: "Khớp với vị trí khác",
    // Modal related
    addCandidate: "Thêm ứng viên",
    editCandidate: "Chỉnh sửa ứng viên",
    updateCandidate: "Cập nhật ứng viên",
    createCandidateProfile:
      "Tạo hồ sơ ứng viên mới thủ công hoặc sử dụng AI để phân tích thông tin từ CV một cách tự động.",
    updateCandidateProfile: "Cập nhật thông tin hồ sơ ứng viên và sở thích.",
    // Form fields
    fullName: "Họ và tên",
    nameRequired: "Tên là bắt buộc",
    enterFullName: "Nhập họ và tên",
    emailAddress: "Địa chỉ email",
    emailRequired: "Email là bắt buộc",
    invalidEmailFormat: "Định dạng email kh��ng hợp lệ",
    enterEmailAddress: "Nhập địa chỉ email",
    phoneNumber: "Số điện thoại",
    location: "Địa điểm",
    cityStateCountry: "Thành phố",
    generalInformation: "Thông tin chung",
    basicInformation: "Thông tin cơ bản",
    professionalInformation: "Thông tin nghề nghiệp",
    positionRole: "Vị trí/Vai trò",
    positionRequired: "Vị trí là bắt buộc",
    positionPlaceholder: "ví dụ: Lập trình viên Frontend Senior",
    experienceLevel: "Cấp độ kinh nghiệm",
    selectExperienceLevel: "Chọn cấp độ kinh nghiệm",
    entryLevel: "Cấp độ nhập môn (0-1 năm)",
    junior: "Junior (2-3 năm)",
    midLevel: "Cấp độ trung bình (3-5 năm)",
    senior: "Senior (5+ năm)",
    leadPrincipal: "Trưởng nhóm/Chính (8+ năm)",
    minSalary: "Lương tối thiểu (VND)",
    maxSalary: "Lương tối đa (VND)",
    currency: "Tiền tệ",
    source: "Nguồn",
    selectSource: "Chọn nguồn",
    applyingForPosition: "Ứng tuyển vị trí",
    selectJobPosition: "Chọn vị trí công việc",
    noSpecificPosition: "Không có vị trí cụ thể",
    skills: "Kỹ năng",
    addSkill: "Thêm kỹ năng",
    documents: "Tài liệu",
    linkedinProfile: "Hồ sơ LinkedIn",
    githubProfile: "Hồ sơ GitHub",
    notes: "Ghi chú",
    addNotes: "Thêm ghi chú bổ sung về ứng viên này...",
  },

  // Dashboard
  dashboard: {
    title: "Bảng điều khiển",
    subtitle: "Quản lý quy trình tuyển dụng của bạn",
    totalCandidates: "Tổng số ứng viên",
    newApplications: "Đơn ứng tuyển mới",
    scheduledInterviews: "Cuộc phỏng v��n đã lên lịch",
    successfulHires: "Tuyển dụng thành công",
    recentActivity: "Hoạt động gần đây",
    quickActions: "Hành động nhanh",
    addCandidate: "Thêm ứng viên",
    scheduleInterview: "Lên lịch phỏng vấn",
    reviewApplications: "Xem xét đơn ứng tuyển",
    sendMessage: "Gửi tin nhắn",
    viewAnalytics: "Xem phân tích",
    systemHealth: "Tình trạng hệ thống",
    performanceMetrics: "Chỉ số hiệu suất",
    upcomingInterviews: "Cuộc phỏng vấn sắp tới",
    pendingTasks: "Nhiệm vụ đang chờ",
    teamActivity: "Hoạt động nhóm",
    aiInsights: "Thông tin chi tiết AI",
  },

  // Status Messages
  status: {
    sourced: "Đã tìm nguồn",
    applied: "Đã ứng tuyển",
    screening: "Sàng lọc",
    interview: "Phỏng vấn",
    offer: "Đề nghị",
    hired: "Đã tuyển",
    rejected: "Từ chối",
    pending: "Đang chờ",
    inProgress: "Đang tiến hành",
    completed: "Hoàn thành",
    cancelled: "Đã hủy",
    postponed: "Hoãn lại",
    expired: "Hết hạn",
    draft: "Bản nháp",
    published: "Đã xuất bản",
    archived: "Đã lưu trữ",
    deleted: "Đã xóa",
  },

  // Toast Messages
  toast: {
    success: {
      saved: "Đã lưu thành công!",
      deleted: "Đã xóa thành công!",
      updated: "Đã cập nhật thành công!",
      created: "Đã tạo thành công!",
      sent: "Đã gửi thành công!",
      imported: "Đã nhập dữ liệu th��nh công!",
      exported: "Đã xuất dữ liệu thành công!",
      uploaded: "Đã tải lên thành công!",
      downloaded: "Đã tải xuống thành công!",
    },
    error: {
      failed: "Thao tác thất bại!",
      notFound: "Không tìm thấy!",
      unauthorized: "Không có quyền truy cập!",
      forbidden: "Bị cấm truy cập!",
      validation: "Dữ liệu không hợp lệ!",
      network: "Lỗi kết nối mạng!",
      server: "Lỗi máy chủ!",
      timeout: "Hết thời gian chờ!",
    },
    warning: {
      unsaved: "Có thay đổi chưa được lưu!",
      duplicate: "Dữ liệu đã tồn tại!",
      limit: "Đã đạt giới hạn!",
      expires: "Sắp hết hạn!",
    },
    info: {
      loading: "Đang tải...",
      processing: "Đang xử lý...",
      syncing: "Đang đồng bộ...",
      scheduled: "Đã lên lịch!",
    },
  },

  nav: {
    dashboard: "Bảng điều khiển",
    candidates: "Ứng viên",
    jobs: "Việc làm",
    pipeline: "Quy trình",
    calendar: "Lịch",
    interviewers: "Người phỏng vấn",
    messages: "Tin nhắn",
    analytics: "Phân tích",
    profile: "Hồ sơ",
    team: "Nhóm",
    settings: "Cài đặt",
  },

  dashboard: {
    title: "Bảng điều khiển",
    subtitle: "Tổng quan hệ thống tuyển dụng với AI thông minh",
    totalCandidates: "Tổng ứng viên",
    newApplications: "Đơn ứng tuyển mới",
    scheduledInterviews: "Phỏng vấn đã lên lịch",
    successfulHires: "Tuyển dụng thành công",
    recentActivity: "Hoạt động gần đây",
    quickActions: "Hành động nhanh",
    addCandidate: "Thêm ứng viên",
    scheduleInterview: "Lên lịch phỏng vấn",
    reviewApplications: "Xem xét đơn ứng tuyển",
    sendMessage: "Gửi tin nhắn",
    viewAnalytics: "Xem phân tích",
    systemHealth: "Tình tr��ng hệ thống",
    performanceMetrics: "Chỉ số hiệu suất",
    upcomingInterviews: "Phỏng vấn sắp tới",
    pendingTasks: "Nhiệm vụ đang chờ",
    teamActivity: "Hoạt động nhóm",
    aiInsights: "Thông tin AI",
  },

  status: {
    sourced: "Đã tìm hiểu",
    applied: "Đã ứng tuyển",
    screening: "Sàng lọc",
    interview: "Phỏng vấn",
    offer: "Đề nghị",
    hired: "Đã tuyển",
    rejected: "Từ chối",
    pending: "Đang chờ",
    inProgress: "Đang tiến h��nh",
    completed: "Hoàn thành",
    cancelled: "Đã hủy",
    postponed: "Hoãn lại",
    expired: "Hết hạn",
    draft: "Bản nháp",
    published: "Đã đăng",
    archived: "Lưu trữ",
    deleted: "Đã xóa",
    active: "Hoạt động",
    paused: "Tạm dừng",
    closed: "Đã đóng",
  },

  toast: {
    success: {
      saved: "Đã lưu thành công!",
      deleted: "Đã xóa thành công!",
      updated: "Đã cập nhật thành công!",
      created: "Đã tạo thành công!",
      sent: "Đã gửi thành công!",
      imported: "Đã nhập thành công!",
      exported: "Đã xuất thành công!",
      uploaded: "Đã tải lên thành công!",
      downloaded: "Đã tải xuống thành công!",
    },
    error: {
      failed: "Thao tác thất bại!",
      notFound: "Không tìm thấy!",
      unauthorized: "Không có quyền!",
      forbidden: "Bị cấm!",
      validation: "Dữ liệu không hợp lệ!",
      network: "Lỗi mạng!",
      server: "Lỗi máy chủ!",
      timeout: "Hết thời gian chờ!",
    },
    warning: {
      unsaved: "Có thay đổi chưa được lưu!",
      duplicate: "Dữ liệu trùng lặp!",
      limit: "Đã đạt giới hạn!",
      expires: "Sắp hết hạn!",
    },
    info: {
      loading: "Đang tải...",
      processing: "Đang xử lý...",
      syncing: "Đang đồng bộ...",
      scheduled: "Đ�� lên lịch!",
    },
  },

  // Landing Page
  landing: {
    hero: {
      title: "Nền tảng tuyển dụng thông minh",
      subtitle: "HireFlow - Hệ thống theo dõi ứng viên tiên tiến",
      description:
        "Tối ưu hóa quy trình tuyển dụng với AI, quản lý ứng viên hiệu quả và nâng cao trải nghiệm tuyển dụng cho doanh nghiệp hiện đại.",
      ctaPrimary: "Dùng thử miễn phí",
      ctaSecondary: "Đặt lịch demo",
      trustBadge: "Tin cậy bởi 500+ doanh nghiệp",
    },
    features: {
      title: "Tính năng nổi bật",
      subtitle: "Giải pháp toàn diện cho quản lý tuyển dụng",
      candidateManagement: {
        title: "Quản lý ứng viên",
        description:
          "Lưu trữ và tìm kiếm hồ sơ ứng viên thông minh với AI parsing và tag tự động.",
      },
      pipeline: {
        title: "Pipeline tuyển dụng",
        description:
          "Theo dõi tiến trình ứng viên qua các giai đoạn với workflow tùy chỉnh.",
      },
      interviews: {
        title: "Quản lý phỏng vấn",
        description:
          "Lên lịch phỏng vấn, đánh giá ứng viên và phản hồi từ interviewer.",
      },
      automation: {
        title: "Tự động hóa email",
        description:
          "Gửi email tự động theo tiến trình, template có sẵn và personalization.",
      },
      analytics: {
        title: "Báo cáo & Phân tích",
        description:
          "Dashboard thống kê chi tiết về hiệu quả tuyển dụng và KPI.",
      },
      collaboration: {
        title: "Cộng tác nhóm",
        description:
          "Chia sẻ feedback, ghi chú và quyết định tuyển dụng trong team.",
      },
    },
    benefits: {
      title: "Lợi ích vượt trội",
      subtitle: "So với quy trình tuyển dụng truyền thống",
      efficiency: {
        title: "Hiệu quả cao hơn 60%",
        description:
          "Giảm thời gian tuyển dụng và tăng chất lượng ứng viên với AI và automation.",
      },
      quality: {
        title: "Chất lượng tuyển dụng",
        description:
          "Đánh giá ứng viên khách quan với scoring system và structured interview.",
      },
      experience: {
        title: "Trải nghiệm ứng viên",
        description:
          "Tạo ấn tượng tích cực với quy trình minh bạch và communication nhất quán.",
      },
    },
    pricing: {
      title: "Bảng giá linh hoạt",
      subtitle: "Chọn gói phù hợp với quy mô doanh nghiệp",
      monthly: "Hàng tháng",
      yearly: "Hàng năm (Tiết kiệm 20%)",
      free: {
        name: "Miễn phí",
        price: "0đ",
        description: "Cho startup và team nhỏ",
        features: [
          "Tối đa 50 ứng viên",
          "3 job posting",
          "Pipeline cơ bản",
          "Email templates",
          "Support cộng đồng",
        ],
        cta: "Bắt đầu miễn phí",
      },
      pro: {
        name: "Pro",
        price: "299.000đ",
        description: "Cho doanh nghiệp vừa và nhỏ",
        features: [
          "Không giới hạn ứng viên",
          "Unlimited job postings",
          "Advanced pipeline",
          "AI parsing & matching",
          "Email automation",
          "Reports & analytics",
          "Priority support",
        ],
        cta: "Chọn gói Pro",
        popular: "Phổ biến nhất",
      },
      enterprise: {
        name: "Enterprise",
        price: "Liên hệ",
        description: "Cho tập đoàn và doanh nghiệp lớn",
        features: [
          "Tất cả tính năng Pro",
          "Custom workflow",
          "Advanced integrations",
          "SSO & LDAP",
          "Dedicated support",
          "Custom training",
          "SLA guarantee",
        ],
        cta: "Liên hệ tư vấn",
      },
    },
    blog: {
      title: "Tin tức & Blog",
      subtitle: "Xu hướng HR Tech và kinh nghiệm tuyển dụng",
      readMore: "Đọc thêm",
      viewAll: "Xem tất cả bài viết",
    },
    contact: {
      title: "Liên hệ với chúng tôi",
      subtitle: "Sẵn sàng hỗ trợ và tư vấn giải pháp tuyển dụng",
      form: {
        name: "Họ và tên",
        email: "Email",
        company: "Công ty",
        message: "Tin nhắn",
        send: "Gửi tin nhắn",
      },
      info: {
        address: "Tầng 10, Tòa nhà ABC, 123 Đường XYZ, Quận 1, TP.HCM",
        phone: "+84 ***********",
        email: "<EMAIL>",
        support: "Hỗ trợ 24/7",
      },
    },
  },

  analytics: {
    title: "Phân tích",
    subtitle: "Thông tin chi tiết và báo cáo về hiệu suất tuyển dụng",
    dateFilter: "Lọc theo ngày",
    exportReport: "Xuất báo cáo",
    totalCandidates: "Tổng ��ng viên",
    timeToHire: "Thời gian tuyển dụng",
    conversionRate: "Tỷ lệ chuyển đổi",
    activeJobs: "Việc làm đang hoạt động",
    pipelineHealth: "Tình trạng quy trình",
    conversionFunnel: "Phễu chuyển đổi",
    sourceEffectiveness: "Hiệu quả nguồn",
    jobPerformance: "Hiệu suất công việc",
    monthlyTrends: "Xu hướng hàng tháng",
    keyInsights: "Thông tin chính",
    recommendationsTitle: "Khuyến nghị",
    nextSteps: "Bước tiếp theo",
    successRate: "tỷ lệ thành công",
    candidates: "ứng viên",
    hired: "đã tuyển",
    applications: "Đơn ứng tuyển",
    interviews: "Phỏng vấn",
    hires: "Tuyển dụng",
    appliedToInterview: "Ứng tuyển → Phỏng vấn",
    interviewToOffer: "Phỏng vấn → Đề nghị",
    offerToHire: "Đề nghị → Tuyển dụng",
    pipelineDistribution: "Phân bố ứng viên theo giai đoạn",
    stageConversion: "Tỷ lệ chuyển đổi từng giai đoạn",
    candidateSource: "Hiệu suất theo nguồn ứng viên",
    positionPerformance: "Ứng tuyển và tuyển dụng theo vị trí",
    trendOverTime: "Ứng tuyển, phỏng vấn và tuyển dụng theo thời gian",

    insights: {
      linkedinTop: "LinkedIn là nguồn hàng đầu với 50% tổng ứng viên",
      referralsBest: "Giới thiệu có tỷ lệ chuyển đổi cao nhất ở 50%",
      frontendMost: "Vị trí Frontend Developer có nhiều đơn ứng tuyển nhất",
      interviewImproved:
        "Tỷ lệ phỏng vấn thành đề nghị cải thiện 15% tháng này",
    },

    recommendations: {
      increaseReferral:
        "Tăng cường chương trình giới thiệu để có ứng viên chất lượng cao",
      optimizeLinkedin: "Tối ưu hóa chiến lược tìm kiếm trên LinkedIn",
      reviewScreening: "Xem xét quy trình sàng lọc để chuyển đổi tốt hơn",
      expandUx: "Cân nhắc mở rộng y��u cầu cho vị trí UX Designer",
    },

    nextStepsItems: {
      scheduleReview: "Lên lịch cuộc họp đánh giá tuyển dụng hàng quý",
      updateJobs: "Cập nhật mô tả công việc dựa trên phản hồi thị trường",
      candidateSurvey: "Triển khai khảo sát trải nghiệm ứng viên",
      reviewCompensation: "Xem xét gói lương thưởng để có đề nghị cạnh tranh",
    },
  },

  // Interview Feedback Modal
  interviewFeedback: {
    title: "Phản hồi phỏng vấn",
    detailsTitle: "Chi tiết cuộc phỏng vấn",
    feedbackRequired: "Cần phản hồi",
    actionNeeded: "Cần hành động",
    submitted: "Đã gửi",
    complete: "Hoàn thành",
    incomplete: "Chưa hoàn thành",
    recommended: "Được khuyến nghị",
    notRecommended: "Không được khuyến nghị",
    noRecommendation: "Chưa có khuyến nghị",
    comments: "Nhận xét",
    detailedScores: "Điểm chi tiết",
    technical: "Kỹ thuật",
    communication: "Giao tiếp",
    cultureFit: "Phù hợp văn hóa",
    viewDetails: "Xem chi tiết",
    editFeedback: "Chỉnh sửa phản hồi",
    submitFeedback: "Gửi phản hồi",
    completeFeedbackProvided: "Đã cung cấp phản hồi đầy đủ",
    detailedAssessment: "Đánh giá chi tiết",
    feedbackIncomplete:
      "Phản hồi này chưa hoàn thành. Hãy xem xét thêm thông tin thiếu để hỗ trợ quyết định tuyển dụng.",
    interviewCompleted:
      "Cuộc phỏng vấn đã được đánh dấu là hoàn thành! Vui lòng cung cấp phản hồi để hỗ trợ quyết định tuyển dụng.",
    noFeedbackYet:
      "Cuộc phỏng vấn này đã hoàn thành nhưng chưa có phản hồi. Việc cung cấp phản hồi giúp đưa ra quyết định tuyển dụng.",
    takesMinutes: "Mất 2-3 phút để hoàn th��nh",
    helpHiringDecision: "Hỗ trợ đưa ra quyết định tuyển dụng",
    feedbackCanBeSubmitted:
      "Phản hồi có thể được gửi sau khi hoàn thành cuộc phỏng vấn.",
    interviewReady: "Sẵn sàng",
    interviewCompletedCanSubmit:
      "Cuộc phỏng vấn đã hoàn thành. Bây giờ bạn có thể gửi phản hồi.",
    editTooltip: "Chỉnh sửa phản hồi phỏng vấn của bạn",
    updateTooltip: "Cập nhật phản hồi và xếp hạng của bạn",
    submittedBy: "Được gửi bởi",
    loading: "Đang tải phản hồi...",
  },

  // Interview Status
  interviewStatus: {
    scheduled: "Đã lên lịch",
    completed: "Hoàn thành",
    cancelled: "Đã hủy",
    rescheduled: "Đã dời lịch",
    noShow: "Không đến",
  },

  // Interview Actions
  interviewActions: {
    markCompleted: "Đánh dấu hoàn thành",
    reschedule: "Dời lịch",
    cancel: "Hủy",
    confirmNewTime: "Xác nhận thời gian mới",
    reactivate: "Kích hoạt lại",
    edit: "Chỉnh sửa",
  },

  // Interview Details
  interviewDetails: {
    information: "Thông tin cuộc phỏng vấn",
    applyingFor: "Ứng tuyển cho",
    meetingDetails: "Chi tiết cuộc họp",
    joinMeeting: "Tham gia cuộc họp",
    password: "Mật khẩu",
    agenda: "Chương trình",
    notes: "Ghi chú",
    duration: "phút",
    type: {
      video: "Video",
      phone: "Điện thoại",
      inPerson: "Trực tiếp",
    },
  },

  // Messages
  messages: {
    title: "Tin nhắn",
    subtitle: "Quản lý giao tiếp với ứng viên",
    compose: "Soạn tin nhắn",
    newTemplate: "Template mới",
    inbox: "Hộp thư",
    sent: "Đã gửi",
    drafts: "Bản nháp",
    templates: "Templates",
    subject: "Tiêu đề",
    content: "Nội dung",
    recipients: "Người nhận",
    attachments: "Tệp đính kèm",
    sendMessage: "Gửi tin nhắn",
    saveDraft: "Lưu bản nháp",
    useTemplate: "Sử dụng template",
    templateName: "Tên template",
    templateCategory: "Danh mục template",
    templateVariables: "Biến template",
    previewTemplate: "Xem trước template",
    createTemplate: "Tạo template",
    editTemplate: "Chỉnh sửa template",
    deleteTemplate: "Xóa template",
    duplicateTemplate: "Sao chép template",
    // Message Template specific translations
    templateManagement: "Quản lý Template",
    templateList: "Danh sách Template",
    templateForm: "Form Template",
    templatePreview: "Xem trước Template",
    variableEditor: "Chỉnh sửa biến",
    categorySelector: "Chọn danh mục",
    // Categories
    categories: {
      interview: "Phỏng vấn",
      offer: "Đề nghị công việc",
      feedback: "Phản hồi",
      reminder: "Nhắc nhở",
      rejection: "Từ chối",
      welcome: "Chào mừng",
      followup: "Theo dõi",
      general: "Tổng quát",
    },
    // Types
    types: {
      email: "Email",
      sms: "SMS",
    },
    // Form labels
    form: {
      basicInfo: "Thông tin cơ bản",
      contentVariables: "Nội dung & Biến",
      preview: "Xem trước",
      templateName: "Tên Template",
      templateNamePlaceholder: "Nhập tên template...",
      templateNameRequired: "Tên template là bắt buộc",
      templateNameMinLength: "Tên template phải có ít nhất 3 ký tự",
      category: "Danh mục",
      categoryPlaceholder: "Chọn danh mục...",
      categoryRequired: "Vui lòng chọn danh mục",
      type: "Loại Template",
      typeRequired: "Vui lòng chọn loại template",
      language: "Ngôn ngữ",
      vietnamese: "Tiếng Việt",
      english: "English",
      emailSubject: "Tiêu đề Email",
      emailSubjectPlaceholder: "Nhập tiêu đề email...",
      emailSubjectRequired: "Tiêu đề email là bắt buộc",
      emailSubjectHelper:
        "Có thể sử dụng biến như {{candidate_name}}, {{job_title}}",
      templateContent: "Nội dung Template",
      templateContentPlaceholder:
        "Nhập nội dung template... Sử dụng {{tên_biến}} để thêm biến động",
      templateContentRequired: "Nội dung template là bắt buộc",
      templateContentMinLength: "Nội dung template phải có ít nhất 10 ký tự",
      templateContentHelper:
        "Sử dụng cú pháp {{tên_biến}} để chèn nội dung động",
      isActive: "Template hoạt động",
      variablesDetected: "Biến được phát hiện",
      variablesDetectedDescription:
        "Các biến được tự động phát hiện từ nội dung",
      commonVariables: "Biến thường dùng",
      commonVariablesDescription: "Click để chèn vào nội dung",
      insertVariable: "Chèn biến",
      createTemplate: "Tạo Template",
      updateTemplate: "Cập nhật Template",
      formErrors: "Vui lòng kiểm tra và sửa các lỗi trong form trước khi lưu",
    },
    // Success/Error messages
    notifications: {
      templateCreated: "Template đã được tạo thành công",
      templateUpdated: "Template đã được cập nhật thành công",
      templateDeleted: "Template đã được xóa thành công",
      templateDuplicated: "Template đã được sao chép thành công",
      templateApplied: "Template đã được áp dụng",
      templateCopied: "Nội dung template đã được sao chép",
      variableAdded: "Đã thêm biến",
      variableRemoved: "Đã xóa biến",
      previewGenerated: "Preview đã được tạo",
      previewCopied: "Nội dung preview đã được sao chép",
      dataReset: "Đã reset về dữ liệu mẫu",
      cannotCreateTemplate: "Không thể tạo template",
      cannotUpdateTemplate: "Không thể cập nhật template",
      cannotDeleteTemplate: "Không thể xóa template",
      cannotDuplicateTemplate: "Không thể sao chép template",
      cannotLoadTemplates: "Không thể tải danh sách template",
      cannotCopyContent: "Không thể sao chép nội dung",
      cannotGeneratePreview: "Không thể tạo preview",
      dataLoadError: "Lỗi tải dữ liệu",
      retryAction: "Thử lại",
    },
    // Variables
    variables: {
      management: "Quản lý biến Template",
      managementDescription: "Thêm và quản lý các biến động trong template",
      addVariable: "Thêm biến",
      removeVariable: "Xóa biến",
      variableName: "Tên biến",
      variableNamePlaceholder: "Nhập tên biến...",
      variableNameRequired: "Tên biến không được để trống",
      variableNameMinLength: "Tên biến phải có ít nhất 2 ký tự",
      variableNameInvalid:
        "Tên biến chỉ được chứa chữ cái, số và dấu gạch dưới, bắt đầu bằng chữ cái",
      variableExists: "Biến này đã tồn tại",
      autoDetected: "Biến tự động phát hiện",
      autoDetectedDescription: "Click để thêm vào danh sách biến",
      currentVariables: "Biến hiện tại",
      suggestions: "Gợi ý biến thường dùng",
      resetToSample: "Reset về mẫu",
      usageHelp: "Cách sử dụng biến:",
      usageHelpItems: [
        "Sử dụng cú pháp {{tên_biến}} trong nội dung template",
        "Tên biến chỉ chứa chữ cái, số và dấu gạch dưới",
        "Biến được tự động phát hiện từ nội dung template",
        "Click vào gợi ý để thêm biến nhanh",
      ],
      // Common variables
      candidateName: "Tên ứng viên",
      candidateNameDesc: "Họ tên đầy đủ của ứng viên",
      jobTitle: "Vị trí công việc",
      jobTitleDesc: "Tên vị trí tuyển dụng",
      companyName: "Tên công ty",
      companyNameDesc: "Tên công ty tuyển dụng",
      recruiterName: "Tên nhà tuyển dụng",
      recruiterNameDesc: "Người phụ trách tuyển dụng",
      recruiterEmail: "Email nhà tuyển d��ng",
      recruiterEmailDesc: "Email liên hệ",
      recruiterPhone: "SĐT nhà tuyển dụng",
      recruiterPhoneDesc: "Số điện thoại liên hệ",
      interviewDate: "Ngày phỏng vấn",
      interviewDateDesc: "Ngày diễn ra phỏng vấn",
      interviewTime: "Giờ phỏng vấn",
      interviewTimeDesc: "Thời gian phỏng vấn",
      interviewLocation: "Địa điểm phỏng vấn",
      interviewLocationDesc: "Nơi phỏng vấn",
      interviewType: "Hình thức phỏng vấn",
      interviewTypeDesc: "Trực tiếp/Online",
      department: "Phòng ban",
      departmentDesc: "Phòng ban tuyển dụng",
      salary: "Mức lương",
      salaryDesc: "Mức lương đề xuất",
      startDate: "Ngày bắt đầu",
      startDateDesc: "Ngày bắt đầu làm việc",
      offerDeadline: "Hạn trả lời",
      offerDeadlineDesc: "Hạn phản hồi offer",
    },
    // Preview
    preview: {
      title: "Xem trước Template",
      refresh: "Làm mới Preview",
      copy: "Sao chép",
      dataEditor: "Dữ liệu mẫu",
      editSampleData: "Chỉnh sửa dữ liệu mẫu",
      editSampleDataDescription:
        "Thay đổi giá trị của các biến để xem kết quả preview khác nhau",
      emailPreview: "Email Preview",
      smsPreview: "SMS Preview",
      subjectLabel: "Tiêu đề:",
      contentLabel: "Nội dung:",
      charactersCount: "ký tự",
      variablesUsed: "Biến đã sử dụng",
      variablesUsedDescription: "Các biến và giá trị tương ứng trong preview",
      missingVariables: "Một số biến chưa có dữ liệu:",
      noVariables: "Template chưa có biến nào để chỉnh sửa",
      generating: "Đang tạo preview...",
      noPreview: "Không có preview",
      previewError: "Lỗi tạo preview",
      localPreviewError: "Không thể tạo preview cục bộ",
    },
    // List
    list: {
      searchPlaceholder: "Tìm kiếm template...",
      allCategories: "Tất cả danh mục",
      allTypes: "Tất cả loại",
      allStatuses: "Tất cả",
      active: "Hoạt động",
      inactive: "Không hoạt động",
      filter: "Lọc",
      refresh: "Làm mới",
      noTemplatesFound: "Không tìm thấy template",
      adjustFilters: "Thử điều chỉnh bộ lọc tìm kiếm.",
      noTemplatesCreated: "Chưa có template nào được tạo.",
      createFirstTemplate: "Tạo Template Đầu Tiên",
      actions: "Hành động",
      useTemplate: "Sử dụng Template",
      editTemplate: "Chỉnh sửa",
      previewTemplate: "Xem trước",
      copyContent: "Sao chép nội dung",
      duplicateTemplate: "Nhân bản Template",
      deleteTemplate: "Xóa",
      confirmDelete: "Xác nhận xóa template",
      confirmDeleteMessage:
        "Bạn có chắc chắn muốn xóa template này? Hành động này không thể hoàn tác.",
      deleteAction: "Xóa",
      updated: "Cập nhật",
      variables: "biến",
      noSubject: "Không có tiêu đề",
    },
  },

  // Messages System
  messagesSystem: {
    title: "Hệ thống tin nhắn",
    subtitle: "Quản lý tin nhắn và template tuyển dụng với API integration",
    composeMessage: "Soạn tin nhắn mới",
    replyMessage: "Trả lời tin nhắn",
    forwardMessage: "Chuyển tiếp tin nhắn",
    messageDetail: "Chi tiết tin nhắn",
    messageThread: "Chuỗi hội thoại",
    sendMessage: "Gửi tin nhắn",
    saveDraft: "Lưu bản nháp",
    scheduleMessage: "Lên lịch gửi",
    bulkSend: "Gửi hàng loạt",

    // Form fields
    recipient: "Người nhận",
    subject: "Tiêu đề",
    content: "Nội dung",
    messageType: "Loại tin nhắn",
    messageCategory: "Danh mục",
    priority: "Độ ưu tiên",
    attachments: "Tệp đính kèm",
    templateData: "Dữ liệu template",

    // Message types
    types: {
      email: "Email",
      sms: "SMS",
      note: "Ghi chú",
    },

    // Message categories
    categories: {
      interview: "Phỏng vấn",
      offer: "Đề nghị công việc",
      feedback: "Phản hồi",
      reminder: "Nhắc nhở",
      rejection: "Từ chối",
      general: "Tổng quát",
    },

    // Message statuses
    statuses: {
      draft: "Bản nháp",
      queued: "Đang chờ",
      sent: "Đã gửi",
      delivered: "Đã nhận",
      read: "Đã đọc",
      failed: "Gửi lỗi",
    },

    // Priority levels
    priorities: {
      veryLow: "Rất thấp",
      low: "Thấp",
      normal: "Trung bình",
      high: "Cao",
      veryHigh: "Rất cao",
    },

    // Actions
    actions: {
      reply: "Trả lời",
      forward: "Chuyển tiếp",
      archive: "L��u trữ",
      delete: "Xóa",
      star: "Đánh dấu",
      markRead: "Đánh dấu đã đọc",
      markUnread: "Đánh dấu chưa đọc",
    },

    // Validation
    validation: {
      recipientRequired: "Vui lòng chọn người nhận",
      subjectRequired: "Tiêu đề là bắt buộc",
      contentRequired: "Nội dung tin nhắn là bắt buộc",
      invalidEmail: "Email không hợp lệ",
      replyContentRequired: "Vui lòng nhập nội dung trả lời",
    },

    // Status messages
    status: {
      messageSent: "Tin nhắn đã được gửi thành công!",
      messageScheduled: "Tin nhắn đã được lên lịch gửi!",
      draftSaved: "Bản nháp đã được lưu",
      messageDeleted: "Tin nhắn đã được xóa",
      messageArchived: "Tin nhắn đã được lưu trữ",
      messageStarred: "Tin nhắn đã được đánh dấu",
      markedAsRead: "Đã đánh dấu là đã đọc",
      replySent: "Phản hồi đã được gửi thành công!",
      bulkSentSuccess: "Tin nhắn hàng loạt đã được gửi thành công!",
    },

    // Error messages
    errors: {
      failedToSend: "Không thể gửi tin nhắn. Vui lòng thử lại.",
      failedToLoad: "Không thể tải danh sách tin nhắn. Vui lòng thử lại.",
      failedToDelete: "Không thể xóa tin nhắn",
      failedToArchive: "Không thể lưu trữ tin nhắn",
      failedToReply: "Không thể gửi phản hồi. Vui lòng thử lại.",
      failedToLoadThread: "Không thể tải chuỗi hội thoại",
      failedToPreview: "Không thể tạo preview",
      networkError: "Lỗi kết nối mạng",
      serverError: "Lỗi máy chủ",
    },

    // Empty states
    empty: {
      noMessages: "Không tìm thấy tin nhắn",
      noSearchResults: "Thử điều chỉnh từ khóa tìm kiếm.",
      noDrafts: "Bạn chưa có bản nháp nào.",
      emptyInbox: "Hộp thư của bạn trống.",
      noThread: "Đây là tin nhắn đầu tiên trong chuỗi",
    },

    // Statistics
    stats: {
      totalMessages: "Tổng số tin nhắn",
      sentMessages: "Tin nhắn đã gửi",
      inboxMessages: "Tin nhắn trong hộp thư",
      draftMessages: "Bản nháp",
      readMessages: "Tin nhắn đã đọc",
      templatesCount: "Số template",
      successRate: "Tỷ lệ thành công",
      averageDeliveryTime: "Thời gian gửi trung bình",
    },

    // Filters
    filters: {
      allTypes: "Tất cả loại",
      allStatuses: "Tất cả trạng thái",
      allCategories: "Tất cả danh mục",
      searchPlaceholder: "Tìm kiếm tin nhắn...",
      selectRecipient: "Chọn người nhận...",
    },

    // Tabs
    tabs: {
      inbox: "Hộp thư",
      sent: "Đã gửi",
      drafts: "Bản nháp",
      templates: "Templates",
    },

    // Placeholders
    placeholders: {
      enterSubject: "Nhập tiêu đề...",
      enterContent: "Nhập nội dung tin nhắn...",
      enterReply: "Nhập nội dung trả lời...",
      searchRecipients: "Tìm kiếm người nhận...",
      selectTemplate: "Chọn template...",
    },

    // File upload
    files: {
      addFiles: "Thêm tệp",
      removeFile: "Xóa tệp",
      noFiles: "Không có tệp đính kèm",
      fileSize: "Kích thước tệp",
      uploadError: "Lỗi khi tải tệp lên",
    },

    // Preview
    preview: {
      title: "Preview tin nhắn",
      templatePreview: "Preview template",
      previewSubject: "Tiêu đề:",
      previewContent: "Nội dung:",
    },
  },

  // Team Management
  team: {
    title: "Quản lý nhóm",
    subtitle: "Quản lý người dùng và phân quyền trong hệ thống",
    addMember: "Thêm thành viên",
    addUser: "Thêm người dùng",
    editUser: "Chỉnh sửa người dùng",
    viewDetails: "Xem chi tiết",
    deactivateUser: "Vô hiệu hóa người dùng",
    activateUser: "Kích hoạt người dụng",
    deleteUser: "Xóa người dùng",

    // User form
    userForm: {
      createTitle: "Thêm người dùng mới",
      editTitle: "Chỉnh sửa thông tin người dùng",
      basicInfo: "Thông tin cơ bản",
      roleAndDepartment: "Vai trò và phòng ban",
      fullName: "Họ và tên",
      fullNamePlaceholder: "Nhập họ và tên",
      emailAddress: "Địa chỉ email",
      emailPlaceholder: "<EMAIL>",
      password: "Mật khẩu",
      passwordNew: "Mật khẩu mới",
      passwordPlaceholder: "Nhập mật khẩu",
      passwordConfirm: "Xác nhận mật khẩu",
      passwordConfirmPlaceholder: "Xác nhận mật khẩu",
      passwordEmpty: "(để trống nếu không thay đổi)",
      phoneNumber: "Số điện thoại",
      phonePlaceholder: "0123456789",
      role: "Vai trò",
      roleSelect: "Chọn vai trò",
      department: "Phòng ban",
      departmentSelect: "Chọn phòng ban",
      title: "Chức vụ",
      titlePlaceholder: "Ví dụ: Senior Recruiter",
      isActive: "Tài khoản hoạt động",
      createUser: "Tạo người dùng",
      updateUser: "Cập nhật",
    },

    // Roles
    roles: {
      admin: "Quản trị viên",
      hiring_manager: "Quản lý tuyển dụng",
      recruiter: "Nhà tuyển dụng",
      interviewer: "Người phỏng vấn",
    },

    // User detail
    userDetail: {
      title: "Chi tiết người dùng",
      accountInfo: "Thông tin tài khoản",
      userId: "ID người dùng",
      interviewerStatus: "Trạng thái phỏng vấn viên",
      lastLogin: "Đăng nhập cuối",
      lastUpdate: "Ngày cập nhật",
      neverLoggedIn: "Chưa đăng nhập",
      statistics: "Thống kê hoạt động",
      candidatesCreated: "Ứng viên tạo",
      candidatesAssigned: "Ứng viên được phân",
      interviewsCreated: "Cuộc phỏng vấn",
      jobsCreated: "Tin tuyển dụng tạo",
      jobsManaged: "Tin quản lý",
      jobsRecruited: "Tin tuyển dụng",
      systemPermissions: "Quyền hệ thống",
      roleAccess: "Vai trò & quyền truy cập",
      joinedDate: "Tham gia",
      status: "Trạng thái",
    },

    // Filters and actions
    filters: {
      searchPlaceholder: "Tìm kiếm theo tên hoặc email...",
      allDepartments: "Tất cả phòng ban",
      allRoles: "Tất cả vai trò",
      allStatus: "Tất cả trạng thái",
      sortBy: "Sắp xếp",
      sortByName: "Tên",
      sortByEmail: "Email",
      sortByRole: "Vai trò",
      sortByDepartment: "Phòng ban",
      sortByCreated: "Ngày tạo",
      sortByLastLogin: "Đăng nhập cuối",
    },

    // Statistics
    stats: {
      totalUsers: "Tổng số người dùng",
      activeUsers: "Người dùng hoạt động",
      inactiveUsers: "Người dùng không hoạt động",
      adminUsers: "Quản trị viên",
      managerUsers: "Quản lý tuyển dụng",
      recruiterUsers: "Nhà tuyển dụng",
      interviewerUsers: "Người phỏng vấn",
      recentLogins: "Đăng nhập gần đây",
      neverLoggedIn: "Chưa đăng nhập bao giờ",
    },

    // Actions and buttons
    actions: {
      refresh: "Làm mới",
      exportData: "Xuất dữ liệu",
      viewDetails: "Xem chi tiết",
      editUser: "Chỉnh sửa",
      deactivate: "Vô hiệu hóa",
      activate: "Kích hoạt",
      delete: "Xóa",
      close: "Đóng",
      cancel: "Hủy",
      save: "Lưu",
      update: "Cập nhật",
      create: "Tạo",
    },

    // Status and states
    status: {
      active: "Hoạt động",
      inactive: "Không hoạt động",
      loading: "Đang tải danh sách người dùng...",
      noPermission: "Không có quyền truy cập",
      noPermissionDesc: "Bạn không có quyền xem danh sách người dùng.",
      userCount: "người dùng",
      showingUsers: "Hiển thị",
      of: "của",
      page: "Trang",
      previous: "Trước",
      next: "Sau",
    },

    // Messages and notifications
    messages: {
      userCreated: "Người dùng đã được tạo thành công!",
      userUpdated: "Thông tin người dùng đã được cập nhật!",
      userDeactivated: "Người dùng đã được vô hiệu hóa!",
      userActivated: "Người dùng đã được kích hoạt!",
      dataExported: "Dữ liệu người dùng đã được xuất!",
      cannotDeactivateSelf:
        "Bạn không thể vô hiệu hóa tài khoản của chính mình",
      cannotDeactivateUser: "Bạn không có quyền vô hiệu hóa người dùng này",
    },

    // Validation
    validation: {
      nameRequired: "Tên là bắt buộc",
      emailRequired: "Email là bắt buộc",
      emailInvalid: "Email không hợp lệ",
      passwordRequired: "Mật khẩu là bắt buộc",
      passwordMinLength: "Mật khẩu phải có ít nhất 8 ký tự",
      passwordMismatch: "Xác nhận mật khẩu không khớp",
      roleRequired: "Vai trò là bắt buộc",
    },

    // Confirmation dialogs
    confirmations: {
      deactivateTitle: "Vô hiệu hóa người dùng",
      activateTitle: "Kích hoạt người dùng",
      deactivateMessage:
        "Bạn có chắc chắn muốn vô hiệu hóa tài khoản của {name}? Họ sẽ không thể đăng nhập vào hệ thống.",
      activateMessage:
        "Bạn có chắc chắn muốn kích hoạt tài khoản của {name}? Họ sẽ có thể đăng nhập vào hệ thống.",
      deactivateAction: "Vô hiệu hóa",
      activateAction: "Kích hoạt",
      cancelAction: "Hủy",
    },

    // Contact info
    contact: {
      email: "Email",
      phone: "Điện thoại",
      noPhone: "Không có số điện thoại",
      noDepartment: "Không có phòng ban",
      noTitle: "Không có chức vụ",
    },

    // Permissions
    permissions: {
      canEdit: "Có thể chỉnh sửa",
      canView: "Có thể xem",
      adminOnly: "Chỉ quản trị viên",
      selfEdit: "Chỉnh sửa hồ sơ cá nhân",
      limitedFields: "Một số trường bị hạn chế",
    },
  },
};
