import { Translations } from "../types";

export const enTranslations: Translations = {
  common: {
    save: "Save",
    cancel: "Cancel",
    delete: "Delete",
    edit: "Edit",
    add: "Add",
    search: "Search",
    filter: "Filter",
    export: "Export",
    import: "Import",
    loading: "Loading...",
    error: "Error",
    success: "Success",
    warning: "Warning",
    info: "Info",
    confirm: "Confirm",
    yes: "Yes",
    no: "No",
    close: "Close",
    back: "Back",
    next: "Next",
    previous: "Previous",
    submit: "Submit",
    reset: "Reset",
    clear: "Clear",
    view: "View",
    download: "Download",
    upload: "Upload",
    refresh: "Refresh",
    settings: "Settings",
    help: "Help",
    logout: "Logout",
    profile: "Profile",
    name: "Name",
    email: "Email",
    phone: "Phone",
    address: "Address",
    status: "Status",
    date: "Date",
    time: "Time",
    actions: "Actions",
    total: "Total",
    active: "Active",
    inactive: "Inactive",
    enabled: "Enabled",
    disabled: "Disabled",
    theme: "Theme",
    themeLight: "Light",
    themeDark: "Dark",
    themeSystem: "System",
    themeToggle: "Toggle theme",
    required: "Required",
    optional: "Optional",
    all: "All",
    none: "None",
    select: "Select",
    selectAll: "Select All",
    deselectAll: "Deselect All",
    today: "Today",
    yesterday: "Yesterday",
    tomorrow: "Tomorrow",
    thisWeek: "This Week",
    thisMonth: "This Month",
    thisYear: "This Year",
    language: "Language",
  },

  nav: {
    dashboard: "Dashboard",
    candidates: "Candidates",
    jobs: "Jobs",
    pipeline: "Pipeline",
    calendar: "Calendar",
    interviewers: "Interviewers",
    messages: "Messages",
    analytics: "Analytics",
    profile: "Profile",
    team: "Team",
    settings: "Settings",
  },

  dashboard: {
    title: "Dashboard",
    subtitle: "Smart recruiting system overview with AI assistance",
    totalCandidates: "Total Candidates",
    newApplications: "New Applications",
    scheduledInterviews: "Scheduled Interviews",
    successfulHires: "Successful Hires",
    recentActivity: "Recent Activity",
    quickActions: "Quick Actions",
    addCandidate: "Add Candidate",
    scheduleInterview: "Schedule Interview",
    reviewApplications: "Review Applications",
    sendMessage: "Send Message",
    viewAnalytics: "View Analytics",
    systemHealth: "System Health",
    performanceMetrics: "Performance Metrics",
    upcomingInterviews: "Upcoming Interviews",
    pendingTasks: "Pending Tasks",
    teamActivity: "Team Activity",
    aiInsights: "AI Insights",
  },

  // Candidates
  candidates: {
    title: "Candidates",
    subtitle: "Manage and track candidates",
    viewDetails: "View Details",
    sendEmail: "Send Email",
    scheduleInterview: "Schedule Interview",
    updateStatus: "Update Status",
    addNote: "Add Note",
    exportProfile: "Export Profile",
    aiSummary: "AI Analysis",
    aiScore: "AI Score",
    overallMatch: "Overall Match",
    technicalSkills: "Technical Skills",
    softSkills: "Soft Skills",
    experience: "Experience",
    strengths: "Strengths",
    improvements: "Areas for Improvement",
    recommendations: "AI Recommendations",
    riskFactors: "Risk Factors",
    nextActions: "Next Actions",
    generateReport: "Generate Detailed Report",
    compareWith: "Compare with Others",
    matchToJobs: "Match to Other Positions",
    // Modal related
    addCandidate: "Add Candidate",
    editCandidate: "Edit Candidate",
    updateCandidate: "Update Candidate",
    createCandidateProfile: "Create a new candidate profile manually or use AI to parse resume information automatically.",
    updateCandidateProfile: "Update candidate profile information and preferences.",
    // Form fields
    fullName: "Full Name",
    nameRequired: "Name is required",
    enterFullName: "Enter full name",
    emailAddress: "Email Address",
    emailRequired: "Email is required",
    invalidEmailFormat: "Invalid email format",
    enterEmailAddress: "Enter email address",
    phoneNumber: "Phone Number",
    location: "Location",
    cityStateCountry: "City, State/Country",
    generalInformation: "General Information",
    basicInformation: "Basic Information",
    professionalInformation: "Professional Information",
    positionRole: "Position/Role",
    positionRequired: "Position is required",
    positionPlaceholder: "e.g., Senior Frontend Developer",
    experienceLevel: "Experience Level",
    selectExperienceLevel: "Select experience level",
    entryLevel: "Entry Level (0-1 years)",
    junior: "Junior (2-3 years)",
    midLevel: "Mid-level (3-5 years)",
    senior: "Senior (5+ years)",
    leadPrincipal: "Lead/Principal (8+ years)",
    minSalary: "Min Salary (VND)",
    maxSalary: "Max Salary (VND)",
    currency: "Currency",
    source: "Source",
    selectSource: "Select source",
    applyingForPosition: "Applying for Position",
    selectJobPosition: "Select job position",
    noSpecificPosition: "No specific position",
    skills: "Skills",
    addSkill: "Add a skill",
    documents: "Documents",
    linkedinProfile: "LinkedIn Profile",
    githubProfile: "GitHub Profile",
    notes: "Notes",
    addNotes: "Add any additional notes about this candidate...",
  },

  status: {
    sourced: "Sourced",
    applied: "Applied",
    screening: "Screening",
    interview: "Interview",
    offer: "Offer",
    hired: "Hired",
    rejected: "Rejected",
    pending: "Pending",
    inProgress: "In Progress",
    completed: "Completed",
    cancelled: "Cancelled",
    postponed: "Postponed",
    expired: "Expired",
    draft: "Draft",
    published: "Published",
    archived: "Archived",
    deleted: "Deleted",
  },

  toast: {
    success: {
      saved: "Saved successfully!",
      deleted: "Deleted successfully!",
      updated: "Updated successfully!",
      created: "Created successfully!",
      sent: "Sent successfully!",
      imported: "Imported successfully!",
      exported: "Exported successfully!",
      uploaded: "Uploaded successfully!",
      downloaded: "Downloaded successfully!",
    },
    error: {
      failed: "Operation failed!",
      notFound: "Not found!",
      unauthorized: "Unauthorized!",
      forbidden: "Forbidden!",
      validation: "Validation error!",
      network: "Network error!",
      server: "Server error!",
      timeout: "Request timeout!",
    },
    warning: {
      unsaved: "You have unsaved changes!",
      duplicate: "Duplicate data found!",
      limit: "Limit reached!",
      expires: "Will expire soon!",
    },
    info: {
      loading: "Loading...",
      processing: "Processing...",
      syncing: "Syncing...",
      scheduled: "Scheduled successfully!",
    },
  },

  // Landing Page
  landing: {
    hero: {
      title: "Smart Recruitment Platform",
      subtitle: "HireFlow - Advanced Applicant Tracking System",
      description:
        "Optimize your hiring process with AI, manage candidates effectively, and enhance recruitment experience for modern businesses.",
      ctaPrimary: "Try for Free",
      ctaSecondary: "Schedule Demo",
      trustBadge: "Trusted by 500+ companies",
    },
    features: {
      title: "Key Features",
      subtitle: "Comprehensive solution for recruitment management",
      candidateManagement: {
        title: "Candidate Management",
        description:
          "Smart storage and search of candidate profiles with AI parsing and automatic tagging.",
      },
      pipeline: {
        title: "Recruitment Pipeline",
        description:
          "Track candidate progress through stages with customizable workflows.",
      },
      interviews: {
        title: "Interview Management",
        description:
          "Schedule interviews, evaluate candidates, and collect interviewer feedback.",
      },
      automation: {
        title: "Email Automation",
        description:
          "Send automated emails based on process, ready templates and personalization.",
      },
      analytics: {
        title: "Reports & Analytics",
        description:
          "Detailed statistics dashboard on recruitment effectiveness and KPIs.",
      },
      collaboration: {
        title: "Team Collaboration",
        description:
          "Share feedback, notes and hiring decisions within the team.",
      },
    },
    benefits: {
      title: "Superior Benefits",
      subtitle: "Compared to traditional recruitment processes",
      efficiency: {
        title: "60% More Efficient",
        description:
          "Reduce hiring time and increase candidate quality with AI and automation.",
      },
      quality: {
        title: "Recruitment Quality",
        description:
          "Objective candidate evaluation with scoring system and structured interviews.",
      },
      experience: {
        title: "Candidate Experience",
        description:
          "Create positive impression with transparent process and consistent communication.",
      },
    },
    pricing: {
      title: "Flexible Pricing",
      subtitle: "Choose the plan that fits your business scale",
      monthly: "Monthly",
      yearly: "Yearly (Save 20%)",
      free: {
        name: "Free",
        price: "$0",
        description: "For startups and small teams",
        features: [
          "Up to 50 candidates",
          "3 job postings",
          "Basic pipeline",
          "Email templates",
          "Community support",
        ],
        cta: "Start Free",
      },
      pro: {
        name: "Pro",
        price: "$29",
        description: "For small and medium businesses",
        features: [
          "Unlimited candidates",
          "Unlimited job postings",
          "Advanced pipeline",
          "AI parsing & matching",
          "Email automation",
          "Reports & analytics",
          "Priority support",
        ],
        cta: "Choose Pro",
        popular: "Most Popular",
      },
      enterprise: {
        name: "Enterprise",
        price: "Contact us",
        description: "For corporations and large enterprises",
        features: [
          "All Pro features",
          "Custom workflow",
          "Advanced integrations",
          "SSO & LDAP",
          "Dedicated support",
          "Custom training",
          "SLA guarantee",
        ],
        cta: "Contact Sales",
      },
    },
    blog: {
      title: "News & Blog",
      subtitle: "HR Tech trends and recruitment insights",
      readMore: "Read more",
      viewAll: "View all articles",
    },
    contact: {
      title: "Contact Us",
      subtitle: "Ready to support and consult recruitment solutions",
      form: {
        name: "Full Name",
        email: "Email",
        company: "Company",
        message: "Message",
        send: "Send Message",
      },
      info: {
        address: "Floor 10, ABC Building, 123 XYZ Street, District 1, HCMC",
        phone: "+84 ***********",
        email: "<EMAIL>",
        support: "24/7 Support",
      },
    },
  },

  analytics: {
    title: "Analytics",
    subtitle: "Insights and reports on recruitment performance",
    dateFilter: "Date Range",
    exportReport: "Export Report",
    totalCandidates: "Total Candidates",
    timeToHire: "Time to Hire",
    conversionRate: "Conversion Rate",
    activeJobs: "Active Jobs",
    pipelineHealth: "Pipeline Health",
    conversionFunnel: "Conversion Funnel",
    sourceEffectiveness: "Source Effectiveness",
    jobPerformance: "Job Performance",
    monthlyTrends: "Monthly Trends",
    keyInsights: "Key Insights",
    recommendationsTitle: "Recommendations",
    nextSteps: "Next Steps",
    successRate: "success rate",
    candidates: "candidates",
    hired: "hired",
    applications: "Applications",
    interviews: "Interviews",
    hires: "Hires",
    appliedToInterview: "Applied → Interview",
    interviewToOffer: "Interview → Offer",
    offerToHire: "Offer → Hire",
    pipelineDistribution: "Candidates distribution across stages",
    stageConversion: "Stage-to-stage conversion rates",
    candidateSource: "Performance by candidate source",
    positionPerformance: "Applications and hires by position",
    trendOverTime: "Applications, interviews, and hires over time",

    insights: {
      linkedinTop: "LinkedIn is your top source with 50% of total candidates",
      referralsBest: "Referrals have the highest conversion rate at 50%",
      frontendMost: "Frontend Developer position has the most applications",
      interviewImproved: "Interview-to-offer rate improved by 15% this month",
    },

    recommendations: {
      increaseReferral:
        "Increase referral program to boost high-quality candidates",
      optimizeLinkedin: "Optimize LinkedIn sourcing strategy",
      reviewScreening: "Review screening process for better conversion",
      expandUx: "Consider expanding UX Designer role requirements",
    },

    nextStepsItems: {
      scheduleReview: "Schedule quarterly hiring review meeting",
      updateJobs: "Update job descriptions based on market feedback",
      candidateSurvey: "Implement candidate experience survey",
      reviewCompensation: "Review compensation packages for competitive offers",
    },
  },

  // Team Management
  team: {
    title: "Team Management",
    subtitle: "Manage users and permissions in the system",
    addMember: "Add Member",
    addUser: "Add User",
    editUser: "Edit User",
    viewDetails: "View Details",
    deactivateUser: "Deactivate User",
    activateUser: "Activate User",
    deleteUser: "Delete User",

    // User form
    userForm: {
      createTitle: "Add New User",
      editTitle: "Edit User Information",
      basicInfo: "Basic Information",
      roleAndDepartment: "Role and Department",
      fullName: "Full Name",
      fullNamePlaceholder: "Enter full name",
      emailAddress: "Email Address",
      emailPlaceholder: "<EMAIL>",
      password: "Password",
      passwordNew: "New Password",
      passwordPlaceholder: "Enter password",
      passwordConfirm: "Confirm Password",
      passwordConfirmPlaceholder: "Confirm password",
      passwordEmpty: "(leave blank if not changing)",
      phoneNumber: "Phone Number",
      phonePlaceholder: "**********",
      role: "Role",
      roleSelect: "Select role",
      department: "Department",
      departmentSelect: "Select department",
      title: "Title",
      titlePlaceholder: "e.g., Senior Recruiter",
      isActive: "Account Active",
      createUser: "Create User",
      updateUser: "Update",
    },

    // Roles
    roles: {
      admin: "Administrator",
      hiring_manager: "Hiring Manager",
      recruiter: "Recruiter",
      interviewer: "Interviewer",
    },

    // User detail
    userDetail: {
      title: "User Details",
      accountInfo: "Account Information",
      userId: "User ID",
      interviewerStatus: "Interviewer Status",
      lastLogin: "Last Login",
      lastUpdate: "Last Updated",
      neverLoggedIn: "Never logged in",
      statistics: "Activity Statistics",
      candidatesCreated: "Candidates Created",
      candidatesAssigned: "Candidates Assigned",
      interviewsCreated: "Interviews Created",
      jobsCreated: "Jobs Created",
      jobsManaged: "Jobs Managed",
      jobsRecruited: "Jobs Recruited",
      systemPermissions: "System Permissions",
      roleAccess: "Role & Access",
      joinedDate: "Joined",
      status: "Status",
    },

    // Filters and actions
    filters: {
      searchPlaceholder: "Search by name or email...",
      allDepartments: "All Departments",
      allRoles: "All Roles",
      allStatus: "All Status",
      sortBy: "Sort By",
      sortByName: "Name",
      sortByEmail: "Email",
      sortByRole: "Role",
      sortByDepartment: "Department",
      sortByCreated: "Created Date",
      sortByLastLogin: "Last Login",
    },

    // Statistics
    stats: {
      totalUsers: "Total Users",
      activeUsers: "Active Users",
      inactiveUsers: "Inactive Users",
      adminUsers: "Administrators",
      managerUsers: "Hiring Managers",
      recruiterUsers: "Recruiters",
      interviewerUsers: "Interviewers",
      recentLogins: "Recent Logins",
      neverLoggedIn: "Never Logged In",
    },

    // Actions and buttons
    actions: {
      refresh: "Refresh",
      exportData: "Export Data",
      viewDetails: "View Details",
      editUser: "Edit",
      deactivate: "Deactivate",
      activate: "Activate",
      delete: "Delete",
      close: "Close",
      cancel: "Cancel",
      save: "Save",
      update: "Update",
      create: "Create",
    },

    // Status and states
    status: {
      active: "Active",
      inactive: "Inactive",
      loading: "Loading users...",
      noPermission: "No Permission",
      noPermissionDesc: "You don't have permission to view the user list.",
      userCount: "users",
      showingUsers: "Showing",
      of: "of",
      page: "Page",
      previous: "Previous",
      next: "Next",
    },

    // Messages and notifications
    messages: {
      userCreated: "User created successfully!",
      userUpdated: "User information updated!",
      userDeactivated: "User has been deactivated!",
      userActivated: "User has been activated!",
      dataExported: "User data exported!",
      cannotDeactivateSelf: "You cannot deactivate your own account",
      cannotDeactivateUser: "You don't have permission to deactivate this user",
    },

    // Validation
    validation: {
      nameRequired: "Name is required",
      emailRequired: "Email is required",
      emailInvalid: "Email is invalid",
      passwordRequired: "Password is required",
      passwordMinLength: "Password must be at least 8 characters",
      passwordMismatch: "Password confirmation does not match",
      roleRequired: "Role is required",
    },

    // Confirmation dialogs
    confirmations: {
      deactivateTitle: "Deactivate User",
      activateTitle: "Activate User",
      deactivateMessage: "Are you sure you want to deactivate {name}'s account? They will not be able to log into the system.",
      activateMessage: "Are you sure you want to activate {name}'s account? They will be able to log into the system.",
      deactivateAction: "Deactivate",
      activateAction: "Activate",
      cancelAction: "Cancel",
    },

    // Contact info
    contact: {
      email: "Email",
      phone: "Phone",
      noPhone: "No phone number",
      noDepartment: "No department",
      noTitle: "No title",
    },

    // Permissions
    permissions: {
      canEdit: "Can edit",
      canView: "Can view",
      adminOnly: "Admin only",
      selfEdit: "Self edit",
      limitedFields: "Limited fields",
    },
  },

  // Page Titles
  pageTitle: {
    // Dashboard and Analytics
    dashboard: "Dashboard",
    analytics: "Analytics & Reports",

    // Candidates
    candidates: {
      list: "Candidates",
      detail: "{name} - Candidate Profile",
      create: "Add New Candidate",
      edit: "Edit Candidate - {name}",
      profile: "{name} - Profile",
    },

    // Jobs
    jobs: {
      list: "Job Postings",
      detail: "{title} - Job Details",
      create: "Create New Job",
      edit: "Edit Job - {title}",
      applications: "{title} - Applications",
    },

    // Pipeline
    pipeline: {
      overview: "Recruitment Pipeline",
      kanban: "Pipeline - Kanban View",
      stage: "{stage} - Pipeline Stage",
    },

    // Messages
    messages: {
      inbox: "Messages & Communications",
      conversation: "Conversation - {name}",
      compose: "Compose Message",
      templates: "Message Templates",
    },

    // Interviews
    interviews: {
      list: "Interview Schedule",
      detail: "Interview with {name}",
      schedule: "Schedule Interview",
      calendar: "Interview Calendar",
      feedback: "Interview Feedback - {name}",
    },

    // Interviewers
    interviewers: {
      list: "Interviewers",
      profile: "{name} - Interviewer Profile",
      create: "Add New Interviewer",
      edit: "Edit Interviewer - {name}",
    },

    // Settings and Configuration
    settings: {
      general: "General Settings",
      account: "Account Settings",
      team: "Team Management",
      integrations: "Integrations & API",
      notifications: "Notification Settings",
    },

    // Auth pages
    auth: {
      login: "Sign In",
      register: "Create Account",
      resetPassword: "Reset Password",
      verifyEmail: "Verify Email",
    },

    // Error pages
    error: {
      notFound: "Page Not Found",
      serverError: "Server Error",
      unauthorized: "Access Denied",
    },
  },
};
