import {
  useState,
  useEffect,
  create<PERSON>ontext,
  use<PERSON>ontext,
  ReactNode,
} from "react";

// Language types
export type Language = "vi" | "en";

// Translation structure
export interface Translations {
  interviewFeedback: {
    title: string;
    detailsTitle: string;
    feedbackRequired: string;
    actionNeeded: string;
    submitted: string;
    complete: string;
    incomplete: string;
    recommended: string;
    notRecommended: string;
    noRecommendation: string;
    comments: string;
    detailedScores: string;
    technical: string;
    communication: string;
    cultureFit: string;
    viewDetails: string;
    editFeedback: string;
  };

  interviewDetails: {
    information: string;
    applyingFor: string;
    meetingDetails: string;
    joinMeeting: string;
    password: string;
    agenda: string;
    notes: string;
    duration: string;
    type: {
      video: string;
      phone: string;
      inPerson: string;
    };
  };
  interviewActions: any;
  // Common
  common: {
    save: string;
    cancel: string;
    delete: string;
    edit: string;
    add: string;
    search: string;
    filter: string;
    export: string;
    import: string;
    loading: string;
    error: string;
    success: string;
    warning: string;
    info: string;
    confirm: string;
    yes: string;
    no: string;
    close: string;
    back: string;
    next: string;
    previous: string;
    submit: string;
    reset: string;
    clear: string;
    view: string;
    download: string;
    upload: string;
    refresh: string;
    settings: string;
    help: string;
    logout: string;
    profile: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    status: string;
    date: string;
    time: string;
    actions: string;
    total: string;
    active: string;
    inactive: string;
    enabled: string;
    disabled: string;
    required: string;
    optional: string;
    all: string;
    none: string;
    select: string;
    selectAll: string;
    deselectAll: string;
    today: string;
    yesterday: string;
    tomorrow: string;
    thisWeek: string;
    thisMonth: string;
    thisYear: string;
    rotate: string;
    print: string;
    fullscreen: string;
  };

  // Navigation
  nav: {
    dashboard: string;
    candidates: string;
    jobs: string;
    pipeline: string;
    calendar: string;
    interviewers: string;
    messages: string;
    analytics: string;
    profile: string;
    team: string;
    settings: string;
  };

  // Dashboard
  dashboard: {
    title: string;
    subtitle: string;
    totalCandidates: string;
    newApplications: string;
    scheduledInterviews: string;
    successfulHires: string;
    recentActivity: string;
    quickActions: string;
    addCandidate: string;
    scheduleInterview: string;
    reviewApplications: string;
    sendMessage: string;
    viewAnalytics: string;
    systemHealth: string;
    performanceMetrics: string;
    upcomingInterviews: string;
    pendingTasks: string;
    teamActivity: string;
    aiInsights: string;
  };

  // Candidates
  candidates: {
    feedbackTab: string;
    documentsTab: string;
    activityTab: string;
    overviewTab: string;
    aiSummaryTab: string;
    maxSalary: string;
    addNote: string;
    title: string;
    subtitle: string;
    addCandidate: string;
    importCsv: string;
    viewDetails: string;
    sendEmail: string;
    scheduleInterview: string;
    updateStatus: string;
    deleteCandidate: string;
    bulkActions: string;
    filters: string;
    sortBy: string;
    gridView: string;
    listView: string;
    totalCandidates: string;
    topRated: string;
    newThisWeek: string;
    interviewReady: string;
    candidateProfile: string;
    personalInfo: string;
    workExperience: string;
    education: string;
    skills: string;
    documents: string;
    interviewHistory: string;
    feedback: string;
    notes: string;
    timeline: string;
    contactInfo: string;
    socialLinks: string;
    resume: string;
    coverLetter: string;
    portfolio: string;
    references: string;
    source: string;
    appliedDate: string;
    lastUpdated: string;
    rating: string;
    tags: string;
    location: string;
    salary: string;
    availability: string;
    keyboardShortcuts: string;
    shortcutsHelp: string;
    navigateUp: string;
    navigateDown: string;
    viewDetailsShortcut: string;
    sendEmailShortcut: string;
    scheduleInterviewShortcut: string;
    addCandidateShortcut: string;
    focusSearchShortcut: string;
    toggleViewShortcut: string;
    clearSelectionShortcut: string;
    showShortcuts: string;
    uploaded: string;
    editCandidate: string;
    updateCandidateProfile: string;
    generalInformation: string;
    basicInformation: string;
    professionalInformation: string;
    fullName: string;
    emailAddress: string;
    phoneNumber: string;
    enterFullName: string;
    enterEmailAddress: string;
    nameRequired: string;
    emailRequired: string;
    invalidEmailFormat: string;
    cityStateCountry: string;
    positionRole: string;
    positionPlaceholder: string;
    positionRequired: string;
    experienceLevel: string;
    selectExperienceLevel: string;
    entryLevel: string;
    junior: string;
    midLevel: string;
    senior: string;
    leadPrincipal: string;
    expectedSalary: string;
    salaryPlaceholder: string;
    selectSource: string;
    applyingForPosition: string;
    selectJobPosition: string;
    addSkill: string;
    updateResume: string;
    uploadNewFiles: string;
    chooseFile: string;
    linkedinProfile: string;
    githubProfile: string;
    addNotes: string;
    updateCandidate: string;
  };

  // Jobs
  jobs: {
    title: string;
    subtitle: string;
    addJob: string;
    editJob: string;
    deleteJob: string;
    viewJob: string;
    publishJob: string;
    unpublishJob: string;
    duplicateJob: string;
    jobDetails: string;
    requirements: string;
    responsibilities: string;
    benefits: string;
    location: string;
    jobType: string;
    department: string;
    salary: string;
    experience: string;
    education: string;
    skills: string;
    deadline: string;
    postedDate: string;
    applicants: string;
    hired: string;
    inProgress: string;
    draft: string;
    closed: string;
    fullTime: string;
    partTime: string;
    contract: string;
    remote: string;
    onSite: string;
    hybrid: string;
    priority: string;
    hiringManager: string;
    jobDescription: string;
    qualifications: string;
    preferredQualifications: string;
    companyInfo: string;
    jobPostingPreview: string;
    applicationProcess: string;
    interviewProcess: string;
    onboardingProcess: string;
  };

  // Pipeline
  pipeline: {
    title: string;
    subtitle: string;
    stages: string;
    sourced: string;
    applied: string;
    screening: string;
    interview: string;
    offer: string;
    hired: string;
    rejected: string;
    moveCandidate: string;
    addStage: string;
    editStage: string;
    deleteStage: string;
    stageSettings: string;
    automations: string;
    bulkEmail: string;
    advanceAll: string;
    rejectAll: string;
    pipelineHealth: string;
    conversionRate: string;
    averageTime: string;
    bottlenecks: string;
    analytics: string;
    dragDrop: string;
    stageDescription: string;
    requirements: string;
    actions: string;
    templates: string;
    notifications: string;
  };

  // Calendar
  calendar: {
    title: string;
    subtitle: string;
    scheduleInterview: string;
    newEvent: string;
    editEvent: string;
    deleteEvent: string;
    monthView: string;
    weekView: string;
    dayView: string;
    todaySchedule: string;
    upcomingEvents: string;
    interviewDetails: string;
    participant: string;
    interviewer: string;
    candidate: string;
    meetingLink: string;
    notes: string;
    feedback: string;
    reschedule: string;
    markCompleted: string;
    sendReminder: string;
    videoCall: string;
    phoneCall: string;
    inPerson: string;
    duration: string;
    agenda: string;
    preparation: string;
    followUp: string;
    availableSlots: string;
    timeZone: string;
    recurring: string;
    reminder: string;
    conflict: string;
    availability: string;
    busyTime: string;
    freeTime: string;
  };

  // Interviewers
  interviewers: {
    title: string;
    subtitle: string;
    addInterviewer: string;
    editInterviewer: string;
    deleteInterviewer: string;
    viewProfile: string;
    activate: string;
    deactivate: string;
    personalInfo: string;
    contactInfo: string;
    expertise: string;
    availability: string;
    schedule: string;
    preferences: string;
    performance: string;
    statistics: string;
    totalInterviews: string;
    avgRating: string;
    completionRate: string;
    candidatesSeen: string;
    successfulHires: string;
    specializations: string;
    languages: string;
    timeSlots: string;
    maxInterviews: string;
    interviewTypes: string;
    weeklyAvailability: string;
    currentProjects: string;
    workload: string;
    feedback: string;
    certifications: string;
    trainingHistory: string;
  };

  // Messages
  messages: {
    title: string;
    subtitle: string;
    compose: string;
    reply: string;
    forward: string;
    delete: string;
    archive: string;
    markRead: string;
    markUnread: string;
    star: string;
    unstar: string;
    inbox: string;
    sent: string;
    drafts: string;
    templates: string;
    newTemplate: string;
    editTemplate: string;
    useTemplate: string;
    recipient: string;
    sender: string;
    subject: string;
    message: string;
    attachment: string;
    signature: string;
    priority: string;
    scheduled: string;
    bulkSend: string;
    emailCampaign: string;
    autoReply: string;
    filters: string;
    search: string;
    folder: string;
    label: string;
    thread: string;
    conversation: string;
    readReceipt: string;
    deliveryStatus: string;
    responseRate: string;
    engagement: string;
    analytics: string;
    bounceRate: string;
    openRate: string;
    clickRate: string;
  };

  // Analytics
  analytics: {
    title: string;
    subtitle: string;
    overview: string;
    reports: string;
    charts: string;
    metrics: string;
    kpis: string;
    performance: string;
    trends: string;
    comparisons: string;
    forecasting: string;
    insights: string;
    recommendations: string;
    timeRange: string;
    dateFilter: string;
    exportReport: string;
    shareReport: string;
    scheduleReport: string;
    customReport: string;
    dashboard: string;
    widgets: string;
    realTime: string;
    historical: string;
    predictive: string;
    benchmarks: string;
    goals: string;
    targets: string;
    alerts: string;
    notifications: string;
    threshold: string;
    variance: string;
    growth: string;
    decline: string;
    improvement: string;
    efficiency: string;
    productivity: string;
    quality: string;
    satisfaction: string;
    roi: string;
    costs: string;
    revenue: string;
  };

  // Profile
  profile: {
    title: string;
    subtitle: string;
    editProfile: string;
    personalInfo: string;
    contactInfo: string;
    security: string;
    preferences: string;
    notifications: string;
    privacy: string;
    appearance: string;
    language: string;
    timezone: string;
    theme: string;
    avatar: string;
    fullName: string;
    firstName: string;
    lastName: string;
    jobTitle: string;
    department: string;
    bio: string;
    skills: string;
    experience: string;
    education: string;
    certifications: string;
    socialLinks: string;
    website: string;
    linkedin: string;
    twitter: string;
    github: string;
    changePassword: string;
    twoFactor: string;
    loginHistory: string;
    sessions: string;
    apiKeys: string;
    permissions: string;
    roles: string;
    lastLogin: string;
    memberSince: string;
    accountStatus: string;
    emailNotifications: string;
    pushNotifications: string;
    smsNotifications: string;
    marketingEmails: string;
    weeklyReports: string;
    systemAlerts: string;
  };

  // Team
  team: {
    title: string;
    subtitle: string;
    addMember: string;
    editMember: string;
    removeMember: string;
    viewProfile: string;
    manageMember: string;
    teamOverview: string;
    organizational: string;
    departments: string;
    roles: string;
    permissions: string;
    hierarchy: string;
    reporting: string;
    teamLead: string;
    teamMembers: string;
    directReports: string;
    colleagues: string;
    collaboration: string;
    communication: string;
    projects: string;
    assignments: string;
    workload: string;
    performance: string;
    goals: string;
    achievements: string;
    feedback: string;
    reviews: string;
    development: string;
    training: string;
    skills: string;
    expertise: string;
    mentoring: string;
    onboarding: string;
    offboarding: string;
    teamHealth: string;
    engagement: string;
    satisfaction: string;
    retention: string;
    turnover: string;
    diversity: string;
    inclusion: string;
  };

  // Settings
  settings: {
    title: string;
    subtitle: string;
    general: string;
    account: string;
    security: string;
    notifications: string;
    integrations: string;
    billing: string;
    advanced: string;
    organization: string;
    companyInfo: string;
    branding: string;
    customization: string;
    features: string;
    limits: string;
    quotas: string;
    usage: string;
    storage: string;
    backup: string;
    restore: string;
    import: string;
    export: string;
    migration: string;
    api: string;
    webhooks: string;
    oauth: string;
    sso: string;
    ldap: string;
    saml: string;
    compliance: string;
    gdpr: string;
    privacy: string;
    terms: string;
    legal: string;
    audit: string;
    logs: string;
    monitoring: string;
    diagnostics: string;
    support: string;
    documentation: string;
    changelog: string;
    updates: string;
    maintenance: string;
  };

  // Status Messages
  status: {
    sourced: string;
    applied: string;
    screening: string;
    interview: string;
    offer: string;
    hired: string;
    rejected: string;
    pending: string;
    inProgress: string;
    completed: string;
    cancelled: string;
    postponed: string;
    expired: string;
    draft: string;
    published: string;
    archived: string;
    deleted: string;
  };

  // Toast Messages
  toast: {
    success: {
      saved: string;
      deleted: string;
      updated: string;
      created: string;
      sent: string;
      imported: string;
      exported: string;
      uploaded: string;
      downloaded: string;
    };
    error: {
      failed: string;
      notFound: string;
      unauthorized: string;
      forbidden: string;
      validation: string;
      network: string;
      server: string;
      timeout: string;
    };
    warning: {
      unsaved: string;
      duplicate: string;
      limit: string;
      expires: string;
    };
    info: {
      loading: string;
      processing: string;
      syncing: string;
      scheduled: string;
    };
  };
}

// Vietnamese translations
export const viTranslations: Translations = {
  common: {
    save: "Lưu",
    cancel: "Hủy",
    delete: "Xóa",
    edit: "Chỉnh sửa",
    add: "Thêm",
    search: "Tìm kiếm",
    filter: "Lọc",
    export: "Xuất",
    import: "Nhập",
    loading: "Đang tải...",
    error: "Lỗi",
    success: "Thành công",
    warning: "Cảnh báo",
    info: "Thông tin",
    confirm: "Xác nhận",
    yes: "Có",
    no: "Không",
    close: "Đóng",
    back: "Quay lại",
    next: "Tiếp theo",
    previous: "Trước",
    submit: "Gửi",
    reset: "Đặt lại",
    clear: "Xóa",
    view: "Xem",
    download: "Tải xuống",
    upload: "Tải lên",
    refresh: "Làm mới",
    settings: "Cài đặt",
    help: "Trợ giúp",
    logout: "Đăng xuất",
    profile: "Hồ sơ",
    name: "Tên",
    email: "Email",
    phone: "Điện thoại",
    address: "Địa chỉ",
    status: "Trạng thái",
    date: "Ngày",
    time: "Thời gian",
    actions: "Hành động",
    total: "Tổng",
    active: "Hoạt động",
    inactive: "Không hoạt động",
    enabled: "Bật",
    disabled: "Tắt",
    required: "Bắt buộc",
    optional: "Tùy chọn",
    all: "Tất cả",
    none: "Không có",
    select: "Chọn",
    selectAll: "Chọn tất cả",
    deselectAll: "Bỏ chọn tất cả",
    today: "Hôm nay",
    yesterday: "Hôm qua",
    tomorrow: "Ngày mai",
    thisWeek: "Tuần này",
    thisMonth: "Tháng này",
    thisYear: "Năm này",
    rotate: "Xoay",
    print: "In",
    fullscreen: "Toàn màn hình",
  },

  // add Vietnames  translation for this interviewFeedback object
  interviewFeedback: {
    title: "Phản hồi phỏng vấn",
    detailsTitle: "Chi tiết cuộc phỏng vấn",
    feedbackRequired: "Cần phản hồi",
    actionNeeded: "Cần hành động",
    submitted: "Đã gửi",
    complete: "Hoàn thành",
    incomplete: "Chưa hoàn thành",
    recommended: "Được khuyến nghị",
    notRecommended: "Không được khuyến nghị",
    noRecommendation: "Chưa có khuyến nghị",
    comments: "Nhận xét",
    detailedScores: "Điểm chi tiết",
    technical: "Kỹ thuật",
    communication: "Giao tiếp",
    cultureFit: "Phù hợp văn hóa",
    viewDetails: "Xem chi tiết",
    editFeedback: "Chỉnh sửa phản hồi",
  },
  interviewDetails: {
    information: "Thông tin cuộc phỏng vấn",
    applyingFor: "Ứng tuyển cho",
    meetingDetails: "Chi tiết cuộc họp",
    joinMeeting: "Tham gia cuộc họp",
    password: "Mật khẩu",
    agenda: "Chương trình",
    notes: "Ghi chú",
    duration: "Thời lượng",
  },
  landing: {
    hero: {
      title: "Nền tảng tuyển dụng thông minh",
      subtitle: "HireFlow - Hệ thống theo dõi ứng viên tiên tiến",
      description:
        "Tối ưu hóa quy trình tuyển dụng với AI, quản lý ứng viên hiệu quả và nâng cao trải nghiệm tuyển dụng cho doanh nghiệp hiện đại.",
      ctaPrimary: "Dùng thử miễn phí",
      ctaSecondary: "Đặt lịch demo",
      trustBadge: "Tin cậy bởi 500+ doanh nghiệp",
    },
    features: {
      title: "Tính năng nổi bật",
      subtitle: "Giải pháp toàn diện cho quản lý tuyển dụng",
      candidateManagement: {
        title: "Quản lý ứng viên",
        description:
          "Lưu trữ và tìm kiếm hồ sơ ứng viên thông minh với AI parsing và tag tự động.",
      },
      pipeline: {
        title: "Pipeline tuyển dụng",
        description:
          "Theo dõi tiến trình ứng viên qua các giai đoạn với workflow tùy chỉnh.",
      },
      interviews: {
        title: "Quản lý phỏng vấn",
        description:
          "Lên lịch phỏng vấn, đánh giá ứng viên và phản hồi từ interviewer.",
      },
      automation: {
        title: "Tự động hóa email",
        description:
          "Gửi email tự động theo tiến trình, template có sẵn và personalization.",
      },
      analytics: {
        title: "Báo cáo & Phân tích",
        description:
          "Dashboard thống kê chi tiết về hiệu quả tuyển dụng và KPI.",
      },
      collaboration: {
        title: "Cộng tác nhóm",
        description:
          "Chia sẻ feedback, ghi chú và quyết định tuyển dụng trong team.",
      },
    },
    benefits: {
      title: "Lợi ích vượt trội",
      subtitle: "So với quy trình tuyển dụng truyền thống",
      efficiency: {
        title: "Hiệu quả cao hơn 60%",
        description:
          "Giảm thời gian tuyển dụng và tăng chất lượng ứng viên với AI và automation.",
      },
      quality: {
        title: "Chất lượng tuyển dụng",
        description:
          "Đánh giá ứng viên khách quan với scoring system và structured interview.",
      },
      experience: {
        title: "Trải nghiệm ứng viên",
        description:
          "Tạo ấn tượng tích cực với quy trình minh bạch và communication nhất quán.",
      },
    },
    pricing: {
      title: "Bảng giá linh hoạt",
      subtitle: "Chọn gói phù hợp với quy mô doanh nghiệp",
      monthly: "Hàng tháng",
      yearly: "Hàng năm (Tiết kiệm 20%)",
      free: {
        name: "Miễn phí",
        price: "0đ",
        description: "Cho startup và team nhỏ",
        features: [
          "Tối đa 50 ứng viên",
          "3 job posting",
          "Pipeline cơ bản",
          "Email templates",
          "Support cộng đồng",
        ],
        cta: "Bắt đầu miễn phí",
      },
      pro: {
        name: "Pro",
        price: "299.000đ",
        description: "Cho doanh nghiệp vừa và nhỏ",
        features: [
          "Không giới hạn ứng viên",
          "Unlimited job postings",
          "Advanced pipeline",
          "AI parsing & matching",
          "Email automation",
          "Reports & analytics",
          "Priority support",
        ],
        cta: "Chọn gói Pro",
        popular: "Phổ biến nhất",
      },
      enterprise: {
        name: "Enterprise",
        price: "Liên hệ",
        description: "Cho tập đoàn và doanh nghi��p lớn",
        features: [
          "Tất cả tính năng Pro",
          "Custom workflow",
          "Advanced integrations",
          "SSO & LDAP",
          "Dedicated support",
          "Custom training",
          "SLA guarantee",
        ],
        cta: "Liên hệ tư vấn",
      },
    },
    blog: {
      title: "Tin tức & Blog",
      subtitle: "Xu hướng HR Tech và kinh nghiệm tuyển dụng",
      readMore: "Đọc thêm",
      viewAll: "Xem tất cả bài viết",
    },
    contact: {
      title: "Liên hệ với chúng tôi",
      subtitle: "Sẵn sàng hỗ trợ và tư vấn giải pháp tuyển dụng",
      form: {
        name: "Họ và tên",
        email: "Email",
        company: "Công ty",
        message: "Tin nhắn",
        send: "Gửi tin nhắn",
      },
      info: {
        address: "Tầng 10, Tòa nhà ABC, 123 Đường XYZ, Quận 1, TP.HCM",
        phone: "+84 ***********",
        email: "<EMAIL>",
        support: "Hỗ trợ 24/7",
      },
    },
  },

  nav: {
    dashboard: "Bảng điều khiển",
    candidates: "Ứng viên",
    jobs: "Việc làm",
    pipeline: "Quy trình",
    calendar: "Lịch",
    interviewers: "Người phỏng vấn",
    messages: "Tin nhắn",
    analytics: "Phân tích",
    profile: "Hồ sơ",
    team: "Nhóm",
    settings: "Cài đặt",
  },

  // Candidates
  candidates: {
    title: "Ứng viên",
    subtitle: "Quản lý và theo dõi ứng viên",
    viewDetails: "Xem chi tiết",
    sendEmail: "Gửi email",
    scheduleInterview: "Lên lịch phỏng vấn",
    updateStatus: "Cập nhật trạng thái",
    addNote: "Thêm ghi chú",
    feedbackTab: "Đánh giá",
    documentsTab: "CV",
    activityTab: "Hoạt động",
    overviewTab: "Thông tin",
    aiSummaryTab: "AI Summary",
    exportProfile: "Xuất hồ sơ",
    aiSummary: "Phân tích AI",
    aiScore: "Điểm AI",
    overallMatch: "Độ phù hợp tổng thể",
    technicalSkills: "Kỹ năng kỹ thuật",
    softSkills: "Kỹ năng mềm",
    experience: "Kinh nghiệm",
    strengths: "Điểm mạnh",
    improvements: "Điểm cần cải thiện",
    recommendations: "Khuyến nghị AI",
    riskFactors: "Yếu tố rủi ro",
    nextActions: "Hành động tiếp theo",
    generateReport: "Tạo báo cáo chi tiết",
    compareWith: "So sánh với ứng viên khác",
    matchToJobs: "Khớp với vị trí kh��c",
  },

  dashboard: {
    title: "Bảng điều khiển",
    subtitle: "Tổng quan hệ thống tuyển dụng với AI thông minh",
    totalCandidates: "Tổng ứng viên",
    newApplications: "Đơn ứng tuyển mới",
    scheduledInterviews: "Phỏng vấn đã lên lịch",
    successfulHires: "Tuyển dụng thành công",
    recentActivity: "Hoạt động gần đây",
    quickActions: "Hành động nhanh",
    addCandidate: "Thêm ứng viên",
    scheduleInterview: "Lên lịch phỏng vấn",
    reviewApplications: "Xem xét đơn ứng tuyển",
    sendMessage: "Gửi tin nhắn",
    viewAnalytics: "Xem phân tích",
    systemHealth: "Tình trạng hệ thống",
    performanceMetrics: "Chỉ số hiệu suất",
    upcomingInterviews: "Phỏng vấn sắp tới",
    pendingTasks: "Nhiệm vụ đang chờ",
    teamActivity: "Hoạt động nhóm",
    aiInsights: "Thông tin AI",
  },
  candidates: {
    title: "Ứng viên",
    subtitle: "Quản lý cơ sở dữ liệu ứng viên với hỗ trợ AI và phím tắt",
    addCandidate: "Thêm ứng viên",
    importCsv: "Nhập CSV",
    viewDetails: "Xem chi tiết",
    sendEmail: "Gửi email",
    scheduleInterview: "Lên lịch phỏng vấn",
    updateStatus: "Cập nhật trạng thái",
    deleteCandidate: "Xóa ứng viên",
    bulkActions: "Hành động hàng loạt",
    filters: "Bộ lọc",
    sortBy: "Sắp xếp theo",
    gridView: "Xem dạng lưới",
    listView: "Xem dạng danh sách",
    totalCandidates: "Tổng ứng viên",
    topRated: "Đánh giá cao",
    newThisWeek: "Mới tuần này",
    interviewReady: "Sẵn sàng phỏng vấn",
    candidateProfile: "Hồ sơ ứng viên",
    personalInfo: "Thông tin cá nhân",
    workExperience: "Kinh nghiệm làm việc",
    education: "Học vấn",
    skills: "Kỹ năng",
    documents: "Tài liệu",
    interviewHistory: "Lịch sử phỏng vấn",
    feedback: "Phản hồi",
    notes: "Ghi chú",
    timeline: "Dòng thời gian",
    contactInfo: "Thông tin liên hệ",
    socialLinks: "Liên kết mạng xã hội",
    resume: "Sơ yếu lý lịch",
    coverLetter: "Thư xin việc",
    portfolio: "Danh mục",
    references: "Người giới thiệu",
    source: "Nguồn",
    appliedDate: "Ngày ứng tuyển",
    lastUpdated: "Cập nhật cuối",
    rating: "Đánh giá",
    tags: "Thẻ",
    location: "Vị trí",
    salary: "Lương",
    availability: "Khả năng làm việc",
    keyboardShortcuts: "Phím tắt",
    shortcutsHelp: "Trợ giúp phím tắt",
    navigateUp: "Di chuyển lên",
    navigateDown: "Di chuyển xuống",
    viewDetailsShortcut: "Xem chi tiết",
    sendEmailShortcut: "Gửi email",
    scheduleInterviewShortcut: "Lên lịch phỏng vấn",
    addCandidateShortcut: "Thêm ứng viên mới",
    focusSearchShortcut: "Tập trung tìm kiếm",
    toggleViewShortcut: "Chuyển đổi chế độ xem",
    clearSelectionShortcut: "Xóa lựa chọn",
    showShortcuts: "Hiển thị phím tắt",
    uploaded: "Đã tải lên",
    editCandidate: "Chỉnh sửa ứng viên",
    updateCandidateProfile: "Cập nhật thông tin hồ sơ ứng viên và tùy chọn",
    generalInformation: "Thông tin chung",
    basicInformation: "Thông tin cơ bản",
    professionalInformation: "Thông tin nghề nghiệp",
    fullName: "Họ và tên",
    emailAddress: "Địa chỉ email",
    phoneNumber: "Số điện thoại",
    enterFullName: "Nhập họ và tên",
    enterEmailAddress: "Nhập địa chỉ email",
    nameRequired: "Tên là bắt buộc",
    emailRequired: "Email là bắt buộc",
    invalidEmailFormat: "Định dạng email không hợp lệ",
    cityStateCountry: "Thành phố",
    positionRole: "Vị trí/Vai trò",
    positionPlaceholder: "ví dụ: Senior Frontend Developer",
    positionRequired: "Vị trí là bắt buộc",
    experienceLevel: "Trình độ kinh nghiệm",
    selectExperienceLevel: "Chọn trình độ kinh nghiệm",
    entryLevel: "Mới bắt đầu (0-1 năm)",
    junior: "Junior (2-3 năm)",
    midLevel: "Trung cấp (3-5 năm)",
    senior: "Senior (5+ năm)",
    leadPrincipal: "Lead/Principal (8+ năm)",
    expectedSalary: "Mức lương mong muốn",
    salaryPlaceholder: "ví dụ: $120,000 - $140,000",
    selectSource: "Chọn nguồn",
    applyingForPosition: "Ứng tuyển cho vị trí",
    selectJobPosition: "Chọn vị trí công việc",
    addSkill: "Thêm kỹ năng",
    updateResume: "Cập nhật sơ yếu lý lịch",
    uploadNewFiles:
      "Tải lên tập PDF, DOC hoặc DOCX mới để thay thế sơ yếu lý lịch hiện tại",
    chooseFile: "Chọn tập",
    linkedinProfile: "Hồ sơ LinkedIn",
    githubProfile: "Hồ sơ GitHub",
    addNotes: "Thêm ghi chú về ứng viên này...",
    updateCandidate: "Cập nhật ứng viên",
  },

  jobs: {
    title: "Việc làm",
    subtitle: "Quản lý tin tuyển dụng và vị trí công việc",
    addJob: "Thêm việc làm",
    editJob: "Chỉnh sửa việc làm",
    deleteJob: "Xóa việc làm",
    viewJob: "Xem việc làm",
    publishJob: "Đăng việc làm",
    unpublishJob: "Hủy đăng việc làm",
    duplicateJob: "Sao chép việc làm",
    jobDetails: "Chi tiết việc làm",
    requirements: "Yêu cầu",
    responsibilities: "Trách nhiệm",
    benefits: "Quyền lợi",
    location: "Địa điểm",
    jobType: "Loại hình công việc",
    department: "Phòng ban",
    salary: "Mức lương",
    experience: "Kinh nghiệm",
    education: "Học vấn",
    skills: "Kỹ năng",
    deadline: "Hạn cuối",
    postedDate: "Ngày đăng",
    applicants: "Ứng viên",
    hired: "Đã tuyển",
    inProgress: "Đang tiến hành",
    draft: "Bản nháp",
    closed: "Đã đóng",
    fullTime: "Toàn thời gian",
    partTime: "Bán thời gian",
    contract: "Hợp đồng",
    remote: "Từ xa",
    onSite: "Tại văn phòng",
    hybrid: "Kết hợp",
    priority: "Độ ưu tiên",
    hiringManager: "Quản lý tuyển dụng",
    jobDescription: "Mô tả công việc",
    qualifications: "Trình độ yêu cầu",
    preferredQualifications: "Trình độ ưu tiên",
    companyInfo: "Thông tin công ty",
    jobPostingPreview: "Xem trước tin tuyển dụng",
    applicationProcess: "Quy trình ứng tuyển",
    interviewProcess: "Quy trình phỏng vấn",
    onboardingProcess: "Quy trình onboarding",
  },

  pipeline: {
    title: "Quy trình tuyển dụng",
    subtitle: "Quản lý quy trình tuyển dụng với kéo thả và tự động hóa",
    stages: "Giai đo��n",
    sourced: "Đã tìm hiểu",
    applied: "Đã ứng tuyển",
    screening: "Sàng lọc",
    interview: "Phỏng vấn",
    offer: "Đề nghị",
    hired: "Đã tuyển",
    rejected: "Từ chối",
    moveCandidate: "Di chuyển ứng viên",
    addStage: "Thêm giai đoạn",
    editStage: "Chỉnh sửa giai đoạn",
    deleteStage: "Xóa giai đoạn",
    stageSettings: "Cài đặt giai đoạn",
    automations: "Tự động hóa",
    bulkEmail: "Email hàng loạt",
    advanceAll: "Tiến tất cả",
    rejectAll: "Từ chối tất cả",
    pipelineHealth: "Tình trạng quy trình",
    conversionRate: "Tỷ lệ chuyển đổi",
    averageTime: "Thời gian trung bình",
    bottlenecks: "Điểm nghẽn",
    analytics: "Phân tích",
    dragDrop: "Kéo thả",
    stageDescription: "Mô tả giai đoạn",
    requirements: "Yêu cầu",
    actions: "Hành động",
    templates: "Mẫu",
    notifications: "Thông báo",
  },

  calendar: {
    title: "Lịch",
    subtitle: "Quản lý lịch phỏng vấn và sự kiện",
    scheduleInterview: "Lên lịch phỏng vấn",
    newEvent: "Sự kiện mới",
    editEvent: "Chỉnh sửa sự kiện",
    deleteEvent: "Xóa sự kiện",
    monthView: "Xem theo tháng",
    weekView: "Xem theo tuần",
    dayView: "Xem theo ngày",
    todaySchedule: "Lịch hôm nay",
    upcomingEvents: "Sự kiện sắp tới",
    interviewDetails: "Chi tiết phỏng vấn",
    participant: "Người tham gia",
    interviewer: "Người phỏng vấn",
    candidate: "Ứng viên",
    meetingLink: "Liên kết cuộc họp",
    notes: "Ghi chú",
    feedback: "Phản hồi",
    reschedule: "Đổi lịch",
    markCompleted: "Đánh dấu hoàn thành",
    sendReminder: "Gửi nhắc nhở",
    videoCall: "Gọi video",
    phoneCall: "Gọi điện thoại",
    inPerson: "Trực tiếp",
    duration: "Thời lượng",
    agenda: "Chương trình",
    preparation: "Chuẩn bị",
    followUp: "Theo dõi",
    availableSlots: "Khung giờ trống",
    timeZone: "Múi giờ",
    recurring: "Lặp lại",
    reminder: "Nhắc nhở",
    conflict: "Xung đột",
    availability: "Khả năng có mặt",
    busyTime: "Thời gian bận",
    freeTime: "Thời gian rảnh",
  },

  interviewers: {
    title: "Người phỏng vấn",
    subtitle: "Quản lý đội ngũ phỏng vấn với hoạt động CRUD đầy đủ",
    addInterviewer: "Thêm người phỏng vấn",
    editInterviewer: "Chỉnh sửa người phỏng vấn",
    deleteInterviewer: "Xóa người phỏng vấn",
    viewProfile: "Xem hồ sơ",
    activate: "Kích hoạt",
    deactivate: "Hủy kích hoạt",
    personalInfo: "Thông tin cá nhân",
    contactInfo: "Thông tin liên hệ",
    expertise: "Chuy��n môn",
    availability: "Khả năng có mặt",
    schedule: "Lịch trình",
    preferences: "T��y chọn",
    performance: "Hiệu suất",
    statistics: "Thống kê",
    totalInterviews: "Tổng phỏng vấn",
    avgRating: "Đánh giá trung bình",
    completionRate: "Tỷ lệ hoàn thành",
    candidatesSeen: "Ứng viên đã gặp",
    successfulHires: "Tuyển thành công",
    specializations: "Chuyên ngành",
    languages: "Ngôn ngữ",
    timeSlots: "Khung giờ",
    maxInterviews: "Tối đa phỏng vấn",
    interviewTypes: "Loại phỏng vấn",
    weeklyAvailability: "Lịch hàng tuần",
    currentProjects: "Dự án hiện tại",
    workload: "Khối lượng công việc",
    feedback: "Phản hồi",
    certifications: "Chứng chỉ",
    trainingHistory: "Lịch sử đào tạo",
  },

  messages: {
    title: "Tin nhắn",
    subtitle: "Giao tiếp với ứng viên và quản lý mẫu email",
    compose: "Soạn",
    reply: "Trả lời",
    forward: "Chuyển tiếp",
    delete: "Xóa",
    archive: "Lưu trữ",
    markRead: "Đánh dấu đã đọc",
    markUnread: "Đánh dấu chưa đọc",
    star: "Gắn sao",
    unstar: "Bỏ sao",
    inbox: "Hộp thư đến",
    sent: "Đã gửi",
    drafts: "Bản nháp",
    templates: "Mẫu",
    newTemplate: "Mẫu mới",
    editTemplate: "Chỉnh sửa mẫu",
    useTemplate: "Sử dụng mẫu",
    recipient: "Người nhận",
    sender: "Người gửi",
    subject: "Chủ đề",
    message: "Tin nhắn",
    attachment: "Tập đính kèm",
    signature: "Chữ ký",
    priority: "Độ ưu tiên",
    scheduled: "Đã lên lịch",
    bulkSend: "Gửi hàng loạt",
    emailCampaign: "Chiến dịch email",
    autoReply: "Trả lời tự động",
    filters: "Bộ lọc",
    search: "Tìm kiếm",
    folder: "Thư mục",
    label: "Nhãn",
    thread: "Chủ đề",
    conversation: "Cuộc trò chuyện",
    readReceipt: "Xác nhận đã đọc",
    deliveryStatus: "Trạng thái gửi",
    responseRate: "Tỷ lệ phản hồi",
    engagement: "Tương tác",
    analytics: "Phân tích",
    bounceRate: "Tỷ lệ bị trả về",
    openRate: "Tỷ lệ mở",
    clickRate: "Tỷ lệ nhấp",
  },

  analytics: {
    title: "Phân tích",
    subtitle: "Thông tin chi tiết và báo cáo về hiệu suất tuyển dụng",
    overview: "Tổng quan",
    reports: "Báo cáo",
    charts: "Biểu đồ",
    metrics: "Chỉ số",
    kpis: "KPI",
    performance: "Hiệu suất",
    trends: "Xu hướng",
    comparisons: "So sánh",
    forecasting: "Dự báo",
    insights: "Thông tin chi tiết",
    recommendations: "Khuyến nghị",
    timeRange: "Khoảng thời gian",
    dateFilter: "Lọc theo ngày",
    exportReport: "Xuất báo cáo",
    shareReport: "Chia sẻ báo cáo",
    scheduleReport: "Lên lịch báo cáo",
    customReport: "Báo cáo tùy chỉnh",
    dashboard: "Bảng điều khiển",
    widgets: "Widget",
    realTime: "Thời gian thực",
    historical: "Lịch sử",
    predictive: "Dự đoán",
    benchmarks: "Điểm chuẩn",
    goals: "Mục tiêu",
    targets: "Chỉ ti��u",
    alerts: "Cảnh báo",
    notifications: "Thông báo",
    threshold: "Ngưỡng",
    variance: "Biến động",
    growth: "Tăng trưởng",
    decline: "Giảm",
    improvement: "Cải thiện",
    efficiency: "Hiệu quả",
    productivity: "Năng suất",
    quality: "Chất lượng",
    satisfaction: "Hài lòng",
    roi: "ROI",
    costs: "Chi phí",
    revenue: "Doanh thu",
    // Additional fields for Analytics page
    totalCandidates: "Tổng Ứng viên",
    timeToHire: "Thời gian Tuyển dụng",
    conversionRate: "Tỷ lệ Chuyển đổi",
    activeJobs: "Việc làm Hoạt động",
    pipelineHealth: "Tình trạng Quy trình",
    conversionFunnel: "Phễu Chuyển đổi",
    sourceEffectiveness: "Hiệu quả Nguồn",
    jobPerformance: "Hiệu suất Công việc",
    monthlyTrends: "Xu hướng Hàng tháng",
    keyInsights: "Thông tin Chính",
    recommendationsTitle: "Khuyến nghị",
    nextSteps: "Bước tiếp theo",
    successRate: "tỷ lệ thành công",
    candidates: "ứng viên",
    hired: "đã tuyển",
    applications: "Ứng tuyển",
    interviews: "Phỏng vấn",
    hires: "Tuyển dụng",
    appliedToInterview: "Ứng tuyển → Phỏng vấn",
    interviewToOffer: "Phỏng vấn → Đề nghị",
    offerToHire: "Đề nghị → Tuyển dụng",
    pipelineDistribution: "Phân bố ứng viên theo giai đoạn",
    stageConversion: "Tỷ lệ chuyển đổi từng giai đoạn",
    candidateSource: "Hiệu suất theo nguồn ứng viên",
    positionPerformance: "Ứng tuyển và tuyển dụng theo vị trí",
    trendOverTime: "Ứng tuyển, phỏng vấn và tuyển dụng theo thời gian",
    insights: {
      linkedinTop: "LinkedIn là nguồn hàng đầu với 50% tổng ứng viên",
      referralsBest: "Giới thiệu có tỷ lệ chuyển đổi cao nhất ở 50%",
      frontendMost: "Vị trí Frontend Developer có nhiều đơn ứng tuyển nhất",
      interviewImproved:
        "Tỷ lệ phỏng vấn thành đề nghị cải thiện 15% tháng này",
    },
    recommendations: {
      increaseReferral:
        "Tăng cường chương trình giới thiệu để có ứng viên chất lượng cao",
      optimizeLinkedin: "Tối ưu hóa chiến lược tìm kiếm trên LinkedIn",
      reviewScreening: "Xem xét quy trình sàng lọc để chuyển đổi tốt hơn",
      expandUx: "Cân nhắc mở rộng yêu cầu cho vị trí UX Designer",
    },
    nextStepsItems: {
      scheduleReview: "Lên lịch cuộc họp đánh giá tuyển dụng hàng quý",
      updateJobs: "Cập nhật mô tả công việc dựa trên phản hồi thị trường",
      candidateSurvey: "Triển khai khảo sát trải nghiệm ứng viên",
      reviewCompensation: "Xem xét gói lương thưởng để có đề nghị cạnh tranh",
    },
  },

  profile: {
    title: "Hồ sơ của tôi",
    subtitle: "Quản lý thông tin cá nhân, tùy chọn và cài đặt bảo mật",
    editProfile: "Chỉnh sửa hồ sơ",
    personalInfo: "Thông tin cá nhân",
    contactInfo: "Thông tin liên hệ",
    security: "Bảo mật",
    preferences: "Tùy chọn",
    notifications: "Thông báo",
    privacy: "Quyền riêng tư",
    appearance: "Giao diện",
    language: "Ngôn ngữ",
    timezone: "Múi giờ",
    theme: "Chủ đề",
    avatar: "Ảnh đại diện",
    fullName: "Họ tên đầy đủ",
    firstName: "Tên",
    lastName: "Họ",
    jobTitle: "Chức danh",
    department: "Phòng ban",
    bio: "Tiểu sử",
    skills: "Kỹ năng",
    experience: "Kinh nghiệm",
    education: "Học vấn",
    certifications: "Chứng chỉ",
    socialLinks: "Liên kết mạng xã hội",
    website: "Website",
    linkedin: "LinkedIn",
    twitter: "Twitter",
    github: "GitHub",
    changePassword: "Đổi mật khẩu",
    twoFactor: "Xác thực hai yếu tố",
    loginHistory: "Lịch sử đăng nhập",
    sessions: "Phiên làm việc",
    apiKeys: "Khóa API",
    permissions: "Quyền hạn",
    roles: "Vai trò",
    lastLogin: "Đăng nhập cuối",
    memberSince: "Thành viên từ",
    accountStatus: "Trạng thái tài khoản",
    emailNotifications: "Thông báo email",
    pushNotifications: "Thông báo đẩy",
    smsNotifications: "Thông báo SMS",
    marketingEmails: "Email marketing",
    weeklyReports: "Báo cáo hàng tuần",
    systemAlerts: "Cảnh báo hệ thống",
  },

  team: {
    title: "Quản lý nhóm",
    subtitle: "Quản lý thành viên nhóm, vai trò và cấu trúc tổ chức",
    addMember: "Thêm thành viên",
    editMember: "Chỉnh sửa thành viên",
    removeMember: "Xóa thành viên",
    viewProfile: "Xem hồ sơ",
    manageMember: "Quản lý thành viên",
    teamOverview: "Tổng quan nhóm",
    organizational: "Sơ đồ tổ chức",
    departments: "Phòng ban",
    roles: "Vai trò",
    permissions: "Quyền hạn",
    hierarchy: "Thứ bậc",
    reporting: "Báo cáo",
    teamLead: "Trưởng nhóm",
    teamMembers: "Thành viên nhóm",
    directReports: "Báo cáo trực tiếp",
    colleagues: "Đồng nghiệp",
    collaboration: "Hợp tác",
    communication: "Giao tiếp",
    projects: "Dự án",
    assignments: "Nhiệm vụ",
    workload: "Khối lượng công việc",
    performance: "Hiệu suất",
    goals: "Mục tiêu",
    achievements: "Thành tích",
    feedback: "Phản hồi",
    reviews: "Đánh giá",
    development: "Phát triển",
    training: "Đào tạo",
    skills: "Kỹ năng",
    expertise: "Chuyên môn",
    mentoring: "Cố vấn",
    onboarding: "Định hướng",
    offboarding: "Kết thúc",
    teamHealth: "Sức khỏe nhóm",
    engagement: "Cam kết",
    satisfaction: "Hài lòng",
    retention: "Giữ chân",
    turnover: "Luân chuyển",
    diversity: "Đa dạng",
    inclusion: "Hòa nhập",
  },

  settings: {
    title: "Cài đặt",
    subtitle: "Cấu hình cài đặt tổ chức, tích hợp và tùy chọn hệ thống",
    general: "Chung",
    account: "Tài khỏan",
    security: "Bảo mật",
    notifications: "Thông báo",
    integrations: "Tích hợp",
    billing: "Thanh toán",
    advanced: "Nâng cao",
    organization: "Tổ chức",
    companyInfo: "Thông tin công ty",
    branding: "Thương hiệu",
    customization: "Tùy chỉnh",
    features: "Tính năng",
    limits: "Giới hạn",
    quotas: "Hạn ngạch",
    usage: "Sử dụng",
    storage: "Lưu trữ",
    backup: "Sao lưu",
    restore: "Phục hồi",
    import: "Nhập",
    export: "Xuất",
    migration: "Di chuyển",
    api: "API",
    webhooks: "Webhook",
    oauth: "OAuth",
    sso: "SSO",
    ldap: "LDAP",
    saml: "SAML",
    compliance: "Tuân thủ",
    gdpr: "GDPR",
    privacy: "Quyền riêng tư",
    terms: "Điều khoản",
    legal: "Pháp lý",
    audit: "Kiểm toán",
    logs: "Nhật ký",
    monitoring: "Giám sát",
    diagnostics: "Chẩn đoán",
    support: "Hỗ trợ",
    documentation: "Tài liệu",
    changelog: "Nhật ký thay đổi",
    updates: "Cập nhật",
    maintenance: "Bảo trì",
  },

  status: {
    sourced: "Đã tìm hiểu",
    applied: "Đã ứng tuyển",
    screening: "Sàng lọc",
    interview: "Phỏng vấn",
    offer: "Đề nghị",
    hired: "Đã tuyển",
    rejected: "Từ chối",
    pending: "Đang chờ",
    inProgress: "Đang tiến hành",
    completed: "Hoàn thành",
    cancelled: "Đã hủy",
    postponed: "Hoãn lại",
    expired: "Hết hạn",
    draft: "Bản nháp",
    published: "Đã đăng",
    archived: "Lưu trữ",
    deleted: "Đã xóa",
  },

  toast: {
    success: {
      saved: "Đã lưu thành công!",
      deleted: "Đã xóa thành công!",
      updated: "Đã cập nhật thành công!",
      created: "Đã tạo thành công!",
      sent: "Đã gửi thành công!",
      imported: "Đã nhập thành công!",
      exported: "Đã xuất thành công!",
      uploaded: "Đã tải lên thành công!",
      downloaded: "Đã tải xuống thành công!",
    },
    error: {
      failed: "Thao tác thất bại!",
      notFound: "Không tìm thấy!",
      unauthorized: "Không có quyền!",
      forbidden: "Bị cấm!",
      validation: "Dữ liệu không hợp lệ!",
      network: "Lỗi mạng!",
      server: "Lỗi máy chủ!",
      timeout: "Hết thời gian chờ!",
    },
    warning: {
      unsaved: "Có thay đổi chưa được lưu!",
      duplicate: "Dữ liệu trùng lặp!",
      limit: "Đã đạt giới hạn!",
      expires: "Sắp hết hạn!",
    },
    info: {
      loading: "Đang tải...",
      processing: "Đang xử lý...",
      syncing: "Đang đồng bộ...",
      scheduled: "Đã lên lịch!",
    },
  },
};

// English translations
export const enTranslations: Translations = {
  common: {
    save: "Save",
    cancel: "Cancel",
    delete: "Delete",
    edit: "Edit",
    add: "Add",
    search: "Search",
    filter: "Filter",
    export: "Export",
    import: "Import",
    loading: "Loading...",
    error: "Error",
    success: "Success",
    warning: "Warning",
    info: "Info",
    confirm: "Confirm",
    yes: "Yes",
    no: "No",
    close: "Close",
    back: "Back",
    next: "Next",
    previous: "Previous",
    submit: "Submit",
    reset: "Reset",
    clear: "Clear",
    view: "View",
    download: "Download",
    upload: "Upload",
    refresh: "Refresh",
    settings: "Settings",
    help: "Help",
    logout: "Logout",
    profile: "Profile",
    name: "Name",
    email: "Email",
    phone: "Phone",
    address: "Address",
    status: "Status",
    date: "Date",
    time: "Time",
    actions: "Actions",
    total: "Total",
    active: "Active",
    inactive: "Inactive",
    enabled: "Enabled",
    disabled: "Disabled",
    required: "Required",
    optional: "Optional",
    all: "All",
    none: "None",
    select: "Select",
    selectAll: "Select All",
    deselectAll: "Deselect All",
    today: "Today",
    yesterday: "Yesterday",
    tomorrow: "Tomorrow",
    thisWeek: "This Week",
    thisMonth: "This Month",
    thisYear: "This Year",
    rotate: "Rotate",
    print: "Print",
    fullscreen: "Fullscreen",
  },

  nav: {
    dashboard: "Dashboard",
    candidates: "Candidates",
    jobs: "Jobs",
    pipeline: "Pipeline",
    calendar: "Calendar",
    interviewers: "Interviewers",
    messages: "Messages",
    analytics: "Analytics",
    profile: "Profile",
    team: "Team",
    settings: "Settings",
  },

  // Candidates
  candidates: {
    feedbackTab: "Feedback",
    documentsTab: "Documents",
    activityTab: "Activity",
    overviewTab: "Overview",
    aiSummaryTab: "AI Summary",
    title: "Candidates",
    subtitle: "Manage and track candidates",
    viewDetails: "View Details",
    sendEmail: "Send Email",
    scheduleInterview: "Schedule Interview",
    updateStatus: "Update Status",
    addNote: "Add Note",
    exportProfile: "Export Profile",
    aiSummary: "AI Analysis",
    aiScore: "AI Score",
    overallMatch: "Overall Match",
    technicalSkills: "Technical Skills",
    softSkills: "Soft Skills",
    experience: "Experience",
    strengths: "Strengths",
    improvements: "Areas for Improvement",
    recommendations: "AI Recommendations",
    riskFactors: "Risk Factors",
    nextActions: "Next Actions",
    generateReport: "Generate Detailed Report",
    compareWith: "Compare with Others",
    matchToJobs: "Match to Other Positions",
  },

  dashboard: {
    title: "Dashboard",
    subtitle: "Smart recruiting system overview with AI assistance",
    totalCandidates: "Total Candidates",
    newApplications: "New Applications",
    scheduledInterviews: "Scheduled Interviews",
    successfulHires: "Successful Hires",
    recentActivity: "Recent Activity",
    quickActions: "Quick Actions",
    addCandidate: "Add Candidate",
    scheduleInterview: "Schedule Interview",
    reviewApplications: "Review Applications",
    sendMessage: "Send Message",
    viewAnalytics: "View Analytics",
    systemHealth: "System Health",
    performanceMetrics: "Performance Metrics",
    upcomingInterviews: "Upcoming Interviews",
    pendingTasks: "Pending Tasks",
    teamActivity: "Team Activity",
    aiInsights: "AI Insights",
  },

  candidates: {
    title: "Candidates",
    subtitle:
      "Manage your candidate database with AI assistance and keyboard shortcuts",
    addCandidate: "Add Candidate",
    importCsv: "Import CSV",
    viewDetails: "View Details",
    sendEmail: "Send Email",
    scheduleInterview: "Schedule Interview",
    updateStatus: "Update Status",
    deleteCandidate: "Delete Candidate",
    bulkActions: "Bulk Actions",
    filters: "Filters",
    sortBy: "Sort By",
    gridView: "Grid View",
    listView: "List View",
    totalCandidates: "Total Candidates",
    topRated: "Top Rated",
    newThisWeek: "New This Week",
    interviewReady: "Interview Ready",
    candidateProfile: "Candidate Profile",
    personalInfo: "Personal Info",
    workExperience: "Work Experience",
    education: "Education",
    skills: "Skills",
    documents: "Documents",
    interviewHistory: "Interview History",
    feedback: "Feedback",
    notes: "Notes",
    timeline: "Timeline",
    contactInfo: "Contact Info",
    socialLinks: "Social Links",
    resume: "Resume",
    coverLetter: "Cover Letter",
    portfolio: "Portfolio",
    references: "References",
    source: "Source",
    appliedDate: "Applied Date",
    lastUpdated: "Last Updated",
    rating: "Rating",
    tags: "Tags",
    location: "Location",
    salary: "Salary",
    availability: "Availability",
    keyboardShortcuts: "Keyboard Shortcuts",
    shortcutsHelp: "Shortcuts Help",
    navigateUp: "Navigate Up",
    navigateDown: "Navigate Down",
    viewDetailsShortcut: "View Details",
    sendEmailShortcut: "Send Email",
    scheduleInterviewShortcut: "Schedule Interview",
    addCandidateShortcut: "Add New Candidate",
    focusSearchShortcut: "Focus Search",
    toggleViewShortcut: "Toggle View",
    clearSelectionShortcut: "Clear Selection",
    showShortcuts: "Show Shortcuts",
    uploaded: "Uploaded",
    editCandidate: "Edit Candidate",
    updateCandidateProfile:
      "Update candidate profile information and preferences",
    generalInformation: "General Information",
    basicInformation: "Basic Information",
    professionalInformation: "Professional Information",
    fullName: "Full Name",
    emailAddress: "Email Address",
    phoneNumber: "Phone Number",
    enterFullName: "Enter full name",
    enterEmailAddress: "Enter email address",
    nameRequired: "Name is required",
    emailRequired: "Email is required",
    invalidEmailFormat: "Invalid email format",
    cityStateCountry: "City, State/Country",
    positionRole: "Position/Role",
    positionPlaceholder: "e.g., Senior Frontend Developer",
    positionRequired: "Position is required",
    experienceLevel: "Experience Level",
    selectExperienceLevel: "Select experience level",
    entryLevel: "Entry Level (0-1 years)",
    junior: "Junior (2-3 years)",
    midLevel: "Mid-level (3-5 years)",
    senior: "Senior (5+ years)",
    leadPrincipal: "Lead/Principal (8+ years)",
    expectedSalary: "Expected Salary",
    salaryPlaceholder: "e.g., $120,000 - $140,000",
    selectSource: "Select source",
    applyingForPosition: "Applying for Position",
    selectJobPosition: "Select job position",
    addSkill: "Add a skill",
    updateResume: "Update Resume",
    uploadNewFiles:
      "Upload new PDF, DOC, or DOCX files to replace existing resume",
    chooseFile: "Choose File",
    linkedinProfile: "LinkedIn Profile",
    githubProfile: "GitHub Profile",
    addNotes: "Add any additional notes about this candidate...",
    updateCandidate: "Update Candidate",
  },

  jobs: {
    title: "Jobs",
    subtitle: "Manage job postings and positions",
    addJob: "Add Job",
    editJob: "Edit Job",
    deleteJob: "Delete Job",
    viewJob: "View Job",
    publishJob: "Publish Job",
    unpublishJob: "Unpublish Job",
    duplicateJob: "Duplicate Job",
    jobDetails: "Job Details",
    requirements: "Requirements",
    responsibilities: "Responsibilities",
    benefits: "Benefits",
    location: "Location",
    jobType: "Job Type",
    department: "Department",
    salary: "Salary",
    experience: "Experience",
    education: "Education",
    skills: "Skills",
    deadline: "Deadline",
    postedDate: "Posted Date",
    applicants: "Applicants",
    hired: "Hired",
    inProgress: "In Progress",
    draft: "Draft",
    closed: "Closed",
    fullTime: "Full Time",
    partTime: "Part Time",
    contract: "Contract",
    remote: "Remote",
    onSite: "On Site",
    hybrid: "Hybrid",
    priority: "Priority",
    hiringManager: "Hiring Manager",
    jobDescription: "Job Description",
    qualifications: "Qualifications",
    preferredQualifications: "Preferred Qualifications",
    companyInfo: "Company Info",
    jobPostingPreview: "Job Posting Preview",
    applicationProcess: "Application Process",
    interviewProcess: "Interview Process",
    onboardingProcess: "Onboarding Process",
  },

  pipeline: {
    title: "Pipeline",
    subtitle: "Manage hiring pipeline with drag-and-drop and automation",
    stages: "Stages",
    sourced: "Sourced",
    applied: "Applied",
    screening: "Screening",
    interview: "Interview",
    offer: "Offer",
    hired: "Hired",
    rejected: "Rejected",
    moveCandidate: "Move Candidate",
    addStage: "Add Stage",
    editStage: "Edit Stage",
    deleteStage: "Delete Stage",
    stageSettings: "Stage Settings",
    automations: "Automations",
    bulkEmail: "Bulk Email",
    advanceAll: "Advance All",
    rejectAll: "Reject All",
    pipelineHealth: "Pipeline Health",
    conversionRate: "Conversion Rate",
    averageTime: "Average Time",
    bottlenecks: "Bottlenecks",
    analytics: "Analytics",
    dragDrop: "Drag & Drop",
    stageDescription: "Stage Description",
    requirements: "Requirements",
    actions: "Actions",
    templates: "Templates",
    notifications: "Notifications",
  },

  calendar: {
    title: "Calendar",
    subtitle: "Manage interview schedules and events",
    scheduleInterview: "Schedule Interview",
    newEvent: "New Event",
    editEvent: "Edit Event",
    deleteEvent: "Delete Event",
    monthView: "Month View",
    weekView: "Week View",
    dayView: "Day View",
    todaySchedule: "Today's Schedule",
    upcomingEvents: "Upcoming Events",
    interviewDetails: "Interview Details",
    participant: "Participant",
    interviewer: "Interviewer",
    candidate: "Candidate",
    meetingLink: "Meeting Link",
    notes: "Notes",
    feedback: "Feedback",
    reschedule: "Reschedule",
    markCompleted: "Mark Completed",
    sendReminder: "Send Reminder",
    videoCall: "Video Call",
    phoneCall: "Phone Call",
    inPerson: "In Person",
    duration: "Duration",
    agenda: "Agenda",
    preparation: "Preparation",
    followUp: "Follow Up",
    availableSlots: "Available Slots",
    timeZone: "Time Zone",
    recurring: "Recurring",
    reminder: "Reminder",
    conflict: "Conflict",
    availability: "Availability",
    busyTime: "Busy Time",
    freeTime: "Free Time",
  },

  interviewers: {
    title: "Interviewers",
    subtitle: "Manage your interview team with comprehensive CRUD operations",
    addInterviewer: "Add Interviewer",
    editInterviewer: "Edit Interviewer",
    deleteInterviewer: "Delete Interviewer",
    viewProfile: "View Profile",
    activate: "Activate",
    deactivate: "Deactivate",
    personalInfo: "Personal Info",
    contactInfo: "Contact Info",
    expertise: "Expertise",
    availability: "Availability",
    schedule: "Schedule",
    preferences: "Preferences",
    performance: "Performance",
    statistics: "Statistics",
    totalInterviews: "Total Interviews",
    avgRating: "Avg Rating",
    completionRate: "Completion Rate",
    candidatesSeen: "Candidates Seen",
    successfulHires: "Successful Hires",
    specializations: "Specializations",
    languages: "Languages",
    timeSlots: "Time Slots",
    maxInterviews: "Max Interviews",
    interviewTypes: "Interview Types",
    weeklyAvailability: "Weekly Availability",
    currentProjects: "Current Projects",
    workload: "Workload",
    feedback: "Feedback",
    certifications: "Certifications",
    trainingHistory: "Training History",
  },

  messages: {
    title: "Messages",
    subtitle: "Communicate with candidates and manage email templates",
    compose: "Compose",
    reply: "Reply",
    forward: "Forward",
    delete: "Delete",
    archive: "Archive",
    markRead: "Mark Read",
    markUnread: "Mark Unread",
    star: "Star",
    unstar: "Unstar",
    inbox: "Inbox",
    sent: "Sent",
    drafts: "Drafts",
    templates: "Templates",
    newTemplate: "New Template",
    editTemplate: "Edit Template",
    useTemplate: "Use Template",
    recipient: "Recipient",
    sender: "Sender",
    subject: "Subject",
    message: "Message",
    attachment: "Attachment",
    signature: "Signature",
    priority: "Priority",
    scheduled: "Scheduled",
    bulkSend: "Bulk Send",
    emailCampaign: "Email Campaign",
    autoReply: "Auto Reply",
    filters: "Filters",
    search: "Search",
    folder: "Folder",
    label: "Label",
    thread: "Thread",
    conversation: "Conversation",
    readReceipt: "Read Receipt",
    deliveryStatus: "Delivery Status",
    responseRate: "Response Rate",
    engagement: "Engagement",
    analytics: "Analytics",
    bounceRate: "Bounce Rate",
    openRate: "Open Rate",
    clickRate: "Click Rate",
  },

  analytics: {
    title: "Analytics",
    subtitle: "Detailed insights and reports on recruitment performance",
    overview: "Overview",
    reports: "Reports",
    charts: "Charts",
    metrics: "Metrics",
    kpis: "KPIs",
    performance: "Performance",
    trends: "Trends",
    comparisons: "Comparisons",
    forecasting: "Forecasting",
    insights: "Insights",
    recommendations: "Recommendations",
    timeRange: "Time Range",
    dateFilter: "Date Filter",
    exportReport: "Export Report",
    shareReport: "Share Report",
    scheduleReport: "Schedule Report",
    customReport: "Custom Report",
    dashboard: "Dashboard",
    widgets: "Widgets",
    realTime: "Real Time",
    historical: "Historical",
    predictive: "Predictive",
    benchmarks: "Benchmarks",
    goals: "Goals",
    targets: "Targets",
    alerts: "Alerts",
    notifications: "Notifications",
    threshold: "Threshold",
    variance: "Variance",
    growth: "Growth",
    decline: "Decline",
    improvement: "Improvement",
    efficiency: "Efficiency",
    productivity: "Productivity",
    quality: "Quality",
    satisfaction: "Satisfaction",
    roi: "ROI",
    costs: "Costs",
    revenue: "Revenue",
    // Additional fields for Analytics page
    totalCandidates: "Total Candidates",
    timeToHire: "Time to Hire",
    conversionRate: "Conversion Rate",
    activeJobs: "Active Jobs",
    pipelineHealth: "Pipeline Health",
    conversionFunnel: "Conversion Funnel",
    sourceEffectiveness: "Source Effectiveness",
    jobPerformance: "Job Performance",
    monthlyTrends: "Monthly Trends",
    keyInsights: "Key Insights",
    recommendationsTitle: "Recommendations",
    nextSteps: "Next Steps",
    successRate: "success rate",
    candidates: "candidates",
    hired: "hired",
    applications: "Applications",
    interviews: "Interviews",
    hires: "Hires",
    appliedToInterview: "Applied → Interview",
    interviewToOffer: "Interview → Offer",
    offerToHire: "Offer → Hire",
    pipelineDistribution: "Candidates distribution across stages",
    stageConversion: "Stage-to-stage conversion rates",
    candidateSource: "Performance by candidate source",
    positionPerformance: "Applications and hires by position",
    trendOverTime: "Applications, interviews, and hires over time",
    insights: {
      linkedinTop: "LinkedIn is your top source with 50% of total candidates",
      referralsBest: "Referrals have the highest conversion rate at 50%",
      frontendMost: "Frontend Developer position has the most applications",
      interviewImproved: "Interview-to-offer rate improved by 15% this month",
    },
    recommendations: {
      increaseReferral:
        "Increase referral program to boost high-quality candidates",
      optimizeLinkedin: "Optimize LinkedIn sourcing strategy",
      reviewScreening: "Review screening process for better conversion",
      expandUx: "Consider expanding UX Designer role requirements",
    },
    nextStepsItems: {
      scheduleReview: "Schedule quarterly hiring review meeting",
      updateJobs: "Update job descriptions based on market feedback",
      candidateSurvey: "Implement candidate experience survey",
      reviewCompensation: "Review compensation packages for competitive offers",
    },
  },

  profile: {
    title: "My Profile",
    subtitle:
      "Manage your personal information, preferences, and security settings",
    editProfile: "Edit Profile",
    personalInfo: "Personal Info",
    contactInfo: "Contact Info",
    security: "Security",
    preferences: "Preferences",
    notifications: "Notifications",
    privacy: "Privacy",
    appearance: "Appearance",
    language: "Language",
    timezone: "Timezone",
    theme: "Theme",
    avatar: "Avatar",
    fullName: "Full Name",
    firstName: "First Name",
    lastName: "Last Name",
    jobTitle: "Job Title",
    department: "Department",
    bio: "Bio",
    skills: "Skills",
    experience: "Experience",
    education: "Education",
    certifications: "Certifications",
    socialLinks: "Social Links",
    website: "Website",
    linkedin: "LinkedIn",
    twitter: "Twitter",
    github: "GitHub",
    changePassword: "Change Password",
    twoFactor: "Two Factor Authentication",
    loginHistory: "Login History",
    sessions: "Sessions",
    apiKeys: "API Keys",
    permissions: "Permissions",
    roles: "Roles",
    lastLogin: "Last Login",
    memberSince: "Member Since",
    accountStatus: "Account Status",
    emailNotifications: "Email Notifications",
    pushNotifications: "Push Notifications",
    smsNotifications: "SMS Notifications",
    marketingEmails: "Marketing Emails",
    weeklyReports: "Weekly Reports",
    systemAlerts: "System Alerts",
  },

  team: {
    title: "Team Management",
    subtitle: "Manage team members, roles, and organizational structure",
    addMember: "Add Member",
    editMember: "Edit Member",
    removeMember: "Remove Member",
    viewProfile: "View Profile",
    manageMember: "Manage Member",
    teamOverview: "Team Overview",
    organizational: "Organizational Chart",
    departments: "Departments",
    roles: "Roles",
    permissions: "Permissions",
    hierarchy: "Hierarchy",
    reporting: "Reporting",
    teamLead: "Team Lead",
    teamMembers: "Team Members",
    directReports: "Direct Reports",
    colleagues: "Colleagues",
    collaboration: "Collaboration",
    communication: "Communication",
    projects: "Projects",
    assignments: "Assignments",
    workload: "Workload",
    performance: "Performance",
    goals: "Goals",
    achievements: "Achievements",
    feedback: "Feedback",
    reviews: "Reviews",
    development: "Development",
    training: "Training",
    skills: "Skills",
    expertise: "Expertise",
    mentoring: "Mentoring",
    onboarding: "Onboarding",
    offboarding: "Offboarding",
    teamHealth: "Team Health",
    engagement: "Engagement",
    satisfaction: "Satisfaction",
    retention: "Retention",
    turnover: "Turnover",
    diversity: "Diversity",
    inclusion: "Inclusion",
  },

  settings: {
    title: "Settings",
    subtitle:
      "Configure organization settings, integrations, and system preferences",
    general: "General",
    account: "Account",
    security: "Security",
    notifications: "Notifications",
    integrations: "Integrations",
    billing: "Billing",
    advanced: "Advanced",
    organization: "Organization",
    companyInfo: "Company Info",
    branding: "Branding",
    customization: "Customization",
    features: "Features",
    limits: "Limits",
    quotas: "Quotas",
    usage: "Usage",
    storage: "Storage",
    backup: "Backup",
    restore: "Restore",
    import: "Import",
    export: "Export",
    migration: "Migration",
    api: "API",
    webhooks: "Webhooks",
    oauth: "OAuth",
    sso: "SSO",
    ldap: "LDAP",
    saml: "SAML",
    compliance: "Compliance",
    gdpr: "GDPR",
    privacy: "Privacy",
    terms: "Terms",
    legal: "Legal",
    audit: "Audit",
    logs: "Logs",
    monitoring: "Monitoring",
    diagnostics: "Diagnostics",
    support: "Support",
    documentation: "Documentation",
    changelog: "Changelog",
    updates: "Updates",
    maintenance: "Maintenance",
  },

  status: {
    sourced: "Sourced",
    applied: "Applied",
    screening: "Screening",
    interview: "Interview",
    offer: "Offer",
    hired: "Hired",
    rejected: "Rejected",
    pending: "Pending",
    inProgress: "In Progress",
    completed: "Completed",
    cancelled: "Cancelled",
    postponed: "Postponed",
    expired: "Expired",
    draft: "Draft",
    published: "Published",
    archived: "Archived",
    deleted: "Deleted",
  },

  toast: {
    success: {
      saved: "Saved successfully!",
      deleted: "Deleted successfully!",
      updated: "Updated successfully!",
      created: "Created successfully!",
      sent: "Sent successfully!",
      imported: "Imported successfully!",
      exported: "Exported successfully!",
      uploaded: "Uploaded successfully!",
      downloaded: "Downloaded successfully!",
    },
    error: {
      failed: "Operation failed!",
      notFound: "Not found!",
      unauthorized: "Unauthorized!",
      forbidden: "Forbidden!",
      validation: "Validation error!",
      network: "Network error!",
      server: "Server error!",
      timeout: "Request timeout!",
    },
    warning: {
      unsaved: "You have unsaved changes!",
      duplicate: "Duplicate data found!",
      limit: "Limit reached!",
      expires: "Will expire soon!",
    },
    info: {
      loading: "Loading...",
      processing: "Processing...",
      syncing: "Syncing...",
      scheduled: "Scheduled successfully!",
    },
  },

  // Landing Page
  landing: {
    hero: {
      title: "Smart Recruitment Platform",
      subtitle: "HireFlow - Advanced Applicant Tracking System",
      description:
        "Optimize your hiring process with AI, manage candidates effectively, and enhance recruitment experience for modern businesses.",
      ctaPrimary: "Try for Free",
      ctaSecondary: "Schedule Demo",
      trustBadge: "Trusted by 500+ companies",
    },
    features: {
      title: "Key Features",
      subtitle: "Comprehensive solution for recruitment management",
      candidateManagement: {
        title: "Candidate Management",
        description:
          "Smart storage and search of candidate profiles with AI parsing and automatic tagging.",
      },
      pipeline: {
        title: "Recruitment Pipeline",
        description:
          "Track candidate progress through stages with customizable workflows.",
      },
      interviews: {
        title: "Interview Management",
        description:
          "Schedule interviews, evaluate candidates, and collect interviewer feedback.",
      },
      automation: {
        title: "Email Automation",
        description:
          "Send automated emails based on process, ready templates and personalization.",
      },
      analytics: {
        title: "Reports & Analytics",
        description:
          "Detailed statistics dashboard on recruitment effectiveness and KPIs.",
      },
      collaboration: {
        title: "Team Collaboration",
        description:
          "Share feedback, notes and hiring decisions within the team.",
      },
    },
    benefits: {
      title: "Superior Benefits",
      subtitle: "Compared to traditional recruitment processes",
      efficiency: {
        title: "60% More Efficient",
        description:
          "Reduce hiring time and increase candidate quality with AI and automation.",
      },
      quality: {
        title: "Recruitment Quality",
        description:
          "Objective candidate evaluation with scoring system and structured interviews.",
      },
      experience: {
        title: "Candidate Experience",
        description:
          "Create positive impression with transparent process and consistent communication.",
      },
    },
    pricing: {
      title: "Flexible Pricing",
      subtitle: "Choose the plan that fits your business scale",
      monthly: "Monthly",
      yearly: "Yearly (Save 20%)",
      free: {
        name: "Free",
        price: "$0",
        description: "For startups and small teams",
        features: [
          "Up to 50 candidates",
          "3 job postings",
          "Basic pipeline",
          "Email templates",
          "Community support",
        ],
        cta: "Start Free",
      },
      pro: {
        name: "Pro",
        price: "$29",
        description: "For small and medium businesses",
        features: [
          "Unlimited candidates",
          "Unlimited job postings",
          "Advanced pipeline",
          "AI parsing & matching",
          "Email automation",
          "Reports & analytics",
          "Priority support",
        ],
        cta: "Choose Pro",
        popular: "Most Popular",
      },
      enterprise: {
        name: "Enterprise",
        price: "Contact us",
        description: "For corporations and large enterprises",
        features: [
          "All Pro features",
          "Custom workflow",
          "Advanced integrations",
          "SSO & LDAP",
          "Dedicated support",
          "Custom training",
          "SLA guarantee",
        ],
        cta: "Contact Sales",
      },
    },
    blog: {
      title: "News & Blog",
      subtitle: "HR Tech trends and recruitment insights",
      readMore: "Read more",
      viewAll: "View all articles",
    },
    contact: {
      title: "Contact Us",
      subtitle: "Ready to support and consult recruitment solutions",
      form: {
        name: "Full Name",
        email: "Email",
        company: "Company",
        message: "Message",
        send: "Send Message",
      },
      info: {
        address: "Floor 10, ABC Building, 123 XYZ Street, District 1, HCMC",
        phone: "+84 ***********",
        email: "<EMAIL>",
        support: "24/7 Support",
      },
    },
  },
};

// Translation context
interface I18nContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: Translations;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

// Translation hook
export const useTranslation = () => {
  const context = useContext(I18nContext);
  if (context === undefined) {
    // Fallback when used outside I18nProvider
    console.warn("useTranslation used outside I18nProvider, using fallback");
    return {
      t: viTranslations,
      language: "vi" as Language,
      setLanguage: () => {},
    };
  }
  return context;
};

// Get translations by language
export const getTranslations = (language: Language): Translations => {
  switch (language) {
    case "vi":
      return viTranslations;
    case "en":
      return enTranslations;
    default:
      return viTranslations; // Default to Vietnamese
  }
};

// Provider component
export const I18nProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<Language>("vi"); // Default to Vietnamese

  const translations = getTranslations(language);

  const value = {
    language,
    setLanguage,
    t: translations,
  };

  return <I18nContext.Provider value={value}>{children}</I18nContext.Provider>;
};
