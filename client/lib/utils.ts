import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { formatDistanceToNow, isValid } from "date-fns";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Safely formats a date string using formatDistanceToNow
 * Returns "Date unavailable" if the date is invalid
 */
export function safeFormatDistanceToNow(
  dateString: string | undefined | null,
  options?: { addSuffix?: boolean },
): string {
  if (!dateString || dateString.trim() === "") {
    return "Date unavailable";
  }

  const date = new Date(dateString);

  if (!isValid(date)) {
    return "Date unavailable";
  }

  try {
    return formatDistanceToNow(date, options);
  } catch (error) {
    console.warn("Error formatting date:", dateString, error);
    return "Date unavailable";
  }
}
