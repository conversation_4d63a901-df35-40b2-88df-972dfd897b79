// Language types
export type Language = "vi" | "en";

// Translation structure
export interface Translations {
  // Common
  common: {
    save: string;
    cancel: string;
    delete: string;
    edit: string;
    add: string;
    search: string;
    filter: string;
    export: string;
    import: string;
    loading: string;
    error: string;
    success: string;
    warning: string;
    info: string;
    confirm: string;
    yes: string;
    no: string;
    close: string;
    back: string;
    next: string;
    previous: string;
    submit: string;
    reset: string;
    clear: string;
    view: string;
    download: string;
    upload: string;
    refresh: string;
    settings: string;
    help: string;
    logout: string;
    profile: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    status: string;
    date: string;
    time: string;
    actions: string;
    total: string;
    active: string;
    inactive: string;
    enabled: string;
    disabled: string;
    required: string;
    optional: string;
    all: string;
    none: string;
    select: string;
    selectAll: string;
    deselectAll: string;
    today: string;
    yesterday: string;
    tomorrow: string;
    thisWeek: string;
    thisMonth: string;
    thisYear: string;
    language: string;
  };

  // Navigation
  nav: {
    dashboard: string;
    candidates: string;
    jobs: string;
    pipeline: string;
    calendar: string;
    interviewers: string;
    messages: string;
    analytics: string;
    profile: string;
    team: string;
    settings: string;
  };

  // Candidates
  candidates: {
    title: string;
    subtitle: string;
    viewDetails: string;
    sendEmail: string;
    scheduleInterview: string;
    updateStatus: string;
    addNote: string;
    exportProfile: string;
    aiSummary: string;
    aiScore: string;
    overallMatch: string;
    technicalSkills: string;
    softSkills: string;
    experience: string;
    strengths: string;
    improvements: string;
    recommendations: string;
    riskFactors: string;
    nextActions: string;
    generateReport: string;
    compareWith: string;
    matchToJobs: string;
  };

  // Dashboard
  dashboard: {
    title: string;
    subtitle: string;
    totalCandidates: string;
    newApplications: string;
    scheduledInterviews: string;
    successfulHires: string;
    recentActivity: string;
    quickActions: string;
    addCandidate: string;
    scheduleInterview: string;
    reviewApplications: string;
    sendMessage: string;
    viewAnalytics: string;
    systemHealth: string;
    performanceMetrics: string;
    upcomingInterviews: string;
    pendingTasks: string;
    teamActivity: string;
    aiInsights: string;
  };

  // Status Messages
  status: {
    sourced: string;
    applied: string;
    screening: string;
    interview: string;
    offer: string;
    hired: string;
    rejected: string;
    pending: string;
    inProgress: string;
    completed: string;
    cancelled: string;
    postponed: string;
    expired: string;
    draft: string;
    published: string;
    archived: string;
    deleted: string;
  };

  // Toast Messages
  toast: {
    success: {
      saved: string;
      deleted: string;
      updated: string;
      created: string;
      sent: string;
      imported: string;
      exported: string;
      uploaded: string;
      downloaded: string;
    };
    error: {
      failed: string;
      notFound: string;
      unauthorized: string;
      forbidden: string;
      validation: string;
      network: string;
      server: string;
      timeout: string;
    };
    warning: {
      unsaved: string;
      duplicate: string;
      limit: string;
      expires: string;
    };
    info: {
      loading: string;
      processing: string;
      syncing: string;
      scheduled: string;
    };
  };

  // Landing Page
  landing: {
    hero: {
      title: string;
      subtitle: string;
      description: string;
      ctaPrimary: string;
      ctaSecondary: string;
      trustBadge: string;
    };
    features: {
      title: string;
      subtitle: string;
      candidateManagement: {
        title: string;
        description: string;
      };
      pipeline: {
        title: string;
        description: string;
      };
      interviews: {
        title: string;
        description: string;
      };
      automation: {
        title: string;
        description: string;
      };
      analytics: {
        title: string;
        description: string;
      };
      collaboration: {
        title: string;
        description: string;
      };
    };
    benefits: {
      title: string;
      subtitle: string;
      efficiency: {
        title: string;
        description: string;
      };
      quality: {
        title: string;
        description: string;
      };
      experience: {
        title: string;
        description: string;
      };
    };
    pricing: {
      title: string;
      subtitle: string;
      monthly: string;
      yearly: string;
      free: {
        name: string;
        price: string;
        description: string;
        features: string[];
        cta: string;
      };
      pro: {
        name: string;
        price: string;
        description: string;
        features: string[];
        cta: string;
        popular: string;
      };
      enterprise: {
        name: string;
        price: string;
        description: string;
        features: string[];
        cta: string;
      };
    };
    blog: {
      title: string;
      subtitle: string;
      readMore: string;
      viewAll: string;
    };
    contact: {
      title: string;
      subtitle: string;
      form: {
        name: string;
        email: string;
        company: string;
        message: string;
        send: string;
      };
      info: {
        address: string;
        phone: string;
        email: string;
        support: string;
      };
    };
  };

  // Analytics
  analytics: {
    title: string;
    subtitle: string;
    dateFilter: string;
    exportReport: string;
    totalCandidates: string;
    timeToHire: string;
    conversionRate: string;
    activeJobs: string;
    pipelineHealth: string;
    conversionFunnel: string;
    sourceEffectiveness: string;
    jobPerformance: string;
    monthlyTrends: string;
    keyInsights: string;
    recommendationsTitle: string;
    nextSteps: string;
    successRate: string;
    candidates: string;
    hired: string;
    applications: string;
    interviews: string;
    hires: string;
    appliedToInterview: string;
    interviewToOffer: string;
    offerToHire: string;
    pipelineDistribution: string;
    stageConversion: string;
    candidateSource: string;
    positionPerformance: string;
    trendOverTime: string;
    insights: {
      linkedinTop: string;
      referralsBest: string;
      frontendMost: string;
      interviewImproved: string;
    };
    recommendations: {
      increaseReferral: string;
      optimizeLinkedin: string;
      reviewScreening: string;
      expandUx: string;
    };
    nextStepsItems: {
      scheduleReview: string;
      updateJobs: string;
      candidateSurvey: string;
      reviewCompensation: string;
    };
  };

  // Page Titles
  pageTitle: {
    // Dashboard and Analytics
    dashboard: string;
    analytics: string;

    // Candidates
    candidates: {
      list: string;
      detail: string;
      create: string;
      edit: string;
      profile: string;
    };

    // Jobs
    jobs: {
      list: string;
      detail: string;
      create: string;
      edit: string;
      applications: string;
    };

    // Pipeline
    pipeline: {
      overview: string;
      kanban: string;
      stage: string;
    };

    // Messages
    messages: {
      inbox: string;
      conversation: string;
      compose: string;
      templates: string;
    };

    // Interviews
    interviews: {
      list: string;
      detail: string;
      schedule: string;
      calendar: string;
      feedback: string;
    };

    // Interviewers
    interviewers: {
      list: string;
      profile: string;
      create: string;
      edit: string;
    };

    // Settings and Configuration
    settings: {
      general: string;
      account: string;
      team: string;
      integrations: string;
      notifications: string;
    };

    // Auth pages
    auth: {
      login: string;
      register: string;
      resetPassword: string;
      verifyEmail: string;
    };

    // Error pages
    error: {
      notFound: string;
      serverError: string;
      unauthorized: string;
    };
  };
}

// Translation context type
export interface I18nContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: Translations;
}
