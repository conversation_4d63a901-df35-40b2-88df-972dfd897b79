# Message Edit Draft & Gmail Quick Send Features

## 1. Edit Draft Messages Feature

### Overview
Users can now edit draft messages directly from the message detail popup. This feature allows users to modify and update draft messages before sending them.

### Implementation Details

#### MessageDetail.tsx Changes
- Added new `onEdit` prop to handle edit functionality
- Added "Edit Draft" button in dropdown menu for draft messages
- Added prominent "Chỉnh sửa nháp" button in quick actions for draft messages
- Only shows edit options when message status is "draft"

#### MessageForm.tsx Changes
- Added new `editMessage` prop and "edit" mode support
- Form auto-populates with existing draft message data
- Save button changes to "Cập nhật nháp" in edit mode
- Handles both updating existing drafts and creating new drafts
- Supports template variables from existing draft messages

#### MessagesWithTemplates.tsx Changes
- Added `handleEditClick` function to manage edit flow
- Updated MessageDetail component to include `onEdit` prop
- Updated MessageForm mode logic to detect draft messages and use edit mode
- Added `editMessage` prop for MessageForm when in edit mode

### Usage Flow
1. User clicks on a draft message in the message list
2. Message detail popup opens showing draft content
3. User sees "Chỉnh sửa nháp" button prominently displayed
4. Clicking edit opens MessageForm in edit mode with pre-filled data
5. User can modify content, recipients, subject, etc.
6. User can save as draft or send the message
7. System updates the existing draft record instead of creating new one

## 2. Gmail Quick Send Feature

### Overview
New Gmail Quick Send button allows users to open Gmail with pre-filled content directly from the message form, enabling quick external email sending.

### Implementation Details

#### MessageForm.tsx Changes
- Added `generateGmailUrl` function to create Gmail compose URLs
- Added `handleGmailQuickSend` function to open Gmail in new tab
- Added Gmail Quick Send button next to save draft button
- Button only shows for email type messages
- Uses Gmail compose URL parameters: `to`, `su` (subject), `body`, `tf=cm`

#### Gmail URL Structure
```
https://mail.google.com/mail/u/0/?fs=1&to=EMAIL&su=SUBJECT&body=CONTENT&tf=cm
```

### URL Parameters Used
- `fs=1`: Opens compose window
- `to`: Recipient email address
- `su`: Email subject
- `body`: Email content
- `tf=cm`: Opens in compose mode

### Usage Flow
1. User fills out message form (recipient, subject, content)
2. User clicks "Gửi qua Gmail" button (blue outlined button with @ and external link icons)
3. System validates that recipient email is provided
4. Gmail opens in new tab with pre-filled compose window
5. User can review and send from Gmail directly
6. Success toast shows "Mở Gmail để gửi tin nhắn"

### Error Handling
- Shows error if no recipient email is provided
- Gmail URL properly encodes special characters in subject and body
- New tab opening respects browser popup blockers

## 3. UI/UX Enhancements

### MessageDetail Visual Indicators
- Draft messages show prominent "Chỉnh sửa nháp" button with `ai-button` styling
- Edit option appears first in dropdown menu for drafts
- Clear visual separation between edit and other actions

### MessageForm Visual Elements
- Gmail button uses blue color scheme to indicate external action
- Icons: `@` (AtSign) and `↗` (ExternalLink) to clearly indicate Gmail integration
- Button positioned logically with other draft-related actions

### Button States
- All buttons properly handle loading states
- Disabled states during API calls
- Clear visual feedback for user actions

## 4. Technical Implementation Notes

### Type Safety
- Added proper TypeScript interfaces for edit mode
- Extended MessageFormProps with editMessage prop
- Proper handling of optional props and null states

### API Integration
- Uses existing `messageService.updateMessage()` for draft updates
- Maintains compatibility with existing message creation flow
- Proper error handling and user feedback

### Browser Compatibility
- Gmail URL generation works across all modern browsers
- `window.open()` with `_blank` for consistent new tab behavior
- URL encoding handled properly for Vietnamese content

## 5. Testing Checklist

- [ ] Draft messages show edit button in detail view
- [ ] Edit form pre-populates with draft data correctly
- [ ] Draft updates save to existing record (not create new)
- [ ] Gmail button generates correct URL with all parameters
- [ ] Gmail opens in new tab with pre-filled content
- [ ] Error handling for missing recipient email
- [ ] Vietnamese characters properly encoded in Gmail URL
- [ ] Template variables work in edit mode
- [ ] Loading states and disabled buttons work correctly
- [ ] Toast notifications show appropriate messages

## 6. Future Enhancements

### Potential Improvements
- Auto-save drafts while typing
- Gmail integration with attachments
- Support for CC/BCC in Gmail URL
- Rich text editor integration
- Draft version history
- Collaborative draft editing

### Gmail URL Extensions
- HTML formatted emails: `html=1` parameter
- CC recipients: `cc=<EMAIL>`
- BCC recipients: `bcc=<EMAIL>`
- Reply-to: `reply-to=<EMAIL>`
