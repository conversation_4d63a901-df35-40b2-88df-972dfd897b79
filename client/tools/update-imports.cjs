#!/usr/bin/env node

/**
 * Simple Import Updater
 * Updates import paths from legacy structure to new domain-driven architecture
 */

const fs = require('fs');
const path = require('path');

// Define import mappings from legacy to new structure
const importMappings = [
  // Candidate imports
  {
    from: '@/components/candidates/CandidateDetailModal',
    to: '@/domains/candidates/components',
  },
  {
    from: '@/components/candidates/AICandidateSummary',
    to: '@/domains/candidates/components',
  },
  {
    from: '@/components/candidates/AIScoreBadge',
    to: '@/domains/candidates/components',
  },
  {
    from: '@/components/candidates/CandidateActivityTimeline',
    to: '@/domains/candidates/components',
  },
  {
    from: '@/components/candidates/AdvancedFilters',
    to: '@/domains/candidates/components',
  },
  {
    from: '@/components/candidates/BulkActionsBar',
    to: '@/domains/candidates/components',
  },
  {
    from: './AICandidateSummary',
    to: './AICandidateSummary',
  },
  {
    from: './detail',
    to: './detail',
  },
  
  // Data imports (legacy types)
  {
    from: '@/data/mockData',
    to: '@/domains/candidates/types',
  },
  
  // Hook imports
  {
    from: '@/hooks/useApi',
    to: '@/domains/candidates/hooks',
  },
];

// File extensions to process
const fileExtensions = ['.ts', '.tsx', '.js', '.jsx'];

// Directories to exclude
const excludeDirs = ['node_modules', '.git', 'dist', 'build', '.next', '__tests__'];

/**
 * Get all files recursively
 */
function getAllFiles(dir, files = []) {
  const entries = fs.readdirSync(dir);
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!excludeDirs.includes(entry)) {
        getAllFiles(fullPath, files);
      }
    } else if (fileExtensions.includes(path.extname(entry))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Update imports in a file
 */
function updateImportsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let updatedContent = content;
    let hasChanges = false;
    
    for (const mapping of importMappings) {
      // Escape special regex characters
      const escapedFrom = mapping.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      
      // Handle different import patterns
      const patterns = [
        // import { Component } from 'path'
        new RegExp(`import\\s*{([^}]+)}\\s*from\\s*['"]${escapedFrom}['"]`, 'g'),
        // import Component from 'path'
        new RegExp(`import\\s+([^\\s{]+)\\s+from\\s*['"]${escapedFrom}['"]`, 'g'),
        // import * as Component from 'path'
        new RegExp(`import\\s*\\*\\s*as\\s+([^\\s]+)\\s+from\\s*['"]${escapedFrom}['"]`, 'g'),
      ];
      
      for (const pattern of patterns) {
        if (pattern.test(updatedContent)) {
          updatedContent = updatedContent.replace(pattern, (match) => {
            return match.replace(mapping.from, mapping.to);
          });
          hasChanges = true;
        }
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, updatedContent, 'utf8');
      console.log(`✅ Updated imports in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main function
 */
function main() {
  console.log('🚀 Starting legacy import migration...\n');
  
  const startTime = Date.now();
  const rootDir = process.cwd();
  
  console.log(`📁 Scanning directory: ${rootDir}`);
  console.log(`📋 Import mappings: ${importMappings.length}`);
  console.log('');
  
  // Get all files
  const allFiles = getAllFiles(rootDir);
  console.log(`📄 Found ${allFiles.length} files to process\n`);
  
  let updatedFiles = 0;
  let totalFiles = 0;
  
  // Process each file
  for (const file of allFiles) {
    totalFiles++;
    if (updateImportsInFile(file)) {
      updatedFiles++;
    }
  }
  
  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;
  
  console.log('\n📊 Migration Summary:');
  console.log(`   Total files processed: ${totalFiles}`);
  console.log(`   Files updated: ${updatedFiles}`);
  console.log(`   Duration: ${duration.toFixed(2)}s`);
  
  if (updatedFiles > 0) {
    console.log('\n✅ Import migration completed successfully!');
  } else {
    console.log('\n✨ No import updates needed!');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
