#!/usr/bin/env tsx
/**
 * Component Migration Tool
 * Automates the migration of components from old structure to domain-driven architecture
 */

import * as fs from "fs";
import * as path from "path";

interface MigrationConfig {
  componentName: string;
  sourcePath: string;
  targetDomain: string;
  dependencies: string[];
  componentType: "component" | "hook" | "service" | "type" | "util";
}

interface MigrationReport {
  success: boolean;
  componentName: string;
  sourcePath: string;
  targetPath: string;
  errors: string[];
  warnings: string[];
  updatedFiles: string[];
}

class ComponentMigrator {
  private rootPath: string;

  constructor(rootPath: string = process.cwd()) {
    this.rootPath = rootPath;
  }

  async migrateComponent(config: MigrationConfig): Promise<MigrationReport> {
    const report: MigrationReport = {
      success: false,
      componentName: config.componentName,
      sourcePath: config.sourcePath,
      targetPath: "",
      errors: [],
      warnings: [],
      updatedFiles: [],
    };

    try {
      // 1. Validate source file exists
      const fullSourcePath = path.join(this.rootPath, config.sourcePath);
      if (!fs.existsSync(fullSourcePath)) {
        report.errors.push(`Source file not found: ${config.sourcePath}`);
        return report;
      }

      // 2. Determine target path
      const targetPath = this.getTargetPath(config);
      report.targetPath = targetPath;

      // 3. Create target directory if it doesn't exist
      const targetDir = path.dirname(targetPath);
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      // 4. Copy and update the component file
      const sourceContent = fs.readFileSync(fullSourcePath, "utf-8");
      const updatedContent = this.updateComponentImports(sourceContent, config);
      fs.writeFileSync(targetPath, updatedContent);

      // 5. Update barrel exports
      this.updateBarrelExports(config);

      report.success = true;
      console.log(`✅ Successfully migrated ${config.componentName}`);
    } catch (error) {
      report.errors.push(`Migration failed: ${error.message}`);
      console.error(`❌ Failed to migrate ${config.componentName}:`, error);
    }

    return report;
  }

  private getTargetPath(config: MigrationConfig): string {
    const fileName = path.basename(config.sourcePath);
    return path.join(
      this.rootPath,
      "domains",
      config.targetDomain,
      config.componentType + "s",
      fileName,
    );
  }

  private updateComponentImports(
    content: string,
    config: MigrationConfig,
  ): string {
    // Update import paths to use new structure
    let updatedContent = content;

    // Replace old import paths with new ones
    const importMappings = {
      "@/components/ui": "@/shared/components/ui",
      "@/components/": "@/shared/components/",
      "@/hooks/": "@/shared/hooks/",
      "@/lib/": "@/shared/",
      "@/data/": "@/shared/data/",
    };

    for (const [oldPath, newPath] of Object.entries(importMappings)) {
      const regex = new RegExp(
        oldPath.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
        "g",
      );
      updatedContent = updatedContent.replace(regex, newPath);
    }

    return updatedContent;
  }

  private updateBarrelExports(config: MigrationConfig): void {
    const barrelPath = path.join(
      this.rootPath,
      "domains",
      config.targetDomain,
      config.componentType + "s",
      "index.ts",
    );

    if (fs.existsSync(barrelPath)) {
      const content = fs.readFileSync(barrelPath, "utf-8");
      const componentNameWithoutExt = config.componentName.replace(
        /\.(ts|tsx)$/,
        "",
      );
      const exportLine = `export * from './${componentNameWithoutExt}';`;

      if (!content.includes(exportLine)) {
        const updatedContent = content + "\n" + exportLine;
        fs.writeFileSync(barrelPath, updatedContent);
      }
    }
  }

  generateMigrationReport(reports: MigrationReport[]): void {
    const reportPath = path.join(this.rootPath, "migration-report.json");
    const summary = {
      timestamp: new Date().toISOString(),
      total: reports.length,
      successful: reports.filter((r) => r.success).length,
      failed: reports.filter((r) => !r.success).length,
      reports,
    };

    fs.writeFileSync(reportPath, JSON.stringify(summary, null, 2));
    console.log(`📊 Migration report saved to: ${reportPath}`);
  }
}

// Export for use in other scripts
export { ComponentMigrator, MigrationConfig, MigrationReport };
