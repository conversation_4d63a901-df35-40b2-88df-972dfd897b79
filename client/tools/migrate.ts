#!/usr/bin/env tsx
/**
 * Main Migration Script
 * Orchestrates the entire migration process
 */

import { ComponentMigrator, MigrationConfig } from "./component-migration";
import { ImportUpdater } from "./import-updater";
import { MigrationValidator } from "./validation-checker";

// Migration configurations for all components
const migrationConfigs: MigrationConfig[] = [
  // Candidate components
  {
    componentName: "CandidateDetailContent",
    sourcePath: "components/candidates/detail/CandidateDetailContent.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "CandidateHeader",
    sourcePath: "components/candidates/detail/CandidateHeader.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "CandidateContactInfo",
    sourcePath: "components/candidates/detail/CandidateContactInfo.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "CandidateStatusActions",
    sourcePath: "components/candidates/detail/CandidateStatusActions.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "CandidateProfessionalInfo",
    sourcePath: "components/candidates/detail/CandidateProfessionalInfo.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "CandidateSkills",
    sourcePath: "components/candidates/detail/CandidateSkills.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "CandidateDocuments",
    sourcePath: "components/candidates/detail/CandidateDocuments.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "CandidateNotes",
    sourcePath: "components/candidates/detail/CandidateNotes.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "EditCandidateModal",
    sourcePath: "components/candidates/EditCandidateModal.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "AIScoreBadge",
    sourcePath: "components/candidates/AIScoreBadge.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "AICandidateSummary",
    sourcePath: "components/candidates/AICandidateSummary.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "CandidateActivityTimeline",
    sourcePath: "components/candidates/CandidateActivityTimeline.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "AdvancedFilters",
    sourcePath: "components/candidates/AdvancedFilters.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
  {
    componentName: "BulkActionsBar",
    sourcePath: "components/candidates/BulkActionsBar.tsx",
    targetDomain: "candidates",
    dependencies: [],
    componentType: "component",
  },
];

async function runMigration() {
  console.log("🚀 Starting HireFlow Frontend Migration...\n");

  const migrator = new ComponentMigrator();
  const importUpdater = new ImportUpdater();
  const validator = new MigrationValidator();

  try {
    // Step 1: Migrate components
    console.log("📦 Step 1: Migrating components...");
    const migrationReports = [];

    for (const config of migrationConfigs) {
      const report = await migrator.migrateComponent(config);
      migrationReports.push(report);

      if (!report.success) {
        console.error(
          `❌ Failed to migrate ${config.componentName}:`,
          report.errors,
        );
      }
    }

    // Generate migration report
    migrator.generateMigrationReport(migrationReports);

    // Step 2: Update import paths
    console.log("\n🔄 Step 2: Updating import paths...");
    const importReport = await importUpdater.updateAllImports();
    importUpdater.generateUpdateReport(importReport);

    // Step 3: Validate migration
    console.log("\n✅ Step 3: Validating migration...");
    const validationResult = await validator.validateMigration();
    validator.generateValidationReport(validationResult);

    // Summary
    console.log("\n📊 Migration Summary:");
    console.log(
      `✅ Components migrated: ${migrationReports.filter((r) => r.success).length}/${migrationReports.length}`,
    );
    console.log(`🔄 Files with updated imports: ${importReport.updatedFiles}`);
    console.log(`⚠️  Validation warnings: ${validationResult.warnings.length}`);
    console.log(`❌ Validation errors: ${validationResult.errors.length}`);

    if (validationResult.success) {
      console.log("\n🎉 Migration completed successfully!");
    } else {
      console.log(
        "\n⚠️  Migration completed with issues. Please check the validation report.",
      );
    }
  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  runMigration();
}

export { runMigration, migrationConfigs };
