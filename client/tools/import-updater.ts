#!/usr/bin/env tsx
/**
 * Import Path Updater
 * Updates import paths across the codebase to match new domain-driven structure
 */

import * as fs from "fs";
import * as path from "path";
import { glob } from "glob";

interface ImportMapping {
  from: string;
  to: string;
  pattern: RegExp;
}

interface UpdateReport {
  totalFiles: number;
  updatedFiles: number;
  errors: string[];
  changes: Array<{
    file: string;
    oldImport: string;
    newImport: string;
  }>;
}

class ImportUpdater {
  private rootPath: string;
  private importMappings: ImportMapping[];

  constructor(rootPath: string = process.cwd()) {
    this.rootPath = rootPath;
    this.importMappings = this.createImportMappings();
  }

  private createImportMappings(): ImportMapping[] {
    return [
      // Components mappings
      {
        from: "@/components/candidates",
        to: "@/domains/candidates/components",
        pattern: /from\s+['"]@\/components\/candidates([^'"]*)['"]/g,
      },
      {
        from: "@/components/jobs",
        to: "@/domains/jobs/components",
        pattern: /from\s+['"]@\/components\/jobs([^'"]*)['"]/g,
      },
      {
        from: "@/components/calendar",
        to: "@/domains/calendar/components",
        pattern: /from\s+['"]@\/components\/calendar([^'"]*)['"]/g,
      },
      {
        from: "@/components/interviewers",
        to: "@/domains/interviews/components",
        pattern: /from\s+['"]@\/components\/interviewers([^'"]*)['"]/g,
      },
      {
        from: "@/components/dashboard",
        to: "@/domains/analytics/components",
        pattern: /from\s+['"]@\/components\/dashboard([^'"]*)['"]/g,
      },

      // Shared components
      {
        from: "@/components/ui",
        to: "@/shared/components/ui",
        pattern: /from\s+['"]@\/components\/ui([^'"]*)['"]/g,
      },
      {
        from: "@/components/layout",
        to: "@/shared/components/layout",
        pattern: /from\s+['"]@\/components\/layout([^'"]*)['"]/g,
      },

      // Hooks mappings
      {
        from: "@/hooks/useApi",
        to: "@/shared/hooks/useApi",
        pattern: /from\s+['"]@\/hooks\/useApi['"]/g,
      },
      {
        from: "@/hooks/",
        to: "@/shared/hooks/",
        pattern: /from\s+['"]@\/hooks\/([^'"]*)['"]/g,
      },

      // Lib mappings
      {
        from: "@/lib/api",
        to: "@/core/api",
        pattern: /from\s+['"]@\/lib\/api['"]/g,
      },
      {
        from: "@/lib/auth",
        to: "@/core/auth",
        pattern: /from\s+['"]@\/lib\/auth['"]/g,
      },
      {
        from: "@/lib/",
        to: "@/shared/",
        pattern: /from\s+['"]@\/lib\/([^'"]*)['"]/g,
      },

      // Data mappings
      {
        from: "@/data/",
        to: "@/shared/data/",
        pattern: /from\s+['"]@\/data\/([^'"]*)['"]/g,
      },
    ];
  }

  async updateAllImports(): Promise<UpdateReport> {
    const report: UpdateReport = {
      totalFiles: 0,
      updatedFiles: 0,
      errors: [],
      changes: [],
    };

    try {
      // Find all TypeScript/TSX files
      const files = await glob("**/*.{ts,tsx}", {
        cwd: this.rootPath,
        ignore: ["node_modules/**", "dist/**", "build/**", ".next/**"],
      });

      report.totalFiles = files.length;

      for (const file of files) {
        const filePath = path.join(this.rootPath, file);
        const updated = await this.updateFileImports(filePath, report);
        if (updated) {
          report.updatedFiles++;
        }
      }

      console.log(
        `✅ Updated imports in ${report.updatedFiles}/${report.totalFiles} files`,
      );
    } catch (error) {
      report.errors.push(`Failed to update imports: ${error.message}`);
      console.error("❌ Import update failed:", error);
    }

    return report;
  }

  private async updateFileImports(
    filePath: string,
    report: UpdateReport,
  ): Promise<boolean> {
    try {
      const content = fs.readFileSync(filePath, "utf-8");
      let updatedContent = content;
      let hasChanges = false;

      for (const mapping of this.importMappings) {
        const matches = content.match(mapping.pattern);
        if (matches) {
          for (const match of matches) {
            const newImport = this.transformImport(match, mapping);
            if (newImport !== match) {
              updatedContent = updatedContent.replace(match, newImport);
              hasChanges = true;

              report.changes.push({
                file: filePath,
                oldImport: match,
                newImport: newImport,
              });
            }
          }
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, updatedContent);
        console.log(
          `📝 Updated imports in: ${path.relative(this.rootPath, filePath)}`,
        );
      }

      return hasChanges;
    } catch (error) {
      report.errors.push(`Failed to update ${filePath}: ${error.message}`);
      return false;
    }
  }

  private transformImport(
    importStatement: string,
    mapping: ImportMapping,
  ): string {
    // Extract the import path and any sub-path
    const match = importStatement.match(/from\s+['"]([^'"]*)['"]/);
    if (!match) return importStatement;

    const fullImportPath = match[1];
    const subPath = fullImportPath.replace(mapping.from, "");
    const newImportPath = mapping.to + subPath;

    return importStatement.replace(fullImportPath, newImportPath);
  }

  generateUpdateReport(report: UpdateReport): void {
    const reportPath = path.join(this.rootPath, "import-update-report.json");
    const summary = {
      timestamp: new Date().toISOString(),
      ...report,
    };

    fs.writeFileSync(reportPath, JSON.stringify(summary, null, 2));
    console.log(`📊 Import update report saved to: ${reportPath}`);
  }
}

// Export for use in other scripts
export { ImportUpdater, ImportMapping, UpdateReport };
