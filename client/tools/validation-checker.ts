#!/usr/bin/env tsx
/**
 * Migration Validation Checker
 * Validates the migration process and checks for common issues
 */

import * as fs from "fs";
import * as path from "path";
import { glob } from "glob";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

interface ValidationResult {
  success: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    totalFiles: number;
    validFiles: number;
    filesWithErrors: number;
    filesWithWarnings: number;
  };
}

interface FileValidation {
  filePath: string;
  hasErrors: boolean;
  hasWarnings: boolean;
  errors: string[];
  warnings: string[];
}

class MigrationValidator {
  private rootPath: string;

  constructor(rootPath: string = process.cwd()) {
    this.rootPath = rootPath;
  }

  async validateMigration(): Promise<ValidationResult> {
    const result: ValidationResult = {
      success: false,
      errors: [],
      warnings: [],
      summary: {
        totalFiles: 0,
        validFiles: 0,
        filesWithErrors: 0,
        filesWithWarnings: 0,
      },
    };

    console.log("🔍 Starting migration validation...");

    try {
      // 1. Check TypeScript compilation
      await this.checkTypeScriptCompilation(result);

      // 2. Validate file structure
      await this.validateFileStructure(result);

      // 3. Check for unused imports
      await this.checkUnusedImports(result);

      // 4. Validate component exports
      await this.validateComponentExports(result);

      // 5. Check for circular dependencies
      await this.checkCircularDependencies(result);

      // 6. Validate barrel exports
      await this.validateBarrelExports(result);

      result.success = result.errors.length === 0;

      console.log(
        result.success ? "✅ Validation passed!" : "❌ Validation failed!",
      );
    } catch (error) {
      result.errors.push(`Validation process failed: ${error.message}`);
      console.error("❌ Validation process failed:", error);
    }

    return result;
  }

  private async checkTypeScriptCompilation(
    result: ValidationResult,
  ): Promise<void> {
    console.log("📝 Checking TypeScript compilation...");

    try {
      const { stdout, stderr } = await execAsync("npx tsc --noEmit", {
        cwd: this.rootPath,
      });

      if (stderr) {
        const errors = stderr.split("\n").filter((line) => line.trim());
        result.errors.push(...errors);
        console.log(`❌ Found ${errors.length} TypeScript errors`);
      } else {
        console.log("✅ TypeScript compilation successful");
      }
    } catch (error) {
      result.errors.push(`TypeScript compilation failed: ${error.message}`);
    }
  }

  private async validateFileStructure(result: ValidationResult): Promise<void> {
    console.log("📁 Validating file structure...");

    const requiredDomains = [
      "candidates",
      "jobs",
      "interviews",
      "calendar",
      "analytics",
    ];
    const requiredSharedDirs = [
      "components",
      "hooks",
      "services",
      "types",
      "utils",
    ];
    const requiredCoreDirs = ["api", "auth", "routing", "providers"];

    // Check domain structure
    for (const domain of requiredDomains) {
      const domainPath = path.join(this.rootPath, "domains", domain);
      if (!fs.existsSync(domainPath)) {
        result.errors.push(`Missing domain directory: domains/${domain}`);
        continue;
      }

      // Check domain subdirectories
      for (const subDir of requiredSharedDirs) {
        const subDirPath = path.join(domainPath, subDir);
        if (!fs.existsSync(subDirPath)) {
          result.warnings.push(
            `Missing subdirectory: domains/${domain}/${subDir}`,
          );
        }
      }
    }

    // Check shared structure
    const sharedPath = path.join(this.rootPath, "shared");
    if (!fs.existsSync(sharedPath)) {
      result.errors.push("Missing shared directory");
    } else {
      for (const subDir of requiredSharedDirs) {
        const subDirPath = path.join(sharedPath, subDir);
        if (!fs.existsSync(subDirPath)) {
          result.warnings.push(`Missing shared subdirectory: shared/${subDir}`);
        }
      }
    }

    // Check core structure
    const corePath = path.join(this.rootPath, "core");
    if (!fs.existsSync(corePath)) {
      result.errors.push("Missing core directory");
    } else {
      for (const subDir of requiredCoreDirs) {
        const subDirPath = path.join(corePath, subDir);
        if (!fs.existsSync(subDirPath)) {
          result.warnings.push(`Missing core subdirectory: core/${subDir}`);
        }
      }
    }

    console.log("✅ File structure validation completed");
  }

  private async checkUnusedImports(result: ValidationResult): Promise<void> {
    console.log("🔍 Checking for unused imports...");

    try {
      const files = await glob("**/*.{ts,tsx}", {
        cwd: this.rootPath,
        ignore: ["node_modules/**", "dist/**", "build/**", ".next/**"],
      });

      let unusedImportsCount = 0;

      for (const file of files) {
        const filePath = path.join(this.rootPath, file);
        const content = fs.readFileSync(filePath, "utf-8");

        // Simple check for unused imports (can be enhanced)
        const importLines = content
          .split("\n")
          .filter(
            (line) =>
              line.trim().startsWith("import") &&
              !line.includes("//") &&
              !line.includes("/*"),
          );

        for (const importLine of importLines) {
          const importMatch = importLine.match(
            /import\s+(?:\{([^}]+)\}|\*\s+as\s+(\w+)|(\w+))/,
          );
          if (importMatch) {
            const importedItems = importMatch[1]
              ? importMatch[1].split(",").map((item) => item.trim())
              : [importMatch[2] || importMatch[3]];

            for (const item of importedItems) {
              if (item && !content.includes(item.replace(/\s+as\s+\w+/, ""))) {
                result.warnings.push(
                  `Potentially unused import '${item}' in ${file}`,
                );
                unusedImportsCount++;
              }
            }
          }
        }
      }

      console.log(`✅ Found ${unusedImportsCount} potentially unused imports`);
    } catch (error) {
      result.warnings.push(`Failed to check unused imports: ${error.message}`);
    }
  }

  private async validateComponentExports(
    result: ValidationResult,
  ): Promise<void> {
    console.log("📦 Validating component exports...");

    try {
      const domains = [
        "candidates",
        "jobs",
        "interviews",
        "calendar",
        "analytics",
      ];

      for (const domain of domains) {
        const componentsDir = path.join(
          this.rootPath,
          "domains",
          domain,
          "components",
        );
        if (!fs.existsSync(componentsDir)) continue;

        const componentFiles = await glob("*.{ts,tsx}", { cwd: componentsDir });
        const indexPath = path.join(componentsDir, "index.ts");

        if (fs.existsSync(indexPath)) {
          const indexContent = fs.readFileSync(indexPath, "utf-8");

          for (const file of componentFiles) {
            if (file === "index.ts") continue;

            const componentName = path.basename(file, path.extname(file));
            const exportPattern = new RegExp(
              `export.*from.*['"]\./${componentName}['"]`,
            );

            if (!exportPattern.test(indexContent)) {
              result.warnings.push(
                `Component ${componentName} not exported from ${domain}/components/index.ts`,
              );
            }
          }
        } else {
          result.warnings.push(
            `Missing index.ts in domains/${domain}/components/`,
          );
        }
      }

      console.log("✅ Component exports validation completed");
    } catch (error) {
      result.warnings.push(
        `Failed to validate component exports: ${error.message}`,
      );
    }
  }

  private async checkCircularDependencies(
    result: ValidationResult,
  ): Promise<void> {
    console.log("🔄 Checking for circular dependencies...");

    try {
      // This is a simplified check - in production, you might want to use a more sophisticated tool
      const files = await glob("**/*.{ts,tsx}", {
        cwd: this.rootPath,
        ignore: ["node_modules/**", "dist/**", "build/**", ".next/**"],
      });

      const dependencyMap = new Map<string, Set<string>>();

      // Build dependency map
      for (const file of files) {
        const filePath = path.join(this.rootPath, file);
        const content = fs.readFileSync(filePath, "utf-8");
        const imports = content.match(/from\s+['"]([^'"]*)['"]/g) || [];

        const dependencies = new Set<string>();
        for (const importMatch of imports) {
          const importPath = importMatch.match(/from\s+['"]([^'"]*)['"]/)?.[1];
          if (importPath && importPath.startsWith("@/")) {
            dependencies.add(importPath);
          }
        }

        dependencyMap.set("@/" + file.replace(/\.(ts|tsx)$/, ""), dependencies);
      }

      // Simple circular dependency detection
      for (const [file, deps] of dependencyMap) {
        for (const dep of deps) {
          const depDeps = dependencyMap.get(dep);
          if (depDeps && depDeps.has(file)) {
            result.warnings.push(
              `Potential circular dependency: ${file} ↔ ${dep}`,
            );
          }
        }
      }

      console.log("✅ Circular dependency check completed");
    } catch (error) {
      result.warnings.push(
        `Failed to check circular dependencies: ${error.message}`,
      );
    }
  }

  private async validateBarrelExports(result: ValidationResult): Promise<void> {
    console.log("📋 Validating barrel exports...");

    try {
      const barrelFiles = await glob("**/index.ts", {
        cwd: this.rootPath,
        ignore: ["node_modules/**", "dist/**", "build/**", ".next/**"],
      });

      for (const barrelFile of barrelFiles) {
        const barrelPath = path.join(this.rootPath, barrelFile);
        const content = fs.readFileSync(barrelPath, "utf-8");

        // Check if barrel file has exports
        if (!content.includes("export") && content.trim().length > 0) {
          result.warnings.push(
            `Barrel file ${barrelFile} has content but no exports`,
          );
        }

        // Check for syntax errors in exports
        const exportLines = content
          .split("\n")
          .filter((line) => line.includes("export"));
        for (const line of exportLines) {
          if (!line.match(/export\s+(\*|{[^}]*})\s+from\s+['"][^'"]*['"];?/)) {
            result.warnings.push(
              `Potentially malformed export in ${barrelFile}: ${line.trim()}`,
            );
          }
        }
      }

      console.log("✅ Barrel exports validation completed");
    } catch (error) {
      result.warnings.push(
        `Failed to validate barrel exports: ${error.message}`,
      );
    }
  }

  generateValidationReport(result: ValidationResult): void {
    const reportPath = path.join(this.rootPath, "validation-report.json");
    const summary = {
      timestamp: new Date().toISOString(),
      ...result,
    };

    fs.writeFileSync(reportPath, JSON.stringify(summary, null, 2));
    console.log(`📊 Validation report saved to: ${reportPath}`);

    // Print summary
    console.log("\n📊 Validation Summary:");
    console.log(`Total Files: ${result.summary.totalFiles}`);
    console.log(`Valid Files: ${result.summary.validFiles}`);
    console.log(`Files with Errors: ${result.summary.filesWithErrors}`);
    console.log(`Files with Warnings: ${result.summary.filesWithWarnings}`);
    console.log(`Total Errors: ${result.errors.length}`);
    console.log(`Total Warnings: ${result.warnings.length}`);
  }
}

// Export for use in other scripts
export { MigrationValidator, ValidationResult, FileValidation };
