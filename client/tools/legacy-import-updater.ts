#!/usr/bin/env node

/**
 * Legacy Import Updater
 * Updates import paths from legacy structure to new domain-driven architecture
 */

import * as fs from 'fs';
import * as path from 'path';

interface ImportMapping {
  from: string;
  to: string;
  description: string;
}

// Define import mappings from legacy to new structure
const importMappings: ImportMapping[] = [
  // Candidate imports
  {
    from: '@/components/candidates/CandidateDetailModal',
    to: '@/domains/candidates/components',
    description: 'Candidate detail modal'
  },
  {
    from: '@/components/candidates/AICandidateSummary',
    to: '@/domains/candidates/components',
    description: 'AI candidate summary'
  },
  {
    from: '@/components/candidates/AIScoreBadge',
    to: '@/domains/candidates/components',
    description: 'AI score badge'
  },
  {
    from: '@/components/candidates/CandidateActivityTimeline',
    to: '@/domains/candidates/components',
    description: 'Candidate activity timeline'
  },
  {
    from: '@/components/candidates/AdvancedFilters',
    to: '@/domains/candidates/components',
    description: 'Advanced filters'
  },
  {
    from: '@/components/candidates/BulkActionsBar',
    to: '@/domains/candidates/components',
    description: 'Bulk actions bar'
  },
  {
    from: '@/components/candidates/detail',
    to: '@/domains/candidates/components/detail',
    description: 'Candidate detail components'
  },
  
  // Job imports
  {
    from: '@/components/jobs/JobDetailModal',
    to: '@/domains/jobs/components',
    description: 'Job detail modal'
  },
  {
    from: '@/components/jobs/AddEditJobModal',
    to: '@/domains/jobs/components',
    description: 'Add/edit job modal'
  },
  {
    from: '@/components/jobs/BulkJobActions',
    to: '@/domains/jobs/components',
    description: 'Bulk job actions'
  },
  {
    from: '@/components/jobs/detail',
    to: '@/domains/jobs/components/detail',
    description: 'Job detail components'
  },
  
  // Interview/Calendar imports
  {
    from: '@/components/calendar',
    to: '@/domains/interviews/components/calendar',
    description: 'Calendar/interview components'
  },
  
  // Data imports (legacy types)
  {
    from: '@/data/mockData',
    to: '@/domains/candidates/types',
    description: 'Legacy candidate types'
  },
  
  // Hook imports
  {
    from: '@/hooks/useApi',
    to: '@/domains/candidates/hooks',
    description: 'Legacy API hooks'
  },
  
  // Service imports
  {
    from: '@/lib/services/candidateAnalysisService',
    to: '@/domains/candidates/services',
    description: 'Candidate analysis service'
  },
  {
    from: '@/lib/services/InterviewFeedbackService',
    to: '@/domains/interviews/services',
    description: 'Interview feedback service'
  },
  {
    from: '@/lib/services/InterviewerService',
    to: '@/domains/interviews/services',
    description: 'Interviewer service'
  },
];

// File extensions to process
const fileExtensions = ['.ts', '.tsx', '.js', '.jsx'];

// Directories to exclude
const excludeDirs = ['node_modules', '.git', 'dist', 'build', '.next'];

/**
 * Get all files recursively
 */
function getAllFiles(dir: string, files: string[] = []): string[] {
  const entries = fs.readdirSync(dir);
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!excludeDirs.includes(entry)) {
        getAllFiles(fullPath, files);
      }
    } else if (fileExtensions.includes(path.extname(entry))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Update imports in a file
 */
function updateImportsInFile(filePath: string): boolean {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let updatedContent = content;
    let hasChanges = false;
    
    for (const mapping of importMappings) {
      // Handle different import patterns
      const patterns = [
        // import { Component } from 'path'
        new RegExp(`import\\s*{([^}]+)}\\s*from\\s*['"]${mapping.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g'),
        // import Component from 'path'
        new RegExp(`import\\s+([^\\s{]+)\\s+from\\s*['"]${mapping.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g'),
        // import * as Component from 'path'
        new RegExp(`import\\s*\\*\\s*as\\s+([^\\s]+)\\s+from\\s*['"]${mapping.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g'),
      ];
      
      for (const pattern of patterns) {
        if (pattern.test(updatedContent)) {
          updatedContent = updatedContent.replace(pattern, (match) => {
            return match.replace(mapping.from, mapping.to);
          });
          hasChanges = true;
        }
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, updatedContent, 'utf8');
      console.log(`✅ Updated imports in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error);
    return false;
  }
}

/**
 * Main function
 */
function main() {
  console.log('🚀 Starting legacy import migration...\n');
  
  const startTime = Date.now();
  const rootDir = process.cwd();
  
  console.log(`📁 Scanning directory: ${rootDir}`);
  console.log(`📋 Import mappings: ${importMappings.length}`);
  console.log('');
  
  // Get all files
  const allFiles = getAllFiles(rootDir);
  console.log(`📄 Found ${allFiles.length} files to process\n`);
  
  let updatedFiles = 0;
  let totalFiles = 0;
  
  // Process each file
  for (const file of allFiles) {
    totalFiles++;
    if (updateImportsInFile(file)) {
      updatedFiles++;
    }
  }
  
  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;
  
  console.log('\n📊 Migration Summary:');
  console.log(`   Total files processed: ${totalFiles}`);
  console.log(`   Files updated: ${updatedFiles}`);
  console.log(`   Duration: ${duration.toFixed(2)}s`);
  
  if (updatedFiles > 0) {
    console.log('\n✅ Import migration completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Run TypeScript check: npm run type-check');
    console.log('   2. Run tests: npm test');
    console.log('   3. Fix any remaining import issues manually');
  } else {
    console.log('\n✨ No import updates needed!');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { main as updateLegacyImports };
