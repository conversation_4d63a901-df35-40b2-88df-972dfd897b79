/**
 * CandidateCard Component Tests
 */

import React from 'react';
import { render, screen, fireEvent } from '@/__tests__/utils/test-utils';
import { CandidateCard } from '@/domains/candidates/components';
import { createMockCandidate } from '@/__tests__/utils/test-utils';

describe('CandidateCard', () => {
  const mockCandidate = createMockCandidate();
  const mockProps = {
    candidate: mockCandidate,
    onView: jest.fn(),
    onEdit: jest.fn(),
    onDelete: jest.fn(),
    onSelect: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders candidate information correctly', () => {
    render(<CandidateCard {...mockProps} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Software Engineer')).toBeInTheDocument();
    expect(screen.getByText('new')).toBeInTheDocument();
  });

  it('displays candidate skills', () => {
    render(<CandidateCard {...mockProps} />);

    expect(screen.getByText('JavaScript')).toBeInTheDocument();
    expect(screen.getByText('React')).toBeInTheDocument();
    expect(screen.getByText('Node.js')).toBeInTheDocument();
  });

  it('calls onView when card is clicked', () => {
    render(<CandidateCard {...mockProps} />);

    fireEvent.click(screen.getByRole('button', { name: /view/i }));
    expect(mockProps.onView).toHaveBeenCalledWith(mockCandidate);
  });

  it('calls onEdit when edit action is clicked', () => {
    render(<CandidateCard {...mockProps} />);

    // Open dropdown menu
    fireEvent.click(screen.getByRole('button', { name: /more/i }));
    
    // Click edit option
    fireEvent.click(screen.getByText('Edit'));
    expect(mockProps.onEdit).toHaveBeenCalledWith(mockCandidate);
  });

  it('calls onDelete when delete action is clicked', () => {
    render(<CandidateCard {...mockProps} />);

    // Open dropdown menu
    fireEvent.click(screen.getByRole('button', { name: /more/i }));
    
    // Click delete option
    fireEvent.click(screen.getByText('Delete'));
    expect(mockProps.onDelete).toHaveBeenCalledWith(mockCandidate);
  });

  it('renders compact variant correctly', () => {
    render(<CandidateCard {...mockProps} variant="compact" />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Software Engineer')).toBeInTheDocument();
    
    // Should not show detailed information in compact mode
    expect(screen.queryByText('<EMAIL>')).not.toBeInTheDocument();
  });

  it('shows selection state when selectable', () => {
    render(
      <CandidateCard 
        {...mockProps} 
        selectable={true} 
        selected={true} 
      />
    );

    const card = screen.getByRole('article');
    expect(card).toHaveClass('ring-2', 'ring-primary');
  });

  it('hides actions when showActions is false', () => {
    render(<CandidateCard {...mockProps} showActions={false} />);

    expect(screen.queryByRole('button', { name: /more/i })).not.toBeInTheDocument();
  });

  it('handles missing optional data gracefully', () => {
    const candidateWithoutOptionalData = createMockCandidate({
      phone: undefined,
      skills: undefined,
      location: undefined,
      expectedSalary: undefined,
    });

    render(
      <CandidateCard 
        {...mockProps} 
        candidate={candidateWithoutOptionalData} 
      />
    );

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
});
