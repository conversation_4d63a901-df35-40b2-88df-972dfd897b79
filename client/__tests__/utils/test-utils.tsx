/**
 * Test Utilities
 * Custom render functions and test helpers
 */

import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AppProviders } from '@/app/providers/AppProviders';

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };

// Test data factories
export const createMockCandidate = (overrides = {}) => ({
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+**********',
  position: 'Software Engineer',
  experience: 5,
  expectedSalary: 100000,
  skills: ['JavaScript', 'React', 'Node.js'],
  status: 'new' as const,
  location: 'San Francisco, CA',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const createMockJob = (overrides = {}) => ({
  id: '1',
  title: 'Software Engineer',
  description: 'We are looking for a software engineer...',
  department: 'Engineering',
  location: 'San Francisco, CA',
  remoteType: 'hybrid' as const,
  employmentType: 'full-time' as const,
  experienceLevel: 'mid' as const,
  requirements: 'Bachelor degree in Computer Science...',
  requiredSkills: ['JavaScript', 'React', 'Node.js'],
  status: 'published' as const,
  currency: 'USD',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const createMockInterview = (overrides = {}) => ({
  id: '1',
  title: 'Technical Interview',
  type: 'technical' as const,
  round: 'first' as const,
  status: 'scheduled' as const,
  candidateId: '1',
  candidateName: 'John Doe',
  candidateEmail: '<EMAIL>',
  jobId: '1',
  jobTitle: 'Software Engineer',
  interviewers: [],
  primaryInterviewerId: 'interviewer-1',
  scheduledAt: '2024-01-15T10:00:00Z',
  duration: 60,
  timezone: 'America/Los_Angeles',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides,
});

// Mock hooks
export const mockUseCandidates = {
  candidates: [],
  meta: {
    total: 0,
    page: 1,
    limit: 10,
    hasNext: false,
    hasPrev: false,
  },
  isLoading: false,
  isError: false,
  error: null,
  refetch: jest.fn(),
};

export const mockUseCandidateTable = {
  candidates: [],
  meta: {
    total: 0,
    page: 1,
    limit: 10,
    hasNext: false,
    hasPrev: false,
  },
  isLoading: false,
  isError: false,
  error: null,
  page: 1,
  limit: 10,
  setPage: jest.fn(),
  setLimit: jest.fn(),
  filters: {},
  setFilters: jest.fn(),
  updateFilter: jest.fn(),
  clearFilters: jest.fn(),
  hasActiveFilters: false,
  selectedIds: [],
  selectedCount: 0,
  toggleSelection: jest.fn(),
  selectAll: jest.fn(),
  clearSelection: jest.fn(),
  isSelected: jest.fn(),
  hasSelection: false,
  createCandidate: jest.fn(),
  updateCandidate: jest.fn(),
  deleteCandidate: jest.fn(),
  updateStatus: jest.fn(),
  bulkUpdateStatus: jest.fn(),
  uploadResume: jest.fn(),
  refetch: jest.fn(),
};
