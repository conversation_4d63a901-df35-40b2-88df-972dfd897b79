/**
 * Job API Mock Handlers
 * MSW handlers for job-related API endpoints
 */

import { http, HttpResponse } from 'msw';

export const jobHandlers = [
  // Get jobs
  http.get('/api/jobs', () => {
    return HttpResponse.json({
      success: true,
      data: [],
      meta: {
        total: 0,
        page: 1,
        limit: 10,
        hasNext: false,
        hasPrev: false,
      },
    });
  }),
];
