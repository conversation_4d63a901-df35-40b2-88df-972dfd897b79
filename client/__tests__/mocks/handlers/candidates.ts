/**
 * Candidate API Mock Handlers
 * MSW handlers for candidate-related API endpoints
 */

import { http, HttpResponse } from 'msw';
import { Candidate } from '@/domains/candidates/types';

// Mock candidate data
const mockCandidates: Candidate[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    position: 'Software Engineer',
    experience: 5,
    expectedSalary: 100000,
    skills: ['JavaScript', 'React', 'Node.js'],
    status: 'new',
    location: 'San Francisco, CA',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567891',
    position: 'Product Manager',
    experience: 7,
    expectedSalary: 120000,
    skills: ['Product Management', 'Analytics', 'Strategy'],
    status: 'screening',
    location: 'New York, NY',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
];

export const candidateHandlers = [
  // Get candidates
  http.get('/api/candidates', ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search');
    const status = url.searchParams.get('status');

    let filteredCandidates = [...mockCandidates];

    // Apply filters
    if (search) {
      filteredCandidates = filteredCandidates.filter(candidate =>
        candidate.name.toLowerCase().includes(search.toLowerCase()) ||
        candidate.email.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (status) {
      const statusArray = status.split(',');
      filteredCandidates = filteredCandidates.filter(candidate =>
        statusArray.includes(candidate.status)
      );
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCandidates = filteredCandidates.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedCandidates,
      meta: {
        total: filteredCandidates.length,
        page,
        limit,
        hasNext: endIndex < filteredCandidates.length,
        hasPrev: page > 1,
      },
    });
  }),

  // Get candidate by ID
  http.get('/api/candidates/:id', ({ params }) => {
    const { id } = params;
    const candidate = mockCandidates.find(c => c.id === id);

    if (!candidate) {
      return HttpResponse.json(
        { success: false, message: 'Candidate not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: candidate,
    });
  }),

  // Create candidate
  http.post('/api/candidates', async ({ request }) => {
    const data = await request.json() as any;
    
    const newCandidate: Candidate = {
      id: String(mockCandidates.length + 1),
      ...data,
      status: data.status || 'new',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockCandidates.push(newCandidate);

    return HttpResponse.json({
      success: true,
      data: newCandidate,
    });
  }),

  // Update candidate
  http.put('/api/candidates/:id', async ({ params, request }) => {
    const { id } = params;
    const data = await request.json() as any;
    
    const candidateIndex = mockCandidates.findIndex(c => c.id === id);
    
    if (candidateIndex === -1) {
      return HttpResponse.json(
        { success: false, message: 'Candidate not found' },
        { status: 404 }
      );
    }

    mockCandidates[candidateIndex] = {
      ...mockCandidates[candidateIndex],
      ...data,
      updatedAt: new Date().toISOString(),
    };

    return HttpResponse.json({
      success: true,
      data: mockCandidates[candidateIndex],
    });
  }),

  // Delete candidate
  http.delete('/api/candidates/:id', ({ params }) => {
    const { id } = params;
    const candidateIndex = mockCandidates.findIndex(c => c.id === id);
    
    if (candidateIndex === -1) {
      return HttpResponse.json(
        { success: false, message: 'Candidate not found' },
        { status: 404 }
      );
    }

    mockCandidates.splice(candidateIndex, 1);

    return HttpResponse.json({
      success: true,
      message: 'Candidate deleted successfully',
    });
  }),

  // Update candidate status
  http.patch('/api/candidates/:id/status', async ({ params, request }) => {
    const { id } = params;
    const { status, notes } = await request.json() as any;
    
    const candidateIndex = mockCandidates.findIndex(c => c.id === id);
    
    if (candidateIndex === -1) {
      return HttpResponse.json(
        { success: false, message: 'Candidate not found' },
        { status: 404 }
      );
    }

    mockCandidates[candidateIndex] = {
      ...mockCandidates[candidateIndex],
      status,
      updatedAt: new Date().toISOString(),
    };

    return HttpResponse.json({
      success: true,
      data: mockCandidates[candidateIndex],
    });
  }),

  // Get candidate stats
  http.get('/api/candidates/stats', () => {
    const stats = {
      total: mockCandidates.length,
      byStatus: mockCandidates.reduce((acc, candidate) => {
        acc[candidate.status] = (acc[candidate.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      recentApplications: mockCandidates.filter(c => 
        new Date(c.createdAt).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000
      ).length,
      averageExperience: mockCandidates.reduce((sum, c) => sum + (c.experience || 0), 0) / mockCandidates.length,
    };

    return HttpResponse.json({
      success: true,
      data: stats,
    });
  }),
];
