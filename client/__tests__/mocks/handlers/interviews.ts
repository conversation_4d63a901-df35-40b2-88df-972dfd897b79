/**
 * Interview API Mock Handlers
 * MSW handlers for interview-related API endpoints
 */

import { http, HttpResponse } from 'msw';

export const interviewHandlers = [
  // Get interviews
  http.get('/api/interviews', () => {
    return HttpResponse.json({
      success: true,
      data: [],
      meta: {
        total: 0,
        page: 1,
        limit: 10,
        hasNext: false,
        hasPrev: false,
      },
    });
  }),
];
