/**
 * MSW Server Setup
 * Mock Service Worker for API mocking in tests
 */

import { setupServer } from 'msw/node';
import { candidateHandlers } from './handlers/candidates';
import { jobHandlers } from './handlers/jobs';
import { interviewHandlers } from './handlers/interviews';

// Setup MSW server with all handlers
export const server = setupServer(
  ...candidateHandlers,
  ...jobHandlers,
  ...interviewHandlers
);
