// User Management Hooks using React Query
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/lib/api";
import { useNotifications } from "./use-notifications";
import { 
  UserListParams, 
  CreateUserData, 
  UpdateUserData,
  User,
  UserListResponse,
  TeamStatistics 
} from "@/lib/types/userManagement";

// Query keys
export const userQueryKeys = {
  users: (params?: UserListParams) => ["users", params],
  user: (id: number, include?: string) => ["user", id, include],
  userRoles: () => ["user-roles"],
  userStatistics: () => ["user-statistics"],
};

// Get users list with pagination and filtering
export const useUsers = (params?: UserListParams) => {
  return useQuery({
    queryKey: userQueryKeys.users(params),
    queryFn: () => apiService.getUsers(params),
    enabled: !!localStorage.getItem("auth_token"),
    retry: false,
  });
};

// Get single user details
export const useUser = (id: number, include?: string) => {
  return useQuery({
    queryKey: userQueryKeys.user(id, include),
    queryFn: () => apiService.getUser(id, include),
    enabled: !!id && !!localStorage.getItem("auth_token"),
    retry: false,
  });
};

// Get available roles
export const useUserRoles = () => {
  return useQuery({
    queryKey: userQueryKeys.userRoles(),
    queryFn: () => apiService.getUserRoles(),
    enabled: !!localStorage.getItem("auth_token"),
    staleTime: 300000, // 5 minutes
    retry: false,
  });
};

// Get user statistics
export const useUserStatistics = () => {
  return useQuery({
    queryKey: userQueryKeys.userStatistics(),
    queryFn: () => apiService.getUserStatistics(),
    enabled: !!localStorage.getItem("auth_token"),
    retry: false,
  });
};

// Create user mutation
export const useCreateUser = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (userData: CreateUserData) => apiService.createUser(userData),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["user-statistics"] });
      notifications.success.created();
    },
    onError: (error: any) => {
      console.error("Create user error:", error);
      notifications.showApiError(error);
    },
  });
};

// Update user mutation
export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateUserData }) =>
      apiService.updateUser(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["user", variables.id] });
      queryClient.invalidateQueries({ queryKey: ["user-statistics"] });
      notifications.success.updated();
    },
    onError: (error: any) => {
      console.error("Update user error:", error);
      notifications.showApiError(error);
    },
  });
};

// Update user role mutation
export const useUpdateUserRole = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ id, role }: { id: number; role: string }) =>
      apiService.updateUserRole(id, role),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["user", variables.id] });
      queryClient.invalidateQueries({ queryKey: ["user-statistics"] });
      notifications.success.updated();
    },
    onError: (error: any) => {
      console.error("Update user role error:", error);
      notifications.showApiError(error);
    },
  });
};

// Deactivate user mutation
export const useDeactivateUser = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (id: number) => apiService.deactivateUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["user-statistics"] });
      notifications.success.deleted();
    },
    onError: (error: any) => {
      console.error("Deactivate user error:", error);
      if (error.statusCode === 403) {
        notifications.error.custom("Bạn không có quyền vô hiệu hóa người dùng này");
      } else {
        notifications.showApiError(error);
      }
    },
  });
};
