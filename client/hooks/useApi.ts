// React Query hooks for API integration
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import { apiService } from "@/lib/api";
import { useNotifications } from "./use-notifications";

// Query Keys
export const queryKeys = {
  candidates: (params?: any) => ["candidates", params],
  candidate: (id: string, include?: string) => ["candidate", id, include],
  jobs: (params?: any) => ["jobs", params],
  job: (id: string, include?: string) => ["job", id, include],
  jobCandidates: (id: string, params?: any) => ["job-candidates", id, params],
  interviews: (params?: any) => ["interviews", params],
  interview: (id: string, include?: string) => ["interview", id, include],
  interviewFeedback: (params?: any) => ["interview-feedback", params],
  interviewers: (params?: any) => ["interviewers", params],
  interviewer: (id: string, params?: any) => ["interviewer", id, params],
  interviewerAvailability: (params: any) => [
    "interviewer-availability",
    params,
  ],
  interviewFeedbackDetails: (id: string, include?: string) => [
    "interview-feedback",
    id,
    include,
  ],
  calendarEvents: (params: any) => ["calendar-events", params],
  dashboard: (dateFrom?: string, dateTo?: string) => [
    "dashboard",
    dateFrom,
    dateTo,
  ],
  user: () => ["user"],
  jobAnalytics: (id: string) => ["job-analytics", id],
  interviewerAvailability: (params: any) => [
    "interviewer-availability",
    params,
  ],
};

// Auth hooks
export const useLogin = () => {
  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      apiService.login(email, password),
  });
};

export const useLogout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => apiService.logout(),
    onSuccess: () => {
      queryClient.clear();
    },
  });
};

export const useCurrentUser = () => {
  return useQuery({
    queryKey: queryKeys.user(),
    queryFn: () => apiService.getCurrentUser(),
    enabled: !!localStorage.getItem("auth_token"),
    retry: false,
  });
};

// Candidates hooks
export const useCandidates = (params?: {
  page?: number;
  per_page?: number;
  filter?: Record<string, any>;
  sort?: string;
  include?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.candidates(params),
    queryFn: () => apiService.getCandidates(params || {}),
    enabled: !!localStorage.getItem("auth_token"),
  });
};

export const useCandidate = (id: string, include?: string) => {
  return useQuery({
    queryKey: queryKeys.candidate(id, include),
    queryFn: () => apiService.getCandidate(id, include),
    enabled: !!id && !!localStorage.getItem("auth_token"),
  });
};

export const useCreateCandidate = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (candidateData: any) =>
      apiService.createCandidate(candidateData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      notifications.success.created();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
      console.error("Create candidate error:", error);
    },
  });
};

export const useUpdateCandidate = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      apiService.updateCandidate(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      queryClient.invalidateQueries({ queryKey: ["candidate", variables.id] });
      notifications.success.updated();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

export const useUpdateCandidateStatus = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({
      id,
      status,
      notes,
    }: {
      id: string;
      status: string;
      notes?: string;
    }) => apiService.updateCandidateStatus(id, status, notes),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      queryClient.invalidateQueries({ queryKey: ["candidate", variables.id] });
      notifications.success.updated();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

export const useDeleteCandidate = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (id: string) => apiService.deleteCandidate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      notifications.success.deleted();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

export const useUploadResume = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ candidateId, file }: { candidateId: string; file: File }) =>
      apiService.uploadResume(candidateId, file),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["candidate", variables.candidateId],
      });
      notifications.success.uploaded();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

export const useTriggerAIAnalysis = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (candidateId: string) =>
      apiService.triggerAIAnalysis(candidateId),
    onSuccess: (_, candidateId) => {
      queryClient.invalidateQueries({ queryKey: ["candidate", candidateId] });
      notifications.info.processing();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

// Jobs hooks
export const useJobs = (params?: {
  page?: number;
  per_page?: number;
  filter?: Record<string, any>;
  sort?: string;
  include?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.jobs(params),
    queryFn: () => apiService.getJobs(params || {}),
    enabled: !!localStorage.getItem("auth_token"),
  });
};

export const useJob = (id: string, include?: string) => {
  return useQuery({
    queryKey: queryKeys.job(id, include),
    queryFn: () => apiService.getJob(id, include),
    enabled: !!id && !!localStorage.getItem("auth_token"),
  });
};

export const useCreateJob = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (jobData: any) => apiService.createJob(jobData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jobs"] });
      notifications.success.created();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

export const useUpdateJob = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      apiService.updateJob(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["jobs"] });
      queryClient.invalidateQueries({ queryKey: ["job", variables.id] });
      notifications.success.updated();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

export const useDeleteJob = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (id: string) => apiService.deleteJob(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jobs"] });
      notifications.success.deleted();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

export const useJobAnalytics = (id: string) => {
  return useQuery({
    queryKey: queryKeys.jobAnalytics(id),
    queryFn: () => apiService.getJobAnalytics(id),
    enabled: !!id && !!localStorage.getItem("auth_token"),
  });
};

export const useJobCandidates = (
  id: string,
  params?: {
    page?: number;
    per_page?: number;
    filter?: Record<string, any>;
    sort?: string;
    include?: string;
  },
) => {
  return useQuery({
    queryKey: queryKeys.jobCandidates(id, params),
    queryFn: () => apiService.getJobCandidates(id, params || {}),
    enabled: !!id && !!localStorage.getItem("auth_token"),
  });
};

export const useUpdateJobStatus = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({
      id,
      status,
      notes,
    }: {
      id: string;
      status: string;
      notes?: string;
    }) => apiService.updateJobStatus(id, status, notes),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["jobs"] });
      queryClient.invalidateQueries({ queryKey: ["job", variables.id] });
      notifications.success.updated();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

export const useBulkJobActions = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ action, jobIds }: { action: string; jobIds: string[] }) =>
      apiService.bulkJobActions(action, jobIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jobs"] });
      notifications.success.updated();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

// Interviewers hooks
export const useInterviewers = (params?: {
  department?: string;
  is_active?: boolean;
  expertise?: string;
  include_stats?: boolean;
  include_interviews?: boolean;
}) => {
  return useQuery({
    queryKey: queryKeys.interviewers(params),
    queryFn: () => apiService.getInterviewers(params || {}),
    enabled: !!localStorage.getItem("auth_token"),
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minute
  });
};

// Get active interviewers for dropdown (cached)
export const useActiveInterviewersDropdown = () => {
  return useQuery({
    queryKey: ["interviewers", "dropdown", "active"],
    queryFn: async () => {
      const response = await apiService.getInterviewers({
        is_active: true,
        include_stats: false,
      });

      if (response.status === "success" && response.data) {
        return response.data.map((interviewer: any) => ({
          value: interviewer.id,
          label: `${interviewer.name} (${interviewer.department})`,
          department: interviewer.department,
          expertise: interviewer.expertise || [],
          maxPerDay: interviewer.max_interviews_per_day,
          isActive: interviewer.is_active,
          avatar: interviewer.user?.avatar,
          initials:
            interviewer.user?.initials ||
            interviewer.name
              .split(" ")
              .map((n: string) => n[0])
              .join(""),
          upcomingInterviews: interviewer.statistics?.upcoming_interviews || 0,
        }));
      }
      return [];
    },
    enabled: !!localStorage.getItem("auth_token"),
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes - longer cache for dropdown
  });
};

// Get interviewers by department
export const useInterviewersByDepartment = (department: string) => {
  return useQuery({
    queryKey: queryKeys.interviewers({ department, is_active: true }),
    queryFn: () => apiService.getInterviewers({ department, is_active: true }),
    enabled: !!department && !!localStorage.getItem("auth_token"),
    refetchOnWindowFocus: false,
    staleTime: 120000, // 2 minutes
  });
};

export const useInterviewer = (
  id: string,
  params?: {
    include_interviews?: boolean;
    include_stats?: boolean;
  },
) => {
  return useQuery({
    queryKey: queryKeys.interviewer(id, params),
    queryFn: () => apiService.getInterviewer(id, params || {}),
    enabled: !!id && !!localStorage.getItem("auth_token"),
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minute
  });
};

export const useCreateInterviewer = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (interviewerData: any) =>
      apiService.createInterviewer(interviewerData),
    onSuccess: (data, variables) => {
      // Invalidate all interviewer-related queries
      queryClient.invalidateQueries({ queryKey: ["interviewers"] });
      queryClient.invalidateQueries({ queryKey: ["interviewer"] });
      notifications.success.created();
    },
    onError: (error: any) => {
      console.error("Create interviewer error:", error);
      if (
        error.validationErrors?.user_id?.some((msg: string) =>
          msg.includes("already has an interviewer profile"),
        )
      ) {
        notifications.error.custom("Người dùng này đã có hồ sơ phỏng vấn viên");
      } else {
        notifications.showApiError(error);
      }
    },
  });
};

export const useUpdateInterviewer = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      apiService.updateInterviewer(id, data),
    onSuccess: (_, variables) => {
      // Invalidate all interviewer-related queries
      queryClient.invalidateQueries({ queryKey: ["interviewers"] });
      queryClient.invalidateQueries({
        queryKey: ["interviewer", variables.id],
      });
      // Also invalidate interview queries as interviewer changes might affect scheduling
      queryClient.invalidateQueries({ queryKey: ["interviews"] });
      notifications.success.updated();
    },
    onError: (error: any) => {
      console.error("Update interviewer error:", error);
      if (error.message?.includes("scheduled interviews")) {
        notifications.error.custom(
          "Không thể cập nhật người phỏng vấn có lịch hẹn",
        );
      } else {
        notifications.showApiError(error);
      }
    },
  });
};

export const useDeleteInterviewer = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (id: string) => apiService.deleteInterviewer(id),
    onSuccess: () => {
      // Invalidate all interviewer-related queries
      queryClient.invalidateQueries({ queryKey: ["interviewers"] });
      queryClient.invalidateQueries({ queryKey: ["interviewer"] });
      // Also invalidate interview queries
      queryClient.invalidateQueries({ queryKey: ["interviews"] });
      notifications.success.deleted();
    },
    onError: (error: any) => {
      console.error("Delete interviewer error:", error);
      if (error.message?.includes("scheduled interviews")) {
        const match = error.message.match(/(\d+) scheduled interviews/);
        const count = match ? match[1] : "một số";
        notifications.error.custom(
          `Không thể xóa người phỏng vấn có ${count} lịch hẹn đã lên lịch`,
        );
      } else if (error.statusCode === 403) {
        notifications.error.custom(
          "Bạn không có quyền xóa người phỏng vấn này",
        );
      } else {
        notifications.showApiError(error);
      }
    },
  });
};

// Interview availability checking
export const useCheckInterviewerAvailability = () => {
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (params: {
      interviewer_id: string;
      date: string;
      time: string;
      duration: number;
    }) => apiService.checkInterviewerAvailability(params),
    onError: (error) => {
      notifications.showApiError(error);
    },
  });
};

// Interviews hooks
export const useInterviews = (params?: {
  page?: number;
  per_page?: number;
  filter?: Record<string, any>;
  sort?: string;
  status?: string;
  type?: string;
  candidate_id?: number;
  interviewer_id?: number;
  date_from?: string;
  date_to?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.interviews(params),
    queryFn: () => apiService.getInterviews(params || {}),
    enabled: !!localStorage.getItem("auth_token"),
    refetchOnWindowFocus: false,
    staleTime: 30000, // 30 seconds
  });
};

export const useScheduleInterview = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (interviewData: any) =>
      apiService.scheduleInterview(interviewData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["interviews"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard"] });
      notifications.info.scheduled();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

export const useInterviewerAvailability = (params: {
  interviewer_id: string;
  date: string;
  time: string;
  duration: number;
}) => {
  return useQuery({
    queryKey: queryKeys.interviewerAvailability(params),
    queryFn: () => apiService.checkInterviewerAvailability(params),
    enabled: !!(
      params.interviewer_id &&
      params.date &&
      params.time &&
      localStorage.getItem("auth_token")
    ),
  });
};

export const useSubmitInterviewFeedback = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (feedbackData: any) =>
      apiService.submitInterviewFeedback(feedbackData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["interview-feedback"] });
      queryClient.invalidateQueries({ queryKey: ["interviews"] });
      // Invalidate specific interview queries
      if (variables.interview_id) {
        queryClient.invalidateQueries({
          queryKey: ["interview", variables.interview_id.toString()],
        });
      }
      notifications.success.created();
    },
    onError: (error: any) => {
      console.error("Submit interview feedback error:", error);
      notifications.error.failed();
    },
  });
};

export const useInterview = (id: string, include?: string) => {
  return useQuery({
    queryKey: queryKeys.interview(id, include),
    queryFn: () => apiService.getInterview(id, include),
    enabled: !!id && !!localStorage.getItem("auth_token"),
  });
};

export const useUpdateInterview = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      apiService.updateInterview(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["interviews"] });
      queryClient.invalidateQueries({ queryKey: ["interview", variables.id] });
      notifications.success.updated();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

export const useDeleteInterview = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (id: string) => apiService.deleteInterview(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["interviews"] });
      notifications.success.deleted();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

export const useUpdateInterviewStatus = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({
      id,
      status,
      notes,
    }: {
      id: string;
      status: string;
      notes?: string;
    }) => apiService.updateInterviewStatus(id, status, notes),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["interviews"] });
      queryClient.invalidateQueries({ queryKey: ["interview", variables.id] });
      notifications.success.updated();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

export const useSendInterviewReminders = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (params: { interview_ids: string[]; hours_before: number }) =>
      apiService.sendInterviewReminders(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["interviews"] });
      notifications.success.created();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

export const useCalendarEvents = (params: {
  start_date: string;
  end_date: string;
  interviewer_id?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.calendarEvents(params),
    queryFn: () => apiService.getCalendarEvents(params),
    enabled: !!(
      params.start_date &&
      params.end_date &&
      localStorage.getItem("auth_token")
    ),
  });
};

export const useInterviewFeedback = (params?: {
  page?: number;
  per_page?: number;
  filter?: Record<string, any>;
  sort?: string;
  include?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.interviewFeedback(params),
    queryFn: () => apiService.getInterviewFeedback(params || {}),
    enabled: !!localStorage.getItem("auth_token"),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// New hook for feedback by interview
export const useInterviewFeedbackByInterview = (
  interviewId: number,
  include?: string,
) => {
  return useQuery({
    queryKey: ["interview-feedback", "by-interview", interviewId, include],
    queryFn: () => apiService.getFeedbackByInterview(interviewId, include),
    enabled: !!interviewId && !!localStorage.getItem("auth_token"),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// New hook for checking feedback existence
export const useFeedbackExists = (
  interviewId: number,
  interviewerId: number,
) => {
  return useQuery({
    queryKey: ["feedback-exists", interviewId, interviewerId],
    queryFn: async () => {
      const response = await apiService.getInterviewFeedback({
        filter: { interview_id: interviewId, interviewer_id: interviewerId },
        per_page: 1,
      });
      return response.data && response.data.length > 0;
    },
    enabled:
      !!interviewId && !!interviewerId && !!localStorage.getItem("auth_token"),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useInterviewFeedbackDetails = (id: string, include?: string) => {
  return useQuery({
    queryKey: queryKeys.interviewFeedbackDetails(id, include),
    queryFn: () => apiService.getInterviewFeedbackDetails(id, include),
    enabled: !!id && !!localStorage.getItem("auth_token"),
  });
};

export const useUpdateInterviewFeedback = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      apiService.updateInterviewFeedback(id, data),
    onSuccess: (responseData, variables) => {
      queryClient.invalidateQueries({ queryKey: ["interview-feedback"] });
      queryClient.invalidateQueries({
        queryKey: ["interview-feedback", variables.id],
      });
      queryClient.invalidateQueries({ queryKey: ["interviews"] });

      // Invalidate specific interview if we have the data
      if (responseData?.data?.interview_id) {
        queryClient.invalidateQueries({
          queryKey: ["interview", responseData.data.interview_id.toString()],
        });
      }

      notifications.success.updated();
    },
    onError: (error: any) => {
      console.error("Update interview feedback error:", error);
      notifications.error.failed();
    },
  });
};

// New hook for patch updates
export const usePatchInterviewFeedback = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      apiService.patchInterviewFeedback(id, data),
    onSuccess: (responseData, variables) => {
      queryClient.invalidateQueries({ queryKey: ["interview-feedback"] });
      queryClient.invalidateQueries({
        queryKey: ["interview-feedback", variables.id],
      });
      notifications.success.updated();
    },
    onError: (error: any) => {
      console.error("Patch interview feedback error:", error);
      notifications.error.failed();
    },
  });
};

export const useDeleteInterviewFeedback = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (id: string) => apiService.deleteInterviewFeedback(id),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["interview-feedback"] });
      queryClient.invalidateQueries({
        queryKey: ["interview-feedback", variables],
      });
      queryClient.invalidateQueries({ queryKey: ["interviews"] });
      notifications.success.deleted();
    },
    onError: (error: any) => {
      console.error("Delete interview feedback error:", error);
      notifications.error.failed();
    },
  });
};

export const useUpcomingInterviews = (params?: {
  limit?: number;
  date_from?: string;
}) => {
  const today = new Date().toISOString().split("T")[0];
  return useQuery({
    queryKey: ["upcoming-interviews", params],
    queryFn: () =>
      apiService.getInterviews({
        per_page: params?.limit || 10,
        filter: {
          date_from: params?.date_from || today,
          status: "scheduled",
        },
        sort: "date,time",
      }),
    enabled: !!localStorage.getItem("auth_token"),
  });
};

// Dashboard hooks
export const useDashboardOverview = (dateFrom?: string, dateTo?: string) => {
  return useQuery({
    queryKey: queryKeys.dashboard(dateFrom, dateTo),
    queryFn: () => apiService.getDashboardOverview(dateFrom, dateTo),
    enabled: !!localStorage.getItem("auth_token"),
    refetchInterval: 60000, // Refresh every minute
  });
};

export const useRecruitmentPipeline = () => {
  return useQuery({
    queryKey: ["recruitment-pipeline"],
    queryFn: () => apiService.getRecruitmentPipeline(),
    enabled: !!localStorage.getItem("auth_token"),
  });
};

export const useSourceEffectiveness = () => {
  return useQuery({
    queryKey: ["source-effectiveness"],
    queryFn: () => apiService.getSourceEffectiveness(),
    enabled: !!localStorage.getItem("auth_token"),
  });
};

export const useTeamPerformance = () => {
  return useQuery({
    queryKey: ["team-performance"],
    queryFn: () => apiService.getTeamPerformance(),
    enabled: !!localStorage.getItem("auth_token"),
  });
};

// Hiring Managers and Recruiters hooks
export const useHiringManagers = () => {
  return useQuery({
    queryKey: ["hiring-managers"],
    queryFn: () => apiService.getHiringManagers(),
    enabled: !!localStorage.getItem("auth_token"),
    staleTime: 300000, // 5 minutes
  });
};

export const useRecruiters = () => {
  return useQuery({
    queryKey: ["recruiters"],
    queryFn: () => apiService.getRecruiters(),
    enabled: !!localStorage.getItem("auth_token"),
    staleTime: 300000, // 5 minutes
  });
};

// Combined hook for interviewer user selection - using proper user management API
export const useInterviewerUsers = () => {
  const { data: usersResponse, isLoading, error } = useQuery({
    queryKey: ["active-users-for-interviewers"],
    queryFn: () => apiService.getUsers({
      filter: {
        is_active: true // Only get active users
      },
      per_page: 100, // Get more users to avoid pagination issues
      include: "roles" // Include role information
    }),
    enabled: !!localStorage.getItem("auth_token"),
    retry: false,
  });

  const users = useMemo(() => {
    if (!usersResponse?.data) return [];

    return usersResponse.data.map((user: any) => ({
      ...user,
      role: user.role || user.role_display_name || "User",
      initials: user.initials || user.name
        ?.split(" ")
        .map((n: string) => n[0])
        .join("") || "U",
    }));
  }, [usersResponse]);

  return {
    data: users,
    isLoading,
    error,
  };
};

export const useAnalytics = (params?: {
  period?: string;
  date_from?: string;
  date_to?: string;
}) => {
  return useQuery({
    queryKey: ["analytics", params],
    queryFn: () =>
      apiService.getDashboardOverview(params?.date_from, params?.date_to),
    enabled: !!localStorage.getItem("auth_token"),
  });
};

// File upload hooks
export const useUploadFile = () => {
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (params: {
      file: File;
      type: "contract" | "certificate" | "portfolio" | "other";
      entity_type: "candidate" | "job" | "interview" | "user";
      entity_id: number;
      description?: string;
    }) => apiService.uploadFile(params),
    onSuccess: () => {
      notifications.success.uploaded();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};

// Export hooks
export const useExportAnalytics = () => {
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (params: {
      type: "pipeline" | "sources" | "team" | "comprehensive";
      format: "pdf" | "excel" | "csv";
      date_from?: string;
      date_to?: string;
    }) => apiService.exportAnalytics(params),
    onSuccess: () => {
      notifications.success.exported();
    },
    onError: () => {
      notifications.error.failed();
    },
  });
};
