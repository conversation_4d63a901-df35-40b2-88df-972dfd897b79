import { useEffect } from "react";
import { useTranslation } from "@/lib/i18n";

interface PageTitleOptions {
  /**
   * The main page title key from translations
   */
  titleKey: string;
  /**
   * Optional context variables for interpolation (e.g., {name}, {title})
   */
  context?: Record<string, string>;
  /**
   * Optional additional context to append (will be appended before HireFlow ATS)
   */
  additionalContext?: string;
  /**
   * Whether to include the app name suffix (default: true)
   */
  includeAppName?: boolean;
}

/**
 * Hook to manage dynamic page titles with i18n support
 *
 * @example
 * // Simple usage
 * usePageTitle({ titleKey: "pageTitle.candidates.list" });
 *
 * @example
 * // With context variables
 * usePageTitle({
 *   titleKey: "pageTitle.candidates.detail",
 *   context: { name: "John Doe" }
 * });
 *
 * @example
 * // With additional context
 * usePageTitle({
 *   titleKey: "pageTitle.jobs.detail",
 *   context: { title: "Frontend Developer" },
 *   additionalContext: "Active Position"
 * });
 */
export const usePageTitle = (options: PageTitleOptions) => {
  const { t } = useTranslation();
  const {
    titleKey,
    context = {},
    additionalContext,
    includeAppName = true,
  } = options;

  useEffect(() => {
    try {
      // Get the translation using the titleKey
      //let title = getNestedTranslation(t, titleKey) || titleKey;
      let title = getNestedTranslation(t, titleKey) || titleKey;

      //console.error(`Translation not found for key: ${titleKey}`);

      // Replace context variables if provided
      if (context && Object.keys(context).length > 0) {
        Object.entries(context).forEach(([key, value]) => {
          const placeholder = `{${key}}`;
          title = title.replace(new RegExp(placeholder, "g"), value || "");
        });
      }

      // Build the complete title
      const titleParts: string[] = [];

      if (title && title !== titleKey) {
        titleParts.push(title);
      }

      if (additionalContext) {
        titleParts.push(additionalContext);
      }

      if (includeAppName) {
        titleParts.push("HireFlow ATS");
      }

      const finalTitle = titleParts.join(" - ");

      // Update the document title
      document.title = finalTitle;

      // Also update meta description for SEO
      updateMetaDescription(title, context);
    } catch (error) {
      console.warn("Error setting page title:", error);
      // Fallback to a basic title
      document.title = includeAppName ? "HireFlow ATS" : "Application";
    }
  }, [titleKey, context, additionalContext, includeAppName, t]);
};

/**
 * Helper function to get nested translation values
 */
function getNestedTranslation(
  translations: any,
  path: string,
): string | undefined {
  const keys = path.split(".");
  let current = translations;

  for (const key of keys) {
    if (current && typeof current === "object" && key in current) {
      current = current[key];
    } else {
      return undefined;
    }
  }

  return typeof current === "string" ? current : undefined;
}

/**
 * Helper function to update meta description based on page content
 */
function updateMetaDescription(title: string, context: Record<string, string>) {
  const metaDescription = document.querySelector('meta[name="description"]');

  if (metaDescription) {
    let description = `${title} - HireFlow ATS: Comprehensive applicant tracking system`;

    // Add context-specific descriptions
    if (context.name) {
      description += ` for ${context.name}`;
    } else if (context.title) {
      description += ` for ${context.title}`;
    }

    metaDescription.setAttribute("content", description);
  }
}

/**
 * Simplified hook for basic page titles without context
 */
export const useSimplePageTitle = (
  titleKey: string,
  additionalContext?: string,
) => {
  usePageTitle({ titleKey, additionalContext });
};

/**
 * Hook for page titles with single context variable (common use case)
 */
export const useContextPageTitle = (
  titleKey: string,
  contextKey: string,
  contextValue: string,
  additionalContext?: string,
) => {
  usePageTitle({
    titleKey,
    context: { [contextKey]: contextValue },
    additionalContext,
  });
};
