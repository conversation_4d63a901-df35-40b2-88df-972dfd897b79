import { useToast } from "./use-toast";
import { useTranslation } from "@/lib/i18n";
import { ValidationError, ApiError } from "@/lib/api";

export const useNotifications = () => {
  const { toast } = useToast();

  // Simple fallback functions
  const showSuccess = (message: string) => {
    toast({
      title: message,
      variant: "success",
    });
  };

  const showError = (message: string, description?: string) => {
    toast({
      title: message,
      description: description,
      variant: "destructive",
    });
  };

  const showValidationError = (error: ValidationError) => {
    const formattedErrors = error.getFormattedErrors();
    toast({
      title: error.message,
      description: formattedErrors,
      variant: "destructive",
    });
  };

  const showApiError = (error: ApiError | ValidationError | Error) => {
    if (error instanceof ValidationError) {
      showValidationError(error);
    } else if (error instanceof ApiError) {
      showError(error.message, `Status: ${error.statusCode}`);
    } else {
      // Handle generic Error or string messages
      const message = error instanceof Error ? error.message : String(error);
      // Check if it's a stringified object
      if (message === "[object Object]") {
        showError("An error occurred", "Please try again");
      } else {
        showError(message);
      }
    }
  };

  const showWarning = (message: string) => {
    toast({
      title: message,
      variant: "warning",
    });
  };

  const showInfo = (message: string) => {
    toast({
      title: message,
      variant: "info",
    });
  };

  // Convenience methods for common actions
  const notifySuccess = {
    saved: () => showSuccess("Saved successfully"),
    deleted: () => showSuccess("Deleted successfully"),
    updated: () => showSuccess("Cập nhật thành công"),
    created: () => showSuccess("Created successfully"),
    sent: () => showSuccess("Sent successfully"),
    imported: () => showSuccess("Imported successfully"),
    exported: () => showSuccess("Exported successfully"),
    uploaded: () => showSuccess("Uploaded successfully"),
    downloaded: () => showSuccess("Downloaded successfully"),
  };

  const notifyError = {
    failed: (error?: any) =>
      error ? showApiError(error) : showError("Operation failed"),
    notFound: () => showError("Not found"),
    unauthorized: () => showError("Unauthorized"),
    forbidden: () => showError("Forbidden"),
    validation: (error?: ValidationError) =>
      error ? showValidationError(error) : showError("Validation error"),
    network: () => showError("Network error"),
    server: () => showError("Server error"),
    timeout: () => showError("Request timeout"),
    custom: (message: string) => showError(message),
  };

  const notifyWarning = {
    unsaved: () => showWarning("Unsaved changes"),
    duplicate: () => showWarning("Duplicate entry"),
    limit: () => showWarning("Limit reached"),
    expires: () => showWarning("Will expire soon"),
  };

  const notifyInfo = {
    loading: () => showInfo("Loading..."),
    processing: () => showInfo("Processing..."),
    syncing: () => showInfo("Syncing..."),
    scheduled: () => showInfo("Scheduled successfully"),
  };

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showValidationError,
    showApiError,
    success: notifySuccess,
    error: notifyError,
    warning: notifyWarning,
    info: notifyInfo,
  };
};
