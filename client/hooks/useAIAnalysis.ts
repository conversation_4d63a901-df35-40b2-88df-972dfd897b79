import { useState, useEffect, useCallback } from "react";
import { CandidateAnalysisService } from "@/lib/services/candidateAnalysisService";
import { CandidateAnalysisData } from "@/lib/types/candidateAnalysis";

interface UseAIAnalysisOptions {
  candidateId: number | string | undefined;
  jobPostingId?: number;
  autoLoad?: boolean;
  pollInterval?: number; // Poll every N milliseconds when processing
}

interface FormattedAnalysis {
  id: number;
  candidateId: number;
  jobPostingId?: number;
  analysisType: "ai_analysis" | "job_matching";
  status: "pending" | "processing" | "completed" | "failed";
  summary: string;
  strengths: string[];
  weaknesses: string[];
  improvementAreas: string[];
  recommendations: string[];
  scores: {
    overall: number;
    skills: number;
    experience: number;
    education: number;
    cultural_fit: number;
    average: number;
  };
  jobMatching?: {
    matchPercentage: number;
    keyAlignments: string[];
    missingRequirements: string[];
    matchingCriteria: string[];
  };
  createdAt: string;
  processing?: {
    started_at: string;
    completed_at?: string;
    duration_seconds?: number;
  };
}

export const useAIAnalysis = ({
  candidateId,
  jobPostingId,
  autoLoad = true,
  pollInterval = 5000, // 5 seconds
}: UseAIAnalysisOptions) => {
  const [analysis, setAnalysis] = useState<FormattedAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Convert candidateId to number
  const numericCandidateId = typeof candidateId === "string" 
    ? parseInt(candidateId) 
    : candidateId;

  // Load existing analysis
  const loadAnalysis = useCallback(async () => {
    if (!numericCandidateId || isNaN(numericCandidateId)) return;

    setIsLoading(true);
    setError(null);

    try {
      const existingAnalysis = await CandidateAnalysisService.getExistingAnalysis(
        numericCandidateId,
        jobPostingId
      );
      
      if (existingAnalysis) {
        const formattedAnalysis = CandidateAnalysisService.formatAnalysisData(existingAnalysis);
        setAnalysis(formattedAnalysis);
      } else {
        setAnalysis(null);
      }
    } catch (error: any) {
      console.warn("Failed to load existing analysis:", error.message);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [numericCandidateId, jobPostingId]);

  // Generate new analysis
  const generateAnalysis = useCallback(async () => {
    if (!numericCandidateId || isNaN(numericCandidateId)) {
      setError("Invalid candidate ID");
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await CandidateAnalysisService.generateAnalysis(
        numericCandidateId,
        jobPostingId
      );

      const formattedAnalysis = CandidateAnalysisService.formatAnalysisData(response.data);
      setAnalysis(formattedAnalysis);
      return response;
    } catch (error: any) {
      const errorMessage = error.message || "Failed to generate AI analysis";
      setError(errorMessage);
      throw error;
    } finally {
      setIsGenerating(false);
    }
  }, [numericCandidateId, jobPostingId]);

  // Retry analysis (clear error and regenerate)
  const retryAnalysis = useCallback(() => {
    setError(null);
    return generateAnalysis();
  }, [generateAnalysis]);

  // Refresh analysis data
  const refreshAnalysis = useCallback(() => {
    return loadAnalysis();
  }, [loadAnalysis]);

  // Check if analysis needs generation
  const needsGeneration = !analysis && !isLoading && !error;

  // Check if analysis is in progress
  const isProcessing = analysis?.status === "pending" || analysis?.status === "processing";

  // Auto-load on mount
  useEffect(() => {
    if (autoLoad && numericCandidateId) {
      loadAnalysis();
    }
  }, [autoLoad, numericCandidateId, loadAnalysis]);

  // Polling for processing status
  useEffect(() => {
    if (!isProcessing || !analysis?.id) return;

    const pollStatus = async () => {
      try {
        const updatedAnalysis = await CandidateAnalysisService.checkAnalysisStatus(analysis.id);
        const formattedAnalysis = CandidateAnalysisService.formatAnalysisData(updatedAnalysis);
        setAnalysis(formattedAnalysis);
      } catch (error: any) {
        console.warn("Failed to poll analysis status:", error.message);
      }
    };

    const interval = setInterval(pollStatus, pollInterval);
    return () => clearInterval(interval);
  }, [isProcessing, analysis?.id, pollInterval]);

  return {
    analysis,
    isLoading,
    isGenerating,
    isProcessing,
    needsGeneration,
    error,
    loadAnalysis,
    generateAnalysis,
    retryAnalysis,
    refreshAnalysis,
  };
};
