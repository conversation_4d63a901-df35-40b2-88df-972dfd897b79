import { useEffect, useState } from "react";
import { apiService } from "@/lib/api";
import { useAuth } from "@/lib/auth";

interface ApiUser {
  id: number;
  name: string;
  email: string;
  role: string;
  department: string;
  permissions: string[];
  is_interviewer: boolean;
}

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  avatar?: string;
  initials: string;
  title: string;
  department: string;
  role: string;
  permissions: string[];
  phone?: string;
  location?: string;
  timezone?: string;
  language?: string;
  isActive: boolean;
  lastLogin?: string;
  joinedDate?: string;
  bio?: string;
  skills: string[];
  isInterviewer: boolean;
  preferences: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    weeklyReports: boolean;
    interviewReminders: boolean;
    candidateUpdates: boolean;
    systemAlerts: boolean;
    theme: "light" | "dark" | "system";
    compactView: boolean;
  };
  socialLinks: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
}

const transformApiUserToProfile = (apiUser: ApiUser): UserProfile => {
  const nameParts = apiUser.name.split(" ");
  const firstName = nameParts[0] || "";
  const lastName = nameParts.slice(1).join(" ") || "";
  const initials = nameParts
    .map((part) => part[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);

  return {
    id: apiUser.id.toString(),
    email: apiUser.email,
    firstName,
    lastName,
    fullName: apiUser.name,
    avatar: undefined, // Not provided by API
    initials,
    title: "Staff", // Default title as not provided by API
    department: apiUser.department,
    role: apiUser.role,
    permissions: apiUser.permissions || [],
    phone: undefined, // Not provided by API
    location: "Vietnam", // Default location as not provided by API
    timezone: "Asia/Ho_Chi_Minh", // Default timezone
    language: "Vietnamese", // Default language
    isActive: true, // Assume active if user can access
    lastLogin: undefined, // Not provided by API
    joinedDate: undefined, // Not provided by API
    bio: undefined, // Not provided by API
    skills: [], // Default empty skills
    isInterviewer: apiUser.is_interviewer || false,
    preferences: {
      emailNotifications: true,
      pushNotifications: true,
      weeklyReports: true,
      interviewReminders: true,
      candidateUpdates: true,
      systemAlerts: false,
      theme: "system",
      compactView: false,
    },
    socialLinks: {
      linkedin: undefined,
      twitter: undefined,
      github: undefined,
    },
  };
};

export const useCurrentUser = () => {
  const { user: authUser, isAuthenticated } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProfile = async () => {
    if (!isAuthenticated || !authUser) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getCurrentUser();

      if (response.status === "success" && response.data.user) {
        const transformedProfile = transformApiUserToProfile(
          response.data.user,
        );
        setProfile(transformedProfile);
      } else {
        setError("Failed to load profile data");
      }
    } catch (err: any) {
      console.error("Error fetching profile:", err);
      setError(err.message || "Failed to load profile");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProfile();
  }, [isAuthenticated, authUser]);

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!profile) return;

    try {
      setError(null);
      // Here you would call an API to update the user profile
      // For now, we'll just update the local state
      setProfile((prev) => (prev ? { ...prev, ...updates } : null));
      return true;
    } catch (err: any) {
      setError(err.message || "Failed to update profile");
      return false;
    }
  };

  return {
    profile,
    isLoading,
    error,
    refetch: fetchProfile,
    updateProfile,
  };
};
