# HireFlow ATS - Frontend

A modern, domain-driven React application for the HireFlow Applicant Tracking System.

## 🏗️ Architecture

This application follows a **Domain-Driven Design (DDD)** architecture with clear separation of concerns:

```
client/
├── app/                    # Next.js App Router
├── core/                   # Core infrastructure
│   ├── api/               # API client and base services
│   ├── auth/              # Authentication logic
│   ├── routing/           # Routing utilities
│   └── providers/         # Core providers
├── domains/               # Domain-specific modules
│   ├── candidates/        # Candidate management
│   ├── jobs/             # Job management
│   ├── interviews/       # Interview scheduling
│   ├── calendar/         # Calendar integration
│   └── analytics/        # Analytics and reporting
├── shared/               # Shared components and utilities
│   ├── components/       # Reusable UI components
│   └── utils/           # Shared utilities
└── tools/               # Migration and development tools
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- TypeScript knowledge

### Installation

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Start development server
npm run dev
```

### Environment Variables

```env
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_APP_ENV=development
```

## 📁 Domain Structure

Each domain follows a consistent structure:

```
domains/[domain]/
├── components/           # Domain-specific components
├── hooks/               # Domain-specific hooks
├── services/            # API and business services
├── types/               # TypeScript interfaces
├── utils/               # Domain utilities
└── index.ts            # Barrel exports
```

### Domain Examples

#### Candidates Domain
- **Components**: `CandidateCard`, `CandidateTable`, `CandidateForm`
- **Hooks**: `useCandidates`, `useCandidateForm`, `useCandidateTable`
- **Services**: `CandidateApiService`, `CandidateBusinessService`
- **Types**: `Candidate`, `CandidateStatus`, `CreateCandidateData`

#### Jobs Domain
- **Components**: `JobCard`, `JobTable`, `JobForm`
- **Hooks**: `useJobs`, `useJobForm`, `useJobTable`
- **Services**: `JobApiService`, `JobBusinessService`
- **Types**: `Job`, `JobStatus`, `CreateJobData`

## 🧩 Shared Components

### Layout Components
- `Page`, `PageHeader`, `PageContent` - Page structure
- `ContentSection` - Content organization
- `GridLayout`, `FlexLayout` - Layout primitives
- `CardLayout` - Card-based layouts

### Form Components
- `FormBuilder` - Dynamic form generation
- `DynamicInput` - Smart input components
- Field types: text, email, select, multiselect, tags, etc.

### Data Display
- `DataTable` - Advanced table with sorting, filtering, pagination
- `DataTableColumns` - Column helper utilities
- Status badges, avatars, and formatting helpers

### Feedback Components
- `ErrorBoundary` - Error handling at different levels
- `LoadingSpinner`, `LoadingCard` - Loading states
- `NotificationSystem` - Toast notifications with patterns

### Modal System
- `Modal` - Base modal with compound components
- `ConfirmModal` - Confirmation dialogs
- `FormModal` - Form-specific modals

## 🔧 Core Infrastructure

### API Layer
- `ApiClient` - HTTP client with error handling, retries, interceptors
- `BaseService` - CRUD operations base class
- Type-safe request/response handling
- Automatic error reporting

### State Management
- React Query for server state
- Custom hooks for domain logic
- Optimistic updates and caching

### Validation
- Zod schemas for runtime validation
- Form validation with react-hook-form
- Reusable validation patterns

## 🧪 Testing

### Test Structure
```
__tests__/
├── setup.ts              # Test configuration
├── mocks/                # MSW API mocks
├── utils/                # Test utilities
└── domains/              # Domain-specific tests
```

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Testing Patterns
- Component testing with React Testing Library
- Hook testing with custom utilities
- API mocking with MSW
- Integration testing for user flows

## 📦 Build and Deployment

### Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript checking
```

### Production Build
```bash
npm run build
npm run start
```

## 🔄 Migration Guide

### From Legacy Structure

Use the provided migration tools:

```bash
# Migrate a component
npm run migrate:component -- --component=CandidateList --domain=candidates

# Update imports
npm run migrate:imports

# Validate migration
npm run migrate:validate
```

### Migration Steps
1. **Identify Domain** - Determine which domain the component belongs to
2. **Move Files** - Use migration scripts to move files to correct locations
3. **Update Imports** - Fix import paths using automated tools
4. **Update Tests** - Move and update test files
5. **Validate** - Run validation checks

## 🎯 Best Practices

### Component Development
- Use compound components for complex UI
- Implement proper TypeScript interfaces
- Follow accessibility guidelines
- Write comprehensive tests

### State Management
- Use React Query for server state
- Keep local state minimal
- Implement optimistic updates
- Handle loading and error states

### API Integration
- Use domain-specific services
- Implement proper error handling
- Add request/response validation
- Use consistent error patterns

### Performance
- Implement code splitting by domain
- Use React.memo for expensive components
- Optimize bundle size with tree shaking
- Implement proper caching strategies

## 🔍 Troubleshooting

### Common Issues

**Import Errors**
```bash
# Fix import paths
npm run migrate:imports
```

**Type Errors**
```bash
# Check TypeScript
npm run type-check
```

**Test Failures**
```bash
# Clear test cache
npm run test -- --clearCache
```

### Development Tools
- React Developer Tools
- React Query Devtools
- TypeScript Language Server
- ESLint and Prettier

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [React Query Documentation](https://tanstack.com/query)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [Testing Library Documentation](https://testing-library.com/docs)

## 🤝 Contributing

1. Follow the domain-driven architecture
2. Write tests for new features
3. Update documentation
4. Follow TypeScript best practices
5. Use conventional commit messages

## 📄 License

This project is proprietary software. All rights reserved.
