/**
 * Root Layout
 * Main layout component with providers and global styles
 */

import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { AppProviders } from './providers/AppProviders';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'HireFlow - ATS System',
  description: 'Modern Applicant Tracking System for efficient hiring',
  keywords: ['ATS', 'hiring', 'recruitment', 'applicant tracking'],
  authors: [{ name: 'HireFlow Team' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <AppProviders>
          {children}
        </AppProviders>
      </body>
    </html>
  );
}
