/**
 * Dashboard Layout
 * Layout for authenticated dashboard pages
 */

import React from 'react';
import { DomainErrorBoundary } from '@/shared/components/feedback';
import { Page, PageContent } from '@/shared/components/layout';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <DomainErrorBoundary domain="dashboard">
      <Page className="min-h-screen bg-gray-50">
        <div className="flex h-screen">
          {/* Sidebar would go here */}
          <aside className="w-64 bg-white border-r border-gray-200">
            <div className="p-6">
              <h1 className="text-xl font-bold text-gray-900">HireFlow</h1>
            </div>
            
            <nav className="mt-6">
              <div className="px-3">
                <div className="space-y-1">
                  <a
                    href="/dashboard"
                    className="bg-gray-100 text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                  >
                    Dashboard
                  </a>
                  <a
                    href="/dashboard/candidates"
                    className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                  >
                    Candidates
                  </a>
                  <a
                    href="/dashboard/jobs"
                    className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                  >
                    Jobs
                  </a>
                  <a
                    href="/dashboard/interviews"
                    className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                  >
                    Interviews
                  </a>
                  <a
                    href="/dashboard/analytics"
                    className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                  >
                    Analytics
                  </a>
                </div>
              </div>
            </nav>
          </aside>
          
          {/* Main content */}
          <main className="flex-1 overflow-auto">
            <PageContent>
              {children}
            </PageContent>
          </main>
        </div>
      </Page>
    </DomainErrorBoundary>
  );
}
