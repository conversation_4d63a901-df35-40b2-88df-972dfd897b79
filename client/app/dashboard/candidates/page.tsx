/**
 * Candidates Page
 * Main candidates listing and management page
 */

'use client';

import React from 'react';
import { DomainErrorBoundary } from '@/shared/components/feedback';
import { PageHeader, ContentSection } from '@/shared/components/layout';
import { Button } from '@/components/ui/button';
import { Plus, Filter, Download } from 'lucide-react';
import { CandidateTable } from '@/domains/candidates/components';
import { useCandidateTable } from '@/domains/candidates/hooks';

export default function CandidatesPage() {
  const {
    candidates,
    isLoading,
    filters,
    updateFilter,
    clearFilters,
    hasActiveFilters,
    selectedIds,
    selectedCount,
    toggleSelection,
    selectAll,
    clearSelection,
    refetch,
  } = useCandidateTable();

  const handleCreateCandidate = () => {
    // Open create candidate modal
    console.log('Create candidate');
  };

  const handleExport = () => {
    // Export candidates
    console.log('Export candidates');
  };

  const handleBulkAction = (action: string) => {
    // Handle bulk actions
    console.log('Bulk action:', action, selectedIds);
  };

  return (
    <DomainErrorBoundary domain="candidates">
      <div className="space-y-6">
        {/* Page Header */}
        <PageHeader
          title="Candidates"
          description="Manage and track all your candidates in one place"
          badge={{
            text: `${candidates.length} candidates`,
            variant: 'secondary',
          }}
          actions={
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button onClick={handleCreateCandidate}>
                <Plus className="h-4 w-4 mr-2" />
                Add Candidate
              </Button>
            </div>
          }
        />

        {/* Filters Section */}
        <ContentSection
          title="Filters"
          actions={
            hasActiveFilters && (
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Clear Filters
              </Button>
            )
          }
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={filters.status?.[0] || ''}
                onChange={(e) => updateFilter('status', e.target.value ? [e.target.value] : undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="new">New</option>
                <option value="screening">Screening</option>
                <option value="interview">Interview</option>
                <option value="offer">Offer</option>
                <option value="hired">Hired</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>

            {/* Location Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location
              </label>
              <input
                type="text"
                value={filters.location || ''}
                onChange={(e) => updateFilter('location', e.target.value || undefined)}
                placeholder="Enter location"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Experience Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Min Experience (years)
              </label>
              <input
                type="number"
                value={filters.experienceRange?.[0] || ''}
                onChange={(e) => {
                  const min = e.target.value ? parseInt(e.target.value) : undefined;
                  const max = filters.experienceRange?.[1];
                  updateFilter('experienceRange', min !== undefined ? [min, max || 50] : undefined);
                }}
                placeholder="0"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <input
                type="text"
                value={filters.search || ''}
                onChange={(e) => updateFilter('search', e.target.value || undefined)}
                placeholder="Search candidates..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </ContentSection>

        {/* Bulk Actions */}
        {selectedCount > 0 && (
          <ContentSection>
            <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-blue-900">
                  {selectedCount} candidate{selectedCount > 1 ? 's' : ''} selected
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSelection}
                  className="text-blue-700 hover:text-blue-900"
                >
                  Clear Selection
                </Button>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('update-status')}
                >
                  Update Status
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('add-tags')}
                >
                  Add Tags
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleBulkAction('delete')}
                >
                  Delete
                </Button>
              </div>
            </div>
          </ContentSection>
        )}

        {/* Candidates Table */}
        <ContentSection>
          <CandidateTable
            candidates={candidates}
            loading={isLoading}
            selectedIds={selectedIds}
            onSelectionChange={(ids) => {
              clearSelection();
              ids.forEach(id => toggleSelection(id));
            }}
            enableSelection={true}
            enableActions={true}
            onView={(candidate) => {
              window.location.href = `/dashboard/candidates/${candidate.id}`;
            }}
            onEdit={(candidate) => {
              console.log('Edit candidate:', candidate);
            }}
            onDelete={(candidate) => {
              console.log('Delete candidate:', candidate);
            }}
          />
        </ContentSection>
      </div>
    </DomainErrorBoundary>
  );
}
