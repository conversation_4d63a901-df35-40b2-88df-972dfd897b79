# MessageForm API Integration Update

## Overview

The MessageForm.tsx component has been updated to replace mock candidates data with real API integration. This provides users with actual candidate data from the database instead of hardcoded mock data.

## Key Changes

### 1. API Integration

- **Replaced Mock Data**: Removed `mockCandidates` array and replaced with real API calls
- **Added API Service**: Integrated `apiService.getCandidates()` to fetch real candidate data
- **Added Loading States**: Implemented loading, error, and success states for API calls

### 2. Enhanced Candidate Interface

```typescript
interface Candidate {
  id: string;
  name: string;
  email: string;
  phone?: string;
  initials: string;
  position?: string;
  avatar?: string; // NEW: Avatar URL from API
  location?: string; // NEW: Candidate location
  status?: string; // NEW: Candidate status
}
```

### 3. Search and Filter Functionality

- **Real-time Search**: Added search functionality in candidate dropdown
- **Multiple Search Fields**: Search by name, email, or position
- **Filtered Results**: Dynamic filtering based on search term
- **Search State Management**: Proper state handling for search terms

### 4. API Error Handling

- **Graceful Degradation**: Falls back to minimal demo data on API errors
- **User Feedback**: Clear error messages and toast notifications
- **Retry Capability**: Error states encourage users to try again

### 5. Enhanced UX Improvements

#### Candidate Selection Dropdown

- **Avatar Support**: Shows candidate avatars when available
- **Rich Information Display**: Shows name, email, position, and location
- **Email Validation Indicators**: Badges for candidates without valid emails
- **Loading States**: Spinner during API calls
- **Error States**: Clear error messaging

#### Improved Validation

- **Email Format Validation**: Validates email addresses using regex
- **Required Field Checks**: Ensures email and content are provided
- **Type-specific Validation**: Email type requires subject line
- **Gmail Integration Validation**: Validates before opening Gmail

### 6. Template Variable Enhancement

Enhanced template variable mapping with real candidate data:

- `candidate_name` → Real candidate name
- `candidate_email` → Real candidate email
- `candidate_phone` → Real candidate phone
- `position` → Real candidate position
- `candidate_location` → Real candidate location

## API Integration Details

### Endpoint Used

```
GET /api/v1/candidates
```

### API Parameters

```typescript
{
  per_page: 100,
  include: "jobPosting",
  sort: "name"
}
```

### Data Mapping

```typescript
// API Response → UI Interface
const mappedCandidates: Candidate[] = response.data.map(
  (apiCandidate: any) => ({
    id: apiCandidate.id.toString(),
    name: apiCandidate.name || "Không có tên",
    email: apiCandidate.email || "",
    phone: apiCandidate.phone || undefined,
    initials: generateInitials(apiCandidate.name),
    position: apiCandidate.position || "Chưa có vị trí",
    avatar: apiCandidate.avatar_url || apiCandidate.avatar,
    location: apiCandidate.location,
    status: apiCandidate.status || "active",
  }),
);
```

## Error Handling Strategy

### 1. Network Errors

- Shows toast error notification
- Falls back to demo candidate data
- Maintains form functionality

### 2. Validation Errors

- Email format validation with regex
- Required field validation
- Type-specific validation (email requires subject)

### 3. User Feedback

- Loading spinners during API calls
- Success/error toast messages
- Clear error states in dropdowns

## Performance Optimizations

### 1. Efficient Search

- `useMemo` for filtered candidates
- Real-time search without API calls
- Optimized rendering

### 2. API Caching

- Uses React Query through `useCandidates` hook
- Automatic caching and invalidation
- Reduced unnecessary API calls

### 3. Lazy Loading

- Candidates loaded only when form opens
- Prevents unnecessary API calls

## User Experience Enhancements

### 1. Visual Improvements

- Avatar display in dropdown and selection
- Rich candidate information display
- Clear loading and error states
- Professional email validation badges

### 2. Accessibility

- Screen reader friendly
- Keyboard navigation support
- Clear error messaging

### 3. Vietnamese Language Support

- All error messages in Vietnamese
- Proper placeholder text
- Consistent UI terminology

## Usage Examples

### 1. Basic Candidate Selection

1. User opens message form
2. API automatically loads candidates
3. User searches for candidate by name/email
4. User selects candidate from dropdown
5. Form auto-populates recipient fields

### 2. Template with Real Data

1. User selects a template
2. System extracts variables
3. Auto-fills with real candidate data
4. User can modify before sending

### 3. Error Recovery

1. API fails to load candidates
2. System shows error message
3. Falls back to demo data
4. User can still compose messages

## Future Enhancements

### Potential Improvements

1. **Pagination**: Handle large candidate lists
2. **Advanced Filters**: Filter by status, position, etc.
3. **Recent Candidates**: Show recently messaged candidates
4. **Bulk Messaging**: Support multiple recipients
5. **Contact Groups**: Predefined recipient groups

### API Enhancements

1. **Search API**: Server-side search for large datasets
2. **Candidate Details**: Fetch additional candidate information
3. **Message History**: Show previous messages with candidate
4. **Smart Suggestions**: AI-powered recipient suggestions

## Testing Checklist

- [x] API integration works correctly
- [x] Search functionality filters candidates
- [x] Email validation works properly
- [x] Error handling shows appropriate messages
- [x] Loading states display correctly
- [x] Avatar images load properly
- [x] Template variables use real data
- [x] Gmail integration validates email
- [x] Form validation prevents invalid submissions
- [x] Vietnamese language support works
- [x] Fallback to demo data on API errors
- [x] Performance is acceptable with large candidate lists

## Technical Implementation

### Key Files Modified

- `client/components/messages/MessageForm.tsx` - Main component update
- Added real API integration
- Enhanced search and filtering
- Improved validation and error handling

### Dependencies Used

- `@/hooks/useApi` - React Query integration
- `@/lib/api` - API service layer
- `lucide-react` - Icons for UI
- `sonner` - Toast notifications

### State Management

- React hooks for local state
- React Query for API state
- Proper cleanup and error boundaries
