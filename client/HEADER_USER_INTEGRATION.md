# Header Component - Real User Data Integration

## Overview
The Header.tsx component has been updated to replace mock user data with real user information from the authentication system and API endpoints.

## Key Changes

### 1. Authentication Integration
- **Added `useAuth` hook**: Imports user data from auth context
- **Added `useCurrentUser` hook**: Fetches fresh user data from `/api/v1/auth/me` endpoint
- **Combined data sources**: Uses currentUserData with fallback to authUser

### 2. Removed Mock Data
- **Removed `mockUser` object**: Eliminated hardcoded user information
- **Dynamic data binding**: All user information now comes from real API responses

### 3. Enhanced User Information Display

#### User Avatar
- **Real avatar URLs**: Supports `avatar` and `avatar_url` fields from API
- **Dynamic initials**: Generated from actual user name
- **Fallback handling**: Shows "U" when name is unavailable

#### User Details
- **Name**: Real user name with loading state
- **Email**: Real user email address
- **Role**: User role with proper capitalization
- **Department**: User department information
- **Plan**: Dynamically determined based on user role

### 4. Loading States
- **Loading indicators**: Shows spinning icon and loading text
- **Disabled interactions**: Prevents clicks during loading
- **Progressive loading**: Shows partial info as it becomes available

### 5. Error Handling
- **Authentication check**: Hides header for unauthenticated users
- **Graceful fallbacks**: Shows minimal info when API calls fail
- **Error boundaries**: Prevents crashes from missing data

## Implementation Details

### User Data Sources
```typescript
const { user: authUser, logout: authLogout, isLoading: authLoading } = useAuth();
const { data: currentUserData, isLoading: userDataLoading } = useCurrentUser();

// Combine with preference for fresh API data
const user = currentUserData?.data || authUser;
```

### Dynamic Initials Generation
```typescript
const getInitials = (name?: string) => {
  if (!name) return "U";
  return name
    .trim()
    .split(" ")
    .filter(n => n.length > 0)
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};
```

### Plan Detection Logic
```typescript
const getUserPlan = () => {
  if (user?.role === "admin") return "enterprise";
  if (user?.role === "manager") return "pro";
  return user?.plan || "free";
};
```

### Avatar URL Handling
```typescript
const getAvatarUrl = (user: any) => {
  return user?.avatar || user?.avatar_url || undefined;
};
```

## User Information Displayed

### Primary Info
- **Name**: User's full name
- **Email**: User's email address
- **Avatar**: Profile picture or generated initials

### Secondary Info
- **Role**: User role (Admin, Manager, etc.)
- **Department**: User's department
- **Plan**: Subscription plan (Enterprise, Pro, Free)

### Status Indicators
- **Loading state**: Shows when fetching user data
- **Plan badges**: Visual indicators for subscription tier
- **Role badges**: Shows user permissions level

## States Handled

### 1. Loading State
- Shows loading spinner in avatar
- Displays "Loading..." text
- Disables dropdown interactions

### 2. Authenticated State
- Shows full user information
- Enables all interactions
- Displays real-time data

### 3. Unauthenticated State
- Hides header completely
- Prevents unauthorized access
- Redirects to login flow

### 4. Error State
- Shows fallback information
- Maintains basic functionality
- Logs errors for debugging

## Logout Functionality
- **Real logout**: Uses auth context logout function
- **Localized messages**: Shows success message in user's language
- **Clean state**: Clears all user data on logout

## Responsive Design
- **Consistent styling**: Maintains original design
- **Loading animations**: Smooth transitions
- **Error states**: Graceful degradation

## Accessibility
- **Screen reader support**: Proper alt texts and labels
- **Keyboard navigation**: Full keyboard accessibility
- **Focus management**: Proper focus handling

## Performance Optimizations
- **React Query caching**: Efficient API data caching
- **Conditional rendering**: Only renders when necessary
- **Optimized re-renders**: Minimal unnecessary updates

## Future Enhancements

### Potential Improvements
1. **Real-time notifications**: Live notification updates
2. **Presence indicators**: Online/offline status
3. **Profile picture upload**: Direct avatar management
4. **Extended user preferences**: More customization options
5. **Activity tracking**: Last seen timestamps

### API Enhancements
1. **WebSocket integration**: Real-time updates
2. **Extended user profile**: Additional user fields
3. **Notification API**: Dynamic notification count
4. **Preference API**: User setting synchronization

## Testing Checklist

- [x] Displays real user name and email
- [x] Shows correct initials when no avatar
- [x] Displays user role and department
- [x] Shows appropriate plan badge
- [x] Handles loading states properly
- [x] Functions when user data is partial
- [x] Logs out correctly using real auth
- [x] Hides for unauthenticated users
- [x] Responsive design maintained
- [x] Accessibility features work
- [x] Error states handled gracefully
- [x] Vietnamese translations work

## Dependencies

### Hooks Used
- `useAuth` - Authentication context
- `useCurrentUser` - Fresh user data from API
- `useTranslation` - Internationalization
- `useTheme` - Theme management

### APIs Called
- `GET /api/v1/auth/me` - Current user information
- Auth context for login/logout state

### Component Libraries
- Radix UI components (Avatar, Dropdown, etc.)
- Lucide React icons
- Sonner for toast notifications
