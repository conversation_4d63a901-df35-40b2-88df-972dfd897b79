# Domain Mapping Analysis - Remaining Components

## 📊 Component Analysis & Domain Assignment

### 🎯 **Pipeline Components** → **Candidates Domain**
**Location**: `components/pipeline/`
**Target Domain**: `domains/candidates/components/pipeline/`
**Rationale**: Pipeline components are specifically for managing candidate workflow stages

**Components to Migrate**:
- `DraggableCandidateCard.tsx` - Candidate card for pipeline drag/drop
- `DroppableStageColumn.tsx` - Pipeline stage columns
- `EmailComposeModal.tsx` - Email composition for candidates

**Business Logic**: Candidate workflow management, stage transitions, candidate communications

---

### 📈 **Dashboard Components** → **Analytics Domain**
**Location**: `components/dashboard/`
**Target Domain**: `domains/analytics/components/dashboard/`
**Rationale**: Dashboard widgets display analytics and metrics across all domains

**Components to Migrate**:
- `DashboardWidgets.tsx` - Various analytics widgets
- `MetricCard.tsx` - Metric display cards
- `QuickActions.tsx` - Dashboard quick actions
- `RecentActivity.tsx` - Activity feed widget

**Business Logic**: Cross-domain analytics, metrics visualization, performance tracking

---

### 💬 **Messages Components** → **New Communications Domain**
**Location**: `components/messages/`
**Target Domain**: `domains/communications/components/`
**Rationale**: Messaging system is a distinct business domain for candidate/team communications

**Components to Migrate**:
- `MessageForm.tsx` - Message composition
- `MessageList.tsx` - Message listing
- `MessageDetail.tsx` - Message detail view
- `MessageTemplateForm.tsx` - Template creation
- `MessageTemplateList.tsx` - Template management
- `MessageTemplatePreview.tsx` - Template preview
- `EnhancedMessageTemplateList.tsx` - Enhanced template list
- `TemplateCategorySelector.tsx` - Category selection
- `TemplateVariableEditor.tsx` - Variable editing

**Business Logic**: Message templates, communications, email campaigns, notifications

---

### 👥 **Interviewers Components** → **Interviews Domain**
**Location**: `components/interviewers/`
**Target Domain**: `domains/interviews/components/interviewers/`
**Rationale**: Interviewer management is part of the interview process domain

**Components to Migrate**:
- `CreateEditInterviewerModal.tsx` - Interviewer CRUD operations
- `InterviewerDetailModal.tsx` - Interviewer profile details

**Business Logic**: Interviewer profiles, availability, skills, interview assignments

---

### 🏢 **Team Components** → **New Users Domain**
**Location**: `components/team/`
**Target Domain**: `domains/users/components/team/`
**Rationale**: Team/user management is a distinct business domain

**Components to Migrate**:
- `UserForm.tsx` - User creation/editing
- `UserDetailModal.tsx` - User profile details

**Business Logic**: User management, roles, permissions, team organization

---

### 🌐 **Landing Components** → **Keep as Shared**
**Location**: `components/landing/`
**Target**: **KEEP** in `components/landing/`
**Rationale**: Marketing/landing pages are shared across the application

**Components to Keep**:
- `Hero.tsx`, `Features.tsx`, `Benefits.tsx`, `Pricing.tsx`, `Blog.tsx`, `Contact.tsx`

**Business Logic**: Marketing content, public-facing pages, lead generation

---

### 🎨 **Layout Components** → **Keep as Shared**
**Location**: `components/layout/`
**Target**: **KEEP** in `components/layout/`
**Rationale**: Layout components are shared infrastructure

**Components to Keep**:
- `Header.tsx`, `Layout.tsx`, `Sidebar.tsx`

**Business Logic**: Application shell, navigation, responsive layout

---

### 🎛️ **UI Components** → **Keep as Shared**
**Location**: `components/ui/`
**Target**: **KEEP** in `components/ui/`
**Rationale**: UI primitives are shared across all domains

**Components to Keep**:
- All UI primitive components (buttons, inputs, cards, etc.)

**Business Logic**: Design system, reusable UI components

---

### 🔧 **Theme Components** → **Keep as Shared**
**Location**: `components/`
**Target**: **KEEP** in `components/`
**Rationale**: Theme management is application-wide functionality

**Components to Keep**:
- `theme-provider.tsx`, `theme-toggle.tsx`, `floating-theme-toggle.tsx`

**Business Logic**: Theme switching, dark/light mode

---

## 🏗️ New Domains to Create

### 1. **Communications Domain**
```
domains/communications/
├── components/
│   ├── messages/
│   ├── templates/
│   └── notifications/
├── hooks/
├── services/
├── types/
└── utils/
```

**Purpose**: Message templates, email campaigns, notifications, communications

### 2. **Users Domain**
```
domains/users/
├── components/
│   ├── team/
│   ├── profiles/
│   └── permissions/
├── hooks/
├── services/
├── types/
└── utils/
```

**Purpose**: User management, team organization, roles, permissions

---

## 📋 Migration Priority

### **High Priority** (Core Business Logic)
1. **Pipeline** → Candidates Domain
2. **Interviewers** → Interviews Domain
3. **Messages** → Communications Domain (new)
4. **Team** → Users Domain (new)

### **Medium Priority** (Analytics)
5. **Dashboard** → Analytics Domain

### **Low Priority** (Keep as Shared)
6. **Landing** → Keep shared
7. **Layout** → Keep shared
8. **UI** → Keep shared
9. **Theme** → Keep shared

---

## 🎯 Expected Outcomes

### **Domain Completeness**
- **Candidates**: Complete with pipeline components
- **Interviews**: Complete with interviewer management
- **Communications**: New domain with full messaging system
- **Users**: New domain with team management
- **Analytics**: Complete with dashboard widgets

### **Code Organization**
- Clear business domain boundaries
- No cross-domain dependencies
- Proper separation of concerns
- Consistent domain patterns

### **Maintainability**
- Easy to find domain-specific code
- Clear ownership of components
- Scalable architecture for future features
- Better testing strategies per domain
