/**
 * Shared code between client and server
 * Useful to share types between client and server
 * and/or small pure JS functions that can be used on both client and server
 */

/**
 * Standard API Response format
 */
export interface ApiResponse<T> {
  status: "success" | "error";
  message: string;
  data: T;
  errors?: Record<string, string[]>;
}

/**
 * Example response type for /api/demo
 */
export interface DemoResponse {
  message: string;
}
