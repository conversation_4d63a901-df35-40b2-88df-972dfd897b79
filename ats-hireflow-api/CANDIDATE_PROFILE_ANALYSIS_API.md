# Candidate Profile Analysis System - API Documentation

## Tổng quan

Hệ thống Candidate Profile Analysis là một giải pháp toàn diện để trích xuất và phân tích thông tin ứng viên từ CV, tích hợp với các dịch vụ AI bên thứ ba.

## Kiến trúc Database

### Bảng `candidate_profile_analyses`

Bảng duy nhất lưu trữ tất cả dữ liệu phân tích:

```sql
- id: Primary key
- candidate_id: Foreign key đến bảng candidates
- job_posting_id: Foreign key đến bảng job_postings (nullable)
- created_by: Foreign key đến bảng users
- analysis_type: enum('resume_extraction', 'ai_analysis', 'job_matching')
- status: enum('pending', 'processing', 'completed', 'failed')
- external_service_id: ID từ dịch vụ bên ngoài

// Thông tin trích xuất từ CV
- extracted_name, extracted_email, extracted_phone, extracted_address
- extracted_skills: JSON array
- extracted_experience: JSON array
- extracted_education: JSON array

// Kết quả phân tích AI
- ai_summary: Text
- strengths, weaknesses, improvement_areas, recommendations: JSON arrays

// Điểm số (0-100)
- overall_score, skills_score, experience_score, education_score, cultural_fit_score

// Dữ liệu job matching
- job_match_details, missing_requirements, matching_criteria: JSON

// Metadata xử lý
- analysis_started_at, analysis_completed_at, error_message, processing_logs
```

## API Endpoints

### 1. Trích xuất thông tin từ CV

#### `POST /api/v1/candidate-analysis/extract-resume`

Trích xuất thông tin từ trường `resume_url` của ứng viên.

**Request:**

```json
{
  "candidate_id": 123
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Resume extraction completed successfully",
  "data": {
    "id": 1,
    "candidate_id": 123,
    "analysis_type": "resume_extraction",
    "status": "completed",
    "extracted_information": {
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "phone": "+84-***********",
      "address": "123 Đường ABC, Quận 1, TP.HCM",
      "skills": ["PHP", "Laravel", "JavaScript", "React"],
      "experience": [
        {
          "company": "Công ty ABC",
          "position": "Senior Developer",
          "duration": "2020-2023",
          "description": "Phát triển ứng dụng web..."
        }
      ],
      "education": [
        {
          "institution": "Đại học Bách Khoa",
          "degree": "Cử nhân Công nghệ Thông tin",
          "graduation_year": "2020"
        }
      ]
    },
    "processing": {
      "started_at": "2024-07-20 10:00:00",
      "completed_at": "2024-07-20 10:00:05",
      "duration_seconds": 5
    },
    "created_at": "2024-07-20 10:00:00"
  }
}
```

### 2. Tạo phân tích AI

#### `POST /api/v1/candidate-analysis/generate-analysis`

Tạo phân tích AI cho ứng viên (có thể kèm job matching).

**Request:**

```json
{
  "candidate_id": 123,
  "job_posting_id": 456 // Optional - cho job matching
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Candidate analysis completed successfully",
  "data": {
    "id": 2,
    "candidate_id": 123,
    "job_posting_id": 456,
    "analysis_type": "job_matching",
    "status": "completed",
    "ai_analysis": {
      "summary": "Ứng viên có kinh nghiệm mạnh trong phát triển web...",
      "strengths": [
        "Kỹ năng lập trình vững chắc",
        "Kinh nghiệm làm việc nhóm tốt",
        "Khả năng học hỏi nhanh"
      ],
      "weaknesses": [
        "Thiếu kinh nghiệm với một số công nghệ mới",
        "Cần cải thiện kỹ năng thuyết trình"
      ],
      "improvement_areas": [
        "Học thêm về cloud computing",
        "Cải thiện kỹ năng quản lý dự án"
      ],
      "recommendations": [
        "Nên phỏng vấn kỹ thuật",
        "Thảo luận về mục tiêu nghề nghiệp",
        "Xem xét cho vị trí senior"
      ]
    },
    "scores": {
      "overall": 85,
      "skills": 90,
      "experience": 80,
      "education": 75,
      "cultural_fit": 88,
      "average": 83.6
    },
    "job_matching": {
      "match_details": {
        "match_percentage": 85,
        "key_alignments": [
          "Kỹ năng lập trình phù hợp",
          "Kinh nghiệm đáp ứng yêu cầu",
          "Trình độ học vấn phù hợp"
        ]
      },
      "missing_requirements": ["Chứng chỉ AWS", "Kinh nghiệm với Docker"],
      "matching_criteria": [
        "Ngôn ngữ lập trình: PHP, JavaScript",
        "Kinh nghiệm: 3+ năm",
        "Học vấn: Đại học chuyên ngành IT"
      ]
    }
  }
}
```

### 3. Lấy danh sách phân tích

#### `GET /api/v1/candidate-analysis/analyses`

Lấy danh sách các phân tích với filtering và pagination.

**Query Parameters:**

- `candidate_id`: Lọc theo ứng viên
- `job_posting_id`: Lọc theo job posting
- `analysis_type`: Lọc theo loại phân tích
- `status`: Lọc theo trạng thái
- `score_min`, `score_max`: Lọc theo điểm số
- `date_from`, `date_to`: Lọc theo thời gian
- `per_page`: Số item per page (default: 15)
- `include`: Include relationships (candidate,jobPosting,createdBy)

### 4. Lấy tóm tắt ứng viên

#### `GET /api/v1/candidate-analysis/candidate/{id}/summary`

Lấy tóm tắt toàn diện về ứng viên với tất cả các phân tích.

**Response:**

```json
{
  "status": "success",
  "data": {
    "candidate": {
      "id": 123,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "position": "Senior Developer",
      "rating": 4.5,
      "ai_score": 85
    },
    "extracted_information": {
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "phone": "+84-***********",
      "skills": ["PHP", "Laravel", "JavaScript"],
      "experience": [...],
      "education": [...]
    },
    "aggregate_scores": {
      "overall_average": 83.5,
      "skills_average": 87.2,
      "experience_average": 81.0,
      "education_average": 78.5,
      "cultural_fit_average": 85.8,
      "total_analyses": 5
    },
    "recent_analyses": [...]
  }
}
```

### 5. Cập nhật thông tin ứng viên từ dữ liệu trích xuất

#### `POST /api/v1/candidate-analysis/candidate/update-from-extraction`

Cập nhật thông tin ứng viên từ dữ liệu đã trích xuất.

**Request:**

```json
{
  "candidate_id": 123,
  "analysis_id": 1,
  "fields": ["name", "email", "phone", "skills"]
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Candidate information updated successfully",
  "data": {
    "updated_fields": ["name", "email", "phone", "skills"],
    "candidate": {
      "id": 123,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>"
      // ... updated candidate data
    }
  }
}
```

## Cấu hình

### Environment Variables

Thêm vào file `.env`:

```env
# Candidate Analysis Service
CANDIDATE_ANALYSIS_API_URL=https://api.your-ai-service.com
CANDIDATE_ANALYSIS_API_KEY=your_api_key_here
CANDIDATE_ANALYSIS_TIMEOUT=30
CANDIDATE_ANALYSIS_USE_MOCK=true  # Set false in production
```

## Loại phân tích

1. **`resume_extraction`**: Trích xuất thông tin từ CV
2. **`ai_analysis`**: Phân tích AI tổng quát về ứng viên
3. **`job_matching`**: So sánh ứng viên với job posting cụ thể

## Trạng thái xử lý

- **`pending`**: Đang chờ xử lý
- **`processing`**: Đang xử lý
- **`completed`**: Hoàn thành thành công
- **`failed`**: Xử lý thất bại

## Mock Data (Dữ liệu tiếng Việt)

Hệ thống hỗ trợ mock data **hoàn toàn bằng tiếng Việt** cho development và testing. Set `CANDIDATE_ANALYSIS_USE_MOCK=true` để sử dụng mock data thay vì gọi API thực.

### Dữ liệu mock bao gồm:

- **Tên ứng viên**: Nguyễn Văn An, Trần Thị Bình, Lê Hoàng Cường...
- **Địa chỉ**: Các địa chỉ tại TP.HCM với tên đường Việt Nam
- **Công ty**: Công ty TNHH Công nghệ ABC, Tập đoàn Phần mềm XYZ...
- **Trường học**: Đại học Bách khoa Hà Nội, Đại học Công nghệ Thông tin...
- **Phân tích AI**: Tất cả nội dung bằng tiếng Việt (điểm mạnh, điểm yếu, khuyến nghị...)
- **Job matching**: Tiêu chí và yêu cầu bằng tiếng Việt

## Tích hợp với hệ thống hiện tại

- Sử dụng authentication hiện có (Laravel Sanctum)
- Tuân theo patterns API hiện có
- Tích hợp với models Candidate và JobPosting
- Hỗ trợ activity logging và audit trails

## Lợi ích

1. **Giảm thời gian nhập liệu**: Tự động trích xuất thông tin từ CV
2. **Phân tích AI**: Đánh giá ứng viên một cách khách quan
3. **Job Matching**: So sánh ứng viên với yêu cầu công việc
4. **Tích hợp dễ dàng**: API đơn giản, dễ tích hợp với frontend
5. **Mở rộng**: Thiết kế để dễ dàng tích hợp với các dịch vụ AI khác
