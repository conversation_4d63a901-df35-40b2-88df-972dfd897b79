<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CandidateController;
use App\Http\Controllers\Api\CandidateProfileAnalysisController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\FileUploadController;
use App\Http\Controllers\Api\InterviewController;
use App\Http\Controllers\Api\InterviewerController;
use App\Http\Controllers\Api\InterviewFeedbackController;
use App\Http\Controllers\Api\JobPostingController;
use App\Http\Controllers\Api\MessageController;
use App\Http\Controllers\Api\MessageTemplateController;
use App\Http\Controllers\Api\UserController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::prefix('v1')->group(function () {
    // Authentication routes
    Route::post('/auth/login', [AuthController::class, 'login']);
    Route::post('/auth/register', [AuthController::class, 'register']);
    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        // Auth routes
        Route::get('/auth/me', [AuthController::class, 'me']);
        Route::post('/auth/logout', [AuthController::class, 'logout']);
        Route::post('/auth/refresh', [AuthController::class, 'refresh']);

        // Candidates routes
        Route::apiResource('candidates', CandidateController::class);
        Route::patch('/candidates/{candidate}/status', [CandidateController::class, 'updateStatus']);
        Route::post('/candidates/{candidate}/resume', [CandidateController::class, 'uploadResume']);
        Route::post('/candidates/{candidate}/ai-analysis', [CandidateController::class, 'triggerAiAnalysis']);

        // Job postings routes

        Route::get('/jobs/hiring-managers', [JobPostingController::class, 'getHiringManagers']);
        Route::get('/jobs/recruiters', [JobPostingController::class, 'getRecruiters']);
        Route::post('/jobs/bulk-action', [JobPostingController::class, 'bulkAction']);
        Route::get('/jobs/{jobPosting}/candidates', [JobPostingController::class, 'candidates']);
        Route::get('/jobs/{jobPosting}/analytics', [JobPostingController::class, 'analytics']);
        Route::patch('/jobs/{jobPosting}/status', [JobPostingController::class, 'updateStatus']);
        Route::apiResource('jobs', JobPostingController::class);

        // Interviews routes
        Route::apiResource('interviews', InterviewController::class);
        Route::patch('/interviews/{interview}/status', [InterviewController::class, 'updateStatus']);
        Route::post('/interviews/availability/check', [InterviewController::class, 'checkAvailability']);
        Route::post('/interviews/reminders/send', [InterviewController::class, 'sendReminders']);
        Route::get('/interviews/calendar/events', [InterviewController::class, 'calendarEvents']);

        // Interviewers routes
        Route::apiResource('interviewers', InterviewerController::class);

        // Interview feedback routes
        Route::apiResource('interview-feedback', InterviewFeedbackController::class);

        // Candidate Profile Analysis routes
        Route::prefix('candidate-analysis')->group(function () {
            Route::post('/extract-resume', [CandidateProfileAnalysisController::class, 'extractResume']);
            Route::post('/generate-analysis', [CandidateProfileAnalysisController::class, 'generateAnalysis']);
            Route::get('/analyses', [CandidateProfileAnalysisController::class, 'getAnalyses']);
            Route::get('/analyses/{analysis}', [CandidateProfileAnalysisController::class, 'getAnalysis']);
            Route::get('/candidate/{candidate}/summary', [CandidateProfileAnalysisController::class, 'getCandidateSummary']);
            Route::post('/candidate/update-from-extraction', [CandidateProfileAnalysisController::class, 'updateCandidateFromExtraction']);
        });

        // Message Template routes
        Route::prefix('message-templates')->group(function () {
            Route::get('/', [MessageTemplateController::class, 'index']);
            Route::post('/', [MessageTemplateController::class, 'store']);
            Route::get('/categories', [MessageTemplateController::class, 'categories']);
            Route::get('/types', [MessageTemplateController::class, 'types']);
            Route::get('/{messageTemplate}', [MessageTemplateController::class, 'show']);
            Route::put('/{messageTemplate}', [MessageTemplateController::class, 'update']);
            Route::delete('/{messageTemplate}', [MessageTemplateController::class, 'destroy']);
            Route::post('/{messageTemplate}/preview', [MessageTemplateController::class, 'preview']);
            Route::post('/{messageTemplate}/duplicate', [MessageTemplateController::class, 'duplicate']);
        });

        // Message routes
        Route::prefix('messages')->group(function () {
            Route::get('/', [MessageController::class, 'index']);
            Route::post('/', [MessageController::class, 'store']);
            Route::get('/statistics', [MessageController::class, 'statistics']);
            Route::post('/bulk-send', [MessageController::class, 'bulkSend']);
            Route::get('/{message}', [MessageController::class, 'show']);
            Route::put('/{message}', [MessageController::class, 'update']);
            Route::delete('/{message}', [MessageController::class, 'destroy']);
            Route::get('/{message}/thread', [MessageController::class, 'thread']);
            Route::post('/{message}/reply', [MessageController::class, 'reply']);
        });

        // Dashboard analytics routes
        Route::get('/dashboard/overview', [DashboardController::class, 'overview']);
        Route::get('/dashboard/recruitment-pipeline', [DashboardController::class, 'recruitmentPipeline']);
        Route::get('/dashboard/source-effectiveness', [DashboardController::class, 'sourceEffectiveness']);
        Route::get('/dashboard/team-performance', [DashboardController::class, 'teamPerformance']);
        Route::get('/dashboard/real-time-metrics', [DashboardController::class, 'realTimeMetrics']);
        Route::post('/dashboard/export', [DashboardController::class, 'exportData']);

        // File upload routes
        Route::post('/candidates/{candidate}/resume', [FileUploadController::class, 'uploadResume']);
        Route::post('/users/{user}/avatar', [FileUploadController::class, 'uploadAvatar']);
        Route::post('/files/upload', [FileUploadController::class, 'uploadDocument']);
        Route::post('/files/bulk-upload', [FileUploadController::class, 'uploadBulkFiles']);
        Route::delete('/files/delete', [FileUploadController::class, 'deleteFile']);
        Route::get('/files/info', [FileUploadController::class, 'getFileInfo']);
        Route::post('/files/download-url', [FileUploadController::class, 'generateDownloadUrl']);

        // User info route
        Route::get('/user', function (Request $request) {
            return $request->user();
        });

        // User management routes (Admin only)
        Route::prefix('users')->group(function () {
            Route::get('/', [UserController::class, 'index']);
            Route::post('/', [UserController::class, 'store']);
            Route::get('/roles', [UserController::class, 'getRoles']);
            Route::get('/statistics', [UserController::class, 'getStatistics']);
            Route::get('/{user}', [UserController::class, 'show']);
            Route::put('/{user}', [UserController::class, 'update']);
            Route::delete('/{user}', [UserController::class, 'destroy']);
            Route::put('/{user}/roles', [UserController::class, 'updateRoles']);
        });
    });
});
