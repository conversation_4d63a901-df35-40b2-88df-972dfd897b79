<?php

namespace Database\Seeders;

use App\Models\Candidate;
use App\Models\JobPosting;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating sample data...');

        // Get existing users
        $admin = User::where('email', '<EMAIL>')->first();
        $recruiter = User::where('email', '<EMAIL>')->first();
        $hiringManager = User::where('email', '<EMAIL>')->first();

        // Create job postings
        $this->command->info('Creating job postings...');

        $jobPostings = JobPosting::factory(10)->create([
            'hiring_manager_id' => $hiringManager->id,
            'recruiter_id' => $recruiter->id,
            'created_by' => $admin->id,
        ]);

        // Create some active job postings
        $activeJobs = JobPosting::factory(5)->active()->create([
            'hiring_manager_id' => $hiringManager->id,
            'recruiter_id' => $recruiter->id,
            'created_by' => $admin->id,
        ]);

        // Create job requirements, responsibilities, benefits, and skills for each job
        foreach ($jobPostings->concat($activeJobs) as $job) {
            // Requirements
            $job->requirements()->createMany([
                ['requirement_text' => 'Bachelor\'s degree in relevant field', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => '3+ years of experience', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Strong communication skills', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Experience with agile methodologies', 'is_mandatory' => false, 'sort_order' => 4],
            ]);

            // Responsibilities
            $job->responsibilities()->createMany([
                ['responsibility_text' => 'Develop and maintain software applications', 'sort_order' => 1],
                ['responsibility_text' => 'Collaborate with cross-functional teams', 'sort_order' => 2],
                ['responsibility_text' => 'Participate in code reviews', 'sort_order' => 3],
                ['responsibility_text' => 'Mentor junior developers', 'sort_order' => 4],
            ]);

            // Benefits
            $job->benefits()->createMany([
                ['benefit_text' => 'Competitive salary and bonuses', 'sort_order' => 1],
                ['benefit_text' => 'Health insurance coverage', 'sort_order' => 2],
                ['benefit_text' => 'Flexible working hours', 'sort_order' => 3],
                ['benefit_text' => 'Professional development opportunities', 'sort_order' => 4],
            ]);

            // Skills
            $job->skills()->createMany([
                ['skill_name' => 'JavaScript', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'React', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Node.js', 'importance_level' => 'preferred', 'proficiency_level' => 'beginner'],
                ['skill_name' => 'TypeScript', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'beginner'],
            ]);
        }

        // Create candidates for active jobs
        $this->command->info('Creating candidates...');

        foreach ($activeJobs as $job) {
            $candidates = Candidate::factory(rand(5, 15))->create([
                'job_posting_id' => $job->id,
                'created_by' => $recruiter->id,
                'assigned_to' => rand(0, 1) ? $recruiter->id : null,
            ]);

            // Create candidate education, work history, skills, and tags
            foreach ($candidates as $candidate) {
                // Education
                $candidate->education()->createMany([
                    [
                        'institution' => 'University of Technology',
                        'degree' => 'Bachelor of Computer Science',
                        'field_of_study' => 'Computer Science',
                        'start_year' => 2018,
                        'end_year' => 2022,
                        'gpa' => '3.5',
                        'sort_order' => 1,
                    ],
                ]);

                // Work History
                $candidate->workHistory()->createMany([
                    [
                        'company' => 'Tech Startup Inc.',
                        'position' => 'Junior Developer',
                        'start_date' => '2022-06-01',
                        'end_date' => '2024-01-01',
                        'is_current' => false,
                        'description' => 'Developed web applications using React and Node.js',
                        'sort_order' => 1,
                    ],
                    [
                        'company' => 'Current Company Ltd.',
                        'position' => 'Software Developer',
                        'start_date' => '2024-01-01',
                        'end_date' => null,
                        'is_current' => true,
                        'description' => 'Working on full-stack development projects',
                        'sort_order' => 2,
                    ],
                ]);

                // Skills
                $candidate->skills()->createMany([
                    ['skill_name' => 'JavaScript', 'proficiency_level' => 'advanced', 'years_of_experience' => 3],
                    ['skill_name' => 'React', 'proficiency_level' => 'intermediate', 'years_of_experience' => 2],
                    ['skill_name' => 'Node.js', 'proficiency_level' => 'intermediate', 'years_of_experience' => 2],
                    ['skill_name' => 'Python', 'proficiency_level' => 'beginner', 'years_of_experience' => 1],
                ]);

                // Tags
                $tags = ['experienced', 'team-player', 'quick-learner', 'problem-solver'];
                foreach (array_slice($tags, 0, rand(1, 3)) as $tag) {
                    $candidate->tags()->create([
                        'tag_name' => $tag,
                        'created_by' => $recruiter->id,
                    ]);
                }

                // Status history
                $candidate->statusHistory()->create([
                    'old_status' => null,
                    'new_status' => $candidate->status,
                    'changed_by' => $recruiter->id,
                    'notes' => 'Initial application received',
                ]);
            }

            // Update job applicant count
            $job->update(['applicant_count' => $candidates->count()]);
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . JobPosting::count() . ' job postings');
        $this->command->info('- ' . Candidate::count() . ' candidates');
    }
}
