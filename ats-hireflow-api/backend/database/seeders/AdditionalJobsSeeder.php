<?php

namespace Database\Seeders;

use App\Models\JobPosting;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdditionalJobsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Tạo thêm các vị trí công việc tiếng Việt...');

        $admin = User::where('email', '<EMAIL>')->first();
        $recruiter = User::where('email', '<EMAIL>')->first();
        $hiringManager = User::where('email', '<EMAIL>')->first();

        $additionalJobs = [
            [
                'title' => 'Chuyên Viên <PERSON> (HR Specialist)',
                'department' => 'Nhân Sự',
                'location' => 'Thành phố Hồ Ch<PERSON>',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 12000000,
                'salary_max' => 20000000,
                'currency' => 'VND',
                'description' => 'Chịu trách nhiệm tuyển dụng, đào tạo và phát triển nhân sự. Xây dựng quy trình HR và văn hóa doanh nghiệp.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Quản trị nhân lực, Tâm lý học hoặc tương đương',
                'company_culture' => 'Môi trường làm việc nhân văn, tôn trọng con người và phát triển bền vững.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'medium',
                'posted_date' => now()->subDays(4),
                'closing_date' => now()->addDays(26),
            ],
            [
                'title' => 'Lập Trình Viên Mobile (React Native)',
                'department' => 'Công Nghệ Thông Tin',
                'location' => 'Hà Nội',
                'type' => 'full-time',
                'work_location' => 'hybrid',
                'salary_min' => 20000000,
                'salary_max' => 35000000,
                'currency' => 'VND',
                'description' => 'Phát triển ứng dụng mobile đa nền tảng sử dụng React Native. Tối ưu hóa hiệu suất và trải nghiệm người dùng.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Công nghệ thông tin hoặc tương đương',
                'company_culture' => 'Văn hóa công nghệ tiên tiến, khuyến khích innovation và continuous learning.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'high',
                'posted_date' => now()->subDays(6),
                'closing_date' => now()->addDays(24),
            ],
            [
                'title' => 'Chuyên Viên Phân Tích Dữ Liệu (Data Analyst)',
                'department' => 'Phân Tích Dữ Liệu',
                'location' => 'Remote',
                'type' => 'full-time',
                'work_location' => 'remote',
                'salary_min' => 18000000,
                'salary_max' => 30000000,
                'currency' => 'VND',
                'description' => 'Phân tích dữ liệu kinh doanh, tạo báo cáo và dashboard. Hỗ trợ ra quyết định dựa trên dữ liệu.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Toán, Thống kê, Kinh tế hoặc tương đương',
                'company_culture' => 'Môi trường data-driven, khuyến khích tư duy phân tích và sáng tạo.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'high',
                'posted_date' => now()->subDays(2),
                'closing_date' => now()->addDays(28),
            ],
            [
                'title' => 'Nhân Viên Chăm Sóc Khách Hàng',
                'department' => 'Dịch Vụ Khách Hàng',
                'location' => 'Đà Nẵng',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 8000000,
                'salary_max' => 15000000,
                'currency' => 'VND',
                'description' => 'Hỗ trợ và tư vấn khách hàng qua điện thoại, email và chat. Giải quyết khiếu nại và xây dựng mối quan hệ tốt.',
                'education_required' => 'Tốt nghiệp Trung cấp trở lên, ưu tiên có kinh nghiệm dịch vụ khách hàng',
                'company_culture' => 'Môi trường thân thiện, tập trung vào sự hài lòng của khách hàng.',
                'experience_level' => 'entry',
                'status' => 'active',
                'priority' => 'medium',
                'posted_date' => now()->subDays(1),
                'closing_date' => now()->addDays(29),
            ],
            [
                'title' => 'Quản Lý Dự Án IT (Project Manager)',
                'department' => 'Công Nghệ Thông Tin',
                'location' => 'Thành phố Hồ Chí Minh',
                'type' => 'full-time',
                'work_location' => 'hybrid',
                'salary_min' => 30000000,
                'salary_max' => 50000000,
                'currency' => 'VND',
                'description' => 'Quản lý các dự án phát triển phần mềm từ khởi tạo đến triển khai. Điều phối team và đảm bảo tiến độ.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Công nghệ thông tin, có chứng chỉ PMP là lợi thế',
                'company_culture' => 'Môi trường agile, khuyến khích teamwork và continuous improvement.',
                'experience_level' => 'senior',
                'status' => 'active',
                'priority' => 'high',
                'posted_date' => now()->subDays(8),
                'closing_date' => now()->addDays(22),
            ],
        ];

        foreach ($additionalJobs as $jobData) {
            $job = JobPosting::create([
                ...$jobData,
                'hiring_manager_id' => $hiringManager->id,
                'recruiter_id' => $recruiter->id,
                'created_by' => $admin->id,
            ]);

            // Tạo yêu cầu, trách nhiệm, quyền lợi và kỹ năng cho từng job
            $this->createJobDetails($job);
        }

        $this->command->info('Hoàn thành tạo thêm vị trí công việc!');
        $this->command->info('Đã tạo thêm: ' . count($additionalJobs) . ' vị trí công việc');
    }

    private function createJobDetails($job)
    {
        // Tạo yêu cầu công việc
        $requirements = $this->getJobRequirements($job->title);
        foreach ($requirements as $req) {
            $job->requirements()->create($req);
        }

        // Tạo trách nhiệm
        $responsibilities = $this->getJobResponsibilities($job->title);
        foreach ($responsibilities as $resp) {
            $job->responsibilities()->create($resp);
        }

        // Tạo quyền lợi chung
        $benefits = [
            ['benefit_text' => 'Lương thưởng cạnh tranh theo năng lực', 'sort_order' => 1],
            ['benefit_text' => 'Bảo hiểm sức khỏe toàn diện', 'sort_order' => 2],
            ['benefit_text' => 'Thưởng hiệu suất và dự án', 'sort_order' => 3],
            ['benefit_text' => 'Đào tạo và phát triển kỹ năng', 'sort_order' => 4],
            ['benefit_text' => 'Môi trường làm việc hiện đại', 'sort_order' => 5],
        ];
        foreach ($benefits as $benefit) {
            $job->benefits()->create($benefit);
        }

        // Tạo kỹ năng yêu cầu
        $skills = $this->getJobSkills($job->title);
        foreach ($skills as $skill) {
            $job->skills()->create($skill);
        }
    }

    private function getJobRequirements($title)
    {
        $requirements = [
            'Chuyên Viên Nhân Sự (HR Specialist)' => [
                ['requirement_text' => 'Có ít nhất 2 năm kinh nghiệm HR', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Hiểu biết về luật lao động Việt Nam', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Kỹ năng giao tiếp và thuyết trình tốt', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Thành thạo MS Office', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            'Lập Trình Viên Mobile (React Native)' => [
                ['requirement_text' => 'Có ít nhất 2 năm kinh nghiệm React Native', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thành thạo JavaScript/TypeScript', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Hiểu biết về iOS và Android platform', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Kinh nghiệm với Redux, MobX', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            'Chuyên Viên Phân Tích Dữ Liệu (Data Analyst)' => [
                ['requirement_text' => 'Có ít nhất 1 năm kinh nghiệm phân tích dữ liệu', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thành thạo SQL và Excel', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Hiểu biết về Python hoặc R', 'is_mandatory' => false, 'sort_order' => 3],
                ['requirement_text' => 'Kinh nghiệm với Tableau, Power BI', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            'Nhân Viên Chăm Sóc Khách Hàng' => [
                ['requirement_text' => 'Kỹ năng giao tiếp tốt', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Kiên nhẫn và thái độ tích cực', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Khả năng làm việc ca', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Kinh nghiệm dịch vụ khách hàng', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            'Quản Lý Dự Án IT (Project Manager)' => [
                ['requirement_text' => 'Có ít nhất 5 năm kinh nghiệm quản lý dự án IT', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Hiểu biết về Agile/Scrum methodology', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Kỹ năng leadership và communication', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Có chứng chỉ PMP hoặc tương đương', 'is_mandatory' => false, 'sort_order' => 4],
            ],
        ];

        return $requirements[$title] ?? [];
    }

    private function getJobResponsibilities($title)
    {
        $responsibilities = [
            'Chuyên Viên Nhân Sự (HR Specialist)' => [
                ['responsibility_text' => 'Tuyển dụng và sàng lọc ứng viên', 'sort_order' => 1],
                ['responsibility_text' => 'Xây dựng quy trình HR và policy', 'sort_order' => 2],
                ['responsibility_text' => 'Đào tạo và phát triển nhân viên', 'sort_order' => 3],
                ['responsibility_text' => 'Quản lý hồ sơ và chấm công', 'sort_order' => 4],
            ],
            'Lập Trình Viên Mobile (React Native)' => [
                ['responsibility_text' => 'Phát triển ứng dụng mobile đa nền tảng', 'sort_order' => 1],
                ['responsibility_text' => 'Tối ưu hóa hiệu suất ứng dụng', 'sort_order' => 2],
                ['responsibility_text' => 'Tích hợp API và third-party services', 'sort_order' => 3],
                ['responsibility_text' => 'Testing và debugging', 'sort_order' => 4],
            ],
            'Chuyên Viên Phân Tích Dữ Liệu (Data Analyst)' => [
                ['responsibility_text' => 'Thu thập và làm sạch dữ liệu', 'sort_order' => 1],
                ['responsibility_text' => 'Tạo báo cáo và dashboard', 'sort_order' => 2],
                ['responsibility_text' => 'Phân tích xu hướng và insight', 'sort_order' => 3],
                ['responsibility_text' => 'Hỗ trợ ra quyết định kinh doanh', 'sort_order' => 4],
            ],
            'Nhân Viên Chăm Sóc Khách Hàng' => [
                ['responsibility_text' => 'Tiếp nhận và xử lý yêu cầu khách hàng', 'sort_order' => 1],
                ['responsibility_text' => 'Tư vấn sản phẩm và dịch vụ', 'sort_order' => 2],
                ['responsibility_text' => 'Giải quyết khiếu nại và phản hồi', 'sort_order' => 3],
                ['responsibility_text' => 'Báo cáo tình hình khách hàng', 'sort_order' => 4],
            ],
            'Quản Lý Dự Án IT (Project Manager)' => [
                ['responsibility_text' => 'Lập kế hoạch và quản lý tiến độ dự án', 'sort_order' => 1],
                ['responsibility_text' => 'Điều phối team và stakeholders', 'sort_order' => 2],
                ['responsibility_text' => 'Quản lý rủi ro và chất lượng', 'sort_order' => 3],
                ['responsibility_text' => 'Báo cáo tiến độ và kết quả', 'sort_order' => 4],
            ],
        ];

        return $responsibilities[$title] ?? [];
    }

    private function getJobSkills($title)
    {
        $skills = [
            'Chuyên Viên Nhân Sự (HR Specialist)' => [
                ['skill_name' => 'Tuyển dụng', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Luật lao động', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'MS Office', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Giao tiếp', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
            ],
            'Lập Trình Viên Mobile (React Native)' => [
                ['skill_name' => 'React Native', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'JavaScript', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'TypeScript', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Redux', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'intermediate'],
            ],
            'Chuyên Viên Phân Tích Dữ Liệu (Data Analyst)' => [
                ['skill_name' => 'SQL', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Excel', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Python', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Tableau', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'beginner'],
            ],
            'Nhân Viên Chăm Sóc Khách Hàng' => [
                ['skill_name' => 'Giao tiếp', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Kiên nhẫn', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Tin học văn phòng', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
            ],
            'Quản Lý Dự Án IT (Project Manager)' => [
                ['skill_name' => 'Agile/Scrum', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Leadership', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Project Management', 'importance_level' => 'required', 'proficiency_level' => 'expert'],
                ['skill_name' => 'JIRA', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
            ],
        ];

        return $skills[$title] ?? [];
    }
}
