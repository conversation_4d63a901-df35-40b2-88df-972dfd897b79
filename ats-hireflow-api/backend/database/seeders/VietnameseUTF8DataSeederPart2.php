<?php

namespace Database\Seeders;

use App\Models\Candidate;
use App\Models\CandidateEducation;
use App\Models\CandidateSkill;
use App\Models\CandidateTag;
use App\Models\CandidateWorkHistory;
use App\Models\Interview;
use App\Models\InterviewFeedback;
use App\Models\Message;
use App\Models\MessageTemplate;
use Illuminate\Database\Seeder;

trait VietnameseUTF8DataSeederPart2
{
    private function createCandidateDetails($candidate)
    {
        // Education data
        $educationData = [
            'Nguy<PERSON>' => [
                [
                    'institution' => 'Dai hoc Bach Khoa TP.HCM',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Cong nghe thong tin',
                    'start_year' => 2015,
                    'end_year' => 2019,
                    'gpa' => '3.6',
                    'sort_order' => 1,
                ],
            ],
            'Tran Thi Huong' => [
                [
                    'institution' => 'Dai hoc Khoa hoc Tu nhien TP.HCM',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Khoa hoc may tinh',
                    'start_year' => 2016,
                    'end_year' => 2020,
                    'gpa' => '3.4',
                    'sort_order' => 1,
                ],
            ],
            '<PERSON><PERSON>' => [
                [
                    'institution' => 'Dai hoc Cong nghe TP.HCM',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Ky thuat phan mem',
                    'start_year' => 2017,
                    'end_year' => 2021,
                    'gpa' => '3.5',
                    'sort_order' => 1,
                ],
            ],
            'Le Minh Tuan' => [
                [
                    'institution' => 'Dai hoc Ngoai thuong',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Marketing',
                    'start_year' => 2017,
                    'end_year' => 2021,
                    'gpa' => '3.7',
                    'sort_order' => 1,
                ],
            ],
            'Pham Thi Mai' => [
                [
                    'institution' => 'Dai hoc Kinh te Quoc dan',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Quan tri kinh doanh',
                    'start_year' => 2019,
                    'end_year' => 2023,
                    'gpa' => '3.5',
                    'sort_order' => 1,
                ],
            ],
            'Trinh Minh Duc' => [
                [
                    'institution' => 'Dai hoc Kinh te TP.HCM',
                    'degree' => 'Thac si',
                    'field_of_study' => 'Marketing',
                    'start_year' => 2015,
                    'end_year' => 2019,
                    'gpa' => '3.8',
                    'sort_order' => 1,
                ],
            ],
            'Hoang Van Duc' => [
                [
                    'institution' => 'Dai hoc Kinh te TP.HCM',
                    'degree' => 'Thac si',
                    'field_of_study' => 'Ke toan - Kiem toan',
                    'start_year' => 2008,
                    'end_year' => 2012,
                    'gpa' => '3.8',
                    'sort_order' => 1,
                ],
            ],
            'Phan Van Thanh' => [
                [
                    'institution' => 'Dai hoc Kinh te Da Nang',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Ke toan',
                    'start_year' => 2014,
                    'end_year' => 2018,
                    'gpa' => '3.6',
                    'sort_order' => 1,
                ],
            ],
            'Vu Thi Lan' => [
                [
                    'institution' => 'Dai hoc Ngoai thuong',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Kinh doanh quoc te',
                    'start_year' => 2016,
                    'end_year' => 2020,
                    'gpa' => '3.6',
                    'sort_order' => 1,
                ],
            ],
            'Dang Minh Khoa' => [
                [
                    'institution' => 'Dai hoc Kinh te TP.HCM',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Marketing',
                    'start_year' => 2020,
                    'end_year' => 2024,
                    'gpa' => '3.3',
                    'sort_order' => 1,
                ],
            ],
            'Bui Thi Thao' => [
                [
                    'institution' => 'Dai hoc My thuat Cong nghiep',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Thiet ke do hoa',
                    'start_year' => 2017,
                    'end_year' => 2021,
                    'gpa' => '3.7',
                    'sort_order' => 1,
                ],
            ],
            'Huynh Thi Tuyet' => [
                [
                    'institution' => 'Dai hoc Kien truc TP.HCM',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Thiet ke noi that',
                    'start_year' => 2016,
                    'end_year' => 2020,
                    'gpa' => '3.8',
                    'sort_order' => 1,
                ],
            ],
            'Ngo Thi Linh' => [
                [
                    'institution' => 'Dai hoc Khoa hoc Xa hoi va Nhan van',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Tam ly hoc',
                    'start_year' => 2018,
                    'end_year' => 2022,
                    'gpa' => '3.4',
                    'sort_order' => 1,
                ],
            ],
            'Cao Van Dat' => [
                [
                    'institution' => 'Dai hoc FPT',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Cong nghe thong tin',
                    'start_year' => 2019,
                    'end_year' => 2023,
                    'gpa' => '3.5',
                    'sort_order' => 1,
                ],
            ],
            'Dinh Thi Nga' => [
                [
                    'institution' => 'Dai hoc Kinh te Quoc dan',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Thong ke',
                    'start_year' => 2018,
                    'end_year' => 2022,
                    'gpa' => '3.6',
                    'sort_order' => 1,
                ],
            ],
            'Tran Quoc Bao' => [
                [
                    'institution' => 'Dai hoc Kinh te TP.HCM',
                    'degree' => 'Thac si',
                    'field_of_study' => 'Quan tri kinh doanh',
                    'start_year' => 2010,
                    'end_year' => 2014,
                    'gpa' => '3.9',
                    'sort_order' => 1,
                ],
            ],
            'Nguyen Thi Hong' => [
                [
                    'institution' => 'Dai hoc Bach Khoa Da Nang',
                    'degree' => 'Cu nhan',
                    'field_of_study' => 'Cong nghe thong tin',
                    'start_year' => 2017,
                    'end_year' => 2021,
                    'gpa' => '3.5',
                    'sort_order' => 1,
                ],
            ],
        ];

        if (isset($educationData[$candidate->name])) {
            foreach ($educationData[$candidate->name] as $edu) {
                $candidate->education()->create($edu);
            }
        }

        // Work History
        $workHistoryData = [
            'Nguyen Van Minh' => [
                [
                    'company' => 'FPT Software',
                    'position' => 'PHP Developer',
                    'start_date' => '2019-07-01',
                    'end_date' => '2021-12-31',
                    'is_current' => false,
                    'description' => 'Phat trien ung dung web cho khach hang Nhat Ban su dung Laravel framework',
                    'sort_order' => 1,
                ],
                [
                    'company' => 'Tiki Corporation',
                    'position' => 'Senior PHP Developer',
                    'start_date' => '2022-01-01',
                    'end_date' => null,
                    'is_current' => true,
                    'description' => 'Phat trien va toi uu hoa he thong e-commerce, quan ly team 3 developers',
                    'sort_order' => 2,
                ],
            ],
            'Tran Thi Huong' => [
                [
                    'company' => 'Viettel Software',
                    'position' => 'Junior PHP Developer',
                    'start_date' => '2020-08-01',
                    'end_date' => '2023-06-30',
                    'is_current' => false,
                    'description' => 'Phat trien cac module cho he thong quan ly noi bo',
                    'sort_order' => 1,
                ],
            ],
            'Ly Van Hung' => [
                [
                    'company' => 'Startup ABC',
                    'position' => 'Full Stack Developer',
                    'start_date' => '2021-06-01',
                    'end_date' => null,
                    'is_current' => true,
                    'description' => 'Phat trien ung dung web va mobile cho startup fintech',
                    'sort_order' => 1,
                ],
            ],
            'Le Minh Tuan' => [
                [
                    'company' => 'VNG Corporation',
                    'position' => 'Digital Marketing Executive',
                    'start_date' => '2021-06-01',
                    'end_date' => '2023-12-31',
                    'is_current' => false,
                    'description' => 'Quan ly chien dich quang cao cho cac san pham game, ngan sach 2 ty/thang',
                    'sort_order' => 1,
                ],
            ],
            'Pham Thi Mai' => [
                [
                    'company' => 'Sendo',
                    'position' => 'Marketing Intern',
                    'start_date' => '2023-01-01',
                    'end_date' => '2023-12-31',
                    'is_current' => false,
                    'description' => 'Ho tro team marketing trong viec tao content va chay quang cao',
                    'sort_order' => 1,
                ],
            ],
        ];

        if (isset($workHistoryData[$candidate->name])) {
            foreach ($workHistoryData[$candidate->name] as $work) {
                $candidate->workHistory()->create($work);
            }
        }

        // Skills - simplified to just arrays of strings
        $skillsData = [
            'Nguyen Van Minh' => ['PHP', 'Laravel', 'MySQL', 'JavaScript', 'Vue.js'],
            'Tran Thi Huong' => ['PHP', 'Laravel', 'MySQL', 'HTML/CSS'],
            'Le Minh Tuan' => ['Facebook Ads', 'Google Ads', 'Google Analytics', 'SEO'],
            'Hoang Van Duc' => ['MISA', 'Excel', 'Luat Thue', 'SAP'],
            'Vu Thi Lan' => ['Ban hang B2B', 'Dam phan', 'CRM', 'Tieng Anh'],
            'Bui Thi Thao' => ['Adobe Photoshop', 'Adobe Illustrator', 'Adobe InDesign', 'Figma'],
            'Huynh Thi Tuyet' => ['UI/UX Design', 'Figma', 'Adobe XD', 'Sketch'],
            'Ngo Thi Linh' => ['Tuyen dung', 'Luat lao dong', 'MS Office', 'Giao tiep'],
            'Cao Van Dat' => ['React Native', 'JavaScript', 'TypeScript', 'Redux'],
            'Dinh Thi Nga' => ['SQL', 'Excel', 'Python', 'Tableau'],
            'Tran Quoc Bao' => ['Quan ly doi ngu', 'Xay dung chien luoc', 'Dam phan thuong mai', 'Phan tich thi truong'],
            'Nguyen Thi Hong' => ['Manual Testing', 'Test Case Design', 'Selenium', 'JIRA'],
        ];

        if (isset($skillsData[$candidate->name])) {
            $candidate->skills = $skillsData[$candidate->name];
        }

        // Tags - simplified to just arrays of strings
        $tagsData = [
            'Nguyen Van Minh' => ['kinh-nghiem-cao', 'leader', 'technical-expert', 'team-player'],
            'Tran Thi Huong' => ['potential-high', 'eager-to-learn', 'good-attitude'],
            'Le Minh Tuan' => ['marketing-expert', 'data-driven', 'creative', 'results-oriented'],
            'Pham Thi Mai' => ['fresh-graduate', 'enthusiastic', 'quick-learner'],
            'Hoang Van Duc' => ['senior-expert', 'leadership', 'compliance-expert', 'strategic-thinking'],
            'Vu Thi Lan' => ['sales-champion', 'relationship-builder', 'target-achiever'],
            'Dang Minh Khoa' => ['junior-talent', 'growth-potential', 'motivated'],
            'Bui Thi Thao' => ['creative-talent', 'portfolio-strong', 'brand-experience'],
            'Huynh Thi Tuyet' => ['ui-ux-expert', 'user-focused', 'design-thinking'],
            'Ngo Thi Linh' => ['hr-potential', 'people-person', 'communication-skills'],
            'Cao Van Dat' => ['mobile-developer', 'react-native', 'cross-platform'],
            'Dinh Thi Nga' => ['data-analyst', 'sql-expert', 'analytical-thinking'],
            'Tran Quoc Bao' => ['sales-leader', 'team-builder', 'strategic-sales'],
            'Nguyen Thi Hong' => ['qa-expert', 'detail-oriented', 'process-improvement'],
        ];

        if (isset($tagsData[$candidate->name])) {
            $candidate->tags = $tagsData[$candidate->name];
        }
    }
}
