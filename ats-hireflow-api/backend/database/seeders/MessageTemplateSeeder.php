<?php

namespace Database\Seeders;

use App\Models\MessageTemplate;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MessageTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminUser = User::first();
        if (!$adminUser) {
            $adminUser = User::factory()->create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
            ]);
        }

        $templates = [
            // Interview Templates
            [
                'name' => 'Lời mời phỏng vấn - Email chính thức',
                'subject' => 'Lời mời phỏng vấn - Vị trí {{job_title}} tại {{company_name}}',
                'content' => '<PERSON><PERSON>h gửi {{candidate_name}},

Chúng tôi rất vui mừng thông báo rằng hồ sơ ứng tuyển của bạn cho vị trí {{job_title}} tại {{company_name}} đã được chọn để tham gia vòng phỏng vấn.

📅 Thông tin chi tiết:
• Thời gian: {{interview_date}} lúc {{interview_time}}
• Địa điểm: {{interview_location}}
• Hình thức: {{interview_type}}
• Người phỏng vấn: {{interviewer_name}}
• Thời gian dự kiến: {{interview_duration}} phút

📋 Tài liệu cần chuẩn bị:
• CMND/CCCD bản gốc
• Bằng cấp liên quan
• Portfolio/dự án đã thực hiện (nếu có)

Vui lòng xác nhận tham gia bằng cách phản hồi email này trước {{confirmation_deadline}}.

Nếu có bất kỳ thắc mắc nào, vui lòng liên hệ với chúng tôi qua:
📧 Email: {{recruiter_email}}
📞 Điện thoại: {{recruiter_phone}}

Chúng tôi rất mong được gặp bạn!

Trân trọng,
{{recruiter_name}}
Phòng Nhân sự - {{company_name}}',
                'variables' => [
                    'candidate_name',
                    'job_title',
                    'company_name',
                    'interview_date',
                    'interview_time',
                    'interview_location',
                    'interview_type',
                    'interviewer_name',
                    'interview_duration',
                    'confirmation_deadline',
                    'recruiter_name',
                    'recruiter_email',
                    'recruiter_phone'
                ],
                'category' => MessageTemplate::CATEGORY_INTERVIEW,
                'type' => MessageTemplate::TYPE_EMAIL,
                'language' => 'vi',
                'is_active' => true,
            ],

            [
                'name' => 'Lời mời phỏng vấn - SMS ngắn gọn',
                'subject' => '',
                'content' => 'Xin chào {{candidate_name}}! 🎉 Chúng tôi mời bạn phỏng vấn vị trí {{job_title}} vào {{interview_date}} lúc {{interview_time}} tại {{interview_location}}. Vui lòng xác nhận: {{recruiter_phone}}',
                'variables' => [
                    'candidate_name',
                    'job_title',
                    'interview_date',
                    'interview_time',
                    'interview_location',
                    'recruiter_phone'
                ],
                'category' => MessageTemplate::CATEGORY_INTERVIEW,
                'type' => MessageTemplate::TYPE_SMS,
                'language' => 'vi',
                'is_active' => true,
            ],

            // Job Offer Templates
            [
                'name' => 'Thư mời làm việc - Email chính thức',
                'subject' => '🎉 Chúc mừng! Thư mời làm việc - {{job_title}} tại {{company_name}}',
                'content' => 'Kính gửi {{candidate_name}},

🎉 CHÚC MỪNG! 🎉

Chúng tôi rất vui mừng thông báo rằng bạn đã được chọn cho vị trí {{job_title}} tại {{company_name}}.

💼 Chi tiết đề nghị làm việc:
• Vị trí: {{job_title}}
• Phòng ban: {{department}}
• Mức lương: {{salary}} (gross)
• Ngày bắt đầu làm việc: {{start_date}}
• Thời gian thử việc: {{probation_period}}
• Địa điểm làm việc: {{work_location}}

🎁 Phúc lợi và quyền lợi:
{{benefits}}

📋 Bước tiếp theo:
Vui lòng phản hồi email này trong vòng {{response_deadline}} để xác nhận việc nhận offer. Chúng tôi sẽ gửi hợp đồng lao động chi tiết sau khi nhận được xác nhận của bạn.

Chúng tôi rất mong được chào đón bạn vào đại gia đình {{company_name}} và tin rằng bạn sẽ có những đóng góp tích cực cho sự phát triển của công ty.

Trân trọng,
{{recruiter_name}}
Phòng Nhân sự - {{company_name}}
📧 {{recruiter_email}} | 📞 {{recruiter_phone}}',
                'variables' => [
                    'candidate_name',
                    'job_title',
                    'company_name',
                    'department',
                    'salary',
                    'start_date',
                    'probation_period',
                    'work_location',
                    'benefits',
                    'response_deadline',
                    'recruiter_name',
                    'recruiter_email',
                    'recruiter_phone'
                ],
                'category' => MessageTemplate::CATEGORY_OFFER,
                'type' => MessageTemplate::TYPE_EMAIL,
                'language' => 'vi',
                'is_active' => true,
            ],

            [
                'name' => 'Thông báo offer - SMS',
                'subject' => '',
                'content' => '🎉 Chúc mừng {{candidate_name}}! Bạn đã được chọn cho vị trí {{job_title}} tại {{company_name}}. Mức lương: {{salary}}. Ngày bắt đầu: {{start_date}}. Chi tiết qua email. Liên hệ: {{recruiter_phone}}',
                'variables' => [
                    'candidate_name',
                    'job_title',
                    'company_name',
                    'salary',
                    'start_date',
                    'recruiter_phone'
                ],
                'category' => MessageTemplate::CATEGORY_OFFER,
                'type' => MessageTemplate::TYPE_SMS,
                'language' => 'vi',
                'is_active' => true,
            ],

            // Rejection Templates
            [
                'name' => 'Thông báo từ chối - Email lịch sự',
                'subject' => 'Thông báo kết quả ứng tuyển - {{job_title}}',
                'content' => 'Kính gửi {{candidate_name}},

Trước tiên, chúng tôi xin chân thành cảm ơn bạn đã dành thời gian ứng tuyển vị trí {{job_title}} tại {{company_name}} và tham gia quá trình tuyển dụng của chúng tôi.

Sau khi xem xét kỹ lưỡng tất cả các ứng viên, chúng tôi rất tiếc phải thông báo rằng lần này chúng tôi đã chọn ứng viên khác có kinh nghiệm và kỹ năng phù hợp hơn với yêu cầu cụ thể của vị trí này.

{{#if feedback}}
💬 Phản hồi từ nhà tuyển dụng:
{{feedback}}
{{/if}}

Chúng tôi rất ấn tượng với hồ sơ và kinh nghiệm của bạn. Chúng tôi sẽ lưu giữ thông tin của bạn trong cơ sở dữ liệu và sẽ liên hệ khi có cơ hội phù hợp khác trong tương lai.

Chúc bạn thành công trong việc tìm kiếm cơ hội nghề nghiệp mới và đạt được những mục tiêu đã đề ra!

Trân trọng,
{{recruiter_name}}
Phòng Nhân sự - {{company_name}}',
                'variables' => [
                    'candidate_name',
                    'job_title',
                    'company_name',
                    'feedback',
                    'recruiter_name'
                ],
                'category' => MessageTemplate::CATEGORY_REJECTION,
                'type' => MessageTemplate::TYPE_EMAIL,
                'language' => 'vi',
                'is_active' => true,
            ],

            [
                'name' => 'Thông báo từ chối - SMS',
                'subject' => '',
                'content' => 'Xin chào {{candidate_name}}, cảm ơn bạn đã ứng tuyển vị trí {{job_title}} tại {{company_name}}. Rất tiếc lần này chúng tôi chưa thể hợp tác. Chúng tôi sẽ liên hệ khi có cơ hội phù hợp khác. Chúc bạn thành công! 🌟',
                'variables' => [
                    'candidate_name',
                    'job_title',
                    'company_name'
                ],
                'category' => MessageTemplate::CATEGORY_REJECTION,
                'type' => MessageTemplate::TYPE_SMS,
                'language' => 'vi',
                'is_active' => true,
            ],
        ];

        foreach ($templates as $template) {
            MessageTemplate::create(array_merge($template, [
                'created_by' => $adminUser->id,
                'version' => 1,
            ]));
        }
    }
}
