<?php

namespace Database\Seeders;

use App\Models\Candidate;
use App\Models\JobPosting;
use App\Models\User;
use App\Models\JobRequirement;
use App\Models\JobResponsibility;
use App\Models\JobBenefit;
use App\Models\JobSkill;
use App\Models\CandidateEducation;
use App\Models\CandidateWorkHistory;
use App\Models\CandidateSkill;
use App\Models\CandidateTag;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VietnameseDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Tạo dữ liệu tiếng Việt...');

        // Lấy users hiện có
        $admin = User::where('email', '<EMAIL>')->first();
        $recruiter = User::where('email', '<EMAIL>')->first();
        $hiringManager = User::where('email', '<EMAIL>')->first();

        // Tạo các vị trí công việc tiếng Việt
        $this->command->info('Tạo các vị trí công việc...');

        $vietnameseJobs = [
            [
                'title' => 'Lập Trình Viên PHP Senior',
                'department' => 'Công Nghệ Thông Tin',
                'location' => 'Thành phố Hồ Chí Minh',
                'type' => 'full-time',
                'work_location' => 'hybrid',
                'salary_min' => ********,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Chúng tôi đang tìm kiếm một Lập trình viên PHP Senior có kinh nghiệm để tham gia vào đội ngũ phát triển sản phẩm. Ứng viên sẽ chịu trách nhiệm phát triển và duy trì các ứng dụng web sử dụng PHP và Laravel framework.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Công nghệ thông tin hoặc tương đương',
                'company_culture' => 'Môi trường làm việc năng động, sáng tạo với nhiều cơ hội phát triển nghề nghiệp.',
                'experience_level' => 'senior',
                'status' => 'active',
                'priority' => 'high',
                'posted_date' => now()->subDays(5),
                'closing_date' => now()->addDays(25),
            ],
            [
                'title' => 'Chuyên Viên Marketing Digital',
                'department' => 'Marketing',
                'location' => 'Hà Nội',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 15000000,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Vị trí này phù hợp với ứng viên có đam mê với marketing số và mong muốn phát triển thương hiệu trực tuyến. Bạn sẽ làm việc với các kênh digital như Facebook, Google Ads, SEO/SEM.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Marketing, Kinh tế hoặc tương đương',
                'company_culture' => 'Văn hóa doanh nghiệp trẻ trung, khuyến khích sáng tạo và học hỏi.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'medium',
                'posted_date' => now()->subDays(3),
                'closing_date' => now()->addDays(27),
            ],
            [
                'title' => 'Kế Toán Trưởng',
                'department' => 'Tài Chính',
                'location' => 'Đà Nẵng',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 20000000,
                'salary_max' => 35000000,
                'currency' => 'VND',
                'description' => 'Chịu trách nhiệm quản lý toàn bộ hoạt động kế toán của công ty, lập báo cáo tài chính, quản lý ngân sách và tuân thủ các quy định pháp luật về tài chính.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Kế toán, Tài chính hoặc tương đương',
                'company_culture' => 'Môi trường làm việc chuyên nghiệp, ổn định với chế độ đãi ngộ hấp dẫn.',
                'experience_level' => 'senior',
                'status' => 'active',
                'priority' => 'high',
                'posted_date' => now()->subDays(7),
                'closing_date' => now()->addDays(23),
            ],
            [
                'title' => 'Nhân Viên Kinh Doanh B2B',
                'department' => 'Kinh Doanh',
                'location' => 'Thành phố Hồ Chí Minh',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 12000000,
                'salary_max' => 20000000,
                'currency' => 'VND',
                'description' => 'Phát triển và duy trì mối quan hệ với khách hàng doanh nghiệp, tìm kiếm cơ hội kinh doanh mới, đàm phán hợp đồng và đạt chỉ tiêu doanh số.',
                'education_required' => 'Tốt nghiệp Đại học các chuyên ngành liên quan',
                'company_culture' => 'Môi trường cạnh tranh lành mạnh với hoa hồng hấp dẫn.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'medium',
                'posted_date' => now()->subDays(2),
                'closing_date' => now()->addDays(28),
            ],
            [
                'title' => 'Thiết Kế Đồ Họa (Graphic Designer)',
                'department' => 'Sáng Tạo',
                'location' => 'Remote',
                'type' => 'full-time',
                'work_location' => 'remote',
                'salary_min' => 10000000,
                'salary_max' => 18000000,
                'currency' => 'VND',
                'description' => 'Thiết kế các tài liệu marketing, branding, website và các sản phẩm truyền thông khác. Làm việc chặt chẽ với đội marketing để tạo ra những sản phẩm sáng tạo.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Mỹ thuật, Thiết kế đồ họa hoặc tương đương',
                'company_culture' => 'Môi trường sáng tạo, linh hoạt với nhiều dự án thú vị.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'medium',
                'posted_date' => now()->subDays(1),
                'closing_date' => now()->addDays(29),
            ],
        ];

        foreach ($vietnameseJobs as $jobData) {
            $job = JobPosting::create([
                ...$jobData,
                'hiring_manager_id' => $hiringManager->id,
                'recruiter_id' => $recruiter->id,
                'created_by' => $admin->id,
            ]);

            // Tạo yêu cầu công việc
            $this->createJobRequirements($job);

            // Tạo trách nhiệm công việc
            $this->createJobResponsibilities($job);

            // Tạo quyền lợi
            $this->createJobBenefits($job);

            // Tạo kỹ năng yêu cầu
            $this->createJobSkills($job);
        }

        // Tạo ứng viên tiếng Việt
        $this->command->info('Tạo hồ sơ ứng viên...');
        $this->createVietnameseCandidates();

        $this->command->info('Hoàn thành tạo dữ liệu tiếng Việt!');
        $this->command->info('Đã tạo:');
        $this->command->info('- ' . count($vietnameseJobs) . ' vị trí công việc');
        $this->command->info('- 25 hồ sơ ứng viên');
    }

    private function createJobRequirements($job)
    {
        $requirements = [
            'Lập Trình Viên PHP Senior' => [
                ['requirement_text' => 'Có ít nhất 5 năm kinh nghiệm lập trình PHP', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thành thạo Laravel Framework', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Kinh nghiệm với MySQL, PostgreSQL', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Hiểu biết về Git, Docker', 'is_mandatory' => false, 'sort_order' => 4],
                ['requirement_text' => 'Kỹ năng giao tiếp tiếng Anh tốt', 'is_mandatory' => false, 'sort_order' => 5],
            ],
            'Chuyên Viên Marketing Digital' => [
                ['requirement_text' => 'Có ít nhất 2 năm kinh nghiệm Digital Marketing', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thành thạo Facebook Ads, Google Ads', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Hiểu biết về SEO/SEM', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Kỹ năng phân tích dữ liệu', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            'Kế Toán Trưởng' => [
                ['requirement_text' => 'Có ít nhất 7 năm kinh nghiệm kế toán', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Có chứng chỉ kế toán trưởng', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Thành thạo phần mềm kế toán MISA, FAST', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Hiểu biết pháp luật thuế', 'is_mandatory' => true, 'sort_order' => 4],
            ],
            'Nhân Viên Kinh Doanh B2B' => [
                ['requirement_text' => 'Có ít nhất 2 năm kinh nghiệm bán hàng B2B', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Kỹ năng đàm phán và thuyết phục tốt', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Khả năng làm việc dưới áp lực', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Có mạng lưới khách hàng sẵn có', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            'Thiết Kế Đồ Họa (Graphic Designer)' => [
                ['requirement_text' => 'Có ít nhất 2 năm kinh nghiệm thiết kế', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thành thạo Adobe Creative Suite', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Có khiếu thẩm mỹ và sáng tạo', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Hiểu biết về UI/UX Design', 'is_mandatory' => false, 'sort_order' => 4],
            ],
        ];

        if (isset($requirements[$job->title])) {
            foreach ($requirements[$job->title] as $req) {
                $job->requirements()->create($req);
            }
        }
    }

    private function createJobResponsibilities($job)
    {
        $responsibilities = [
            'Lập Trình Viên PHP Senior' => [
                ['responsibility_text' => 'Phát triển và duy trì các ứng dụng web sử dụng PHP/Laravel', 'sort_order' => 1],
                ['responsibility_text' => 'Thiết kế cơ sở dữ liệu và tối ưu hóa hiệu suất', 'sort_order' => 2],
                ['responsibility_text' => 'Code review và hướng dẫn junior developers', 'sort_order' => 3],
                ['responsibility_text' => 'Tham gia vào việc lập kế hoạch và ước lượng dự án', 'sort_order' => 4],
                ['responsibility_text' => 'Nghiên cứu và áp dụng công nghệ mới', 'sort_order' => 5],
            ],
            'Chuyên Viên Marketing Digital' => [
                ['responsibility_text' => 'Lập kế hoạch và triển khai chiến dịch marketing online', 'sort_order' => 1],
                ['responsibility_text' => 'Quản lý và tối ưu hóa quảng cáo Facebook, Google', 'sort_order' => 2],
                ['responsibility_text' => 'Phân tích dữ liệu và báo cáo hiệu quả chiến dịch', 'sort_order' => 3],
                ['responsibility_text' => 'Tạo nội dung marketing sáng tạo', 'sort_order' => 4],
            ],
            'Kế Toán Trưởng' => [
                ['responsibility_text' => 'Quản lý và giám sát toàn bộ hoạt động kế toán', 'sort_order' => 1],
                ['responsibility_text' => 'Lập báo cáo tài chính định kỳ', 'sort_order' => 2],
                ['responsibility_text' => 'Quản lý ngân sách và dự báo tài chính', 'sort_order' => 3],
                ['responsibility_text' => 'Đảm bảo tuân thủ các quy định pháp luật', 'sort_order' => 4],
            ],
            'Nhân Viên Kinh Doanh B2B' => [
                ['responsibility_text' => 'Tìm kiếm và phát triển khách hàng mới', 'sort_order' => 1],
                ['responsibility_text' => 'Duy trì mối quan hệ với khách hàng hiện tại', 'sort_order' => 2],
                ['responsibility_text' => 'Đàm phán hợp đồng và chốt deal', 'sort_order' => 3],
                ['responsibility_text' => 'Báo cáo kết quả kinh doanh định kỳ', 'sort_order' => 4],
            ],
            'Thiết Kế Đồ Họa (Graphic Designer)' => [
                ['responsibility_text' => 'Thiết kế các tài liệu marketing và branding', 'sort_order' => 1],
                ['responsibility_text' => 'Tạo concept và ý tưởng sáng tạo', 'sort_order' => 2],
                ['responsibility_text' => 'Phối hợp với team marketing trong các dự án', 'sort_order' => 3],
                ['responsibility_text' => 'Đảm bảo chất lượng và thống nhất thương hiệu', 'sort_order' => 4],
            ],
        ];

        if (isset($responsibilities[$job->title])) {
            foreach ($responsibilities[$job->title] as $resp) {
                $job->responsibilities()->create($resp);
            }
        }
    }

    private function createJobBenefits($job)
    {
        $commonBenefits = [
            ['benefit_text' => 'Lương thưởng cạnh tranh, xét tăng lương định kỳ', 'sort_order' => 1],
            ['benefit_text' => 'Bảo hiểm sức khỏe cao cấp cho nhân viên và gia đình', 'sort_order' => 2],
            ['benefit_text' => 'Thưởng tháng 13, thưởng hiệu suất công việc', 'sort_order' => 3],
            ['benefit_text' => 'Nghỉ phép năm 12 ngày + các ngày lễ tết', 'sort_order' => 4],
            ['benefit_text' => 'Cơ hội đào tạo và phát triển nghề nghiệp', 'sort_order' => 5],
            ['benefit_text' => 'Môi trường làm việc trẻ trung, năng động', 'sort_order' => 6],
            ['benefit_text' => 'Team building, du lịch công ty hàng năm', 'sort_order' => 7],
        ];

        foreach ($commonBenefits as $benefit) {
            $job->benefits()->create($benefit);
        }
    }

    private function createJobSkills($job)
    {
        $skills = [
            'Lập Trình Viên PHP Senior' => [
                ['skill_name' => 'PHP', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Laravel', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'MySQL', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'JavaScript', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Vue.js', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'beginner'],
                ['skill_name' => 'Docker', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'beginner'],
            ],
            'Chuyên Viên Marketing Digital' => [
                ['skill_name' => 'Facebook Ads', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Google Ads', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Google Analytics', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'SEO', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Content Marketing', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
            ],
            'Kế Toán Trưởng' => [
                ['skill_name' => 'MISA', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Excel', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Luật Thuế', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Báo cáo tài chính', 'importance_level' => 'required', 'proficiency_level' => 'expert'],
            ],
            'Nhân Viên Kinh Doanh B2B' => [
                ['skill_name' => 'Bán hàng B2B', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Đàm phán', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'CRM', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Tiếng Anh', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'intermediate'],
            ],
            'Thiết Kế Đồ Họa (Graphic Designer)' => [
                ['skill_name' => 'Adobe Photoshop', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Adobe Illustrator', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Adobe InDesign', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Figma', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'beginner'],
            ],
        ];

        if (isset($skills[$job->title])) {
            foreach ($skills[$job->title] as $skill) {
                $job->skills()->create($skill);
            }
        }
    }

    private function createVietnameseCandidates()
    {
        $recruiter = User::where('email', '<EMAIL>')->first();
        $jobs = JobPosting::whereIn('title', [
            'Lập Trình Viên PHP Senior',
            'Chuyên Viên Marketing Digital',
            'Kế Toán Trưởng',
            'Nhân Viên Kinh Doanh B2B',
            'Thiết Kế Đồ Họa (Graphic Designer)'
        ])->get();

        $vietnameseCandidates = [
            // Ứng viên cho vị trí Lập Trình Viên PHP Senior
            [
                'name' => 'Nguyễn Văn Minh',
                'email' => '<EMAIL>',
                'phone' => '0901234567',
                'position' => 'Senior PHP Developer',
                'experience' => '5-10 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(3),
                'source' => 'LinkedIn',
                'location' => 'Quận 1, TP.HCM',
                'salary_expectation_min' => 28000000,
                'salary_expectation_max' => 42000000,
                'rating' => 4.5,
                'ai_score' => 88,
                'linkedin_url' => 'https://linkedin.com/in/minh-nguyen-dev',
                'github_url' => 'https://github.com/minhdev',
                'notes' => 'Ứng viên có kinh nghiệm tốt với Laravel, đã làm việc tại nhiều công ty công nghệ.',
                'job_title' => 'Lập Trình Viên PHP Senior',
            ],
            [
                'name' => 'Trần Thị Hương',
                'email' => '<EMAIL>',
                'phone' => '0912345678',
                'position' => 'PHP Developer',
                'experience' => '3-5 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(5),
                'source' => 'TopCV',
                'location' => 'Quận 7, TP.HCM',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => 35000000,
                'rating' => 4.2,
                'ai_score' => 82,
                'notes' => 'Có kinh nghiệm với PHP và Laravel, kỹ năng giao tiếp tốt.',
                'job_title' => 'Lập Trình Viên PHP Senior',
            ],

            // Ứng viên cho vị trí Marketing Digital
            [
                'name' => 'Lê Minh Tuấn',
                'email' => '<EMAIL>',
                'phone' => '0923456789',
                'position' => 'Digital Marketing Specialist',
                'experience' => '3-5 years',
                'status' => 'offer',
                'applied_date' => now()->subDays(8),
                'source' => 'VietnamWorks',
                'location' => 'Ba Đình, Hà Nội',
                'salary_expectation_min' => 18000000,
                'salary_expectation_max' => 28000000,
                'rating' => 4.7,
                'ai_score' => 91,
                'notes' => 'Có kinh nghiệm quản lý ngân sách quảng cáo lớn, hiểu biết sâu về Facebook Ads.',
                'job_title' => 'Chuyên Viên Marketing Digital',
            ],
            [
                'name' => 'Phạm Thị Mai',
                'email' => '<EMAIL>',
                'phone' => '0934567890',
                'position' => 'Marketing Executive',
                'experience' => '1-3 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(2),
                'source' => 'Referral',
                'location' => 'Cầu Giấy, Hà Nội',
                'salary_expectation_min' => 15000000,
                'salary_expectation_max' => 22000000,
                'rating' => 3.8,
                'ai_score' => 75,
                'notes' => 'Ứng viên trẻ, nhiệt huyết, có kiến thức cơ bản về digital marketing.',
                'job_title' => 'Chuyên Viên Marketing Digital',
            ],

            // Ứng viên cho vị trí Kế Toán Trưởng
            [
                'name' => 'Hoàng Văn Đức',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'Chief Accountant',
                'experience' => '10+ years',
                'status' => 'interview',
                'applied_date' => now()->subDays(6),
                'source' => 'CareerBuilder',
                'location' => 'Hải Châu, Đà Nẵng',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 4.8,
                'ai_score' => 94,
                'notes' => 'Có chứng chỉ kế toán trưởng, kinh nghiệm quản lý tại công ty niêm yết.',
                'job_title' => 'Kế Toán Trưởng',
            ],

            // Ứng viên cho vị trí Kinh Doanh B2B
            [
                'name' => 'Vũ Thị Lan',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'B2B Sales Manager',
                'experience' => '3-5 years',
                'status' => 'hired',
                'applied_date' => now()->subDays(15),
                'source' => 'Indeed',
                'location' => 'Quận 3, TP.HCM',
                'salary_expectation_min' => 15000000,
                'salary_expectation_max' => ********,
                'rating' => 4.6,
                'ai_score' => 89,
                'notes' => 'Có mạng lưới khách hàng rộng, kỹ năng đàm phán xuất sắc.',
                'job_title' => 'Nhân Viên Kinh Doanh B2B',
            ],
            [
                'name' => 'Đặng Minh Khoa',
                'email' => '<EMAIL>',
                'phone' => '0967890123',
                'position' => 'Sales Executive',
                'experience' => '1-3 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(4),
                'source' => 'Company Website',
                'location' => 'Quận 1, TP.HCM',
                'salary_expectation_min' => 12000000,
                'salary_expectation_max' => 18000000,
                'rating' => 3.9,
                'ai_score' => 78,
                'notes' => 'Ứng viên năng động, có tinh thần học hỏi cao.',
                'job_title' => 'Nhân Viên Kinh Doanh B2B',
            ],

            // Ứng viên cho vị trí Thiết Kế Đồ Họa
            [
                'name' => 'Bùi Thị Thảo',
                'email' => '<EMAIL>',
                'phone' => '0978901234',
                'position' => 'Graphic Designer',
                'experience' => '3-5 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(7),
                'source' => 'Behance',
                'location' => 'Remote',
                'salary_expectation_min' => 12000000,
                'salary_expectation_max' => 20000000,
                'rating' => 4.4,
                'ai_score' => 86,
                'portfolio_url' => 'https://behance.net/thaobui',
                'notes' => 'Portfolio ấn tượng, có kinh nghiệm làm việc với các thương hiệu lớn.',
                'job_title' => 'Thiết Kế Đồ Họa (Graphic Designer)',
            ],
        ];

        foreach ($vietnameseCandidates as $candidateData) {
            $job = $jobs->where('title', $candidateData['job_title'])->first();
            if (!$job) continue;

            unset($candidateData['job_title']);

            $candidate = Candidate::create([
                ...$candidateData,
                'job_posting_id' => $job->id,
                'created_by' => $recruiter->id,
                'assigned_to' => $recruiter->id,
                'salary_currency' => 'VND',
            ]);

            // Tạo học vấn cho ứng viên
            $this->createCandidateEducation($candidate);

            // Tạo kinh nghiệm làm việc
            $this->createCandidateWorkHistory($candidate);

            // Tạo kỹ năng
            $this->createCandidateSkills($candidate);

            // Tạo tags
            $this->createCandidateTags($candidate);

            // Tạo lịch sử trạng thái
            $candidate->statusHistory()->create([
                'old_status' => null,
                'new_status' => $candidate->status,
                'changed_by' => $recruiter->id,
                'notes' => 'Hồ sơ ứng viên được tạo',
            ]);
        }
    }

    private function createCandidateEducation($candidate)
    {
        $educationData = [
            'Nguyễn Văn Minh' => [
                [
                    'institution' => 'Đại học Bách Khoa TP.HCM',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Công nghệ thông tin',
                    'start_year' => 2015,
                    'end_year' => 2019,
                    'gpa' => '3.6',
                    'sort_order' => 1,
                ],
            ],
            'Trần Thị Hương' => [
                [
                    'institution' => 'Đại học Khoa học Tự nhiên TP.HCM',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Khoa học máy tính',
                    'start_year' => 2016,
                    'end_year' => 2020,
                    'gpa' => '3.4',
                    'sort_order' => 1,
                ],
            ],
            'Lê Minh Tuấn' => [
                [
                    'institution' => 'Đại học Ngoại thương',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Marketing',
                    'start_year' => 2017,
                    'end_year' => 2021,
                    'gpa' => '3.7',
                    'sort_order' => 1,
                ],
            ],
            'Phạm Thị Mai' => [
                [
                    'institution' => 'Đại học Kinh tế Quốc dân',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Quản trị kinh doanh',
                    'start_year' => 2019,
                    'end_year' => 2023,
                    'gpa' => '3.5',
                    'sort_order' => 1,
                ],
            ],
            'Hoàng Văn Đức' => [
                [
                    'institution' => 'Đại học Kinh tế TP.HCM',
                    'degree' => 'Thạc sĩ',
                    'field_of_study' => 'Kế toán - Kiểm toán',
                    'start_year' => 2008,
                    'end_year' => 2012,
                    'gpa' => '3.8',
                    'sort_order' => 1,
                ],
            ],
            'Vũ Thị Lan' => [
                [
                    'institution' => 'Đại học Ngoại thương',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Kinh doanh quốc tế',
                    'start_year' => 2016,
                    'end_year' => 2020,
                    'gpa' => '3.6',
                    'sort_order' => 1,
                ],
            ],
            'Đặng Minh Khoa' => [
                [
                    'institution' => 'Đại học Kinh tế TP.HCM',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Marketing',
                    'start_year' => 2020,
                    'end_year' => 2024,
                    'gpa' => '3.3',
                    'sort_order' => 1,
                ],
            ],
            'Bùi Thị Thảo' => [
                [
                    'institution' => 'Đại học Mỹ thuật Công nghiệp',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Thiết kế đồ họa',
                    'start_year' => 2017,
                    'end_year' => 2021,
                    'gpa' => '3.7',
                    'sort_order' => 1,
                ],
            ],
        ];

        if (isset($educationData[$candidate->name])) {
            foreach ($educationData[$candidate->name] as $edu) {
                $candidate->education()->create($edu);
            }
        }
    }

    private function createCandidateWorkHistory($candidate)
    {
        $workHistoryData = [
            'Nguyễn Văn Minh' => [
                [
                    'company' => 'FPT Software',
                    'position' => 'PHP Developer',
                    'start_date' => '2019-07-01',
                    'end_date' => '2021-12-31',
                    'is_current' => false,
                    'description' => 'Phát triển ứng dụng web cho khách hàng Nhật Bản sử dụng Laravel framework',
                    'sort_order' => 1,
                ],
                [
                    'company' => 'Tiki Corporation',
                    'position' => 'Senior PHP Developer',
                    'start_date' => '2022-01-01',
                    'end_date' => null,
                    'is_current' => true,
                    'description' => 'Phát triển và tối ưu hóa hệ thống e-commerce, quản lý team 3 developers',
                    'sort_order' => 2,
                ],
            ],
            'Trần Thị Hương' => [
                [
                    'company' => 'Viettel Software',
                    'position' => 'Junior PHP Developer',
                    'start_date' => '2020-08-01',
                    'end_date' => '2023-06-30',
                    'is_current' => false,
                    'description' => 'Phát triển các module cho hệ thống quản lý nội bộ',
                    'sort_order' => 1,
                ],
            ],
            'Lê Minh Tuấn' => [
                [
                    'company' => 'VNG Corporation',
                    'position' => 'Digital Marketing Executive',
                    'start_date' => '2021-06-01',
                    'end_date' => '2023-12-31',
                    'is_current' => false,
                    'description' => 'Quản lý chiến dịch quảng cáo cho các sản phẩm game, ngân sách 2 tỷ/tháng',
                    'sort_order' => 1,
                ],
            ],
            'Phạm Thị Mai' => [
                [
                    'company' => 'Sendo',
                    'position' => 'Marketing Intern',
                    'start_date' => '2023-01-01',
                    'end_date' => '2023-12-31',
                    'is_current' => false,
                    'description' => 'Hỗ trợ team marketing trong việc tạo content và chạy quảng cáo',
                    'sort_order' => 1,
                ],
            ],
            'Hoàng Văn Đức' => [
                [
                    'company' => 'Sacombank',
                    'position' => 'Kế toán viên',
                    'start_date' => '2012-03-01',
                    'end_date' => '2017-12-31',
                    'is_current' => false,
                    'description' => 'Thực hiện công tác kế toán tổng hợp, lập báo cáo tài chính',
                    'sort_order' => 1,
                ],
                [
                    'company' => 'Vingroup',
                    'position' => 'Kế toán trưởng',
                    'start_date' => '2018-01-01',
                    'end_date' => null,
                    'is_current' => true,
                    'description' => 'Quản lý bộ phận kế toán 15 người, chịu trách nhiệm về báo cáo tài chính tập đoàn',
                    'sort_order' => 2,
                ],
            ],
            'Vũ Thị Lan' => [
                [
                    'company' => 'Samsung Vietnam',
                    'position' => 'B2B Sales Executive',
                    'start_date' => '2020-07-01',
                    'end_date' => null,
                    'is_current' => true,
                    'description' => 'Phụ trách khu vực miền Nam, đạt 120% target hàng năm',
                    'sort_order' => 1,
                ],
            ],
            'Đặng Minh Khoa' => [
                [
                    'company' => 'Shopee Vietnam',
                    'position' => 'Sales Intern',
                    'start_date' => '2023-06-01',
                    'end_date' => '2024-01-31',
                    'is_current' => false,
                    'description' => 'Hỗ trợ team sales trong việc chăm sóc khách hàng và tìm kiếm leads mới',
                    'sort_order' => 1,
                ],
            ],
            'Bùi Thị Thảo' => [
                [
                    'company' => 'Ogilvy Vietnam',
                    'position' => 'Junior Graphic Designer',
                    'start_date' => '2021-08-01',
                    'end_date' => '2023-12-31',
                    'is_current' => false,
                    'description' => 'Thiết kế các campaign cho khách hàng FMCG, làm việc với các thương hiệu lớn',
                    'sort_order' => 1,
                ],
            ],
        ];

        if (isset($workHistoryData[$candidate->name])) {
            foreach ($workHistoryData[$candidate->name] as $work) {
                $candidate->workHistory()->create($work);
            }
        }
    }

    private function createCandidateSkills($candidate)
    {
        $skillsData = [
            'Nguyễn Văn Minh' => [
                ['skill_name' => 'PHP', 'proficiency_level' => 'advanced', 'years_of_experience' => 5],
                ['skill_name' => 'Laravel', 'proficiency_level' => 'advanced', 'years_of_experience' => 4],
                ['skill_name' => 'MySQL', 'proficiency_level' => 'advanced', 'years_of_experience' => 5],
                ['skill_name' => 'JavaScript', 'proficiency_level' => 'intermediate', 'years_of_experience' => 3],
                ['skill_name' => 'Vue.js', 'proficiency_level' => 'intermediate', 'years_of_experience' => 2],
            ],
            'Trần Thị Hương' => [
                ['skill_name' => 'PHP', 'proficiency_level' => 'intermediate', 'years_of_experience' => 3],
                ['skill_name' => 'Laravel', 'proficiency_level' => 'intermediate', 'years_of_experience' => 2],
                ['skill_name' => 'MySQL', 'proficiency_level' => 'intermediate', 'years_of_experience' => 3],
                ['skill_name' => 'HTML/CSS', 'proficiency_level' => 'advanced', 'years_of_experience' => 4],
            ],
            'Lê Minh Tuấn' => [
                ['skill_name' => 'Facebook Ads', 'proficiency_level' => 'advanced', 'years_of_experience' => 3],
                ['skill_name' => 'Google Ads', 'proficiency_level' => 'advanced', 'years_of_experience' => 3],
                ['skill_name' => 'Google Analytics', 'proficiency_level' => 'advanced', 'years_of_experience' => 3],
                ['skill_name' => 'SEO', 'proficiency_level' => 'intermediate', 'years_of_experience' => 2],
            ],
            'Phạm Thị Mai' => [
                ['skill_name' => 'Facebook Ads', 'proficiency_level' => 'beginner', 'years_of_experience' => 1],
                ['skill_name' => 'Content Marketing', 'proficiency_level' => 'intermediate', 'years_of_experience' => 1],
                ['skill_name' => 'Canva', 'proficiency_level' => 'intermediate', 'years_of_experience' => 2],
            ],
            'Hoàng Văn Đức' => [
                ['skill_name' => 'MISA', 'proficiency_level' => 'expert', 'years_of_experience' => 10],
                ['skill_name' => 'Excel', 'proficiency_level' => 'expert', 'years_of_experience' => 12],
                ['skill_name' => 'Luật Thuế', 'proficiency_level' => 'expert', 'years_of_experience' => 12],
                ['skill_name' => 'SAP', 'proficiency_level' => 'advanced', 'years_of_experience' => 6],
            ],
            'Vũ Thị Lan' => [
                ['skill_name' => 'Bán hàng B2B', 'proficiency_level' => 'advanced', 'years_of_experience' => 4],
                ['skill_name' => 'Đàm phán', 'proficiency_level' => 'advanced', 'years_of_experience' => 4],
                ['skill_name' => 'CRM', 'proficiency_level' => 'intermediate', 'years_of_experience' => 3],
                ['skill_name' => 'Tiếng Anh', 'proficiency_level' => 'intermediate', 'years_of_experience' => 8],
            ],
            'Đặng Minh Khoa' => [
                ['skill_name' => 'Bán hàng', 'proficiency_level' => 'beginner', 'years_of_experience' => 1],
                ['skill_name' => 'Giao tiếp', 'proficiency_level' => 'intermediate', 'years_of_experience' => 2],
                ['skill_name' => 'Excel', 'proficiency_level' => 'intermediate', 'years_of_experience' => 2],
            ],
            'Bùi Thị Thảo' => [
                ['skill_name' => 'Adobe Photoshop', 'proficiency_level' => 'advanced', 'years_of_experience' => 4],
                ['skill_name' => 'Adobe Illustrator', 'proficiency_level' => 'advanced', 'years_of_experience' => 4],
                ['skill_name' => 'Adobe InDesign', 'proficiency_level' => 'intermediate', 'years_of_experience' => 3],
                ['skill_name' => 'Figma', 'proficiency_level' => 'intermediate', 'years_of_experience' => 2],
            ],
        ];

        if (isset($skillsData[$candidate->name])) {
            foreach ($skillsData[$candidate->name] as $skill) {
                $candidate->skills()->create($skill);
            }
        }
    }

    private function createCandidateTags($candidate)
    {
        $recruiter = User::where('email', '<EMAIL>')->first();

        $tagsData = [
            'Nguyễn Văn Minh' => ['kinh-nghiem-cao', 'leader', 'technical-expert', 'team-player'],
            'Trần Thị Hương' => ['potential-high', 'eager-to-learn', 'good-attitude'],
            'Lê Minh Tuấn' => ['marketing-expert', 'data-driven', 'creative', 'results-oriented'],
            'Phạm Thị Mai' => ['fresh-graduate', 'enthusiastic', 'quick-learner'],
            'Hoàng Văn Đức' => ['senior-expert', 'leadership', 'compliance-expert', 'strategic-thinking'],
            'Vũ Thị Lan' => ['sales-champion', 'relationship-builder', 'target-achiever'],
            'Đặng Minh Khoa' => ['junior-talent', 'growth-potential', 'motivated'],
            'Bùi Thị Thảo' => ['creative-talent', 'portfolio-strong', 'brand-experience'],
        ];

        if (isset($tagsData[$candidate->name])) {
            foreach ($tagsData[$candidate->name] as $tagName) {
                $candidate->tags()->create([
                    'tag_name' => $tagName,
                    'created_by' => $recruiter->id,
                ]);
            }
        }
    }
}
