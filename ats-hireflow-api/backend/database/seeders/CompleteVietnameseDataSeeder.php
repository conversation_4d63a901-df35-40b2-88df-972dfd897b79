<?php

namespace Database\Seeders;

use App\Models\Candidate;
use App\Models\JobPosting;
use App\Models\User;
use App\Models\Interview;
use App\Models\Interviewer;
use App\Models\InterviewFeedback;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CompleteVietnameseDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🇻🇳 Tạo dữ liệu hoàn toàn mới bằng tiếng Việt...');

        // Tạo permissions và roles
        $this->createPermissionsAndRoles();

        // Tạo users
        $users = $this->createUsers();

        // Tạo job postings
        $jobs = $this->createJobPostings($users);

        // Tạo candidates
        $candidates = $this->createCandidates($users, $jobs);

        // Tạo interviewers
        $interviewers = $this->createInterviewers($users);

        // Tạo interviews
        $interviews = $this->createInterviews($candidates, $jobs, $interviewers, $users);

        // Tạo interview feedback
        $this->createInterviewFeedback($interviews, $interviewers);

        // Tạo message templates
        $this->call(MessageTemplateSeeder::class);

        $this->command->info('✅ Hoàn thành tạo dữ liệu tiếng Việt!');
        $this->command->info("📊 Thống kê:");
        $this->command->info("- Users: " . User::count());
        $this->command->info("- Job Postings: " . JobPosting::count());
        $this->command->info("- Candidates: " . Candidate::count());
        $this->command->info("- Interviews: " . Interview::count());
        $this->command->info("- Interview Feedback: " . InterviewFeedback::count());
    }

    private function createPermissionsAndRoles()
    {
        $this->command->info('🔐 Tạo permissions và roles...');

        // Tạo permissions
        $permissions = [
            // Candidate permissions
            'view_candidates',
            'create_candidates',
            'edit_candidates',
            'delete_candidates',
            'manage_candidate_status',
            'view_candidate_analytics',

            // Job posting permissions
            'view_jobs',
            'create_jobs',
            'edit_jobs',
            'delete_jobs',
            'publish_jobs',
            'manage_job_status',
            'view_job_analytics',

            // Interview permissions
            'view_interviews',
            'create_interviews',
            'edit_interviews',
            'delete_interviews',
            'manage_interview_schedule',
            'conduct_interviews',
            'view_interview_feedback',

            // Interviewer management permissions
            'view_interviewers',
            'create_interviewers',
            'edit_interviewers',
            'delete_interviewers',
            'manage_interviewer_availability',
            'assign_interviewers',

            // User management permissions
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',
            'manage_roles',
            'manage_permissions',

            // Dashboard and analytics
            'view_dashboard',
            'view_analytics',
            'export_data',

            // System permissions
            'manage_settings',
            'view_activity_log',
            'manage_templates'
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Tạo roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $recruiterRole = Role::firstOrCreate(['name' => 'recruiter']);
        $hiringManagerRole = Role::firstOrCreate(['name' => 'hiring_manager']);
        $interviewerRole = Role::firstOrCreate(['name' => 'interviewer']);

        // Gán permissions cho roles
        $adminRole->givePermissionTo(Permission::all());

        $recruiterRole->givePermissionTo([
            'view_candidates',
            'create_candidates',
            'edit_candidates',
            'manage_candidate_status',
            'view_jobs',
            'create_jobs',
            'edit_jobs',
            'publish_jobs',
            'view_interviews',
            'create_interviews',
            'edit_interviews',
            'manage_interview_schedule',
            'view_interviewers',
            'create_interviewers',
            'edit_interviewers',
            'assign_interviewers',
            'view_dashboard',
            'view_analytics',
            'export_data'
        ]);

        $hiringManagerRole->givePermissionTo([
            'view_candidates',
            'view_candidate_analytics',
            'view_jobs',
            'create_jobs',
            'edit_jobs',
            'view_job_analytics',
            'view_interviews',
            'create_interviews',
            'view_interview_feedback',
            'view_interviewers',
            'assign_interviewers',
            'view_dashboard',
            'view_analytics'
        ]);

        $interviewerRole->givePermissionTo([
            'view_candidates',
            'view_interviews',
            'conduct_interviews',
            'view_interview_feedback'
        ]);
    }

    private function createUsers()
    {
        $this->command->info('👥 Tạo users...');

        $users = [];

        // Admin user
        $admin = User::create([
            'name' => 'Quản Trị Viên',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'admin',
            'department' => 'Quản Trị',
            'title' => 'Quản Trị Viên Hệ Thống',
            'phone' => '0901234567',
        ]);
        $admin->assignRole('admin');
        $users['admin'] = $admin;

        // Recruiter users
        $recruiter1 = User::create([
            'name' => 'Nguyễn Thị Lan Anh',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'recruiter',
            'department' => 'Nhân Sự',
            'title' => 'Chuyên Viên Tuyển Dụng Senior',
            'phone' => '0912345678',
        ]);
        $recruiter1->assignRole('recruiter');
        $users['recruiter1'] = $recruiter1;

        $recruiter2 = User::create([
            'name' => 'Trần Văn Minh',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'recruiter',
            'department' => 'Nhân Sự',
            'title' => 'Chuyên Viên Tuyển Dụng',
            'phone' => '**********',
        ]);
        $recruiter2->assignRole('recruiter');
        $users['recruiter2'] = $recruiter2;

        // Hiring Manager users
        $manager1 = User::create([
            'name' => 'Lê Hoàng Nam',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'hiring_manager',
            'department' => 'Công Nghệ Thông Tin',
            'title' => 'Trưởng Phòng IT',
            'phone' => '0934567890',
        ]);
        $manager1->assignRole('hiring_manager');
        $users['manager1'] = $manager1;

        $manager2 = User::create([
            'name' => 'Phạm Thị Hương',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'hiring_manager',
            'department' => 'Marketing',
            'title' => 'Trưởng Phòng Marketing',
            'phone' => '**********',
        ]);
        $manager2->assignRole('hiring_manager');
        $users['manager2'] = $manager2;

        // Interviewer users
        $interviewer1 = User::create([
            'name' => 'Võ Minh Tuấn',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'interviewer',
            'department' => 'Công Nghệ Thông Tin',
            'title' => 'Senior Software Engineer',
            'phone' => '**********',
        ]);
        $interviewer1->assignRole('interviewer');
        $users['interviewer1'] = $interviewer1;

        $interviewer2 = User::create([
            'name' => 'Đặng Thị Mai',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'interviewer',
            'department' => 'Marketing',
            'title' => 'Marketing Manager',
            'phone' => '0967890123',
        ]);
        $interviewer2->assignRole('interviewer');
        $users['interviewer2'] = $interviewer2;

        return $users;
    }

    private function createJobPostings($users)
    {
        $this->command->info('💼 Tạo job postings...');

        $jobs = [];

        $jobsData = [
            [
                'title' => 'Lập Trình Viên PHP Senior',
                'department' => 'Công Nghệ Thông Tin',
                'location' => 'Thành phố Hồ Chí Minh',
                'type' => 'full-time',
                'work_location' => 'hybrid',
                'salary_min' => ********,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Chúng tôi đang tìm kiếm một Lập trình viên PHP Senior có kinh nghiệm để tham gia vào đội ngũ phát triển sản phẩm. Ứng viên sẽ chịu trách nhiệm phát triển và duy trì các ứng dụng web sử dụng PHP và Laravel framework, đồng thời hướng dẫn các developer junior.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Công nghệ thông tin, Khoa học máy tính hoặc tương đương',
                'company_culture' => 'Môi trường làm việc năng động, sáng tạo với nhiều cơ hội phát triển nghề nghiệp. Văn hóa học hỏi và chia sẻ kiến thức.',
                'experience_level' => 'senior',
                'positions' => 2,
                'status' => 'active',
                'priority' => 'high',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter1',
            ],
            [
                'title' => 'Chuyên Viên Marketing Digital',
                'department' => 'Marketing',
                'location' => 'Hà Nội',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 15000000,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Vị trí này phù hợp với ứng viên có đam mê với marketing số và mong muốn phát triển thương hiệu trực tuyến. Bạn sẽ làm việc với các kênh digital như Facebook, Google Ads, SEO/SEM và phân tích dữ liệu để tối ưu hóa chiến dịch.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Marketing, Kinh tế, Truyền thông hoặc tương đương',
                'company_culture' => 'Văn hóa doanh nghiệp trẻ trung, khuyến khích sáng tạo và học hỏi. Môi trường làm việc linh hoạt và hỗ trợ phát triển cá nhân.',
                'experience_level' => 'mid',
                'positions' => 1,
                'status' => 'active',
                'priority' => 'medium',
                'hiring_manager' => 'manager2',
                'recruiter' => 'recruiter1',
            ],
            [
                'title' => 'Kế Toán Trưởng',
                'department' => 'Tài Chính',
                'location' => 'Đà Nẵng',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 20000000,
                'salary_max' => 35000000,
                'currency' => 'VND',
                'description' => 'Chịu trách nhiệm quản lý toàn bộ hoạt động kế toán của công ty, lập báo cáo tài chính, quản lý ngân sách và đảm bảo tuân thủ các quy định pháp luật về tài chính. Vị trí này đòi hỏi kinh nghiệm quản lý và kiến thức sâu về kế toán.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Kế toán, Tài chính hoặc tương đương. Có chứng chỉ kế toán trưởng là lợi thế.',
                'company_culture' => 'Môi trường làm việc chuyên nghiệp, ổn định với chế độ đãi ngộ hấp dẫn và cơ hội phát triển nghề nghiệp lâu dài.',
                'experience_level' => 'senior',
                'positions' => 1,
                'status' => 'active',
                'priority' => 'high',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter2',
            ],
            [
                'title' => 'Nhân Viên Kinh Doanh B2B',
                'department' => 'Kinh Doanh',
                'location' => 'Thành phố Hồ Chí Minh',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 12000000,
                'salary_max' => 20000000,
                'currency' => 'VND',
                'description' => 'Phát triển và duy trì mối quan hệ với khách hàng doanh nghiệp, tìm kiếm cơ hội kinh doanh mới, đàm phán hợp đồng và đạt chỉ tiêu doanh số. Công việc đòi hỏi kỹ năng giao tiếp tốt và khả năng xây dựng mối quan hệ.',
                'education_required' => 'Tốt nghiệp Đại học các chuyên ngành Kinh tế, Quản trị kinh doanh hoặc tương đương',
                'company_culture' => 'Môi trường cạnh tranh lành mạnh với hoa hồng hấp dẫn và nhiều cơ hội thăng tiến.',
                'experience_level' => 'mid',
                'positions' => 3,
                'status' => 'active',
                'priority' => 'medium',
                'hiring_manager' => 'manager2',
                'recruiter' => 'recruiter1',
            ],
            [
                'title' => 'Thiết Kế Đồ Họa (Graphic Designer)',
                'department' => 'Sáng Tạo',
                'location' => 'Remote',
                'type' => 'full-time',
                'work_location' => 'remote',
                'salary_min' => 10000000,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Thiết kế các tài liệu marketing, branding, website và các sản phẩm truyền thông khác. Làm việc chặt chẽ với đội marketing để tạo ra những sản phẩm sáng tạo và hiệu quả.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Mỹ thuật, Thiết kế đồ họa, Truyền thông đa phương tiện hoặc tương đương',
                'company_culture' => 'Môi trường sáng tạo, linh hoạt với nhiều dự án thú vị và cơ hội phát triển kỹ năng.',
                'experience_level' => 'mid',
                'positions' => 2,
                'status' => 'active',
                'priority' => 'medium',
                'hiring_manager' => 'manager2',
                'recruiter' => 'recruiter2',
            ],
            [
                'title' => 'Chuyên Viên Nhân Sự (HR Specialist)',
                'department' => 'Nhân Sự',
                'location' => 'Thành phố Hồ Chí Minh',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 12000000,
                'salary_max' => 20000000,
                'currency' => 'VND',
                'description' => 'Chịu trách nhiệm tuyển dụng, đào tạo và phát triển nhân sự. Xây dựng quy trình HR, chính sách nhân sự và văn hóa doanh nghiệp. Hỗ trợ các hoạt động quản lý nhân sự hàng ngày.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Quản trị nhân lực, Tâm lý học, Luật hoặc tương đương',
                'company_culture' => 'Môi trường làm việc nhân văn, tôn trọng con người và phát triển bền vững.',
                'experience_level' => 'mid',
                'positions' => 1,
                'status' => 'active',
                'priority' => 'medium',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter1',
            ],
            [
                'title' => 'Lập Trình Viên Mobile (React Native)',
                'department' => 'Công Nghệ Thông Tin',
                'location' => 'Hà Nội',
                'type' => 'full-time',
                'work_location' => 'hybrid',
                'salary_min' => 20000000,
                'salary_max' => 35000000,
                'currency' => 'VND',
                'description' => 'Phát triển ứng dụng mobile đa nền tảng sử dụng React Native. Tối ưu hóa hiệu suất ứng dụng, tích hợp API và đảm bảo trải nghiệm người dùng tốt nhất.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Công nghệ thông tin hoặc tương đương',
                'company_culture' => 'Văn hóa công nghệ tiên tiến, khuyến khích innovation và continuous learning.',
                'experience_level' => 'mid',
                'positions' => 1,
                'status' => 'active',
                'priority' => 'high',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter2',
            ],
            [
                'title' => 'Chuyên Viên Phân Tích Dữ Liệu (Data Analyst)',
                'department' => 'Phân Tích Dữ Liệu',
                'location' => 'Remote',
                'type' => 'full-time',
                'work_location' => 'remote',
                'salary_min' => ********,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Phân tích dữ liệu kinh doanh, tạo báo cáo và dashboard để hỗ trợ ra quyết định. Làm việc với big data và các công cụ phân tích hiện đại.',
                'education_required' => 'Tốt nghiệp Đại học chuyên ngành Toán, Thống kê, Kinh tế, Công nghệ thông tin hoặc tương đương',
                'company_culture' => 'Môi trường data-driven, khuyến khích tư duy phân tích và sáng tạo.',
                'experience_level' => 'mid',
                'positions' => 2,
                'status' => 'active',
                'priority' => 'high',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter1',
            ],
        ];

        foreach ($jobsData as $index => $jobData) {
            $hiringManager = $users[$jobData['hiring_manager']];
            $recruiter = $users[$jobData['recruiter']];

            unset($jobData['hiring_manager'], $jobData['recruiter']);

            $job = JobPosting::create([
                ...$jobData,
                'hiring_manager_id' => $hiringManager->id,
                'recruiter_id' => $recruiter->id,
                'created_by' => $users['admin']->id,
                'posted_date' => now()->subDays(rand(1, 10)),
                'closing_date' => now()->addDays(rand(20, 40)),
            ]);

            // Tạo job requirements, responsibilities, benefits, skills
            $this->createJobDetails($job, $index);

            $jobs[] = $job;
        }

        return $jobs;
    }

    private function createJobDetails($job, $index)
    {
        // Job Requirements - simplified to just arrays of strings
        $requirements = [
            // PHP Senior
            0 => [
                'Có ít nhất 5 năm kinh nghiệm lập trình PHP',
                'Thành thạo Laravel Framework và các design patterns',
                'Kinh nghiệm với MySQL, PostgreSQL và Redis',
                'Hiểu biết về Git, Docker và CI/CD',
                'Kỹ năng giao tiếp tiếng Anh tốt',
            ],
            // Marketing Digital
            1 => [
                'Có ít nhất 2 năm kinh nghiệm Digital Marketing',
                'Thành thạo Facebook Ads, Google Ads và Google Analytics',
                'Hiểu biết về SEO/SEM và Content Marketing',
                'Kỹ năng phân tích dữ liệu và báo cáo',
            ],
            // Kế toán trưởng
            2 => [
                'Có ít nhất 7 năm kinh nghiệm kế toán',
                'Có chứng chỉ kế toán trưởng hoặc CPA',
                'Thành thạo phần mềm kế toán MISA, FAST, SAP',
                'Hiểu biết sâu về pháp luật thuế Việt Nam',
            ],
            // Sales B2B
            3 => [
                'Có ít nhất 2 năm kinh nghiệm bán hàng B2B',
                'Kỹ năng đàm phán và thuyết phục xuất sắc',
                'Khả năng làm việc dưới áp lực cao',
                'Có mạng lưới khách hàng sẵn có',
            ],
            // Graphic Designer
            4 => [
                'Có ít nhất 2 năm kinh nghiệm thiết kế đồ họa',
                'Thành thạo Adobe Creative Suite (Photoshop, Illustrator, InDesign)',
                'Có khiếu thẩm mỹ và tư duy sáng tạo',
                'Hiểu biết về UI/UX Design và Figma',
            ],
            // HR Specialist
            5 => [
                'Có ít nhất 2 năm kinh nghiệm HR',
                'Hiểu biết về luật lao động Việt Nam',
                'Kỹ năng giao tiếp và thuyết trình tốt',
                'Thành thạo MS Office và các công cụ HR',
            ],
            // React Native
            6 => [
                'Có ít nhất 2 năm kinh nghiệm React Native',
                'Thành thạo JavaScript/TypeScript và React',
                'Hiểu biết về iOS và Android platform',
                'Kinh nghiệm với Redux, MobX và native modules',
            ],
            // Data Analyst
            7 => [
                'Có ít nhất 1 năm kinh nghiệm phân tích dữ liệu',
                'Thành thạo SQL và Excel nâng cao',
                'Hiểu biết về Python hoặc R cho data science',
                'Kinh nghiệm với Tableau, Power BI hoặc các công cụ BI',
            ],
            // Trưởng phong kinh doanh
            8 => [
                'Có ít nhất 5 năm kinh nghiệm kinh doanh, trong đó có 2 năm ở vị trí quản lý',
                'Khả năng xây dựng và quản lý đội nhóm kinh doanh hiệu quả',
                'Kỹ năng đàm phán, thương lượng và đóng cửa deal lớn',
                'Hiểu biết sâu về thị trường và đối thủ cạnh tranh',
                'Kỹ năng phân tích dữ liệu kinh doanh và dự báo',
            ],
            // QA/QC
            9 => [
                'Có ít nhất 2 năm kinh nghiệm QA/QC trong lĩnh vực phần mềm',
                'Kinh nghiệm viết test case và thực hiện test automation',
                'Hiểu biết về quy trình phát triển phần mềm Agile/Scrum',
                'Kinh nghiệm với các công cụ kiểm thử như Selenium, JUnit, TestNG',
            ],
        ];

        if (isset($requirements[$index])) {
            $job->requirements = $requirements[$index];
            $job->save();
        }

        // Job Responsibilities - simplified to just arrays of strings
        $responsibilities = [
            0 => [ // PHP Senior
                'Phát triển và duy trì các ứng dụng web sử dụng PHP/Laravel',
                'Thiết kế cơ sở dữ liệu và tối ưu hóa hiệu suất hệ thống',
                'Code review và hướng dẫn junior developers',
                'Tham gia vào việc lập kế hoạch và ước lượng dự án',
                'Nghiên cứu và áp dụng công nghệ mới',
            ],
            1 => [ // Marketing Digital
                'Lập kế hoạch và triển khai chiến dịch marketing online',
                'Quản lý và tối ưu hóa quảng cáo Facebook, Google Ads',
                'Phân tích dữ liệu và báo cáo hiệu quả chiến dịch',
                'Tạo nội dung marketing sáng tạo và hấp dẫn',
            ],
            2 => [ // Kế toán trưởng
                'Quản lý và giám sát toàn bộ hoạt động kế toán',
                'Lập báo cáo tài chính định kỳ và báo cáo thuế',
                'Quản lý ngân sách và dự báo tài chính',
                'Đảm bảo tuân thủ các quy định pháp luật',
            ],
            3 => [ // Sales B2B
                'Tìm kiếm và phát triển khách hàng doanh nghiệp mới',
                'Duy trì và phát triển mối quan hệ với khách hàng hiện tại',
                'Đàm phán hợp đồng và chốt deal',
                'Báo cáo kết quả kinh doanh và phân tích thị trường',
            ],
            4 => [ // Graphic Designer
                'Thiết kế các tài liệu marketing và branding',
                'Tạo concept và ý tưởng sáng tạo cho các dự án',
                'Phối hợp với team marketing trong các campaign',
                'Đảm bảo chất lượng và thống nhất thương hiệu',
            ],
            5 => [ // HR Specialist
                'Tuyển dụng và sàng lọc ứng viên',
                'Xây dựng quy trình HR và chính sách nhân sự',
                'Đào tạo và phát triển nhân viên',
                'Quản lý hồ sơ nhân sự và chấm công',
            ],
            6 => [ // React Native
                'Phát triển ứng dụng mobile đa nền tảng',
                'Tối ưu hóa hiệu suất và trải nghiệm người dùng',
                'Tích hợp API và third-party services',
                'Testing, debugging và deployment',
            ],
            7 => [ // Data Analyst
                'Thu thập, làm sạch và phân tích dữ liệu kinh doanh',
                'Tạo báo cáo và dashboard trực quan',
                'Phân tích xu hướng và đưa ra insights',
                'Hỗ trợ ra quyết định kinh doanh dựa trên dữ liệu',
            ],
            8 => [ // Trưởng phòng kinh doanh
                'Xây dựng và triển khai chiến lược kinh doanh',
                'Quản lý và phát triển đội nhóm sales',
                'Thiết lập mục tiêu và KPI cho team',
                'Phân tích thị trường và đối thủ cạnh tranh',
                'Đàm phán các hợp đồng lớn và quan trọng',
            ],
            9 => [ // QA/QC
                'Thiết kế và thực hiện test cases cho sản phẩm',
                'Thực hiện manual testing và automation testing',
                'Báo cáo bugs và theo dõi quá trình fix',
                'Đảm bảo chất lượng sản phẩm trước khi release',
            ],
        ];

        if (isset($responsibilities[$index])) {
            $job->responsibilities = $responsibilities[$index];
            $job->save();
        }

        // Job Benefits (chung cho tất cả) - simplified to just array of strings
        $benefits = [
            'Lương thưởng cạnh tranh, xét tăng lương định kỳ 6 tháng',
            'Bảo hiểm sức khỏe cao cấp cho nhân viên và gia đình',
            'Thưởng tháng 13, thưởng hiệu suất và thưởng dự án',
            'Nghỉ phép năm 12 ngày + các ngày lễ tết theo quy định',
            'Cơ hội đào tạo và phát triển nghề nghiệp',
            'Môi trường làm việc trẻ trung, năng động và thân thiện',
            'Team building, du lịch công ty hàng năm',
            'Hỗ trợ laptop, thiết bị làm việc hiện đại',
        ];

        $job->benefits = $benefits;
        $job->save();

        // Job Skills - simplified to just arrays of strings
        $skills = [
            0 => [ // PHP Senior
                'PHP',
                'Laravel',
                'MySQL',
                'JavaScript',
                'Vue.js',
                'Docker'
            ],
            1 => [ // Marketing Digital
                'SEO',
                'Content Marketing'
            ],
            2 => [ // Kế toán trưởng
                'MISA',
                'Excel',
                'Luật Thuế',
                'Báo cáo tài chính',
                'SAP'
            ],
            3 => [ // Sales B2B
                'Bán hàng B2B',
                'Đàm phán',
                'CRM',
                'Tiếng Anh'
            ],
            4 => [ // Graphic Designer
                'Adobe Photoshop',
                'Adobe Illustrator',
                'Adobe InDesign',
                'Figma'
            ],
            5 => [ // HR Specialist
                'Tuyển dụng',
                'Luật lao động',
                'MS Office',
                'Giao tiếp'
            ],
            6 => [ // React Native
                'React Native',
                'JavaScript',
                'TypeScript',
                'Redux'
            ],
            7 => [ // Data Analyst
                'SQL',
                'Excel',
                'Python',
                'Tableau'
            ],
            8 => [ // Trưởng phòng kinh doanh
                'Quản lý đội nhóm',
                'Xây dựng chiến lược',
                'Đàm phán thương mại',
                'Phân tích thị trường'
            ],
            9 => [ // QA/QC
                'Manual Testing',
                'Test Case Design',
                'Selenium',
                'JIRA'
            ],
        ];

        if (isset($skills[$index])) {
            $job->skills = $skills[$index];
            $job->save();
        }
    }

    private function createCandidates($users, $jobs)
    {
        $this->command->info('👥 Tạo candidates...');

        $candidates = [];

        $candidatesData = [
            // Ứng viên cho PHP Senior
            [
                'name' => 'Nguyễn Văn Minh',
                'email' => '<EMAIL>',
                'phone' => '0901234567',
                'position' => 'Senior PHP Developer',
                'experience' => '5-10 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(5),
                'source' => 'LinkedIn',
                'location' => 'Quận 1, TP.HCM',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => 42000000,
                'rating' => 4.5,
                'ai_score' => 88,
                'linkedin_url' => 'https://linkedin.com/in/minh-nguyen-dev',
                'github_url' => 'https://github.com/minhdev',
                'notes' => 'Ứng viên có kinh nghiệm tốt với Laravel, đã làm việc tại nhiều công ty công nghệ lớn. Kỹ năng technical và leadership xuất sắc.',
                'job_index' => 0,
            ],
            [
                'name' => 'Trần Thị Hương',
                'email' => '<EMAIL>',
                'phone' => '0912345678',
                'position' => 'PHP Developer',
                'experience' => '3-5 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(8),
                'source' => 'TopCV',
                'location' => 'Quận 7, TP.HCM',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => 35000000,
                'rating' => 4.2,
                'ai_score' => 82,
                'notes' => 'Có kinh nghiệm với PHP và Laravel, kỹ năng giao tiếp tốt. Cần đánh giá thêm về khả năng leadership.',
                'job_index' => 0,
            ],
            [
                'name' => 'Lý Văn Hùng',
                'email' => '<EMAIL>',
                'phone' => '0989012345',
                'position' => 'Full Stack Developer',
                'experience' => '3-5 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(2),
                'source' => 'ITviec',
                'location' => 'Quận Bình Thạnh, TP.HCM',
                'salary_expectation_min' => 22000000,
                'salary_expectation_max' => 32000000,
                'rating' => 4.0,
                'ai_score' => 79,
                'notes' => 'Có kinh nghiệm fullstack, từng làm việc tại startup. Cần đánh giá kỹ năng PHP backend.',
                'job_index' => 0,
            ],

            // Ứng viên cho Marketing Digital
            [
                'name' => 'Lê Minh Tuấn',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'Digital Marketing Manager',
                'experience' => '3-5 years',
                'status' => 'offer',
                'applied_date' => now()->subDays(12),
                'source' => 'VietnamWorks',
                'location' => 'Ba Đình, Hà Nội',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 4.7,
                'ai_score' => 91,
                'notes' => 'Có kinh nghiệm quản lý ngân sách quảng cáo lớn, hiểu biết sâu về Facebook Ads và Google Ads. Ứng viên xuất sắc.',
                'job_index' => 1,
            ],
            [
                'name' => 'Phạm Thị Mai',
                'email' => '<EMAIL>',
                'phone' => '0934567890',
                'position' => 'Marketing Executive',
                'experience' => '1-3 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(3),
                'source' => 'Referral',
                'location' => 'Cầu Giấy, Hà Nội',
                'salary_expectation_min' => 15000000,
                'salary_expectation_max' => 22000000,
                'rating' => 3.8,
                'ai_score' => 75,
                'notes' => 'Ứng viên trẻ, nhiệt huyết, có kiến thức cơ bản về digital marketing. Cần đào tạo thêm.',
                'job_index' => 1,
            ],
            [
                'name' => 'Trịnh Minh Đức',
                'email' => '<EMAIL>',
                'phone' => '0901234567',
                'position' => 'Performance Marketing Specialist',
                'experience' => '5-10 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(6),
                'source' => 'LinkedIn',
                'location' => 'Hoàn Kiếm, Hà Nội',
                'salary_expectation_min' => 20000000,
                'salary_expectation_max' => ********,
                'rating' => 4.6,
                'ai_score' => 87,
                'notes' => 'Chuyên gia performance marketing, có kinh nghiệm quản lý ngân sách lớn và tối ưu ROI.',
                'job_index' => 1,
            ],

            // Ứng viên cho Kế toán trưởng
            [
                'name' => 'Hoàng Văn Đức',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'Chief Accountant',
                'experience' => '10+ years',
                'status' => 'interview',
                'applied_date' => now()->subDays(9),
                'source' => 'CareerBuilder',
                'location' => 'Hải Châu, Đà Nẵng',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 4.8,
                'ai_score' => 94,
                'notes' => 'Có chứng chỉ kế toán trưởng, kinh nghiệm quản lý tại công ty niêm yết. Ứng viên rất phù hợp.',
                'job_index' => 2,
            ],
            [
                'name' => 'Phan Văn Thành',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'Senior Accountant',
                'experience' => '5-10 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(4),
                'source' => 'VietnamWorks',
                'location' => 'Sơn Trà, Đà Nẵng',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 4.1,
                'ai_score' => 81,
                'notes' => 'Có kinh nghiệm kế toán tại công ty sản xuất, hiểu biết về thuế và báo cáo tài chính.',
                'job_index' => 2,
            ],

            // Ứng viên cho Sales B2B
            [
                'name' => 'Vũ Thị Lan',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'B2B Sales Manager',
                'experience' => '3-5 years',
                'status' => 'hired',
                'applied_date' => now()->subDays(20),
                'source' => 'Indeed',
                'location' => 'Quận 3, TP.HCM',
                'salary_expectation_min' => 15000000,
                'salary_expectation_max' => ********,
                'rating' => 4.6,
                'ai_score' => 89,
                'notes' => 'Có mạng lưới khách hàng rộng, kỹ năng đàm phán xuất sắc. Đã được tuyển dụng.',
                'job_index' => 3,
            ],
            [
                'name' => 'Đặng Minh Khoa',
                'email' => '<EMAIL>',
                'phone' => '0967890123',
                'position' => 'Business Development Executive',
                'experience' => '1-3 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(7),
                'source' => 'Company Website',
                'location' => 'Quận 1, TP.HCM',
                'salary_expectation_min' => 12000000,
                'salary_expectation_max' => ********,
                'rating' => 3.9,
                'ai_score' => 78,
                'notes' => 'Ứng viên năng động, có tinh thần học hỏi cao. Cần đánh giá kỹ năng sales.',
                'job_index' => 3,
            ],

            // Ứng viên cho Graphic Designer
            [
                'name' => 'Bùi Thị Thảo',
                'email' => '<EMAIL>',
                'phone' => '0978901234',
                'position' => 'Senior Graphic Designer',
                'experience' => '3-5 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(10),
                'source' => 'Behance',
                'location' => 'Remote',
                'salary_expectation_min' => 12000000,
                'salary_expectation_max' => 20000000,
                'rating' => 4.4,
                'ai_score' => 86,
                'portfolio_url' => 'https://behance.net/thaobui',
                'notes' => 'Portfolio ấn tượng, có kinh nghiệm làm việc với các thương hiệu lớn. Kỹ năng design xuất sắc.',
                'job_index' => 4,
            ],
            [
                'name' => 'Huỳnh Thị Tuyết',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'UI/UX Designer',
                'experience' => '3-5 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(8),
                'source' => 'Dribbble',
                'location' => 'Remote',
                'salary_expectation_min' => 15000000,
                'salary_expectation_max' => 22000000,
                'rating' => 4.3,
                'ai_score' => 84,
                'portfolio_url' => 'https://dribbble.com/tuyetdesign',
                'notes' => 'Có kinh nghiệm UI/UX, portfolio đẹp và chuyên nghiệp. Phù hợp cho cả graphic design.',
                'job_index' => 4,
            ],

            // Ứng viên cho HR Specialist
            [
                'name' => 'Ngô Thị Linh',
                'email' => '<EMAIL>',
                'phone' => '0990123456',
                'position' => 'HR Specialist',
                'experience' => '1-3 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(3),
                'source' => 'TopDev',
                'location' => 'Quận 2, TP.HCM',
                'salary_expectation_min' => 13000000,
                'salary_expectation_max' => ********,
                'rating' => 3.7,
                'ai_score' => 76,
                'notes' => 'Có kiến thức cơ bản về HR, kỹ năng giao tiếp tốt. Cần đào tạo thêm về luật lao động.',
                'job_index' => 5,
            ],

            // Ứng viên cho React Native
            [
                'name' => 'Cao Văn Đạt',
                'email' => '<EMAIL>',
                'phone' => '0967890123',
                'position' => 'React Native Developer',
                'experience' => '1-3 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(5),
                'source' => 'GitHub',
                'location' => 'Quận 1, TP.HCM',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 3.9,
                'ai_score' => 80,
                'github_url' => 'https://github.com/datmobile',
                'notes' => 'Có kinh nghiệm React Native, portfolio mobile app ấn tượng. Cần đánh giá kỹ năng advanced.',
                'job_index' => 6,
            ],

            // Ứng viên cho Data Analyst
            [
                'name' => 'Đinh Thị Nga',
                'email' => '<EMAIL>',
                'phone' => '0934567890',
                'position' => 'Data Analyst',
                'experience' => '1-3 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(4),
                'source' => 'Kaggle',
                'location' => 'Remote',
                'salary_expectation_min' => 16000000,
                'salary_expectation_max' => 24000000,
                'rating' => 4.0,
                'ai_score' => 83,
                'notes' => 'Có kinh nghiệm với SQL và Python, tham gia nhiều competition trên Kaggle. Tiềm năng tốt.',
                'job_index' => 7,
            ],
        ];

        foreach ($candidatesData as $candidateData) {
            $jobIndex = $candidateData['job_index'];
            $job = $jobs[$jobIndex];

            unset($candidateData['job_index']);

            $candidate = Candidate::create([
                ...$candidateData,
                'job_posting_id' => $job->id,
                'created_by' => $users['recruiter1']->id,
                'assigned_to' => $users['recruiter1']->id,
                'salary_currency' => 'VND',
            ]);

            // Tạo education, work history, skills, tags
            $this->createCandidateDetails($candidate);

            // Tạo status history
            $candidate->statusHistory()->create([
                'old_status' => null,
                'new_status' => $candidate->status,
                'changed_by' => $users['recruiter1']->id,
                'notes' => 'Hồ sơ ứng viên được tạo và phân loại ban đầu',
            ]);

            $candidates[] = $candidate;
        }

        return $candidates;
    }

    private function createCandidateDetails($candidate)
    {
        // Education data - simplified to text format
        $educationData = [
            'Nguyễn Văn Minh' => 'Cử nhân Công nghệ thông tin tại Đại học Bách Khoa TP.HCM (2015-2019), GPA: 3.6',
            'Trần Thị Hương' => 'Cử nhân Khoa học máy tính tại Đại học Khoa học Tự nhiên TP.HCM (2016-2020), GPA: 3.4',
            'Lý Văn Hùng' => 'Cử nhân Kỹ thuật phần mềm tại Đại học Công nghệ TP.HCM (2017-2021), GPA: 3.5',
            'Lê Minh Tuấn' => 'Cử nhân Marketing tại Đại học Ngoại thương (2017-2021), GPA: 3.7',
            'Phạm Thị Mai' => 'Cử nhân Kế toán tại Đại học Kinh tế TP.HCM (2014-2018), GPA: 3.8',
            'Hoàng Văn Đức' => 'Cử nhân Kế toán - Kiểm toán tại Đại học Kinh tế Quốc dân (2008-2012), GPA: 3.8',
            'Vũ Thị Lan' => 'Cử nhân Kinh doanh quốc tế tại Đại học Ngoại thương (2016-2020), GPA: 3.6',
            'Bùi Thị Thảo' => 'Cử nhân Mỹ thuật ứng dụng tại Đại học Mỹ thuật Công nghiệp (2015-2019), GPA: 3.7',
            'Huỳnh Thị Tuyết' => 'Cử nhân Thiết kế nội thất tại Đại học Kiến trúc TP.HCM (2016-2020), GPA: 3.8',
            'Ngô Thị Linh' => 'Cử nhân Tâm lý học tại Đại học Khoa học Xã hội và Nhân văn (2018-2022), GPA: 3.4',
            'Cao Văn Đạt' => 'Cử nhân Công nghệ thông tin tại Đại học FPT (2019-2023), GPA: 3.5',
            'Đinh Thị Nga' => 'Cử nhân Thống kê tại Đại học Kinh tế Quốc dân (2018-2022), GPA: 3.6',
            'Lý Văn Hùng' => [
                [
                    'institution' => 'Đại học Công nghệ TP.HCM',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Kỹ thuật phần mềm',
                    'start_year' => 2017,
                    'end_year' => 2021,
                    'gpa' => '3.5',
                    'sort_order' => 1,
                ],
            ],
            'Lê Minh Tuấn' => [
                [
                    'institution' => 'Đại học Ngoại thương',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Marketing',
                    'start_year' => 2017,
                    'end_year' => 2021,
                    'gpa' => '3.7',
                    'sort_order' => 1,
                ],
            ],
            'Phạm Thị Mai' => [
                [
                    'institution' => 'Đại học Kinh tế Quốc dân',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Quản trị kinh doanh',
                    'start_year' => 2019,
                    'end_year' => 2023,
                    'gpa' => '3.5',
                    'sort_order' => 1,
                ],
            ],
            'Trịnh Minh Đức' => [
                [
                    'institution' => 'Đại học Kinh tế TP.HCM',
                    'degree' => 'Thạc sĩ',
                    'field_of_study' => 'Marketing',
                    'start_year' => 2015,
                    'end_year' => 2019,
                    'gpa' => '3.8',
                    'sort_order' => 1,
                ],
            ],
            'Hoàng Văn Đức' => [
                [
                    'institution' => 'Đại học Kinh tế TP.HCM',
                    'degree' => 'Thạc sĩ',
                    'field_of_study' => 'Kế toán - Kiểm toán',
                    'start_year' => 2008,
                    'end_year' => 2012,
                    'gpa' => '3.8',
                    'sort_order' => 1,
                ],
            ],
            'Phan Văn Thành' => [
                [
                    'institution' => 'Đại học Kinh tế Đà Nẵng',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Kế toán',
                    'start_year' => 2014,
                    'end_year' => 2018,
                    'gpa' => '3.6',
                    'sort_order' => 1,
                ],
            ],
            'Vũ Thị Lan' => [
                [
                    'institution' => 'Đại học Ngoại thương',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Kinh doanh quốc tế',
                    'start_year' => 2016,
                    'end_year' => 2020,
                    'gpa' => '3.6',
                    'sort_order' => 1,
                ],
            ],
            'Đặng Minh Khoa' => [
                [
                    'institution' => 'Đại học Kinh tế TP.HCM',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Marketing',
                    'start_year' => 2020,
                    'end_year' => 2024,
                    'gpa' => '3.3',
                    'sort_order' => 1,
                ],
            ],
            'Bùi Thị Thảo' => [
                [
                    'institution' => 'Đại học Mỹ thuật Công nghiệp',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Thiết kế đồ họa',
                    'start_year' => 2017,
                    'end_year' => 2021,
                    'gpa' => '3.7',
                    'sort_order' => 1,
                ],
            ],
            'Huỳnh Thị Tuyết' => [
                [
                    'institution' => 'Đại học Kiến trúc TP.HCM',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Thiết kế nội thất',
                    'start_year' => 2016,
                    'end_year' => 2020,
                    'gpa' => '3.8',
                    'sort_order' => 1,
                ],
            ],
            'Ngô Thị Linh' => [
                [
                    'institution' => 'Đại học Khoa học Xã hội và Nhân văn',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Tâm lý học',
                    'start_year' => 2018,
                    'end_year' => 2022,
                    'gpa' => '3.4',
                    'sort_order' => 1,
                ],
            ],
            'Cao Văn Đạt' => [
                [
                    'institution' => 'Đại học FPT',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Công nghệ thông tin',
                    'start_year' => 2019,
                    'end_year' => 2023,
                    'gpa' => '3.5',
                    'sort_order' => 1,
                ],
            ],
            'Đinh Thị Nga' => [
                [
                    'institution' => 'Đại học Kinh tế Quốc dân',
                    'degree' => 'Cử nhân',
                    'field_of_study' => 'Thống kê',
                    'start_year' => 2018,
                    'end_year' => 2022,
                    'gpa' => '3.6',
                    'sort_order' => 1,
                ],
            ],
        ];

        if (isset($educationData[$candidate->name])) {
            $candidate->education = $educationData[$candidate->name];
            $candidate->save();
        }

        // Work History - simplified to text format
        $workHistoryData = [
            'Nguyễn Văn Minh' => 'PHP Developer tại FPT Software (2019-2021) - Phát triển ứng dụng web cho khách hàng Nhật Bản sử dụng Laravel framework; Senior PHP Developer tại Tiki Corporation (2022-hiện tại) - Phát triển và tối ưu hóa hệ thống e-commerce, quản lý team 3 developers',
            'Trần Thị Hương' => 'Junior PHP Developer tại Startup ABC (2020-2022) - Phát triển website bán hàng online; PHP Developer tại Công ty DEF (2022-hiện tại) - Phát triển hệ thống quản lý nội bộ',
            'Lý Văn Hùng' => 'Frontend Developer tại Công ty GHI (2021-2023) - Phát triển giao diện web responsive; Senior Frontend Developer tại Công ty JKL (2023-hiện tại) - Lead team frontend 4 người',
            'Lê Minh Tuấn' => 'Marketing Executive tại Công ty MNO (2021-2023) - Chạy quảng cáo Facebook và Google Ads; Digital Marketing Specialist tại Công ty PQR (2023-hiện tại) - Quản lý toàn bộ hoạt động marketing online',
            'Phạm Thị Mai' => 'Kế toán viên tại Công ty STU (2018-2021) - Xử lý hóa đơn và báo cáo tài chính; Kế toán trưởng tại Công ty VWX (2021-hiện tại) - Quản lý toàn bộ hoạt động kế toán',
            'Hoàng Văn Đức' => 'Kế toán viên tại Công ty YZ1 (2012-2016) - Kế toán tổng hợp; Kế toán trưởng tại Công ty ABC2 (2016-hiện tại) - Quản lý tài chính và thuế',
            'Vũ Thị Lan' => 'Sales Executive tại Công ty DEF3 (2020-2022) - Bán hàng B2B; Senior Sales tại Công ty GHI4 (2022-hiện tại) - Quản lý khách hàng lớn',
            'Bùi Thị Thảo' => 'Graphic Designer tại Agency JKL5 (2019-2021) - Thiết kế branding và marketing materials; Senior Designer tại Công ty MNO6 (2021-hiện tại) - Lead design team',
            'Huỳnh Thị Tuyết' => 'UI/UX Designer tại Startup PQR7 (2020-2022) - Thiết kế app mobile; Senior UI/UX Designer tại Công ty STU8 (2022-hiện tại) - Thiết kế hệ thống design system',
            'Ngô Thị Linh' => 'HR Assistant tại Công ty VWX9 (2022-2023) - Hỗ trợ tuyển dụng; HR Specialist tại Công ty YZ10 (2023-hiện tại) - Quản lý toàn bộ quy trình HR',
            'Cao Văn Đạt' => 'React Native Developer tại Startup ABC11 (2023-hiện tại) - Phát triển app mobile đa nền tảng',
            'Đinh Thị Nga' => 'Data Analyst tại Công ty DEF12 (2022-hiện tại) - Phân tích dữ liệu kinh doanh và tạo báo cáo',
            'Trần Thị Hương' => [
                [
                    'company' => 'Viettel Software',
                    'position' => 'Junior PHP Developer',
                    'start_date' => '2020-08-01',
                    'end_date' => '2023-06-30',
                    'is_current' => false,
                    'description' => 'Phát triển các module cho hệ thống quản lý nội bộ',
                    'sort_order' => 1,
                ],
            ],
            'Lý Văn Hùng' => [
                [
                    'company' => 'Startup ABC',
                    'position' => 'Full Stack Developer',
                    'start_date' => '2021-06-01',
                    'end_date' => null,
                    'is_current' => true,
                    'description' => 'Phát triển ứng dụng web và mobile cho startup fintech',
                    'sort_order' => 1,
                ],
            ],
            'Lê Minh Tuấn' => [
                [
                    'company' => 'VNG Corporation',
                    'position' => 'Digital Marketing Executive',
                    'start_date' => '2021-06-01',
                    'end_date' => '2023-12-31',
                    'is_current' => false,
                    'description' => 'Quản lý chiến dịch quảng cáo cho các sản phẩm game, ngân sách 2 tỷ/tháng',
                    'sort_order' => 1,
                ],
            ],
            'Phạm Thị Mai' => [
                [
                    'company' => 'Sendo',
                    'position' => 'Marketing Intern',
                    'start_date' => '2023-01-01',
                    'end_date' => '2023-12-31',
                    'is_current' => false,
                    'description' => 'Hỗ trợ team marketing trong việc tạo content và chạy quảng cáo',
                    'sort_order' => 1,
                ],
            ],
        ];

        if (isset($workHistoryData[$candidate->name])) {
            $candidate->work_history = $workHistoryData[$candidate->name];
            $candidate->save();
        }

        // Skills - simplified to just arrays of strings
        $skillsData = [
            'Nguyễn Văn Minh' => ['PHP', 'Laravel', 'MySQL', 'JavaScript', 'Vue.js'],
            'Trần Thị Hương' => ['PHP', 'Laravel', 'MySQL', 'HTML/CSS'],
            'Lê Minh Tuấn' => ['Facebook Ads', 'Google Ads', 'Google Analytics', 'SEO'],
            'Hoàng Văn Đức' => ['MISA', 'Excel', 'Luật Thuế', 'SAP'],
            'Vũ Thị Lan' => ['Bán hàng B2B', 'Đàm phán', 'CRM', 'Tiếng Anh'],
            'Bùi Thị Thảo' => ['Adobe Photoshop', 'Adobe Illustrator', 'Adobe InDesign', 'Figma'],
            'Huỳnh Thị Tuyết' => ['UI/UX Design', 'Figma', 'Adobe XD', 'Sketch'],
            'Ngô Thị Linh' => ['Tuyển dụng', 'Luật lao động', 'MS Office', 'Giao tiếp'],
            'Cao Văn Đạt' => ['React Native', 'JavaScript', 'TypeScript', 'Redux'],
            'Đinh Thị Nga' => ['SQL', 'Excel', 'Python', 'Tableau'],
            'Trần Quốc Bảo' => ['Quản lý đội nhóm', 'Xây dựng chiến lược', 'Đàm phán thương mại', 'Phân tích thị trường'],
            'Nguyễn Thị Hồng' => ['Manual Testing', 'Test Case Design', 'Selenium', 'JIRA'],
        ];

        if (isset($skillsData[$candidate->name])) {
            $candidate->skills = $skillsData[$candidate->name];
            $candidate->save();
        }

        // Tags
        $tagsData = [
            'Nguyễn Văn Minh' => ['kinh-nghiem-cao', 'leader', 'technical-expert', 'team-player'],
            'Trần Thị Hương' => ['potential-high', 'eager-to-learn', 'good-attitude'],
            'Lê Minh Tuấn' => ['marketing-expert', 'data-driven', 'creative', 'results-oriented'],
            'Phạm Thị Mai' => ['fresh-graduate', 'enthusiastic', 'quick-learner'],
            'Hoàng Văn Đức' => ['senior-expert', 'leadership', 'compliance-expert', 'strategic-thinking'],
            'Vũ Thị Lan' => ['sales-champion', 'relationship-builder', 'target-achiever'],
            'Đặng Minh Khoa' => ['junior-talent', 'growth-potential', 'motivated'],
            'Bùi Thị Thảo' => ['creative-talent', 'portfolio-strong', 'brand-experience'],
            'Huỳnh Thị Tuyết' => ['ui-ux-expert', 'user-focused', 'design-thinking'],
            'Ngô Thị Linh' => ['hr-potential', 'people-person', 'communication-skills'],
            'Cao Văn Đạt' => ['mobile-developer', 'react-native', 'cross-platform'],
            'Đinh Thị Nga' => ['data-analyst', 'sql-expert', 'analytical-thinking'],
        ];

        if (isset($tagsData[$candidate->name])) {
            $candidate->tags = $tagsData[$candidate->name];
            $candidate->save();
        }
    }

    private function createInterviewers($users)
    {
        $this->command->info('👨‍💼 Tạo interviewers...');

        $interviewers = [];

        $interviewersData = [
            [
                'user_id' => $users['manager1']->id,
                'department' => 'Công Nghệ Thông Tin',
                'expertise' => json_encode(['Backend Development', 'System Architecture', 'PHP', 'Laravel']),
                'max_interviews_per_day' => 4,
                'availability' => json_encode([
                    'monday' => true,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'monday' => ['09:00-12:00', '14:00-17:00'],
                    'tuesday' => ['09:00-12:00', '14:00-17:00'],
                    'wednesday' => ['09:00-12:00', '14:00-17:00'],
                    'thursday' => ['09:00-12:00', '14:00-17:00'],
                    'friday' => ['09:00-12:00', '14:00-16:00'],
                ]),
                'location' => 'TP.HCM',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
            [
                'user_id' => $users['manager2']->id,
                'department' => 'Marketing',
                'expertise' => json_encode(['Digital Marketing', 'Brand Management', 'Performance Marketing']),
                'max_interviews_per_day' => 3,
                'availability' => json_encode([
                    'monday' => true,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'monday' => ['10:00-12:00', '14:00-17:00'],
                    'tuesday' => ['10:00-12:00', '14:00-17:00'],
                    'wednesday' => ['10:00-12:00', '14:00-17:00'],
                    'thursday' => ['10:00-12:00', '14:00-17:00'],
                    'friday' => ['10:00-12:00'],
                ]),
                'location' => 'Hà Nội',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
            [
                'user_id' => $users['interviewer1']->id,
                'department' => 'Công Nghệ Thông Tin',
                'expertise' => json_encode(['PHP', 'Laravel', 'Frontend Technologies', 'JavaScript']),
                'max_interviews_per_day' => 3,
                'availability' => json_encode([
                    'monday' => true,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'monday' => ['09:00-11:00', '15:00-17:00'],
                    'tuesday' => ['09:00-11:00', '15:00-17:00'],
                    'wednesday' => ['09:00-11:00', '15:00-17:00'],
                    'thursday' => ['09:00-11:00', '15:00-17:00'],
                    'friday' => ['09:00-11:00'],
                ]),
                'location' => 'TP.HCM',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
            [
                'user_id' => $users['interviewer2']->id,
                'department' => 'Marketing',
                'expertise' => json_encode(['Performance Marketing', 'Analytics', 'Digital Advertising']),
                'max_interviews_per_day' => 2,
                'availability' => json_encode([
                    'monday' => false,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'tuesday' => ['10:00-12:00', '14:00-16:00'],
                    'wednesday' => ['10:00-12:00', '14:00-16:00'],
                    'thursday' => ['10:00-12:00', '14:00-16:00'],
                    'friday' => ['10:00-12:00'],
                ]),
                'location' => 'Hà Nội',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
        ];

        foreach ($interviewersData as $data) {
            $interviewer = Interviewer::create($data);
            $interviewers[] = $interviewer;
        }

        return $interviewers;
    }

    private function createInterviews($candidates, $jobs, $interviewers, $users)
    {
        $this->command->info('📅 Tạo interviews...');

        $interviews = [];

        // Tạo interviews cho các candidates có status phù hợp
        $interviewCandidates = collect($candidates)->filter(function ($candidate) {
            return in_array($candidate->status, ['interview', 'offer', 'hired']);
        });

        foreach ($interviewCandidates as $index => $candidate) {
            // Chọn interviewer phù hợp với job department
            $job = $candidate->jobPosting;
            $suitableInterviewers = collect($interviewers)->filter(function ($interviewer) use ($job) {
                return $interviewer->department === $job->department ||
                    $interviewer->user->department === $job->department;
            });

            if ($suitableInterviewers->isEmpty()) {
                $suitableInterviewers = collect($interviewers);
            }

            $interviewer = $suitableInterviewers->random();

            // Tạo interview date trong quá khứ hoặc tương lai
            $interviewDate = $candidate->status === 'hired'
                ? now()->subDays(rand(5, 15))
                : ($candidate->status === 'offer'
                    ? now()->subDays(rand(1, 7))
                    : now()->addDays(rand(1, 10)));

            $interviewTypes = ['screening', 'technical', 'cultural', 'final'];
            $interviewType = $interviewTypes[array_rand($interviewTypes)];
            $meetingType = rand(0, 1) ? 'video' : 'in-person';

            $interview = Interview::create([
                'candidate_id' => $candidate->id,
                'job_posting_id' => $job->id,
                'interviewer_id' => $interviewer->id,
                'date' => $interviewDate->format('Y-m-d'),
                'time' => sprintf('%02d:00', rand(9, 16)),
                'duration' => rand(3, 6) * 15, // 45-90 minutes
                'type' => $meetingType,
                'status' => $candidate->status === 'hired' ? 'completed' : ($candidate->status === 'offer' ? 'completed' : 'scheduled'),
                'interview_type' => $interviewType,
                'meeting_link' => $meetingType === 'video' ? 'https://meet.google.com/abc-defg-hij' : null,
                'location' => $meetingType === 'in-person' ? 'Phòng họp A - Tầng 5' : null,
                'notes' => "Phỏng vấn {$interviewType} cho vị trí {$job->title}",
                'agenda' => json_encode([
                    'Giới thiệu bản thân và công ty',
                    'Đánh giá kinh nghiệm và kỹ năng',
                    'Thảo luận về dự án đã làm',
                    'Câu hỏi từ ứng viên',
                    'Thông tin về bước tiếp theo'
                ]),
                'round' => rand(1, 3),
                'created_by' => $users['recruiter1']->id,
            ]);

            $interviews[] = $interview;
        }

        return $interviews;
    }

    private function createInterviewFeedback($interviews, $interviewers)
    {
        $this->command->info('📝 Tạo interview feedback...');

        // Tạo feedback cho các interview đã completed
        $completedInterviews = collect($interviews)->filter(function ($interview) {
            return $interview->status === 'completed';
        });

        foreach ($completedInterviews as $interview) {
            $rating = rand(30, 50) / 10; // 3.0 - 5.0
            $recommend = $rating >= 4.0;

            $strengths = [
                'Kinh nghiệm làm việc phong phú',
                'Kỹ năng giao tiếp tốt',
                'Tư duy logic và phân tích',
                'Khả năng học hỏi nhanh',
                'Thái độ tích cực và nhiệt huyết',
                'Kiến thức chuyên môn vững vàng',
                'Khả năng làm việc nhóm',
                'Tính chủ động cao'
            ];

            $concerns = [
                'Cần cải thiện kỹ năng thuyết trình',
                'Kinh nghiệm với công nghệ mới còn hạn chế',
                'Cần thời gian để làm quen với quy trình',
                'Kỹ năng tiếng Anh cần cải thiện',
                'Cần phát triển thêm soft skills'
            ];

            InterviewFeedback::create([
                'interview_id' => $interview->id,
                'interviewer_id' => $interview->interviewer_id,
                'rating' => $rating,
                'comments' => $recommend
                    ? 'Ứng viên có tiềm năng tốt, phù hợp với vị trí. Kiến thức chuyên môn vững vàng và thái độ làm việc tích cực.'
                    : 'Ứng viên có một số điểm tốt nhưng cần cải thiện thêm để phù hợp với yêu cầu công việc.',
                'recommend' => $recommend,
                'strengths' => json_encode(array_slice($strengths, 0, rand(2, 4))),
                'concerns' => json_encode($recommend ? [] : array_slice($concerns, 0, rand(1, 3))),
                'next_round_recommendation' => $recommend ?
                    (['technical', 'cultural', 'final'][array_rand(['technical', 'cultural', 'final'])]) : null,
                'technical_score' => rand(60, 95),
                'communication_score' => rand(70, 95),
                'cultural_fit_score' => rand(65, 90),
            ]);
        }
    }
}
