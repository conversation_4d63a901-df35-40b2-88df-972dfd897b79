<?php

namespace Database\Seeders;

use App\Models\Candidate;
use App\Models\CandidateEducation;
use App\Models\CandidateSkill;
use App\Models\CandidateStatusHistory;
use App\Models\CandidateTag;
use App\Models\CandidateWorkHistory;
use App\Models\Interview;
use App\Models\InterviewFeedback;
use App\Models\Interviewer;
use App\Models\JobBenefit;
use App\Models\JobPosting;
use App\Models\JobRequirement;
use App\Models\JobResponsibility;
use App\Models\JobSkill;
use App\Models\Message;
use App\Models\MessageTemplate;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class VietnameseUTF8DataSeeder extends Seeder
{
    use VietnameseUTF8DataSeederPart2;
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Tao du lieu moi voi chuan UTF-8...');

        // Tao permissions va roles
        $this->createPermissionsAndRoles();

        // Tao users
        $users = $this->createUsers();

        // Tao job postings
        $jobs = $this->createJobPostings($users);

        // Tao interviewers
        $interviewers = $this->createInterviewers($users);

        // Tao candidates
        $candidates = $this->createCandidates($users, $jobs);

        // Tao interviews
        $interviews = $this->createInterviews($candidates, $jobs, $interviewers, $users);

        // Tao interview feedback
        $this->createInterviewFeedback($interviews, $interviewers);

        // Tao message templates
        $messageTemplates = $this->createMessageTemplates($users);

        // Tao messages
        $this->createMessages($candidates, $jobs, $messageTemplates, $users);

        $this->command->info('Hoan thanh tao du lieu voi chuan UTF-8!');
        $this->command->info("Thong ke:");
        $this->command->info("- Users: " . User::count());
        $this->command->info("- Job Postings: " . JobPosting::count());
        $this->command->info("- Candidates: " . Candidate::count());
        $this->command->info("- Interviews: " . Interview::count());
        $this->command->info("- Interview Feedback: " . InterviewFeedback::count());
        $this->command->info("- Message Templates: " . MessageTemplate::count());
        $this->command->info("- Messages: " . Message::count());
    }

    private function createPermissionsAndRoles()
    {
        $this->command->info('Tao permissions va roles...');

        // Tao permissions
        $permissions = [
            // Candidate permissions
            'view_candidates', 'create_candidates', 'edit_candidates', 'delete_candidates',
            'manage_candidate_status', 'view_candidate_analytics',

            // Job posting permissions
            'view_jobs', 'create_jobs', 'edit_jobs', 'delete_jobs',
            'publish_jobs', 'manage_job_status', 'view_job_analytics',

            // Interview permissions
            'view_interviews', 'create_interviews', 'edit_interviews', 'delete_interviews',
            'manage_interview_schedule', 'conduct_interviews', 'view_interview_feedback',

            // User management permissions
            'view_users', 'create_users', 'edit_users', 'delete_users',
            'manage_roles', 'manage_permissions',

            // Dashboard and analytics
            'view_dashboard', 'view_analytics', 'export_data',

            // System permissions
            'manage_settings', 'view_activity_log', 'manage_templates',

            // Message permissions
            'view_messages', 'create_messages', 'edit_messages', 'delete_messages',
            'send_messages', 'manage_templates'
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Tao roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $recruiterRole = Role::firstOrCreate(['name' => 'recruiter']);
        $hiringManagerRole = Role::firstOrCreate(['name' => 'hiring_manager']);
        $interviewerRole = Role::firstOrCreate(['name' => 'interviewer']);

        // Gan permissions cho roles
        $adminRole->givePermissionTo(Permission::all());

        $recruiterRole->givePermissionTo([
            'view_candidates', 'create_candidates', 'edit_candidates', 'manage_candidate_status',
            'view_jobs', 'create_jobs', 'edit_jobs', 'publish_jobs',
            'view_interviews', 'create_interviews', 'edit_interviews', 'manage_interview_schedule',
            'view_dashboard', 'view_analytics', 'export_data',
            'view_messages', 'create_messages', 'send_messages'
        ]);

        $hiringManagerRole->givePermissionTo([
            'view_candidates', 'view_candidate_analytics',
            'view_jobs', 'create_jobs', 'edit_jobs', 'view_job_analytics',
            'view_interviews', 'create_interviews', 'view_interview_feedback',
            'view_dashboard', 'view_analytics',
            'view_messages', 'create_messages'
        ]);

        $interviewerRole->givePermissionTo([
            'view_candidates', 'view_interviews', 'conduct_interviews', 'view_interview_feedback',
            'view_messages'
        ]);
    }

    private function createUsers()
    {
        $this->command->info('Tao users...');

        $users = [];

        // Admin user
        $admin = User::create([
            'name' => 'Quan Tri Vien',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'admin',
            'department' => 'Quan Tri',
            'title' => 'Quan Tri Vien He Thong',
            'phone' => '0901234567',
        ]);
        $admin->assignRole('admin');
        $users['admin'] = $admin;

        // Recruiter users
        $recruiter1 = User::create([
            'name' => 'Nguyen Thi Lan Anh',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'recruiter',
            'department' => 'Nhan Su',
            'title' => 'Chuyen Vien Tuyen Dung Senior',
            'phone' => '0912345678',
        ]);
        $recruiter1->assignRole('recruiter');
        $users['recruiter1'] = $recruiter1;

        $recruiter2 = User::create([
            'name' => 'Tran Van Minh',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'recruiter',
            'department' => 'Nhan Su',
            'title' => 'Chuyen Vien Tuyen Dung',
            'phone' => '**********',
        ]);
        $recruiter2->assignRole('recruiter');
        $users['recruiter2'] = $recruiter2;

        // Hiring Manager users
        $manager1 = User::create([
            'name' => 'Le Hoang Nam',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'hiring_manager',
            'department' => 'Cong Nghe Thong Tin',
            'title' => 'Truong Phong IT',
            'phone' => '0934567890',
        ]);
        $manager1->assignRole('hiring_manager');
        $users['manager1'] = $manager1;

        $manager2 = User::create([
            'name' => 'Pham Thi Huong',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'hiring_manager',
            'department' => 'Marketing',
            'title' => 'Truong Phong Marketing',
            'phone' => '**********',
        ]);
        $manager2->assignRole('hiring_manager');
        $users['manager2'] = $manager2;

        $manager3 = User::create([
            'name' => 'Hoang Thi Linh',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'hiring_manager',
            'department' => 'Tai Chinh',
            'title' => 'Truong Phong Tai Chinh',
            'phone' => '0989012345',
        ]);
        $manager3->assignRole('hiring_manager');
        $users['manager3'] = $manager3;

        // Interviewer users
        $interviewer1 = User::create([
            'name' => 'Vo Minh Tuan',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'interviewer',
            'department' => 'Cong Nghe Thong Tin',
            'title' => 'Senior Software Engineer',
            'phone' => '**********',
        ]);
        $interviewer1->assignRole('interviewer');
        $users['interviewer1'] = $interviewer1;

        $interviewer2 = User::create([
            'name' => 'Dang Thi Mai',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'interviewer',
            'department' => 'Marketing',
            'title' => 'Marketing Manager',
            'phone' => '0967890123',
        ]);
        $interviewer2->assignRole('interviewer');
        $users['interviewer2'] = $interviewer2;

        $interviewer3 = User::create([
            'name' => 'Bui Van Duc',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'interviewer',
            'department' => 'Tai Chinh',
            'title' => 'Finance Manager',
            'phone' => '0978901234',
        ]);
        $interviewer3->assignRole('interviewer');
        $users['interviewer3'] = $interviewer3;

        $interviewer4 = User::create([
            'name' => 'Cao Thi Yen',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'interviewer',
            'department' => 'Kinh Doanh',
            'title' => 'Sales Director',
            'phone' => '0990123456',
        ]);
        $interviewer4->assignRole('interviewer');
        $users['interviewer4'] = $interviewer4;

        return $users;
    }

    private function createJobPostings($users)
    {
        $this->command->info('Tao job postings...');

        $jobs = [];

        $jobsData = [
            [
                'title' => 'Lap Trinh Vien PHP Senior',
                'department' => 'Cong Nghe Thong Tin',
                'location' => 'TP Ho Chi Minh',
                'type' => 'full-time',
                'work_location' => 'hybrid',
                'salary_min' => ********,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Chung toi dang tim kiem mot Lap trinh vien PHP Senior co kinh nghiem de tham gia vao doi ngu phat trien san pham. Ung vien se chiu trach nhiem phat trien va duy tri cac ung dung web su dung PHP va Laravel framework, dong thoi huong dan cac developer junior.',
                'education_required' => 'Tot nghiep Dai hoc chuyen nganh Cong nghe thong tin, Khoa hoc may tinh hoac tuong duong',
                'company_culture' => 'Moi truong lam viec nang dong, sang tao voi nhieu co hoi phat trien nghe nghiep. Van hoa hoc hoi va chia se kien thuc.',
                'experience_level' => 'senior',
                'status' => 'active',
                'priority' => 'high',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter1',
            ],
            [
                'title' => 'Chuyen Vien Marketing Digital',
                'department' => 'Marketing',
                'location' => 'Ha Noi',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 15000000,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Vi tri nay phu hop voi ung vien co dam me voi marketing so va mong muon phat trien thuong hieu truc tuyen. Ban se lam viec voi cac kenh digital nhu Facebook, Google Ads, SEO/SEM va phan tich du lieu de toi uu hoa chien dich.',
                'education_required' => 'Tot nghiep Dai hoc chuyen nganh Marketing, Kinh te, Truyen thong hoac tuong duong',
                'company_culture' => 'Van hoa doanh nghiep tre trung, khuyen khich sang tao va hoc hoi. Moi truong lam viec linh hoat va ho tro phat trien ca nhan.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'medium',
                'hiring_manager' => 'manager2',
                'recruiter' => 'recruiter1',
            ],
            [
                'title' => 'Ke Toan Truong',
                'department' => 'Tai Chinh',
                'location' => 'Da Nang',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 20000000,
                'salary_max' => 35000000,
                'currency' => 'VND',
                'description' => 'Chiu trach nhiem quan ly toan bo hoat dong ke toan cua cong ty, lap bao cao tai chinh, quan ly ngan sach va dam bao tuan thu cac quy dinh phap luat ve tai chinh. Vi tri nay doi hoi kinh nghiem quan ly va kien thuc sau ve ke toan.',
                'education_required' => 'Tot nghiep Dai hoc chuyen nganh Ke toan, Tai chinh hoac tuong duong. Co chung chi ke toan truong la loi the.',
                'company_culture' => 'Moi truong lam viec chuyen nghiep, on dinh voi che do dai ngo hap dan va co hoi phat trien nghe nghiep lau dai.',
                'experience_level' => 'senior',
                'status' => 'active',
                'priority' => 'high',
                'hiring_manager' => 'manager3',
                'recruiter' => 'recruiter2',
            ],
            [
                'title' => 'Nhan Vien Kinh Doanh B2B',
                'department' => 'Kinh Doanh',
                'location' => 'TP Ho Chi Minh',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 12000000,
                'salary_max' => 20000000,
                'currency' => 'VND',
                'description' => 'Phat trien va duy tri moi quan he voi khach hang doanh nghiep, tim kiem co hoi kinh doanh moi, dam phan hop dong va dat chi tieu doanh so. Cong viec doi hoi ky nang giao tiep tot va kha nang xay dung moi quan he.',
                'education_required' => 'Tot nghiep Dai hoc cac chuyen nganh Kinh te, Quan tri kinh doanh hoac tuong duong',
                'company_culture' => 'Moi truong canh tranh lanh manh voi hoa hong hap dan va nhieu co hoi thang tien.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'medium',
                'hiring_manager' => 'manager2',
                'recruiter' => 'recruiter1',
            ],
            [
                'title' => 'Thiet Ke Do Hoa (Graphic Designer)',
                'department' => 'Sang Tao',
                'location' => 'Remote',
                'type' => 'full-time',
                'work_location' => 'remote',
                'salary_min' => 10000000,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Thiet ke cac tai lieu marketing, branding, website va cac san pham truyen thong khac. Lam viec chat che voi doi marketing de tao ra nhung san pham sang tao va hieu qua.',
                'education_required' => 'Tot nghiep Dai hoc chuyen nganh My thuat, Thiet ke do hoa, Truyen thong da phuong tien hoac tuong duong',
                'company_culture' => 'Moi truong sang tao, linh hoat voi nhieu du an thu vi va co hoi phat trien ky nang.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'medium',
                'hiring_manager' => 'manager2',
                'recruiter' => 'recruiter2',
            ],
            [
                'title' => 'Chuyen Vien Nhan Su (HR Specialist)',
                'department' => 'Nhan Su',
                'location' => 'TP Ho Chi Minh',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => 12000000,
                'salary_max' => 20000000,
                'currency' => 'VND',
                'description' => 'Chiu trach nhiem tuyen dung, dao tao va phat trien nhan su. Xay dung quy trinh HR, chinh sach nhan su va van hoa doanh nghiep. Ho tro cac hoat dong quan ly nhan su hang ngay.',
                'education_required' => 'Tot nghiep Dai hoc chuyen nganh Quan tri nhan luc, Tam ly hoc, Luat hoac tuong duong',
                'company_culture' => 'Moi truong lam viec nhan van, ton trong con nguoi va phat trien ben vung.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'medium',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter1',
            ],
            [
                'title' => 'Lap Trinh Vien Mobile (React Native)',
                'department' => 'Cong Nghe Thong Tin',
                'location' => 'Ha Noi',
                'type' => 'full-time',
                'work_location' => 'hybrid',
                'salary_min' => 20000000,
                'salary_max' => 35000000,
                'currency' => 'VND',
                'description' => 'Phat trien ung dung mobile da nen tang su dung React Native. Toi uu hoa hieu suat ung dung, tich hop API va dam bao trai nghiem nguoi dung tot nhat.',
                'education_required' => 'Tot nghiep Dai hoc chuyen nganh Cong nghe thong tin hoac tuong duong',
                'company_culture' => 'Van hoa cong nghe tien tien, khuyen khich innovation va continuous learning.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'high',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter2',
            ],
            [
                'title' => 'Chuyen Vien Phan Tich Du Lieu (Data Analyst)',
                'department' => 'Phan Tich Du Lieu',
                'location' => 'Remote',
                'type' => 'full-time',
                'work_location' => 'remote',
                'salary_min' => ********,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Phan tich du lieu kinh doanh, tao bao cao va dashboard de ho tro ra quyet dinh. Lam viec voi big data va cac cong cu phan tich hien dai.',
                'education_required' => 'Tot nghiep Dai hoc chuyen nganh Toan, Thong ke, Kinh te, Cong nghe thong tin hoac tuong duong',
                'company_culture' => 'Moi truong data-driven, khuyen khich tu duy phan tich va sang tao.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'high',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter1',
            ],
            [
                'title' => 'Truong Phong Kinh Doanh',
                'department' => 'Kinh Doanh',
                'location' => 'TP Ho Chi Minh',
                'type' => 'full-time',
                'work_location' => 'onsite',
                'salary_min' => ********,
                'salary_max' => 50000000,
                'currency' => 'VND',
                'description' => 'Quan ly va phat trien doi ngu kinh doanh, xay dung chien luoc ban hang, thiet lap muc tieu va KPI cho team. Phat trien moi quan he voi khach hang lon va doi tac chien luoc.',
                'education_required' => 'Tot nghiep Dai hoc chuyen nganh Kinh te, Quan tri kinh doanh hoac tuong duong',
                'company_culture' => 'Moi truong lam viec nang dong, huong den ket qua va thuong xung dang cho thanh tich.',
                'experience_level' => 'senior',
                'status' => 'active',
                'priority' => 'high',
                'hiring_manager' => 'manager2',
                'recruiter' => 'recruiter2',
            ],
            [
                'title' => 'Ky Su QA/QC',
                'department' => 'Cong Nghe Thong Tin',
                'location' => 'Da Nang',
                'type' => 'full-time',
                'work_location' => 'hybrid',
                'salary_min' => 15000000,
                'salary_max' => ********,
                'currency' => 'VND',
                'description' => 'Thiet ke va thuc hien cac test case, kiem tra chat luong phan mem, phat hien va bao cao loi. Lam viec chat che voi doi phat trien de dam bao chat luong san pham.',
                'education_required' => 'Tot nghiep Dai hoc chuyen nganh Cong nghe thong tin hoac tuong duong',
                'company_culture' => 'Moi truong lam viec chuyen nghiep, chu trong chat luong va quy trinh.',
                'experience_level' => 'mid',
                'status' => 'active',
                'priority' => 'medium',
                'hiring_manager' => 'manager1',
                'recruiter' => 'recruiter1',
            ],
        ];

        foreach ($jobsData as $index => $jobData) {
            $hiringManager = $users[$jobData['hiring_manager']];
            $recruiter = $users[$jobData['recruiter']];

            unset($jobData['hiring_manager'], $jobData['recruiter']);

            $job = JobPosting::create([
                ...$jobData,
                'hiring_manager_id' => $hiringManager->id,
                'recruiter_id' => $recruiter->id,
                'created_by' => $users['admin']->id,
                'posted_date' => now()->subDays(rand(1, 10)),
                'closing_date' => now()->addDays(rand(20, 40)),
            ]);

            // Tao job requirements, responsibilities, benefits, skills
            $this->createJobDetails($job, $index);

            $jobs[] = $job;
        }

        return $jobs;
    }

    private function createJobDetails($job, $index)
    {
        // Job Requirements
        $requirements = [
            // PHP Senior
            0 => [
                ['requirement_text' => 'Co it nhat 5 nam kinh nghiem lap trinh PHP', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thanh thao Laravel Framework va cac design patterns', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Kinh nghiem voi MySQL, PostgreSQL va Redis', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Hieu biet ve Git, Docker va CI/CD', 'is_mandatory' => false, 'sort_order' => 4],
                ['requirement_text' => 'Ky nang giao tiep tieng Anh tot', 'is_mandatory' => false, 'sort_order' => 5],
            ],
            // Marketing Digital
            1 => [
                ['requirement_text' => 'Co it nhat 2 nam kinh nghiem Digital Marketing', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thanh thao Facebook Ads, Google Ads va Google Analytics', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Hieu biet ve SEO/SEM va Content Marketing', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Ky nang phan tich du lieu va bao cao', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            // Ke toan truong
            2 => [
                ['requirement_text' => 'Co it nhat 7 nam kinh nghiem ke toan', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Co chung chi ke toan truong hoac CPA', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Thanh thao phan mem ke toan MISA, FAST, SAP', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Hieu biet sau ve phap luat thue Viet Nam', 'is_mandatory' => true, 'sort_order' => 4],
            ],
            // Sales B2B
            3 => [
                ['requirement_text' => 'Co it nhat 2 nam kinh nghiem ban hang B2B', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Ky nang dam phan va thuyet phuc xuat sac', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Kha nang lam viec duoi ap luc cao', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Co mang luoi khach hang san co', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            // Graphic Designer
            4 => [
                ['requirement_text' => 'Co it nhat 2 nam kinh nghiem thiet ke do hoa', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thanh thao Adobe Creative Suite (Photoshop, Illustrator, InDesign)', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Co khieu tham my va tu duy sang tao', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Hieu biet ve UI/UX Design va Figma', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            // HR Specialist
            5 => [
                ['requirement_text' => 'Co it nhat 2 nam kinh nghiem HR', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Hieu biet ve luat lao dong Viet Nam', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Ky nang giao tiep va thuyet trinh tot', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Thanh thao MS Office va cac cong cu HR', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            // React Native
            6 => [
                ['requirement_text' => 'Co it nhat 2 nam kinh nghiem React Native', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thanh thao JavaScript/TypeScript va React', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Hieu biet ve iOS va Android platform', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Kinh nghiem voi Redux, MobX va native modules', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            // Data Analyst
            7 => [
                ['requirement_text' => 'Co it nhat 1 nam kinh nghiem phan tich du lieu', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Thanh thao SQL va Excel nang cao', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Hieu biet ve Python hoac R cho data science', 'is_mandatory' => false, 'sort_order' => 3],
                ['requirement_text' => 'Kinh nghiem voi Tableau, Power BI hoac cac cong cu BI', 'is_mandatory' => false, 'sort_order' => 4],
            ],
            // Truong phong kinh doanh
            8 => [
                ['requirement_text' => 'Co it nhat 5 nam kinh nghiem kinh doanh, trong do co 2 nam o vi tri quan ly', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Kha nang xay dung va quan ly doi ngu kinh doanh hieu qua', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Ky nang dam phan, thuong luong va dong cua deal lon', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Hieu biet sau ve thi truong va doi thu canh tranh', 'is_mandatory' => true, 'sort_order' => 4],
                ['requirement_text' => 'Ky nang phan tich du lieu kinh doanh va du bao', 'is_mandatory' => false, 'sort_order' => 5],
            ],
            // QA/QC
            9 => [
                ['requirement_text' => 'Co it nhat 2 nam kinh nghiem QA/QC trong linh vuc phan mem', 'is_mandatory' => true, 'sort_order' => 1],
                ['requirement_text' => 'Kinh nghiem viet test case va thuc hien test automation', 'is_mandatory' => true, 'sort_order' => 2],
                ['requirement_text' => 'Hieu biet ve quy trinh phat trien phan mem Agile/Scrum', 'is_mandatory' => true, 'sort_order' => 3],
                ['requirement_text' => 'Kinh nghiem voi cac cong cu kiem thu nhu Selenium, JUnit, TestNG', 'is_mandatory' => false, 'sort_order' => 4],
            ],
        ];

        if (isset($requirements[$index])) {
            foreach ($requirements[$index] as $req) {
                $job->requirements()->create($req);
            }
        }

        // Job Responsibilities
        $responsibilities = [
            0 => [ // PHP Senior
                ['responsibility_text' => 'Phat trien va duy tri cac ung dung web su dung PHP/Laravel', 'sort_order' => 1],
                ['responsibility_text' => 'Thiet ke co so du lieu va toi uu hoa hieu suat he thong', 'sort_order' => 2],
                ['responsibility_text' => 'Code review va huong dan junior developers', 'sort_order' => 3],
                ['responsibility_text' => 'Tham gia vao viec lap ke hoach va uoc luong du an', 'sort_order' => 4],
                ['responsibility_text' => 'Nghien cuu va ap dung cong nghe moi', 'sort_order' => 5],
            ],
            1 => [ // Marketing Digital
                ['responsibility_text' => 'Lap ke hoach va trien khai chien dich marketing online', 'sort_order' => 1],
                ['responsibility_text' => 'Quan ly va toi uu hoa quang cao Facebook, Google Ads', 'sort_order' => 2],
                ['responsibility_text' => 'Phan tich du lieu va bao cao hieu qua chien dich', 'sort_order' => 3],
                ['responsibility_text' => 'Tao noi dung marketing sang tao va hap dan', 'sort_order' => 4],
            ],
            2 => [ // Ke toan truong
                ['responsibility_text' => 'Quan ly va giam sat toan bo hoat dong ke toan', 'sort_order' => 1],
                ['responsibility_text' => 'Lap bao cao tai chinh dinh ky va bao cao thue', 'sort_order' => 2],
                ['responsibility_text' => 'Quan ly ngan sach va du bao tai chinh', 'sort_order' => 3],
                ['responsibility_text' => 'Dam bao tuan thu cac quy dinh phap luat', 'sort_order' => 4],
            ],
            3 => [ // Sales B2B
                ['responsibility_text' => 'Tim kiem va phat trien khach hang doanh nghiep moi', 'sort_order' => 1],
                ['responsibility_text' => 'Duy tri va phat trien moi quan he voi khach hang hien tai', 'sort_order' => 2],
                ['responsibility_text' => 'Dam phan hop dong va chot deal', 'sort_order' => 3],
                ['responsibility_text' => 'Bao cao ket qua kinh doanh va phan tich thi truong', 'sort_order' => 4],
            ],
            4 => [ // Graphic Designer
                ['responsibility_text' => 'Thiet ke cac tai lieu marketing va branding', 'sort_order' => 1],
                ['responsibility_text' => 'Tao concept va y tuong sang tao cho cac du an', 'sort_order' => 2],
                ['responsibility_text' => 'Phoi hop voi team marketing trong cac campaign', 'sort_order' => 3],
                ['responsibility_text' => 'Dam bao chat luong va thong nhat thuong hieu', 'sort_order' => 4],
            ],
            5 => [ // HR Specialist
                ['responsibility_text' => 'Tuyen dung va sang loc ung vien', 'sort_order' => 1],
                ['responsibility_text' => 'Xay dung quy trinh HR va chinh sach nhan su', 'sort_order' => 2],
                ['responsibility_text' => 'Dao tao va phat trien nhan vien', 'sort_order' => 3],
                ['responsibility_text' => 'Quan ly ho so nhan su va cham cong', 'sort_order' => 4],
            ],
            6 => [ // React Native
                ['responsibility_text' => 'Phat trien ung dung mobile da nen tang', 'sort_order' => 1],
                ['responsibility_text' => 'Toi uu hoa hieu suat va trai nghiem nguoi dung', 'sort_order' => 2],
                ['responsibility_text' => 'Tich hop API va third-party services', 'sort_order' => 3],
                ['responsibility_text' => 'Testing, debugging va deployment', 'sort_order' => 4],
            ],
            7 => [ // Data Analyst
                ['responsibility_text' => 'Thu thap, lam sach va phan tich du lieu kinh doanh', 'sort_order' => 1],
                ['responsibility_text' => 'Tao bao cao va dashboard truc quan', 'sort_order' => 2],
                ['responsibility_text' => 'Phan tich xu huong va dua ra insights', 'sort_order' => 3],
                ['responsibility_text' => 'Ho tro ra quyet dinh kinh doanh dua tren du lieu', 'sort_order' => 4],
            ],
            8 => [ // Truong phong kinh doanh
                ['responsibility_text' => 'Xay dung va thuc hien chien luoc kinh doanh', 'sort_order' => 1],
                ['responsibility_text' => 'Quan ly va phat trien doi ngu kinh doanh', 'sort_order' => 2],
                ['responsibility_text' => 'Thiet lap muc tieu va KPI cho team', 'sort_order' => 3],
                ['responsibility_text' => 'Phat trien moi quan he voi khach hang lon va doi tac chien luoc', 'sort_order' => 4],
                ['responsibility_text' => 'Bao cao ket qua kinh doanh va de xuat giai phap cai thien', 'sort_order' => 5],
            ],
            9 => [ // QA/QC
                ['responsibility_text' => 'Thiet ke va thuc hien cac test case', 'sort_order' => 1],
                ['responsibility_text' => 'Kiem tra chat luong phan mem va phat hien loi', 'sort_order' => 2],
                ['responsibility_text' => 'Lam viec voi doi phat trien de sua loi va cai thien san pham', 'sort_order' => 3],
                ['responsibility_text' => 'Xay dung va duy tri quy trinh kiem thu', 'sort_order' => 4],
                ['responsibility_text' => 'Thuc hien test automation khi can thiet', 'sort_order' => 5],
            ],
        ];

        if (isset($responsibilities[$index])) {
            foreach ($responsibilities[$index] as $resp) {
                $job->responsibilities()->create($resp);
            }
        }

        // Job Benefits (chung cho tat ca)
        $benefits = [
            ['benefit_text' => 'Luong thuong canh tranh, xet tang luong dinh ky 6 thang', 'sort_order' => 1],
            ['benefit_text' => 'Bao hiem suc khoe cao cap cho nhan vien va gia dinh', 'sort_order' => 2],
            ['benefit_text' => 'Thuong thang 13, thuong hieu suat va thuong du an', 'sort_order' => 3],
            ['benefit_text' => 'Nghi phep nam 12 ngay + cac ngay le tet theo quy dinh', 'sort_order' => 4],
            ['benefit_text' => 'Co hoi dao tao va phat trien nghe nghiep', 'sort_order' => 5],
            ['benefit_text' => 'Moi truong lam viec tre trung, nang dong va than thien', 'sort_order' => 6],
            ['benefit_text' => 'Team building, du lich cong ty hang nam', 'sort_order' => 7],
            ['benefit_text' => 'Ho tro laptop, thiet bi lam viec hien dai', 'sort_order' => 8],
        ];

        foreach ($benefits as $benefit) {
            $job->benefits()->create($benefit);
        }

        // Job Skills
        $skills = [
            0 => [ // PHP Senior
                ['skill_name' => 'PHP', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Laravel', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'MySQL', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'JavaScript', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Vue.js', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'beginner'],
                ['skill_name' => 'Docker', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'beginner'],
            ],
            1 => [ // Marketing Digital
                ['skill_name' => 'Facebook Ads', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Google Ads', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Google Analytics', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'SEO', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Content Marketing', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
            ],
            2 => [ // Ke toan truong
                ['skill_name' => 'MISA', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Excel', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Luat Thue', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Bao cao tai chinh', 'importance_level' => 'required', 'proficiency_level' => 'expert'],
                ['skill_name' => 'SAP', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
            ],
            3 => [ // Sales B2B
                ['skill_name' => 'Ban hang B2B', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Dam phan', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'CRM', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Tieng Anh', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'intermediate'],
            ],
            4 => [ // Graphic Designer
                ['skill_name' => 'Adobe Photoshop', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Adobe Illustrator', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Adobe InDesign', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Figma', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'beginner'],
            ],
            5 => [ // HR Specialist
                ['skill_name' => 'Tuyen dung', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Luat lao dong', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'MS Office', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Giao tiep', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
            ],
            6 => [ // React Native
                ['skill_name' => 'React Native', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'JavaScript', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'TypeScript', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Redux', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'intermediate'],
            ],
            7 => [ // Data Analyst
                ['skill_name' => 'SQL', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Excel', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Python', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Tableau', 'importance_level' => 'nice-to-have', 'proficiency_level' => 'beginner'],
            ],
            8 => [ // Truong phong kinh doanh
                ['skill_name' => 'Quan ly doi ngu', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Xay dung chien luoc', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Dam phan thuong mai', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Phan tich thi truong', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Tieng Anh', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
            ],
            9 => [ // QA/QC
                ['skill_name' => 'Manual Testing', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Test Case Design', 'importance_level' => 'required', 'proficiency_level' => 'advanced'],
                ['skill_name' => 'Selenium', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'JIRA', 'importance_level' => 'required', 'proficiency_level' => 'intermediate'],
                ['skill_name' => 'Agile/Scrum', 'importance_level' => 'preferred', 'proficiency_level' => 'intermediate'],
            ],
        ];

        if (isset($skills[$index])) {
            foreach ($skills[$index] as $skill) {
                $job->skills()->create($skill);
            }
        }
    }

    private function createInterviewers($users)
    {
        $this->command->info('Tao interviewers...');

        $interviewers = [];

        $interviewersData = [
            [
                'user_id' => $users['manager1']->id,
                'department' => 'Cong Nghe Thong Tin',
                'expertise' => json_encode(['Backend Development', 'System Architecture', 'PHP', 'Laravel']),
                'max_interviews_per_day' => 4,
                'availability' => json_encode([
                    'monday' => true,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'monday' => ['09:00-12:00', '14:00-17:00'],
                    'tuesday' => ['09:00-12:00', '14:00-17:00'],
                    'wednesday' => ['09:00-12:00', '14:00-17:00'],
                    'thursday' => ['09:00-12:00', '14:00-17:00'],
                    'friday' => ['09:00-12:00', '14:00-16:00'],
                ]),
                'location' => 'TP Ho Chi Minh',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
            [
                'user_id' => $users['manager2']->id,
                'department' => 'Marketing',
                'expertise' => json_encode(['Digital Marketing', 'Brand Management', 'Performance Marketing']),
                'max_interviews_per_day' => 3,
                'availability' => json_encode([
                    'monday' => true,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'monday' => ['10:00-12:00', '14:00-17:00'],
                    'tuesday' => ['10:00-12:00', '14:00-17:00'],
                    'wednesday' => ['10:00-12:00', '14:00-17:00'],
                    'thursday' => ['10:00-12:00', '14:00-17:00'],
                    'friday' => ['10:00-12:00'],
                ]),
                'location' => 'Ha Noi',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
            [
                'user_id' => $users['manager3']->id,
                'department' => 'Tai Chinh',
                'expertise' => json_encode(['Accounting', 'Financial Management', 'Tax Law', 'Budgeting']),
                'max_interviews_per_day' => 3,
                'availability' => json_encode([
                    'monday' => true,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'monday' => ['09:00-12:00', '14:00-17:00'],
                    'tuesday' => ['09:00-12:00', '14:00-17:00'],
                    'wednesday' => ['09:00-12:00', '14:00-17:00'],
                    'thursday' => ['09:00-12:00', '14:00-17:00'],
                    'friday' => ['09:00-12:00'],
                ]),
                'location' => 'Da Nang',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
            [
                'user_id' => $users['interviewer1']->id,
                'department' => 'Cong Nghe Thong Tin',
                'expertise' => json_encode(['PHP', 'Laravel', 'Frontend Technologies', 'JavaScript']),
                'max_interviews_per_day' => 3,
                'availability' => json_encode([
                    'monday' => true,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'monday' => ['09:00-11:00', '15:00-17:00'],
                    'tuesday' => ['09:00-11:00', '15:00-17:00'],
                    'wednesday' => ['09:00-11:00', '15:00-17:00'],
                    'thursday' => ['09:00-11:00', '15:00-17:00'],
                    'friday' => ['09:00-11:00'],
                ]),
                'location' => 'TP Ho Chi Minh',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
            [
                'user_id' => $users['interviewer2']->id,
                'department' => 'Marketing',
                'expertise' => json_encode(['Performance Marketing', 'Analytics', 'Digital Advertising']),
                'max_interviews_per_day' => 2,
                'availability' => json_encode([
                    'monday' => false,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'tuesday' => ['10:00-12:00', '14:00-16:00'],
                    'wednesday' => ['10:00-12:00', '14:00-16:00'],
                    'thursday' => ['10:00-12:00', '14:00-16:00'],
                    'friday' => ['10:00-12:00'],
                ]),
                'location' => 'Ha Noi',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
            [
                'user_id' => $users['interviewer3']->id,
                'department' => 'Tai Chinh',
                'expertise' => json_encode(['Financial Analysis', 'Accounting Software', 'Tax Compliance']),
                'max_interviews_per_day' => 2,
                'availability' => json_encode([
                    'monday' => true,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => false,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'monday' => ['09:00-12:00', '14:00-16:00'],
                    'tuesday' => ['09:00-12:00', '14:00-16:00'],
                    'wednesday' => ['09:00-12:00', '14:00-16:00'],
                    'thursday' => ['09:00-12:00', '14:00-16:00'],
                ]),
                'location' => 'Da Nang',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
            [
                'user_id' => $users['interviewer4']->id,
                'department' => 'Kinh Doanh',
                'expertise' => json_encode(['B2B Sales', 'Business Development', 'Client Relationship']),
                'max_interviews_per_day' => 3,
                'availability' => json_encode([
                    'monday' => true,
                    'tuesday' => true,
                    'wednesday' => true,
                    'thursday' => true,
                    'friday' => true,
                    'saturday' => false,
                    'sunday' => false,
                ]),
                'time_slots' => json_encode([
                    'monday' => ['10:00-12:00', '14:00-17:00'],
                    'tuesday' => ['10:00-12:00', '14:00-17:00'],
                    'wednesday' => ['10:00-12:00', '14:00-17:00'],
                    'thursday' => ['10:00-12:00', '14:00-17:00'],
                    'friday' => ['10:00-12:00'],
                ]),
                'location' => 'TP Ho Chi Minh',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'is_active' => true,
            ],
        ];

        foreach ($interviewersData as $data) {
            $interviewer = Interviewer::create($data);
            $interviewers[] = $interviewer;
        }

        return $interviewers;
    }

    private function createCandidates($users, $jobs)
    {
        $this->command->info('Tao candidates...');

        $candidates = [];

        $candidatesData = [
            // Ung vien cho PHP Senior
            [
                'name' => 'Nguyen Van Minh',
                'email' => '<EMAIL>',
                'phone' => '0901234567',
                'position' => 'Senior PHP Developer',
                'experience' => '5-10 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(5),
                'source' => 'LinkedIn',
                'location' => 'Quan 1, TP.HCM',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => 42000000,
                'rating' => 4.5,
                'ai_score' => 88,
                'linkedin_url' => 'https://linkedin.com/in/minh-nguyen-dev',
                'github_url' => 'https://github.com/minhdev',
                'notes' => 'Ung vien co kinh nghiem tot voi Laravel, da lam viec tai nhieu cong ty cong nghe lon. Ky nang technical va leadership xuat sac.',
                'job_index' => 0,
            ],
            [
                'name' => 'Tran Thi Huong',
                'email' => '<EMAIL>',
                'phone' => '0912345678',
                'position' => 'PHP Developer',
                'experience' => '3-5 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(8),
                'source' => 'TopCV',
                'location' => 'Quan 7, TP.HCM',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => 35000000,
                'rating' => 4.2,
                'ai_score' => 82,
                'notes' => 'Co kinh nghiem voi PHP va Laravel, ky nang giao tiep tot. Can danh gia them ve kha nang leadership.',
                'job_index' => 0,
            ],
            [
                'name' => 'Ly Van Hung',
                'email' => '<EMAIL>',
                'phone' => '0989012345',
                'position' => 'Full Stack Developer',
                'experience' => '3-5 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(2),
                'source' => 'ITviec',
                'location' => 'Quan Binh Thanh, TP.HCM',
                'salary_expectation_min' => 22000000,
                'salary_expectation_max' => 32000000,
                'rating' => 4.0,
                'ai_score' => 79,
                'notes' => 'Co kinh nghiem fullstack, tung lam viec tai startup. Can danh gia ky nang PHP backend.',
                'job_index' => 0,
            ],

            // Ung vien cho Marketing Digital
            [
                'name' => 'Le Minh Tuan',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'Digital Marketing Manager',
                'experience' => '3-5 years',
                'status' => 'offer',
                'applied_date' => now()->subDays(12),
                'source' => 'VietnamWorks',
                'location' => 'Ba Dinh, Ha Noi',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 4.7,
                'ai_score' => 91,
                'notes' => 'Co kinh nghiem quan ly ngan sach quang cao lon, hieu biet sau ve Facebook Ads va Google Ads. Ung vien xuat sac.',
                'job_index' => 1,
            ],
            [
                'name' => 'Pham Thi Mai',
                'email' => '<EMAIL>',
                'phone' => '0934567890',
                'position' => 'Marketing Executive',
                'experience' => '1-3 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(3),
                'source' => 'Referral',
                'location' => 'Cau Giay, Ha Noi',
                'salary_expectation_min' => 15000000,
                'salary_expectation_max' => 22000000,
                'rating' => 3.8,
                'ai_score' => 75,
                'notes' => 'Ung vien tre, nhiet huyet, co kien thuc co ban ve digital marketing. Can dao tao them.',
                'job_index' => 1,
            ],
            [
                'name' => 'Trinh Minh Duc',
                'email' => '<EMAIL>',
                'phone' => '0901234567',
                'position' => 'Performance Marketing Specialist',
                'experience' => '5-10 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(6),
                'source' => 'LinkedIn',
                'location' => 'Hoan Kiem, Ha Noi',
                'salary_expectation_min' => 20000000,
                'salary_expectation_max' => ********,
                'rating' => 4.6,
                'ai_score' => 87,
                'notes' => 'Chuyen gia performance marketing, co kinh nghiem quan ly ngan sach lon va toi uu ROI.',
                'job_index' => 1,
            ],

            // Ung vien cho Ke toan truong
            [
                'name' => 'Hoang Van Duc',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'Chief Accountant',
                'experience' => '10+ years',
                'status' => 'interview',
                'applied_date' => now()->subDays(9),
                'source' => 'CareerBuilder',
                'location' => 'Hai Chau, Da Nang',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 4.8,
                'ai_score' => 94,
                'notes' => 'Co chung chi ke toan truong, kinh nghiem quan ly tai cong ty niem yet. Ung vien rat phu hop.',
                'job_index' => 2,
            ],
            [
                'name' => 'Phan Van Thanh',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'Senior Accountant',
                'experience' => '5-10 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(4),
                'source' => 'VietnamWorks',
                'location' => 'Son Tra, Da Nang',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 4.1,
                'ai_score' => 81,
                'notes' => 'Co kinh nghiem ke toan tai cong ty san xuat, hieu biet ve thue va bao cao tai chinh.',
                'job_index' => 2,
            ],

            // Ung vien cho Sales B2B
            [
                'name' => 'Vu Thi Lan',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'B2B Sales Manager',
                'experience' => '3-5 years',
                'status' => 'hired',
                'applied_date' => now()->subDays(20),
                'source' => 'Indeed',
                'location' => 'Quan 3, TP.HCM',
                'salary_expectation_min' => 15000000,
                'salary_expectation_max' => ********,
                'rating' => 4.6,
                'ai_score' => 89,
                'notes' => 'Co mang luoi khach hang rong, ky nang dam phan xuat sac. Da duoc tuyen dung.',
                'job_index' => 3,
            ],
            [
                'name' => 'Dang Minh Khoa',
                'email' => '<EMAIL>',
                'phone' => '0967890123',
                'position' => 'Business Development Executive',
                'experience' => '1-3 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(7),
                'source' => 'Company Website',
                'location' => 'Quan 1, TP.HCM',
                'salary_expectation_min' => 12000000,
                'salary_expectation_max' => ********,
                'rating' => 3.9,
                'ai_score' => 78,
                'notes' => 'Ung vien nang dong, co tinh than hoc hoi cao. Can danh gia ky nang sales.',
                'job_index' => 3,
            ],

            // Ung vien cho Graphic Designer
            [
                'name' => 'Bui Thi Thao',
                'email' => '<EMAIL>',
                'phone' => '0978901234',
                'position' => 'Senior Graphic Designer',
                'experience' => '3-5 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(10),
                'source' => 'Behance',
                'location' => 'Remote',
                'salary_expectation_min' => 12000000,
                'salary_expectation_max' => 20000000,
                'rating' => 4.4,
                'ai_score' => 86,
                'portfolio_url' => 'https://behance.net/thaobui',
                'notes' => 'Portfolio an tuong, co kinh nghiem lam viec voi cac thuong hieu lon. Ky nang design xuat sac.',
                'job_index' => 4,
            ],
            [
                'name' => 'Huynh Thi Tuyet',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'UI/UX Designer',
                'experience' => '3-5 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(8),
                'source' => 'Dribbble',
                'location' => 'Remote',
                'salary_expectation_min' => 15000000,
                'salary_expectation_max' => 22000000,
                'rating' => 4.3,
                'ai_score' => 84,
                'portfolio_url' => 'https://dribbble.com/tuyetdesign',
                'notes' => 'Co kinh nghiem UI/UX, portfolio dep va chuyen nghiep. Phu hop cho ca graphic design.',
                'job_index' => 4,
            ],

            // Ung vien cho HR Specialist
            [
                'name' => 'Ngo Thi Linh',
                'email' => '<EMAIL>',
                'phone' => '0990123456',
                'position' => 'HR Specialist',
                'experience' => '1-3 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(3),
                'source' => 'TopDev',
                'location' => 'Quan 2, TP.HCM',
                'salary_expectation_min' => 13000000,
                'salary_expectation_max' => ********,
                'rating' => 3.7,
                'ai_score' => 76,
                'notes' => 'Co kien thuc co ban ve HR, ky nang giao tiep tot. Can dao tao them ve luat lao dong.',
                'job_index' => 5,
            ],

            // Ung vien cho React Native
            [
                'name' => 'Cao Van Dat',
                'email' => '<EMAIL>',
                'phone' => '0967890123',
                'position' => 'React Native Developer',
                'experience' => '1-3 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(5),
                'source' => 'GitHub',
                'location' => 'Quan 1, TP.HCM',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 3.9,
                'ai_score' => 80,
                'github_url' => 'https://github.com/datmobile',
                'notes' => 'Co kinh nghiem React Native, portfolio mobile app an tuong. Can danh gia ky nang advanced.',
                'job_index' => 6,
            ],

            // Ung vien cho Data Analyst
            [
                'name' => 'Dinh Thi Nga',
                'email' => '<EMAIL>',
                'phone' => '0934567890',
                'position' => 'Data Analyst',
                'experience' => '1-3 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(4),
                'source' => 'Kaggle',
                'location' => 'Remote',
                'salary_expectation_min' => 16000000,
                'salary_expectation_max' => 24000000,
                'rating' => 4.0,
                'ai_score' => 83,
                'notes' => 'Co kinh nghiem voi SQL va Python, tham gia nhieu competition tren Kaggle. Tiem nang tot.',
                'job_index' => 7,
            ],

            // Ung vien cho Truong phong kinh doanh
            [
                'name' => 'Tran Quoc Bao',
                'email' => '<EMAIL>',
                'phone' => '0912345678',
                'position' => 'Sales Director',
                'experience' => '7-10 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(7),
                'source' => 'Headhunting',
                'location' => 'Quan 3, TP.HCM',
                'salary_expectation_min' => 35000000,
                'salary_expectation_max' => 55000000,
                'rating' => 4.7,
                'ai_score' => 92,
                'notes' => 'Ung vien co kinh nghiem quan ly doi ngu kinh doanh lon, dat doanh so cao. Ky nang lanh dao tot.',
                'job_index' => 8,
            ],

            // Ung vien cho QA/QC
            [
                'name' => 'Nguyen Thi Hong',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'QA Engineer',
                'experience' => '2-4 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(6),
                'source' => 'ITviec',
                'location' => 'Lien Chieu, Da Nang',
                'salary_expectation_min' => 14000000,
                'salary_expectation_max' => 22000000,
                'rating' => 4.1,
                'ai_score' => 82,
                'notes' => 'Co kinh nghiem QA/QC cho cac du an web va mobile. Ky nang viet test case tot.',
                'job_index' => 9,
            ],
        ];

        foreach ($candidatesData as $candidateData) {
            $jobIndex = $candidateData['job_index'];
            $job = $jobs[$jobIndex];

            unset($candidateData['job_index']);

            $candidate = Candidate::create([
                ...$candidateData,
                'job_posting_id' => $job->id,
                'created_by' => $users['recruiter1']->id,
                'assigned_to' => $users['recruiter1']->id,
                'salary_currency' => 'VND',
            ]);

            // Tao education, work history, skills, tags
            $this->createCandidateDetails($candidate);

            // Tao status history
            $candidate->statusHistory()->create([
                'old_status' => null,
                'new_status' => $candidate->status,
                'changed_by' => $users['recruiter1']->id,
                'notes' => 'Ho so ung vien duoc tao va phan loai ban dau',
            ]);

            $candidates[] = $candidate;
        }

        return $candidates;
    }

    private function createInterviews($candidates, $jobs, $interviewers, $users)
    {
        $this->command->info('Tao interviews...');

        $interviews = [];

        // Tao interviews cho cac candidates co status phu hop
        $interviewCandidates = collect($candidates)->filter(function($candidate) {
            return in_array($candidate->status, ['interview', 'offer', 'hired']);
        });

        foreach ($interviewCandidates as $index => $candidate) {
            // Chon interviewer phu hop voi job department
            $job = $candidate->jobPosting;
            $suitableInterviewers = collect($interviewers)->filter(function($interviewer) use ($job) {
                return $interviewer->department === $job->department ||
                       $interviewer->user->department === $job->department;
            });

            if ($suitableInterviewers->isEmpty()) {
                $suitableInterviewers = collect($interviewers);
            }

            $interviewer = $suitableInterviewers->random();

            // Tao interview date trong qua khu hoac tuong lai
            $interviewDate = $candidate->status === 'hired'
                ? now()->subDays(rand(5, 15))
                : ($candidate->status === 'offer'
                    ? now()->subDays(rand(1, 7))
                    : now()->addDays(rand(1, 10)));

            $interviewTypes = ['screening', 'technical', 'cultural', 'final'];
            $interviewType = $interviewTypes[array_rand($interviewTypes)];
            $meetingType = rand(0, 1) ? 'video' : 'in-person';

            $interview = Interview::create([
                'candidate_id' => $candidate->id,
                'job_posting_id' => $job->id,
                'interviewer_id' => $interviewer->id,
                'date' => $interviewDate->format('Y-m-d'),
                'time' => sprintf('%02d:00', rand(9, 16)),
                'duration' => rand(3, 6) * 15, // 45-90 minutes
                'type' => $meetingType,
                'status' => $candidate->status === 'hired' ? 'completed' :
                           ($candidate->status === 'offer' ? 'completed' : 'scheduled'),
                'interview_type' => $interviewType,
                'meeting_link' => $meetingType === 'video' ? 'https://meet.google.com/abc-defg-hij' : null,
                'location' => $meetingType === 'in-person' ? 'Phong hop A - Tang 5' : null,
                'notes' => "Phong van {$interviewType} cho vi tri {$job->title}",
                'agenda' => json_encode([
                    'Gioi thieu ban than va cong ty',
                    'Danh gia kinh nghiem va ky nang',
                    'Thao luan ve du an da lam',
                    'Cau hoi tu ung vien',
                    'Thong tin ve buoc tiep theo'
                ]),
                'round' => rand(1, 3),
                'created_by' => $users['recruiter1']->id,
            ]);

            $interviews[] = $interview;
        }

        return $interviews;
    }

    private function createInterviewFeedback($interviews, $interviewers)
    {
        $this->command->info('Tao interview feedback...');

        // Tao feedback cho cac interview da completed
        $completedInterviews = collect($interviews)->filter(function($interview) {
            return $interview->status === 'completed';
        });

        foreach ($completedInterviews as $interview) {
            $rating = rand(30, 50) / 10; // 3.0 - 5.0
            $recommend = $rating >= 4.0;

            $strengths = [
                'Kinh nghiem lam viec phong phu',
                'Ky nang giao tiep tot',
                'Tu duy logic va phan tich',
                'Kha nang hoc hoi nhanh',
                'Thai do tich cuc va nhiet huyet',
                'Kien thuc chuyen mon vung vang',
                'Kha nang lam viec nhom',
                'Tinh chu dong cao'
            ];

            $concerns = [
                'Can cai thien ky nang thuyet trinh',
                'Kinh nghiem voi cong nghe moi con han che',
                'Can thoi gian de lam quen voi quy trinh',
                'Ky nang tieng Anh can cai thien',
                'Can phat trien them soft skills'
            ];

            InterviewFeedback::create([
                'interview_id' => $interview->id,
                'interviewer_id' => $interview->interviewer_id,
                'rating' => $rating,
                'comments' => $recommend
                    ? 'Ung vien co tiem nang tot, phu hop voi vi tri. Kien thuc chuyen mon vung vang va thai do lam viec tich cuc.'
                    : 'Ung vien co mot so diem tot nhung can cai thien them de phu hop voi yeu cau cong viec.',
                'recommend' => $recommend,
                'strengths' => json_encode(array_slice($strengths, 0, rand(2, 4))),
                'concerns' => json_encode($recommend ? [] : array_slice($concerns, 0, rand(1, 3))),
                'next_round_recommendation' => $recommend ?
                    (['technical', 'cultural', 'final'][array_rand(['technical', 'cultural', 'final'])]) : null,
                'technical_score' => rand(60, 95),
                'communication_score' => rand(70, 95),
                'cultural_fit_score' => rand(65, 90),
            ]);
        }
    }
