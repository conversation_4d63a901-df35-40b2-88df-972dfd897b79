<?php

namespace Database\Seeders;

use App\Models\Candidate;
use App\Models\JobPosting;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdditionalVietnameseDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Tạo thêm dữ liệu ứng viên tiếng Việt...');

        $recruiter = User::where('email', '<EMAIL>')->first();
        $jobs = JobPosting::whereIn('title', [
            'Lậ<PERSON> Tr<PERSON><PERSON>iên PHP Senior',
            'Chuyên Viên Marketing Digital',
            'Kế Toán Trưởng',
            'Nhân Viên Kinh <PERSON>h B2B',
            '<PERSON><PERSON><PERSON><PERSON> (Graphic Designer)'
        ])->get();

        // Thêm 15 ứng viên nữa với dữ liệu đa dạng
        $additionalCandidates = [
            // Thêm ứng viên cho PHP Developer
            [
                'name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'phone' => '0989012345',
                'position' => 'Full Stack Developer',
                'experience' => '3-5 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(1),
                'source' => 'ITviec',
                'location' => 'Quận Bình Thạnh, TP.HCM',
                'salary_expectation_min' => 22000000,
                'salary_expectation_max' => 32000000,
                'rating' => 4.0,
                'ai_score' => 79,
                'notes' => 'Có kinh nghiệm fullstack, từng làm việc tại startup.',
                'job_title' => 'Lập Trình Viên PHP Senior',
            ],
            [
                'name' => 'Ngô Thị Linh',
                'email' => '<EMAIL>',
                'phone' => '0990123456',
                'position' => 'Backend Developer',
                'experience' => '1-3 years',
                'status' => 'rejected',
                'applied_date' => now()->subDays(10),
                'source' => 'TopDev',
                'location' => 'Quận 2, TP.HCM',
                'salary_expectation_min' => 18000000,
                'salary_expectation_max' => 25000000,
                'rating' => 3.2,
                'ai_score' => 65,
                'notes' => 'Kinh nghiệm chưa đủ cho vị trí senior.',
                'job_title' => 'Lập Trình Viên PHP Senior',
            ],

            // Thêm ứng viên cho Marketing
            [
                'name' => 'Trịnh Minh Đức',
                'email' => '<EMAIL>',
                'phone' => '0901234567',
                'position' => 'Performance Marketing Manager',
                'experience' => '5-10 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(4),
                'source' => 'LinkedIn',
                'location' => 'Hoàn Kiếm, Hà Nội',
                'salary_expectation_min' => 20000000,
                'salary_expectation_max' => 30000000,
                'rating' => 4.6,
                'ai_score' => 87,
                'notes' => 'Có kinh nghiệm quản lý ngân sách lớn, chuyên về performance marketing.',
                'job_title' => 'Chuyên Viên Marketing Digital',
            ],
            [
                'name' => 'Võ Thị Hạnh',
                'email' => '<EMAIL>',
                'phone' => '0912345678',
                'position' => 'Social Media Specialist',
                'experience' => '1-3 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(6),
                'source' => 'Facebook Jobs',
                'location' => 'Đống Đa, Hà Nội',
                'salary_expectation_min' => 12000000,
                'salary_expectation_max' => 18000000,
                'rating' => 3.7,
                'ai_score' => 72,
                'notes' => 'Chuyên về social media, có nhiều case study thành công.',
                'job_title' => 'Chuyên Viên Marketing Digital',
            ],

            // Thêm ứng viên cho Kế toán
            [
                'name' => 'Phan Văn Thành',
                'email' => '<EMAIL>',
                'phone' => '0923456789',
                'position' => 'Kế toán tổng hợp',
                'experience' => '5-10 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(3),
                'source' => 'VietnamWorks',
                'location' => 'Sơn Trà, Đà Nẵng',
                'salary_expectation_min' => 18000000,
                'salary_expectation_max' => ********,
                'rating' => 4.1,
                'ai_score' => 81,
                'notes' => 'Có kinh nghiệm kế toán tại công ty sản xuất, hiểu biết về thuế.',
                'job_title' => 'Kế Toán Trưởng',
            ],

            // Thêm ứng viên cho Sales
            [
                'name' => 'Đinh Thị Nga',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'Key Account Manager',
                'experience' => '3-5 years',
                'status' => 'offer',
                'applied_date' => now()->subDays(12),
                'source' => 'Referral',
                'location' => 'Quận 10, TP.HCM',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 4.4,
                'ai_score' => 85,
                'notes' => 'Có kinh nghiệm quản lý key account, mối quan hệ tốt với khách hàng.',
                'job_title' => 'Nhân Viên Kinh Doanh B2B',
            ],
            [
                'name' => 'Lê Quang Minh',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'position' => 'Business Development',
                'experience' => '1-3 years',
                'status' => 'applied',
                'applied_date' => now()->subDays(2),
                'source' => 'Indeed',
                'location' => 'Quận 7, TP.HCM',
                'salary_expectation_min' => ********,
                'salary_expectation_max' => ********,
                'rating' => 3.6,
                'ai_score' => 74,
                'notes' => 'Ứng viên trẻ, có tiềm năng phát triển.',
                'job_title' => 'Nhân Viên Kinh Doanh B2B',
            ],

            // Thêm ứng viên cho Designer
            [
                'name' => 'Huỳnh Thị Tuyết',
                'email' => '<EMAIL>',
                'phone' => '0956789012',
                'position' => 'UI/UX Designer',
                'experience' => '3-5 years',
                'status' => 'interview',
                'applied_date' => now()->subDays(5),
                'source' => 'Dribbble',
                'location' => 'Remote',
                'salary_expectation_min' => 15000000,
                'salary_expectation_max' => 22000000,
                'rating' => 4.3,
                'ai_score' => 84,
                'portfolio_url' => 'https://dribbble.com/tuyetdesign',
                'notes' => 'Có kinh nghiệm UI/UX, portfolio đẹp và chuyên nghiệp.',
                'job_title' => 'Thiết Kế Đồ Họa (Graphic Designer)',
            ],
            [
                'name' => 'Cao Văn Đạt',
                'email' => '<EMAIL>',
                'phone' => '0967890123',
                'position' => 'Motion Graphics Designer',
                'experience' => '1-3 years',
                'status' => 'screening',
                'applied_date' => now()->subDays(8),
                'source' => 'Behance',
                'location' => 'Quận 1, TP.HCM',
                'salary_expectation_min' => 11000000,
                'salary_expectation_max' => 17000000,
                'rating' => 3.9,
                'ai_score' => 76,
                'portfolio_url' => 'https://behance.net/datmotion',
                'notes' => 'Chuyên về motion graphics, có nhiều video viral.',
                'job_title' => 'Thiết Kế Đồ Họa (Graphic Designer)',
            ],
        ];

        foreach ($additionalCandidates as $candidateData) {
            $job = $jobs->where('title', $candidateData['job_title'])->first();
            if (!$job) continue;

            unset($candidateData['job_title']);

            $candidate = Candidate::create([
                ...$candidateData,
                'job_posting_id' => $job->id,
                'created_by' => $recruiter->id,
                'assigned_to' => $recruiter->id,
                'salary_currency' => 'VND',
            ]);

            // Tạo lịch sử trạng thái
            $candidate->statusHistory()->create([
                'old_status' => null,
                'new_status' => $candidate->status,
                'changed_by' => $recruiter->id,
                'notes' => 'Hồ sơ ứng viên được tạo',
            ]);
        }

        $this->command->info('Hoàn thành tạo thêm dữ liệu!');
        $this->command->info('Đã tạo thêm: ' . count($additionalCandidates) . ' ứng viên');
    }
}
