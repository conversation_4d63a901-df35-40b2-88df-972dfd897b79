<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Candidate permissions
            'manage_candidates',
            'view_candidates',
            'create_candidates',
            'edit_candidates',
            'delete_candidates',
            'assign_candidates',
            'change_candidate_status',

            // Job posting permissions
            'manage_jobs',
            'view_jobs',
            'create_jobs',
            'edit_jobs',
            'delete_jobs',
            'publish_jobs',

            // Interview permissions
            'manage_interviews',
            'view_interviews',
            'schedule_interviews',
            'conduct_interviews',
            'provide_feedback',
            'cancel_interviews',

            // Analytics permissions
            'view_analytics',
            'view_reports',
            'export_data',

            // User management permissions
            'manage_users',
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',

            // System permissions
            'manage_settings',
            'view_activity_logs',
            'manage_templates',
            'send_messages',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Admin role - has all permissions
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        // Recruiter role
        $recruiterRole = Role::create(['name' => 'recruiter']);
        $recruiterRole->givePermissionTo([
            'manage_candidates',
            'view_candidates',
            'create_candidates',
            'edit_candidates',
            'assign_candidates',
            'change_candidate_status',
            'view_jobs',
            'create_jobs',
            'edit_jobs',
            'manage_interviews',
            'view_interviews',
            'schedule_interviews',
            'cancel_interviews',
            'view_analytics',
            'view_reports',
            'send_messages',
            'manage_templates',
        ]);

        // Hiring Manager role
        $hiringManagerRole = Role::create(['name' => 'hiring_manager']);
        $hiringManagerRole->givePermissionTo([
            'view_candidates',
            'edit_candidates',
            'change_candidate_status',
            'manage_jobs',
            'view_jobs',
            'create_jobs',
            'edit_jobs',
            'publish_jobs',
            'view_interviews',
            'schedule_interviews',
            'provide_feedback',
            'view_analytics',
            'view_reports',
            'export_data',
            'send_messages',
        ]);

        // Interviewer role
        $interviewerRole = Role::create(['name' => 'interviewer']);
        $interviewerRole->givePermissionTo([
            'view_candidates',
            'view_jobs',
            'view_interviews',
            'conduct_interviews',
            'provide_feedback',
            'send_messages',
        ]);

        $this->command->info('Roles and permissions created successfully!');
    }
}
