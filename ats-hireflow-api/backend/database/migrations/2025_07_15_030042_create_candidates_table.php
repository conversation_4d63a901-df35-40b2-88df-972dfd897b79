<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('candidates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone', 20)->nullable();
            $table->string('position');
            $table->string('experience', 100)->nullable();
            $table->enum('status', ['sourced', 'applied', 'screening', 'interview', 'offer', 'hired', 'rejected'])->default('applied');
            $table->date('applied_date');
            $table->string('source', 100)->nullable();
            $table->string('location')->nullable();
            $table->decimal('salary_expectation_min', 15, 2)->nullable();
            $table->decimal('salary_expectation_max', 15, 2)->nullable();
            $table->string('salary_currency', 3)->default('VND');
            $table->decimal('rating', 3, 2)->nullable();
            $table->integer('ai_score')->nullable();
            $table->string('linkedin_url', 500)->nullable();
            $table->string('github_url', 500)->nullable();
            $table->string('portfolio_url', 500)->nullable();
            $table->string('avatar', 500)->nullable();
            $table->string('resume_url', 500)->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('restrict');
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['email', 'job_posting_id']);
            $table->index(['status', 'applied_date']);
            $table->index(['source', 'location']);
            $table->index('ai_score');
            $table->index('assigned_to');
            $table->fullText(['name', 'email', 'position']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('candidates');
    }
};
