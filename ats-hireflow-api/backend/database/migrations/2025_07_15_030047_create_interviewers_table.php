<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interviewers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->unique()->constrained('users')->onDelete('cascade');
            $table->string('department', 100)->nullable();
            $table->json('expertise')->nullable(); // Array of expertise areas
            $table->boolean('is_active')->default(true);
            $table->string('location')->nullable();
            $table->integer('max_interviews_per_day')->default(4);
            $table->json('availability')->nullable(); // Weekly availability schedule
            $table->json('time_slots')->nullable(); // Available time slots
            $table->string('timezone', 50)->default('Asia/Ho_Chi_Minh');
            $table->timestamps();

            // Indexes
            $table->index(['department', 'is_active']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interviewers');
    }
};
