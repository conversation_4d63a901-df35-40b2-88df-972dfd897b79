<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Add new JSON columns to candidates table
        Schema::table('candidates', function (Blueprint $table) {
            $table->json('skills')->nullable()->after('notes');
            $table->json('tags')->nullable()->after('skills');
        });

        // Step 2: Migrate existing data from relational tables to JSON columns
        $this->migrateExistingData();

        // Step 3: Drop the old relational tables (commented out for safety)
        // Schema::dropIfExists('candidate_skills');
        // Schema::dropIfExists('candidate_tags');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the old tables
        Schema::create('candidate_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('candidate_id')->constrained('candidates')->onDelete('cascade');
            $table->string('skill_name', 100);
            $table->enum('proficiency_level', ['beginner', 'intermediate', 'advanced', 'expert'])->nullable();
            $table->integer('years_of_experience')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamps();

            $table->index(['candidate_id', 'skill_name']);
            $table->unique(['candidate_id', 'skill_name']);
        });

        Schema::create('candidate_tags', function (Blueprint $table) {
            $table->id();
            $table->foreignId('candidate_id')->constrained('candidates')->onDelete('cascade');
            $table->string('tag_name', 100);
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            $table->index(['candidate_id', 'tag_name']);
            $table->unique(['candidate_id', 'tag_name']);
        });

        // Migrate data back from JSON to relational tables
        $this->migrateDataBack();

        // Remove JSON columns
        Schema::table('candidates', function (Blueprint $table) {
            $table->dropColumn(['skills', 'tags']);
        });
    }

    /**
     * Migrate existing relational data to JSON format
     */
    private function migrateExistingData(): void
    {
        // Migrate skills
        $candidates = DB::table('candidates')->get();

        foreach ($candidates as $candidate) {
            // Get skills for this candidate
            $skills = DB::table('candidate_skills')
                ->where('candidate_id', $candidate->id)
                ->pluck('skill_name')
                ->toArray();

            // Get tags for this candidate
            $tags = DB::table('candidate_tags')
                ->where('candidate_id', $candidate->id)
                ->pluck('tag_name')
                ->toArray();

            // Update candidate with JSON data
            DB::table('candidates')
                ->where('id', $candidate->id)
                ->update([
                    'skills' => json_encode($skills),
                    'tags' => json_encode($tags),
                ]);
        }
    }

    /**
     * Migrate JSON data back to relational tables (for rollback)
     */
    private function migrateDataBack(): void
    {
        $candidates = DB::table('candidates')->get();

        foreach ($candidates as $candidate) {
            // Migrate skills back
            if ($candidate->skills) {
                $skills = json_decode($candidate->skills, true);
                if (is_array($skills)) {
                    foreach ($skills as $skill) {
                        DB::table('candidate_skills')->insert([
                            'candidate_id' => $candidate->id,
                            'skill_name' => $skill,
                            'proficiency_level' => null,
                            'years_of_experience' => null,
                            'is_verified' => false,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            // Migrate tags back
            if ($candidate->tags) {
                $tags = json_decode($candidate->tags, true);
                if (is_array($tags)) {
                    foreach ($tags as $tag) {
                        DB::table('candidate_tags')->insert([
                            'candidate_id' => $candidate->id,
                            'tag_name' => $tag,
                            'created_by' => $candidate->created_by,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }
        }
    }
};
