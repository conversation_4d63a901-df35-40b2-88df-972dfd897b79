<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['email', 'note', 'sms'])->default('email');
            $table->enum('category', ['interview', 'offer', 'feedback', 'reminder', 'rejection', 'general']);
            $table->foreignId('candidate_id')->nullable()->constrained('candidates')->onDelete('set null');
            $table->foreignId('job_posting_id')->nullable()->constrained('job_postings')->onDelete('set null');
            $table->foreignId('template_id')->nullable()->constrained('message_templates')->onDelete('set null');
            $table->string('to_email')->nullable();
            $table->string('to_phone', 20)->nullable();
            $table->string('subject', 500)->nullable();
            $table->text('content');
            $table->enum('status', ['draft', 'sent', 'delivered', 'read', 'failed'])->default('draft');
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->text('error_message')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            $table->index(['candidate_id', 'type', 'status']);
            $table->index(['job_posting_id', 'category']);
            $table->index(['status', 'sent_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
