<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Drop candidate related tables that have been replaced by JSON/TEXT fields:
     * - candidate_skills -> replaced by 'skills' JSON field in candidates table
     * - candidate_tags -> replaced by 'tags' JSON field in candidates table
     * - candidate_education -> replaced by 'education' TEXT field in candidates table
     * - candidate_work_history -> replaced by 'work_history' TEXT field in candidates table
     */
    public function up(): void
    {
        // Drop candidate related tables in reverse dependency order
        Schema::dropIfExists('candidate_skills');
        Schema::dropIfExists('candidate_tags');
        Schema::dropIfExists('candidate_education');
        Schema::dropIfExists('candidate_work_history');
    }

    /**
     * Reverse the migrations.
     *
     * Recreate the dropped tables for rollback functionality.
     */
    public function down(): void
    {
        // Recreate candidate_skills table
        Schema::create('candidate_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('candidate_id')->constrained('candidates')->onDelete('cascade');
            $table->string('skill_name', 100);
            $table->enum('proficiency_level', ['beginner', 'intermediate', 'advanced', 'expert'])->nullable();
            $table->integer('years_of_experience')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamps();

            $table->index(['candidate_id', 'skill_name']);
            $table->unique(['candidate_id', 'skill_name']);
        });

        // Recreate candidate_tags table
        Schema::create('candidate_tags', function (Blueprint $table) {
            $table->id();
            $table->foreignId('candidate_id')->constrained('candidates')->onDelete('cascade');
            $table->string('tag_name', 100);
            $table->string('tag_color', 7)->default('#3B82F6'); // Default blue color
            $table->timestamps();

            $table->index(['candidate_id', 'tag_name']);
            $table->unique(['candidate_id', 'tag_name']);
        });

        // Recreate candidate_education table
        Schema::create('candidate_education', function (Blueprint $table) {
            $table->id();
            $table->foreignId('candidate_id')->constrained('candidates')->onDelete('cascade');
            $table->string('institution', 255);
            $table->string('degree', 255);
            $table->string('field_of_study', 255)->nullable();
            $table->integer('start_year')->nullable();
            $table->integer('end_year')->nullable();
            $table->string('gpa', 10)->nullable();
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['candidate_id', 'sort_order']);
        });

        // Recreate candidate_work_history table
        Schema::create('candidate_work_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('candidate_id')->constrained('candidates')->onDelete('cascade');
            $table->string('company', 255);
            $table->string('position', 255);
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->boolean('is_current')->default(false);
            $table->text('description')->nullable();
            $table->text('achievements')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['candidate_id', 'sort_order']);
        });
    }
};
