<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Add new TEXT columns for simplified education and work_history
        Schema::table('candidates', function (Blueprint $table) {
            $table->text('education_text')->nullable()->after('tags');
            $table->text('work_history_text')->nullable()->after('education_text');
        });

        // Step 2: Migrate existing JSON data to text format
        $this->migrateExistingData();

        // Step 3: Drop the old JSON columns and rename new ones
        Schema::table('candidates', function (Blueprint $table) {
            // Drop old columns if they exist
            if (Schema::hasColumn('candidates', 'education')) {
                $table->dropColumn('education');
            }
            if (Schema::hasColumn('candidates', 'work_history')) {
                $table->dropColumn('work_history');
            }
        });

        // Step 4: Rename new columns to original names
        Schema::table('candidates', function (Blueprint $table) {
            $table->renameColumn('education_text', 'education');
            $table->renameColumn('work_history_text', 'work_history');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Step 1: Add temporary columns for JSON data
        Schema::table('candidates', function (Blueprint $table) {
            $table->json('education_json')->nullable()->after('tags');
            $table->json('work_history_json')->nullable()->after('education_json');
        });

        // Step 2: Migrate text data back to JSON format
        $this->migrateDataBack();

        // Step 3: Drop text columns and rename JSON columns
        Schema::table('candidates', function (Blueprint $table) {
            $table->dropColumn(['education', 'work_history']);
            $table->renameColumn('education_json', 'education');
            $table->renameColumn('work_history_json', 'work_history');
        });
    }

    /**
     * Migrate existing JSON data to text format
     */
    private function migrateExistingData(): void
    {
        $candidates = DB::table('candidates')->get();

        foreach ($candidates as $candidate) {
            $educationText = '';
            $workHistoryText = '';

            // Convert education JSON to text (if column exists)
            if (property_exists($candidate, 'education') && $candidate->education) {
                $educationData = json_decode($candidate->education, true);
                if (is_array($educationData)) {
                    $educationParts = [];
                    foreach ($educationData as $edu) {
                        $parts = [];
                        if (isset($edu['degree'])) $parts[] = $edu['degree'];
                        if (isset($edu['institution'])) $parts[] = 'tại ' . $edu['institution'];
                        if (isset($edu['field_of_study'])) $parts[] = 'chuyên ngành ' . $edu['field_of_study'];
                        if (isset($edu['start_year']) && isset($edu['end_year'])) {
                            $parts[] = '(' . $edu['start_year'] . '-' . $edu['end_year'] . ')';
                        }
                        if (isset($edu['gpa'])) $parts[] = 'GPA: ' . $edu['gpa'];

                        if (!empty($parts)) {
                            $educationParts[] = implode(' ', $parts);
                        }
                    }
                    $educationText = implode('; ', $educationParts);
                }
            }

            // Convert work_history JSON to text (if column exists)
            if (property_exists($candidate, 'work_history') && $candidate->work_history) {
                $workHistoryData = json_decode($candidate->work_history, true);
                if (is_array($workHistoryData)) {
                    $workHistoryParts = [];
                    foreach ($workHistoryData as $work) {
                        $parts = [];
                        if (isset($work['position'])) $parts[] = $work['position'];
                        if (isset($work['company'])) $parts[] = 'tại ' . $work['company'];
                        if (isset($work['start_date']) && isset($work['end_date'])) {
                            $endDate = $work['is_current'] ?? false ? 'hiện tại' : $work['end_date'];
                            $parts[] = '(' . $work['start_date'] . ' - ' . $endDate . ')';
                        }
                        if (isset($work['description'])) $parts[] = '- ' . $work['description'];

                        if (!empty($parts)) {
                            $workHistoryParts[] = implode(' ', $parts);
                        }
                    }
                    $workHistoryText = implode('; ', $workHistoryParts);
                }
            }

            // Update candidate with text data
            DB::table('candidates')
                ->where('id', $candidate->id)
                ->update([
                    'education_text' => $educationText,
                    'work_history_text' => $workHistoryText,
                ]);
        }
    }

    /**
     * Migrate text data back to JSON format (for rollback)
     */
    private function migrateDataBack(): void
    {
        $candidates = DB::table('candidates')->get();

        foreach ($candidates as $candidate) {
            // For rollback, we'll create simple JSON structure from text
            $educationJson = [];
            $workHistoryJson = [];

            if ($candidate->education) {
                $educationJson[] = [
                    'institution' => 'Converted from text',
                    'degree' => $candidate->education,
                    'field_of_study' => null,
                    'start_year' => null,
                    'end_year' => null,
                    'gpa' => null
                ];
            }

            if ($candidate->work_history) {
                $workHistoryJson[] = [
                    'company' => 'Converted from text',
                    'position' => $candidate->work_history,
                    'start_date' => null,
                    'end_date' => null,
                    'is_current' => false,
                    'description' => null
                ];
            }

            DB::table('candidates')
                ->where('id', $candidate->id)
                ->update([
                    'education_json' => !empty($educationJson) ? json_encode($educationJson) : null,
                    'work_history_json' => !empty($workHistoryJson) ? json_encode($workHistoryJson) : null,
                ]);
        }
    }
};
