<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interview_feedback', function (Blueprint $table) {
            $table->id();
            $table->foreignId('interview_id')->constrained('interviews')->onDelete('cascade');
            $table->foreignId('interviewer_id')->constrained('interviewers')->onDelete('restrict');
            $table->decimal('rating', 3, 2)->nullable();
            $table->text('comments')->nullable();
            $table->boolean('recommend')->nullable();
            $table->json('strengths')->nullable(); // Array of strengths
            $table->json('concerns')->nullable(); // Array of concerns
            $table->enum('next_round_recommendation', ['screening', 'technical', 'case-study', 'portfolio', 'cultural', 'final', 'offer', 'reject'])->nullable();
            $table->integer('technical_score')->nullable();
            $table->integer('communication_score')->nullable();
            $table->integer('cultural_fit_score')->nullable();
            $table->timestamps();

            $table->unique(['interview_id', 'interviewer_id']);
            $table->index('interview_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interview_feedback');
    }
};
