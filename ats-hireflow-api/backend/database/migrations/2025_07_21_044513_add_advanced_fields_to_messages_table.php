<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            // Add new fields for advanced messaging features
            $table->foreignId('parent_message_id')->nullable()->constrained('messages')->onDelete('set null')->after('template_id');
            $table->string('thread_id')->nullable()->after('parent_message_id');
            $table->string('to_name')->nullable()->after('to_phone');
            $table->string('from_email')->nullable()->after('to_name');
            $table->string('from_name')->nullable()->after('from_email');
            $table->tinyInteger('priority')->default(5)->after('status'); // 1-10 scale
            $table->timestamp('scheduled_at')->nullable()->after('priority');
            $table->json('metadata')->nullable()->after('error_message');
            $table->string('external_id')->nullable()->after('metadata'); // External service ID

            // Update status enum to include 'queued'
            $table->dropColumn('status');
        });

        // Add the updated status column
        Schema::table('messages', function (Blueprint $table) {
            $table->enum('status', ['draft', 'queued', 'sent', 'delivered', 'read', 'failed'])->default('draft')->after('content');
        });

        // Add indexes for new fields
        Schema::table('messages', function (Blueprint $table) {
            $table->index(['parent_message_id', 'thread_id'], 'msg_parent_thread_idx');
            $table->index(['status', 'priority', 'scheduled_at'], 'msg_status_priority_scheduled_idx');
            $table->index(['external_id'], 'msg_external_id_idx');
            $table->index(['thread_id'], 'msg_thread_id_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex('msg_parent_thread_idx');
            $table->dropIndex('msg_status_priority_scheduled_idx');
            $table->dropIndex('msg_external_id_idx');
            $table->dropIndex('msg_thread_id_idx');

            // Drop foreign key and columns
            $table->dropForeign(['parent_message_id']);
            $table->dropColumn([
                'parent_message_id',
                'thread_id',
                'to_name',
                'from_email',
                'from_name',
                'priority',
                'scheduled_at',
                'metadata',
                'external_id'
            ]);

            // Revert status enum
            $table->dropColumn('status');
        });

        Schema::table('messages', function (Blueprint $table) {
            $table->enum('status', ['draft', 'sent', 'delivered', 'read', 'failed'])->default('draft')->after('content');
        });
    }
};
