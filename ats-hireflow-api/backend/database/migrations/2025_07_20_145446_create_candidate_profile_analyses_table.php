<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('candidate_profile_analyses', function (Blueprint $table) {
            $table->id();

            // Foreign keys
            $table->foreignId('candidate_id')->constrained('candidates')->onDelete('cascade');
            $table->foreignId('job_posting_id')->nullable()->constrained('job_postings')->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');

            // Analysis metadata
            $table->enum('analysis_type', ['resume_extraction', 'ai_analysis', 'job_matching'])->default('resume_extraction');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->string('external_service_id')->nullable(); // ID from external AI service

            // Extracted candidate information from resume
            $table->string('extracted_name')->nullable();
            $table->string('extracted_email')->nullable();
            $table->string('extracted_phone')->nullable();
            $table->text('extracted_address')->nullable();
            $table->json('extracted_skills')->nullable(); // Array of skills
            $table->json('extracted_experience')->nullable(); // Work experience data
            $table->json('extracted_education')->nullable(); // Education data

            // AI analysis results
            $table->text('ai_summary')->nullable(); // AI-generated summary
            $table->json('strengths')->nullable(); // Array of strengths
            $table->json('weaknesses')->nullable(); // Array of weaknesses
            $table->json('improvement_areas')->nullable(); // Areas for improvement
            $table->json('recommendations')->nullable(); // AI recommendations

            // Scoring (0-100 scale)
            $table->integer('overall_score')->nullable();
            $table->integer('skills_score')->nullable();
            $table->integer('experience_score')->nullable();
            $table->integer('education_score')->nullable();
            $table->integer('cultural_fit_score')->nullable();

            // Job matching specific data
            $table->json('job_match_details')->nullable(); // Detailed job matching analysis
            $table->json('missing_requirements')->nullable(); // Requirements not met
            $table->json('matching_criteria')->nullable(); // Criteria that match

            // Processing metadata
            $table->timestamp('analysis_started_at')->nullable();
            $table->timestamp('analysis_completed_at')->nullable();
            $table->text('error_message')->nullable(); // Error details if failed
            $table->json('processing_logs')->nullable(); // Processing logs for debugging

            $table->timestamps();

            // Indexes for performance
            $table->index(['candidate_id', 'analysis_type'], 'cpa_candidate_type_idx');
            $table->index(['job_posting_id', 'overall_score'], 'cpa_job_score_idx');
            $table->index(['status', 'created_at'], 'cpa_status_created_idx');
            $table->index('external_service_id', 'cpa_external_id_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('candidate_profile_analyses');
    }
};
