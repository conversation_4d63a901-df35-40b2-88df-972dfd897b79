<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['admin', 'recruiter', 'hiring_manager', 'interviewer'])->default('recruiter')->after('password');
            $table->string('department', 100)->nullable()->after('role');
            $table->string('title', 100)->nullable()->after('department');
            $table->string('phone', 20)->nullable()->after('title');
            $table->string('avatar', 500)->nullable()->after('phone');
            $table->boolean('is_active')->default(true)->after('avatar');
            $table->timestamp('last_login_at')->nullable()->after('is_active');

            // Indexes
            $table->index(['role', 'is_active']);
            $table->index('department');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role', 'is_active']);
            $table->dropIndex(['department']);
            $table->dropColumn([
                'role', 'department', 'title', 'phone', 'avatar',
                'is_active', 'last_login_at'
            ]);
        });
    }
};
