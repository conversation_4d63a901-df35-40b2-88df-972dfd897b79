<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Fix foreign key constraint for job_posting_id in candidates table.
     * Change from onDelete('restrict') to onDelete('set null') to allow
     * job posting deletion when candidates are assigned.
     */
    public function up(): void
    {
        Schema::table('candidates', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['job_posting_id']);

            // Add new foreign key constraint with SET NULL on delete
            $table->foreign('job_posting_id')
                  ->references('id')
                  ->on('job_postings')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * Restore the original RESTRICT constraint.
     */
    public function down(): void
    {
        Schema::table('candidates', function (Blueprint $table) {
            // Drop the SET NULL constraint
            $table->dropForeign(['job_posting_id']);

            // Restore original RESTRICT constraint
            $table->foreign('job_posting_id')
                  ->references('id')
                  ->on('job_postings')
                  ->onDelete('restrict');
        });
    }
};
