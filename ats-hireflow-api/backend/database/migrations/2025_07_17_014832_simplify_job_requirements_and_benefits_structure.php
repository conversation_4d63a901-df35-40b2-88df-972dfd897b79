<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Add new JSON columns to job_postings table
        Schema::table('job_postings', function (Blueprint $table) {
            $table->json('requirements')->nullable()->after('company_culture');
            $table->json('benefits')->nullable()->after('requirements');
        });

        // Step 2: Migrate existing data from relational tables to JSON columns
        $this->migrateExistingData();

        // Step 3: Drop the old relational tables (commented out for safety)
        // Schema::dropIfExists('job_requirements');
        // Schema::dropIfExists('job_benefits');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the old tables
        Schema::create('job_requirements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('cascade');
            $table->text('requirement_text');
            $table->boolean('is_mandatory')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['job_posting_id', 'sort_order']);
        });

        Schema::create('job_benefits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('cascade');
            $table->text('benefit_text');
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['job_posting_id', 'sort_order']);
        });

        // Migrate data back from JSON to relational tables
        $this->migrateDataBack();

        // Remove JSON columns
        Schema::table('job_postings', function (Blueprint $table) {
            $table->dropColumn(['requirements', 'benefits']);
        });
    }

    /**
     * Migrate existing relational data to JSON format
     */
    private function migrateExistingData(): void
    {
        // Migrate requirements and benefits
        $jobPostings = DB::table('job_postings')->get();

        foreach ($jobPostings as $jobPosting) {
            // Get requirements for this job posting
            $requirements = DB::table('job_requirements')
                ->where('job_posting_id', $jobPosting->id)
                ->orderBy('sort_order')
                ->pluck('requirement_text')
                ->toArray();

            // Get benefits for this job posting
            $benefits = DB::table('job_benefits')
                ->where('job_posting_id', $jobPosting->id)
                ->orderBy('sort_order')
                ->pluck('benefit_text')
                ->toArray();

            // Update job posting with JSON data
            DB::table('job_postings')
                ->where('id', $jobPosting->id)
                ->update([
                    'requirements' => json_encode($requirements),
                    'benefits' => json_encode($benefits),
                ]);
        }
    }

    /**
     * Migrate JSON data back to relational tables (for rollback)
     */
    private function migrateDataBack(): void
    {
        $jobPostings = DB::table('job_postings')->get();

        foreach ($jobPostings as $jobPosting) {
            // Migrate requirements back
            if ($jobPosting->requirements) {
                $requirements = json_decode($jobPosting->requirements, true);
                if (is_array($requirements)) {
                    foreach ($requirements as $index => $requirement) {
                        DB::table('job_requirements')->insert([
                            'job_posting_id' => $jobPosting->id,
                            'requirement_text' => $requirement,
                            'is_mandatory' => true,
                            'sort_order' => $index + 1,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            // Migrate benefits back
            if ($jobPosting->benefits) {
                $benefits = json_decode($jobPosting->benefits, true);
                if (is_array($benefits)) {
                    foreach ($benefits as $index => $benefit) {
                        DB::table('job_benefits')->insert([
                            'job_posting_id' => $jobPosting->id,
                            'benefit_text' => $benefit,
                            'sort_order' => $index + 1,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }
        }
    }
};
