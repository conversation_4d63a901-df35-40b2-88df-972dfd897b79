<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Add new JSON columns to job_postings table
        Schema::table('job_postings', function (Blueprint $table) {
            $table->json('responsibilities')->nullable()->after('benefits');
            $table->json('skills')->nullable()->after('responsibilities');
        });

        // Step 2: Migrate existing data from relational tables to JSON columns
        $this->migrateExistingData();

        // Step 3: Drop the old relational tables (commented out for safety)
        // Schema::dropIfExists('job_responsibilities');
        // Schema::dropIfExists('job_skills');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the old tables
        Schema::create('job_responsibilities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('cascade');
            $table->text('responsibility_text');
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['job_posting_id', 'sort_order']);
        });

        Schema::create('job_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('cascade');
            $table->string('skill_name', 100);
            $table->enum('importance_level', ['required', 'preferred', 'nice-to-have'])->default('required');
            $table->enum('proficiency_level', ['beginner', 'intermediate', 'advanced', 'expert'])->nullable();
            $table->timestamps();

            $table->index(['job_posting_id', 'skill_name']);
            $table->unique(['job_posting_id', 'skill_name']);
        });

        // Migrate data back from JSON to relational tables
        $this->migrateDataBack();

        // Remove JSON columns
        Schema::table('job_postings', function (Blueprint $table) {
            $table->dropColumn(['responsibilities', 'skills']);
        });
    }

    /**
     * Migrate existing relational data to JSON format
     */
    private function migrateExistingData(): void
    {
        // Migrate responsibilities and skills
        $jobPostings = DB::table('job_postings')->get();

        foreach ($jobPostings as $jobPosting) {
            // Get responsibilities for this job posting
            $responsibilities = DB::table('job_responsibilities')
                ->where('job_posting_id', $jobPosting->id)
                ->orderBy('sort_order')
                ->pluck('responsibility_text')
                ->toArray();

            // Get skills for this job posting
            $skills = DB::table('job_skills')
                ->where('job_posting_id', $jobPosting->id)
                ->pluck('skill_name')
                ->toArray();

            // Update job posting with JSON data
            DB::table('job_postings')
                ->where('id', $jobPosting->id)
                ->update([
                    'responsibilities' => json_encode($responsibilities),
                    'skills' => json_encode($skills),
                ]);
        }
    }

    /**
     * Migrate JSON data back to relational tables (for rollback)
     */
    private function migrateDataBack(): void
    {
        $jobPostings = DB::table('job_postings')->get();

        foreach ($jobPostings as $jobPosting) {
            // Migrate responsibilities back
            if ($jobPosting->responsibilities) {
                $responsibilities = json_decode($jobPosting->responsibilities, true);
                if (is_array($responsibilities)) {
                    foreach ($responsibilities as $index => $responsibility) {
                        DB::table('job_responsibilities')->insert([
                            'job_posting_id' => $jobPosting->id,
                            'responsibility_text' => $responsibility,
                            'sort_order' => $index + 1,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            // Migrate skills back
            if ($jobPosting->skills) {
                $skills = json_decode($jobPosting->skills, true);
                if (is_array($skills)) {
                    foreach ($skills as $skill) {
                        DB::table('job_skills')->insert([
                            'job_posting_id' => $jobPosting->id,
                            'skill_name' => $skill,
                            'importance_level' => 'required',
                            'proficiency_level' => null,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }
        }
    }
};
