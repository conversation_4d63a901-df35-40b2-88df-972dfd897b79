<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('message_templates', function (Blueprint $table) {
            $table->string('language', 5)->default('vi')->after('type');
            $table->integer('version')->default(1)->after('language');
            $table->foreignId('parent_template_id')->nullable()->constrained('message_templates')->onDelete('set null')->after('version');

            // Add indexes for new fields
            $table->index(['language', 'is_active'], 'mt_language_active_idx');
            $table->index(['parent_template_id', 'version'], 'mt_parent_version_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('message_templates', function (Blueprint $table) {
            $table->dropIndex('mt_language_active_idx');
            $table->dropIndex('mt_parent_version_idx');
            $table->dropForeign(['parent_template_id']);
            $table->dropColumn(['language', 'version', 'parent_template_id']);
        });
    }
};
