<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('candidate_id')->constrained('candidates')->onDelete('cascade');
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('restrict');
            $table->foreignId('interviewer_id')->constrained('interviewers')->onDelete('restrict');
            $table->date('date');
            $table->time('time');
            $table->integer('duration')->default(60); // Duration in minutes
            $table->enum('type', ['video', 'phone', 'in-person'])->default('video');
            $table->enum('status', ['scheduled', 'completed', 'cancelled', 'rescheduled', 'no-show'])->default('scheduled');
            $table->string('meeting_link', 500)->nullable();
            $table->string('meeting_password', 100)->nullable();
            $table->string('location')->nullable();
            $table->text('address')->nullable();
            $table->text('notes')->nullable();
            $table->json('agenda')->nullable(); // Interview agenda items
            $table->integer('round')->default(1);
            $table->enum('interview_type', ['screening', 'technical', 'case-study', 'portfolio', 'cultural', 'final'])->default('screening');
            $table->boolean('reminder_sent')->default(false);
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            // Indexes
            $table->index(['candidate_id', 'date']);
            $table->index(['job_posting_id', 'status']);
            $table->index(['interviewer_id', 'date']);
            $table->index(['status', 'type']);
            $table->index('interview_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interviews');
    }
};
