<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_postings', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('department', 100);
            $table->string('location');
            $table->enum('type', ['full-time', 'part-time', 'contract', 'internship'])->default('full-time');
            $table->enum('work_location', ['remote', 'onsite', 'hybrid'])->default('onsite');
            $table->decimal('salary_min', 15, 2)->nullable();
            $table->decimal('salary_max', 15, 2)->nullable();
            $table->string('currency', 3)->default('VND');
            $table->text('description');
            $table->text('education_required')->nullable();
            $table->text('company_culture')->nullable();
            $table->enum('status', ['draft', 'active', 'paused', 'closed'])->default('draft');
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
            $table->date('posted_date')->nullable();
            $table->date('closing_date')->nullable();
            $table->foreignId('hiring_manager_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('recruiter_id')->nullable()->constrained('users')->onDelete('set null');
            $table->enum('experience_level', ['entry', 'mid', 'senior', 'lead', 'junior']);
            $table->integer('applicant_count')->default(0);
            $table->integer('view_count')->default(0);
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['status', 'posted_date']);
            $table->index(['department', 'location']);
            $table->index(['experience_level', 'type']);
            $table->index('hiring_manager_id');
            $table->index('recruiter_id');
            $table->fullText(['title', 'description']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_postings');
    }
};
