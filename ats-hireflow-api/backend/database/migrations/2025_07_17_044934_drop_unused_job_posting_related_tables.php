<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Drop job posting related tables that have been replaced by JSON fields:
     * - job_requirements -> replaced by 'requirements' JSON field in job_postings table
     * - job_benefits -> replaced by 'benefits' JSON field in job_postings table
     * - job_responsibilities -> replaced by 'responsibilities' JSON field in job_postings table
     * - job_skills -> replaced by 'skills' JSON field in job_postings table
     */
    public function up(): void
    {
        // Drop job posting related tables in reverse dependency order
        Schema::dropIfExists('job_requirements');
        Schema::dropIfExists('job_benefits');
        Schema::dropIfExists('job_responsibilities');
        Schema::dropIfExists('job_skills');
    }

    /**
     * Reverse the migrations.
     *
     * Recreate the dropped tables for rollback functionality.
     */
    public function down(): void
    {
        // Recreate job_requirements table
        Schema::create('job_requirements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('cascade');
            $table->text('requirement_text');
            $table->boolean('is_mandatory')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index('job_posting_id');
        });

        // Recreate job_benefits table
        Schema::create('job_benefits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('cascade');
            $table->text('benefit_text');
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index('job_posting_id');
        });

        // Recreate job_responsibilities table
        Schema::create('job_responsibilities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('cascade');
            $table->text('responsibility_text');
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['job_posting_id', 'sort_order']);
        });

        // Recreate job_skills table
        Schema::create('job_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_posting_id')->constrained('job_postings')->onDelete('cascade');
            $table->string('skill_name', 100);
            $table->enum('importance_level', ['required', 'preferred', 'nice-to-have'])->default('required');
            $table->enum('proficiency_level', ['beginner', 'intermediate', 'advanced', 'expert'])->nullable();
            $table->timestamps();

            $table->index(['job_posting_id', 'skill_name']);
            $table->unique(['job_posting_id', 'skill_name']);
        });
    }
};
