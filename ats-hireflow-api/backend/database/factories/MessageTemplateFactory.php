<?php

namespace Database\Factories;

use App\Models\MessageTemplate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MessageTemplate>
 */
class MessageTemplateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            MessageTemplate::CATEGORY_INTERVIEW,
            MessageTemplate::CATEGORY_OFFER,
            MessageTemplate::CATEGORY_FEEDBACK,
            MessageTemplate::CATEGORY_REMINDER,
            MessageTemplate::CATEGORY_REJECTION,
            MessageTemplate::CATEGORY_WELCOME,
        ];

        $types = [MessageTemplate::TYPE_EMAIL, MessageTemplate::TYPE_SMS];

        $category = $this->faker->randomElement($categories);
        $type = $this->faker->randomElement($types);

        return [
            'name' => $this->generateTemplateName($category, $type),
            'subject' => $this->generateSubject($category, $type),
            'content' => $this->generateContent($category, $type),
            'variables' => $this->generateVariables($category),
            'category' => $category,
            'type' => $type,
            'language' => 'vi',
            'version' => 1,
            'is_active' => $this->faker->boolean(80),
            'created_by' => User::factory(),
        ];
    }

    private function generateTemplateName(string $category, string $type): string
    {
        $categoryNames = [
            MessageTemplate::CATEGORY_INTERVIEW => 'Phỏng vấn',
            MessageTemplate::CATEGORY_OFFER => 'Đề nghị công việc',
            MessageTemplate::CATEGORY_FEEDBACK => 'Phản hồi',
            MessageTemplate::CATEGORY_REMINDER => 'Nhắc nhở',
            MessageTemplate::CATEGORY_REJECTION => 'Từ chối',
            MessageTemplate::CATEGORY_WELCOME => 'Chào mừng',
        ];

        $typeNames = [
            MessageTemplate::TYPE_EMAIL => 'Email',
            MessageTemplate::TYPE_SMS => 'SMS',
        ];

        return $categoryNames[$category] . ' - ' . $typeNames[$type] . ' ' . $this->faker->numberBetween(1, 10);
    }

    private function generateSubject(string $category, string $type): string
    {
        if ($type === MessageTemplate::TYPE_SMS) {
            return ''; // SMS doesn't have subject
        }

        return match ($category) {
            MessageTemplate::CATEGORY_INTERVIEW => 'Lời mời phỏng vấn - Vị trí {{job_title}}',
            MessageTemplate::CATEGORY_OFFER => 'Thư mời làm việc - {{job_title}} tại {{company_name}}',
            MessageTemplate::CATEGORY_FEEDBACK => 'Phản hồi về ứng tuyển vị trí {{job_title}}',
            MessageTemplate::CATEGORY_REMINDER => 'Nhắc nhở: {{reminder_subject}}',
            MessageTemplate::CATEGORY_REJECTION => 'Thông báo kết quả ứng tuyển - {{job_title}}',
            MessageTemplate::CATEGORY_WELCOME => 'Chào mừng bạn đến với {{company_name}}',
            default => 'Thông báo từ {{company_name}}',
        };
    }

    private function generateContent(string $category, string $type): string
    {
        return match ($category) {
            MessageTemplate::CATEGORY_INTERVIEW => $this->getInterviewContent($type),
            MessageTemplate::CATEGORY_OFFER => $this->getOfferContent($type),
            MessageTemplate::CATEGORY_FEEDBACK => $this->getFeedbackContent($type),
            MessageTemplate::CATEGORY_REMINDER => $this->getReminderContent($type),
            MessageTemplate::CATEGORY_REJECTION => $this->getRejectionContent($type),
            MessageTemplate::CATEGORY_WELCOME => $this->getWelcomeContent($type),
            default => $this->getGeneralContent($type),
        };
    }

    private function getInterviewContent(string $type): string
    {
        if ($type === MessageTemplate::TYPE_SMS) {
            return 'Xin chào {{candidate_name}}, chúng tôi mời bạn tham gia phỏng vấn vị trí {{job_title}} vào {{interview_date}} lúc {{interview_time}}. Địa điểm: {{interview_location}}. Liên hệ: {{recruiter_phone}}';
        }

        return 'Kính gửi {{candidate_name}},

Chúng tôi rất vui mừng thông báo rằng hồ sơ ứng tuyển của bạn cho vị trí {{job_title}} tại {{company_name}} đã được chọn để tham gia vòng phỏng vấn.

Thông tin chi tiết:
- Thời gian: {{interview_date}} lúc {{interview_time}}
- Địa điểm: {{interview_location}}
- Hình thức: {{interview_type}}
- Người phỏng vấn: {{interviewer_name}}

Vui lòng xác nhận tham gia và chuẩn bị các tài liệu cần thiết.

Trân trọng,
{{recruiter_name}}
{{company_name}}
{{recruiter_email}} | {{recruiter_phone}}';
    }

    private function getOfferContent(string $type): string
    {
        if ($type === MessageTemplate::TYPE_SMS) {
            return 'Chúc mừng {{candidate_name}}! Bạn đã được chọn cho vị trí {{job_title}} tại {{company_name}}. Mức lương: {{salary}}. Ngày bắt đầu: {{start_date}}. Liên hệ: {{recruiter_phone}}';
        }

        return 'Kính gửi {{candidate_name}},

Chúc mừng! Chúng tôi rất vui mừng thông báo rằng bạn đã được chọn cho vị trí {{job_title}} tại {{company_name}}.

Chi tiết đề nghị:
- Vị trí: {{job_title}}
- Mức lương: {{salary}}
- Ngày bắt đầu làm việc: {{start_date}}
- Phúc lợi: {{benefits}}

Vui lòng phản hồi trong vòng 3 ngày làm việc để xác nhận việc nhận offer này.

Chúng tôi rất mong được chào đón bạn vào đội ngũ!

Trân trọng,
{{recruiter_name}}
{{company_name}}';
    }

    private function getRejectionContent(string $type): string
    {
        if ($type === MessageTemplate::TYPE_SMS) {
            return 'Xin chào {{candidate_name}}, cảm ơn bạn đã ứng tuyển vị trí {{job_title}}. Rất tiếc lần này chúng tôi chưa thể hợp tác. Chúc bạn thành công!';
        }

        return 'Kính gửi {{candidate_name}},

Cảm ơn bạn đã dành thời gian ứng tuyển vị trí {{job_title}} tại {{company_name}} và tham gia quá trình tuyển dụng của chúng tôi.

Sau khi xem xét kỹ lưỡng, chúng tôi rất tiếc phải thông báo rằng lần này chúng tôi đã chọn ứng viên khác phù hợp hơn với yêu cầu công việc.

{{#if feedback}}
Phản hồi: {{feedback}}
{{/if}}

Chúng tôi rất ấn tượng với hồ sơ của bạn và hy vọng sẽ có cơ hội hợp tác trong tương lai.

Chúc bạn thành công trong việc tìm kiếm cơ hội mới!

Trân trọng,
{{recruiter_name}}
{{company_name}}';
    }

    private function getFeedbackContent(string $type): string
    {
        if ($type === MessageTemplate::TYPE_SMS) {
            return 'Xin chào {{candidate_name}}, cảm ơn bạn đã tham gia phỏng vấn. Chúng tôi sẽ có phản hồi trong vòng {{feedback_timeline}}. Liên hệ: {{recruiter_phone}}';
        }

        return 'Kính gửi {{candidate_name}},

Cảm ơn bạn đã tham gia phỏng vấn cho vị trí {{job_title}} tại {{company_name}}.

Chúng tôi đã ghi nhận những thông tin bạn chia sẻ và sẽ có phản hồi kết quả trong vòng {{feedback_timeline}}.

Nếu có bất kỳ câu hỏi nào, vui lòng liên hệ với chúng tôi.

Trân trọng,
{{recruiter_name}}
{{company_name}}';
    }

    private function getReminderContent(string $type): string
    {
        if ($type === MessageTemplate::TYPE_SMS) {
            return 'Nhắc nhở: {{reminder_content}}. Thời gian: {{reminder_time}}. Liên hệ: {{recruiter_phone}}';
        }

        return 'Kính gửi {{candidate_name}},

Đây là lời nhắc nhở về {{reminder_subject}}.

{{reminder_content}}

Thời gian: {{reminder_time}}

Vui lòng xác nhận hoặc liên hệ nếu cần thay đổi.

Trân trọng,
{{recruiter_name}}
{{company_name}}';
    }

    private function getWelcomeContent(string $type): string
    {
        if ($type === MessageTemplate::TYPE_SMS) {
            return 'Chào mừng {{candidate_name}} đến với {{company_name}}! Ngày đầu làm việc: {{start_date}}. Liên hệ HR: {{hr_phone}}';
        }

        return 'Kính gửi {{candidate_name}},

Chào mừng bạn đến với đại gia đình {{company_name}}!

Chúng tôi rất vui mừng được chào đón bạn vào vị trí {{job_title}}. Ngày đầu tiên làm việc của bạn sẽ là {{start_date}}.

Thông tin quan trọng:
- Thời gian làm việc: {{working_hours}}
- Địa điểm: {{office_address}}
- Người liên hệ HR: {{hr_name}} - {{hr_phone}}

Chúng tôi tin rằng bạn sẽ có những đóng góp tích cực cho công ty.

Chào mừng bạn!

Trân trọng,
{{recruiter_name}}
{{company_name}}';
    }

    private function getGeneralContent(string $type): string
    {
        if ($type === MessageTemplate::TYPE_SMS) {
            return 'Xin chào {{candidate_name}}, {{message_content}}. Liên hệ: {{recruiter_phone}}';
        }

        return 'Kính gửi {{candidate_name}},

{{message_content}}

Nếu có bất kỳ câu hỏi nào, vui lòng liên hệ với chúng tôi.

Trân trọng,
{{recruiter_name}}
{{company_name}}';
    }

    private function generateVariables(string $category): array
    {
        $baseVariables = [
            'candidate_name',
            'company_name',
            'recruiter_name',
            'recruiter_email',
            'recruiter_phone',
        ];

        return match ($category) {
            MessageTemplate::CATEGORY_INTERVIEW => array_merge($baseVariables, [
                'job_title',
                'interview_date',
                'interview_time',
                'interview_location',
                'interview_type',
                'interviewer_name'
            ]),
            MessageTemplate::CATEGORY_OFFER => array_merge($baseVariables, [
                'job_title',
                'salary',
                'start_date',
                'benefits'
            ]),
            MessageTemplate::CATEGORY_FEEDBACK => array_merge($baseVariables, [
                'job_title',
                'feedback_timeline'
            ]),
            MessageTemplate::CATEGORY_REMINDER => array_merge($baseVariables, [
                'reminder_subject',
                'reminder_content',
                'reminder_time'
            ]),
            MessageTemplate::CATEGORY_REJECTION => array_merge($baseVariables, [
                'job_title',
                'feedback'
            ]),
            MessageTemplate::CATEGORY_WELCOME => array_merge($baseVariables, [
                'job_title',
                'start_date',
                'working_hours',
                'office_address',
                'hr_name',
                'hr_phone'
            ]),
            default => array_merge($baseVariables, ['message_content']),
        };
    }
}
