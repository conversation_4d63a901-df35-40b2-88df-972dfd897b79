<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JobPosting>
 */
class JobPostingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations'];
        $positions = [
            'Engineering' => ['Software Engineer', 'Senior Developer', 'DevOps Engineer', 'QA Engineer', 'Tech Lead'],
            'Marketing' => ['Marketing Manager', 'Content Creator', 'SEO Specialist', 'Digital Marketer'],
            'Sales' => ['Sales Representative', 'Account Manager', 'Business Development', 'Sales Manager'],
            'HR' => ['HR Manager', 'Recruiter', 'HR Business Partner', 'Talent Acquisition'],
            'Finance' => ['Financial Analyst', 'Accountant', 'Finance Manager', 'Controller'],
            'Operations' => ['Operations Manager', 'Project Manager', 'Business Analyst', 'Operations Coordinator']
        ];

        $department = $this->faker->randomElement($departments);
        $title = $this->faker->randomElement($positions[$department]);

        return [
            'title' => $title,
            'department' => $department,
            'location' => $this->faker->randomElement(['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Remote', 'Hybrid']),
            'type' => $this->faker->randomElement(['full-time', 'part-time', 'contract', 'internship']),
            'work_location' => $this->faker->randomElement(['remote', 'onsite', 'hybrid']),
            'salary_min' => $this->faker->numberBetween(********, ********),
            'salary_max' => $this->faker->numberBetween(********, ********),
            'currency' => 'VND',
            'description' => $this->faker->paragraphs(3, true),
            'education_required' => $this->faker->randomElement([
                'Bachelor\'s degree in Computer Science or related field',
                'Bachelor\'s degree in Business or related field',
                'High school diploma or equivalent',
                'Master\'s degree preferred'
            ]),
            'company_culture' => $this->faker->paragraph(),
            'status' => $this->faker->randomElement(['draft', 'active', 'paused', 'closed']),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high']),
            'posted_date' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'closing_date' => $this->faker->dateTimeBetween('now', '+60 days'),
            'hiring_manager_id' => User::factory(),
            'recruiter_id' => User::factory(),
            'experience_level' => $this->faker->randomElement(['entry', 'mid', 'senior', 'lead']),
            'applicant_count' => $this->faker->numberBetween(0, 50),
            'view_count' => $this->faker->numberBetween(0, 200),
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the job posting is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'posted_date' => now(),
        ]);
    }

    /**
     * Indicate that the job posting is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'posted_date' => null,
        ]);
    }

    /**
     * Indicate that the job posting is high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'high',
        ]);
    }
}
