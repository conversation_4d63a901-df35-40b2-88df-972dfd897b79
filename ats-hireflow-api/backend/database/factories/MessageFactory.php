<?php

namespace Database\Factories;

use App\Models\Message;
use App\Models\Candidate;
use App\Models\JobPosting;
use App\Models\MessageTemplate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Message>
 */
class MessageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = [Message::TYPE_EMAIL, Message::TYPE_SMS, Message::TYPE_NOTE];
        $categories = [
            Message::CATEGORY_INTERVIEW,
            Message::CATEGORY_OFFER,
            Message::CATEGORY_FEEDBACK,
            Message::CATEGORY_REMINDER,
            Message::CATEGORY_REJECTION,
            Message::CATEGORY_GENERAL,
        ];
        $statuses = [
            Message::STATUS_DRAFT,
            Message::STATUS_QUEUED,
            Message::STATUS_SENT,
            Message::STATUS_DELIVERED,
            Message::STATUS_READ,
            Message::STATUS_FAILED,
        ];

        $type = $this->faker->randomElement($types);
        $category = $this->faker->randomElement($categories);
        $status = $this->faker->randomElement($statuses);

        $candidate = Candidate::factory();
        $candidateName = $this->faker->name();
        $candidateEmail = $this->faker->email();

        return [
            'type' => $type,
            'category' => $category,
            'candidate_id' => $candidate,
            'job_posting_id' => $this->faker->boolean(70) ? JobPosting::factory() : null,
            'template_id' => $this->faker->boolean(60) ? MessageTemplate::factory() : null,
            'to_email' => $type === Message::TYPE_EMAIL ? $candidateEmail : null,
            'to_phone' => $type === Message::TYPE_SMS ? $this->faker->phoneNumber() : null,
            'to_name' => $candidateName,
            'from_email' => $type === Message::TYPE_EMAIL ? $this->faker->companyEmail() : null,
            'from_name' => $this->faker->name(),
            'subject' => $type === Message::TYPE_EMAIL ? $this->generateSubject($category) : null,
            'content' => $this->generateContent($category, $type, $candidateName),
            'status' => $status,
            'priority' => $this->faker->numberBetween(1, 10),
            'scheduled_at' => $status === Message::STATUS_QUEUED && $this->faker->boolean(30)
                ? $this->faker->dateTimeBetween('now', '+1 week') : null,
            'sent_at' => in_array($status, [Message::STATUS_SENT, Message::STATUS_DELIVERED, Message::STATUS_READ])
                ? $this->faker->dateTimeBetween('-1 week', 'now') : null,
            'delivered_at' => in_array($status, [Message::STATUS_DELIVERED, Message::STATUS_READ])
                ? $this->faker->dateTimeBetween('-1 week', 'now') : null,
            'read_at' => $status === Message::STATUS_READ
                ? $this->faker->dateTimeBetween('-1 week', 'now') : null,
            'error_message' => $status === Message::STATUS_FAILED
                ? $this->faker->sentence() : null,
            'metadata' => $this->faker->boolean(30) ? [
                'campaign_id' => $this->faker->uuid(),
                'source' => $this->faker->randomElement(['manual', 'automated', 'bulk']),
            ] : null,
            'external_id' => $this->faker->boolean(40) ? $this->faker->uuid() : null,
            'created_by' => User::factory(),
        ];
    }

    private function generateSubject(string $category): string
    {
        return match ($category) {
            Message::CATEGORY_INTERVIEW => 'Lời mời phỏng vấn - Vị trí ' . $this->faker->jobTitle(),
            Message::CATEGORY_OFFER => 'Thư mời làm việc - ' . $this->faker->jobTitle(),
            Message::CATEGORY_FEEDBACK => 'Phản hồi về ứng tuyển',
            Message::CATEGORY_REMINDER => 'Nhắc nhở: ' . $this->faker->sentence(3),
            Message::CATEGORY_REJECTION => 'Thông báo kết quả ứng tuyển',
            Message::CATEGORY_GENERAL => 'Thông báo từ ' . $this->faker->company(),
            default => 'Thông báo',
        };
    }

    private function generateContent(string $category, string $type, string $candidateName): string
    {
        $isShort = $type === Message::TYPE_SMS;

        return match ($category) {
            Message::CATEGORY_INTERVIEW => $isShort
                ? "Xin chào {$candidateName}, chúng tôi mời bạn tham gia phỏng vấn vào " . $this->faker->date() . " lúc " . $this->faker->time() . ". Liên hệ: " . $this->faker->phoneNumber()
                : "Kính gửi {$candidateName},\n\nChúng tôi rất vui mừng mời bạn tham gia phỏng vấn cho vị trí " . $this->faker->jobTitle() . ".\n\nThời gian: " . $this->faker->dateTime()->format('d/m/Y H:i') . "\nĐịa điểm: " . $this->faker->address() . "\n\nTrân trọng,\n" . $this->faker->name(),

            Message::CATEGORY_OFFER => $isShort
                ? "Chúc mừng {$candidateName}! Bạn đã được chọn cho vị trí " . $this->faker->jobTitle() . ". Mức lương: " . number_format($this->faker->numberBetween(15, 50)) . " triệu VNĐ."
                : "Kính gửi {$candidateName},\n\nChúc mừng! Bạn đã được chọn cho vị trí " . $this->faker->jobTitle() . ".\n\nMức lương: " . number_format($this->faker->numberBetween(15, 50)) . " triệu VNĐ\nNgày bắt đầu: " . $this->faker->date() . "\n\nTrân trọng,\n" . $this->faker->name(),

            Message::CATEGORY_REJECTION => $isShort
                ? "Xin chào {$candidateName}, cảm ơn bạn đã ứng tuyển. Rất tiếc lần này chúng tôi chưa thể hợp tác. Chúc bạn thành công!"
                : "Kính gửi {$candidateName},\n\nCảm ơn bạn đã ứng tuyển vị trí " . $this->faker->jobTitle() . ". Sau khi xem xét, chúng tôi đã chọn ứng viên khác phù hợp hơn.\n\nChúc bạn thành công!\n\nTrân trọng,\n" . $this->faker->name(),

            Message::CATEGORY_FEEDBACK => $isShort
                ? "Xin chào {$candidateName}, cảm ơn bạn đã tham gia phỏng vấn. Chúng tôi sẽ có phản hồi trong vòng 3-5 ngày."
                : "Kính gửi {$candidateName},\n\nCảm ơn bạn đã tham gia phỏng vấn. Chúng tôi sẽ có phản hồi kết quả trong vòng 3-5 ngày làm việc.\n\nTrân trọng,\n" . $this->faker->name(),

            Message::CATEGORY_REMINDER => $isShort
                ? "Nhắc nhở: {$candidateName}, bạn có lịch " . $this->faker->sentence(3) . " vào " . $this->faker->time() . " hôm nay."
                : "Kính gửi {$candidateName},\n\nĐây là lời nhắc nhở về " . $this->faker->sentence(5) . ".\n\nThời gian: " . $this->faker->dateTime()->format('d/m/Y H:i') . "\n\nTrân trọng,\n" . $this->faker->name(),

            default => $isShort
                ? "Xin chào {$candidateName}, " . $this->faker->sentence()
                : "Kính gửi {$candidateName},\n\n" . $this->faker->paragraph() . "\n\nTrân trọng,\n" . $this->faker->name(),
        };
    }

    /**
     * Indicate that the message is an email.
     */
    public function email(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => Message::TYPE_EMAIL,
            'to_phone' => null,
        ]);
    }

    /**
     * Indicate that the message is an SMS.
     */
    public function sms(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => Message::TYPE_SMS,
            'to_email' => null,
            'subject' => null,
        ]);
    }

    /**
     * Indicate that the message is sent.
     */
    public function sent(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => Message::STATUS_SENT,
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the message is delivered.
     */
    public function delivered(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => Message::STATUS_DELIVERED,
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'delivered_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }
}
