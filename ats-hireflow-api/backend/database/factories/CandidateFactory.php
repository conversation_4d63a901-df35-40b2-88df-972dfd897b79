<?php

namespace Database\Factories;

use App\Models\JobPosting;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Candidate>
 */
class CandidateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $positions = [
            'Software Engineer', 'Frontend Developer', 'Backend Developer', 'Full Stack Developer',
            'DevOps Engineer', 'QA Engineer', 'Product Manager', 'UI/UX Designer',
            'Marketing Manager', 'Sales Representative', 'Business Analyst', 'Data Scientist'
        ];

        $sources = ['LinkedIn', 'Indeed', 'Company Website', 'Referral', 'Job Fair', 'Recruiter', 'Direct Application'];
        $experiences = ['0-1 years', '1-3 years', '3-5 years', '5-10 years', '10+ years'];

        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'position' => $this->faker->randomElement($positions),
            'experience' => $this->faker->randomElement($experiences),
            'status' => $this->faker->randomElement(['sourced', 'applied', 'screening', 'interview', 'offer', 'hired', 'rejected']),
            'applied_date' => $this->faker->dateTimeBetween('-60 days', 'now'),
            'source' => $this->faker->randomElement($sources),
            'location' => $this->faker->randomElement(['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Can Tho', 'Hai Phong']),
            'salary_expectation_min' => $this->faker->numberBetween(15000000, 40000000),
            'salary_expectation_max' => $this->faker->numberBetween(40000000, 100000000),
            'salary_currency' => 'VND',
            'rating' => $this->faker->randomFloat(2, 1, 5),
            'ai_score' => $this->faker->numberBetween(60, 95),
            'linkedin_url' => $this->faker->optional(0.7)->url(),
            'github_url' => $this->faker->optional(0.4)->url(),
            'portfolio_url' => $this->faker->optional(0.3)->url(),
            'notes' => $this->faker->optional(0.6)->paragraph(),
            'job_posting_id' => JobPosting::factory(),
            'created_by' => User::factory(),
            'assigned_to' => $this->faker->optional(0.7)->randomElement([User::factory()]),
        ];
    }

    /**
     * Indicate that the candidate is highly rated.
     */
    public function highRated(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->randomFloat(2, 4.0, 5.0),
            'ai_score' => $this->faker->numberBetween(85, 95),
        ]);
    }

    /**
     * Indicate that the candidate is in interview stage.
     */
    public function inInterview(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'interview',
        ]);
    }

    /**
     * Indicate that the candidate is hired.
     */
    public function hired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'hired',
        ]);
    }

    /**
     * Indicate that the candidate has complete profile.
     */
    public function withCompleteProfile(): static
    {
        return $this->state(fn (array $attributes) => [
            'linkedin_url' => $this->faker->url(),
            'github_url' => $this->faker->url(),
            'portfolio_url' => $this->faker->url(),
            'notes' => $this->faker->paragraphs(2, true),
        ]);
    }
}
