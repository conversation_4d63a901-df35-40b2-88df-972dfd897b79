<?php

namespace Database\Factories;

use App\Models\Candidate;
use App\Models\JobPosting;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CandidateProfileAnalysis>
 */
class CandidateProfileAnalysisFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $analysisType = $this->faker->randomElement(['resume_extraction', 'ai_analysis', 'job_matching']);
        $status = $this->faker->randomElement(['completed', 'failed', 'pending', 'processing']);

        return [
            'candidate_id' => Candidate::factory(),
            'job_posting_id' => $analysisType === 'job_matching' ? JobPosting::factory() : null,
            'created_by' => User::factory(),
            'analysis_type' => $analysisType,
            'status' => $status,
            'external_service_id' => $this->faker->uuid(),

            // Extracted information (for resume_extraction) - Vietnamese
            'extracted_name' => $analysisType === 'resume_extraction' ?
                $this->faker->randomElement(['<PERSON>uyễn Văn An', 'Trần Thị Bình', 'Lê Hoàng Cường', 'Phạm Minh Đức', 'Hoàng Thị Lan']) : null,
            'extracted_email' => $analysisType === 'resume_extraction' ?
                strtolower($this->faker->firstName()) . '.' . strtolower($this->faker->lastName()) . '@gmail.com' : null,
            'extracted_phone' => $analysisType === 'resume_extraction' ?
                '+84-' . $this->faker->numberBetween(90, 99) . '-' . $this->faker->numberBetween(100, 999) . '-' . $this->faker->numberBetween(1000, 9999) : null,
            'extracted_address' => $analysisType === 'resume_extraction' ?
                $this->faker->numberBetween(1, 999) . ' Đường ' . $this->faker->randomElement(['Nguyễn Huệ', 'Lê Lợi', 'Trần Hưng Đạo', 'Hai Bà Trưng']) . ', Quận ' . $this->faker->numberBetween(1, 12) . ', TP.HCM' : null,
            'extracted_skills' => $analysisType === 'resume_extraction' ?
                $this->faker->randomElements(['PHP', 'Laravel', 'JavaScript', 'React', 'MySQL', 'Git', 'Docker', 'Giải quyết vấn đề', 'Lãnh đạo nhóm'], 5) : null,
            'extracted_experience' => $analysisType === 'resume_extraction' ? [
                [
                    'company' => 'Công ty TNHH ' . $this->faker->company(),
                    'position' => $this->faker->randomElement(['Lập trình viên Senior', 'Lập trình viên Full-stack', 'Team Lead', 'Technical Lead']),
                    'duration' => '2020-2023',
                    'description' => 'Phát triển và bảo trì các ứng dụng web, làm việc với team, tham gia thiết kế hệ thống.',
                ]
            ] : null,
            'extracted_education' => $analysisType === 'resume_extraction' ? [
                [
                    'institution' => $this->faker->randomElement(['Đại học Bách khoa Hà Nội', 'Đại học Công nghệ Thông tin', 'Đại học Khoa học Tự nhiên']),
                    'degree' => 'Cử nhân Công nghệ Thông tin',
                    'graduation_year' => $this->faker->year(),
                ]
            ] : null,

            // AI analysis results - Vietnamese
            'ai_summary' => in_array($analysisType, ['ai_analysis', 'job_matching']) ?
                'Ứng viên có nền tảng kỹ thuật vững chắc và kinh nghiệm làm việc tốt. Thể hiện khả năng học hỏi và phát triển nghề nghiệp.' : null,
            'strengths' => in_array($analysisType, ['ai_analysis', 'job_matching']) ? [
                'Kỹ năng lập trình vững chắc',
                'Kinh nghiệm làm việc nhóm tốt',
                'Khả năng giải quyết vấn đề'
            ] : null,
            'weaknesses' => in_array($analysisType, ['ai_analysis', 'job_matching']) ? [
                'Cần cải thiện kỹ năng thuyết trình',
                'Chưa có kinh nghiệm với một số công nghệ mới'
            ] : null,
            'improvement_areas' => in_array($analysisType, ['ai_analysis', 'job_matching']) ? [
                'Nâng cao kiến thức về cloud computing',
                'Học thêm về quản lý dự án'
            ] : null,
            'recommendations' => in_array($analysisType, ['ai_analysis', 'job_matching']) ? [
                'Nên tổ chức phỏng vấn kỹ thuật',
                'Thảo luận về mục tiêu nghề nghiệp',
                'Xem xét cho vị trí senior'
            ] : null,

            // Scoring
            'overall_score' => $status === 'completed' ? $this->faker->numberBetween(60, 95) : null,
            'skills_score' => $status === 'completed' ? $this->faker->numberBetween(60, 95) : null,
            'experience_score' => $status === 'completed' ? $this->faker->numberBetween(60, 95) : null,
            'education_score' => $status === 'completed' ? $this->faker->numberBetween(60, 95) : null,
            'cultural_fit_score' => $status === 'completed' ? $this->faker->numberBetween(60, 95) : null,

            // Job matching specific - Vietnamese
            'job_match_details' => $analysisType === 'job_matching' && $status === 'completed' ? [
                'match_percentage' => $this->faker->numberBetween(70, 95),
                'key_alignments' => [
                    'Kỹ năng lập trình phù hợp với yêu cầu',
                    'Kinh nghiệm làm việc phù hợp',
                    'Trình độ học vấn đáp ứng yêu cầu'
                ],
            ] : null,
            'missing_requirements' => $analysisType === 'job_matching' && $status === 'completed' ? [
                'Chứng chỉ chuyên ngành cụ thể',
                'Kinh nghiệm với framework đặc thù'
            ] : null,
            'matching_criteria' => $analysisType === 'job_matching' && $status === 'completed' ? [
                'Ngôn ngữ lập trình: PHP, JavaScript',
                'Số năm kinh nghiệm: 3-5 năm',
                'Trình độ học vấn: Cử nhân CNTT'
            ] : null,

            // Processing metadata
            'analysis_started_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'analysis_completed_at' => $status === 'completed' ? $this->faker->dateTimeBetween('-1 week', 'now') : null,
            'error_message' => $status === 'failed' ? $this->faker->sentence() : null,
        ];
    }

    /**
     * Indicate that the analysis is for resume extraction.
     */
    public function resumeExtraction(): static
    {
        return $this->state(fn(array $attributes) => [
            'analysis_type' => 'resume_extraction',
            'job_posting_id' => null,
        ]);
    }

    /**
     * Indicate that the analysis is completed.
     */
    public function completed(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'completed',
            'analysis_completed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }
}
