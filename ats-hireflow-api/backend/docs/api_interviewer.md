# Interviewer Management API Documentation

## Overview
API endpoints for managing interviewers in the HireFlow ATS system. All endpoints require authentication via San<PERSON><PERSON> token.

## Base URL
```
https://hireflow.test/api/v1
```

## Authentication
All requests must include the Authorization header:
```
Authorization: Bearer {your-token}
```

## Endpoints

### 1. List Interviewers
Get a list of all interviewers with optional filtering.

**Endpoint:** `GET /interviewers`

**Query Parameters:**
- `department` (string, optional) - Filter by department
- `is_active` (boolean, optional) - Filter by active status (true/false)
- `expertise` (string, optional) - Filter by expertise area
- `include_stats` (boolean, optional) - Include interview statistics

**Example Request:**
```bash
GET /api/v1/interviewers?department=Engineering&is_active=true&include_stats=true
```

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "user_id": 5,
      "name": "<PERSON><PERSON><PERSON><PERSON>",
      "email": "<EMAIL>",
      "department": "Engineering",
      "expertise": ["JavaScript", "React", "Node.js"],
      "expertise_list": "JavaScript, React, Node.js",
      "location": "H<PERSON> Chí <PERSON>",
      "max_interviews_per_day": 4,
      "availability": {
        "monday": [
          {"start_time": "09:00", "end_time": "12:00"},
          {"start_time": "14:00", "end_time": "17:00"}
        ],
        "tuesday": [
          {"start_time": "09:00", "end_time": "17:00"}
        ]
      },
      "time_slots": ["09:00", "10:00", "14:00", "15:00"],
      "timezone": "Asia/Ho_Chi_Minh",
      "is_active": true,
      "user": {
        "id": 5,
        "name": "Nguyễn Văn An",
        "email": "<EMAIL>",
        "phone": "0901234567",
        "avatar": "https://example.com/avatar.jpg",
        "initials": "NA",
        "title": "Senior Developer",
        "is_active": true
      },
      "statistics": {
        "total_interviews": 45,
        "completed_interviews": 40,
        "scheduled_interviews": 5,
        "upcoming_interviews": 3,
        "average_rating": 4.2
      },
      "created_at": "2025-01-15 10:30:00",
      "updated_at": "2025-01-20 14:20:00"
    }
  ]
}
```

### 2. Create Interviewer
Create a new interviewer profile.

**Endpoint:** `POST /interviewers`

**Required Permissions:** `create_interviewers` or roles: `admin`, `recruiter`, `hiring_manager`

**Request Body:**
```json
{
  "user_id": 5,
  "department": "Engineering",
  "expertise": ["JavaScript", "React", "Node.js"],
  "location": "Hồ Chí Minh",
  "max_interviews_per_day": 4,
  "availability": {
    "monday": [
      {"start_time": "09:00", "end_time": "12:00"},
      {"start_time": "14:00", "end_time": "17:00"}
    ],
    "tuesday": [
      {"start_time": "09:00", "end_time": "17:00"}
    ]
  },
  "time_slots": ["09:00", "10:00", "14:00", "15:00"],
  "timezone": "Asia/Ho_Chi_Minh",
  "is_active": true
}
```

**Validation Rules:**
- `user_id`: Required, must exist in users table, must be unique
- `department`: Optional, max 100 characters
- `expertise`: Optional array of strings, each max 255 characters
- `location`: Optional, max 255 characters
- `max_interviews_per_day`: Optional integer, 1-20
- `availability`: Optional object with day-based time slots
- `time_slots`: Optional array of time strings (HH:MM format)
- `timezone`: Optional, must be valid timezone
- `is_active`: Boolean, defaults to true

**Response:**
```json
{
  "status": "success",
  "message": "Interviewer created successfully",
  "data": {
    // Same structure as list response
  }
}
```

### 3. Show Interviewer
Get details of a specific interviewer.

**Endpoint:** `GET /interviewers/{id}`

**Query Parameters:**
- `include_interviews` (boolean, optional) - Include interview history
- `include_stats` (boolean, optional) - Include statistics

**Response:**
```json
{
  "status": "success",
  "data": {
    // Same structure as list response
  }
}
```

### 4. Update Interviewer
Update an existing interviewer profile.

**Endpoint:** `PUT/PATCH /interviewers/{id}`

**Required Permissions:** `edit_interviewers` or roles: `admin`, `recruiter`, `hiring_manager`, or own profile

**Request Body:** Same as create, but all fields are optional

**Additional Validation:**
- Cannot deactivate interviewer with scheduled interviews
- Time slots cannot overlap within the same day
- User ID must be unique (excluding current interviewer)

**Response:**
```json
{
  "status": "success",
  "message": "Interviewer updated successfully",
  "data": {
    // Updated interviewer data
  }
}
```

### 5. Delete Interviewer
Delete an interviewer profile.

**Endpoint:** `DELETE /interviewers/{id}`

**Required Permissions:** `delete_interviewers` or role: `admin`

**Business Rules:**
- Cannot delete interviewer with scheduled interviews
- Will return error 422 if interviewer has upcoming interviews

**Response (Success):**
```json
{
  "status": "success",
  "message": "Interviewer deleted successfully"
}
```

**Response (Error - Has Scheduled Interviews):**
```json
{
  "status": "error",
  "message": "Cannot delete interviewer with 3 scheduled interviews."
}
```

## Error Responses

### Validation Errors (422)
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "user_id": ["This user already has an interviewer profile."],
    "availability.monday": ["Time slots for monday cannot overlap."],
    "max_interviews_per_day": ["Maximum interviews per day must be at least 1."]
  }
}
```

### Authorization Errors (403)
```json
{
  "message": "This action is unauthorized."
}
```

### Not Found Errors (404)
```json
{
  "message": "No query results for model [App\\Models\\Interviewer] 999"
}
```

## Frontend Integration Examples

### React/TypeScript Interface
```typescript
interface Interviewer {
  id: number;
  user_id: number;
  name: string;
  email: string;
  department: string;
  expertise: string[];
  expertise_list: string;
  location: string;
  max_interviews_per_day: number;
  availability: {
    [key: string]: Array<{
      start_time: string;
      end_time: string;
    }>;
  };
  time_slots: string[];
  timezone: string;
  is_active: boolean;
  user: {
    id: number;
    name: string;
    email: string;
    phone: string;
    avatar?: string;
    initials: string;
    title: string;
    is_active: boolean;
  };
  statistics?: {
    total_interviews: number;
    completed_interviews: number;
    scheduled_interviews: number;
    upcoming_interviews: number;
    average_rating: number;
  };
  created_at: string;
  updated_at: string;
}

interface InterviewerResponse {
  data: Interviewer[];
}

interface SingleInterviewerResponse {
  status: string;
  data: Interviewer;
}
```

### API Service Example (JavaScript)
```javascript
class InterviewerService {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async getInterviewers(filters = {}) {
    const params = new URLSearchParams(filters);
    const response = await fetch(`${this.baseURL}/interviewers?${params}`, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Accept': 'application/json'
      }
    });
    return response.json();
  }

  async createInterviewer(data) {
    const response = await fetch(`${this.baseURL}/interviewers`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(data)
    });
    return response.json();
  }

  async updateInterviewer(id, data) {
    const response = await fetch(`${this.baseURL}/interviewers/${id}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(data)
    });
    return response.json();
  }

  async deleteInterviewer(id) {
    const response = await fetch(`${this.baseURL}/interviewers/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Accept': 'application/json'
      }
    });
    return response.json();
  }
}
```

### Usage Examples

#### 1. Get Active Interviewers for Dropdown
```javascript
// Get only active interviewers for interview scheduling
const activeInterviewers = await interviewerService.getInterviewers({
  is_active: true
});

// Transform for dropdown component
const dropdownOptions = activeInterviewers.data.map(interviewer => ({
  value: interviewer.id,
  label: `${interviewer.name} (${interviewer.department})`,
  expertise: interviewer.expertise,
  maxPerDay: interviewer.max_interviews_per_day
}));
```

#### 2. Create Interviewer with Availability
```javascript
const newInterviewer = {
  user_id: 15,
  department: "Engineering",
  expertise: ["React", "Node.js", "TypeScript"],
  location: "Hồ Chí Minh",
  max_interviews_per_day: 3,
  availability: {
    monday: [
      { start_time: "09:00", end_time: "12:00" },
      { start_time: "14:00", end_time: "17:00" }
    ],
    wednesday: [
      { start_time: "09:00", end_time: "17:00" }
    ],
    friday: [
      { start_time: "09:00", end_time: "12:00" }
    ]
  },
  time_slots: ["09:00", "10:00", "11:00", "14:00", "15:00", "16:00"],
  timezone: "Asia/Ho_Chi_Minh",
  is_active: true
};

const result = await interviewerService.createInterviewer(newInterviewer);
```

#### 3. Update Interviewer Availability
```javascript
// Update only availability and max interviews
const updateData = {
  max_interviews_per_day: 5,
  availability: {
    ...existingAvailability,
    thursday: [
      { start_time: "13:00", end_time: "17:00" }
    ]
  }
};

const result = await interviewerService.updateInterviewer(interviewerId, updateData);
```

## Integration with Interview Scheduling

### Filter Interviews by Interviewer
The interviewer API integrates with the interview filtering system:

```javascript
// Get interviews for specific interviewer
const interviews = await fetch('/api/v1/interviews?filter[interviewer_id]=5');

// Get interviews by department
const deptInterviews = await fetch('/api/v1/interviews?filter[interviewer_department]=Engineering');
```

### Check Interviewer Availability
```javascript
// Check if interviewer is available for scheduling
const availability = await fetch('/api/v1/interviews/availability/check', {
  method: 'POST',
  body: JSON.stringify({
    interviewer_id: 5,
    date: '2025-01-25',
    time: '14:00',
    duration: 60
  })
});
```

## Best Practices

### 1. Error Handling
Always handle validation errors and display user-friendly messages:

```javascript
try {
  const result = await interviewerService.createInterviewer(data);
  showSuccess('Interviewer created successfully');
} catch (error) {
  if (error.status === 422) {
    // Handle validation errors
    const errors = error.response.errors;
    Object.keys(errors).forEach(field => {
      showFieldError(field, errors[field][0]);
    });
  } else {
    showError('An unexpected error occurred');
  }
}
```

### 2. Caching
Cache interviewer data for dropdown components:

```javascript
// Cache active interviewers for 5 minutes
const cachedInterviewers = useMemo(() => {
  return interviewers.filter(i => i.is_active);
}, [interviewers]);
```

### 3. Real-time Updates
Consider using WebSocket or polling for real-time availability updates:

```javascript
// Poll for interviewer availability changes
useEffect(() => {
  const interval = setInterval(() => {
    if (selectedDate && selectedInterviewer) {
      checkInterviewerAvailability(selectedInterviewer, selectedDate);
    }
  }, 30000); // Check every 30 seconds

  return () => clearInterval(interval);
}, [selectedDate, selectedInterviewer]);
```

## Permissions Summary

| Action | Required Permission | Alternative Roles |
|--------|-------------------|------------------|
| View Interviewers | `view_interviewers` | `admin`, `recruiter`, `hiring_manager`, `interviewer` |
| Create Interviewer | `create_interviewers` | `admin`, `recruiter`, `hiring_manager` |
| Edit Interviewer | `edit_interviewers` | `admin`, `recruiter`, `hiring_manager`, own profile |
| Delete Interviewer | `delete_interviewers` | `admin` |
| Assign Interviewers | `assign_interviewers` | `admin`, `recruiter`, `hiring_manager` |

## Rate Limiting
- Standard rate limiting applies: 60 requests per minute per user
- Bulk operations should be batched appropriately
- Consider implementing client-side debouncing for search/filter operations
