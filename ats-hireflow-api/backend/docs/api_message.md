# Message System API Documentation

## Overview

The Message System provides comprehensive messaging capabilities for the ATS application, including template management and message sending/tracking functionality. All endpoints support Vietnamese content and professional recruitment communication workflows.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

Most endpoints require authentication using Laravel Sanctum tokens:

```http
Authorization: Bearer {your-token}
Content-Type: application/json
Accept: application/json
```

## Message Templates API

### 1. List Message Templates

**Endpoint:** `GET /message-templates`  
**Authentication:** Required  
**Description:** Retrieve paginated list of message templates with filtering options.

#### Query Parameters

| Parameter   | Type    | Description                           | Example              |
| ----------- | ------- | ------------------------------------- | -------------------- |
| `page`      | integer | Page number for pagination            | `1`                  |
| `per_page`  | integer | Items per page (max 50)               | `15`                 |
| `category`  | string  | Filter by category                    | `interview`          |
| `type`      | string  | Filter by type                        | `email`              |
| `language`  | string  | Filter by language                    | `vi`                 |
| `is_active` | boolean | Filter by active status               | `true`               |
| `search`    | string  | Search in name, subject, content      | `phỏng vấn`          |
| `sort`      | string  | Sort field (prefix with `-` for desc) | `-created_at`        |
| `include`   | string  | Include relationships                 | `createdBy,messages` |

#### Response Example

```json
{
  "data": [
    {
      "id": 1,
      "name": "Lời mời phỏng vấn - Email chính thức",
      "subject": "Lời mời phỏng vấn - Vị trí {{job_title}} tại {{company_name}}",
      "content": "Kính gửi {{candidate_name}},\n\nChúng tôi rất vui mừng thông báo...",
      "variables": ["candidate_name", "job_title", "company_name"],
      "category": "interview",
      "category_name": "Phỏng vấn",
      "type": "email",
      "type_name": "Email",
      "language": "vi",
      "version": 1,
      "is_active": true,
      "parent_template_id": null,
      "available_variables": ["candidate_name", "job_title"],
      "extracted_variables": ["candidate_name", "job_title"],
      "is_latest_version": true,
      "messages_count": 5,
      "created_at": "2024-07-21 10:30:00",
      "updated_at": "2024-07-21 10:30:00",
      "created_by": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>"
      }
    }
  ],
  "links": {
    "first": "http://localhost:8000/api/v1/message-templates?page=1",
    "last": "http://localhost:8000/api/v1/message-templates?page=3",
    "prev": null,
    "next": "http://localhost:8000/api/v1/message-templates?page=2"
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 3,
    "per_page": 15,
    "to": 15,
    "total": 42
  }
}
```

### 2. Create Message Template

**Endpoint:** `POST /message-templates`  
**Authentication:** Required

#### Request Body

```json
{
  "name": "Thông báo kết quả phỏng vấn",
  "subject": "Kết quả phỏng vấn - Vị trí {{job_title}}",
  "content": "Kính gửi {{candidate_name}},\n\nCảm ơn bạn đã tham gia phỏng vấn cho vị trí {{job_title}}.\n\n{{#if passed}}Chúc mừng! Bạn đã vượt qua vòng phỏng vấn.{{else}}Rất tiếc, lần này chúng tôi chưa thể hợp tác.{{/if}}\n\nTrân trọng,\n{{recruiter_name}}",
  "variables": ["candidate_name", "job_title", "passed", "recruiter_name"],
  "category": "feedback",
  "type": "email",
  "language": "vi",
  "is_active": true
}
```

#### Response

```json
{
  "status": "success",
  "message": "Template đã được tạo thành công",
  "data": {
    "id": 15,
    "name": "Thông báo kết quả phỏng vấn",
    "subject": "Kết quả phỏng vấn - Vị trí {{job_title}}",
    "content": "Kính gửi {{candidate_name}}...",
    "variables": ["candidate_name", "job_title", "passed", "recruiter_name"],
    "category": "feedback",
    "category_name": "Phản hồi",
    "type": "email",
    "type_name": "Email",
    "language": "vi",
    "version": 1,
    "is_active": true,
    "created_at": "2024-07-21 14:25:00",
    "updated_at": "2024-07-21 14:25:00"
  }
}
```

### 3. Get Message Template

**Endpoint:** `GET /message-templates/{id}`  
**Authentication:** Required

#### Response

```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Lời mời phỏng vấn - Email chính thức",
    "subject": "Lời mời phỏng vấn - Vị trí {{job_title}} tại {{company_name}}",
    "content": "Kính gửi {{candidate_name}},\n\nChúng tôi rất vui mừng...",
    "variables": ["candidate_name", "job_title", "company_name"],
    "category": "interview",
    "type": "email",
    "language": "vi",
    "version": 1,
    "is_active": true,
    "created_by": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>"
    },
    "messages": [
      {
        "id": 101,
        "type": "email",
        "status": "sent",
        "to_email": "<EMAIL>",
        "subject": "Lời mời phỏng vấn - Vị trí Lập trình viên PHP",
        "sent_at": "2024-07-21 09:00:00",
        "created_at": "2024-07-21 08:55:00"
      }
    ]
  }
}
```

### 4. Update Message Template

**Endpoint:** `PUT /message-templates/{id}`  
**Authentication:** Required

#### Request Body

```json
{
  "name": "Lời mời phỏng vấn - Email chính thức (Cập nhật)",
  "subject": "Lời mời phỏng vấn - {{job_title}} tại {{company_name}}",
  "content": "Kính gửi {{candidate_name}},\n\nChúng tôi rất vui mừng thông báo...",
  "is_active": true
}
```

### 5. Delete Message Template

**Endpoint:** `DELETE /message-templates/{id}`  
**Authentication:** Required

#### Response

```json
{
  "status": "success",
  "message": "Template đã được xóa thành công"
}
```

### 6. Preview Message Template

**Endpoint:** `POST /message-templates/{id}/preview`  
**Authentication:** Required (Public route available for testing)

#### Request Body

```json
{
  "data": {
    "candidate_name": "Nguyễn Văn An",
    "job_title": "Lập trình viên PHP Senior",
    "company_name": "Công ty TNHH ABC",
    "interview_date": "25/07/2024",
    "interview_time": "14:00",
    "interview_location": "Tầng 5, Tòa nhà ABC, 123 Đường XYZ",
    "recruiter_name": "Trần Thị Bình",
    "recruiter_email": "<EMAIL>",
    "recruiter_phone": "0123456789"
  }
}
```

#### Response

```json
{
  "status": "success",
  "data": {
    "template": {
      "id": 1,
      "name": "Lời mời phỏng vấn - Email chính thức",
      "category": "interview",
      "type": "email"
    },
    "preview": {
      "subject": "Lời mời phỏng vấn - Vị trí Lập trình viên PHP Senior tại Công ty TNHH ABC",
      "content": "Kính gửi Nguyễn Văn An,\n\nChúng tôi rất vui mừng thông báo rằng hồ sơ ứng tuyển của bạn cho vị trí Lập trình viên PHP Senior tại Công ty TNHH ABC đã được chọn để tham gia vòng phỏng vấn.\n\n📅 Thông tin chi tiết:\n• Thời gian: 25/07/2024 lúc 14:00\n• Địa điểm: Tầng 5, Tòa nhà ABC, 123 Đường XYZ\n\nTrân trọng,\nTrần Thị Bình"
    },
    "variables_used": {
      "candidate_name": "Nguyễn Văn An",
      "job_title": "Lập trình viên PHP Senior"
    },
    "missing_variables": [],
    "available_variables": ["candidate_name", "job_title", "company_name"]
  }
}
```

### 7. Duplicate Message Template

**Endpoint:** `POST /message-templates/{id}/duplicate`  
**Authentication:** Required

#### Response

```json
{
  "status": "success",
  "message": "Template đã được sao chép thành công",
  "data": {
    "id": 16,
    "name": "Lời mời phỏng vấn - Email chính thức (Copy)",
    "subject": "Lời mời phỏng vấn - Vị trí {{job_title}} tại {{company_name}}",
    "version": 1,
    "parent_template_id": null,
    "created_at": "2024-07-21 15:30:00"
  }
}
```

### 8. Get Template Categories

**Endpoint:** `GET /message-templates/categories`  
**Authentication:** Not required

#### Response

```json
{
  "status": "success",
  "data": {
    "interview": "Phỏng vấn",
    "offer": "Đề nghị công việc",
    "feedback": "Phản hồi",
    "reminder": "Nhắc nhở",
    "rejection": "Từ chối",
    "welcome": "Chào mừng"
  }
}
```

### 9. Get Template Types

**Endpoint:** `GET /message-templates/types`  
**Authentication:** Not required

#### Response

```json
{
  "status": "success",
  "data": {
    "email": "Email",
    "sms": "SMS"
  }
}
```

## Messages API

### 1. List Messages

**Endpoint:** `GET /messages`  
**Authentication:** Required

#### Query Parameters

| Parameter        | Type    | Description               | Example              |
| ---------------- | ------- | ------------------------- | -------------------- |
| `candidate_id`   | integer | Filter by candidate       | `123`                |
| `job_posting_id` | integer | Filter by job posting     | `456`                |
| `type`           | string  | Filter by message type    | `email`              |
| `category`       | string  | Filter by category        | `interview`          |
| `status`         | string  | Filter by status          | `sent`               |
| `template_id`    | integer | Filter by template        | `1`                  |
| `thread_id`      | string  | Filter by thread          | `msg_001`            |
| `priority_min`   | integer | Minimum priority (1-10)   | `5`                  |
| `priority_max`   | integer | Maximum priority (1-10)   | `10`                 |
| `date_from`      | date    | Filter from date          | `2024-07-01`         |
| `date_to`        | date    | Filter to date            | `2024-07-31`         |
| `search`         | string  | Search in subject/content | `phỏng vấn`          |
| `sort`           | string  | Sort field                | `-created_at`        |
| `include`        | string  | Include relationships     | `candidate,template` |

#### Response Example

```json
{
  "data": [
    {
      "id": 101,
      "type": "email",
      "type_name": "Email",
      "category": "interview",
      "category_name": "Phỏng vấn",
      "candidate_id": 123,
      "job_posting_id": 456,
      "template_id": 1,
      "parent_message_id": null,
      "thread_id": null,
      "to_email": "<EMAIL>",
      "to_phone": null,
      "to_name": "Nguyễn Văn An",
      "from_email": "<EMAIL>",
      "from_name": "Phòng Nhân sự",
      "subject": "Lời mời phỏng vấn - Vị trí Lập trình viên PHP Senior",
      "content": "Kính gửi Nguyễn Văn An,\n\nChúng tôi rất vui mừng...",
      "status": "delivered",
      "status_name": "Đã nhận",
      "status_color": "bg-green-500",
      "priority": 7,
      "is_scheduled": false,
      "is_due": false,
      "delivery_time": 2.5,
      "read_time": null,
      "scheduled_at": null,
      "sent_at": "2024-07-21 09:00:00",
      "delivered_at": "2024-07-21 09:00:03",
      "read_at": null,
      "created_at": "2024-07-21 08:55:00",
      "updated_at": "2024-07-21 09:00:03",
      "metadata": {
        "campaign_id": "summer_2024",
        "source": "manual"
      },
      "external_id": "msg_ext_12345",
      "candidate": {
        "id": 123,
        "name": "Nguyễn Văn An",
        "email": "<EMAIL>",
        "phone": "0123456789",
        "position": "Lập trình viên PHP"
      },
      "template": {
        "id": 1,
        "name": "Lời mời phỏng vấn - Email chính thức",
        "category": "interview",
        "type": "email",
        "version": 1
      },
      "replies_count": 0
    }
  ],
  "links": {
    "first": "http://localhost:8000/api/v1/messages?page=1",
    "last": "http://localhost:8000/api/v1/messages?page=5",
    "prev": null,
    "next": "http://localhost:8000/api/v1/messages?page=2"
  },
  "meta": {
    "current_page": 1,
    "per_page": 15,
    "total": 67
  }
}
```

### 2. Send Message

**Endpoint:** `POST /messages`
**Authentication:** Required

#### Request Body

```json
{
  "type": "email",
  "category": "interview",
  "candidate_id": 123,
  "job_posting_id": 456,
  "template_id": 1,
  "to_email": "<EMAIL>",
  "to_name": "Nguyễn Văn An",
  "from_email": "<EMAIL>",
  "from_name": "Phòng Nhân sự",
  "subject": "Lời mời phỏng vấn - Vị trí Lập trình viên PHP Senior",
  "content": "Kính gửi Nguyễn Văn An,\n\nChúng tôi rất vui mừng...",
  "priority": 7,
  "scheduled_at": "2024-07-22 09:00:00",
  "metadata": {
    "campaign_id": "summer_2024",
    "source": "manual"
  },
  "template_data": {
    "candidate_name": "Nguyễn Văn An",
    "job_title": "Lập trình viên PHP Senior",
    "company_name": "Công ty TNHH ABC",
    "interview_date": "25/07/2024",
    "interview_time": "14:00"
  }
}
```

#### Response

```json
{
  "status": "success",
  "message": "Tin nhắn đã được gửi thành công",
  "data": {
    "id": 102,
    "type": "email",
    "category": "interview",
    "status": "queued",
    "scheduled_at": "2024-07-22 09:00:00",
    "created_at": "2024-07-21 15:30:00",
    "candidate": {
      "id": 123,
      "name": "Nguyễn Văn An",
      "email": "<EMAIL>"
    }
  }
}
```

### 3. Get Message

**Endpoint:** `GET /messages/{id}`
**Authentication:** Required

#### Response

```json
{
  "status": "success",
  "data": {
    "id": 101,
    "type": "email",
    "category": "interview",
    "subject": "Lời mời phỏng vấn - Vị trí Lập trình viên PHP Senior",
    "content": "Kính gửi Nguyễn Văn An,\n\nChúng tôi rất vui mừng...",
    "status": "delivered",
    "priority": 7,
    "sent_at": "2024-07-21 09:00:00",
    "delivered_at": "2024-07-21 09:00:03",
    "candidate": {
      "id": 123,
      "name": "Nguyễn Văn An",
      "email": "<EMAIL>"
    },
    "template": {
      "id": 1,
      "name": "Lời mời phỏng vấn - Email chính thức"
    },
    "replies": []
  }
}
```

### 4. Update Message Status

**Endpoint:** `PUT /messages/{id}`
**Authentication:** Required

#### Request Body

```json
{
  "status": "read",
  "external_id": "msg_ext_12345",
  "metadata": {
    "read_location": "mobile_app",
    "user_agent": "ATS Mobile App v1.2"
  }
}
```

#### Response

```json
{
  "status": "success",
  "message": "Trạng thái tin nhắn đã được cập nhật",
  "data": {
    "id": 101,
    "status": "read",
    "read_at": "2024-07-21 16:45:00",
    "external_id": "msg_ext_12345"
  }
}
```

### 5. Delete Message

**Endpoint:** `DELETE /messages/{id}`
**Authentication:** Required

#### Response

```json
{
  "status": "success",
  "message": "Tin nhắn đã được xóa thành công"
}
```

### 6. Send Bulk Messages

**Endpoint:** `POST /messages/bulk-send`
**Authentication:** Required

#### Request Body

```json
{
  "template_id": 1,
  "candidate_ids": [123, 124, 125, 126],
  "job_posting_id": 456,
  "scheduled_at": "2024-07-22 09:00:00",
  "priority": 8
}
```

#### Response

```json
{
  "status": "success",
  "message": "Tin nhắn hàng loạt đã được gửi thành công",
  "data": {
    "total_sent": 3,
    "total_failed": 1,
    "success": [
      {
        "id": 103,
        "candidate_id": 123,
        "status": "queued",
        "scheduled_at": "2024-07-22 09:00:00"
      },
      {
        "id": 104,
        "candidate_id": 124,
        "status": "queued",
        "scheduled_at": "2024-07-22 09:00:00"
      },
      {
        "id": 105,
        "candidate_id": 125,
        "status": "queued",
        "scheduled_at": "2024-07-22 09:00:00"
      }
    ],
    "failed": [
      {
        "candidate_id": 126,
        "candidate_name": "Trần Văn Bình",
        "error": "Email address is invalid"
      }
    ]
  }
}
```

### 7. Get Message Thread

**Endpoint:** `GET /messages/{id}/thread`
**Authentication:** Required

#### Response

```json
{
  "status": "success",
  "data": {
    "thread_id": "msg_101",
    "total_messages": 3,
    "messages": [
      {
        "id": 101,
        "type": "email",
        "subject": "Lời mời phỏng vấn - Vị trí Lập trình viên PHP Senior",
        "status": "read",
        "created_at": "2024-07-21 09:00:00",
        "candidate": {
          "id": 123,
          "name": "Nguyễn Văn An"
        }
      },
      {
        "id": 107,
        "type": "email",
        "subject": "Re: Lời mời phỏng vấn - Vị trí Lập trình viên PHP Senior",
        "status": "sent",
        "created_at": "2024-07-21 14:30:00",
        "created_by": {
          "id": 1,
          "name": "HR Manager"
        }
      },
      {
        "id": 108,
        "type": "email",
        "subject": "Re: Lời mời phỏng vấn - Xác nhận tham gia",
        "status": "delivered",
        "created_at": "2024-07-21 16:15:00",
        "candidate": {
          "id": 123,
          "name": "Nguyễn Văn An"
        }
      }
    ]
  }
}
```

### 8. Reply to Message

**Endpoint:** `POST /messages/{id}/reply`
**Authentication:** Required

#### Request Body

```json
{
  "type": "email",
  "category": "interview",
  "to_email": "<EMAIL>",
  "to_name": "Nguyễn Văn An",
  "subject": "Re: Lời mời phỏng vấn - Thông tin bổ sung",
  "content": "Kính gửi Nguyễn Văn An,\n\nCảm ơn bạn đã xác nhận tham gia phỏng vấn.\n\nMột số thông tin bổ sung:\n- Vui lòng mang theo laptop cá nhân\n- Thời gian phỏng vấn dự kiến: 90 phút\n\nTrân trọng,\nPhòng Nhân sự",
  "priority": 6
}
```

#### Response

```json
{
  "status": "success",
  "message": "Phản hồi đã được gửi thành công",
  "data": {
    "id": 109,
    "parent_message_id": 101,
    "thread_id": "msg_101",
    "type": "email",
    "status": "sent",
    "subject": "Re: Lời mời phỏng vấn - Thông tin bổ sung",
    "created_at": "2024-07-21 17:00:00"
  }
}
```

### 9. Get Message Statistics

**Endpoint:** `GET /messages/statistics`
**Authentication:** Required

#### Query Parameters

| Parameter   | Type | Description          | Example      |
| ----------- | ---- | -------------------- | ------------ |
| `date_from` | date | Statistics from date | `2024-07-01` |
| `date_to`   | date | Statistics to date   | `2024-07-31` |

#### Response

```json
{
  "status": "success",
  "data": {
    "total_messages": 1247,
    "by_status": {
      "draft": 23,
      "queued": 45,
      "sent": 892,
      "delivered": 856,
      "read": 234,
      "failed": 31
    },
    "by_type": {
      "email": 1089,
      "sms": 134,
      "note": 24
    },
    "by_category": {
      "interview": 456,
      "offer": 123,
      "feedback": 234,
      "reminder": 189,
      "rejection": 167,
      "general": 78
    },
    "success_rate": 94.2,
    "average_delivery_time": 2.8
  }
}
```

## Data Models

### MessageTemplate Model

```json
{
  "id": "integer",
  "name": "string",
  "subject": "string|null",
  "content": "string",
  "variables": "array",
  "category": "enum[interview,offer,feedback,reminder,rejection,welcome]",
  "type": "enum[email,sms]",
  "language": "string",
  "version": "integer",
  "is_active": "boolean",
  "parent_template_id": "integer|null",
  "created_by": "integer",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Message Model

```json
{
  "id": "integer",
  "type": "enum[email,sms,note]",
  "category": "enum[interview,offer,feedback,reminder,rejection,general]",
  "candidate_id": "integer",
  "job_posting_id": "integer|null",
  "template_id": "integer|null",
  "parent_message_id": "integer|null",
  "thread_id": "string|null",
  "to_email": "string|null",
  "to_phone": "string|null",
  "to_name": "string|null",
  "from_email": "string|null",
  "from_name": "string|null",
  "subject": "string|null",
  "content": "string",
  "status": "enum[draft,queued,sent,delivered,read,failed]",
  "priority": "integer",
  "scheduled_at": "datetime|null",
  "sent_at": "datetime|null",
  "delivered_at": "datetime|null",
  "read_at": "datetime|null",
  "error_message": "string|null",
  "metadata": "object|null",
  "external_id": "string|null",
  "created_by": "integer",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## Error Responses

### Validation Error (422)

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "name": ["Tên template là bắt buộc."],
    "category": ["Danh mục không hợp lệ."],
    "to_email": ["Email người nhận không hợp lệ."]
  }
}
```

### Not Found Error (404)

```json
{
  "message": "Template không tồn tại.",
  "status": "error"
}
```

### Unauthorized Error (401)

```json
{
  "message": "Unauthenticated."
}
```

### Server Error (500)

```json
{
  "status": "error",
  "message": "Không thể xử lý yêu cầu",
  "error": "Internal server error details"
}
```

## Common Workflows

### 1. Creating and Using a Template

```bash
# 1. Create template
POST /message-templates
{
  "name": "Lời mời phỏng vấn",
  "subject": "Phỏng vấn {{job_title}}",
  "content": "Xin chào {{candidate_name}}...",
  "category": "interview",
  "type": "email"
}

# 2. Preview template
POST /message-templates/1/preview
{
  "data": {
    "candidate_name": "Nguyễn Văn An",
    "job_title": "Developer"
  }
}

# 3. Send message using template
POST /messages
{
  "template_id": 1,
  "candidate_id": 123,
  "template_data": {
    "candidate_name": "Nguyễn Văn An",
    "job_title": "Developer"
  }
}
```

### 2. Message Threading

```bash
# 1. Send initial message
POST /messages
{
  "type": "email",
  "candidate_id": 123,
  "subject": "Lời mời phỏng vấn",
  "content": "..."
}

# 2. Reply to message
POST /messages/101/reply
{
  "subject": "Re: Lời mời phỏng vấn",
  "content": "Thông tin bổ sung..."
}

# 3. Get full thread
GET /messages/101/thread
```

### 3. Bulk Messaging

```bash
# 1. Get template categories
GET /message-templates/categories

# 2. Find suitable template
GET /message-templates?category=interview&type=email

# 3. Send bulk messages
POST /messages/bulk-send
{
  "template_id": 1,
  "candidate_ids": [123, 124, 125],
  "job_posting_id": 456
}

# 4. Check statistics
GET /messages/statistics?date_from=2024-07-01
```

### 4. Message Status Tracking

```bash
# 1. Send message
POST /messages
{
  "type": "email",
  "candidate_id": 123,
  "subject": "Test message",
  "content": "Hello world"
}

# 2. Check message status
GET /messages/101

# 3. Update status (webhook from email provider)
PUT /messages/101
{
  "status": "delivered",
  "external_id": "provider_msg_id_123"
}

# 4. Mark as read (when candidate opens email)
PUT /messages/101
{
  "status": "read"
}
```

### 5. Template Management

```bash
# 1. List all templates
GET /message-templates?include=createdBy,messages

# 2. Filter by category
GET /message-templates?category=interview&is_active=true

# 3. Search templates
GET /message-templates?search=phỏng vấn

# 4. Create new version
PUT /message-templates/1
{
  "content": "Updated content..."
}

# 5. Duplicate template
POST /message-templates/1/duplicate
```

## Public Testing Routes

For development and testing purposes, the following public routes are available without authentication:

```
GET /api/v1/message-templates
GET /api/v1/message-templates/categories
GET /api/v1/message-templates/types
GET /api/v1/message-templates/{id}
POST /api/v1/message-templates/{id}/preview
GET /api/v1/messages
GET /api/v1/messages/statistics
GET /api/v1/messages/{id}
GET /api/v1/messages/{id}/thread
```

## Rate Limiting

- **General API calls**: 60 requests per minute per user
- **Bulk operations**: 10 requests per minute per user
- **Template preview**: 30 requests per minute per user

## Webhooks

The system supports webhooks for external email/SMS providers to update message status:

```bash
POST /api/webhooks/message-status
{
  "external_id": "provider_msg_id_123",
  "status": "delivered",
  "timestamp": "2024-07-21T09:00:03Z",
  "provider": "sendgrid",
  "event_data": {
    "reason": "delivered to inbox"
  }
}
```
