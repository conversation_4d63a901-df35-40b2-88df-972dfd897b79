# Interview Feedback API Documentation

## Overview

The Interview Feedback API provides comprehensive functionality for managing interview feedback in the ATS HireFlow system. This API allows interviewers to submit, view, update, and manage feedback for completed interviews.

## Base URL
```
/api/v1/interview-feedback
```

## Authentication
All endpoints require authentication via Bear<PERSON> token:
```
Authorization: Bearer {your-token}
```

## Data Structures

### InterviewFeedback Object

```typescript
interface InterviewFeedback {
  id: number;
  interview_id: number;
  interviewer_id: number;
  rating: number | null;                    // 0-5 scale
  comments: string | null;
  recommend: boolean | null;
  strengths: string[] | null;               // Array of strength descriptions
  concerns: string[] | null;                // Array of concern descriptions
  next_round_recommendation: NextRoundType | null;
  technical_score: number | null;           // 0-100 scale
  communication_score: number | null;       // 0-100 scale
  cultural_fit_score: number | null;        // 0-100 scale
  overall_score: number;                    // Computed average of scores
  created_at: string;                       // ISO 8601 format
  updated_at: string;                       // ISO 8601 format
  
  // Relationships (when included)
  interview?: InterviewSummary;
  interviewer?: InterviewerSummary;
}

type NextRoundType = 
  | 'screening' 
  | 'technical' 
  | 'case-study' 
  | 'portfolio' 
  | 'cultural' 
  | 'final' 
  | 'offer' 
  | 'reject';

interface InterviewSummary {
  id: number;
  date: string;                             // YYYY-MM-DD format
  time: string;                             // HH:mm format
  type: string;
  interview_type: string;
  round: number;
}

interface InterviewerSummary {
  id: number;
  name: string;
  email: string;
  department: string;
}
```

### API Response Format

```typescript
interface ApiResponse<T> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
  error?: string;
}

interface PaginatedResponse<T> {
  data: T[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}
```

## API Endpoints

### 1. List Interview Feedback

**GET** `/api/v1/interview-feedback`

Retrieve a paginated list of interview feedback with filtering and sorting options.

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | integer | Page number (default: 1) |
| `per_page` | integer | Items per page (default: 15, max: 100) |
| `sort` | string | Sort field: `rating`, `technical_score`, `communication_score`, `cultural_fit_score`, `created_at`, `updated_at` |
| `filter[interview_id]` | integer | Filter by specific interview ID |
| `filter[interviewer_id]` | integer | Filter by specific interviewer ID |
| `filter[recommend]` | boolean | Filter by recommendation (true/false) |
| `filter[next_round_recommendation]` | string | Filter by next round type |
| `filter[rating_min]` | number | Minimum rating filter (0-5) |
| `filter[technical_score_min]` | integer | Minimum technical score (0-100) |
| `filter[communication_score_min]` | integer | Minimum communication score (0-100) |
| `filter[cultural_fit_score_min]` | integer | Minimum cultural fit score (0-100) |
| `include` | string | Include relationships: `interview.candidate`, `interview.jobPosting`, `interviewer.user` |

#### Example Request
```bash
GET /api/v1/interview-feedback?filter[recommend]=true&filter[rating_min]=4&include=interview.candidate,interviewer.user&sort=-created_at&per_page=20
```

#### Response
```json
{
  "data": [
    {
      "id": 1,
      "interview_id": 15,
      "interviewer_id": 3,
      "rating": 4.5,
      "comments": "Excellent candidate with strong technical skills",
      "recommend": true,
      "strengths": [
        "Strong problem-solving abilities",
        "Excellent communication skills",
        "Good cultural fit"
      ],
      "concerns": [
        "Limited experience with microservices"
      ],
      "next_round_recommendation": "technical",
      "technical_score": 85,
      "communication_score": 90,
      "cultural_fit_score": 88,
      "overall_score": 87.67,
      "created_at": "2025-07-19 10:30:00",
      "updated_at": "2025-07-19 10:30:00",
      "interview": {
        "id": 15,
        "date": "2025-07-18",
        "time": "14:00",
        "type": "video",
        "interview_type": "screening",
        "round": 1
      },
      "interviewer": {
        "id": 3,
        "name": "John Smith",
        "email": "<EMAIL>",
        "department": "Engineering"
      }
    }
  ],
  "links": {
    "first": "http://api.example.com/interview-feedback?page=1",
    "last": "http://api.example.com/interview-feedback?page=5",
    "prev": null,
    "next": "http://api.example.com/interview-feedback?page=2"
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 5,
    "per_page": 15,
    "to": 15,
    "total": 73
  }
}
```

### 2. Create Interview Feedback

**POST** `/api/v1/interview-feedback`

Submit new interview feedback for a completed interview.

#### Authorization
- User must have `provide_feedback` permission
- User must be the assigned interviewer for the interview
- Feedback cannot already exist for this interview-interviewer combination

#### Request Body

```json
{
  "interview_id": 15,
  "interviewer_id": 3,
  "rating": 4.5,
  "comments": "Excellent candidate with strong technical skills",
  "recommend": true,
  "strengths": [
    "Strong problem-solving abilities",
    "Excellent communication skills"
  ],
  "concerns": [
    "Limited experience with microservices"
  ],
  "next_round_recommendation": "technical",
  "technical_score": 85,
  "communication_score": 90,
  "cultural_fit_score": 88
}
```

#### Validation Rules

| Field | Rules |
|-------|-------|
| `interview_id` | required, must exist in interviews table |
| `interviewer_id` | required, must exist in interviewers table |
| `rating` | optional, numeric, between 0 and 5 |
| `comments` | optional, string |
| `recommend` | optional, boolean |
| `strengths` | optional, array of strings (max 255 chars each) |
| `concerns` | optional, array of strings (max 255 chars each) |
| `next_round_recommendation` | optional, enum: screening, technical, case-study, portfolio, cultural, final, offer, reject |
| `technical_score` | optional, integer, between 0 and 100 |
| `communication_score` | optional, integer, between 0 and 100 |
| `cultural_fit_score` | optional, integer, between 0 and 100 |

#### Success Response (201)
```json
{
  "status": "success",
  "message": "Interview feedback submitted successfully",
  "data": {
    "id": 1,
    "interview_id": 15,
    "interviewer_id": 3,
    // ... full feedback object
  }
}
```

#### Error Responses

**422 Validation Error**
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "interview_id": ["Feedback already exists for this interview."],
    "rating": ["The rating must be between 0 and 5."]
  }
}
```

**500 Server Error**
```json
{
  "status": "error",
  "message": "Failed to submit interview feedback",
  "error": "Database connection failed"
}
```

### 3. Get Specific Interview Feedback

**GET** `/api/v1/interview-feedback/{id}`

Retrieve a specific interview feedback record by ID.

#### Authorization
- User must have permission to view the feedback
- Typically restricted to the interviewer who created it, recruiters, or admins

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | integer | Interview feedback ID |

#### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `include` | string | Include relationships: `interview.candidate`, `interview.jobPosting`, `interviewer.user` |

#### Success Response (200)
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "interview_id": 15,
    "interviewer_id": 3,
    "rating": 4.5,
    "comments": "Excellent candidate with strong technical skills",
    "recommend": true,
    "strengths": [
      "Strong problem-solving abilities",
      "Excellent communication skills"
    ],
    "concerns": [
      "Limited experience with microservices"
    ],
    "next_round_recommendation": "technical",
    "technical_score": 85,
    "communication_score": 90,
    "cultural_fit_score": 88,
    "overall_score": 87.67,
    "created_at": "2025-07-19 10:30:00",
    "updated_at": "2025-07-19 10:30:00"
  }
}
```

#### Error Responses

**403 Forbidden**
```json
{
  "message": "This action is unauthorized."
}
```

**404 Not Found**
```json
{
  "message": "No query results for model [App\\Models\\InterviewFeedback] 999"
}
```

### 4. Update Interview Feedback

**PUT/PATCH** `/api/v1/interview-feedback/{id}`

Update an existing interview feedback record.

#### Authorization
- User must have permission to update the feedback
- Typically restricted to the interviewer who created it, recruiters, or admins

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | integer | Interview feedback ID |

#### Request Body
All fields are optional for updates:

```json
{
  "rating": 4.0,
  "comments": "Updated comments after further consideration",
  "recommend": false,
  "strengths": [
    "Good technical foundation",
    "Enthusiastic attitude"
  ],
  "concerns": [
    "Needs more experience",
    "Communication could be clearer"
  ],
  "next_round_recommendation": "hold",
  "technical_score": 75,
  "communication_score": 70,
  "cultural_fit_score": 80
}
```

#### Success Response (200)
```json
{
  "status": "success",
  "message": "Interview feedback updated successfully",
  "data": {
    "id": 1,
    // ... updated feedback object
  }
}
```

#### Error Responses

**403 Forbidden**
```json
{
  "message": "This action is unauthorized."
}
```

**422 Validation Error**
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "rating": ["The rating must be between 0 and 5."]
  }
}
```

**500 Server Error**
```json
{
  "status": "error",
  "message": "Failed to update interview feedback",
  "error": "Database error occurred"
}
```

### 5. Delete Interview Feedback

**DELETE** `/api/v1/interview-feedback/{id}`

Delete an interview feedback record.

#### Authorization
- User must have permission to delete the feedback
- Typically restricted to the interviewer who created it, recruiters, or admins

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | integer | Interview feedback ID |

#### Success Response (200)
```json
{
  "status": "success",
  "message": "Interview feedback deleted successfully"
}
```

#### Error Responses

**403 Forbidden**
```json
{
  "message": "This action is unauthorized."
}
```

**404 Not Found**
```json
{
  "message": "No query results for model [App\\Models\\InterviewFeedback] 999"
}
```

**500 Server Error**
```json
{
  "status": "error",
  "message": "Failed to delete interview feedback",
  "error": "Database error occurred"
}
```

## Frontend Integration Guide

### TypeScript Service Class

```typescript
import axios, { AxiosResponse } from 'axios';

interface ApiConfig {
  baseURL: string;
  token: string;
}

export class InterviewFeedbackService {
  private api;

  constructor(config: ApiConfig) {
    this.api = axios.create({
      baseURL: config.baseURL,
      headers: {
        'Authorization': `Bearer ${config.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
  }

  /**
   * Get paginated list of interview feedback
   */
  async getFeedbackList(params: {
    page?: number;
    per_page?: number;
    sort?: string;
    include?: string;
    filters?: Record<string, any>;
  } = {}): Promise<PaginatedResponse<InterviewFeedback>> {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.per_page) queryParams.append('per_page', params.per_page.toString());
    if (params.sort) queryParams.append('sort', params.sort);
    if (params.include) queryParams.append('include', params.include);

    // Add filters
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          queryParams.append(`filter[${key}]`, value.toString());
        }
      });
    }

    const response: AxiosResponse<PaginatedResponse<InterviewFeedback>> =
      await this.api.get(`/interview-feedback?${queryParams.toString()}`);

    return response.data;
  }

  /**
   * Create new interview feedback
   */
  async createFeedback(data: Partial<InterviewFeedback>): Promise<ApiResponse<InterviewFeedback>> {
    const response: AxiosResponse<ApiResponse<InterviewFeedback>> =
      await this.api.post('/interview-feedback', data);

    return response.data;
  }

  /**
   * Get specific interview feedback
   */
  async getFeedback(id: number, include?: string): Promise<ApiResponse<InterviewFeedback>> {
    const params = include ? `?include=${include}` : '';
    const response: AxiosResponse<ApiResponse<InterviewFeedback>> =
      await this.api.get(`/interview-feedback/${id}${params}`);

    return response.data;
  }

  /**
   * Update interview feedback
   */
  async updateFeedback(id: number, data: Partial<InterviewFeedback>): Promise<ApiResponse<InterviewFeedback>> {
    const response: AxiosResponse<ApiResponse<InterviewFeedback>> =
      await this.api.patch(`/interview-feedback/${id}`, data);

    return response.data;
  }

  /**
   * Delete interview feedback
   */
  async deleteFeedback(id: number): Promise<ApiResponse<null>> {
    const response: AxiosResponse<ApiResponse<null>> =
      await this.api.delete(`/interview-feedback/${id}`);

    return response.data;
  }
}
```

### Error Handling

```typescript
import { AxiosError } from 'axios';

export class FeedbackApiError extends Error {
  public status: number;
  public errors?: Record<string, string[]>;

  constructor(message: string, status: number, errors?: Record<string, string[]>) {
    super(message);
    this.name = 'FeedbackApiError';
    this.status = status;
    this.errors = errors;
  }
}

export const handleApiError = (error: AxiosError): never => {
  if (error.response) {
    const { status, data } = error.response;

    switch (status) {
      case 401:
        throw new FeedbackApiError('Authentication required', 401);
      case 403:
        throw new FeedbackApiError('Access denied', 403);
      case 404:
        throw new FeedbackApiError('Feedback not found', 404);
      case 422:
        const validationData = data as { message: string; errors: Record<string, string[]> };
        throw new FeedbackApiError(
          validationData.message || 'Validation failed',
          422,
          validationData.errors
        );
      case 500:
        const serverData = data as { message: string; error?: string };
        throw new FeedbackApiError(
          serverData.message || 'Server error occurred',
          500
        );
      default:
        throw new FeedbackApiError(`HTTP ${status} error`, status);
    }
  } else if (error.request) {
    throw new FeedbackApiError('Network error - no response received', 0);
  } else {
    throw new FeedbackApiError('Request setup error', 0);
  }
};

// Usage example with error handling
export const createFeedbackWithErrorHandling = async (
  service: InterviewFeedbackService,
  feedbackData: Partial<InterviewFeedback>
): Promise<InterviewFeedback> => {
  try {
    const response = await service.createFeedback(feedbackData);
    if (response.status === 'success' && response.data) {
      return response.data;
    }
    throw new Error('Unexpected response format');
  } catch (error) {
    if (error instanceof AxiosError) {
      handleApiError(error);
    }
    throw error;
  }
};
```

### React Hook Example

```typescript
import { useState, useEffect } from 'react';
import { InterviewFeedbackService } from './InterviewFeedbackService';

interface UseFeedbackListOptions {
  page?: number;
  per_page?: number;
  filters?: Record<string, any>;
  sort?: string;
  include?: string;
}

export const useFeedbackList = (
  service: InterviewFeedbackService,
  options: UseFeedbackListOptions = {}
) => {
  const [data, setData] = useState<InterviewFeedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<any>(null);

  const fetchFeedback = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await service.getFeedbackList(options);
      setData(response.data);
      setPagination(response.meta);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch feedback');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFeedback();
  }, [options.page, options.per_page, JSON.stringify(options.filters)]);

  const refresh = () => fetchFeedback();

  return {
    data,
    loading,
    error,
    pagination,
    refresh
  };
};

export const useFeedbackMutations = (service: InterviewFeedbackService) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createFeedback = async (data: Partial<InterviewFeedback>) => {
    try {
      setLoading(true);
      setError(null);

      const response = await service.createFeedback(data);
      if (response.status === 'success') {
        return response.data;
      }
      throw new Error(response.message || 'Failed to create feedback');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create feedback';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateFeedback = async (id: number, data: Partial<InterviewFeedback>) => {
    try {
      setLoading(true);
      setError(null);

      const response = await service.updateFeedback(id, data);
      if (response.status === 'success') {
        return response.data;
      }
      throw new Error(response.message || 'Failed to update feedback');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update feedback';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteFeedback = async (id: number) => {
    try {
      setLoading(true);
      setError(null);

      const response = await service.deleteFeedback(id);
      if (response.status === 'success') {
        return true;
      }
      throw new Error(response.message || 'Failed to delete feedback');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete feedback';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    createFeedback,
    updateFeedback,
    deleteFeedback,
    loading,
    error
  };
};
```

## Common Usage Examples

### 1. Fetch Feedback for Specific Interview

```typescript
const service = new InterviewFeedbackService({
  baseURL: 'https://api.example.com/api/v1',
  token: 'your-auth-token'
});

// Get all feedback for interview ID 15
const interviewFeedback = await service.getFeedbackList({
  filters: { interview_id: 15 },
  include: 'interviewer.user'
});

console.log('Feedback count:', interviewFeedback.data.length);
```

### 2. Create Feedback with Validation

```typescript
const feedbackData: Partial<InterviewFeedback> = {
  interview_id: 15,
  interviewer_id: 3,
  rating: 4.5,
  recommend: true,
  strengths: [
    'Excellent problem-solving skills',
    'Strong communication'
  ],
  concerns: [
    'Limited experience with our tech stack'
  ],
  technical_score: 85,
  communication_score: 90,
  cultural_fit_score: 88,
  next_round_recommendation: 'technical'
};

try {
  const result = await service.createFeedback(feedbackData);
  console.log('Feedback created:', result.data);
} catch (error) {
  if (error instanceof FeedbackApiError && error.status === 422) {
    console.log('Validation errors:', error.errors);
  } else {
    console.error('Failed to create feedback:', error.message);
  }
}
```

### 3. Filter High-Rated Candidates

```typescript
// Get feedback with rating >= 4.0 and positive recommendations
const highRatedFeedback = await service.getFeedbackList({
  filters: {
    rating_min: 4.0,
    recommend: true
  },
  sort: '-rating',
  include: 'interview.candidate',
  per_page: 50
});

const topCandidates = highRatedFeedback.data.map(feedback => ({
  candidateName: feedback.interview?.candidate?.name,
  rating: feedback.rating,
  overallScore: feedback.overall_score
}));
```

### 4. Update Feedback After Review

```typescript
const feedbackId = 123;
const updates = {
  rating: 4.0,
  comments: 'Updated after team discussion',
  next_round_recommendation: 'final' as NextRoundType
};

try {
  const updated = await service.updateFeedback(feedbackId, updates);
  console.log('Feedback updated successfully');
} catch (error) {
  console.error('Update failed:', error.message);
}
```

## Best Practices

### 1. Error Handling
- Always wrap API calls in try-catch blocks
- Handle different error types appropriately (validation, authorization, server errors)
- Provide meaningful error messages to users

### 2. Performance Optimization
- Use pagination for large datasets
- Only include relationships when needed using the `include` parameter
- Implement client-side caching for frequently accessed data

### 3. Data Validation
- Validate data on the frontend before sending to API
- Handle validation errors gracefully with user-friendly messages
- Use TypeScript interfaces to ensure type safety

### 4. Security
- Always include authentication tokens
- Validate user permissions before showing UI elements
- Sanitize user input before sending to API

### 5. User Experience
- Show loading states during API calls
- Implement optimistic updates where appropriate
- Provide clear feedback on successful operations

## Status Codes Reference

| Code | Meaning | Description |
|------|---------|-------------|
| 200 | OK | Request successful |
| 201 | Created | Resource created successfully |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Access denied |
| 404 | Not Found | Resource not found |
| 422 | Unprocessable Entity | Validation failed |
| 500 | Internal Server Error | Server error occurred |

This comprehensive documentation provides everything needed for frontend developers to integrate with the Interview Feedback API effectively.
```
