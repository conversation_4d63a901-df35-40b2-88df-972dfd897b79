# ATS Message System - Complete Documentation

## 📋 Overview

The ATS Message System is a comprehensive messaging solution designed for recruitment workflows, supporting both message templates and direct messaging with Vietnamese content optimization.

## 📁 Documentation Files

### 1. `api_message.md`

Complete API documentation with:

- ✅ All endpoint descriptions for Message Templates and Messages
- ✅ Request/response examples with realistic Vietnamese data
- ✅ Authentication requirements and headers
- ✅ Query parameters for filtering, sorting, and pagination
- ✅ Error response formats and status codes
- ✅ Data models/schemas for all entities
- ✅ Usage examples for common workflows

### 2. `api_message.json`

Postman collection including:

- ✅ All API endpoints with proper HTTP methods and URLs
- ✅ Pre-configured request headers (Content-Type, Accept, Authorization)
- ✅ Sample request bodies with Vietnamese test data
- ✅ Environment variables for base URL and authentication tokens
- ✅ Organized folders for Message Templates and Messages endpoints
- ✅ Test scripts for validating responses

## 🚀 Quick Start

### 1. Import Postman Collection

```bash
# Import the collection file
File > Import > api_message.json
```

### 2. Set Environment Variables

```javascript
// Set these variables in Postman
baseUrl: "http://localhost:8000/api/v1";
authToken: ""; // Will be set automatically after login
```

### 3. Test Authentication

```bash
# Run the Login request first
POST {{baseUrl}}/auth/login
{
  "email": "<EMAIL>",
  "password": "password"
}
```

## 🔧 API Endpoints Summary

### Message Templates

- `GET /message-templates` - List templates with filtering
- `POST /message-templates` - Create new template
- `GET /message-templates/{id}` - Get specific template
- `PUT /message-templates/{id}` - Update template
- `DELETE /message-templates/{id}` - Delete template
- `POST /message-templates/{id}/preview` - Preview with data
- `POST /message-templates/{id}/duplicate` - Duplicate template
- `GET /message-templates/categories` - Get categories
- `GET /message-templates/types` - Get types

### Messages

- `GET /messages` - List messages with filtering
- `POST /messages` - Send new message
- `GET /messages/{id}` - Get specific message
- `PUT /messages/{id}` - Update message status
- `DELETE /messages/{id}` - Delete message
- `POST /messages/bulk-send` - Send bulk messages
- `GET /messages/{id}/thread` - Get message thread
- `POST /messages/{id}/reply` - Reply to message
- `GET /messages/statistics` - Get statistics

## 🎯 Key Features

### Template System

- **Multi-language support** (Vietnamese/English)
- **Variable substitution** with `{{variable}}` syntax
- **Conditional rendering** with `{{#if}}...{{/if}}`
- **Loop support** with `{{#each}}...{{/each}}`
- **Template versioning** and history tracking
- **Professional Vietnamese templates** for recruitment

### Message Management

- **Multiple message types**: Email, SMS, Note
- **Message threading** and reply functionality
- **Scheduling** and priority management
- **Delivery tracking** with timestamps
- **Bulk messaging** with template integration
- **Comprehensive statistics** and reporting

### Vietnamese Content

- **Professional templates** for recruitment scenarios
- **Realistic sample data** with Vietnamese names and addresses
- **Proper Vietnamese formatting** and tone of voice
- **Emoji integration** for modern communication

## 📊 Sample Data

### Template Categories (Vietnamese)

```json
{
  "interview": "Phỏng vấn",
  "offer": "Đề nghị công việc",
  "feedback": "Phản hồi",
  "reminder": "Nhắc nhở",
  "rejection": "Từ chối",
  "welcome": "Chào mừng"
}
```

### Sample Template Variables

```json
{
  "candidate_name": "Nguyễn Văn An",
  "job_title": "Lập trình viên PHP Senior",
  "company_name": "Công ty TNHH ABC",
  "interview_date": "25/07/2024",
  "interview_time": "14:00",
  "interview_location": "Tầng 5, Tòa nhà ABC, 123 Đường XYZ",
  "recruiter_name": "Trần Thị Bình",
  "recruiter_email": "<EMAIL>",
  "recruiter_phone": "0123456789"
}
```

## 🔐 Authentication

Most endpoints require Bearer token authentication:

```http
Authorization: Bearer {your-token}
Content-Type: application/json
Accept: application/json
```

## 🧪 Testing Workflows

### 1. Template Creation & Usage

```bash
# 1. Create template
POST /message-templates
# 2. Preview template
POST /message-templates/{id}/preview
# 3. Send message using template
POST /messages
```

### 2. Message Threading

```bash
# 1. Send initial message
POST /messages
# 2. Reply to message
POST /messages/{id}/reply
# 3. Get full thread
GET /messages/{id}/thread
```

### 3. Bulk Messaging

```bash
# 1. Find suitable template
GET /message-templates?category=interview
# 2. Send bulk messages
POST /messages/bulk-send
# 3. Check statistics
GET /messages/statistics
```

## 🐛 Error Handling

### Common Error Responses

#### Validation Error (422)

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "name": ["Tên template là bắt buộc."],
    "to_email": ["Email người nhận không hợp lệ."]
  }
}
```

#### Not Found (404)

```json
{
  "message": "Template không tồn tại.",
  "status": "error"
}
```

#### Unauthorized (401)

```json
{
  "message": "Unauthenticated."
}
```

## 📈 Performance & Limits

### Rate Limiting

- **General API calls**: 60 requests per minute per user
- **Bulk operations**: 10 requests per minute per user
- **Template preview**: 30 requests per minute per user

### Pagination

- **Default page size**: 15 items
- **Maximum page size**: 50 items
- **Pagination format**: Laravel standard with `links` and `meta`

## 🔄 Integration Guide

### Frontend Integration

1. **Import Postman collection** for API reference
2. **Use public routes** for initial testing
3. **Implement authentication** flow
4. **Handle Vietnamese content** properly (UTF-8)
5. **Implement error handling** for all scenarios

### Backend Integration

1. **Message Service** handles business logic
2. **Queue support** for scheduled messages
3. **Webhook endpoints** for delivery status updates
4. **Activity logging** for audit trails

## 📝 Development Notes

### Database Schema

- **message_templates**: Template storage with versioning
- **messages**: Message records with status tracking
- **Proper indexing** for performance
- **Foreign key constraints** for data integrity

### Code Structure

- **Controllers**: API endpoint handling
- **Services**: Business logic layer
- **Resources**: Response formatting
- **Requests**: Input validation
- **Models**: Database interactions with relationships

## 🎉 Ready for Production

The Message System is production-ready with:

- ✅ **Complete API documentation**
- ✅ **Comprehensive Postman collection**
- ✅ **Vietnamese content optimization**
- ✅ **Professional recruitment templates**
- ✅ **Robust error handling**
- ✅ **Performance optimization**
- ✅ **Security considerations**
- ✅ **Scalable architecture**

## 📞 Support

For questions or issues:

1. Check the API documentation (`api_message.md`)
2. Test with Postman collection (`api_message.json`)
3. Review error responses and status codes
4. Check authentication and permissions

---

**Happy Coding! 🚀**
