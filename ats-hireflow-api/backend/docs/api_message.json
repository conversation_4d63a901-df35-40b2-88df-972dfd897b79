{"info": {"name": "ATS Message System API", "description": "Comprehensive API collection for the ATS Message System including Message Templates and Messages endpoints with Vietnamese content examples.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "templateId", "value": "1", "type": "string"}, {"key": "messageId", "value": "1", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('authToken', response.data.token);", "        console.log('Auth token saved:', response.data.token);", "    }", "}"]}}]}]}, {"name": "Message Templates", "item": [{"name": "List Message Templates", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/message-templates?page=1&per_page=15&category=interview&is_active=true&include=createdBy,messages", "host": ["{{baseUrl}}"], "path": ["message-templates"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "category", "value": "interview"}, {"key": "is_active", "value": "true"}, {"key": "include", "value": "createdBy,messages"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has data array', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('data');", "    pm.expect(response.data).to.be.an('array');", "});", "", "pm.test('Response has pagination meta', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('meta');", "    pm.expect(response.meta).to.have.property('current_page');", "});"]}}]}, {"name": "Create Message Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Thông báo kết quả phỏng vấn\",\n  \"subject\": \"Kết quả phỏng vấn - Vị trí {{job_title}}\",\n  \"content\": \"<PERSON><PERSON><PERSON> gửi {{candidate_name}},\\n\\nCảm ơn bạn đã tham gia phỏng vấn cho vị trí {{job_title}} tại {{company_name}}.\\n\\n{{#if passed}}\\nChúc mừng! Bạn đã vượt qua vòng phỏng vấn này. Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất để thảo luận về các bước tiếp theo.\\n{{else}}\\nRất tiếc, lần này chúng tôi đã chọn ứng viên khác phù hợp hơn với yêu cầu của vị trí. <PERSON><PERSON>hi<PERSON>, chúng tôi rất ấn tượng với hồ sơ của bạn và hy vọng sẽ có cơ hội hợp tác trong tương lai.\\n{{/if}}\\n\\nTrân trọng,\\n{{recruiter_name}}\\nPhòng Nhân sự - {{company_name}}\",\n  \"variables\": [\"candidate_name\", \"job_title\", \"company_name\", \"passed\", \"recruiter_name\"],\n  \"category\": \"feedback\",\n  \"type\": \"email\",\n  \"language\": \"vi\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{baseUrl}}/message-templates", "host": ["{{baseUrl}}"], "path": ["message-templates"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success status', function () {", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "});", "", "pm.test('Template created with correct data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data.name).to.eql('<PERSON><PERSON><PERSON>ng báo kết quả phỏng vấn');", "    pm.expect(response.data.category).to.eql('feedback');", "});", "", "// Save template ID for other requests", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('templateId', response.data.id);", "}"]}}]}, {"name": "Get Message Template", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/message-templates/{{templateId}}?include=createdBy,messages,childTemplates", "host": ["{{baseUrl}}"], "path": ["message-templates", "{{templateId}}"], "query": [{"key": "include", "value": "createdBy,messages,childTemplates"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Template data is complete', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('name');", "    pm.expect(response.data).to.have.property('content');", "    pm.expect(response.data).to.have.property('variables');", "});"]}}]}, {"name": "Preview Message Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"candidate_name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"job_title\": \"<PERSON><PERSON><PERSON> trình viên <PERSON>HP Senior\",\n    \"company_name\": \"Công ty TNHH ABC\",\n    \"passed\": true,\n    \"recruiter_name\": \"<PERSON><PERSON>ầ<PERSON>\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/message-templates/{{templateId}}/preview", "host": ["{{baseUrl}}"], "path": ["message-templates", "{{templateId}}", "preview"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Preview contains rendered content', function () {", "    const response = pm.response.json();", "    pm.expect(response.data.preview).to.have.property('subject');", "    pm.expect(response.data.preview).to.have.property('content');", "    pm.expect(response.data.preview.content).to.include('<PERSON><PERSON><PERSON><PERSON>');", "});"]}}]}, {"name": "Update Message Template", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Thông báo kết quả phỏng vấn (<PERSON><PERSON><PERSON> nh<PERSON>t)\",\n  \"content\": \"<PERSON><PERSON><PERSON> gửi {{candidate_name}},\\n\\nCảm ơn bạn đã tham gia phỏng vấn cho vị trí {{job_title}} tại {{company_name}}.\\n\\n{{#if passed}}\\n🎉 Chúc mừng! Bạn đã vượt qua vòng phỏng vấn này. Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất để thảo luận về các bước tiếp theo.\\n{{else}}\\nRất tiếc, lần này chúng tôi đã chọn ứng viên khác phù hợp hơn với yêu cầu của vị trí. <PERSON><PERSON> nhiên, chúng tôi rất ấn tượng với hồ sơ của bạn và hy vọng sẽ có cơ hội hợp tác trong tương lai.\\n{{/if}}\\n\\nTrân trọng,\\n{{recruiter_name}}\\nPhòng Nhân sự - {{company_name}}\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{baseUrl}}/message-templates/{{templateId}}", "host": ["{{baseUrl}}"], "path": ["message-templates", "{{templateId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Template updated successfully', function () {", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.name).to.include('<PERSON><PERSON><PERSON> nhật');", "});"]}}]}, {"name": "Duplicate Message Template", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/message-templates/{{templateId}}/duplicate", "host": ["{{baseUrl}}"], "path": ["message-templates", "{{templateId}}", "duplicate"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Template duplicated with Copy suffix', function () {", "    const response = pm.response.json();", "    pm.expect(response.data.name).to.include('(Copy)');", "});"]}}]}, {"name": "Get Template Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/message-templates/categories", "host": ["{{baseUrl}}"], "path": ["message-templates", "categories"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Categories include Vietnamese names', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('interview');", "    pm.expect(response.data.interview).to.eql('Phỏng vấn');", "});"]}}]}, {"name": "Get Template Types", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/message-templates/types", "host": ["{{baseUrl}}"], "path": ["message-templates", "types"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Types include email and SMS', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('email');", "    pm.expect(response.data).to.have.property('sms');", "});"]}}]}]}, {"name": "Messages", "item": [{"name": "List Messages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/messages?page=1&per_page=15&type=email&category=interview&status=sent&include=candidate,template,createdBy", "host": ["{{baseUrl}}"], "path": ["messages"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "type", "value": "email"}, {"key": "category", "value": "interview"}, {"key": "status", "value": "sent"}, {"key": "include", "value": "candidate,template,created<PERSON><PERSON>"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has messages data', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('data');", "    pm.expect(response.data).to.be.an('array');", "});"]}}]}, {"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"email\",\n  \"category\": \"interview\",\n  \"candidate_id\": 1,\n  \"job_posting_id\": 1,\n  \"template_id\": 1,\n  \"to_email\": \"nguy<PERSON><EMAIL>\",\n  \"to_name\": \"<PERSON>uy<PERSON><PERSON>\",\n  \"from_email\": \"<EMAIL>\",\n  \"from_name\": \"Phòng Nhân sự\",\n  \"subject\": \"Lời mời phỏng vấn - Vị trí <PERSON> trình viên PHP Senior\",\n  \"content\": \"<PERSON>ính gửi <PERSON>uyễn <PERSON>ă<PERSON>,\\n\\nChúng tôi rất vui mừng thông báo rằng hồ sơ ứng tuyển của bạn cho vị trí <PERSON>p trình viên PHP Senior tại Công ty TNHH ABC đã được chọn để tham gia vòng phỏng vấn.\\n\\n📅 Thông tin chi tiết:\\n• Thời gian: 25/07/2024 lúc 14:00\\n• Đ<PERSON>a điểm: Tầng 5, <PERSON><PERSON><PERSON> nh<PERSON>, 123 Đường XYZ\\n• Hình thức: Phỏng vấn trực tiếp\\n\\nVui lòng xác nhận tham gia bằng cách phản hồi email này.\\n\\nTrân trọng,\\nTrần Thị Bình\\nPhòng Nhân sự - Công ty TNHH ABC\",\n  \"priority\": 7,\n  \"metadata\": {\n    \"campaign_id\": \"summer_2024\",\n    \"source\": \"manual\"\n  },\n  \"template_data\": {\n    \"candidate_name\": \"Nguyễn Văn An\",\n    \"job_title\": \"Lập trình viên PHP Senior\",\n    \"company_name\": \"Công ty TNHH ABC\",\n    \"interview_date\": \"25/07/2024\",\n    \"interview_time\": \"14:00\",\n    \"interview_location\": \"Tầng 5, Tòa nhà ABC, 123 Đường XYZ\",\n    \"recruiter_name\": \"Trần Thị Bình\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/messages", "host": ["{{baseUrl}}"], "path": ["messages"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Message sent successfully', function () {", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data).to.have.property('id');", "});", "", "// Save message ID for other requests", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('messageId', response.data.id);", "}"]}}]}, {"name": "Get Message", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/messages/{{messageId}}?include=candidate,template,createdBy,replies", "host": ["{{baseUrl}}"], "path": ["messages", "{{messageId}}"], "query": [{"key": "include", "value": "candidate,template,created<PERSON><PERSON>,replies"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Message data is complete', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('subject');", "    pm.expect(response.data).to.have.property('content');", "});"]}}]}, {"name": "Update Message Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"delivered\",\n  \"external_id\": \"msg_ext_12345\",\n  \"metadata\": {\n    \"delivery_location\": \"inbox\",\n    \"provider\": \"sendgrid\",\n    \"delivery_time\": 2.5\n  }\n}"}, "url": {"raw": "{{baseUrl}}/messages/{{messageId}}", "host": ["{{baseUrl}}"], "path": ["messages", "{{messageId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Status updated successfully', function () {", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.status).to.eql('delivered');", "});"]}}]}, {"name": "Send Bulk Messages", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"template_id\": 1,\n  \"candidate_ids\": [1, 2, 3, 4],\n  \"job_posting_id\": 1,\n  \"scheduled_at\": \"2024-07-22 09:00:00\",\n  \"priority\": 8\n}"}, "url": {"raw": "{{baseUrl}}/messages/bulk-send", "host": ["{{baseUrl}}"], "path": ["messages", "bulk-send"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Bulk send results provided', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('total_sent');", "    pm.expect(response.data).to.have.property('total_failed');", "    pm.expect(response.data).to.have.property('success');", "    pm.expect(response.data).to.have.property('failed');", "});"]}}]}, {"name": "Get Message Thread", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/messages/{{messageId}}/thread", "host": ["{{baseUrl}}"], "path": ["messages", "{{messageId}}", "thread"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Thread data is provided', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('thread_id');", "    pm.expect(response.data).to.have.property('messages');", "    pm.expect(response.data.messages).to.be.an('array');", "});"]}}]}, {"name": "Reply to Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"email\",\n  \"category\": \"interview\",\n  \"to_email\": \"ng<PERSON><PERSON><EMAIL>\",\n  \"to_name\": \"Nguyễn <PERSON>\",\n  \"subject\": \"Re: Lời mời phỏng vấn - Thông tin bổ sung\",\n  \"content\": \"<PERSON><PERSON><PERSON> gửi <PERSON>uyễ<PERSON>,\\n\\nCảm ơn bạn đã xác nhận tham gia phỏng vấn.\\n\\nMột số thông tin bổ sung:\\n• Vui lòng mang theo laptop cá nhân để demo dự án\\n• Thời gian phỏng vấn dự kiến: 90 phút\\n• Địa điểm có bãi đỗ xe miễn phí\\n\\nNếu có bất kỳ thắc mắc nào, vui lòng liên hệ với chúng tôi.\\n\\nTrân trọng,\\nTrần Thị Bình\\nPhòng Nhân sự\",\n  \"priority\": 6\n}"}, "url": {"raw": "{{baseUrl}}/messages/{{messageId}}/reply", "host": ["{{baseUrl}}"], "path": ["messages", "{{messageId}}", "reply"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Reply sent successfully', function () {", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data).to.have.property('parent_message_id');", "    pm.expect(response.data).to.have.property('thread_id');", "});"]}}]}, {"name": "Get Message Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/messages/statistics?date_from=2024-07-01&date_to=2024-07-31", "host": ["{{baseUrl}}"], "path": ["messages", "statistics"], "query": [{"key": "date_from", "value": "2024-07-01"}, {"key": "date_to", "value": "2024-07-31"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Statistics data is complete', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('total_messages');", "    pm.expect(response.data).to.have.property('by_status');", "    pm.expect(response.data).to.have.property('by_type');", "    pm.expect(response.data).to.have.property('success_rate');", "});"]}}]}]}]}