<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Candidate;
use App\Models\JobPosting;
use App\Models\CandidateProfileAnalysis;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class CandidateProfileAnalysisTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user and authenticate
        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    public function test_can_extract_resume_information(): void
    {
        // Create a candidate with resume URL
        $candidate = Candidate::factory()->create([
            'resume_url' => 'https://example.com/resume.pdf'
        ]);

        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume', [
            'candidate_id' => $candidate->id
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'candidate_id',
                        'analysis_type',
                        'status',
                        'extracted_information' => [
                            'name',
                            'email',
                            'phone',
                            'address',
                            'skills',
                            'experience',
                            'education',
                        ]
                    ]
                ]);

        // Verify analysis was created in database
        $this->assertDatabaseHas('candidate_profile_analyses', [
            'candidate_id' => $candidate->id,
            'analysis_type' => 'resume_extraction',
            'status' => 'completed'
        ]);
    }

    public function test_can_generate_ai_analysis(): void
    {
        $candidate = Candidate::factory()->create();

        $response = $this->postJson('/api/v1/candidate-analysis/generate-analysis', [
            'candidate_id' => $candidate->id
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'candidate_id',
                        'analysis_type',
                        'status',
                        'ai_analysis' => [
                            'summary',
                            'strengths',
                            'weaknesses',
                            'improvement_areas',
                            'recommendations',
                        ],
                        'scores' => [
                            'overall',
                            'skills',
                            'experience',
                            'education',
                            'cultural_fit',
                        ]
                    ]
                ]);

        // Verify analysis was created in database
        $this->assertDatabaseHas('candidate_profile_analyses', [
            'candidate_id' => $candidate->id,
            'analysis_type' => 'ai_analysis',
            'status' => 'completed'
        ]);
    }

    public function test_can_generate_job_matching_analysis(): void
    {
        $candidate = Candidate::factory()->create();
        $jobPosting = JobPosting::factory()->create();

        $response = $this->postJson('/api/v1/candidate-analysis/generate-analysis', [
            'candidate_id' => $candidate->id,
            'job_posting_id' => $jobPosting->id
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'candidate_id',
                        'job_posting_id',
                        'analysis_type',
                        'status',
                        'job_matching' => [
                            'match_details',
                            'missing_requirements',
                            'matching_criteria',
                        ]
                    ]
                ]);

        // Verify analysis was created in database
        $this->assertDatabaseHas('candidate_profile_analyses', [
            'candidate_id' => $candidate->id,
            'job_posting_id' => $jobPosting->id,
            'analysis_type' => 'job_matching',
            'status' => 'completed'
        ]);
    }

    public function test_can_get_candidate_summary(): void
    {
        $candidate = Candidate::factory()->create();
        
        // Create some analyses for the candidate
        CandidateProfileAnalysis::factory()->create([
            'candidate_id' => $candidate->id,
            'analysis_type' => 'ai_analysis',
            'status' => 'completed',
            'overall_score' => 85,
        ]);

        $response = $this->getJson("/api/v1/candidate-analysis/candidate/{$candidate->id}/summary");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'candidate',
                        'extracted_information',
                        'aggregate_scores',
                        'recent_analyses',
                    ]
                ]);
    }

    public function test_can_update_candidate_from_extraction(): void
    {
        $candidate = Candidate::factory()->create([
            'name' => 'Old Name',
            'email' => '<EMAIL>'
        ]);
        
        $analysis = CandidateProfileAnalysis::factory()->create([
            'candidate_id' => $candidate->id,
            'analysis_type' => 'resume_extraction',
            'status' => 'completed',
            'extracted_name' => 'New Name',
            'extracted_email' => '<EMAIL>',
        ]);

        $response = $this->postJson('/api/v1/candidate-analysis/candidate/update-from-extraction', [
            'candidate_id' => $candidate->id,
            'analysis_id' => $analysis->id,
            'fields' => ['name', 'email']
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'updated_fields',
                        'candidate'
                    ]
                ]);

        // Verify candidate was updated
        $candidate->refresh();
        $this->assertEquals('New Name', $candidate->name);
        $this->assertEquals('<EMAIL>', $candidate->email);
    }

    public function test_requires_authentication(): void
    {
        // Remove authentication
        auth()->logout();

        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume', [
            'candidate_id' => 1
        ]);

        $response->assertStatus(401);
    }

    public function test_validates_required_fields(): void
    {
        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['candidate_id']);
    }
}
