<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileUploadService
{
    /**
     * Upload and process resume file.
     */
    public function uploadResume(UploadedFile $file, string $candidateId): array
    {
        $this->validateResumeFile($file);

        $filename = $this->generateUniqueFilename($file, 'resume', $candidateId);
        $path = "resumes/{$candidateId}/{$filename}";

        // Store the file
        $storedPath = Storage::disk('public')->putFileAs(
            "resumes/{$candidateId}",
            $file,
            $filename
        );

        // Extract text content for parsing
        $textContent = $this->extractTextFromFile($file);

        return [
            'path' => $storedPath,
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'text_content' => $textContent,
            'url' => Storage::disk('public')->url($storedPath),
        ];
    }

    /**
     * Upload and process avatar image.
     */
    public function uploadAvatar(UploadedFile $file, string $userId): array
    {
        $this->validateImageFile($file);

        $filename = $this->generateUniqueFilename($file, 'avatar', $userId);
        $path = "avatars/{$userId}/{$filename}";

        // Process and resize image
        $image = Image::make($file);
        $image->fit(300, 300); // Resize to 300x300

        // Store the processed image
        $storedPath = Storage::disk('public')->put($path, $image->encode());

        // Create thumbnail
        $thumbnailImage = Image::make($file);
        $thumbnailImage->fit(100, 100);
        $thumbnailPath = "avatars/{$userId}/thumb_{$filename}";
        Storage::disk('public')->put($thumbnailPath, $thumbnailImage->encode());

        return [
            'path' => $path,
            'thumbnail_path' => $thumbnailPath,
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'url' => Storage::disk('public')->url($path),
            'thumbnail_url' => Storage::disk('public')->url($thumbnailPath),
        ];
    }

    /**
     * Upload general document.
     */
    public function uploadDocument(UploadedFile $file, string $type, string $entityId): array
    {
        $this->validateDocumentFile($file);

        $filename = $this->generateUniqueFilename($file, $type, $entityId);
        $path = "documents/{$type}/{$entityId}/{$filename}";

        // Store the file
        $storedPath = Storage::disk('public')->putFileAs(
            "documents/{$type}/{$entityId}",
            $file,
            $filename
        );

        return [
            'path' => $storedPath,
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'url' => Storage::disk('public')->url($storedPath),
        ];
    }

    /**
     * Process bulk file upload.
     */
    public function uploadBulkFiles(array $files, string $type, string $entityId): array
    {
        $results = [];

        foreach ($files as $index => $file) {
            try {
                $result = $this->uploadDocument($file, $type, $entityId);
                $result['index'] = $index;
                $result['status'] = 'success';
                $results[] = $result;
            } catch (\Exception $e) {
                $results[] = [
                    'index' => $index,
                    'status' => 'error',
                    'error' => $e->getMessage(),
                    'original_name' => $file->getClientOriginalName(),
                ];
            }
        }

        return $results;
    }

    /**
     * Delete file and its versions.
     */
    public function deleteFile(string $path): bool
    {
        try {
            // Delete main file
            Storage::disk('public')->delete($path);

            // Delete thumbnail if exists
            $pathInfo = pathinfo($path);
            $thumbnailPath = $pathInfo['dirname'] . '/thumb_' . $pathInfo['basename'];
            if (Storage::disk('public')->exists($thumbnailPath)) {
                Storage::disk('public')->delete($thumbnailPath);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get file information.
     */
    public function getFileInfo(string $path): ?array
    {
        if (!Storage::disk('public')->exists($path)) {
            return null;
        }

        return [
            'path' => $path,
            'size' => Storage::disk('public')->size($path),
            'last_modified' => Storage::disk('public')->lastModified($path),
            'url' => Storage::disk('public')->url($path),
            'exists' => true,
        ];
    }

    /**
     * Generate secure download URL with expiration.
     */
    public function generateSecureUrl(string $path, int $expirationMinutes = 60): string
    {
        // For local storage, return regular URL
        // For S3, this would generate a signed URL
        return Storage::disk('public')->url($path) . '?expires=' . (time() + ($expirationMinutes * 60));
    }

    /**
     * Validate resume file.
     */
    private function validateResumeFile(UploadedFile $file): void
    {
        $allowedMimes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        $maxSize = 10 * 1024 * 1024; // 10MB

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \InvalidArgumentException('Invalid file type. Only PDF, DOC, and DOCX files are allowed.');
        }

        if ($file->getSize() > $maxSize) {
            throw new \InvalidArgumentException('File size exceeds 10MB limit.');
        }

        // Basic virus scan simulation
        $this->performVirusScan($file);
    }

    /**
     * Validate image file.
     */
    private function validateImageFile(UploadedFile $file): void
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 5 * 1024 * 1024; // 5MB

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \InvalidArgumentException('Invalid image type. Only JPEG, PNG, GIF, and WebP images are allowed.');
        }

        if ($file->getSize() > $maxSize) {
            throw new \InvalidArgumentException('Image size exceeds 5MB limit.');
        }
    }

    /**
     * Validate document file.
     */
    private function validateDocumentFile(UploadedFile $file): void
    {
        $allowedMimes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'image/jpeg',
            'image/png',
        ];
        $maxSize = 20 * 1024 * 1024; // 20MB

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \InvalidArgumentException('Invalid file type.');
        }

        if ($file->getSize() > $maxSize) {
            throw new \InvalidArgumentException('File size exceeds 20MB limit.');
        }

        $this->performVirusScan($file);
    }

    /**
     * Generate unique filename.
     */
    private function generateUniqueFilename(UploadedFile $file, string $type, string $entityId): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('YmdHis');
        $random = Str::random(8);

        return "{$type}_{$entityId}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Extract text content from file for parsing.
     */
    private function extractTextFromFile(UploadedFile $file): ?string
    {
        // This is a simplified implementation
        // In production, you would use libraries like:
        // - Smalot\PdfParser for PDF files
        // - PhpOffice\PhpWord for Word documents

        $mimeType = $file->getMimeType();

        switch ($mimeType) {
            case 'text/plain':
                return file_get_contents($file->getPathname());

            case 'application/pdf':
                // Would use PDF parser library
                return 'PDF content extraction would be implemented here';

            case 'application/msword':
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                // Would use Word document parser library
                return 'Word document content extraction would be implemented here';

            default:
                return null;
        }
    }

    /**
     * Perform basic virus scan simulation.
     */
    private function performVirusScan(UploadedFile $file): void
    {
        // In production, integrate with antivirus service like ClamAV
        // For now, just check file size and basic patterns

        $content = file_get_contents($file->getPathname());
        $suspiciousPatterns = ['<script', 'javascript:', 'vbscript:', 'onload='];

        foreach ($suspiciousPatterns as $pattern) {
            if (stripos($content, $pattern) !== false) {
                throw new \InvalidArgumentException('File contains suspicious content and cannot be uploaded.');
            }
        }
    }
}
