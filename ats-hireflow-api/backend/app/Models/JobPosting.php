<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;

class JobPosting extends Model
{
    /** @use HasFactory<\Database\Factories\JobPostingFactory> */
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'title',
        'department',
        'location',
        'type',
        'work_location',
        'salary_min',
        'salary_max',
        'currency',
        'description',
        'education_required',
        'company_culture',
        'requirements',
        'benefits',
        'responsibilities',
        'skills',
        'status',
        'priority',
        'posted_date',
        'closing_date',
        'hiring_manager_id',
        'recruiter_id',
        'experience_level',
        'positions',
        'applicant_count',
        'view_count',
        'created_by',
    ];

    protected $casts = [
        'posted_date' => 'date',
        'closing_date' => 'date',
        'salary_min' => 'decimal:2',
        'salary_max' => 'decimal:2',
        'applicant_count' => 'integer',
        'view_count' => 'integer',
        'positions' => 'integer',
        'requirements' => 'array',
        'benefits' => 'array',
        'responsibilities' => 'array',
        'skills' => 'array',
    ];

    protected static $logAttributes = ['title', 'status', 'priority'];
    protected static $logOnlyDirty = true;

    public function getActivitylogOptions(): \Spatie\Activitylog\LogOptions
    {
        return \Spatie\Activitylog\LogOptions::defaults()
            ->logOnly(['title', 'status', 'priority'])
            ->logOnlyDirty();
    }

    // Relationships
    public function hiringManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'hiring_manager_id');
    }

    public function recruiter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recruiter_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function candidates(): HasMany
    {
        return $this->hasMany(Candidate::class);
    }

    public function interviews(): HasMany
    {
        return $this->hasMany(Interview::class);
    }

    // Legacy relationships removed - replaced by JSON fields:
    // - requirements (JSON field)
    // - benefits (JSON field)
    // - responsibilities (JSON field)
    // - skills (JSON field)

    // New helper methods for simplified requirements and benefits
    public function getRequirementsListAttribute(): array
    {
        return $this->requirements ?? [];
    }

    public function getBenefitsListAttribute(): array
    {
        return $this->benefits ?? [];
    }

    public function addRequirement(string $requirement): void
    {
        $requirements = $this->requirements ?? [];
        if (!in_array($requirement, $requirements)) {
            $requirements[] = $requirement;
            $this->requirements = $requirements;
            $this->save();
        }
    }

    public function removeRequirement(string $requirement): void
    {
        $requirements = $this->requirements ?? [];
        $requirements = array_values(array_filter($requirements, fn($r) => $r !== $requirement));
        $this->requirements = $requirements;
        $this->save();
    }

    public function addBenefit(string $benefit): void
    {
        $benefits = $this->benefits ?? [];
        if (!in_array($benefit, $benefits)) {
            $benefits[] = $benefit;
            $this->benefits = $benefits;
            $this->save();
        }
    }

    public function removeBenefit(string $benefit): void
    {
        $benefits = $this->benefits ?? [];
        $benefits = array_values(array_filter($benefits, fn($b) => $b !== $benefit));
        $this->benefits = $benefits;
        $this->save();
    }

    public function hasRequirement(string $requirement): bool
    {
        return in_array($requirement, $this->requirements ?? []);
    }

    public function hasBenefit(string $benefit): bool
    {
        return in_array($benefit, $this->benefits ?? []);
    }

    // Helper methods for responsibilities
    public function getResponsibilitiesListAttribute(): array
    {
        return $this->responsibilities ?? [];
    }

    public function addResponsibility(string $responsibility): void
    {
        $responsibilities = $this->responsibilities ?? [];
        if (!in_array($responsibility, $responsibilities)) {
            $responsibilities[] = $responsibility;
            $this->responsibilities = $responsibilities;
            $this->save();
        }
    }

    public function removeResponsibility(string $responsibility): void
    {
        $responsibilities = $this->responsibilities ?? [];
        $responsibilities = array_values(array_filter($responsibilities, fn($r) => $r !== $responsibility));
        $this->responsibilities = $responsibilities;
        $this->save();
    }

    public function hasResponsibility(string $responsibility): bool
    {
        return in_array($responsibility, $this->responsibilities ?? []);
    }

    // Helper methods for skills
    public function getSkillsListAttribute(): array
    {
        return $this->skills ?? [];
    }

    public function addSkill(string $skill): void
    {
        $skills = $this->skills ?? [];
        if (!in_array($skill, $skills)) {
            $skills[] = $skill;
            $this->skills = $skills;
            $this->save();
        }
    }

    public function removeSkill(string $skill): void
    {
        $skills = $this->skills ?? [];
        $skills = array_values(array_filter($skills, fn($s) => $s !== $skill));
        $this->skills = $skills;
        $this->save();
    }

    public function hasSkill(string $skill): bool
    {
        return in_array($skill, $this->skills ?? []);
    }

    public function skills(): HasMany
    {
        return $this->hasMany(JobSkill::class);
    }

    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByDepartment($query, $department)
    {
        return $query->where('department', $department);
    }

    public function scopeByExperienceLevel($query, $level)
    {
        return $query->where('experience_level', $level);
    }

    // Accessors & Mutators
    public function getInitialsAttribute(): string
    {
        $words = explode(' ', $this->title);
        $initials = '';
        foreach ($words as $word) {
            $initials .= strtoupper(mb_substr($word, 0, 1));
        }
        return mb_substr($initials, 0, 2);
    }

    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    public function getSalaryRangeAttribute(): string
    {
        if ($this->salary_min && $this->salary_max) {
            return number_format($this->salary_min) . ' - ' . number_format($this->salary_max) . ' ' . $this->currency;
        }
        return 'Negotiable';
    }
}
