<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Message extends Model
{
    /** @use HasFactory<\Database\Factories\MessageFactory> */
    use HasFactory, LogsActivity;

    // Message types
    const TYPE_EMAIL = 'email';
    const TYPE_SMS = 'sms';
    const TYPE_NOTE = 'note';

    // Message categories
    const CATEGORY_INTERVIEW = 'interview';
    const CATEGORY_OFFER = 'offer';
    const CATEGORY_FEEDBACK = 'feedback';
    const CATEGORY_REMINDER = 'reminder';
    const CATEGORY_REJECTION = 'rejection';
    const CATEGORY_GENERAL = 'general';

    // Message statuses
    const STATUS_DRAFT = 'draft';
    const STATUS_QUEUED = 'queued';
    const STATUS_SENT = 'sent';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_READ = 'read';
    const STATUS_FAILED = 'failed';

    protected $fillable = [
        'type',
        'category',
        'candidate_id',
        'job_posting_id',
        'template_id',
        'parent_message_id',
        'thread_id',
        'to_email',
        'to_phone',
        'to_name',
        'from_email',
        'from_name',
        'subject',
        'content',
        'status',
        'priority',
        'scheduled_at',
        'sent_at',
        'delivered_at',
        'read_at',
        'error_message',
        'metadata',
        'external_id',
        'created_by',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
        'metadata' => 'array',
        'priority' => 'integer',
    ];

    protected static $logAttributes = [
        'status',
        'type',
        'category',
        'subject'
    ];
    protected static $logOnlyDirty = true;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'type', 'category', 'subject'])
            ->logOnlyDirty();
    }

    // Relationships
    public function candidate(): BelongsTo
    {
        return $this->belongsTo(Candidate::class);
    }

    public function jobPosting(): BelongsTo
    {
        return $this->belongsTo(JobPosting::class);
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(MessageTemplate::class, 'template_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function parentMessage(): BelongsTo
    {
        return $this->belongsTo(Message::class, 'parent_message_id');
    }

    public function replies(): HasMany
    {
        return $this->hasMany(Message::class, 'parent_message_id');
    }

    public function threadMessages(): HasMany
    {
        return $this->hasMany(Message::class, 'thread_id');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeSent($query)
    {
        return $query->whereIn('status', ['sent', 'delivered', 'read']);
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeScheduled($query)
    {
        return $query->where('status', 'queued')
            ->whereNotNull('scheduled_at')
            ->where('scheduled_at', '>', now());
    }

    public function scopeDue($query)
    {
        return $query->where('status', 'queued')
            ->where(function ($q) {
                $q->whereNull('scheduled_at')
                    ->orWhere('scheduled_at', '<=', now());
            });
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeHighPriority($query)
    {
        return $query->where('priority', '>=', 8);
    }

    // Computed attributes
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'draft' => 'bg-gray-500',
            'queued' => 'bg-yellow-500',
            'sent' => 'bg-blue-500',
            'delivered' => 'bg-green-500',
            'read' => 'bg-indigo-500',
            'failed' => 'bg-red-500',
            default => 'bg-gray-500'
        };
    }

    public function getIsScheduledAttribute(): bool
    {
        return $this->status === 'queued' && $this->scheduled_at && $this->scheduled_at->isFuture();
    }

    public function getIsDueAttribute(): bool
    {
        return $this->status === 'queued' &&
            (!$this->scheduled_at || $this->scheduled_at->isPast());
    }

    public function getDeliveryTimeAttribute(): ?int
    {
        if ($this->sent_at && $this->delivered_at) {
            return $this->sent_at->diffInSeconds($this->delivered_at);
        }
        return null;
    }

    public function getReadTimeAttribute(): ?int
    {
        if ($this->delivered_at && $this->read_at) {
            return $this->delivered_at->diffInSeconds($this->read_at);
        }
        return null;
    }

    // Status management methods
    public function markAsQueued(): void
    {
        $this->update(['status' => self::STATUS_QUEUED]);
    }

    public function markAsSent(): void
    {
        $this->update([
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    public function markAsDelivered(): void
    {
        $this->update([
            'status' => self::STATUS_DELIVERED,
            'delivered_at' => now(),
        ]);
    }

    public function markAsRead(): void
    {
        $this->update([
            'status' => self::STATUS_READ,
            'read_at' => now(),
        ]);
    }

    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
        ]);
    }

    public function schedule(\DateTime $scheduledAt): void
    {
        $this->update([
            'status' => self::STATUS_QUEUED,
            'scheduled_at' => $scheduledAt,
        ]);
    }

    // Thread management
    public function createReply(array $data): self
    {
        $replyData = array_merge($data, [
            'parent_message_id' => $this->id,
            'thread_id' => $this->thread_id ?: $this->id,
            'candidate_id' => $this->candidate_id,
            'job_posting_id' => $this->job_posting_id,
        ]);

        return self::create($replyData);
    }

    public function getThreadRoot(): self
    {
        if ($this->parent_message_id) {
            return $this->parentMessage->getThreadRoot();
        }
        return $this;
    }

    public function getThreadMessages()
    {
        $threadId = $this->thread_id ?: $this->id;
        return self::where('thread_id', $threadId)
            ->orWhere('id', $threadId)
            ->orderBy('created_at')
            ->get();
    }

    // Static helper methods
    public static function getStatuses(): array
    {
        return [
            self::STATUS_DRAFT => 'Bản nháp',
            self::STATUS_QUEUED => 'Đang chờ',
            self::STATUS_SENT => 'Đã gửi',
            self::STATUS_DELIVERED => 'Đã nhận',
            self::STATUS_READ => 'Đã đọc',
            self::STATUS_FAILED => 'Thất bại',
        ];
    }

    public static function getTypes(): array
    {
        return [
            self::TYPE_EMAIL => 'Email',
            self::TYPE_SMS => 'SMS',
            self::TYPE_NOTE => 'Ghi chú',
        ];
    }

    public static function getCategories(): array
    {
        return [
            self::CATEGORY_INTERVIEW => 'Phỏng vấn',
            self::CATEGORY_OFFER => 'Đề nghị công việc',
            self::CATEGORY_FEEDBACK => 'Phản hồi',
            self::CATEGORY_REMINDER => 'Nhắc nhở',
            self::CATEGORY_REJECTION => 'Từ chối',
            self::CATEGORY_GENERAL => 'Chung',
        ];
    }

    public function getStatusNameAttribute(): string
    {
        return self::getStatuses()[$this->status] ?? $this->status;
    }

    public function getTypeNameAttribute(): string
    {
        return self::getTypes()[$this->type] ?? $this->type;
    }

    public function getCategoryNameAttribute(): string
    {
        return self::getCategories()[$this->category] ?? $this->category;
    }
}
