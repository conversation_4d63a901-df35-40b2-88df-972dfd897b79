<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;

class Candidate extends Model
{
    /** @use HasFactory<\Database\Factories\CandidateFactory> */
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'position',
        'experience',
        'status',
        'applied_date',
        'source',
        'location',
        'salary_expectation_min',
        'salary_expectation_max',
        'salary_currency',
        'rating',
        'ai_score',
        'linkedin_url',
        'github_url',
        'portfolio_url',
        'avatar',
        'resume_url',
        'notes',
        'skills',
        'tags',
        'education',
        'work_history',
        'job_posting_id',
        'created_by',
        'assigned_to',
    ];

    protected $casts = [
        'applied_date' => 'date',
        'salary_expectation_min' => 'decimal:2',
        'salary_expectation_max' => 'decimal:2',
        'rating' => 'decimal:2',
        'ai_score' => 'integer',
        'skills' => 'array',
        'tags' => 'array',
    ];

    protected static $logAttributes = ['name', 'status', 'rating', 'ai_score'];
    protected static $logOnlyDirty = true;

    public function getActivitylogOptions(): \Spatie\Activitylog\LogOptions
    {
        return \Spatie\Activitylog\LogOptions::defaults()
            ->logOnly(['name', 'status', 'rating', 'ai_score'])
            ->logOnlyDirty();
    }

    // Relationships
    public function jobPosting(): BelongsTo
    {
        return $this->belongsTo(JobPosting::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function interviews(): HasMany
    {
        return $this->hasMany(Interview::class);
    }

    // Legacy relationships removed - replaced by JSON/TEXT fields:
    // - skills (JSON field)
    // - tags (JSON field)
    // - education (TEXT field)
    // - work_history (TEXT field)

    // New helper methods for simplified skills and tags
    public function getSkillsListAttribute(): array
    {
        return $this->skills ?? [];
    }

    public function getTagsListAttribute(): array
    {
        return $this->tags ?? [];
    }

    public function addSkill(string $skill): void
    {
        $skills = $this->skills ?? [];
        if (!in_array($skill, $skills)) {
            $skills[] = $skill;
            $this->skills = $skills;
            $this->save();
        }
    }

    public function removeSkill(string $skill): void
    {
        $skills = $this->skills ?? [];
        $skills = array_values(array_filter($skills, fn($s) => $s !== $skill));
        $this->skills = $skills;
        $this->save();
    }

    public function addTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->tags = $tags;
            $this->save();
        }
    }

    public function removeTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        $tags = array_values(array_filter($tags, fn($t) => $t !== $tag));
        $this->tags = $tags;
        $this->save();
    }

    public function hasSkill(string $skill): bool
    {
        return in_array($skill, $this->skills ?? []);
    }

    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags ?? []);
    }

    public function statusHistory(): HasMany
    {
        return $this->hasMany(CandidateStatusHistory::class)->orderBy('created_at', 'desc');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByJobPosting($query, $jobPostingId)
    {
        return $query->where('job_posting_id', $jobPostingId);
    }

    public function scopeBySource($query, $source)
    {
        return $query->where('source', $source);
    }

    public function scopeHighRated($query, $minRating = 4.0)
    {
        return $query->where('rating', '>=', $minRating);
    }

    public function scopeHighAiScore($query, $minScore = 80)
    {
        return $query->where('ai_score', '>=', $minScore);
    }

    // Accessors & Mutators
    public function getInitialsAttribute(): string
    {

        // return "XX";
        $names = explode(' ', $this->name);
        $initials = '';
        foreach ($names as $name) {
            $initials .= strtoupper(mb_substr($name, 0, 1));
        }
        return mb_substr($initials, 0, 2);
    }

    public function getSalaryExpectationRangeAttribute(): string
    {
        if ($this->salary_expectation_min && $this->salary_expectation_max) {
            return number_format($this->salary_expectation_min) . ' - ' .
                number_format($this->salary_expectation_max) . ' ' . $this->salary_currency;
        }
        return 'Not specified';
    }

    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'sourced' => 'bg-blue-500',
            'applied' => 'bg-primary',
            'screening' => 'bg-warning',
            'interview' => 'bg-purple-500',
            'offer' => 'bg-orange-500',
            'hired' => 'bg-success',
            'rejected' => 'bg-danger',
            default => 'bg-gray-500'
        };
    }

    // Helper methods
    public function updateStatus(string $newStatus, User $changedBy, ?string $notes = null): void
    {
        $oldStatus = $this->status;

        $this->update(['status' => $newStatus]);

        $this->statusHistory()->create([
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'changed_by' => $changedBy->id,
            'notes' => $notes,
        ]);
    }

    // Helper methods for simplified education and work_history
    public function getEducationSummary(): string
    {
        return $this->education ?? 'Chưa cập nhật thông tin học vấn';
    }

    public function getWorkHistorySummary(): string
    {
        return $this->work_history ?? 'Chưa cập nhật kinh nghiệm làm việc';
    }

    public function hasEducation(): bool
    {
        return !empty($this->education);
    }

    public function hasWorkHistory(): bool
    {
        return !empty($this->work_history);
    }

    public function updateEducation(string $education): void
    {
        $this->update(['education' => $education]);
    }

    public function updateWorkHistory(string $workHistory): void
    {
        $this->update(['work_history' => $workHistory]);
    }
}
