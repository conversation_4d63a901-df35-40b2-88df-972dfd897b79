<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'department',
        'title',
        'phone',
        'avatar',
        'is_active',
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    // Relationships
    public function createdJobPostings(): HasMany
    {
        return $this->hasMany(JobPosting::class, 'created_by');
    }

    public function managedJobPostings(): HasMany
    {
        return $this->hasMany(JobPosting::class, 'hiring_manager_id');
    }

    public function recruitedJobPostings(): HasMany
    {
        return $this->hasMany(JobPosting::class, 'recruiter_id');
    }

    public function createdCandidates(): HasMany
    {
        return $this->hasMany(Candidate::class, 'created_by');
    }

    public function assignedCandidates(): HasMany
    {
        return $this->hasMany(Candidate::class, 'assigned_to');
    }

    public function createdInterviews(): HasMany
    {
        return $this->hasMany(Interview::class, 'created_by');
    }

    public function interviewer(): HasOne
    {
        return $this->hasOne(Interviewer::class);
    }

    public function createdMessages(): HasMany
    {
        return $this->hasMany(Message::class, 'created_by');
    }

    public function createdMessageTemplates(): HasMany
    {
        return $this->hasMany(MessageTemplate::class, 'created_by');
    }

    public function candidateStatusChanges(): HasMany
    {
        return $this->hasMany(CandidateStatusHistory::class, 'changed_by');
    }

    public function createdCandidateTags(): HasMany
    {
        return $this->hasMany(CandidateTag::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }

    public function scopeByDepartment($query, $department)
    {
        return $query->where('department', $department);
    }

    // Accessors & Mutators
    public function getInitialsAttribute(): string
    {
        $names = explode(' ', $this->name);
        $initials = '';
        foreach ($names as $name) {
            $initials .= strtoupper(mb_substr($name, 0, 1));
        }
        return mb_substr($initials, 0, 2);
    }

    public function getIsInterviewerAttribute(): bool
    {
        return $this->interviewer()->exists();
    }

    // Helper methods
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    public function canManageJobPosting(JobPosting $jobPosting): bool
    {
        return $this->hasRole('admin') ||
            $this->id === $jobPosting->hiring_manager_id ||
            $this->id === $jobPosting->recruiter_id ||
            $this->id === $jobPosting->created_by;
    }

    public function canManageCandidate(Candidate $candidate): bool
    {
        return $this->hasRole('admin') ||
            $this->id === $candidate->created_by ||
            $this->id === $candidate->assigned_to ||
            $this->canManageJobPosting($candidate->jobPosting);
    }

    public function canManageInterview($interview): bool
    {
        return $this->hasRole('admin') ||
            $this->id === $interview->created_by ||
            $this->interviewer && $this->interviewer->id === $interview->interviewer_id ||
            $this->canManageCandidate($interview->candidate);
    }
}
