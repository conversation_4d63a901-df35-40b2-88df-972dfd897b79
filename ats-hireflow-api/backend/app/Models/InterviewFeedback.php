<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InterviewFeedback extends Model
{
    protected $fillable = [
        'interview_id',
        'interviewer_id',
        'rating',
        'comments',
        'recommend',
        'strengths',
        'concerns',
        'next_round_recommendation',
        'technical_score',
        'communication_score',
        'cultural_fit_score',
    ];

    protected $casts = [
        'rating' => 'decimal:2',
        'recommend' => 'boolean',
        'strengths' => 'array',
        'concerns' => 'array',
        'technical_score' => 'integer',
        'communication_score' => 'integer',
        'cultural_fit_score' => 'integer',
    ];

    public function interview(): BelongsTo
    {
        return $this->belongsTo(Interview::class);
    }

    public function interviewer(): BelongsTo
    {
        return $this->belongsTo(Interviewer::class);
    }

    public function getOverallScoreAttribute(): float
    {
        $scores = array_filter([
            $this->technical_score,
            $this->communication_score,
            $this->cultural_fit_score
        ]);

        return count($scores) > 0 ? array_sum($scores) / count($scores) : 0;
    }
}
