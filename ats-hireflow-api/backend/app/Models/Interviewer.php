<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Interviewer extends Model
{
    /** @use HasFactory<\Database\Factories\InterviewerFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'department',
        'expertise',
        'is_active',
        'location',
        'max_interviews_per_day',
        'availability',
        'time_slots',
        'timezone',
    ];

    protected $casts = [
        'expertise' => 'array',
        'is_active' => 'boolean',
        'max_interviews_per_day' => 'integer',
        'availability' => 'array',
        'time_slots' => 'array',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function interviews(): HasMany
    {
        return $this->hasMany(Interview::class);
    }

    public function feedback(): Has<PERSON>any
    {
        return $this->hasMany(InterviewFeedback::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByDepartment($query, $department)
    {
        return $query->where('department', $department);
    }

    public function scopeAvailableOn($query, $date)
    {
        return $query->where('is_active', true)
                    ->whereHas('interviews', function($q) use ($date) {
                        $q->whereDate('date', $date)
                          ->where('status', 'scheduled');
                    }, '<', function($interviewer) {
                        return $interviewer->max_interviews_per_day;
                    });
    }

    // Accessors & Mutators
    public function getNameAttribute(): string
    {
        return $this->user->name;
    }

    public function getEmailAttribute(): string
    {
        return $this->user->email;
    }

    public function getExpertiseListAttribute(): string
    {
        return is_array($this->expertise) ? implode(', ', $this->expertise) : '';
    }

    // Helper methods
    public function isAvailableOn($date, $time = null): bool
    {

        return true;
        if (!$this->is_active) {
            return false;
        }

        // Check daily interview limit
        $interviewsOnDate = $this->interviews()
            ->whereDate('date', $date)
            ->where('status', 'scheduled')
            ->count();

        if ($interviewsOnDate >= $this->max_interviews_per_day) {
            return false;
        }

        // Check availability schedule if provided
        if ($this->availability && $time) {
            $dayOfWeek = strtolower(date('l', strtotime($date)));
            $dayAvailability = $this->availability[$dayOfWeek] ?? null;

            if (!$dayAvailability || !$dayAvailability['available']) {
                return false;
            }

            // Check time slots
            if (isset($dayAvailability['slots'])) {
                $timeSlot = date('H:i', strtotime($time));
                foreach ($dayAvailability['slots'] as $slot) {
                    if ($timeSlot >= $slot['start'] && $timeSlot <= $slot['end']) {
                        return true;
                    }
                }
                return false;
            }
        }

        return true;
    }

    public function getInterviewsCountForDate($date): int
    {
        return $this->interviews()
            ->whereDate('date', $date)
            ->where('status', 'scheduled')
            ->count();
    }

    public function getRemainingCapacityForDate($date): int
    {
        return max(0, $this->max_interviews_per_day - $this->getInterviewsCountForDate($date));
    }
}
