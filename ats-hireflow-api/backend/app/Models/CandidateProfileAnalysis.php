<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class CandidateProfileAnalysis extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'candidate_id',
        'job_posting_id',
        'created_by',
        'analysis_type',
        'status',
        'external_service_id',
        'extracted_name',
        'extracted_email',
        'extracted_phone',
        'extracted_address',
        'extracted_skills',
        'extracted_experience',
        'extracted_education',
        'ai_summary',
        'strengths',
        'weaknesses',
        'improvement_areas',
        'recommendations',
        'overall_score',
        'skills_score',
        'experience_score',
        'education_score',
        'cultural_fit_score',
        'job_match_details',
        'missing_requirements',
        'matching_criteria',
        'analysis_started_at',
        'analysis_completed_at',
        'error_message',
        'processing_logs',
    ];

    protected $casts = [
        'extracted_skills' => 'array',
        'extracted_experience' => 'array',
        'extracted_education' => 'array',
        'strengths' => 'array',
        'weaknesses' => 'array',
        'improvement_areas' => 'array',
        'recommendations' => 'array',
        'job_match_details' => 'array',
        'missing_requirements' => 'array',
        'matching_criteria' => 'array',
        'processing_logs' => 'array',
        'overall_score' => 'integer',
        'skills_score' => 'integer',
        'experience_score' => 'integer',
        'education_score' => 'integer',
        'cultural_fit_score' => 'integer',
        'analysis_started_at' => 'datetime',
        'analysis_completed_at' => 'datetime',
    ];

    protected static $logAttributes = [
        'status',
        'overall_score',
        'skills_score',
        'experience_score'
    ];
    protected static $logOnlyDirty = true;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'overall_score', 'skills_score', 'experience_score'])
            ->logOnlyDirty();
    }

    // Relationships
    public function candidate(): BelongsTo
    {
        return $this->belongsTo(Candidate::class);
    }

    public function jobPosting(): BelongsTo
    {
        return $this->belongsTo(JobPosting::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Computed attributes
    public function getAverageScoreAttribute(): float
    {
        $scores = array_filter([
            $this->overall_score,
            $this->skills_score,
            $this->experience_score,
            $this->education_score,
            $this->cultural_fit_score
        ]);

        return count($scores) > 0 ? round(array_sum($scores) / count($scores), 1) : 0;
    }

    public function getProcessingDurationAttribute(): ?int
    {
        if ($this->analysis_started_at && $this->analysis_completed_at) {
            return $this->analysis_started_at->diffInSeconds($this->analysis_completed_at);
        }
        return null;
    }

    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getIsFailedAttribute(): bool
    {
        return $this->status === 'failed';
    }

    public function getIsProcessingAttribute(): bool
    {
        return in_array($this->status, ['pending', 'processing']);
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeForCandidate($query, $candidateId)
    {
        return $query->where('candidate_id', $candidateId);
    }

    public function scopeForJob($query, $jobId)
    {
        return $query->where('job_posting_id', $jobId);
    }

    public function scopeByAnalysisType($query, $type)
    {
        return $query->where('analysis_type', $type);
    }

    public function scopeWithHighScore($query, $minScore = 70)
    {
        return $query->where('overall_score', '>=', $minScore);
    }
}
