<?php

namespace App\Console\Commands;

use App\Models\Candidate;
use App\Models\JobPosting;
use App\Services\CandidateProfileAnalysisService;
use Illuminate\Console\Command;

class DemoCandidateAnalysis extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demo:candidate-analysis {--candidate-id=} {--job-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Demo hệ thống phân tích hồ sơ ứng viên';

    protected CandidateProfileAnalysisService $analysisService;

    public function __construct(CandidateProfileAnalysisService $analysisService)
    {
        parent::__construct();
        $this->analysisService = $analysisService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Demo Hệ thống Phân tích Hồ sơ Ứng viên');
        $this->info('==========================================');

        // Get candidate
        $candidateId = $this->option('candidate-id');
        if ($candidateId) {
            $candidate = Candidate::findOrFail($candidateId);
        } else {
            $candidate = Candidate::first();
            if (!$candidate) {
                $this->error('❌ Không tìm thấy ứng viên nào trong database');
                return;
            }
        }

        $this->info("📋 Ứng viên: {$candidate->name} (ID: {$candidate->id})");
        $this->info("📧 Email: {$candidate->email}");
        $this->info("💼 Vị trí: {$candidate->position}");

        // Get job posting
        $jobId = $this->option('job-id');
        if ($jobId) {
            $jobPosting = JobPosting::findOrFail($jobId);
        } else {
            $jobPosting = JobPosting::first();
        }

        if ($jobPosting) {
            $this->info("🎯 Job: {$jobPosting->title} (ID: {$jobPosting->id})");
        }

        $this->newLine();

        // Demo 1: Resume Extraction
        $this->info('📄 Demo 1: Trích xuất thông tin từ CV');
        $this->info('----------------------------------');
        
        try {
            $this->info('🔄 Đang trích xuất thông tin từ CV...');
            
            $extractionAnalysis = $this->analysisService->extractResumeInformation(
                $candidate->id,
                1 // Demo user ID
            );

            $this->info("✅ Trích xuất CV hoàn thành!");
            $this->info("🆔 Analysis ID: {$extractionAnalysis->id}");
            $this->info("📊 Trạng thái: {$extractionAnalysis->status}");
            
            if ($extractionAnalysis->extracted_name) {
                $this->info("👤 Tên trích xuất: {$extractionAnalysis->extracted_name}");
            }
            
            if ($extractionAnalysis->extracted_email) {
                $this->info("📧 Email trích xuất: {$extractionAnalysis->extracted_email}");
            }
            
            if ($extractionAnalysis->extracted_skills) {
                $skills = implode(', ', array_slice($extractionAnalysis->extracted_skills, 0, 5));
                $this->info("🛠️ Kỹ năng: {$skills}");
            }

        } catch (\Exception $e) {
            $this->error("❌ Trích xuất CV thất bại: " . $e->getMessage());
        }

        $this->newLine();

        // Demo 2: AI Analysis
        $this->info('🤖 Demo 2: Phân tích AI');
        $this->info('----------------------');
        
        try {
            $this->info('🔄 Đang tạo phân tích AI...');
            
            $aiAnalysis = $this->analysisService->generateAIAnalysis(
                $candidate->id,
                null, // No job matching, just general analysis
                1 // Demo user ID
            );

            $this->info("✅ Phân tích AI hoàn thành!");
            $this->info("🆔 Analysis ID: {$aiAnalysis->id}");
            $this->info("📊 Trạng thái: {$aiAnalysis->status}");
            
            if ($aiAnalysis->ai_summary) {
                $summary = substr($aiAnalysis->ai_summary, 0, 100) . "...";
                $this->info("📝 Tóm tắt: {$summary}");
            }
            
            if ($aiAnalysis->strengths) {
                $this->info("💪 Điểm mạnh:");
                foreach (array_slice($aiAnalysis->strengths, 0, 3) as $strength) {
                    $this->info("  • {$strength}");
                }
            }
            
            if ($aiAnalysis->overall_score) {
                $this->info("🎯 Điểm tổng: {$aiAnalysis->overall_score}/100");
            }

        } catch (\Exception $e) {
            $this->error("❌ Phân tích AI thất bại: " . $e->getMessage());
        }

        $this->newLine();

        // Demo 3: Job Matching (if job posting available)
        if ($jobPosting) {
            $this->info('🎯 Demo 3: So khớp công việc');
            $this->info('---------------------------');
            
            try {
                $this->info("🔄 Đang so khớp với công việc: {$jobPosting->title}");
                
                $matchingAnalysis = $this->analysisService->generateAIAnalysis(
                    $candidate->id,
                    $jobPosting->id,
                    1 // Demo user ID
                );

                $this->info("✅ So khớp công việc hoàn thành!");
                $this->info("🆔 Analysis ID: {$matchingAnalysis->id}");
                $this->info("📊 Trạng thái: {$matchingAnalysis->status}");
                
                // Display scores
                $this->info("📊 Điểm số so khớp:");
                if ($matchingAnalysis->overall_score) {
                    $this->info("  🎯 Tổng điểm: {$matchingAnalysis->overall_score}%");
                }
                if ($matchingAnalysis->skills_score) {
                    $this->info("  🛠️ Kỹ năng: {$matchingAnalysis->skills_score}%");
                }
                if ($matchingAnalysis->experience_score) {
                    $this->info("  💼 Kinh nghiệm: {$matchingAnalysis->experience_score}%");
                }
                if ($matchingAnalysis->education_score) {
                    $this->info("  🎓 Học vấn: {$matchingAnalysis->education_score}%");
                }
                if ($matchingAnalysis->cultural_fit_score) {
                    $this->info("  🏢 Phù hợp văn hóa: {$matchingAnalysis->cultural_fit_score}%");
                }
                
                if ($matchingAnalysis->missing_requirements) {
                    $this->info("⚠️ Thiếu yêu cầu:");
                    foreach (array_slice($matchingAnalysis->missing_requirements, 0, 2) as $requirement) {
                        $this->info("  • {$requirement}");
                    }
                }

            } catch (\Exception $e) {
                $this->error("❌ So khớp công việc thất bại: " . $e->getMessage());
            }
        }

        $this->newLine();
        $this->info('🎉 Demo hoàn thành!');
        $this->info('💡 Kiểm tra database để xem kết quả chi tiết.');
        $this->info('🔗 Sử dụng API endpoints để tích hợp với frontend.');
        
        // Show API examples
        $this->newLine();
        $this->info('📚 Ví dụ API calls:');
        $this->info("POST /api/v1/candidate-analysis/extract-resume");
        $this->info('{"candidate_id": ' . $candidate->id . '}');
        $this->newLine();
        $this->info("POST /api/v1/candidate-analysis/generate-analysis");
        $this->info('{"candidate_id": ' . $candidate->id . ($jobPosting ? ', "job_posting_id": ' . $jobPosting->id : '') . '}');
        $this->newLine();
        $this->info("GET /api/v1/candidate-analysis/candidate/{$candidate->id}/summary");
    }
}
