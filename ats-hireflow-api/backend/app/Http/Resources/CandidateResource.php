<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CandidateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'position' => $this->position,
            'experience' => $this->experience,
            'status' => $this->status,
            'status_color' => $this->status_color,
            'applied_date' => $this->applied_date?->format('Y-m-d'),
            'source' => $this->source,
            'location' => $this->location,
            'salary_expectation_min' => round($this->salary_expectation_min,0),
            'salary_expectation_max' => round($this->salary_expectation_max),
            'salary_currency' => $this->salary_currency,
            'salary_expectation_range' => $this->salary_expectation_range,
            'rating' => $this->rating,

            'ai_score' => $this->ai_score,
            'social_links' => [
                'linkedin' => $this->linkedin_url,
                'github' => $this->github_url,
                'portfolio' => $this->portfolio_url,
            ],
            'avatar' => $this->avatar,
            'resume_url' => $this->resume_url,
            'notes' => $this->notes,
            'initials' => $this->initials,
            'job_posting_id' => $this->job_posting_id,


            // Relationships
            'job_posting' => $this->whenLoaded('jobPosting', function () {
                return [
                    'id' => $this->jobPosting->id,
                    'title' => $this->jobPosting->title,
                    'department' => $this->jobPosting->department,
                    'location' => $this->jobPosting->location,
                ];
            }),

            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'email' => $this->createdBy->email,
                ];
            }),

            'assigned_to' => $this->whenLoaded('assignedTo', function () {
                return $this->assignedTo ? [
                    'id' => $this->assignedTo->id,
                    'name' => $this->assignedTo->name,
                    'email' => $this->assignedTo->email,
                ] : null;
            }),

            // Simplified education and work_history - now just text fields
            'education' => $this->education,
            'work_history' => $this->work_history,

            // Simplified skills and tags - now just arrays of strings
            'skills' => $this->skills ?? [],
            'tags' => $this->tags ?? [],

            'interviews' => $this->whenLoaded('interviews', function () {
                return $this->interviews->map(function ($interview) {
                    return [
                        'id' => $interview->id,
                        'date' => $interview->date->format('Y-m-d'),
                        'time' => $interview->time->format('H:i'),
                        'type' => $interview->type,
                        'status' => $interview->status,
                        'interview_type' => $interview->interview_type,
                        'round' => $interview->round,
                        'interviewer' => $interview->interviewer->name ?? null,
                    ];
                });
            }),

            'status_history' => $this->whenLoaded('statusHistory', function () {
                return $this->statusHistory->take(5)->map(function ($history) {
                    return [
                        'id' => $history->id,
                        'old_status' => $history->old_status,
                        'new_status' => $history->new_status,
                        'notes' => $history->notes,
                        'changed_by' => $history->changedBy->name,
                        'created_at' => $history->created_at->format('Y-m-d H:i:s'),
                    ];
                });
            }),

            // Timestamps
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
