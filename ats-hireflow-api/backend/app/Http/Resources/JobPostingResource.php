<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobPostingResource extends JsonResource
{

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'department' => $this->department,
            'location' => $this->location,
            'type' => $this->type,
            'work_location' => $this->work_location,
            'salary' => [
                'min' => $this->salary_min,
                'max' => $this->salary_max,
                'currency' => $this->currency,
                'range' => $this->salary_range,
            ],
            'description' => $this->description,
            'education_required' => $this->education_required,
            'company_culture' => $this->company_culture,
            'status' => $this->status,
            'priority' => $this->priority,
            'posted_date' => $this->posted_date?->format('Y-m-d'),
            'closing_date' => $this->closing_date?->format('Y-m-d'),
            'experience_level' => $this->experience_level,
            'positions' => $this->positions,
            'applicant_count' => $this->applicant_count,
            'view_count' => $this->view_count,
            'initials' => $this->initials,
            'is_active' => $this->is_active,
            'hiring_manager_id' => $this->hiring_manager_id,
            'recruiter_id' => $this->recruiter_id,


            // Relationships
            'hiring_manager' => $this->whenLoaded('hiringManager', function () {
                return $this->hiringManager ? [
                    'id' => $this->hiringManager->id,
                    'name' => $this->hiringManager->name,
                    'email' => $this->hiringManager->email,
                    'department' => $this->hiringManager->department,
                ] : null;
            }),

            'recruiter' => $this->whenLoaded('recruiter', function () {
                return $this->recruiter ? [
                    'id' => $this->recruiter->id,
                    'name' => $this->recruiter->name,
                    'email' => $this->recruiter->email,
                    'department' => $this->recruiter->department,
                ] : null;
            }),

            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'email' => $this->createdBy->email,
                ];
            }),

            // Simplified requirements, benefits, responsibilities and skills - now just arrays of strings
            'requirements' => $this->requirements ?? [],
            'benefits' => $this->benefits ?? [],
            'responsibilities' => $this->responsibilities ?? [],

            'skills' => $this->skills ?? [],

            'candidates' => $this->whenLoaded('candidates', function () {
                return $this->candidates->map(function ($candidate) {
                    return [
                        'id' => $candidate->id,
                        'name' => $candidate->name,
                        'email' => $candidate->email,
                        'status' => $candidate->status,
                        'rating' => $candidate->rating,
                        'ai_score' => $candidate->ai_score,
                        'applied_date' => $candidate->applied_date->format('Y-m-d'),
                    ];
                });
            }),

            'interviews' => $this->whenLoaded('interviews', function () {
                return $this->interviews->map(function ($interview) {
                    return [
                        'id' => $interview->id,
                        'candidate_name' => $interview->candidate->name,
                        'interviewer_name' => $interview->interviewer->name ?? null,
                        'date' => $interview->date->format('Y-m-d'),
                        'time' => $interview->time->format('H:i'),
                        'status' => $interview->status,
                        'type' => $interview->type,
                    ];
                });
            }),

            // Analytics data
            'analytics' => $this->when($request->routeIs('*.analytics'), function () {
                return [
                    'total_applicants' => $this->candidates()->count(),
                    'by_status' => $this->candidates()
                        ->selectRaw('status, COUNT(*) as count')
                        ->groupBy('status')
                        ->pluck('count', 'status'),
                    'by_source' => $this->candidates()
                        ->selectRaw('source, COUNT(*) as count')
                        ->whereNotNull('source')
                        ->groupBy('source')
                        ->pluck('count', 'source'),
                    'avg_rating' => round($this->candidates()->avg('rating'), 2),
                    'avg_ai_score' => round($this->candidates()->avg('ai_score'), 2),
                    'interviews_scheduled' => $this->interviews()->where('status', 'scheduled')->count(),
                    'interviews_completed' => $this->interviews()->where('status', 'completed')->count(),
                ];
            }),

            // Timestamps
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
