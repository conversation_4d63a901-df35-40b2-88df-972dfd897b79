<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InterviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'date' => $this->date->format('Y-m-d'),
            'time' => $this->time->format('H:i'),
            'scheduled_at' => $this->date->format('Y-m-d') . 'T' . $this->time->format('H:i'),
            'duration' => $this->duration,
            'type' => $this->type,
            'status' => $this->status,
            'status_color' => $this->status_color,
            'meeting_link' => $this->meeting_link,
            'meeting_password' => $this->meeting_password,
            'location' => $this->location,
            'address' => $this->address,
            'notes' => $this->notes,
            'agenda' => $this->agenda,
            'round' => $this->round,
            'interview_type' => $this->interview_type,
            'type_icon' => $this->type_icon,
            'reminder_sent' => $this->reminder_sent,
            'date_time' => $this->date_time,
            'is_upcoming' => $this->isUpcoming(),
            'can_be_cancelled' => $this->canBeCancelled(),
            'can_be_rescheduled' => $this->canBeRescheduled(),
            'has_feedback' => $this->hasFeedback(),
            'candidate_id' => $this->candidate_id,
            'job_posting_id' => $this->job_posting_id,
            'interviewer_id' => $this->interviewer_id,

            // Relationships
            'candidate' => $this->whenLoaded('candidate', function () {
                return [
                    'id' => $this->candidate->id,
                    'name' => $this->candidate->name,
                    'email' => $this->candidate->email,
                    'phone' => $this->candidate->phone,
                  
                ];
            }),

            'job_posting' => $this->whenLoaded('jobPosting', function () {
                return [
                    'id' => $this->jobPosting->id,
                    'title' => $this->jobPosting->title,
                    'department' => $this->jobPosting->department,
                    'location' => $this->jobPosting->location,
                    'type' => $this->jobPosting->type,
                    'work_location' => $this->jobPosting->work_location,
                   
                ];
            }),

            'interviewer' => $this->whenLoaded('interviewer', function () {
                return [
                    'id' => $this->interviewer->id,
                    'name' => $this->interviewer->name,
                    'email' => $this->interviewer->email,
                    'department' => $this->interviewer->department,
                    'expertise' => $this->interviewer->expertise,
                    'location' => $this->interviewer->location,
                ];
            }),

            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'email' => $this->createdBy->email,
                ];
            }),

            'feedback' => $this->whenLoaded('feedback', function () {
                return $this->feedback ? new InterviewFeedbackResource($this->feedback) : null;
            }),

            // Timestamps
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
