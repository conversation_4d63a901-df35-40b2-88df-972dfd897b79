<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InterviewerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'email' => $this->email,
            'department' => $this->department,
            'expertise' => $this->expertise ?? [],
            'expertise_list' => $this->expertise_list,
            'location' => $this->location,
            'max_interviews_per_day' => $this->max_interviews_per_day,
            'availability' => $this->availability ?? [],
            'time_slots' => $this->time_slots ?? [],
            'timezone' => $this->timezone,
            'is_active' => $this->is_active,

            // User information
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                    'phone' => $this->user->phone,
                    'avatar' => $this->user->avatar,
                    'initials' => $this->user->initials,
                    'title' => $this->user->title,
                    'is_active' => $this->user->is_active,
                ];
            }),

            // Statistics
            'statistics' => $this->when($request->has('include_stats'), function () {
                return [
                    'total_interviews' => $this->interviews()->count(),
                    'completed_interviews' => $this->interviews()->where('status', 'completed')->count(),
                    'scheduled_interviews' => $this->interviews()->where('status', 'scheduled')->count(),
                    'upcoming_interviews' => $this->interviews()
                        ->where('status', 'scheduled')
                        ->where('date', '>=', now())
                        ->count(),
                    'average_rating' => round($this->feedback()->avg('rating') ?? 0, 2),
                ];
            }),

            // Timestamps
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
