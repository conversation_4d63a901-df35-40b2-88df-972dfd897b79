<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role,
            'role_display_name' => $this->getRoleDisplayName($this->role),
            'department' => $this->department,
            'title' => $this->title,
            'phone' => $this->phone,
            'avatar' => $this->avatar,
            'is_active' => $this->is_active,
            'last_login_at' => $this->last_login_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            
            // Include relationships when loaded
            'roles' => $this->whenLoaded('roles', function () {
                return $this->roles->map(function ($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->name,
                        'display_name' => $this->getRoleDisplayName($role->name),
                    ];
                });
            }),
            
            'permissions' => $this->whenLoaded('permissions', function () {
                return $this->permissions->pluck('name');
            }),
            
            // Additional computed fields
            'initials' => $this->initials,
            'is_interviewer' => $this->is_interviewer,
            
            // Statistics for admin view
            'statistics' => $this->when(
                auth()->user() && auth()->user()->hasRole('admin'),
                function () {
                    return [
                        'created_candidates_count' => $this->createdCandidates()->count(),
                        'assigned_candidates_count' => $this->assignedCandidates()->count(),
                        'created_job_postings_count' => $this->createdJobPostings()->count(),
                        'managed_job_postings_count' => $this->managedJobPostings()->count(),
                        'recruited_job_postings_count' => $this->recruitedJobPostings()->count(),
                        'created_interviews_count' => $this->createdInterviews()->count(),
                    ];
                }
            ),
        ];
    }

    /**
     * Get role display name in Vietnamese.
     */
    private function getRoleDisplayName(string $role): string
    {
        $roleNames = [
            'admin' => 'Quản trị viên',
            'recruiter' => 'Nhà tuyển dụng',
            'hiring_manager' => 'Quản lý tuyển dụng',
            'interviewer' => 'Người phỏng vấn',
        ];

        return $roleNames[$role] ?? $role;
    }
}
