<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CandidateProfileAnalysisResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'candidate_id' => $this->candidate_id,
            'job_posting_id' => $this->job_posting_id,
            'analysis_type' => $this->analysis_type,
            'status' => $this->status,
            'external_service_id' => $this->external_service_id,

            // Extracted information (for resume_extraction type)
            'extracted_information' => $this->when($this->analysis_type === 'resume_extraction', [
                'name' => $this->extracted_name,
                'email' => $this->extracted_email,
                'phone' => $this->extracted_phone,
                'address' => $this->extracted_address,
                'skills' => $this->extracted_skills,
                'experience' => $this->extracted_experience,
                'education' => $this->extracted_education,
            ]),

            // AI analysis results (for ai_analysis and job_matching types)
            'ai_analysis' => $this->when(in_array($this->analysis_type, ['ai_analysis', 'job_matching']), [
                'summary' => $this->ai_summary,
                'strengths' => $this->strengths,
                'weaknesses' => $this->weaknesses,
                'improvement_areas' => $this->improvement_areas,
                'recommendations' => $this->recommendations,
            ]),

            // Scoring
            'scores' => [
                'overall' => $this->overall_score,
                'skills' => $this->skills_score,
                'experience' => $this->experience_score,
                'education' => $this->education_score,
                'cultural_fit' => $this->cultural_fit_score,
                'average' => $this->average_score,
            ],

            // Job matching specific (for job_matching type)
            'job_matching' => $this->when($this->analysis_type === 'job_matching', [
                'match_details' => $this->job_match_details,
                'missing_requirements' => $this->missing_requirements,
                'matching_criteria' => $this->matching_criteria,
            ]),

            // Processing metadata
            'processing' => [
                'started_at' => $this->analysis_started_at?->format('Y-m-d H:i:s'),
                'completed_at' => $this->analysis_completed_at?->format('Y-m-d H:i:s'),
                'duration_seconds' => $this->processing_duration,
                'error_message' => $this->when($this->status === 'failed', $this->error_message),
            ],

            // Status helpers
            'is_completed' => $this->is_completed,
            'is_failed' => $this->is_failed,
            'is_processing' => $this->is_processing,

            // Timestamps
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),

            // Relationships
            'candidate' => $this->whenLoaded('candidate', function () {
                return [
                    'id' => $this->candidate->id,
                    'name' => $this->candidate->name,
                    'email' => $this->candidate->email,
                    'position' => $this->candidate->position,
                    'rating' => $this->candidate->rating,
                    'ai_score' => $this->candidate->ai_score,
                    'status' => $this->candidate->status,
                ];
            }),

            'job_posting' => $this->whenLoaded('jobPosting', function () {
                return [
                    'id' => $this->jobPosting->id,
                    'title' => $this->jobPosting->title,
                    'department' => $this->jobPosting->department,
                    'location' => $this->jobPosting->location,
                    'type' => $this->jobPosting->type,
                    'status' => $this->jobPosting->status,
                ];
            }),

            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'email' => $this->createdBy->email,
                ];
            }),
        ];
    }
}
