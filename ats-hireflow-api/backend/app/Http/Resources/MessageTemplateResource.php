<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MessageTemplateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'subject' => $this->subject,
            'content' => $this->content,
            'variables' => $this->variables,
            'category' => $this->category,
            'category_name' => $this->category_name,
            'type' => $this->type,
            'type_name' => $this->type_name,
            'language' => $this->language,
            'version' => $this->version,
            'is_active' => $this->is_active,
            'parent_template_id' => $this->parent_template_id,

            // Computed attributes
            'available_variables' => $this->getAvailableVariables(),
            'extracted_variables' => $this->extractVariablesFromContent(),
            'is_latest_version' => $this->isLatestVersion(),

            // Usage statistics
            'messages_count' => $this->whenLoaded('messages', function () {
                return $this->messages->count();
            }),

            // Timestamps
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),

            // Relationships
            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'email' => $this->createdBy->email,
                ];
            }),

            'parent_template' => $this->whenLoaded('parentTemplate', function () {
                return [
                    'id' => $this->parentTemplate->id,
                    'name' => $this->parentTemplate->name,
                    'version' => $this->parentTemplate->version,
                ];
            }),

            'child_templates' => $this->whenLoaded('childTemplates', function () {
                return $this->childTemplates->map(function ($template) {
                    return [
                        'id' => $template->id,
                        'name' => $template->name,
                        'version' => $template->version,
                        'is_active' => $template->is_active,
                        'created_at' => $template->created_at?->format('Y-m-d H:i:s'),
                    ];
                });
            }),

            'messages' => $this->whenLoaded('messages', function () {
                return $this->messages->map(function ($message) {
                    return [
                        'id' => $message->id,
                        'type' => $message->type,
                        'status' => $message->status,
                        'to_email' => $message->to_email,
                        'subject' => $message->subject,
                        'sent_at' => $message->sent_at?->format('Y-m-d H:i:s'),
                        'created_at' => $message->created_at?->format('Y-m-d H:i:s'),
                    ];
                });
            }),
        ];
    }
}
