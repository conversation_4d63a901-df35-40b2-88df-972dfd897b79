<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InterviewFeedbackResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'rating' => $this->rating,
            'comments' => $this->comments,
            'recommend' => $this->recommend,
            'strengths' => $this->strengths,
            'concerns' => $this->concerns,
            'next_round_recommendation' => $this->next_round_recommendation,
            'technical_score' => $this->technical_score,
            'communication_score' => $this->communication_score,
            'cultural_fit_score' => $this->cultural_fit_score,
            'overall_score' => $this->overall_score,

            // Relationships
            'interview' => $this->whenLoaded('interview', function () {
                return [
                    'id' => $this->interview->id,
                    'date' => $this->interview->date->format('Y-m-d'),
                    'time' => $this->interview->time->format('H:i'),
                    'type' => $this->interview->type,
                    'interview_type' => $this->interview->interview_type,
                    'round' => $this->interview->round,
                ];
            }),

            'interviewer' => $this->whenLoaded('interviewer', function () {
                return [
                    'id' => $this->interviewer->id,
                    'name' => $this->interviewer->name,
                    'email' => $this->interviewer->email,
                    'department' => $this->interviewer->department,
                ];
            }),

            // Timestamps
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
