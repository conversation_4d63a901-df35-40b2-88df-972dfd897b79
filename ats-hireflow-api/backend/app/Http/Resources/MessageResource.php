<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'type_name' => $this->type_name,
            'category' => $this->category,
            'category_name' => $this->category_name,
            'candidate_id' => $this->candidate_id,
            'job_posting_id' => $this->job_posting_id,
            'template_id' => $this->template_id,
            'parent_message_id' => $this->parent_message_id,
            'thread_id' => $this->thread_id,

            // Recipient information
            'to_email' => $this->to_email,
            'to_phone' => $this->to_phone,
            'to_name' => $this->to_name,
            'from_email' => $this->from_email,
            'from_name' => $this->from_name,

            // Message content
            'subject' => $this->subject,
            'content' => $this->content,

            // Status and tracking
            'status' => $this->status,
            'status_name' => $this->status_name,
            'status_color' => $this->status_color,
            'priority' => $this->priority,

            // Computed attributes
            'is_scheduled' => $this->is_scheduled,
            'is_due' => $this->is_due,
            'delivery_time' => $this->delivery_time,
            'read_time' => $this->read_time,

            // Timestamps
            'scheduled_at' => $this->scheduled_at?->format('Y-m-d H:i:s'),
            'sent_at' => $this->sent_at?->format('Y-m-d H:i:s'),
            'delivered_at' => $this->delivered_at?->format('Y-m-d H:i:s'),
            'read_at' => $this->read_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),

            // Error information
            'error_message' => $this->when($this->status === 'failed', $this->error_message),

            // Metadata
            'metadata' => $this->metadata,
            'external_id' => $this->external_id,

            // Relationships
            'candidate' => $this->whenLoaded('candidate', function () {
                return [
                    'id' => $this->candidate->id,
                    'name' => $this->candidate->name,
                    'email' => $this->candidate->email,
                    'phone' => $this->candidate->phone,
                    'position' => $this->candidate->position,
                ];
            }),

            'job_posting' => $this->whenLoaded('jobPosting', function () {
                return [
                    'id' => $this->jobPosting->id,
                    'title' => $this->jobPosting->title,
                    'department' => $this->jobPosting->department,
                    'location' => $this->jobPosting->location,
                ];
            }),

            'template' => $this->whenLoaded('template', function () {
                return [
                    'id' => $this->template->id,
                    'name' => $this->template->name,
                    'category' => $this->template->category,
                    'type' => $this->template->type,
                    'version' => $this->template->version,
                ];
            }),

            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'email' => $this->createdBy->email,
                ];
            }),

            'parent_message' => $this->whenLoaded('parentMessage', function () {
                return [
                    'id' => $this->parentMessage->id,
                    'subject' => $this->parentMessage->subject,
                    'type' => $this->parentMessage->type,
                    'status' => $this->parentMessage->status,
                    'created_at' => $this->parentMessage->created_at?->format('Y-m-d H:i:s'),
                ];
            }),

            'replies' => $this->whenLoaded('replies', function () {
                return $this->replies->map(function ($reply) {
                    return [
                        'id' => $reply->id,
                        'subject' => $reply->subject,
                        'type' => $reply->type,
                        'status' => $reply->status,
                        'created_at' => $reply->created_at?->format('Y-m-d H:i:s'),
                    ];
                });
            }),

            // Thread information
            'replies_count' => $this->whenLoaded('replies', function () {
                return $this->replies->count();
            }),
        ];
    }
}
