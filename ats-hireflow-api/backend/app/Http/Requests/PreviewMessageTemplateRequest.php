<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PreviewMessageTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'data' => 'nullable|array',
            'data.*' => 'string|max:1000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'data.array' => 'Dữ liệu preview phải là một object.',
            'data.*.string' => 'Giá trị của biến phải là chuỗi.',
            'data.*.max' => 'Giá trị của biến không được vượt quá 1000 ký tự.',
        ];
    }
}
