<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreInterviewerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create_interviews') || 
               $this->user()->hasRole(['admin', 'recruiter', 'hiring_manager']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id|unique:interviewers,user_id',
            'department' => 'nullable|string|max:100',
            'expertise' => 'nullable|array',
            'expertise.*' => 'string|max:255|distinct',
            'location' => 'nullable|string|max:255',
            'max_interviews_per_day' => 'nullable|integer|min:1|max:20',
            'availability' => 'nullable|array',
            'availability.monday' => 'nullable|array',
            'availability.tuesday' => 'nullable|array',
            'availability.wednesday' => 'nullable|array',
            'availability.thursday' => 'nullable|array',
            'availability.friday' => 'nullable|array',
            'availability.saturday' => 'nullable|array',
            'availability.sunday' => 'nullable|array',
            'availability.*.start_time' => 'nullable|date_format:H:i',
            'availability.*.end_time' => 'nullable|date_format:H:i|after:availability.*.start_time',
            'time_slots' => 'nullable|array',
            'time_slots.*' => 'string|date_format:H:i|distinct',
            'timezone' => 'nullable|string|max:50|in:Asia/Ho_Chi_Minh,UTC,Asia/Bangkok,Asia/Singapore',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'User is required to create an interviewer profile.',
            'user_id.exists' => 'Selected user does not exist.',
            'user_id.unique' => 'This user already has an interviewer profile.',
            'expertise.array' => 'Expertise must be an array of skills.',
            'expertise.*.string' => 'Each expertise item must be a string.',
            'max_interviews_per_day.min' => 'Maximum interviews per day must be at least 1.',
            'max_interviews_per_day.max' => 'Maximum interviews per day cannot exceed 20.',
            'availability.*.start_time.date_format' => 'Start time must be in HH:MM format.',
            'availability.*.end_time.date_format' => 'End time must be in HH:MM format.',
            'availability.*.end_time.after' => 'End time must be after start time.',
            'time_slots.*.date_format' => 'Time slots must be in HH:MM format.',
            'timezone.in' => 'Please select a valid timezone.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if user is not already an interviewer
            if ($this->filled('user_id')) {
                $existingInterviewer = \App\Models\Interviewer::where('user_id', $this->user_id)->first();
                if ($existingInterviewer) {
                    $validator->errors()->add('user_id', 'This user already has an interviewer profile.');
                }
            }

            // Validate availability time slots don't overlap
            if ($this->filled('availability')) {
                foreach ($this->availability as $day => $slots) {
                    if (is_array($slots) && count($slots) > 1) {
                        for ($i = 0; $i < count($slots) - 1; $i++) {
                            for ($j = $i + 1; $j < count($slots); $j++) {
                                if (isset($slots[$i]['start_time'], $slots[$i]['end_time'], 
                                         $slots[$j]['start_time'], $slots[$j]['end_time'])) {
                                    $start1 = strtotime($slots[$i]['start_time']);
                                    $end1 = strtotime($slots[$i]['end_time']);
                                    $start2 = strtotime($slots[$j]['start_time']);
                                    $end2 = strtotime($slots[$j]['end_time']);
                                    
                                    if (($start1 < $end2) && ($start2 < $end1)) {
                                        $validator->errors()->add("availability.{$day}", 
                                            "Time slots for {$day} cannot overlap.");
                                        break 2;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
    }
}
