<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreInterviewFeedbackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
        return $this->user()->can('provide_feedback');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'interview_id' => 'required|exists:interviews,id',
            'interviewer_id' => 'required|exists:interviewers,id',
            'rating' => 'nullable|numeric|min:0|max:5',
            'comments' => 'nullable|string',
            'recommend' => 'nullable|boolean',
            'strengths' => 'nullable|array',
            'strengths.*' => 'string|max:255',
            'concerns' => 'nullable|array',
            'concerns.*' => 'string|max:255',
            'next_round_recommendation' => 'nullable|in:screening,technical,case-study,portfolio,cultural,final,offer,reject',
            'technical_score' => 'nullable|integer|min:0|max:100',
            'communication_score' => 'nullable|integer|min:0|max:100',
            'cultural_fit_score' => 'nullable|integer|min:0|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'interview_id.required' => 'Interview is required.',
            'interview_id.exists' => 'Selected interview does not exist.',
            'interviewer_id.required' => 'Interviewer is required.',
            'interviewer_id.exists' => 'Selected interviewer does not exist.',
            'rating.min' => 'Rating must be between 0 and 5.',
            'rating.max' => 'Rating must be between 0 and 5.',
            'technical_score.min' => 'Technical score must be between 0 and 100.',
            'technical_score.max' => 'Technical score must be between 0 and 100.',
            'communication_score.min' => 'Communication score must be between 0 and 100.',
            'communication_score.max' => 'Communication score must be between 0 and 100.',
            'cultural_fit_score.min' => 'Cultural fit score must be between 0 and 100.',
            'cultural_fit_score.max' => 'Cultural fit score must be between 0 and 100.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if feedback already exists for this interview and interviewer
            if ($this->filled(['interview_id', 'interviewer_id'])) {
                $existingFeedback = \App\Models\InterviewFeedback::where('interview_id', $this->interview_id)
                    ->where('interviewer_id', $this->interviewer_id)
                    ->exists();

                if ($existingFeedback) {
                    $validator->errors()->add('interview_id', 'Feedback already exists for this interview.');
                }
            }

            // Check if the interviewer is authorized to provide feedback for this interview
            if ($this->filled(['interview_id', 'interviewer_id'])) {
                $interview = \App\Models\Interview::find($this->interview_id);
                if ($interview && $interview->interviewer_id != $this->interviewer_id) {
                    $validator->errors()->add('interviewer_id', 'You are not authorized to provide feedback for this interview.');
                }
            }
        });
    }
}
