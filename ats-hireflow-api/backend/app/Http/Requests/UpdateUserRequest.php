<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->route('user');
        
        // Admin can update any user, users can update themselves
        return auth()->user() && (
            auth()->user()->hasRole('admin') || 
            auth()->id() === $user->id
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->route('user');
        $isAdmin = auth()->user()->hasRole('admin');
        
        $rules = [
            'name' => 'sometimes|string|max:255',
            'phone' => 'sometimes|nullable|string|max:20',
            'password' => 'sometimes|nullable|string|min:8|confirmed',
        ];

        // Only admin can update these fields
        if ($isAdmin) {
            $rules['email'] = [
                'sometimes',
                'string',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($user->id)
            ];
            $rules['role'] = [
                'sometimes',
                Rule::in(['admin', 'recruiter', 'hiring_manager', 'interviewer'])
            ];
            $rules['department'] = 'sometimes|nullable|string|max:100';
            $rules['title'] = 'sometimes|nullable|string|max:100';
            $rules['is_active'] = 'sometimes|boolean';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.string' => 'Tên phải là chuỗi ký tự.',
            'name.max' => 'Tên không được vượt quá 255 ký tự.',
            
            'email.email' => 'Email không hợp lệ.',
            'email.unique' => 'Email này đã được sử dụng.',
            'email.max' => 'Email không được vượt quá 255 ký tự.',
            
            'password.min' => 'Mật khẩu phải có ít nhất 8 ký tự.',
            'password.confirmed' => 'Xác nhận mật khẩu không khớp.',
            
            'role.in' => 'Vai trò không hợp lệ.',
            
            'department.string' => 'Phòng ban phải là chuỗi ký tự.',
            'department.max' => 'Phòng ban không được vượt quá 100 ký tự.',
            
            'title.string' => 'Chức danh phải là chuỗi ký tự.',
            'title.max' => 'Chức danh không được vượt quá 100 ký tự.',
            
            'phone.string' => 'Số điện thoại phải là chuỗi ký tự.',
            'phone.max' => 'Số điện thoại không được vượt quá 20 ký tự.',
            
            'is_active.boolean' => 'Trạng thái hoạt động phải là true hoặc false.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'tên',
            'email' => 'email',
            'password' => 'mật khẩu',
            'role' => 'vai trò',
            'department' => 'phòng ban',
            'title' => 'chức danh',
            'phone' => 'số điện thoại',
            'is_active' => 'trạng thái hoạt động',
        ];
    }
}
