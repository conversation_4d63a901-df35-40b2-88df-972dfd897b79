<?php

namespace App\Http\Requests;

use App\Models\JobPosting;
use Illuminate\Foundation\Http\FormRequest;

class UpdateJobPostingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $jobPostingId = $this->route('job');
        $jobPosting = JobPosting::find($jobPostingId)->first();
        return $this->user()->can('edit_jobs') &&
               $this->user()->canManageJobPosting($jobPosting);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'sometimes|required|string|max:255',
            'department' => 'sometimes|required|string|max:100',
            'location' => 'sometimes|required|string|max:255',
            'type' => 'sometimes|required|in:full-time,part-time,contract,internship',
            'work_location' => 'sometimes|required|in:remote,onsite,hybrid',
            'salary_min' => 'nullable|numeric|min:0',
            'salary_max' => 'nullable|numeric|min:0|gte:salary_min',
            'currency' => 'nullable|string|max:3',
            'description' => 'sometimes|required|string',
            'education_required' => 'nullable|string',
            'company_culture' => 'nullable|string',
            'status' => 'nullable|in:draft,active,paused,closed',
            'priority' => 'nullable|in:low,medium,high',
            'posted_date' => 'nullable|date',
            'closing_date' => 'nullable|date|after_or_equal:posted_date',
            'hiring_manager_id' => 'nullable|exists:users,id',
            'recruiter_id' => 'nullable|exists:users,id',
            'experience_level' => 'sometimes|required|in:entry,mid,senior,lead',
            'positions' => 'nullable|integer|min:1|max:100',

            // Simplified requirements, benefits, responsibilities and skills - now just arrays of strings
            'requirements' => 'nullable|array',
            'requirements.*' => 'string|max:500|distinct',

            'benefits' => 'nullable|array',
            'benefits.*' => 'string|max:500|distinct',

            'responsibilities' => 'nullable|array',
            'responsibilities.*' => 'string|max:500|distinct',

            'skills' => 'nullable|array',
            'skills.*' => 'string|max:100|distinct',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Job title is required.',
            'department.required' => 'Department is required.',
            'location.required' => 'Location is required.',
            'type.required' => 'Job type is required.',
            'work_location.required' => 'Work location is required.',
            'description.required' => 'Job description is required.',
            'experience_level.required' => 'Experience level is required.',
            'salary_max.gte' => 'Maximum salary must be greater than or equal to minimum salary.',
            'closing_date.after_or_equal' => 'Closing date must be after or equal to posted date.',
            'hiring_manager_id.exists' => 'Selected hiring manager does not exist.',
            'recruiter_id.exists' => 'Selected recruiter does not exist.',
            'positions.integer' => 'Number of positions must be a valid integer.',
            'positions.min' => 'Number of positions must be at least 1.',
            'positions.max' => 'Number of positions cannot exceed 100.',
        ];
    }
}
