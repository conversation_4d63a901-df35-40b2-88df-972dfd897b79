<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateInterviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        \Log::info('UpdateInterviewRequest authorize called', [
            'user_id' => $this->user()->id,
            'interview_id' => $this->route('interview')?->id,
            'user_roles' => $this->user()->roles->pluck('name'),
            'user_permissions' => $this->user()->getAllPermissions()->pluck('name')
        ]);

        return true; // Temporary for testing - REMOVE IN PRODUCTION

        $interview = $this->route('interview');

        // Allow if user has permissions or roles
        if ($this->user()->can('schedule_interviews') ||
            $this->user()->can('edit_interviews') ||
            $this->user()->hasRole(['admin', 'recruiter', 'hiring_manager'])) {
            return true;
        }

        // Allow if user can manage this specific interview
        if ($interview && $this->user()->canManageInterview($interview)) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'candidate_id' => 'sometimes|required|exists:candidates,id',
            'job_posting_id' => 'sometimes|required|exists:job_postings,id',
            'interviewer_id' => 'sometimes|required|exists:interviewers,id',
            'date' => 'sometimes|required|date|after_or_equal:today',
            'time' => 'sometimes|required|date_format:H:i',
            'duration' => 'sometimes|required|integer|min:15|max:480',
            'type' => 'sometimes|required|in:video,phone,in-person',
            'status' => 'nullable|in:scheduled,completed,cancelled,rescheduled,no-show',
            'meeting_link' => 'nullable|url|max:500',
            'meeting_password' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'notes' => 'nullable|string',
            'agenda' => 'nullable|array',
            'agenda.*' => 'string',
            'round' => 'nullable|integer|min:1|max:10',
            'interview_type' => 'sometimes|required|in:screening,technical,case-study,portfolio,cultural,final',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'candidate_id.exists' => 'Selected candidate does not exist.',
            'job_posting_id.exists' => 'Selected job posting does not exist.',
            'interviewer_id.exists' => 'Selected interviewer does not exist.',
            'date.after_or_equal' => 'Interview date must be today or in the future.',
            'time.date_format' => 'Interview time must be in HH:MM format.',
            'duration.min' => 'Interview duration must be at least 15 minutes.',
            'duration.max' => 'Interview duration cannot exceed 8 hours.',
            'meeting_link.url' => 'Meeting link must be a valid URL.',
        ];
    }
}
