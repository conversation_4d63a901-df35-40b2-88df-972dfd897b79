<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreInterviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('schedule_interviews') ||
               $this->user()->can('create_interviews') ||
               $this->user()->hasRole(['admin', 'recruiter', 'hiring_manager']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'candidate_id' => 'required|exists:candidates,id',
            'job_posting_id' => 'required|exists:job_postings,id',
            'interviewer_id' => 'required|exists:interviewers,id',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required|date_format:H:i',
            'duration' => 'required|integer|min:15|max:480', // 15 minutes to 8 hours
            'type' => 'required|in:video,phone,in-person',
            'status' => 'nullable|in:scheduled,completed,cancelled,rescheduled,no-show',
            'meeting_link' => 'nullable|url|max:500',
            'meeting_password' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'notes' => 'nullable|string',
            'agenda' => 'nullable|array',
            'agenda.*' => 'string',
            'round' => 'nullable|integer|min:1|max:10',
            'interview_type' => 'required|in:screening,technical,case-study,portfolio,cultural,final',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'candidate_id.required' => 'Candidate is required.',
            'candidate_id.exists' => 'Selected candidate does not exist.',
            'job_posting_id.required' => 'Job posting is required.',
            'job_posting_id.exists' => 'Selected job posting does not exist.',
            'interviewer_id.required' => 'Interviewer is required.',
            'interviewer_id.exists' => 'Selected interviewer does not exist.',
            'date.required' => 'Interview date is required.',
            'date.after_or_equal' => 'Interview date must be today or in the future.',
            'time.required' => 'Interview time is required.',
            'time.date_format' => 'Interview time must be in HH:MM format.',
            'duration.required' => 'Interview duration is required.',
            'duration.min' => 'Interview duration must be at least 15 minutes.',
            'duration.max' => 'Interview duration cannot exceed 8 hours.',
            'type.required' => 'Interview type is required.',
            'interview_type.required' => 'Interview category is required.',
            'meeting_link.url' => 'Meeting link must be a valid URL.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $this->merge([
            'status' => $this->status ?? 'scheduled',
            'round' => $this->round ?? 1,
            'duration' => $this->duration ?? 60,
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if interviewer is available at the requested time
            if ($this->filled(['interviewer_id', 'date', 'time', 'duration'])) {
                $interviewer = \App\Models\Interviewer::find($this->interviewer_id);
                if ($interviewer && !$interviewer->isAvailableOn($this->date, $this->time)) {
                    $validator->errors()->add('interviewer_id', 'Interviewer is not available at the requested time.');
                }
            }

            // Validate that candidate belongs to the job posting
            if ($this->filled(['candidate_id', 'job_posting_id'])) {
                $candidate = \App\Models\Candidate::find($this->candidate_id);
                if ($candidate && $candidate->job_posting_id != $this->job_posting_id) {
                    $validator->errors()->add('candidate_id', 'Candidate does not belong to the selected job posting.');
                }
            }
        });
    }
}
