<?php

namespace App\Http\Requests;

use App\Models\MessageTemplate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreMessageTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:message_templates,name',
            'subject' => 'required|string|max:500',
            'content' => 'required|string',
            'variables' => 'nullable|array',
            'variables.*' => 'string|max:100',
            'category' => [
                'required',
                'string',
                Rule::in([
                    MessageTemplate::CATEGORY_INTERVIEW,
                    MessageTemplate::CATEGORY_OFFER,
                    MessageTemplate::CATEGORY_FEEDBACK,
                    MessageTemplate::CATEGORY_REMINDER,
                    MessageTemplate::CATEGORY_REJECTION,
                    MessageTemplate::CATEGORY_WELCOME,
                ])
            ],
            'type' => [
                'required',
                'string',
                Rule::in([
                    MessageTemplate::TYPE_EMAIL,
                    MessageTemplate::TYPE_SMS,
                ])
            ],
            'language' => 'nullable|string|max:5|in:vi,en',
            'is_active' => 'boolean',
            'version' => 'nullable|integer|min:1',
            'parent_template_id' => 'nullable|exists:message_templates,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Tên template là bắt buộc.',
            'name.unique' => 'Tên template đã tồn tại.',
            'subject.required' => 'Tiêu đề là bắt buộc.',
            'content.required' => 'Nội dung là bắt buộc.',
            'category.required' => 'Danh mục là bắt buộc.',
            'category.in' => 'Danh mục không hợp lệ.',
            'type.required' => 'Loại template là bắt buộc.',
            'type.in' => 'Loại template không hợp lệ.',
            'language.in' => 'Ngôn ngữ không được hỗ trợ.',
            'parent_template_id.exists' => 'Template cha không tồn tại.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default language if not provided
        if (!$this->has('language')) {
            $this->merge(['language' => 'vi']);
        }

        // Set default active status if not provided
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }
    }
}
