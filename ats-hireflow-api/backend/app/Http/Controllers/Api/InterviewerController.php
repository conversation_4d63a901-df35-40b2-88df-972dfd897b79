<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreInterviewerRequest;
use App\Http\Requests\UpdateInterviewerRequest;
use App\Http\Resources\InterviewerResource;
use App\Models\Interviewer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class InterviewerController extends Controller
{

   
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = Interviewer::with(['user']);

        // Apply filters
        if ($request->has('department')) {
            $query->where('department', $request->department);
        }

        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->has('expertise')) {
            $query->whereJsonContains('expertise', $request->expertise);
        }

        $interviewers = $query->orderBy('department')
            ->orderBy('is_active', 'desc')
            ->get();

        return InterviewerResource::collection($interviewers);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreInterviewerRequest $request): JsonResponse
    {
        $interviewer = Interviewer::create($request->validated());

        return response()->json([
            'status' => 'success',
            'message' => 'Interviewer created successfully',
            'data' => new InterviewerResource($interviewer->load(['user'])),
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Interviewer $interviewer): JsonResponse
    {
        $interviewer->load(['user']);

        // Load additional relationships if requested
        if ($request->has('include_interviews')) {
            $interviewer->load(['interviews.candidate', 'interviews.jobPosting']);
        }

        return response()->json([
            'status' => 'success',
            'data' => new InterviewerResource($interviewer),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateInterviewerRequest $request, Interviewer $interviewer): JsonResponse
    {
        $interviewer->update($request->validated());

        return response()->json([
            'status' => 'success',
            'message' => 'Interviewer updated successfully',
            'data' => new InterviewerResource($interviewer->load(['user'])),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Interviewer $interviewer): JsonResponse
    {
        // Check if interviewer has scheduled interviews
        $scheduledInterviews = $interviewer->interviews()
            ->where('status', 'scheduled')
            ->where('date', '>=', now())
            ->count();

        if ($scheduledInterviews > 0) {
            return response()->json([
                'status' => 'error',
                'message' => "Cannot delete interviewer with {$scheduledInterviews} scheduled interviews.",
            ], 422);
        }

        $interviewer->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Interviewer deleted successfully',
        ]);
    }
}
