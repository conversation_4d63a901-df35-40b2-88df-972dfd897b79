<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreJobPostingRequest;
use App\Http\Requests\UpdateJobPostingRequest;
use App\Http\Resources\CandidateResource;
use App\Http\Resources\JobPostingResource;
use App\Models\JobPosting;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class JobPostingController extends Controller
{
    /**
     * Display a listing of job postings with filtering and pagination.
     */
    public function index(Request $request)
    {
        if (!auth()->user()->can('view_jobs')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $jobPostings = QueryBuilder::for(JobPosting::class)->whereNull('deleted_at')
            ->allowedFilters([
                'title',
                'department',
                'location',
                'type',
                'work_location',
                'status',
                'priority',
                'experience_level',
                AllowedFilter::exact('hiring_manager_id'),
                AllowedFilter::exact('recruiter_id'),
                AllowedFilter::exact('created_by'),
                AllowedFilter::scope('active'),
                AllowedFilter::callback('salary_min', function ($query, $value) {
                    $query->where('salary_min', '>=', $value);
                }),
                AllowedFilter::callback('salary_max', function ($query, $value) {
                    $query->where('salary_max', '<=', $value);
                }),
                AllowedFilter::callback('posted_date_from', function ($query, $value) {
                    $query->where('posted_date', '>=', $value);
                }),
                AllowedFilter::callback('posted_date_to', function ($query, $value) {
                    $query->where('posted_date', '<=', $value);
                }),
                AllowedFilter::callback('skills', function ($query, $value) {
                    $skills = is_array($value) ? $value : [$value];
                    $query->whereHas('skills', function ($q) use ($skills) {
                        $q->whereIn('skill_name', $skills);
                    });
                }),
            ])
            ->allowedSorts([
                'title',
                'department',
                'location',
                'type',
                'status',
                'priority',
                'posted_date',
                'closing_date',
                'applicant_count',
                'view_count',
                'created_at',
                'updated_at',
            ])
            ->allowedIncludes([
                'hiringManager',
                'recruiter',
                'recruiter_id',
                'createdBy',
                'candidates',
                'interviews',
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return JobPostingResource::collection($jobPostings);
    }

    /**
     * Store a newly created job posting.
     */
    public function store(StoreJobPostingRequest $request): JsonResponse
    {
        if (!auth()->user()->can('create_jobs')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            DB::beginTransaction();

            $jobPosting = JobPosting::create([
                ...$request->validated(),
                'created_by' => $request->user()->id,
            ]);

            // Create related records
            $this->syncJobPostingData($jobPosting, $request);

            DB::commit();

            $jobPosting->load([
                'hiringManager',
                'recruiter',
                'createdBy',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Job posting created successfully',
                'data' => new JobPostingResource($jobPosting),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create job posting',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified job posting.
     */
    public function show(Request $request, JobPosting $job): JsonResponse
    {
        if (!auth()->user()->can('view_jobs')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            // Use QueryBuilder to handle includes properly
            $jobPostingQuery = QueryBuilder::for(JobPosting::class)
                ->where('id', $job->id)
                ->allowedIncludes([
                    'hiringManager',
                    'recruiter',
                    'createdBy',
                    'candidates',
                    'candidates.assignedTo',
                    'interviews',
                    'interviews.interviewer.user',
                ]);

            $jobPosting = $jobPostingQuery->first();

            // If QueryBuilder didn't find it (shouldn't happen since route model binding worked), use the original
            if (!$jobPosting) {
                $jobPosting = $job;
            }

            // Increment view count
            $jobPosting->increment('view_count');

            // If no specific includes requested, load default relationships
            if (!$request->has('include')) {
                $jobPosting->load([
                    'hiringManager',
                    'recruiter',
                    'createdBy',
                ]);
            }

            return response()->json([
                'status' => 'success',
                'data' => new JobPostingResource($jobPosting),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve job posting',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update the specified job posting.
     */
    public function update(UpdateJobPostingRequest $request, JobPosting $job): JsonResponse
    {
        if (!auth()->user()->can('edit_jobs')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            DB::beginTransaction();

            $job->update($request->validated());

            // Update related records
            $this->syncJobPostingData($job, $request);

            DB::commit();

            $job->load([
                'hiringManager',
                'recruiter',
                'createdBy',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Job posting updated successfully',
                'data' => new JobPostingResource($job),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update job posting',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified job posting.
     */
    public function destroy($id): JsonResponse
    {
        if (!auth()->user()->can('delete_jobs')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            // Load job posting manually to debug
            $jobPosting = JobPosting::find($id);

            if (!$jobPosting) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Job posting not found',
                ], 404);
            }

            \Log::info('Attempting to delete job posting', ['id' => $jobPosting->id, 'title' => $jobPosting->title]);

            $result = $jobPosting->delete();

            \Log::info('Delete result', ['result' => $result, 'deleted_at' => $jobPosting->deleted_at]);

            if ($result) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Job posting deleted successfully',
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to delete job posting',
                ], 500);
            }


        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete job posting',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get candidates for a specific job posting.
     */
    public function candidates(Request $request, JobPosting $job)
    {
        if (!auth()->user()->can('view_jobs') && !auth()->user()->can('view_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $candidates = QueryBuilder::for($job->candidates())
            ->allowedFilters([
                'name',
                'email',
                'status',
                'source',
                'location',
                AllowedFilter::callback('rating_min', function ($query, $value) {
                    $query->where('rating', '>=', $value);
                }),
                AllowedFilter::callback('ai_score_min', function ($query, $value) {
                    $query->where('ai_score', '>=', $value);
                }),
                AllowedFilter::callback('applied_date_from', function ($query, $value) {
                    $query->where('applied_date', '>=', $value);
                }),
                AllowedFilter::callback('applied_date_to', function ($query, $value) {
                    $query->where('applied_date', '<=', $value);
                }),
            ])
            ->allowedSorts([
                'name',
                'status',
                'applied_date',
                'rating',
                'ai_score',
                'created_at',
            ])
            ->allowedIncludes([
                'assignedTo',
                'education',
                'workHistory',
                'skills',
                'tags',
                'interviews',
            ])
            ->defaultSort('-applied_date')
            ->paginate($request->get('per_page', 15));

        return CandidateResource::collection($candidates);
    }

    /**
     * Get analytics for a specific job posting.
     */
    public function analytics(JobPosting $job): JsonResponse
    {
        if (!auth()->user()->can('view_job_analytics')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $analytics = [
                'job_info' => [
                    'id' => $job->id,
                    'title' => $job->title,
                    'department' => $job->department,
                    'status' => $job->status,
                    'posted_date' => $job->posted_date?->format('Y-m-d'),
                    'closing_date' => $job->closing_date?->format('Y-m-d'),
                ],

                'applicant_statistics' => [
                    'total_applicants' => $job->candidates()->count(),
                    'new_this_week' => $job->candidates()
                        ->where('applied_date', '>=', now()->startOfWeek())
                        ->count(),
                    'new_this_month' => $job->candidates()
                        ->where('applied_date', '>=', now()->startOfMonth())
                        ->count(),
                ],

                'status_breakdown' => $job->candidates()
                    ->selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status'),

                'source_effectiveness' => $job->candidates()
                    ->selectRaw('source, COUNT(*) as count, AVG(rating) as avg_rating, AVG(ai_score) as avg_ai_score')
                    ->whereNotNull('source')
                    ->groupBy('source')
                    ->get()
                    ->map(function ($item) {
                        return [
                            'source' => $item->source,
                            'count' => $item->count,
                            'avg_rating' => round($item->avg_rating, 2),
                            'avg_ai_score' => round($item->avg_ai_score, 2),
                        ];
                    }),

                'conversion_rates' => [
                    'applied_to_screening' => $this->calculateConversionRate($job, 'applied', 'screening'),
                    'screening_to_interview' => $this->calculateConversionRate($job, 'screening', 'interview'),
                    'interview_to_offer' => $this->calculateConversionRate($job, 'interview', 'offer'),
                    'offer_to_hired' => $this->calculateConversionRate($job, 'offer', 'hired'),
                    'overall_conversion' => $this->calculateConversionRate($job, 'applied', 'hired'),
                ],

                'quality_metrics' => [
                    'avg_rating' => round($job->candidates()->avg('rating'), 2),
                    'avg_ai_score' => round($job->candidates()->avg('ai_score'), 2),
                    'high_quality_candidates' => $job->candidates()
                        ->where('rating', '>=', 4.0)
                        ->where('ai_score', '>=', 80)
                        ->count(),
                ],

                'interview_statistics' => [
                    'total_interviews' => $job->interviews()->count(),
                    'scheduled' => $job->interviews()->where('status', 'scheduled')->count(),
                    'completed' => $job->interviews()->where('status', 'completed')->count(),
                    'cancelled' => $job->interviews()->where('status', 'cancelled')->count(),
                    'avg_feedback_rating' => round($job->interviews()
                        ->whereHas('feedback')
                        ->with('feedback')
                        ->get()
                        ->pluck('feedback.rating')
                        ->avg(), 2),
                ],

                'time_metrics' => [
                    'avg_time_to_first_interview' => $this->calculateAvgTimeToFirstInterview($job),
                    'avg_time_to_hire' => $this->calculateAvgTimeToHire($job),
                    'days_since_posted' => $job->posted_date ?
                        $job->posted_date->diffInDays(now()) : null,
                ],

                'view_statistics' => [
                    'total_views' => $job->view_count,
                    'views_to_applications_ratio' => $job->view_count > 0 ?
                        round(($job->applicant_count / $job->view_count) * 100, 2) : 0,
                ],
            ];

            return response()->json([
                'status' => 'success',
                'data' => $analytics,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update job posting status.
     */
    public function updateStatus(Request $request, JobPosting $job): JsonResponse
    {
        if (!auth()->user()->can('manage_job_status')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'status' => 'required|in:draft,active,paused,closed',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $oldStatus = $job->status;

            $job->update([
                'status' => $request->status,
                'posted_date' => $request->status === 'active' && !$job->posted_date ?
                    now() : $job->posted_date,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Job posting status updated successfully',
                'data' => [
                    'old_status' => $oldStatus,
                    'new_status' => $job->status,
                    'updated_at' => $job->updated_at->format('Y-m-d H:i:s'),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update job posting status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bulk operations for job postings.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,pause,close,delete',
            'job_ids' => 'required|array|min:1',
            'job_ids.*' => 'exists:job_postings,id',
        ]);

        try {
            $jobPostings = JobPosting::whereIn('id', $request->job_ids)->get();

            // Check authorization for each job posting
            foreach ($jobPostings as $jobPosting) {
                if (!$request->user()->canManageJobPosting($jobPosting)) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Unauthorized to perform action on job posting: ' . $jobPosting->title,
                    ], 403);
                }
            }

            $updatedCount = 0;
            $statusMap = [
                'activate' => 'active',
                'deactivate' => 'draft',
                'pause' => 'paused',
                'close' => 'closed',
            ];

            DB::beginTransaction();

            foreach ($jobPostings as $jobPosting) {
                if ($request->action === 'delete') {
                    $jobPosting->delete();
                    $updatedCount++;
                } else {
                    $jobPosting->update([
                        'status' => $statusMap[$request->action],
                        'posted_date' => $request->action === 'activate' && !$jobPosting->posted_date ?
                            now() : $jobPosting->posted_date,
                    ]);
                    $updatedCount++;
                }
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => "Successfully {$request->action}d {$updatedCount} job posting(s)",
                'data' => [
                    'action' => $request->action,
                    'updated_count' => $updatedCount,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to perform bulk action',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Sync job posting related data (requirements, responsibilities, benefits, skills).
     */
    private function syncJobPostingData(JobPosting $jobPosting, Request $request): void
    {
        // Sync requirements - now stored as JSON array
        if ($request->has('requirements')) {
            $requirements = is_array($request->requirements) ? $request->requirements : [];
            // Clean and validate requirements
            $requirements = array_values(array_unique(array_filter($requirements, 'trim')));
            $jobPosting->requirements = $requirements;
        }

        // Sync responsibilities - now stored as JSON array
        if ($request->has('responsibilities')) {
            $responsibilities = is_array($request->responsibilities) ? $request->responsibilities : [];
            // Clean and validate responsibilities
            $responsibilities = array_values(array_unique(array_filter($responsibilities, 'trim')));
            $jobPosting->responsibilities = $responsibilities;
        }

        // Sync benefits - now stored as JSON array
        if ($request->has('benefits')) {
            $benefits = is_array($request->benefits) ? $request->benefits : [];
            // Clean and validate benefits
            $benefits = array_values(array_unique(array_filter($benefits, 'trim')));
            $jobPosting->benefits = $benefits;
        }

        // Sync skills - now stored as JSON array
        if ($request->has('skills')) {
            $skills = is_array($request->skills) ? $request->skills : [];
            // Clean and validate skills
            $skills = array_values(array_unique(array_filter($skills, 'trim')));
            $jobPosting->skills = $skills;
        }
    }

    /**
     * Calculate conversion rate between two statuses.
     */
    private function calculateConversionRate(JobPosting $jobPosting, string $fromStatus, string $toStatus): float
    {
        $fromCount = $jobPosting->candidates()->where('status', $fromStatus)->count();
        if ($fromCount === 0) return 0;

        $toCount = $jobPosting->candidates()->where('status', $toStatus)->count();
        return round(($toCount / $fromCount) * 100, 2);
    }

    /**
     * Calculate average time to first interview.
     */
    private function calculateAvgTimeToFirstInterview(JobPosting $jobPosting): ?float
    {
        $candidates = $jobPosting->candidates()
            ->whereHas('interviews')
            ->with(['interviews' => function ($query) {
                $query->orderBy('date');
            }])
            ->get();

        if ($candidates->isEmpty()) return null;

        $totalDays = 0;
        $count = 0;

        foreach ($candidates as $candidate) {
            $firstInterview = $candidate->interviews->first();
            if ($firstInterview) {
                $days = $candidate->applied_date->diffInDays($firstInterview->date);
                $totalDays += $days;
                $count++;
            }
        }

        return $count > 0 ? round($totalDays / $count, 1) : null;
    }

    /**
     * Calculate average time to hire.
     */
    private function calculateAvgTimeToHire(JobPosting $jobPosting): ?float
    {
        $hiredCandidates = $jobPosting->candidates()
            ->where('status', 'hired')
            ->get();

        if ($hiredCandidates->isEmpty()) return null;

        $totalDays = 0;
        foreach ($hiredCandidates as $candidate) {
            $hiredDate = $candidate->statusHistory()
                ->where('new_status', 'hired')
                ->first()?->created_at ?? $candidate->updated_at;

            $days = $candidate->applied_date->diffInDays($hiredDate);
            $totalDays += $days;
        }

        return round($totalDays / $hiredCandidates->count(), 1);
    }

    /**
     * Get list of hiring managers for job posting forms.
     */
    public function getHiringManagers(Request $request): JsonResponse
    {
        // Check permissions
        if (!auth()->user()->can('view_users') && !auth()->user()->can('view_jobs')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = User::whereHas('roles', function ($q) {
            $q->where('name', 'hiring_manager');
        })->where('is_active', true);

        // Filter by department if provided
        if ($request->has('department') && $request->department) {
            $query->where('department', $request->department);
        }

        $hiringManagers = $query->select(['id', 'name', 'email', 'department', 'title'])
            ->orderBy('name')
            ->get();

        return response()->json([
            'status' => 'success',
            'message' => 'Hiring managers retrieved successfully',
            'data' => $hiringManagers,
        ]);
    }

    /**
     * Get list of recruiters for job posting forms.
     */
    public function getRecruiters(Request $request): JsonResponse
    {
        // Check permissions
        if (!auth()->user()->can('view_users') && !auth()->user()->can('view_jobs')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = User::whereHas('roles', function ($q) {
            $q->where('name', 'recruiter');
        })->where('is_active', true);

        // Filter by department if provided
        if ($request->has('department') && $request->department) {
            $query->where('department', $request->department);
        }

        $recruiters = $query->select(['id', 'name', 'email', 'department', 'title'])
            ->orderBy('name')
            ->get();

        return response()->json([
            'status' => 'success',
            'message' => 'Recruiters retrieved successfully',
            'data' => $recruiters,
        ]);
    }
}
