<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use App\Models\User;
use App\Services\FileUploadService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FileUploadController extends Controller
{
    protected FileUploadService $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Upload candidate resume.
     */
    public function uploadResume(Request $request, Candidate $candidate): JsonResponse
    {
        $this->authorize('update', $candidate);

        $request->validate([
            'resume' => 'required|file|mimes:pdf,doc,docx|max:10240', // 10MB max
        ]);

        try {
            DB::beginTransaction();

            // Delete old resume if exists
            if ($candidate->resume_url) {
                $this->fileUploadService->deleteFile($candidate->resume_url);
            }

            // Upload new resume
            $uploadResult = $this->fileUploadService->uploadResume(
                $request->file('resume'),
                (string) $candidate->id
            );

            // Update candidate record
            $candidate->update([
                'resume_url' => $uploadResult['path'],
            ]);

            // Parse resume content for auto-population (simplified)
            $parsedData = $this->parseResumeContent($uploadResult['text_content']);
            if (!empty($parsedData)) {
                $candidate->update($parsedData);
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Resume uploaded successfully',
                'data' => [
                    'resume_url' => $uploadResult['url'],
                    'file_info' => [
                        'original_name' => $uploadResult['original_name'],
                        'size' => $uploadResult['size'],
                        'mime_type' => $uploadResult['mime_type'],
                    ],
                    'parsed_data' => $parsedData ?? null,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to upload resume',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload user avatar.
     */
    public function uploadAvatar(Request $request, User $user): JsonResponse
    {
        $this->authorize('update', $user);

        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,gif,webp|max:5120', // 5MB max
        ]);

        try {
            DB::beginTransaction();

            // Delete old avatar if exists
            if ($user->avatar) {
                $this->fileUploadService->deleteFile($user->avatar);
            }

            // Upload new avatar
            $uploadResult = $this->fileUploadService->uploadAvatar(
                $request->file('avatar'),
                (string) $user->id
            );

            // Update user record
            $user->update([
                'avatar' => $uploadResult['path'],
            ]);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Avatar uploaded successfully',
                'data' => [
                    'avatar_url' => $uploadResult['url'],
                    'thumbnail_url' => $uploadResult['thumbnail_url'],
                    'file_info' => [
                        'original_name' => $uploadResult['original_name'],
                        'size' => $uploadResult['size'],
                        'mime_type' => $uploadResult['mime_type'],
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to upload avatar',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload general document.
     */
    public function uploadDocument(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'required|file|max:20480', // 20MB max
            'type' => 'required|string|in:contract,certificate,portfolio,other',
            'entity_type' => 'required|string|in:candidate,job,interview,user',
            'entity_id' => 'required|integer',
            'description' => 'nullable|string|max:500',
        ]);

        try {
            // Upload document
            $uploadResult = $this->fileUploadService->uploadDocument(
                $request->file('file'),
                $request->type,
                (string) $request->entity_id
            );

            // Here you could store document metadata in a documents table
            // For now, we'll just return the upload result

            return response()->json([
                'status' => 'success',
                'message' => 'Document uploaded successfully',
                'data' => [
                    'document_url' => $uploadResult['url'],
                    'file_info' => [
                        'original_name' => $uploadResult['original_name'],
                        'size' => $uploadResult['size'],
                        'mime_type' => $uploadResult['mime_type'],
                        'path' => $uploadResult['path'],
                    ],
                    'metadata' => [
                        'type' => $request->type,
                        'entity_type' => $request->entity_type,
                        'entity_id' => $request->entity_id,
                        'description' => $request->description,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to upload document',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload multiple files (bulk upload).
     */
    public function uploadBulkFiles(Request $request): JsonResponse
    {
        $request->validate([
            'files' => 'required|array|min:1|max:10',
            'files.*' => 'file|max:20480', // 20MB max per file
            'type' => 'required|string|in:resume,document,image,other',
            'entity_type' => 'required|string|in:candidate,job,interview,user',
            'entity_id' => 'required|integer',
        ]);

        try {
            $uploadResults = $this->fileUploadService->uploadBulkFiles(
                $request->file('files'),
                $request->type,
                (string) $request->entity_id
            );

            $successCount = count(array_filter($uploadResults, fn($result) => $result['status'] === 'success'));
            $errorCount = count($uploadResults) - $successCount;

            return response()->json([
                'status' => 'success',
                'message' => "Uploaded {$successCount} files successfully" . ($errorCount > 0 ? ", {$errorCount} failed" : ""),
                'data' => [
                    'results' => $uploadResults,
                    'summary' => [
                        'total_files' => count($uploadResults),
                        'successful_uploads' => $successCount,
                        'failed_uploads' => $errorCount,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to upload files',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete uploaded file.
     */
    public function deleteFile(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
            'entity_type' => 'required|string|in:candidate,job,interview,user',
            'entity_id' => 'required|integer',
        ]);

        try {
            // Verify user has permission to delete this file
            // This is a simplified check - in production you'd have more robust authorization

            $deleted = $this->fileUploadService->deleteFile($request->file_path);

            if ($deleted) {
                // Update related model if it's a resume or avatar
                if (str_contains($request->file_path, 'resume')) {
                    $candidate = Candidate::find($request->entity_id);
                    if ($candidate && $candidate->resume_url === $request->file_path) {
                        $candidate->update(['resume_url' => null]);
                    }
                } elseif (str_contains($request->file_path, 'avatar')) {
                    $user = User::find($request->entity_id);
                    if ($user && $user->avatar === $request->file_path) {
                        $user->update(['avatar' => null]);
                    }
                }

                return response()->json([
                    'status' => 'success',
                    'message' => 'File deleted successfully',
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to delete file',
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete file',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get file information.
     */
    public function getFileInfo(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        try {
            $fileInfo = $this->fileUploadService->getFileInfo($request->file_path);

            if ($fileInfo) {
                return response()->json([
                    'status' => 'success',
                    'data' => $fileInfo,
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'File not found',
                ], 404);
            }

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get file information',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate secure download URL.
     */
    public function generateDownloadUrl(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
            'expiration_minutes' => 'nullable|integer|min:1|max:1440', // Max 24 hours
        ]);

        try {
            $expirationMinutes = $request->get('expiration_minutes', 60);
            $secureUrl = $this->fileUploadService->generateSecureUrl(
                $request->file_path,
                $expirationMinutes
            );

            return response()->json([
                'status' => 'success',
                'data' => [
                    'download_url' => $secureUrl,
                    'expires_in_minutes' => $expirationMinutes,
                    'expires_at' => now()->addMinutes($expirationMinutes)->format('Y-m-d H:i:s'),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate download URL',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Parse resume content for auto-population.
     */
    private function parseResumeContent(?string $textContent): array
    {
        if (!$textContent) {
            return [];
        }

        $parsedData = [];

        // Simple email extraction
        if (preg_match('/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/', $textContent, $matches)) {
            $parsedData['email'] = $matches[0];
        }

        // Simple phone extraction
        if (preg_match('/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/', $textContent, $matches)) {
            $parsedData['phone'] = $matches[0];
        }

        // Simple skills extraction (this would be much more sophisticated in production)
        $commonSkills = ['PHP', 'Laravel', 'JavaScript', 'React', 'Vue', 'Node.js', 'Python', 'Java', 'C++', 'SQL', 'MySQL', 'PostgreSQL'];
        $foundSkills = [];

        foreach ($commonSkills as $skill) {
            if (stripos($textContent, $skill) !== false) {
                $foundSkills[] = $skill;
            }
        }

        if (!empty($foundSkills)) {
            $parsedData['extracted_skills'] = $foundSkills;
        }

        return $parsedData;
    }
}
