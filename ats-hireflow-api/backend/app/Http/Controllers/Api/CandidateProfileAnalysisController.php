<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ExtractResumeRequest;
use App\Http\Requests\GenerateAnalysisRequest;
use App\Http\Resources\CandidateProfileAnalysisResource;
use App\Models\Candidate;
use App\Models\CandidateProfileAnalysis;
use App\Services\CandidateProfileAnalysisService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class CandidateProfileAnalysisController extends Controller
{
    protected CandidateProfileAnalysisService $analysisService;

    public function __construct(CandidateProfileAnalysisService $analysisService)
    {
        $this->analysisService = $analysisService;
    }

    /**
     * Extract information from candidate's resume
     */
    public function extractResume(ExtractResumeRequest $request): JsonResponse
    {
        try {
            $candidateId = $request->validated()['candidate_id'];
            $candidate = Candidate::findOrFail($candidateId);

            // Check if candidate has a resume URL
            if (empty($candidate->resume_url)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Candidate does not have a resume URL to extract from',
                ], 400);
            }

            // Check if extraction is already in progress
            $existingAnalysis = CandidateProfileAnalysis::where('candidate_id', $candidateId)
                ->where('analysis_type', 'resume_extraction')
                ->whereIn('status', ['pending', 'processing'])
                ->first();

            if ($existingAnalysis) {
                return response()->json([
                    'status' => 'info',
                    'message' => 'Resume extraction is already in progress for this candidate',
                    'data' => new CandidateProfileAnalysisResource($existingAnalysis),
                ], 200);
            }

            // Start resume extraction
            $analysis = $this->analysisService->extractResumeInformation(
                $candidateId,
                auth()->id()
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Resume extraction completed successfully',
                'data' => new CandidateProfileAnalysisResource($analysis->load(['candidate', 'createdBy'])),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to extract resume information',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate AI analysis for candidate
     */
    public function generateAnalysis(GenerateAnalysisRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            $candidateId = $validated['candidate_id'];
            $jobPostingId = $validated['job_posting_id'] ?? null;

            // Check if analysis is already in progress
            $analysisType = $jobPostingId ? 'job_matching' : 'ai_analysis';
            $existingAnalysis = CandidateProfileAnalysis::where('candidate_id', $candidateId)
                ->where('analysis_type', $analysisType)
                ->when($jobPostingId, function ($query) use ($jobPostingId) {
                    return $query->where('job_posting_id', $jobPostingId);
                })
                ->whereIn('status', ['pending', 'processing'])
                ->first();

            if ($existingAnalysis) {
                return response()->json([
                    'status' => 'info',
                    'message' => 'Analysis is already in progress for this candidate',
                    'data' => new CandidateProfileAnalysisResource($existingAnalysis),
                ], 200);
            }

            // Generate analysis
            $analysis = $this->analysisService->generateAIAnalysis(
                $candidateId,
                $jobPostingId,
                auth()->id()
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate analysis completed successfully',
                'data' => new CandidateProfileAnalysisResource($analysis->load(['candidate', 'jobPosting', 'createdBy'])),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate candidate analysis',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get analysis results with filtering
     */
    public function getAnalyses(Request $request): AnonymousResourceCollection
    {
        // Transform standard query parameters to Spatie QueryBuilder format
        $this->transformQueryParameters($request);

        $analyses = QueryBuilder::for(CandidateProfileAnalysis::class)
            ->allowedFilters([
                AllowedFilter::exact('candidate_id'),
                AllowedFilter::exact('job_posting_id'),
                AllowedFilter::exact('analysis_type'),
                AllowedFilter::exact('status'),
                AllowedFilter::callback('score_min', function ($query, $value) {
                    $query->where('overall_score', '>=', $value);
                }),
                AllowedFilter::callback('score_max', function ($query, $value) {
                    $query->where('overall_score', '<=', $value);
                }),
                AllowedFilter::callback('date_from', function ($query, $value) {
                    $query->where('created_at', '>=', $value);
                }),
                AllowedFilter::callback('date_to', function ($query, $value) {
                    $query->where('created_at', '<=', $value);
                }),
            ])
            ->allowedSorts([
                'created_at',
                'analysis_completed_at',
                'overall_score',
                'skills_score',
                'experience_score',
            ])
            ->allowedIncludes([
                'candidate',
                'jobPosting',
                'createdBy',
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return CandidateProfileAnalysisResource::collection($analyses);
    }

    /**
     * Transform standard query parameters to Spatie QueryBuilder format
     */
    private function transformQueryParameters(Request $request): void
    {
        $standardParams = [
            'candidate_id',
            'job_posting_id',
            'analysis_type',
            'status',
            'score_min',
            'score_max',
            'date_from',
            'date_to'
        ];

        $filters = $request->get('filter', []);

        foreach ($standardParams as $param) {
            if ($request->has($param) && !isset($filters[$param])) {
                $filters[$param] = $request->get($param);
            }
        }

        if (!empty($filters)) {
            $request->merge(['filter' => $filters]);
        }
    }

    /**
     * Get specific analysis result
     */
    public function getAnalysis(CandidateProfileAnalysis $analysis): JsonResponse
    {


        $analysis->load(['candidate', 'jobPosting', 'createdBy']);

        return response()->json([
            'status' => 'success',
            'data' => new CandidateProfileAnalysisResource($analysis),
        ]);
    }

    /**
     * Get candidate profile summary with all analyses
     */
    public function getCandidateSummary(Candidate $candidate): JsonResponse
    {
        try {
            // Get latest analyses for this candidate
            $latestAnalyses = CandidateProfileAnalysis::where('candidate_id', $candidate->id)
                ->where('status', 'completed')
                ->with(['jobPosting:id,title,department'])
                ->latest()
                ->limit(5)
                ->get();

            // Calculate aggregate scores
            $aggregateScores = [
                'overall_average' => 0,
                'skills_average' => 0,
                'experience_average' => 0,
                'education_average' => 0,
                'cultural_fit_average' => 0,
                'total_analyses' => $latestAnalyses->count(),
            ];

            if ($latestAnalyses->count() > 0) {
                $aggregateScores['overall_average'] = round($latestAnalyses->avg('overall_score'), 1);
                $aggregateScores['skills_average'] = round($latestAnalyses->avg('skills_score'), 1);
                $aggregateScores['experience_average'] = round($latestAnalyses->avg('experience_score'), 1);
                $aggregateScores['education_average'] = round($latestAnalyses->avg('education_score'), 1);
                $aggregateScores['cultural_fit_average'] = round($latestAnalyses->avg('cultural_fit_score'), 1);
            }

            // Get most recent extraction data
            $latestExtraction = CandidateProfileAnalysis::where('candidate_id', $candidate->id)
                ->where('analysis_type', 'resume_extraction')
                ->where('status', 'completed')
                ->latest()
                ->first();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'candidate' => [
                        'id' => $candidate->id,
                        'name' => $candidate->name,
                        'email' => $candidate->email,
                        'position' => $candidate->position,
                        'rating' => $candidate->rating,
                        'ai_score' => $candidate->ai_score,
                    ],
                    'extracted_information' => $latestExtraction ? [
                        'name' => $latestExtraction->extracted_name,
                        'email' => $latestExtraction->extracted_email,
                        'phone' => $latestExtraction->extracted_phone,
                        'address' => $latestExtraction->extracted_address,
                        'skills' => $latestExtraction->extracted_skills,
                        'experience' => $latestExtraction->extracted_experience,
                        'education' => $latestExtraction->extracted_education,
                    ] : null,
                    'aggregate_scores' => $aggregateScores,
                    'recent_analyses' => CandidateProfileAnalysisResource::collection($latestAnalyses),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve candidate summary',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update candidate information from extracted data
     */
    public function updateCandidateFromExtraction(Request $request): JsonResponse
    {
        $request->validate([
            'candidate_id' => 'required|exists:candidates,id',
            'analysis_id' => 'required|exists:candidate_profile_analyses,id',
            'fields' => 'required|array',
            'fields.*' => 'in:name,email,phone,address,skills,experience,education',
        ]);

        try {
            $candidate = Candidate::findOrFail($request->candidate_id);
            $analysis = CandidateProfileAnalysis::findOrFail($request->analysis_id);

            // Verify the analysis belongs to the candidate and is completed
            if ($analysis->candidate_id !== $candidate->id || $analysis->status !== 'completed') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid analysis or analysis not completed',
                ], 400);
            }

            $updateData = [];
            $fieldsToUpdate = $request->fields;

            // Map extracted data to candidate fields
            foreach ($fieldsToUpdate as $field) {
                switch ($field) {
                    case 'name':
                        if ($analysis->extracted_name) {
                            $updateData['name'] = $analysis->extracted_name;
                        }
                        break;
                    case 'email':
                        if ($analysis->extracted_email) {
                            $updateData['email'] = $analysis->extracted_email;
                        }
                        break;
                    case 'phone':
                        if ($analysis->extracted_phone) {
                            $updateData['phone'] = $analysis->extracted_phone;
                        }
                        break;
                    case 'address':
                        if ($analysis->extracted_address) {
                            $updateData['location'] = $analysis->extracted_address;
                        }
                        break;
                    case 'skills':
                        if ($analysis->extracted_skills) {
                            $updateData['skills'] = $analysis->extracted_skills;
                        }
                        break;
                    case 'experience':
                        if ($analysis->extracted_experience) {
                            // Calculate total years of experience
                            $totalYears = 0;
                            foreach ($analysis->extracted_experience as $exp) {
                                if (isset($exp['duration'])) {
                                    // Simple calculation - can be improved
                                    $totalYears += 2; // Default 2 years per position
                                }
                            }
                            $updateData['experience'] = $totalYears . ' years';
                        }
                        break;
                    case 'education':
                        if ($analysis->extracted_education) {
                            // Store education data in a JSON field if available
                            $updateData['education'] = $analysis->extracted_education;
                        }
                        break;
                }
            }

            if (!empty($updateData)) {
                $candidate->update($updateData);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate information updated successfully',
                'data' => [
                    'updated_fields' => array_keys($updateData),
                    'candidate' => $candidate->fresh(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update candidate information',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
