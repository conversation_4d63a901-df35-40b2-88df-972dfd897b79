<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Requests\UpdateUserRoleRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class UserController extends Controller
{
    /**
     * Display a listing of users with filtering and pagination.
     * Only accessible by admin users.
     */
    public function index(Request $request): JsonResponse
    {
        // Check admin permission
        if (!auth()->user()->hasRole('admin')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Chỉ admin mới có quyền xem danh sách người dùng',
            ], 403);
        }

        try {
            $users = QueryBuilder::for(User::class)
                ->allowedFilters([
                    AllowedFilter::exact('role'),
                    AllowedFilter::exact('department'),
                    AllowedFilter::exact('is_active'),
                    AllowedFilter::partial('name'),
                    AllowedFilter::partial('email'),
                ])
                ->allowedSorts(['name', 'email', 'role', 'department', 'created_at', 'last_login_at'])
                ->allowedIncludes(['roles', 'permissions'])
                ->with(['roles', 'permissions'])
                ->paginate($request->get('per_page', 15));

            return response()->json([
                'status' => 'success',
                'data' => UserResource::collection($users->items()),
                'meta' => [
                    'current_page' => $users->currentPage(),
                    'last_page' => $users->lastPage(),
                    'per_page' => $users->perPage(),
                    'total' => $users->total(),
                    'from' => $users->firstItem(),
                    'to' => $users->lastItem(),
                ],
                'links' => [
                    'first' => $users->url(1),
                    'last' => $users->url($users->lastPage()),
                    'prev' => $users->previousPageUrl(),
                    'next' => $users->nextPageUrl(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching users list', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Không thể lấy danh sách người dùng',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Store a newly created user.
     * Only accessible by admin users.
     */
    public function store(StoreUserRequest $request): JsonResponse
    {
        // Check admin permission
        if (!auth()->user()->hasRole('admin')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Chỉ admin mới có quyền tạo người dùng mới',
            ], 403);
        }

        try {
            DB::beginTransaction();

            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'department' => $request->department,
                'title' => $request->title,
                'phone' => $request->phone,
                'is_active' => $request->get('is_active', true),
            ]);

            // Assign role
            $user->assignRole($request->role);

            // Log activity
            Log::info('New user created by admin', [
                'created_user_id' => $user->id,
                'created_user_email' => $user->email,
                'created_by' => auth()->id(),
                'assigned_role' => $request->role,
            ]);

            DB::commit();

            $user->load(['roles', 'permissions']);

            return response()->json([
                'status' => 'success',
                'message' => 'Người dùng đã được tạo thành công',
                'data' => new UserResource($user),
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Error creating user', [
                'error' => $e->getMessage(),
                'request_data' => $request->except(['password']),
                'created_by' => auth()->id(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Không thể tạo người dùng mới',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified user.
     */
    public function show(User $user): JsonResponse
    {
        // Check permission - admin can view all, others can only view themselves
        if (!auth()->user()->hasRole('admin') && auth()->id() !== $user->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Bạn không có quyền xem thông tin người dùng này',
            ], 403);
        }

        $user->load(['roles', 'permissions']);

        return response()->json([
            'status' => 'success',
            'data' => new UserResource($user),
        ]);
    }

    /**
     * Update the specified user.
     */
    public function update(UpdateUserRequest $request, User $user): JsonResponse
    {
        // Check permission - admin can update all, others can only update themselves (limited fields)
        if (!auth()->user()->hasRole('admin') && auth()->id() !== $user->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Bạn không có quyền cập nhật thông tin người dùng này',
            ], 403);
        }

        try {
            DB::beginTransaction();

            $updateData = [];

            // Fields that users can update themselves
            if ($request->has('name')) $updateData['name'] = $request->name;
            if ($request->has('phone')) $updateData['phone'] = $request->phone;
            if ($request->has('password') && $request->password) {
                $updateData['password'] = Hash::make($request->password);
            }

            // Fields only admin can update
            if (auth()->user()->hasRole('admin')) {
                if ($request->has('email')) $updateData['email'] = $request->email;
                if ($request->has('role')) $updateData['role'] = $request->role;
                if ($request->has('department')) $updateData['department'] = $request->department;
                if ($request->has('title')) $updateData['title'] = $request->title;
                if ($request->has('is_active')) $updateData['is_active'] = $request->is_active;

                // Update role if changed
                if ($request->has('role') && $request->role !== $user->role) {
                    $user->syncRoles([$request->role]);
                }
            }

            $user->update($updateData);

            Log::info('User updated', [
                'updated_user_id' => $user->id,
                'updated_by' => auth()->id(),
                'updated_fields' => array_keys($updateData),
            ]);

            DB::commit();

            $user->load(['roles', 'permissions']);

            return response()->json([
                'status' => 'success',
                'message' => 'Thông tin người dùng đã được cập nhật',
                'data' => new UserResource($user),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Error updating user', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'updated_by' => auth()->id(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Không thể cập nhật thông tin người dùng',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified user from storage.
     * Only accessible by admin users.
     */
    public function destroy(User $user): JsonResponse
    {
        // Check admin permission
        if (!auth()->user()->hasRole('admin')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Chỉ admin mới có quyền xóa người dùng',
            ], 403);
        }

        // Prevent admin from deleting themselves
        if (auth()->id() === $user->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Bạn không thể xóa tài khoản của chính mình',
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Soft delete or deactivate instead of hard delete to preserve data integrity
            $user->update(['is_active' => false]);

            Log::info('User deactivated by admin', [
                'deactivated_user_id' => $user->id,
                'deactivated_user_email' => $user->email,
                'deactivated_by' => auth()->id(),
            ]);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Người dùng đã được vô hiệu hóa',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Error deactivating user', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'deactivated_by' => auth()->id(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Không thể vô hiệu hóa người dùng',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update user roles.
     * Only accessible by admin users.
     */
    public function updateRoles(UpdateUserRoleRequest $request, User $user): JsonResponse
    {
        // Check admin permission
        if (!auth()->user()->hasRole('admin')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Chỉ admin mới có quyền cập nhật quyền người dùng',
            ], 403);
        }

        // Prevent admin from changing their own role
        if (auth()->id() === $user->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Bạn không thể thay đổi quyền của chính mình',
            ], 422);
        }

        try {
            DB::beginTransaction();

            $oldRoles = $user->getRoleNames()->toArray();

            // Update role in users table
            $user->update(['role' => $request->role]);

            // Sync roles using Spatie Permission
            $user->syncRoles([$request->role]);

            Log::info('User roles updated by admin', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'old_roles' => $oldRoles,
                'new_role' => $request->role,
                'updated_by' => auth()->id(),
            ]);

            DB::commit();

            $user->load(['roles', 'permissions']);

            return response()->json([
                'status' => 'success',
                'message' => 'Quyền người dùng đã được cập nhật thành công',
                'data' => new UserResource($user),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Error updating user roles', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'new_role' => $request->role,
                'updated_by' => auth()->id(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Không thể cập nhật quyền người dùng',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available roles for user assignment.
     * Only accessible by admin users.
     */
    public function getRoles(): JsonResponse
    {
        // Check admin permission
        if (!auth()->user()->hasRole('admin')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Chỉ admin mới có quyền xem danh sách quyền',
            ], 403);
        }

        $roles = Role::all(['id', 'name'])->map(function ($role) {
            return [
                'id' => $role->id,
                'name' => $role->name,
                'display_name' => $this->getRoleDisplayName($role->name),
            ];
        });

        return response()->json([
            'status' => 'success',
            'data' => $roles,
        ]);
    }

    /**
     * Get user statistics for admin dashboard.
     * Only accessible by admin users.
     */
    public function getStatistics(): JsonResponse
    {
        // Check admin permission
        if (!auth()->user()->hasRole('admin')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Chỉ admin mới có quyền xem thống kê người dùng',
            ], 403);
        }

        try {
            $stats = [
                'total_users' => User::count(),
                'active_users' => User::where('is_active', true)->count(),
                'inactive_users' => User::where('is_active', false)->count(),
                'by_role' => [
                    'admin' => User::where('role', 'admin')->count(),
                    'recruiter' => User::where('role', 'recruiter')->count(),
                    'hiring_manager' => User::where('role', 'hiring_manager')->count(),
                    'interviewer' => User::where('role', 'interviewer')->count(),
                ],
                'recent_logins' => User::whereNotNull('last_login_at')
                    ->where('last_login_at', '>=', now()->subDays(7))
                    ->count(),
                'never_logged_in' => User::whereNull('last_login_at')->count(),
            ];

            return response()->json([
                'status' => 'success',
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching user statistics', [
                'error' => $e->getMessage(),
                'requested_by' => auth()->id(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Không thể lấy thống kê người dùng',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get role display name in Vietnamese.
     */
    private function getRoleDisplayName(string $role): string
    {
        $roleNames = [
            'admin' => 'Quản trị viên',
            'recruiter' => 'Nhà tuyển dụng',
            'hiring_manager' => 'Quản lý tuyển dụng',
            'interviewer' => 'Người phỏng vấn',
        ];

        return $roleNames[$role] ?? $role;
    }
}
