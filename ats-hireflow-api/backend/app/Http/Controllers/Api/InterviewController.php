<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreInterviewRequest;
use App\Http\Requests\UpdateInterviewRequest;
use App\Http\Resources\InterviewResource;
use App\Models\Interview;
use App\Models\Interviewer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class InterviewController extends Controller
{
    /**
     * Display a listing of interviews with filtering and pagination.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $interviews = QueryBuilder::for(Interview::class)
            ->allowedFilters([
                'status',
                'type',
                'interview_type',
                AllowedFilter::exact('candidate_id'),
                AllowedFilter::exact('job_posting_id'),
                AllowedFilter::exact('interviewer_id'),
                AllowedFilter::exact('created_by'),
                AllowedFilter::scope('scheduled'),
                AllowedFilter::scope('completed'),
                AllowedFilter::scope('today'),
                AllowedFilter::scope('upcoming'),
                AllowedFilter::callback('date_from', function ($query, $value) {
                    $query->where('date', '>=', $value);
                }),
                AllowedFilter::callback('date_to', function ($query, $value) {
                    $query->where('date', '<=', $value);
                }),
                AllowedFilter::callback('interviewer_department', function ($query, $value) {
                    $query->whereHas('interviewer', function ($q) use ($value) {
                        $q->where('department', $value);
                    });
                }),
            ])
            ->allowedSorts([
                'date',
                'time',
                'status',
                'type',
                'interview_type',
                'round',
                'created_at',
                'updated_at',
            ])
            ->allowedIncludes([
                'candidate',
                'jobPosting',
                'interviewer.user',
                'createdBy',
                'feedback',
            ])
            // Always load essential relationships by default
            ->with([
                'candidate',
                'jobPosting',
                'interviewer.user',
                'createdBy',
            ])
            ->defaultSort('date', 'time')
            ->paginate($request->get('per_page', 15));

        return InterviewResource::collection($interviews);
    }

    /**
     * Store a newly created interview.
     */
    public function store(StoreInterviewRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $interview = Interview::create([
                ...$request->validated(),
                'created_by' => $request->user()->id,
            ]);

            DB::commit();

            $interview->load([
                'candidate',
                'jobPosting',
                'interviewer.user',
                'createdBy',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Interview scheduled successfully',
                'data' => new InterviewResource($interview),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to schedule interview',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified interview.
     */
    public function show(Interview $interview): JsonResponse
    {
        $this->authorize('view', $interview);

        $interview->load([
            'candidate',
            'jobPosting',
            'interviewer.user',
            'createdBy',
            'feedback',
        ]);

        return response()->json([
            'status' => 'success',
            'data' => new InterviewResource($interview),
        ]);
    }

    /**
     * Update the specified interview.
     */
    public function update(UpdateInterviewRequest $request, Interview $interview): JsonResponse
    {
        try {
            DB::beginTransaction();

            $interview->update($request->validated());

            DB::commit();

            $interview->load([
                'candidate',
                'jobPosting',
                'interviewer.user',
                'createdBy',
                'feedback',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Interview updated successfully',
                'data' => new InterviewResource($interview),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update interview',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified interview.
     */
    public function destroy(Interview $interview): JsonResponse
    {

        try {
            $interview->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Interview deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete interview',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update interview status.
     */
    public function updateStatus(Request $request, Interview $interview): JsonResponse
    {

        $request->validate([
            'status' => 'required|in:scheduled,completed,cancelled,rescheduled,no-show',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $oldStatus = $interview->status;

            $interview->update([
                'status' => $request->status,
                'notes' => $request->notes ?
                    ($interview->notes ? $interview->notes . "\n\n" . $request->notes : $request->notes) :
                    $interview->notes,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Interview status updated successfully',
                'data' => [
                    'old_status' => $oldStatus,
                    'new_status' => $interview->status,
                    'updated_at' => $interview->updated_at->format('Y-m-d H:i:s'),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update interview status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check interviewer availability.
     */
    public function checkAvailability(Request $request): JsonResponse
    {
        $request->validate([
            'interviewer_id' => 'required|exists:interviewers,id',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'nullable|date_format:H:i',
            'duration' => 'nullable|integer|min:15|max:480',
        ]);

        try {
            $interviewer = Interviewer::findOrFail($request->interviewer_id);
            $date = $request->date;
            $time = $request->time;
            $duration = $request->duration ?? 60;

            $availability = [
                'interviewer_id' => $interviewer->id,
                'interviewer_name' => $interviewer->name,
                'date' => $date,
                'is_available' => $interviewer->isAvailableOn($date, $time),
                'remaining_capacity' => $interviewer->getRemainingCapacityForDate($date),
                'max_interviews_per_day' => $interviewer->max_interviews_per_day,
                'scheduled_interviews' => $interviewer->getInterviewsCountForDate($date),
            ];

            // Get available time slots for the date
            if ($interviewer->availability) {
                $dayOfWeek = strtolower(date('l', strtotime($date)));
                $dayAvailability = $interviewer->availability[$dayOfWeek] ?? null;

                if ($dayAvailability && isset($dayAvailability['slots'])) {
                    $availability['available_slots'] = $dayAvailability['slots'];
                }
            }

            // Get existing interviews for the date
            $existingInterviews = $interviewer->interviews()
                ->whereDate('date', $date)
                ->where('status', 'scheduled')
                ->select('time', 'duration')
                ->get()
                ->map(function ($interview) {
                    return [
                        'start_time' => $interview->time->format('H:i'),
                        'end_time' => $interview->time->addMinutes($interview->duration)->format('H:i'),
                    ];
                });

            $availability['existing_interviews'] = $existingInterviews;

            return response()->json([
                'status' => 'success',
                'data' => $availability,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to check availability',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send interview reminders.
     */
    public function sendReminders(Request $request): JsonResponse
    {
        $request->validate([
            'interview_ids' => 'nullable|array',
            'interview_ids.*' => 'exists:interviews,id',
            'hours_before' => 'nullable|integer|min:1|max:168', // 1 hour to 1 week
        ]);

        try {
            $hoursBeforeDefault = 24; // 24 hours before
            $hoursBefore = $request->get('hours_before', $hoursBeforeDefault);

            $query = Interview::where('status', 'scheduled')
                ->where('reminder_sent', false)
                ->where('date', '>=', now())
                ->where('date', '<=', now()->addHours($hoursBefore));

            if ($request->has('interview_ids')) {
                $query->whereIn('id', $request->interview_ids);
            }

            $interviews = $query->with(['candidate', 'interviewer.user', 'jobPosting'])->get();

            $sentCount = 0;
            foreach ($interviews as $interview) {
                // Here you would integrate with your email service
                // For now, we'll just mark as sent
                $interview->update(['reminder_sent' => true]);
                $sentCount++;
            }

            return response()->json([
                'status' => 'success',
                'message' => "Sent {$sentCount} interview reminder(s)",
                'data' => [
                    'sent_count' => $sentCount,
                    'hours_before' => $hoursBefore,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to send reminders',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get calendar events for integration.
     */
    public function calendarEvents(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'interviewer_id' => 'nullable|exists:interviewers,id',
        ]);

        try {
            $query = Interview::whereBetween('date', [$request->start_date, $request->end_date])
                ->where('status', 'scheduled');

            if ($request->has('interviewer_id')) {
                $query->where('interviewer_id', $request->interviewer_id);
            }

            $interviews = $query->with(['candidate', 'interviewer.user', 'jobPosting'])->get();

            $events = $interviews->map(function ($interview) {
                $startDateTime = $interview->date->format('Y-m-d') . ' ' . $interview->time->format('H:i:s');
                $endDateTime = $interview->date->setTimeFromTimeString($interview->time->format('H:i:s'))
                    ->addMinutes($interview->duration)
                    ->format('Y-m-d H:i:s');

                return [
                    'id' => $interview->id,
                    'title' => "Interview: {$interview->candidate->name} - {$interview->jobPosting->title}",
                    'start' => $startDateTime,
                    'end' => $endDateTime,
                    'type' => $interview->type,
                    'interview_type' => $interview->interview_type,
                    'status' => $interview->status,
                    'candidate_name' => $interview->candidate->name,
                    'interviewer_name' => $interview->interviewer->name,
                    'job_title' => $interview->jobPosting->title,
                    'meeting_link' => $interview->meeting_link,
                    'location' => $interview->location,
                    'notes' => $interview->notes,
                ];
            });

            return response()->json([
                'status' => 'success',
                'data' => $events,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get calendar events',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
