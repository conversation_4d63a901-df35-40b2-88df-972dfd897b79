<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreMessageTemplateRequest;
use App\Http\Requests\UpdateMessageTemplateRequest;
use App\Http\Requests\PreviewMessageTemplateRequest;
use App\Http\Resources\MessageTemplateResource;
use App\Models\MessageTemplate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class MessageTemplateController extends Controller
{
    /**
     * Display a listing of message templates with filtering
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = MessageTemplate::query();

        // Apply filters
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        if ($request->has('type') && $request->type) {
            $query->byType($request->type);
        }

        if ($request->has('language') && $request->language) {
            $query->byLanguage($request->language);
        }

        if ($request->has('is_active')) {
            if ($request->boolean('is_active')) {
                $query->active();
            } else {
                $query->where('is_active', false);
            }
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('subject', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Apply includes
        $includes = [];
        if ($request->has('include')) {
            $requestedIncludes = explode(',', $request->include);
            $allowedIncludes = ['createdBy', 'messages', 'parentTemplate', 'childTemplates'];
            $includes = array_intersect($requestedIncludes, $allowedIncludes);
        }

        if (!empty($includes)) {
            $query->with($includes);
        }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = 'desc';

        if (str_starts_with($sortField, '-')) {
            $sortField = substr($sortField, 1);
            $sortDirection = 'desc';
        } else {
            $sortDirection = 'asc';
        }

        $allowedSorts = ['name', 'category', 'type', 'language', 'version', 'created_at', 'updated_at'];
        if (in_array($sortField, $allowedSorts)) {
            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // Paginate
        $perPage = $request->get('per_page', 15);
        $templates = $query->paginate($perPage);

        return MessageTemplateResource::collection($templates);
    }

    /**
     * Store a newly created message template
     */
    public function store(StoreMessageTemplateRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            $validated['created_by'] = auth()->id();

            // Set default version if not provided
            if (!isset($validated['version'])) {
                $validated['version'] = 1;
            }

            $template = MessageTemplate::create($validated);
            $template->load('createdBy');

            return response()->json([
                'status' => 'success',
                'message' => 'Template đã được tạo thành công',
                'data' => new MessageTemplateResource($template),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể tạo template',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified message template
     */
    public function show(MessageTemplate $messageTemplate): JsonResponse
    {
        $messageTemplate->load(['createdBy', 'messages', 'parentTemplate', 'childTemplates']);

        return response()->json([
            'status' => 'success',
            'data' => new MessageTemplateResource($messageTemplate),
        ]);
    }

    /**
     * Update the specified message template
     */
    public function update(UpdateMessageTemplateRequest $request, MessageTemplate $messageTemplate): JsonResponse
    {
        try {
            $validated = $request->validated();

            // If content is being changed, create a new version
            if (isset($validated['content']) && $validated['content'] !== $messageTemplate->content) {
                $newTemplate = $messageTemplate->createNewVersion();
                $newTemplate->update($validated);
                $newTemplate->load('createdBy');

                return response()->json([
                    'status' => 'success',
                    'message' => 'Template đã được cập nhật với phiên bản mới',
                    'data' => new MessageTemplateResource($newTemplate),
                ]);
            }

            // Otherwise, just update the current template
            $messageTemplate->update($validated);
            $messageTemplate->load('createdBy');

            return response()->json([
                'status' => 'success',
                'message' => 'Template đã được cập nhật thành công',
                'data' => new MessageTemplateResource($messageTemplate),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể cập nhật template',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified message template
     */
    public function destroy(MessageTemplate $messageTemplate): JsonResponse
    {
        try {
            // Check if template is being used
            if ($messageTemplate->messages()->count() > 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Không thể xóa template đang được sử dụng',
                ], 400);
            }

            $messageTemplate->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Template đã được xóa thành công',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể xóa template',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Preview template with sample data
     */
    public function preview(PreviewMessageTemplateRequest $request, MessageTemplate $messageTemplate): JsonResponse
    {
        try {
            $data = $request->validated()['data'] ?? [];

            // If no data provided, use sample data
            if (empty($data)) {
                $data = $this->getSampleData($messageTemplate->category);
            }

            $renderedSubject = $messageTemplate->renderSubject($data);
            $renderedContent = $messageTemplate->renderContent($data);

            // Validate that all required variables are provided
            $missingVariables = $messageTemplate->validateVariables($data);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'template' => new MessageTemplateResource($messageTemplate),
                    'preview' => [
                        'subject' => $renderedSubject,
                        'content' => $renderedContent,
                    ],
                    'variables_used' => $data,
                    'missing_variables' => $missingVariables,
                    'available_variables' => $messageTemplate->getAvailableVariables(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể preview template',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get template categories
     */
    public function categories(): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'data' => MessageTemplate::getCategories(),
        ]);
    }

    /**
     * Get template types
     */
    public function types(): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'data' => MessageTemplate::getTypes(),
        ]);
    }

    /**
     * Duplicate a template
     */
    public function duplicate(MessageTemplate $messageTemplate): JsonResponse
    {
        try {
            $newTemplate = $messageTemplate->replicate();
            $newTemplate->name = $messageTemplate->name . ' (Copy)';
            $newTemplate->created_by = auth()->id();
            $newTemplate->version = 1;
            $newTemplate->parent_template_id = null;
            $newTemplate->save();

            $newTemplate->load('createdBy');

            return response()->json([
                'status' => 'success',
                'message' => 'Template đã được sao chép thành công',
                'data' => new MessageTemplateResource($newTemplate),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể sao chép template',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get sample data for template preview
     */
    private function getSampleData(string $category): array
    {
        $baseData = [
            'candidate_name' => 'Nguyễn Văn An',
            'candidate_email' => '<EMAIL>',
            'candidate_phone' => '0123456789',
            'company_name' => 'Công ty TNHH ABC',
            'recruiter_name' => 'Trần Thị Bình',
            'recruiter_email' => '<EMAIL>',
            'recruiter_phone' => '0987654321',
        ];

        return match ($category) {
            MessageTemplate::CATEGORY_INTERVIEW => array_merge($baseData, [
                'job_title' => 'Lập trình viên PHP Senior',
                'interview_date' => '25/07/2024',
                'interview_time' => '14:00',
                'interview_location' => 'Tầng 5, Tòa nhà ABC, 123 Đường XYZ',
                'interview_type' => 'Phỏng vấn trực tiếp',
            ]),
            MessageTemplate::CATEGORY_OFFER => array_merge($baseData, [
                'job_title' => 'Lập trình viên PHP Senior',
                'salary' => '25.000.000 VNĐ',
                'start_date' => '01/08/2024',
                'benefits' => 'Bảo hiểm, thưởng tháng 13, du lịch hàng năm',
            ]),
            MessageTemplate::CATEGORY_REJECTION => array_merge($baseData, [
                'job_title' => 'Lập trình viên PHP Senior',
                'feedback' => 'Ứng viên có kỹ năng tốt nhưng chưa phù hợp với yêu cầu hiện tại',
            ]),
            default => $baseData,
        };
    }
}
