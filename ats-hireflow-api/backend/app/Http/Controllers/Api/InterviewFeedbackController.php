<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreInterviewFeedbackRequest;
use App\Http\Resources\InterviewFeedbackResource;
use App\Models\InterviewFeedback;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class InterviewFeedbackController extends Controller
{
    /**
     * Display a listing of interview feedback with filtering and pagination.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $feedback = QueryBuilder::for(InterviewFeedback::class)
            ->allowedFilters([
                'recommend',
                'next_round_recommendation',
                AllowedFilter::exact('interview_id'),
                AllowedFilter::exact('interviewer_id'),
                AllowedFilter::callback('rating_min', function ($query, $value) {
                    $query->where('rating', '>=', $value);
                }),
                AllowedFilter::callback('technical_score_min', function ($query, $value) {
                    $query->where('technical_score', '>=', $value);
                }),
                AllowedFilter::callback('communication_score_min', function ($query, $value) {
                    $query->where('communication_score', '>=', $value);
                }),
                AllowedFilter::callback('cultural_fit_score_min', function ($query, $value) {
                    $query->where('cultural_fit_score', '>=', $value);
                }),
            ])
            ->allowedSorts([
                'rating',
                'technical_score',
                'communication_score',
                'cultural_fit_score',
                'created_at',
                'updated_at',
            ])
            ->allowedIncludes([
                'interview.candidate',
                'interview.jobPosting',
                'interviewer.user',
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return InterviewFeedbackResource::collection($feedback);
    }

    /**
     * Store newly created interview feedback.
     */
    public function store(StoreInterviewFeedbackRequest $request): JsonResponse
    {
        try {
            $feedback = InterviewFeedback::create($request->validated());

            $feedback->load([
                'interview.candidate',
                'interview.jobPosting',
                'interviewer.user',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Interview feedback submitted successfully',
                'data' => new InterviewFeedbackResource($feedback),
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to submit interview feedback',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified interview feedback.
     */
    public function show(InterviewFeedback $interviewFeedback): JsonResponse
    {
       // $this->authorize('view', $interviewFeedback);

        $interviewFeedback->load([
            'interview.candidate',
            'interview.jobPosting',
            'interviewer.user',
        ]);

        return response()->json([
            'status' => 'success',
            'data' => new InterviewFeedbackResource($interviewFeedback),
        ]);
    }

    /**
     * Update the specified interview feedback.
     */
    public function update(Request $request, InterviewFeedback $interviewFeedback): JsonResponse
    {

        $validatedData = $request->validate([
            'rating' => 'nullable|numeric|min:0|max:5',
            'comments' => 'nullable|string',
            'recommend' => 'nullable|boolean',
            'strengths' => 'nullable|array',
            'strengths.*' => 'string|max:255',
            'concerns' => 'nullable|array',
            'concerns.*' => 'string|max:255',
            'next_round_recommendation' => 'nullable|in:screening,technical,case-study,portfolio,cultural,final,offer,reject',
            'technical_score' => 'nullable|integer|min:0|max:100',
            'communication_score' => 'nullable|integer|min:0|max:100',
            'cultural_fit_score' => 'nullable|integer|min:0|max:100',
        ]);

        try {
            $interviewFeedback->update($validatedData);

            $interviewFeedback->load([
                'interview.candidate',
                'interview.jobPosting',
                'interviewer.user',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Interview feedback updated successfully',
                'data' => new InterviewFeedbackResource($interviewFeedback),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update interview feedback',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified interview feedback.
     */
    public function destroy(InterviewFeedback $interviewFeedback): JsonResponse
    {
        $this->authorize('delete', $interviewFeedback);

        try {
            $interviewFeedback->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Interview feedback deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete interview feedback',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
