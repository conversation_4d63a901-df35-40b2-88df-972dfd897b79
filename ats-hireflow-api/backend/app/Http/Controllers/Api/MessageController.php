<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SendMessageRequest;
use App\Http\Resources\MessageResource;
use App\Models\Message;
use App\Models\MessageTemplate;
use App\Services\MessageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class MessageController extends Controller
{
    protected MessageService $messageService;

    public function __construct(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }

    /**
     * Display a listing of messages with filtering
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = Message::query();

        // Apply filters
        if ($request->has('candidate_id') && $request->candidate_id) {
            $query->where('candidate_id', $request->candidate_id);
        }

        if ($request->has('job_posting_id') && $request->job_posting_id) {
            $query->where('job_posting_id', $request->job_posting_id);
        }

        if ($request->has('type') && $request->type) {
            $query->byType($request->type);
        }

        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        if ($request->has('status') && $request->status) {
            // $query->byStatus($request->status);
        }

        if ($request->has('template_id') && $request->template_id) {
            $query->where('template_id', $request->template_id);
        }

        if ($request->has('thread_id') && $request->thread_id) {
            $query->where('thread_id', $request->thread_id);
        }

        if ($request->has('priority_min') && $request->priority_min) {
            $query->where('priority', '>=', $request->priority_min);
        }

        if ($request->has('priority_max') && $request->priority_max) {
            $query->where('priority', '<=', $request->priority_max);
        }

        if ($request->has('date_from') && $request->date_from) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->where('created_at', '<=', $request->date_to);
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%")
                    ->orWhere('to_email', 'like', "%{$search}%")
                    ->orWhere('to_name', 'like', "%{$search}%");
            });
        }

        // Apply includes
        $includes = [];
        if ($request->has('include')) {
            $requestedIncludes = explode(',', $request->include);
            $allowedIncludes = ['candidate', 'jobPosting', 'template', 'createdBy', 'parentMessage', 'replies'];
            $includes = array_intersect($requestedIncludes, $allowedIncludes);
        }

        if (!empty($includes)) {
            $query->with($includes);
        }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = 'desc';

        if (str_starts_with($sortField, '-')) {
            $sortField = substr($sortField, 1);
            $sortDirection = 'desc';
        } else {
            $sortDirection = 'asc';
        }

        $allowedSorts = ['created_at', 'sent_at', 'priority', 'status', 'type', 'category'];
        if (in_array($sortField, $allowedSorts)) {
            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // Paginate
        $perPage = $request->get('per_page', 15);
        $messages = $query->paginate($perPage);

        return MessageResource::collection($messages);
    }

    /**
     * Send a new message
     */
    public function store(SendMessageRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            $message = $this->messageService->sendMessage($validated);

            return response()->json([
                'status' => 'success',
                'message' => 'Tin nhắn đã được gửi thành công',
                'data' => new MessageResource($message->load(['candidate', 'jobPosting', 'template', 'createdBy'])),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể gửi tin nhắn',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified message
     */
    public function show(Message $message): JsonResponse
    {
        $message->load(['candidate', 'jobPosting', 'template', 'createdBy', 'parentMessage', 'replies']);

        return response()->json([
            'status' => 'success',
            'data' => new MessageResource($message),
        ]);
    }

    /**
     * Update message status
     */
    public function update(Request $request, Message $message): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:draft,queued,sent,delivered,read,failed',
            'error_message' => 'nullable|string|max:1000',
            'external_id' => 'nullable|string|max:255',
            'metadata' => 'nullable|array',
        ]);

        try {
            // $updateData = $request->only(['status', 'error_message', 'external_id', 'metadata', 'subject', 'content']);
            $updateData = $request->all();

            // Update timestamps based on status
            switch ($request->status) {
                case 'sent':
                    $updateData['sent_at'] = now();
                    break;
                case 'delivered':
                    $updateData['delivered_at'] = now();
                    break;
                case 'read':
                    $updateData['read_at'] = now();
                    break;
            }

            $message->update($updateData);
            $message->load(['candidate', 'jobPosting', 'template', 'createdBy']);

            return response()->json([
                'status' => 'success',
                'message' => 'Trạng thái tin nhắn đã được cập nhật',
                'data' => new MessageResource($message),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể cập nhật trạng thái tin nhắn',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a message
     */
    public function destroy(Message $message): JsonResponse
    {
        try {
            // Only allow deletion of draft messages or failed messages
            // if (!in_array($message->status, ['draft', 'failed'])) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Chỉ có thể xóa tin nhắn ở trạng thái bản nháp hoặc thất bại',
            //     ], 400);
            // }

            $message->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Tin nhắn đã được xóa thành công',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể xóa tin nhắn',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send bulk messages using template
     */
    public function bulkSend(Request $request): JsonResponse
    {
        $request->validate([
            'template_id' => 'required|exists:message_templates,id',
            'candidate_ids' => 'required|array|min:1',
            'candidate_ids.*' => 'exists:candidates,id',
            'job_posting_id' => 'nullable|exists:job_postings,id',
            'scheduled_at' => 'nullable|date|after:now',
            'priority' => 'nullable|integer|min:1|max:10',
        ]);

        try {
            $results = $this->messageService->sendBulkMessages(
                $request->template_id,
                $request->candidate_ids,
                $request->job_posting_id,
                $request->scheduled_at,
                $request->priority ?? 5
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Tin nhắn hàng loạt đã được gửi thành công',
                'data' => [
                    'total_sent' => count($results['success']),
                    'total_failed' => count($results['failed']),
                    'success' => MessageResource::collection($results['success']),
                    'failed' => $results['failed'],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể gửi tin nhắn hàng loạt',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get message thread
     */
    public function thread(Message $message): JsonResponse
    {
        try {
            $threadMessages = $message->getThreadMessages();
            $threadMessages->load(['candidate', 'createdBy']);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'thread_id' => $message->thread_id ?: $message->id,
                    'messages' => MessageResource::collection($threadMessages),
                    'total_messages' => $threadMessages->count(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể lấy chuỗi tin nhắn',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reply to a message
     */
    public function reply(SendMessageRequest $request, Message $message): JsonResponse
    {
        try {
            $validated = $request->validated();
            $validated['parent_message_id'] = $message->id;
            $validated['thread_id'] = $message->thread_id ?: $message->id;

            $reply = $this->messageService->sendMessage($validated);

            return response()->json([
                'status' => 'success',
                'message' => 'Phản hồi đã được gửi thành công',
                'data' => new MessageResource($reply->load(['candidate', 'jobPosting', 'template', 'createdBy'])),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể gửi phản hồi',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get message statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $query = Message::query();

            // Apply date filter if provided
            if ($request->has('date_from')) {
                $query->where('created_at', '>=', $request->date_from);
            }
            if ($request->has('date_to')) {
                $query->where('created_at', '<=', $request->date_to);
            }

            $stats = [
                'total_messages' => $query->count(),
                'by_status' => $query->selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status')
                    ->toArray(),
                'by_type' => $query->selectRaw('type, COUNT(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type')
                    ->toArray(),
                'by_category' => $query->selectRaw('category, COUNT(*) as count')
                    ->groupBy('category')
                    ->pluck('count', 'category')
                    ->toArray(),
                'success_rate' => $this->calculateSuccessRate($query),
                'average_delivery_time' => $this->calculateAverageDeliveryTime($query),
            ];

            return response()->json([
                'status' => 'success',
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể lấy thống kê tin nhắn',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate success rate
     */
    private function calculateSuccessRate($query): float
    {
        $total = $query->count();
        if ($total === 0) return 0;

        $successful = $query->whereIn('status', ['sent', 'delivered', 'read'])->count();
        return round(($successful / $total) * 100, 2);
    }

    /**
     * Calculate average delivery time
     */
    private function calculateAverageDeliveryTime($query): ?float
    {
        return 0;
        $messages = $query->whereNotNull('sent_at')
            ->whereNotNull('delivered_at')
            ->get();

        if ($messages->isEmpty()) return null;

        $totalTime = $messages->sum(function ($message) {
            return $message->sent_at->diffInSeconds($message->delivered_at);
        });

        return round($totalTime / $messages->count(), 2);
    }
}
