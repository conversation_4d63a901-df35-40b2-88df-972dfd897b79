<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use App\Models\Interview;
use App\Models\JobPosting;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Get overview dashboard metrics.
     */
    public function overview(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));

            $overview = [
                'summary_cards' => [
                    'total_jobs' => JobPosting::count(),
                    'active_jobs' => JobPosting::where('status', 'active')->count(),
                    'total_candidates' => Candidate::count(),
                    'new_candidates_this_month' => Candidate::where('applied_date', '>=', now()->startOfMonth())->count(),
                    'interviews_this_week' => Interview::whereBetween('date', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                    'pending_interviews' => Interview::where('status', 'scheduled')->where('date', '>=', now())->count(),
                ],

                'recruitment_pipeline' => $this->getRecruitmentPipeline($dateFrom, $dateTo),
                 'recent_activities' => $this->getRecentActivities(),
                'top_performing_jobs' => $this->getTopPerformingJobs($dateFrom, $dateTo),
                'upcoming_interviews' => $this->getUpcomingInterviews(),
                'team_performance' => $this->getTeamPerformance($dateFrom, $dateTo),
            ];

            return response()->json([
                'status' => 'success',
                'data' => $overview,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get dashboard overview',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get recruitment pipeline analytics.
     */
    public function recruitmentPipeline(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'job_posting_id' => 'nullable|exists:job_postings,id',
            'department' => 'nullable|string',
        ]);

        try {
            $dateFrom = $request->get('date_from', now()->subDays(90)->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));

            $query = Candidate::whereBetween('applied_date', [$dateFrom, $dateTo]);

            if ($request->has('job_posting_id')) {
                $query->where('job_posting_id', $request->job_posting_id);
            }

            if ($request->has('department')) {
                $query->whereHas('jobPosting', function ($q) use ($request) {
                    $q->where('department', $request->department);
                });
            }

            $pipeline = [
                'candidates_by_stage' => $query->selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status'),

                'conversion_rates' => $this->calculatePipelineConversions($query),
                'time_in_stage' => $this->calculateTimeInStage($query),
                'stage_progression' => $this->getStageProgression($dateFrom, $dateTo),
                'bottlenecks' => $this->identifyBottlenecks($query),
            ];

            return response()->json([
                'status' => 'success',
                'data' => $pipeline,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get recruitment pipeline analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get source effectiveness analytics.
     */
    public function sourceEffectiveness(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $dateFrom = $request->get('date_from', now()->subDays(90)->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));

            $sourceAnalytics = Candidate::whereBetween('applied_date', [$dateFrom, $dateTo])
                ->whereNotNull('source')
                ->selectRaw('
                    source,
                    COUNT(*) as total_candidates,
                    AVG(rating) as avg_rating,
                    AVG(ai_score) as avg_ai_score,
                    SUM(CASE WHEN status = "hired" THEN 1 ELSE 0 END) as hired_count,
                    SUM(CASE WHEN status IN ("interview", "offer", "hired") THEN 1 ELSE 0 END) as qualified_count
                ')
                ->groupBy('source')
                ->get()
                ->map(function ($item) {
                    $conversionRate = $item->total_candidates > 0 ?
                        round(($item->hired_count / $item->total_candidates) * 100, 2) : 0;
                    $qualificationRate = $item->total_candidates > 0 ?
                        round(($item->qualified_count / $item->total_candidates) * 100, 2) : 0;

                    return [
                        'source' => $item->source,
                        'total_candidates' => $item->total_candidates,
                        'avg_rating' => round($item->avg_rating, 2),
                        'avg_ai_score' => round($item->avg_ai_score, 2),
                        'hired_count' => $item->hired_count,
                        'conversion_rate' => $conversionRate,
                        'qualification_rate' => $qualificationRate,
                        'quality_score' => round(($item->avg_rating * 20 + $item->avg_ai_score) / 2, 2),
                    ];
                });

            return response()->json([
                'status' => 'success',
                'data' => [
                    'source_performance' => $sourceAnalytics,
                    'best_sources' => $sourceAnalytics->sortByDesc('quality_score')->take(5)->values(),
                    'highest_volume' => $sourceAnalytics->sortByDesc('total_candidates')->take(5)->values(),
                    'best_conversion' => $sourceAnalytics->sortByDesc('conversion_rate')->take(5)->values(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get source effectiveness analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get team performance analytics.
     */
    public function teamPerformance(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));

            $teamPerformance = [
                'recruiter_performance' => $this->getRecruiterPerformance($dateFrom, $dateTo),
                'interviewer_performance' => $this->getInterviewerPerformance($dateFrom, $dateTo),
                'hiring_manager_performance' => $this->getHiringManagerPerformance($dateFrom, $dateTo),
                'department_performance' => $this->getDepartmentPerformance($dateFrom, $dateTo),
            ];

            return response()->json([
                'status' => 'success',
                'data' => $teamPerformance,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get team performance analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export analytics data.
     */
    public function exportData(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|in:candidates,jobs,interviews,pipeline,sources,team',
            'format' => 'required|in:pdf,excel,csv',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));

            // Here you would implement the actual export logic
            // For now, we'll return a mock response
            $exportData = [
                'export_id' => uniqid('export_'),
                'type' => $request->type,
                'format' => $request->format,
                'date_range' => ['from' => $dateFrom, 'to' => $dateTo],
                'status' => 'processing',
                'download_url' => null, // Will be populated when ready
                'created_at' => now()->format('Y-m-d H:i:s'),
            ];

            return response()->json([
                'status' => 'success',
                'message' => 'Export initiated successfully',
                'data' => $exportData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to initiate export',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get real-time metrics for dashboard widgets.
     */
    public function realTimeMetrics(): JsonResponse
    {
        try {
            $metrics = [
                'active_users' => User::where('last_login_at', '>=', now()->subMinutes(15))->count(),
                'interviews_today' => Interview::whereDate('date', today())->count(),
                'new_applications_today' => Candidate::whereDate('applied_date', today())->count(),
                'jobs_posted_this_week' => JobPosting::whereBetween('posted_date', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                'pending_feedback' => Interview::where('status', 'completed')
                    ->whereDoesntHave('feedback')
                    ->count(),
                'urgent_interviews' => Interview::where('status', 'scheduled')
                    ->where('date', '<=', now()->addHours(24))
                    ->count(),
                'last_updated' => now()->format('Y-m-d H:i:s'),
            ];

            return response()->json([
                'status' => 'success',
                'data' => $metrics,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get real-time metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // Helper methods
    private function getRecruitmentPipeline(string $dateFrom, string $dateTo): array
    {
        $candidates = Candidate::whereBetween('applied_date', [$dateFrom, $dateTo]);

        return [
            'total_candidates' => $candidates->count(),
            'by_status' => $candidates->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'conversion_funnel' => [
                'applied' => $candidates->count(),
                'screening' => $candidates->where('status', '!=', 'applied')->count(),
                'interview' => $candidates->whereIn('status', ['interview', 'offer', 'hired'])->count(),
                'offer' => $candidates->whereIn('status', ['offer', 'hired'])->count(),
                'hired' => $candidates->where('status', 'hired')->count(),
            ],
        ];
    }

    private function getRecentActivities(): array
    {
        // This would typically come from an activity log
        return [
            [
                'type' => 'candidate_applied',
                'message' => 'New candidate applied for Software Engineer position',
                'timestamp' => now()->subMinutes(15)->format('Y-m-d H:i:s'),
            ],
            [
                'type' => 'interview_scheduled',
                'message' => 'Interview scheduled for John Doe',
                'timestamp' => now()->subMinutes(30)->format('Y-m-d H:i:s'),
            ],
            [
                'type' => 'job_posted',
                'message' => 'New job posting: Senior Developer',
                'timestamp' => now()->subHours(2)->format('Y-m-d H:i:s'),
            ],
        ];
    }

    private function getTopPerformingJobs(string $dateFrom, string $dateTo): array
    {
        return JobPosting::withCount(['candidates' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('applied_date', [$dateFrom, $dateTo]);
            }])
            ->having('candidates_count', '>', 0)
            ->orderByDesc('candidates_count')
            ->take(5)
            ->get()
            ->map(function ($job) {
                return [
                    'id' => $job->id,
                    'title' => $job->title,
                    'department' => $job->department,
                    'candidates_count' => $job->candidates_count,
                    'status' => $job->status,
                ];
            })
            ->toArray();
    }

    private function getUpcomingInterviews(): array
    {
        return Interview::where('status', 'scheduled')
            ->where('date', '>=', now())
            ->where('date', '<=', now()->addDays(7))
            ->with(['candidate', 'interviewer.user', 'jobPosting'])
            ->orderBy('date')
            ->orderBy('time')
            ->take(10)
            ->get()
            ->map(function ($interview) {
                return [
                    'id' => $interview->id,
                    'candidate_name' => $interview->candidate->name,
                    'job_title' => $interview->jobPosting == null ? '' : $interview->jobPosting->title,
                    'interviewer_name' => $interview->interviewer->name,
                    'date' => $interview->date->format('Y-m-d'),
                    'time' => $interview->time->format('H:i'),
                    'type' => $interview->type,
                ];
            })
            ->toArray();
    }

    private function getTeamPerformance(string $dateFrom, string $dateTo): array
    {
        return [
            'top_recruiters' => User::where('role', 'recruiter')
                ->withCount(['createdCandidates' => function ($query) use ($dateFrom, $dateTo) {
                    $query->whereBetween('applied_date', [$dateFrom, $dateTo]);
                }])
                ->orderByDesc('created_candidates_count')
                ->take(5)
                ->get(['id', 'name', 'created_candidates_count'])
                ->toArray(),

            'interview_completion_rate' => $this->calculateInterviewCompletionRate($dateFrom, $dateTo),
        ];
    }

    private function calculatePipelineConversions($query): array
    {
        $total = $query->count();
        if ($total === 0) return [];

        return [
            'applied_to_screening' => round(($query->where('status', '!=', 'applied')->count() / $total) * 100, 2),
            'screening_to_interview' => round(($query->whereIn('status', ['interview', 'offer', 'hired'])->count() / $total) * 100, 2),
            'interview_to_offer' => round(($query->whereIn('status', ['offer', 'hired'])->count() / $total) * 100, 2),
            'offer_to_hired' => round(($query->where('status', 'hired')->count() / $total) * 100, 2),
        ];
    }

    private function calculateTimeInStage($query): array
    {
        // This would require more complex queries with status history
        return [
            'avg_time_to_interview' => 7.5, // days
            'avg_time_to_offer' => 14.2, // days
            'avg_time_to_hire' => 21.8, // days
        ];
    }

    private function getStageProgression(string $dateFrom, string $dateTo): array
    {
        // This would show how candidates move through stages over time
        return [
            'weekly_progression' => [], // Would contain weekly data
            'bottleneck_stages' => ['screening', 'interview'],
        ];
    }

    private function identifyBottlenecks($query): array
    {
        return [
            'stages_with_high_dropout' => ['screening'],
            'stages_with_long_duration' => ['interview'],
            'recommendations' => [
                'Improve screening process efficiency',
                'Reduce interview scheduling delays',
            ],
        ];
    }

    private function getRecruiterPerformance(string $dateFrom, string $dateTo): array
    {
        return User::where('role', 'recruiter')
            ->with(['createdCandidates' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('applied_date', [$dateFrom, $dateTo]);
            }])
            ->get()
            ->map(function ($recruiter) {
                $candidates = $recruiter->createdCandidates;
                return [
                    'id' => $recruiter->id,
                    'name' => $recruiter->name,
                    'candidates_sourced' => $candidates->count(),
                    'candidates_hired' => $candidates->where('status', 'hired')->count(),
                    'avg_candidate_rating' => round($candidates->avg('rating'), 2),
                    'conversion_rate' => $candidates->count() > 0 ?
                        round(($candidates->where('status', 'hired')->count() / $candidates->count()) * 100, 2) : 0,
                ];
            })
            ->toArray();
    }

    private function getInterviewerPerformance(string $dateFrom, string $dateTo): array
    {
        return User::where('role', 'interviewer')
            ->whereHas('interviewer')
            ->with(['interviewer.interviews' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('date', [$dateFrom, $dateTo]);
            }])
            ->get()
            ->map(function ($user) {
                $interviews = $user->interviewer->interviews ?? collect();
                $feedbackCount = $interviews->filter(function ($interview) {
                    return $interview->feedback()->exists();
                })->count();

                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'interviews_conducted' => $interviews->count(),
                    'feedback_provided' => $feedbackCount,
                    'feedback_rate' => $interviews->count() > 0 ?
                        round(($feedbackCount / $interviews->count()) * 100, 2) : 0,
                ];
            })
            ->toArray();
    }

    private function getHiringManagerPerformance(string $dateFrom, string $dateTo): array
    {
        return User::where('role', 'hiring_manager')
            ->with(['managedJobPostings.candidates' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('applied_date', [$dateFrom, $dateTo]);
            }])
            ->get()
            ->map(function ($manager) {
                $allCandidates = $manager->managedJobPostings->flatMap->candidates;
                return [
                    'id' => $manager->id,
                    'name' => $manager->name,
                    'active_jobs' => $manager->managedJobPostings->where('status', 'active')->count(),
                    'total_candidates' => $allCandidates->count(),
                    'candidates_hired' => $allCandidates->where('status', 'hired')->count(),
                    'avg_time_to_hire' => 21.5, // Would calculate from actual data
                ];
            })
            ->toArray();
    }

    private function getDepartmentPerformance(string $dateFrom, string $dateTo): array
    {
        return JobPosting::selectRaw('department, COUNT(*) as job_count')
            ->withCount(['candidates' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('applied_date', [$dateFrom, $dateTo]);
            }])
            ->groupBy('department')
            ->get()
            ->map(function ($dept) {
                return [
                    'department' => $dept->department,
                    'active_jobs' => $dept->job_count,
                    'total_candidates' => $dept->candidates_count,
                    'avg_candidates_per_job' => $dept->job_count > 0 ?
                        round($dept->candidates_count / $dept->job_count, 1) : 0,
                ];
            })
            ->toArray();
    }

    private function calculateInterviewCompletionRate(string $dateFrom, string $dateTo): float
    {
        $totalInterviews = Interview::whereBetween('date', [$dateFrom, $dateTo])->count();
        $completedInterviews = Interview::whereBetween('date', [$dateFrom, $dateTo])
            ->where('status', 'completed')
            ->count();

        return $totalInterviews > 0 ? round(($completedInterviews / $totalInterviews) * 100, 2) : 0;
    }
}
