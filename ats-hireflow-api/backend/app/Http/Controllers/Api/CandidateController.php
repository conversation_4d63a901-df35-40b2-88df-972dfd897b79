<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreCandidateRequest;
use App\Http\Requests\UpdateCandidateRequest;
use App\Http\Resources\CandidateResource;
use App\Models\Candidate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class CandidateController extends Controller
{
    /**
     * Display a listing of candidates with filtering and pagination.
     */
    public function index(Request $request)
    {
        if (!auth()->user()->can('view_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $candidates = QueryBuilder::for(Candidate::class)
            ->allowedFilters([
                'name',
                'email',
                'position',
                'status',
                'source',
                'location',
                AllowedFilter::exact('job_posting_id'),
                AllowedFilter::exact('assigned_to'),
                AllowedFilter::exact('created_by'),
                AllowedFilter::scope('high_rated'),
                AllowedFilter::scope('high_ai_score'),
                AllowedFilter::callback('skills', function ($query, $value) {
                    $skills = is_array($value) ? $value : [$value];
                    foreach ($skills as $skill) {
                        $query->whereJsonContains('skills', $skill);
                    }
                }),
                AllowedFilter::callback('rating_min', function ($query, $value) {
                    $query->where('rating', '>=', $value);
                }),
                AllowedFilter::callback('ai_score_min', function ($query, $value) {
                    $query->where('ai_score', '>=', $value);
                }),
                AllowedFilter::callback('applied_date_from', function ($query, $value) {
                    $query->where('applied_date', '>=', $value);
                }),
                AllowedFilter::callback('applied_date_to', function ($query, $value) {
                    $query->where('applied_date', '<=', $value);
                }),
            ])
            ->allowedSorts([
                'name',
                'email',
                'position',
                'status',
                'applied_date',
                'rating',
                'ai_score',
                'created_at',
                'updated_at',
            ])
            ->allowedIncludes([
                'jobPosting',
                'createdBy',
                'assignedTo',
                'interviews',
                'statusHistory',
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return CandidateResource::collection($candidates);
    }

    /**
     * Store a newly created candidate.
     */
    public function store(StoreCandidateRequest $request): JsonResponse
    {
        if (!auth()->user()->can('create_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            DB::beginTransaction();

            $candidate = Candidate::create([
                ...$request->validated(),
                'created_by' => $request->user()->id,
            ]);

            // Create related records
            $this->syncCandidateData($candidate, $request);

            // Log status history
            $candidate->statusHistory()->create([
                'old_status' => null,
                'new_status' => $candidate->status,
                'changed_by' => $request->user()->id,
                'notes' => 'Initial candidate creation',
            ]);

            DB::commit();

            $candidate->load([
                'jobPosting',
                'createdBy',
                'assignedTo',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate created successfully',
                'data' => new CandidateResource($candidate),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create candidate',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified candidate.
     */
    public function show(Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('view_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $candidate->load([
            'jobPosting',
            'createdBy',
            'assignedTo',
            'interviews.interviewer.user',
            'statusHistory.changedBy',
        ]);

        return response()->json([
            'status' => 'success',
            'data' => new CandidateResource($candidate),
        ]);
    }

    /**
     * Update the specified candidate.
     */
    public function update(UpdateCandidateRequest $request, Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('edit_candidates')) {
               return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            DB::beginTransaction();

            $oldStatus = $candidate->status;
            $candidate->update($request->validated());

            // Update related records
            $this->syncCandidateData($candidate, $request);

            // Log status change if status was updated
            if ($request->has('status') && $oldStatus !== $candidate->status) {
                $candidate->statusHistory()->create([
                    'old_status' => $oldStatus,
                    'new_status' => $candidate->status,
                    'changed_by' => $request->user()->id,
                    'notes' => $request->get('status_notes', 'Status updated via API'),
                ]);
            }

            DB::commit();

            $candidate->load([
                'jobPosting',
                'createdBy',
                'assignedTo',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate updated successfully',
                'data' => new CandidateResource($candidate),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update candidate',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified candidate.
     */
    public function destroy(Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('delete_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $candidate->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete candidate',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update candidate status.
     */
    public function updateStatus(Request $request, Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('manage_candidate_status')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'status' => 'required|in:sourced,applied,screening,interview,offer,hired,rejected',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $oldStatus = $candidate->status;

            $candidate->updateStatus(
                $request->status,
                $request->user(),
                $request->notes
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate status updated successfully',
                'data' => [
                    'old_status' => $oldStatus,
                    'new_status' => $candidate->status,
                    'updated_at' => $candidate->updated_at->format('Y-m-d H:i:s'),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update candidate status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload candidate resume.
     */
    public function uploadResume(Request $request, Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('edit_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'resume' => 'required|file|mimes:pdf,doc,docx|max:10240', // 10MB max
        ]);

        try {
            if ($request->hasFile('resume')) {
                // Delete old resume if exists
                if ($candidate->resume_url) {
                    Storage::delete($candidate->resume_url);
                }

                // Store new resume
                $path = $request->file('resume')->store('resumes', 'public');

                $candidate->update([
                    'resume_url' => $path,
                ]);

                return response()->json([
                    'status' => 'success',
                    'message' => 'Resume uploaded successfully',
                    'data' => [
                        'resume_url' => $candidate->resume_url,
                        'resume_full_url' => Storage::url($candidate->resume_url),
                    ],
                ]);
            }

            return response()->json([
                'status' => 'error',
                'message' => 'No resume file provided',
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to upload resume',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Trigger AI analysis for candidate.
     */
    public function triggerAiAnalysis(Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('edit_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            // This would integrate with your AI service
            // For now, we'll simulate with a random score
            $aiScore = rand(60, 95);

            $candidate->update([
                'ai_score' => $aiScore,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'AI analysis completed successfully',
                'data' => [
                    'ai_score' => $candidate->ai_score,
                    'analysis_date' => now()->format('Y-m-d H:i:s'),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to perform AI analysis',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Sync candidate related data (education, work history, skills, tags).
     */
    private function syncCandidateData(Candidate $candidate, Request $request): void
    {
        // Education and work_history are now simple text fields, handled automatically by model update
        // No need for separate sync logic as they are part of the main candidate record

        // Note: education and work_history are now stored as text fields in the candidates table
        // and are updated directly through the model's fillable attributes

        // Sync skills - now stored as JSON array
        if ($request->has('skills')) {
            $skills = is_array($request->skills) ? $request->skills : [];
            // Clean and validate skills
            $skills = array_values(array_unique(array_filter($skills, 'trim')));
            $candidate->skills = $skills;
        }

        // Sync tags - now stored as JSON array
        if ($request->has('tags')) {
            $tags = is_array($request->tags) ? $request->tags : [];
            // Clean and validate tags
            $tags = array_values(array_unique(array_filter($tags, 'trim')));
            $candidate->tags = $tags;
        }
    }


}
