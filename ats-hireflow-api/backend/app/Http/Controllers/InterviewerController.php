<?php

namespace App\Http\Controllers;

use App\Models\Interviewer;
use Illuminate\Http\Request;

class Interviewer<PERSON><PERSON><PERSON><PERSON> extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Interviewer $interviewer)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Interviewer $interviewer)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Interviewer $interviewer)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Interviewer $interviewer)
    {
        //
    }
}
