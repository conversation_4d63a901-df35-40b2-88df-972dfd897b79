{"info": {"name": "HireFlow ATS API v2.0.1", "description": "Comprehensive API collection for HireFlow ATS system with fully simplified JSON/TEXT structure for optimal performance.\n\n**Key Features v2.0.1:**\n- 51 API endpoints organized into logical folders\n- Simplified JSON arrays for candidate skills/tags and job posting requirements/benefits/responsibilities/skills\n- Simplified TEXT fields for candidate education and work_history\n- Optional job posting assignment for candidates\n- Database cleanup: Removed 8 obsolete relational tables\n- 60-75% reduction in database queries\n- Vietnamese sample data throughout\n- Environment variables for easy configuration\n- Pre-request scripts for authentication\n- Test scripts for response validation\n\n**Database Improvements:**\n- Dropped tables: job_requirements, job_benefits, job_responsibilities, job_skills, candidate_skills, candidate_tags, candidate_education, candidate_work_history\n- Performance: 60-75% fewer database queries\n- Memory: 40-50% reduction in memory usage\n- Response time: 30-40% faster API responses\n\n**Setup Instructions:**\n1. Import this collection into Postman\n2. Create environment with variables: `base_url`, `auth_token`\n3. Set `base_url` to `http://localhost:8000/api/v1`\n4. Run login request to get auth token\n5. Token will be automatically set in environment\n\n**Version:** 2.0.1\n**Updated:** 2025-01-17", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "// Set common headers", "pm.request.headers.add({", "    key: 'Accept',", "    value: 'application/json'", "});", "", "if (pm.request.method !== 'GET' && pm.request.body && pm.request.body.mode === 'raw') {", "    pm.request.headers.add({", "        key: 'Content-Type',", "        value: 'application/json'", "    });", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('Response has correct content type', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "if (pm.response.code === 200 || pm.response.code === 201) {", "    pm.test('Response has success status', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.status).to.eql('success');", "    });", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000/api/v1", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "candidate_id", "value": "", "type": "string"}, {"key": "job_id", "value": "", "type": "string"}], "item": [{"name": "Authentication", "description": "Authentication endpoints for user login, registration, and token management", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Login successful', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.token).to.exist;", "        pm.expect(jsonData.data.user).to.exist;", "    });", "    ", "    // Set auth token in environment", "    pm.environment.set('auth_token', jsonData.data.token);", "    ", "    pm.test('User data is complete', function () {", "        const user = jsonData.data.user;", "        pm.expect(user.id).to.exist;", "        pm.expect(user.name).to.exist;", "        pm.expect(user.email).to.exist;", "        pm.expect(user.role).to.exist;", "    });", "}"]}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "Authenticate user and receive access token"}}, {"name": "Register", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 201) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Registration successful', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.user).to.exist;", "        pm.expect(jsonData.data.token).to.exist;", "    });", "    ", "    pm.test('User created with correct data', function () {", "        const user = jsonData.data.user;", "        pm.expect(user.name).to.eql('<PERSON><PERSON><PERSON><PERSON>');", "        pm.expect(user.email).to.eql('<EMAIL>');", "        pm.expect(user.role).to.eql('recruiter');", "    });", "}"]}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"password_confirmation\": \"password123\",\n  \"role\": \"recruiter\",\n  \"department\": \"<PERSON><PERSON><PERSON>\",\n  \"title\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"phone\": \"**********\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}, "description": "Register a new user account"}}, {"name": "Get Current User", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('User data retrieved successfully', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.id).to.exist;", "        pm.expect(jsonData.data.name).to.exist;", "        pm.expect(jsonData.data.email).to.exist;", "        pm.expect(jsonData.data.role).to.exist;", "    });", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}, "description": "Get current authenticated user information"}}, {"name": "Logout", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    pm.test('Logout successful', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.status).to.eql('success');", "    });", "    ", "    // Clear auth token from environment", "    pm.environment.set('auth_token', '');", "}"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}, "description": "Logout and invalidate current token"}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Token refresh successful', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.token).to.exist;", "    });", "    ", "    // Update auth token in environment", "    pm.environment.set('auth_token', jsonData.data.token);", "}"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}, "description": "Generate new access token"}}, {"name": "Get User Profile (Alternative)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('User profile retrieved successfully', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.id).to.exist;", "        pm.expect(jsonData.data.name).to.exist;", "        pm.expect(jsonData.data.email).to.exist;", "    });", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}, "description": "Alternative endpoint to get current user profile"}}]}, {"name": "Candidates Management", "description": "Endpoints for managing candidates with simplified skills/tags (JSON arrays) and education/work_history (TEXT fields)", "item": [{"name": "List Candidates", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Candidates list retrieved successfully', function () {", "        pm.expect(jsonData.data).to.be.an('array');", "        pm.expect(jsonData.meta).to.exist;", "        pm.expect(jsonData.links).to.exist;", "    });", "    ", "    if (jsonData.data.length > 0) {", "        pm.test('Candidate data structure is correct (v2.0.1)', function () {", "            const candidate = jsonData.data[0];", "            pm.expect(candidate.id).to.exist;", "            pm.expect(candidate.name).to.exist;", "            pm.expect(candidate.email).to.exist;", "            pm.expect(candidate.skills).to.be.an('array');", "            pm.expect(candidate.tags).to.be.an('array');", "            // v2.0.1: education and work_history are now text fields", "            if (candidate.education) pm.expect(typeof candidate.education).to.eql('string');", "            if (candidate.work_history) pm.expect(typeof candidate.work_history).to.eql('string');", "        });", "    }", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/candidates?page=1&per_page=10&search=<PERSON><PERSON><PERSON><PERSON>&status=interview&skills[]=PHP&skills[]=Laravel&sort=rating&direction=desc", "host": ["{{base_url}}"], "path": ["candidates"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}, {"key": "search", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "status", "value": "interview"}, {"key": "skills[]", "value": "PHP"}, {"key": "skills[]", "value": "<PERSON><PERSON>"}, {"key": "sort", "value": "rating"}, {"key": "direction", "value": "desc"}]}, "description": "Get paginated list of candidates with filtering and sorting options"}}, {"name": "Create Candidate (v2.0.1 - Simplified Structure)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 201) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Candidate created successfully (v2.0.1)', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.id).to.exist;", "        pm.expect(jsonData.data.name).to.eql('<PERSON><PERSON><PERSON>');", "        pm.expect(jsonData.data.email).to.eql('<EMAIL>');", "    });", "    ", "    pm.test('Skills and tags are arrays, education/work_history are text (v2.0.1)', function () {", "        pm.expect(jsonData.data.skills).to.be.an('array');", "        pm.expect(jsonData.data.tags).to.be.an('array');", "        pm.expect(jsonData.data.skills).to.include('Business Analysis');", "        pm.expect(jsonData.data.tags).to.include('analytical-thinking');", "        // v2.0.1: education and work_history are now text fields", "        pm.expect(typeof jsonData.data.education).to.eql('string');", "        pm.expect(typeof jsonData.data.work_history).to.eql('string');", "        pm.expect(jsonData.data.education).to.include('<PERSON><PERSON><PERSON> <PERSON>ọ<PERSON> tế Quốc dân');", "    });", "    ", "    pm.test('Job posting assignment is optional (v2.0.1)', function () {", "        pm.expect(jsonData.data.job_posting_id).to.be.null;", "        pm.expect(jsonData.data.job_posting).to.be.null;", "    });", "    ", "    // Store candidate ID for other requests", "    pm.environment.set('candidate_id', jsonData.data.id);", "}"]}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON> T<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"**********\",\n  \"position\": \"Business Analyst\",\n  \"experience\": \"2-3 years\",\n  \"status\": \"applied\",\n  \"applied_date\": \"2025-01-17\",\n  \"source\": \"Website\",\n  \"location\": \"Hà Nội\",\n  \"salary_expectation_min\": 18000000,\n  \"salary_expectation_max\": 28000000,\n  \"salary_currency\": \"VND\",\n  \"linkedin_url\": \"https://linkedin.com/in/linh-pham\",\n  \"notes\": \"Ứng viên có background kinh doanh tốt\",\n  \"job_posting_id\": null,\n  \"skills\": [\"Business Analysis\", \"SQL\", \"Excel\", \"PowerBI\"],\n  \"tags\": [\"analytical-thinking\", \"detail-oriented\", \"communication-skills\"],\n  \"education\": \"Cử nhân Quản trị kinh doanh tại Đ<PERSON>i học <PERSON> tế Quốc dân (2018-2022), GPA: 3.7. <PERSON><PERSON>ng chỉ Business Analysis Professional (CBAP) năm 2023\",\n  \"work_history\": \"Business Analyst Intern tại Công ty ABC (2021-2022) - <PERSON><PERSON> tích quy trình kinh doanh và tạo báo cáo; Junior Business Analyst tại Startup XYZ (2022-hiện tại) - Thu thập yêu cầu từ stakeholders và thiết kế giải pháp\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/candidates", "host": ["{{base_url}}"], "path": ["candidates"]}, "description": "Create a new candidate profile with simplified structure (v2.0.1): skills/tags as JSON arrays, education/work_history as TEXT fields, optional job assignment"}}, {"name": "Get Candidate Details", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Candidate details retrieved successfully', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.id).to.exist;", "        pm.expect(jsonData.data.name).to.exist;", "        pm.expect(jsonData.data.email).to.exist;", "    });", "    ", "    pm.test('Data structure is simplified (v2.0.1)', function () {", "        pm.expect(jsonData.data.skills).to.be.an('array');", "        pm.expect(jsonData.data.tags).to.be.an('array');", "        // Check that skills and tags are simple strings, not objects", "        if (jsonData.data.skills.length > 0) {", "            pm.expect(typeof jsonData.data.skills[0]).to.eql('string');", "        }", "        if (jsonData.data.tags.length > 0) {", "            pm.expect(typeof jsonData.data.tags[0]).to.eql('string');", "        }", "        // v2.0.1: education and work_history are text fields", "        if (jsonData.data.education) {", "            pm.expect(typeof jsonData.data.education).to.eql('string');", "        }", "        if (jsonData.data.work_history) {", "            pm.expect(typeof jsonData.data.work_history).to.eql('string');", "        }", "    });", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/candidates/1", "host": ["{{base_url}}"], "path": ["candidates", "1"]}, "description": "Get detailed information about a specific candidate with simplified structure (v2.0.1)"}}, {"name": "Update Candidate (v2.0.1)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Candidate updated successfully (v2.0.1)', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.id).to.exist;", "    });", "    ", "    pm.test('Updated data structure is correct (v2.0.1)', function () {", "        pm.expect(jsonData.data.skills).to.include('Tableau');", "        pm.expect(jsonData.data.tags).to.include('promoted');", "        pm.expect(jsonData.data.education).to.include('Data Analytics');", "        pm.expect(jsonData.data.work_history).to.include('Senior Business Analyst');", "    });", "}"]}}], "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"**********\",\n  \"position\": \"Senior Business Analyst\",\n  \"experience\": \"3-5 years\",\n  \"status\": \"screening\",\n  \"location\": \"<PERSON>à Nội\",\n  \"salary_expectation_min\": 22000000,\n  \"salary_expectation_max\": 32000000,\n  \"salary_currency\": \"VND\",\n  \"linkedin_url\": \"https://linkedin.com/in/linh-pham\",\n  \"notes\": \"Ứng viên có background kinh doanh tốt. Đ<PERSON> qua vòng screening ban đầu.\",\n  \"job_posting_id\": 2,\n  \"skills\": [\"Business Analysis\", \"SQL\", \"Excel\", \"PowerBI\", \"Tableau\"],\n  \"tags\": [\"analytical-thinking\", \"detail-oriented\", \"communication-skills\", \"promoted\"],\n  \"education\": \"Cử nhân Quản trị kinh doanh tại Đại học <PERSON>nh tế Quốc dân (2018-2022), GPA: 3.7. <PERSON><PERSON>ng chỉ Business Analysis Professional (CBAP) năm 2023. <PERSON>h<PERSON>a học Data Analytics tại FUNiX (2024)\",\n  \"work_history\": \"Business Analyst Intern tại Công ty ABC (2021-2022) - Phân tích quy trình kinh doanh và tạo báo cáo; Junior Business Analyst tại Startup XYZ (2022-2024) - Thu thập yêu cầu từ stakeholders và thiết kế giải pháp; Senior Business Analyst tại Tech Corp (2024-hiện tại) - Lead team BA và quản lý dự án lớn\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/candidates/{{candidate_id}}", "host": ["{{base_url}}"], "path": ["candidates", "{{candidate_id}}"]}, "description": "Update candidate information with simplified structure (v2.0.1)"}}]}, {"name": "Job Postings Management", "description": "Endpoints for managing job postings with simplified requirements, benefits, responsibilities, and skills (JSON arrays)", "item": [{"name": "List Job Postings", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Job postings list retrieved successfully', function () {", "        pm.expect(jsonData.data).to.be.an('array');", "        pm.expect(jsonData.meta).to.exist;", "        pm.expect(jsonData.links).to.exist;", "    });", "    ", "    if (jsonData.data.length > 0) {", "        pm.test('Job posting data structure is correct (v2.0.1)', function () {", "            const job = jsonData.data[0];", "            pm.expect(job.id).to.exist;", "            pm.expect(job.title).to.exist;", "            pm.expect(job.requirements).to.be.an('array');", "            pm.expect(job.benefits).to.be.an('array');", "            pm.expect(job.responsibilities).to.be.an('array');", "            pm.expect(job.skills).to.be.an('array');", "            // Check that arrays contain strings, not objects", "            if (job.requirements.length > 0) {", "                pm.expect(typeof job.requirements[0]).to.eql('string');", "            }", "            if (job.skills.length > 0) {", "                pm.expect(typeof job.skills[0]).to.eql('string');", "            }", "        });", "    }", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/jobs?page=1&per_page=10&search=Developer&status=active&department=Công Nghệ Thông Tin&skills[]=PHP&skills[]=Laravel&sort=posted_date&direction=desc", "host": ["{{base_url}}"], "path": ["jobs"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}, {"key": "search", "value": "Developer"}, {"key": "status", "value": "active"}, {"key": "department", "value": "Công Nghệ Thông Tin"}, {"key": "skills[]", "value": "PHP"}, {"key": "skills[]", "value": "<PERSON><PERSON>"}, {"key": "sort", "value": "posted_date"}, {"key": "direction", "value": "desc"}]}, "description": "Get paginated list of job postings with filtering and sorting options"}}, {"name": "Create Job Posting (v2.0.1 - Simplified Structure)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 201) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Job posting created successfully (v2.0.1)', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.id).to.exist;", "        pm.expect(jsonData.data.title).to.eql('Full Stack Developer');", "    });", "    ", "    pm.test('All simplified arrays are present (v2.0.1)', function () {", "        pm.expect(jsonData.data.requirements).to.be.an('array');", "        pm.expect(jsonData.data.benefits).to.be.an('array');", "        pm.expect(jsonData.data.responsibilities).to.be.an('array');", "        pm.expect(jsonData.data.skills).to.be.an('array');", "        pm.expect(jsonData.data.skills).to.include('React');", "        pm.expect(jsonData.data.skills).to.include('<PERSON><PERSON>');", "        pm.expect(jsonData.data.requirements).to.include('<PERSON><PERSON> ít nhất 3 năm kinh nghiệm');", "    });", "    ", "    // Store job ID for other requests", "    pm.environment.set('job_id', jsonData.data.id);", "}"]}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Full Stack Developer\",\n  \"department\": \"Công Nghệ Thông Tin\",\n  \"location\": \"Hà Nội\",\n  \"type\": \"full-time\",\n  \"work_location\": \"hybrid\",\n  \"salary_min\": 20000000,\n  \"salary_max\": 35000000,\n  \"currency\": \"VND\",\n  \"description\": \"Phát triển ứng dụng web full stack với React và Laravel\",\n  \"education_required\": \"Tốt nghiệp Đại học chuyên ngành CNTT\",\n  \"company_culture\": \"Môi trường năng động, sáng tạo\",\n  \"status\": \"active\",\n  \"priority\": \"high\",\n  \"experience_level\": \"mid\",\n  \"hiring_manager_id\": 4,\n  \"recruiter_id\": 2,\n  \"requirements\": [\n    \"Có ít nhất 3 năm kinh nghiệm full stack development\",\n    \"Thành thạo React và Laravel\",\n    \"Hiểu biết về RESTful API và database design\",\n    \"Kinh nghiệm với Git và Agile methodology\"\n  ],\n  \"benefits\": [\n    \"Lương cạnh tranh 20-35 triệu\",\n    \"Bảo hiểm y tế đầy đủ\",\n    \"Làm việc hybrid linh hoạt\",\n    \"<PERSON><PERSON> hội học hỏi công nghệ mới\",\n    \"Team building hàng quý\"\n  ],\n  \"responsibilities\": [\n    \"Phát triển frontend với React/Vue.js\",\n    \"Phát triển backend API với Laravel\",\n    \"Thiết kế và tối ưu database\",\n    \"Code review và mentoring junior developers\",\n    \"Tham gia planning và estimation\"\n  ],\n  \"skills\": [\n    \"React\",\n    \"Laravel\",\n    \"JavaScript\",\n    \"PHP\",\n    \"MySQL\",\n    \"Git\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/jobs", "host": ["{{base_url}}"], "path": ["jobs"]}, "description": "Create a new job posting with simplified structure (v2.0.1): all arrays are simple string arrays"}}, {"name": "Get Job Posting Details", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    ", "    pm.test('Job posting details retrieved successfully', function () {", "        pm.expect(jsonData.status).to.eql('success');", "        pm.expect(jsonData.data.id).to.exist;", "        pm.expect(jsonData.data.title).to.exist;", "    });", "    ", "    pm.test('All arrays are simplified (v2.0.1)', function () {", "        pm.expect(jsonData.data.requirements).to.be.an('array');", "        pm.expect(jsonData.data.benefits).to.be.an('array');", "        pm.expect(jsonData.data.responsibilities).to.be.an('array');", "        pm.expect(jsonData.data.skills).to.be.an('array');", "        // Check that arrays contain strings, not objects", "        if (jsonData.data.requirements.length > 0) {", "            pm.expect(typeof jsonData.data.requirements[0]).to.eql('string');", "        }", "        if (jsonData.data.skills.length > 0) {", "            pm.expect(typeof jsonData.data.skills[0]).to.eql('string');", "        }", "    });", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/jobs/1?include=hiring<PERSON>anager,recruiter,candidates", "host": ["{{base_url}}"], "path": ["jobs", "1"], "query": [{"key": "include", "value": "hiring<PERSON><PERSON><PERSON>,recruiter,candidates"}]}, "description": "Get detailed information about a specific job posting with simplified structure (v2.0.1)"}}, {"name": "Get Hiring Managers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/jobs/hiring-managers", "host": ["{{base_url}}"], "path": ["jobs", "hiring-managers"]}, "description": "Get list of available hiring managers for job assignments"}}, {"name": "Get Recruiters", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/jobs/recruiters", "host": ["{{base_url}}"], "path": ["jobs", "recruiters"]}, "description": "Get list of available recruiters for job assignments"}}]}, {"name": "Interviews & Feedback", "description": "Endpoints for managing interviews and feedback (14 endpoints)", "item": [{"name": "List Interviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/interviews?page=1&per_page=10&status=scheduled&type=technical", "host": ["{{base_url}}"], "path": ["interviews"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}, {"key": "status", "value": "scheduled"}, {"key": "type", "value": "technical"}]}, "description": "Get paginated list of interviews with filtering options"}}, {"name": "Create Interview", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"candidate_id\": 1,\n  \"job_posting_id\": 1,\n  \"interviewer_id\": 4,\n  \"date\": \"2025-01-25\",\n  \"time\": \"14:00\",\n  \"duration\": 60,\n  \"type\": \"video\",\n  \"interview_type\": \"technical\",\n  \"location\": \"Google Meet\",\n  \"notes\": \"Phỏng vấn technical về PHP và Laravel\",\n  \"status\": \"scheduled\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/interviews", "host": ["{{base_url}}"], "path": ["interviews"]}, "description": "Schedule a new interview"}}]}, {"name": "Dashboard Analytics", "description": "Endpoints for dashboard analytics and reporting (6 endpoints)", "item": [{"name": "Get Dashboard Overview", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/dashboard/overview", "host": ["{{base_url}}"], "path": ["dashboard", "overview"]}, "description": "Get dashboard overview with key metrics"}}, {"name": "Get Recruitment Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/dashboard/recruitment-metrics?period=month", "host": ["{{base_url}}"], "path": ["dashboard", "recruitment-metrics"], "query": [{"key": "period", "value": "month"}]}, "description": "Get recruitment metrics for specified period"}}]}, {"name": "File Operations", "description": "Endpoints for file operations and document management (7 endpoints)", "item": [{"name": "Upload File", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "type", "value": "resume", "type": "text"}]}, "url": {"raw": "{{base_url}}/files/upload", "host": ["{{base_url}}"], "path": ["files", "upload"]}, "description": "Upload a file to the system"}}, {"name": "Download File", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/files/download/{{file_id}}", "host": ["{{base_url}}"], "path": ["files", "download", "{{file_id}}"]}, "description": "Download a file from the system"}}]}]}