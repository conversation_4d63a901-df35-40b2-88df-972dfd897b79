# HireFlow ATS - API Documentation

## Overview

The HireFlow ATS API is a RESTful API built with Laravel 12.x that provides comprehensive recruitment management functionality. This documentation covers all 51 endpoints available in the system.

### Base URL
```
http://localhost:8000/api/v1
```

### Authentication
All protected endpoints require Bearer token authentication:
```
Authorization: Bearer {your_access_token}
```

### Response Format
All API responses follow a consistent JSON structure:

**Success Response:**
```json
{
  "status": "success",
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

**Error Response:**
```json
{
  "status": "error",
  "message": "Error description",
  "errors": {
    // Validation errors (if applicable)
  }
}
```

### Pagination
List endpoints support pagination with the following parameters:
- `page` - Page number (default: 1)
- `per_page` - Items per page (default: 15, max: 100)

**Paginated Response:**
```json
{
  "status": "success",
  "data": [
    // Array of items
  ],
  "links": {
    "first": "http://localhost:8000/api/v1/candidates?page=1",
    "last": "http://localhost:8000/api/v1/candidates?page=10",
    "prev": null,
    "next": "http://localhost:8000/api/v1/candidates?page=2"
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 10,
    "per_page": 15,
    "to": 15,
    "total": 150
  }
}
```

### Filtering and Sorting
Most list endpoints support advanced filtering and sorting:

**Filtering:**
```
GET /candidates?filter[status]=interview&filter[rating_min]=4.0
```

**Sorting:**
```
GET /candidates?sort=-created_at,name
```

**Including Relationships:**
```
GET /candidates?include=jobPosting,education,skills
```

---

## Authentication Endpoints

### 1. Login
**POST** `/auth/login`

Authenticate user and receive access token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>",
      "role": "admin",
      "department": "IT",
      "permissions": ["manage_candidates", "manage_jobs"],
      "roles": ["admin"]
    },
    "token": "1|abc123...",
    "token_type": "Bearer"
  }
}
```

### 2. Register
**POST** `/auth/register`

Register a new user account.

**Request:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "role": "recruiter",
  "department": "HR",
  "title": "Senior Recruiter"
}
```

### 3. Get Current User
**GET** `/auth/me`

Get authenticated user information.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>",
      "role": "admin",
      "department": "IT",
      "permissions": ["manage_candidates", "manage_jobs"],
      "is_interviewer": false
    }
  }
}
```

### 4. Logout
**POST** `/auth/logout`

Revoke current access token.

### 5. Refresh Token
**POST** `/auth/refresh`

Generate new access token.

### 6. Get User Profile
**GET** `/user`

Get current authenticated user profile (alternative endpoint).

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Admin User",
    "email": "<EMAIL>",
    "role": "admin",
    "department": "IT",
    "title": "System Administrator",
    "phone": "0901234567",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-15T10:30:00.000000Z"
  }
}
```

---

## Candidates Management

### 7. List Candidates
**GET** `/candidates`

Get paginated list of candidates with filtering options.

**Query Parameters:**
- `filter[name]` - Filter by candidate name
- `filter[status]` - Filter by status (sourced, applied, screening, interview, offer, hired, rejected)
- `filter[job_posting_id]` - Filter by specific job
- `filter[rating_min]` - Minimum rating filter
- `filter[ai_score_min]` - Minimum AI score filter
- `filter[skills]` - Filter by skills (comma-separated)
- `sort` - Sort fields (name, rating, ai_score, created_at)
- `include` - Include relationships (jobPosting, education, workHistory, skills, tags)

**Example Request:**
```
GET /candidates?filter[status]=interview&filter[rating_min]=4.0&sort=-rating&include=jobPosting,skills&page=1&per_page=20
```

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "position": "Software Engineer",
      "status": "interview",
      "rating": 4.5,
      "ai_score": 85,
      "applied_date": "2024-01-15",
      "job_posting": {
        "id": 1,
        "title": "Senior Software Engineer",
        "department": "Engineering"
      },
      "skills": [
        {
          "skill_name": "JavaScript",
          "proficiency_level": "advanced"
        }
      ]
    }
  ]
}
```

### 8. Create Candidate
**POST** `/candidates`

Create a new candidate profile.

**Request:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "position": "Software Engineer",
  "experience": "3-5 years",
  "status": "applied",
  "applied_date": "2024-01-15",
  "source": "LinkedIn",
  "location": "Ho Chi Minh City",
  "salary_expectation_min": 25000000,
  "salary_expectation_max": 35000000,
  "salary_currency": "VND",
  "job_posting_id": 1,
  "interviewer_id": null,
  "education": [
    {
      "institution": "University of Technology",
      "degree": "Bachelor of Computer Science",
      "field_of_study": "Computer Science",
      "start_year": 2018,
      "end_year": 2022
    }
  ],
  "work_history": [
    {
      "company": "Tech Startup",
      "position": "Junior Developer",
      "start_date": "2022-06-01",
      "end_date": "2024-01-01",
      "is_current": false,
      "description": "Developed web applications"
    }
  ],
  "skills": [
    {
      "skill_name": "JavaScript",
      "proficiency_level": "advanced",
      "years_of_experience": 3
    }
  ],
  "tags": ["experienced", "team-player"]
}
```

**Note:** Both `job_posting_id` and `interviewer_id` are now optional (nullable) fields, allowing candidates to be created without immediate assignment to jobs or interviewers.

### 9. Get Candidate Details
**GET** `/candidates/{id}`

Get detailed information about a specific candidate.

**Example:**
```
GET /candidates/1?include=jobPosting,education,workHistory,skills,interviews
```

### 10. Update Candidate
**PUT** `/candidates/{id}`

Update candidate information.

### 11. Delete Candidate
**DELETE** `/candidates/{id}`

Delete a candidate (soft delete).

### 12. Update Candidate Status
**PATCH** `/candidates/{id}/status`

Update candidate status with notes.

**Request:**
```json
{
  "status": "interview",
  "notes": "Candidate passed initial screening"
}
```

### 13. Upload Resume
**POST** `/candidates/{id}/resume`

Upload candidate resume file.

**Request (multipart/form-data):**
```
resume: [PDF/DOC/DOCX file, max 10MB]
```

### 14. Trigger AI Analysis
**POST** `/candidates/{id}/ai-analysis`

Trigger AI analysis for candidate profile.

---

## Job Postings Management

### 15. List Job Postings
**GET** `/jobs`

Get paginated list of job postings.

**Query Parameters:**
- `filter[title]` - Filter by job title
- `filter[department]` - Filter by department
- `filter[status]` - Filter by status (draft, active, paused, closed)
- `filter[location]` - Filter by location
- `filter[type]` - Filter by job type
- `sort` - Sort fields
- `include` - Include relationships

### 16. Create Job Posting
**POST** `/jobs`

Create a new job posting.

**Request:**
```json
{
  "title": "Senior Software Engineer",
  "department": "Engineering",
  "location": "Ho Chi Minh City",
  "type": "full-time",
  "work_location": "hybrid",
  "salary_min": 30000000,
  "salary_max": 50000000,
  "currency": "VND",
  "description": "We are looking for a senior software engineer...",
  "experience_level": "senior",
  "hiring_manager_id": 2,
  "recruiter_id": 3,
  "requirements": [
    {
      "requirement_text": "5+ years of software development experience",
      "is_mandatory": true,
      "sort_order": 1
    }
  ],
  "responsibilities": [
    {
      "responsibility_text": "Lead development of new features",
      "sort_order": 1
    }
  ],
  "benefits": [
    {
      "benefit_text": "Competitive salary and bonuses",
      "sort_order": 1
    }
  ],
  "skills": [
    {
      "skill_name": "JavaScript",
      "importance_level": "required",
      "proficiency_level": "advanced"
    }
  ]
}
```

### 17. Get Job Details
**GET** `/jobs/{id}`

Get detailed job posting information.

### 18. Update Job Posting
**PUT** `/jobs/{id}`

Update job posting information.

### 19. Delete Job Posting
**DELETE** `/jobs/{id}`

Delete a job posting.

### 20. Get Job Candidates
**GET** `/jobs/{id}/candidates`

Get all candidates for a specific job posting.

### 21. Get Job Analytics
**GET** `/jobs/{id}/analytics`

Get comprehensive analytics for a job posting.

**Response:**
```json
{
  "status": "success",
  "data": {
    "job_info": {
      "id": 1,
      "title": "Senior Software Engineer",
      "status": "active"
    },
    "applicant_statistics": {
      "total_applicants": 45,
      "new_this_week": 8,
      "new_this_month": 32
    },
    "status_breakdown": {
      "applied": 20,
      "screening": 15,
      "interview": 8,
      "offer": 2
    },
    "conversion_rates": {
      "applied_to_screening": 75.0,
      "screening_to_interview": 53.3,
      "interview_to_offer": 25.0
    },
    "quality_metrics": {
      "avg_rating": 4.2,
      "avg_ai_score": 78,
      "high_quality_candidates": 12
    }
  }
}
```

### 22. Get Hiring Managers
**GET** `/jobs/hiring-managers`

Get list of hiring managers for job posting forms.

**Query Parameters:**
- `department` - Filter by department (optional)

**Response:**
```json
{
  "status": "success",
  "message": "Hiring managers retrieved successfully",
  "data": [
    {
      "id": 4,
      "name": "Lê Hoàng Nam",
      "email": "<EMAIL>",
      "department": "Công Nghệ Thông Tin",
      "title": "Trưởng Phòng IT"
    },
    {
      "id": 5,
      "name": "Phạm Thị Hương",
      "email": "<EMAIL>",
      "department": "Marketing",
      "title": "Trưởng Phòng Marketing"
    }
  ]
}
```

### 23. Get Recruiters
**GET** `/jobs/recruiters`

Get list of recruiters for job posting forms.

**Query Parameters:**
- `department` - Filter by department (optional)

**Response:**
```json
{
  "status": "success",
  "message": "Recruiters retrieved successfully",
  "data": [
    {
      "id": 2,
      "name": "Nguyễn Thị Lan Anh",
      "email": "<EMAIL>",
      "department": "Nhân Sự",
      "title": "Chuyên Viên Tuyển Dụng Senior"
    },
    {
      "id": 3,
      "name": "Trần Văn Minh",
      "email": "<EMAIL>",
      "department": "Nhân Sự",
      "title": "Chuyên Viên Tuyển Dụng"
    }
  ]
}
```

### 24. Bulk Job Actions
**POST** `/jobs/bulk-action`

Perform bulk operations on multiple job postings.

**Request:**
```json
{
  "action": "activate",
  "job_ids": [1, 2, 3, 4]
}
```

---

## Interviews and Scheduling

### 25. List Interviews
**GET** `/interviews`

Get paginated list of interviews.

**Query Parameters:**
- `filter[status]` - Filter by status
- `filter[type]` - Filter by interview type
- `filter[candidate_id]` - Filter by candidate
- `filter[interviewer_id]` - Filter by interviewer
- `filter[date_from]` - Filter from date
- `filter[date_to]` - Filter to date

### 26. Schedule Interview
**POST** `/interviews`

Schedule a new interview.

**Request:**
```json
{
  "candidate_id": 1,
  "job_posting_id": 1,
  "interviewer_id": 1,
  "date": "2024-02-15",
  "time": "14:00",
  "duration": 60,
  "type": "video",
  "interview_type": "technical",
  "meeting_link": "https://zoom.us/j/123456789",
  "notes": "Technical interview focusing on JavaScript",
  "agenda": [
    "Introduction and background",
    "Technical questions",
    "Coding exercise",
    "Q&A session"
  ]
}
```

### 27. Get Interview Details
**GET** `/interviews/{id}`

Get detailed interview information.

### 28. Update Interview
**PUT** `/interviews/{id}`

Update interview details.

### 29. Delete Interview
**DELETE** `/interviews/{id}`

Cancel/delete an interview.

### 30. Update Interview Status
**PATCH** `/interviews/{id}/status`

Update interview status.

**Request:**
```json
{
  "status": "completed",
  "notes": "Interview completed successfully"
}
```

### 31. Check Interviewer Availability
**GET** `/interviews/availability/check`

Check if an interviewer is available for scheduling.

**Query Parameters:**
```
?interviewer_id=1&date=2024-02-15&time=14:00&duration=60
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "interviewer_id": 1,
    "interviewer_name": "John Smith",
    "date": "2024-02-15",
    "is_available": true,
    "remaining_capacity": 3,
    "existing_interviews": [
      {
        "start_time": "10:00",
        "end_time": "11:00"
      }
    ]
  }
}
```

### 32. Send Interview Reminders
**POST** `/interviews/reminders/send`

Send reminder notifications for upcoming interviews.

**Request:**
```json
{
  "interview_ids": [1, 2, 3],
  "hours_before": 24
}
```

### 33. Get Calendar Events
**GET** `/interviews/calendar/events`

Get interview events for calendar integration.

**Query Parameters:**
```
?start_date=2024-02-01&end_date=2024-02-29&interviewer_id=1
```

### 34. List Interview Feedback
**GET** `/interview-feedback`

Get paginated list of interview feedback.

### 35. Submit Interview Feedback
**POST** `/interview-feedback`

Submit feedback for a completed interview.

**Request:**
```json
{
  "interview_id": 1,
  "interviewer_id": 1,
  "rating": 4.5,
  "comments": "Strong technical skills, good communication",
  "recommend": true,
  "strengths": [
    "Excellent problem-solving skills",
    "Strong JavaScript knowledge"
  ],
  "concerns": [
    "Limited experience with React"
  ],
  "next_round_recommendation": "final",
  "technical_score": 85,
  "communication_score": 90,
  "cultural_fit_score": 88
}
```

### 36. Get Feedback Details
**GET** `/interview-feedback/{id}`

Get detailed interview feedback.

### 37. Update Feedback
**PUT** `/interview-feedback/{id}`

Update interview feedback.

### 38. Delete Feedback
**DELETE** `/interview-feedback/{id}`

Delete interview feedback.

---

## Dashboard Analytics

### 39. Dashboard Overview
**GET** `/dashboard/overview`

Get comprehensive dashboard overview with key metrics.

**Query Parameters:**
- `date_from` - Start date for analytics
- `date_to` - End date for analytics

**Response:**
```json
{
  "status": "success",
  "data": {
    "summary_cards": {
      "total_jobs": 45,
      "active_jobs": 32,
      "total_candidates": 234,
      "new_candidates_this_month": 67,
      "interviews_this_week": 18,
      "pending_interviews": 12
    },
    "recruitment_pipeline": {
      "total_candidates": 234,
      "by_status": {
        "applied": 89,
        "screening": 67,
        "interview": 45,
        "offer": 23,
        "hired": 10
      }
    },
    "top_performing_jobs": [
      {
        "id": 1,
        "title": "Senior Software Engineer",
        "candidates_count": 45,
        "status": "active"
      }
    ],
    "upcoming_interviews": [
      {
        "id": 1,
        "candidate_name": "Jane Smith",
        "job_title": "Software Engineer",
        "interviewer_name": "John Doe",
        "date": "2024-02-15",
        "time": "14:00"
      }
    ]
  }
}
```

### 40. Recruitment Pipeline Analytics
**GET** `/dashboard/recruitment-pipeline`

Get detailed recruitment pipeline analytics.

### 41. Source Effectiveness Analytics
**GET** `/dashboard/source-effectiveness`

Get source performance and ROI analysis.

### 42. Team Performance Analytics
**GET** `/dashboard/team-performance`

Get team productivity and performance metrics.

### 43. Real-time Metrics
**GET** `/dashboard/real-time-metrics`

Get real-time dashboard metrics for widgets.

### 44. Export Analytics Data
**POST** `/dashboard/export`

Export analytics data in various formats.

**Request:**
```json
{
  "type": "pipeline",
  "format": "excel",
  "date_from": "2024-01-01",
  "date_to": "2024-01-31"
}
```

---

## File Upload Operations

### 45. Upload Resume
**POST** `/candidates/{id}/resume`

Upload candidate resume (covered in candidates section).

### 46. Upload Avatar
**POST** `/users/{id}/avatar`

Upload user avatar image.

**Request (multipart/form-data):**
```
avatar: [JPEG/PNG/GIF/WebP file, max 5MB]
```

### 47. Upload Document
**POST** `/files/upload`

Upload general document.

**Request (multipart/form-data):**
```
file: [File, max 20MB]
type: "contract" | "certificate" | "portfolio" | "other"
entity_type: "candidate" | "job" | "interview" | "user"
entity_id: 123
description: "Optional description"
```

### 48. Bulk File Upload
**POST** `/files/bulk-upload`

Upload multiple files at once.

### 49. Delete File
**DELETE** `/files/delete`

Delete an uploaded file.

**Request:**
```json
{
  "file_path": "resumes/1/resume_1_20240115_abc123.pdf",
  "entity_type": "candidate",
  "entity_id": 1
}
```

### 50. Get File Info
**GET** `/files/info`

Get information about an uploaded file.

### 51. Generate Download URL
**POST** `/files/download-url`

Generate secure download URL for a file.

**Request:**
```json
{
  "file_path": "resumes/1/resume_1_20240115_abc123.pdf",
  "expiration_minutes": 60
}
```

---

## Error Handling

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### Common Error Responses

**Validation Error (422):**
```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": {
    "email": ["The email field is required."],
    "password": ["The password must be at least 8 characters."]
  }
}
```

**Unauthorized (401):**
```json
{
  "status": "error",
  "message": "Unauthenticated"
}
```

**Forbidden (403):**
```json
{
  "status": "error",
  "message": "This action is unauthorized"
}
```

---

## Frontend Implementation Examples

### JavaScript/Fetch Example
```javascript
// Login and store token
async function login(email, password) {
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password })
  });
  
  const data = await response.json();
  if (data.status === 'success') {
    localStorage.setItem('token', data.data.token);
    return data.data.user;
  }
  throw new Error(data.message);
}

// API call with authentication
async function getCandidates(filters = {}) {
  const token = localStorage.getItem('token');
  const queryParams = new URLSearchParams(filters).toString();
  
  const response = await fetch(`/api/v1/candidates?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json',
    }
  });
  
  return await response.json();
}

// File upload example
async function uploadResume(candidateId, file) {
  const token = localStorage.getItem('token');
  const formData = new FormData();
  formData.append('resume', file);
  
  const response = await fetch(`/api/v1/candidates/${candidateId}/resume`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
    body: formData
  });
  
  return await response.json();
}
```

### React Hook Example
```javascript
import { useState, useEffect } from 'react';

function useCandidates(filters = {}) {
  const [candidates, setCandidates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    async function fetchCandidates() {
      try {
        setLoading(true);
        const response = await getCandidates(filters);
        if (response.status === 'success') {
          setCandidates(response.data);
        } else {
          setError(response.message);
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    
    fetchCandidates();
  }, [JSON.stringify(filters)]);
  
  return { candidates, loading, error };
}
```

### Vue.js Composable Example
```javascript
import { ref, computed } from 'vue';

export function useApi() {
  const token = ref(localStorage.getItem('token'));
  
  const apiCall = async (endpoint, options = {}) => {
    const response = await fetch(`/api/v1${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token.value}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });
    
    return await response.json();
  };
  
  return {
    token,
    apiCall,
    isAuthenticated: computed(() => !!token.value),
  };
}
```

---

## Quick Reference - All 51 Endpoints

### Authentication (6 endpoints)
1. `POST /auth/login` - Login
2. `POST /auth/register` - Register
3. `GET /auth/me` - Get current user
4. `POST /auth/logout` - Logout
5. `POST /auth/refresh` - Refresh token
6. `GET /user` - Get user profile

### Candidates (8 endpoints)
7. `GET /candidates` - List candidates
8. `POST /candidates` - Create candidate
9. `GET /candidates/{id}` - Get candidate details
10. `PUT /candidates/{id}` - Update candidate
11. `DELETE /candidates/{id}` - Delete candidate
12. `PATCH /candidates/{id}/status` - Update status
13. `POST /candidates/{id}/resume` - Upload resume
14. `POST /candidates/{id}/ai-analysis` - Trigger AI analysis

### Job Postings (10 endpoints)
15. `GET /jobs` - List jobs
16. `POST /jobs` - Create job
17. `GET /jobs/{id}` - Get job details
18. `PUT /jobs/{id}` - Update job
19. `DELETE /jobs/{id}` - Delete job
20. `GET /jobs/{id}/candidates` - Get job candidates
21. `GET /jobs/{id}/analytics` - Get job analytics
22. `GET /jobs/hiring-managers` - Get hiring managers
23. `GET /jobs/recruiters` - Get recruiters
24. `POST /jobs/bulk-action` - Bulk job actions

### Interviews (14 endpoints)
25. `GET /interviews` - List interviews
26. `POST /interviews` - Schedule interview
27. `GET /interviews/{id}` - Get interview details
28. `PUT /interviews/{id}` - Update interview
29. `DELETE /interviews/{id}` - Delete interview
30. `PATCH /interviews/{id}/status` - Update interview status
31. `GET /interviews/availability/check` - Check availability
32. `POST /interviews/reminders/send` - Send reminders
33. `GET /interviews/calendar/events` - Get calendar events
34. `GET /interview-feedback` - List feedback
35. `POST /interview-feedback` - Submit feedback
36. `GET /interview-feedback/{id}` - Get feedback details
37. `PUT /interview-feedback/{id}` - Update feedback
38. `DELETE /interview-feedback/{id}` - Delete feedback

### Dashboard (6 endpoints)
39. `GET /dashboard/overview` - Dashboard overview
40. `GET /dashboard/recruitment-pipeline` - Pipeline analytics
41. `GET /dashboard/source-effectiveness` - Source analytics
42. `GET /dashboard/team-performance` - Team analytics
43. `GET /dashboard/real-time-metrics` - Real-time metrics
44. `POST /dashboard/export` - Export analytics

### File Operations (7 endpoints)
45. `POST /candidates/{id}/resume` - Upload resume
46. `POST /users/{id}/avatar` - Upload avatar
47. `POST /files/upload` - Upload document
48. `POST /files/bulk-upload` - Bulk upload
49. `DELETE /files/delete` - Delete file
50. `GET /files/info` - Get file info
51. `POST /files/download-url` - Generate download URL

---

This documentation provides comprehensive coverage of all 51 API endpoints in the HireFlow ATS system, with practical examples for frontend implementation across different frameworks.

---

## Additional Notes

### Permission Requirements

All endpoints require appropriate permissions based on user roles:

- **Admin**: Full access to all endpoints
- **Recruiter**: Access to candidates, jobs, interviews, and related operations
- **Hiring Manager**: Access to view candidates, jobs, interviews, and analytics
- **Interviewer**: Access to view candidates, interviews, and submit feedback

### Rate Limiting

API endpoints are rate-limited to prevent abuse:
- **Authentication endpoints**: 5 requests per minute
- **File upload endpoints**: 10 requests per minute
- **General endpoints**: 60 requests per minute
- **Analytics endpoints**: 30 requests per minute

### Data Validation

All POST/PUT/PATCH requests include comprehensive validation:
- Required fields validation
- Data type validation
- Business logic validation
- File type and size validation for uploads

**Important Changes:**
- `job_posting_id` and `interviewer_id` are now **nullable** in candidate creation/update
- Candidates can be created without immediate job assignment
- Flexible workflow allowing candidates to be assigned to jobs later

### Caching

Certain endpoints implement caching for better performance:
- Dashboard analytics: 5 minutes cache
- Job listings: 2 minutes cache
- User lists: 10 minutes cache

### Localization

The API supports Vietnamese language data:
- All text fields support UTF-8 encoding
- Date formats follow Vietnamese standards
- Currency values in VND
- Location data for Vietnam

### Security Features

- JWT token authentication
- CSRF protection
- SQL injection prevention
- XSS protection
- File upload security scanning
- Rate limiting and throttling
