# HireFlow ATS - Backend API

<p align="center">
  <img src="https://img.shields.io/badge/Laravel-12.x-red.svg" alt="Laravel Version">
  <img src="https://img.shields.io/badge/PHP-8.3+-blue.svg" alt="PHP Version">
  <img src="https://img.shields.io/badge/MySQL-8.0+-orange.svg" alt="MySQL Version">
  <img src="https://img.shields.io/badge/License-MIT-green.svg" alt="License">
</p>

A comprehensive **Applicant Tracking System (ATS)** backend built with Laravel 12.x, featuring advanced candidate management, job posting workflows, interview scheduling, and AI-powered candidate analysis.

## 🚀 Features

### Core Functionality
- **Candidate Management** - Complete CRUD with advanced filtering, status tracking, and AI analysis
- **Job Posting Management** - Full job lifecycle from creation to closing
- **Interview Scheduling** - Calendar integration and feedback collection
- **User Management** - Role-based access control with granular permissions
- **Analytics & Reporting** - Comprehensive recruitment metrics and insights
- **File Management** - Resume uploads and document handling
- **Email System** - Automated notifications and template management
- **Activity Logging** - Complete audit trail for all actions

### Technical Features
- **RESTful API** - Clean, consistent API design following Laravel best practices
- **Authentication** - Laravel Sanctum for SPA authentication
- **Authorization** - Spatie Laravel Permission for role-based access control
- **Advanced Filtering** - Spatie Query Builder for complex data filtering
- **Database Optimization** - Proper indexing and query optimization
- **File Storage** - Configurable storage (local/S3) with security validation
- **Caching** - Redis integration for performance optimization
- **Queue System** - Background job processing for heavy operations

## 📋 Requirements

- **PHP 8.3+**
- **Composer**
- **MySQL 8.0+**
- **Redis** (optional but recommended)
- **Node.js** (for frontend integration)

## 🛠️ Installation

### 1. Clone and Setup
```bash
git clone <repository-url>
cd ats-hireflow-api/backend
composer install
cp .env.example .env
php artisan key:generate
```

### 2. Database Configuration
Update your `.env` file with database credentials:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hireflow_ats
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 3. Run Migrations and Seeders
```bash
php artisan migrate:fresh --seed
```

### 4. Configure Storage
```bash
php artisan storage:link
```

### 5. Start Development Server
```bash
php artisan serve --host=0.0.0.0 --port=8000
```

The API will be available at `http://localhost:8000`

## 🔐 Authentication

### Test User Accounts

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Admin | <EMAIL> | password | Full system access |
| Recruiter | <EMAIL> | password | Candidate & job management |
| Hiring Manager | <EMAIL> | password | Job oversight & decisions |
| Interviewer | <EMAIL> | password | Interview & feedback |

### Login Example
```bash
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'
```

## 📚 API Documentation

### Base URL
```
http://localhost:8000/api/v1
```

### Authentication Endpoints
- `POST /auth/login` - User login
- `POST /auth/register` - User registration  
- `GET /auth/me` - Get current user
- `POST /auth/logout` - Logout
- `POST /auth/refresh` - Refresh token

### Candidates Endpoints
- `GET /candidates` - List candidates (with filtering)
- `POST /candidates` - Create candidate
- `GET /candidates/{id}` - Get candidate details
- `PUT /candidates/{id}` - Update candidate
- `DELETE /candidates/{id}` - Delete candidate
- `PATCH /candidates/{id}/status` - Update status
- `POST /candidates/{id}/resume` - Upload resume
- `POST /candidates/{id}/ai-analysis` - Trigger AI analysis

### Job Postings Endpoints
- `GET /jobs` - List job postings
- `POST /jobs` - Create job posting
- `GET /jobs/{id}` - Get job details
- `PUT /jobs/{id}` - Update job posting
- `DELETE /jobs/{id}` - Delete job posting
- `GET /jobs/{id}/candidates` - Get job candidates
- `GET /jobs/{id}/analytics` - Job analytics
- `PATCH /jobs/{id}/status` - Update job status
- `POST /jobs/bulk-action` - Bulk operations (activate, deactivate, close, delete)

### Interviews Endpoints
- `GET /interviews` - List interviews (with filtering)
- `POST /interviews` - Schedule new interview
- `GET /interviews/{id}` - Get interview details
- `PUT /interviews/{id}` - Update interview
- `DELETE /interviews/{id}` - Delete interview
- `PATCH /interviews/{id}/status` - Update interview status
- `GET /interviews/availability/check` - Check interviewer availability
- `POST /interviews/reminders/send` - Send interview reminders
- `GET /interviews/calendar/events` - Get calendar events for integration

### Interview Feedback Endpoints
- `GET /interview-feedback` - List interview feedback
- `POST /interview-feedback` - Submit interview feedback
- `GET /interview-feedback/{id}` - Get feedback details
- `PUT /interview-feedback/{id}` - Update feedback
- `DELETE /interview-feedback/{id}` - Delete feedback

### Dashboard Analytics Endpoints
- `GET /dashboard/overview` - Dashboard overview with key metrics
- `GET /dashboard/recruitment-pipeline` - Pipeline analytics and conversion rates
- `GET /dashboard/source-effectiveness` - Source performance and ROI analysis
- `GET /dashboard/team-performance` - Team productivity and performance metrics
- `GET /dashboard/real-time-metrics` - Real-time dashboard widgets
- `POST /dashboard/export` - Export analytics data (PDF/Excel/CSV)

### File Upload Endpoints
- `POST /candidates/{id}/resume` - Upload candidate resume
- `POST /users/{id}/avatar` - Upload user avatar
- `POST /files/upload` - Upload general document
- `POST /files/bulk-upload` - Bulk file upload
- `DELETE /files/delete` - Delete uploaded file
- `GET /files/info` - Get file information
- `POST /files/download-url` - Generate secure download URL

## 🔍 Advanced Filtering

### Candidates Filtering Examples
```bash
# Filter by status
GET /candidates?filter[status]=interview

# Filter by skills
GET /candidates?filter[skills]=JavaScript,React

# Filter by rating
GET /candidates?filter[rating_min]=4.0

# Filter by AI score
GET /candidates?filter[ai_score_min]=80

# Filter by date range
GET /candidates?filter[applied_date_from]=2024-01-01&filter[applied_date_to]=2024-12-31

# Sort by rating (descending)
GET /candidates?sort=-rating

# Include relationships
GET /candidates?include=jobPosting,education,workHistory,skills

# Pagination
GET /candidates?page=2&per_page=20
```

## 🗄️ Database Schema

### Core Tables
- **users** - System users with roles and permissions
- **job_postings** - Job postings with requirements, responsibilities, benefits
- **candidates** - Candidate profiles with education, work history, skills
- **interviews** - Interview scheduling and feedback
- **messages** - Communication tracking and templates
- **activity_log** - Comprehensive audit trail

### Relationships
- User hasMany JobPostings, Candidates, Interviews
- JobPosting hasMany Candidates, hasMany Interviews
- Candidate belongsTo JobPosting, hasMany Interviews
- Interview belongsTo Candidate, belongsTo JobPosting, belongsTo Interviewer

## 🎭 Roles & Permissions

### Roles
- **Admin** - Full system access
- **Recruiter** - Candidate and job management
- **Hiring Manager** - Job oversight and hiring decisions
- **Interviewer** - Interview conduct and feedback

### Key Permissions
- `manage_candidates`, `view_candidates`, `create_candidates`, `edit_candidates`
- `manage_jobs`, `view_jobs`, `create_jobs`, `edit_jobs`, `publish_jobs`
- `manage_interviews`, `schedule_interviews`, `conduct_interviews`, `provide_feedback`
- `view_analytics`, `view_reports`, `export_data`
- `manage_users`, `manage_settings`

## 📊 Sample Data

The system comes pre-populated with:
- **4 test users** with different roles
- **45 job postings** across various departments
- **71 candidates** with complete profiles
- **Job requirements, responsibilities, benefits, and skills**
- **Candidate education, work history, skills, and tags**
- **Status history tracking**

## 🧪 Testing

### API Testing Examples
```bash
# Get candidates with authentication
curl -X GET "http://localhost:8000/api/v1/candidates" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"

# Create a new candidate
curl -X POST "http://localhost:8000/api/v1/candidates" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "position": "Software Engineer",
    "job_posting_id": 1,
    "applied_date": "2024-01-15"
  }'
```

### Running Tests
```bash
php artisan test
```

## 🔧 Development Commands

### Database Operations
```bash
# Reset and seed database
php artisan migrate:fresh --seed

# Add more sample data
php artisan db:seed --class=SampleDataSeeder

# Create new migration
php artisan make:migration create_new_table
```

### Generate Components
```bash
# Create API controller
php artisan make:controller Api/NewController --api

# Create model with migration, factory, and resource
php artisan make:model NewModel -mfr

# Create API resource
php artisan make:resource NewResource

# Create form request
php artisan make:request StoreNewRequest
```

### Clear Caches
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

## 🏗️ Architecture Overview

### Project Structure
```
ats-hireflow-api/
├── backend/                    # Laravel application
│   ├── app/
│   │   ├── Http/
│   │   │   ├── Controllers/Api/    # API controllers
│   │   │   ├── Requests/          # Form request validation
│   │   │   └── Resources/         # API response resources
│   │   ├── Models/                # Eloquent models
│   │   └── Services/              # Business logic services
│   ├── database/
│   │   ├── migrations/            # Database migrations
│   │   ├── seeders/              # Database seeders
│   │   └── factories/            # Model factories
│   ├── routes/
│   │   └── api.php               # API routes
│   └── config/                   # Configuration files
├── fe/                         # Frontend API documentation
│   ├── API.md                  # API specifications
│   └── api/                    # API response examples
├── TECHNICAL_ARCHITECTURE.md   # Technical architecture plan
├── DATABASE_SCHEMA.md          # Database design documentation
├── DEVELOPMENT_PLAN.md         # Implementation roadmap
└── README.md                   # This file
```

### Key Design Patterns
- **Repository Pattern** - Data access abstraction
- **Service Layer** - Business logic separation
- **Resource Pattern** - API response transformation
- **Request Validation** - Input validation and authorization
- **Observer Pattern** - Model event handling
- **Factory Pattern** - Object creation for testing

## 🔒 Security Features

### Authentication & Authorization
- **Laravel Sanctum** - SPA token authentication
- **Role-based Access Control** - Granular permission system
- **Request Validation** - Comprehensive input validation
- **CSRF Protection** - Cross-site request forgery protection
- **Rate Limiting** - API endpoint throttling

### Data Protection
- **SQL Injection Prevention** - Eloquent ORM protection
- **XSS Protection** - Output escaping and sanitization
- **File Upload Security** - Validation and type checking
- **Audit Logging** - Complete activity tracking
- **Soft Deletes** - Data preservation and recovery

## 📈 Performance Optimization

### Database Optimization
- **Strategic Indexing** - Optimized query performance
- **Eager Loading** - N+1 query prevention
- **Query Optimization** - Efficient data retrieval
- **Connection Pooling** - Database connection management

### Application Performance
- **Redis Caching** - Fast data access
- **Queue Processing** - Background job handling
- **Response Caching** - API response optimization
- **File Storage** - Efficient file management

## 🚀 Deployment

### Production Environment
```bash
# Set production environment
APP_ENV=production
APP_DEBUG=false

# Configure database
DB_CONNECTION=mysql
DB_HOST=your-production-host
DB_DATABASE=hireflow_ats_prod

# Configure cache and sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Configure file storage
FILESYSTEM_DISK=s3
AWS_BUCKET=your-s3-bucket
```

### Deployment Commands
```bash
# Install dependencies
composer install --optimize-autoloader --no-dev

# Run migrations
php artisan migrate --force

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Generate application key
php artisan key:generate
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PSR-12 coding standards
- Write comprehensive tests for new features
- Update documentation for API changes
- Use meaningful commit messages
- Ensure all tests pass before submitting

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in `/fe/API.md`
- Review the technical architecture in `TECHNICAL_ARCHITECTURE.md`

## 🎯 Roadmap

### Completed ✅
- Core database schema and models (25+ tables)
- Authentication and authorization system (Laravel Sanctum + Spatie Permissions)
- Candidates API with advanced filtering and CRUD operations
- Complete job postings API with analytics and bulk operations
- Interview scheduling system with availability checking
- Interview feedback collection and management
- Comprehensive dashboard analytics and reporting
- File upload system with resume parsing and document management
- Sample data and testing framework (45 jobs, 71 candidates)

### In Progress 🔄
- Advanced email notification system
- Calendar integration (Google Calendar, Outlook)
- Real-time notifications with WebSockets

### Planned 📋
- n8n workflow integration for automation
- Advanced AI-powered candidate matching
- Video interview integration
- Mobile API optimization
- Advanced reporting with custom dashboards
- Multi-tenant support for enterprise clients

---

**Built with ❤️ using Laravel 12.x**
