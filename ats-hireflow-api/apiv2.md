# HireFlow ATS - API Documentation v2.0.1

## Overview

The HireFlow ATS API v2.0.1 is a RESTful API built with Laravel 12.x that provides comprehensive recruitment management functionality. This version features fully simplified JSON/TEXT structure for optimal performance and developer experience.

**Key Improvements in v2.0.1:**
- Simplified JSON arrays for candidate skills and tags
- Simplified TEXT fields for candidate education and work_history
- Simplified JSON arrays for job posting requirements, benefits, responsibilities, and skills
- Optional job posting assignment for candidates
- Database cleanup: Removed 8 obsolete relational tables
- Improved performance with 60-75% reduction in database queries
- Better frontend integration with consistent data structures
- Full UTF-8 Vietnamese language support

**Total Endpoints:** 51
**Base URL:** `http://localhost:8000/api/v1`
**Authentication:** Bearer Token (Laravel Sanctum)
**Database Tables Removed:** 8 (job_requirements, job_benefits, job_responsibilities, job_skills, candidate_skills, candidate_tags, candidate_education, candidate_work_history)

## Authentication

All API endpoints require authentication using Bearer tokens, except for login and register endpoints.

**Headers Required:**
```
Accept: application/json
Content-Type: application/json
Authorization: Bearer {token}
```

## Standard Response Format

All API responses follow this consistent format:

**Success Response:**
```json
{
  "status": "success",
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

**Error Response:**
```json
{
  "status": "error",
  "message": "Error description",
  "errors": {
    // Validation errors (if applicable)
  }
}
```

## HTTP Status Codes

- `200` - OK (Success)
- `201` - Created (Resource created successfully)
- `400` - Bad Request (Invalid request data)
- `401` - Unauthorized (Authentication required)
- `403` - Forbidden (Insufficient permissions)
- `404` - Not Found (Resource not found)
- `422` - Unprocessable Entity (Validation errors)
- `500` - Internal Server Error

## Permission System

The API uses role-based permissions:

**Roles:**
- `admin` - Full system access
- `recruiter` - Candidate and job management
- `hiring_manager` - View candidates, jobs, interviews
- `interviewer` - Conduct interviews, submit feedback

**Key Permissions:**
- `view_candidates`, `create_candidates`, `edit_candidates`, `delete_candidates`
- `view_jobs`, `create_jobs`, `edit_jobs`, `delete_jobs`
- `view_interviews`, `create_interviews`, `edit_interviews`, `delete_interviews`
- `view_dashboard`, `view_analytics`, `export_data`

---

# API Endpoints

## Authentication (6 endpoints)

### 1. Login
**POST** `/auth/login`

Authenticate user and receive access token.

**Permission:** None (public endpoint)

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response (200):**
```json
{
  "status": "success",
  "message": "Đăng nhập thành công",
  "data": {
    "user": {
      "id": 1,
      "name": "Quản Trị Viên",
      "email": "<EMAIL>",
      "role": "admin",
      "department": "Quản Trị",
      "title": "Quản Trị Viên Hệ Thống",
      "phone": "**********",
      "avatar": null,
      "is_active": true,
      "permissions": ["view_candidates", "create_candidates", "..."],
      "roles": ["admin"]
    },
    "token": "1|abc123def456...",
    "token_type": "Bearer"
  }
}
```

**Error Response (401):**
```json
{
  "status": "error",
  "message": "Thông tin đăng nhập không chính xác"
}
```

### 2. Register
**POST** `/auth/register`

Register a new user account.

**Permission:** `create_users` or public (depending on configuration)

**Request:**
```json
{
  "name": "Nguyễn Văn An",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "role": "recruiter",
  "department": "Nhân Sự",
  "title": "Chuyên Viên Tuyển Dụng",
  "phone": "**********"
}
```

**Response (201):**
```json
{
  "status": "success",
  "message": "Tài khoản đã được tạo thành công",
  "data": {
    "user": {
      "id": 8,
      "name": "Nguyễn Văn An",
      "email": "<EMAIL>",
      "role": "recruiter",
      "department": "Nhân Sự",
      "title": "Chuyên Viên Tuyển Dụng",
      "phone": "**********",
      "is_active": true
    },
    "token": "2|xyz789abc456...",
    "token_type": "Bearer"
  }
}
```

### 3. Get Current User
**GET** `/auth/me`

Get current authenticated user information.

**Permission:** Authenticated user

**Headers:**
```
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Quản Trị Viên",
    "email": "<EMAIL>",
    "role": "admin",
    "department": "Quản Trị",
    "title": "Quản Trị Viên Hệ Thống",
    "phone": "**********",
    "avatar": null,
    "is_active": true,
    "permissions": ["view_candidates", "create_candidates", "..."],
    "roles": ["admin"]
  }
}
```

### 4. Logout
**POST** `/auth/logout`

Logout and invalidate current token.

**Permission:** Authenticated user

**Headers:**
```
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "status": "success",
  "message": "Đăng xuất thành công"
}
```

### 5. Refresh Token
**POST** `/auth/refresh`

Generate new access token.

**Permission:** Authenticated user

**Headers:**
```
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "status": "success",
  "message": "Token đã được làm mới",
  "data": {
    "token": "3|new_token_here...",
    "token_type": "Bearer"
  }
}
```

### 6. Get User Profile (Alternative)
**GET** `/user`

Alternative endpoint to get current user profile.

**Permission:** Authenticated user

**Headers:**
```
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Quản Trị Viên",
    "email": "<EMAIL>",
    "role": "admin",
    "department": "Quản Trị",
    "title": "Quản Trị Viên Hệ Thống",
    "phone": "**********",
    "is_active": true
  }
}
```

---

## Candidates Management (8 endpoints)

### 7. List Candidates
**GET** `/candidates`

Get paginated list of candidates with filtering and sorting options.

**Permission:** `view_candidates`

**Query Parameters:**
- `page` (integer) - Page number (default: 1)
- `per_page` (integer) - Items per page (default: 15, max: 100)
- `search` (string) - Search in name, email, position
- `status` (string) - Filter by status: `applied`, `screening`, `interview`, `offer`, `hired`, `rejected`
- `experience` (string) - Filter by experience level
- `location` (string) - Filter by location
- `source` (string) - Filter by application source
- `skills` (array) - Filter by skills (simplified)
- `tags` (array) - Filter by tags (simplified)
- `job_posting_id` (integer) - Filter by job posting
- `sort` (string) - Sort field: `name`, `applied_date`, `rating`, `ai_score`
- `direction` (string) - Sort direction: `asc`, `desc`

**Example Request:**
```
GET /candidates?search=Nguyễn&status=interview&skills[]=PHP&skills[]=Laravel&sort=rating&direction=desc&page=1&per_page=10
```

**Response (200):**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Nguyễn Văn Minh",
      "email": "<EMAIL>",
      "phone": "**********",
      "position": "Lập Trình Viên PHP Senior",
      "experience": "5+ years",
      "status": "interview",
      "applied_date": "2025-01-15",
      "source": "LinkedIn",
      "location": "Thành phố Hồ Chí Minh",
      "salary_expectation": {
        "min": "25000000.00",
        "max": "35000000.00",
        "currency": "VND",
        "range": "25,000,000 - 35,000,000 VND"
      },
      "rating": 4.5,
      "ai_score": 85,
      "linkedin_url": "https://linkedin.com/in/minh-nguyen",
      "github_url": "https://github.com/minhnguyen",
      "portfolio_url": "https://minhnguyen.dev",
      "avatar": null,
      "resume_url": "resumes/1/resume_1_20250115_abc123.pdf",
      "notes": "Ứng viên có kinh nghiệm tốt với Laravel và Vue.js",
      "skills": ["PHP", "Laravel", "MySQL", "JavaScript", "Vue.js"],
      "tags": ["kinh-nghiem-cao", "leader", "technical-expert", "team-player"],
      "job_posting": {
        "id": 1,
        "title": "Lập Trình Viên PHP Senior",
        "department": "Công Nghệ Thông Tin"
      },
      "created_by": {
        "id": 2,
        "name": "Nguyễn Thị Lan Anh",
        "email": "<EMAIL>"
      },
      "assigned_to": {
        "id": 3,
        "name": "Trần Văn Minh",
        "email": "<EMAIL>"
      },
      "created_at": "2025-01-15T08:30:00.000000Z",
      "updated_at": "2025-01-16T14:20:00.000000Z"
    }
  ],
  "links": {
    "first": "http://localhost:8000/api/v1/candidates?page=1",
    "last": "http://localhost:8000/api/v1/candidates?page=5",
    "prev": null,
    "next": "http://localhost:8000/api/v1/candidates?page=2"
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 5,
    "per_page": 10,
    "to": 10,
    "total": 45
  }
}
```

### 8. Create Candidate
**POST** `/candidates`

Create a new candidate profile with simplified skills and tags structure.

**Permission:** `create_candidates`

**Request:**
```json
{
  "name": "Trần Thị Hương",
  "email": "<EMAIL>",
  "phone": "**********",
  "position": "Frontend Developer",
  "experience": "2-3 years",
  "status": "applied",
  "applied_date": "2025-01-17",
  "source": "Website",
  "location": "Hà Nội",
  "salary_expectation_min": 15000000,
  "salary_expectation_max": 25000000,
  "salary_currency": "VND",
  "linkedin_url": "https://linkedin.com/in/huong-tran",
  "github_url": "https://github.com/huongtran",
  "portfolio_url": "https://huongtran.dev",
  "notes": "Ứng viên có tiềm năng phát triển cao",
  "job_posting_id": null,
  "skills": ["React", "JavaScript", "HTML/CSS", "TypeScript"],
  "tags": ["potential-high", "eager-to-learn", "good-attitude"],
  "education": "Cử nhân Công nghệ Thông tin tại Đại học Bách Khoa Hà Nội, chuyên ngành Khoa học Máy tính (2019-2023), GPA: 3.5",
  "work_history": "Junior Frontend Developer tại Startup ABC (2023-2025) - Phát triển giao diện web với React và TypeScript"
}
```

**Response (201):**
```json
{
  "status": "success",
  "message": "Ứng viên đã được tạo thành công",
  "data": {
    "id": 16,
    "name": "Trần Thị Hương",
    "email": "<EMAIL>",
    "phone": "**********",
    "position": "Frontend Developer",
    "experience": "2-3 years",
    "status": "applied",
    "applied_date": "2025-01-17",
    "source": "Website",
    "location": "Hà Nội",
    "salary_expectation": {
      "min": "15000000.00",
      "max": "25000000.00",
      "currency": "VND",
      "range": "15,000,000 - 25,000,000 VND"
    },
    "rating": null,
    "ai_score": null,
    "linkedin_url": "https://linkedin.com/in/huong-tran",
    "github_url": "https://github.com/huongtran",
    "portfolio_url": "https://huongtran.dev",
    "avatar": null,
    "resume_url": null,
    "notes": "Ứng viên có tiềm năng phát triển cao",
    "skills": ["React", "JavaScript", "HTML/CSS", "TypeScript"],
    "tags": ["potential-high", "eager-to-learn", "good-attitude"],
    "education": "Cử nhân Công nghệ Thông tin tại Đại học Bách Khoa Hà Nội, chuyên ngành Khoa học Máy tính (2019-2023), GPA: 3.5",
    "work_history": "Junior Frontend Developer tại Startup ABC (2023-2025) - Phát triển giao diện web với React và TypeScript",
    "job_posting": null,
    "created_by": {
      "id": 1,
      "name": "Quản Trị Viên",
      "email": "<EMAIL>"
    },
    "assigned_to": null,
    "created_at": "2025-01-17T10:15:00.000000Z",
    "updated_at": "2025-01-17T10:15:00.000000Z"
  }
}
```

**Error Response (422):**
```json
{
  "status": "error",
  "message": "Dữ liệu không hợp lệ",
  "errors": {
    "email": ["Email đã tồn tại trong hệ thống"],
    "skills": ["Mỗi kỹ năng không được vượt quá 100 ký tự"],
    "tags": ["Mỗi tag không được vượt quá 100 ký tự"]
  }
}
```

### 9. Get Candidate Details
**GET** `/candidates/{id}`

Get detailed information about a specific candidate.

**Permission:** `view_candidates`

**Path Parameters:**
- `id` (integer, required) - Candidate ID

**Query Parameters:**
- `include` (string) - Include related data: `jobPosting`, `createdBy`, `assignedTo`, `education`, `workHistory`, `interviews`, `statusHistory`

**Example Request:**
```
GET /candidates/1?include=jobPosting,education,workHistory,interviews
```

**Response (200):**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Nguyễn Văn Minh",
    "email": "<EMAIL>",
    "phone": "**********",
    "position": "Lập Trình Viên PHP Senior",
    "experience": "5+ years",
    "status": "interview",
    "applied_date": "2025-01-15",
    "source": "LinkedIn",
    "location": "Thành phố Hồ Chí Minh",
    "salary_expectation": {
      "min": "25000000.00",
      "max": "35000000.00",
      "currency": "VND",
      "range": "25,000,000 - 35,000,000 VND"
    },
    "rating": 4.5,
    "ai_score": 85,
    "linkedin_url": "https://linkedin.com/in/minh-nguyen",
    "github_url": "https://github.com/minhnguyen",
    "portfolio_url": "https://minhnguyen.dev",
    "avatar": null,
    "resume_url": "resumes/1/resume_1_20250115_abc123.pdf",
    "notes": "Ứng viên có kinh nghiệm tốt với Laravel và Vue.js",
    "skills": ["PHP", "Laravel", "MySQL", "JavaScript", "Vue.js"],
    "tags": ["kinh-nghiem-cao", "leader", "technical-expert", "team-player"],
    "job_posting": {
      "id": 1,
      "title": "Lập Trình Viên PHP Senior",
      "department": "Công Nghệ Thông Tin",
      "location": "Thành phố Hồ Chí Minh",
      "status": "active"
    },
    "education": [
      {
        "id": 1,
        "institution": "Đại học Bách Khoa TP.HCM",
        "degree": "Cử nhân Công nghệ Thông tin",
        "field_of_study": "Khoa học Máy tính",
        "start_year": 2015,
        "end_year": 2019,
        "gpa": 3.2
      }
    ],
    "work_history": [
      {
        "id": 1,
        "company": "Công ty TNHH ABC",
        "position": "Senior PHP Developer",
        "start_date": "2020-01-01",
        "end_date": "2024-12-31",
        "is_current": false,
        "description": "Phát triển và duy trì hệ thống ERP sử dụng Laravel"
      }
    ],
    "interviews": [
      {
        "id": 1,
        "date": "2025-01-20",
        "time": "14:00",
        "type": "video",
        "interview_type": "technical",
        "status": "scheduled",
        "interviewer": {
          "id": 1,
          "name": "Lê Hoàng Nam",
          "email": "<EMAIL>"
        }
      }
    ],
    "created_by": {
      "id": 2,
      "name": "Nguyễn Thị Lan Anh",
      "email": "<EMAIL>"
    },
    "assigned_to": {
      "id": 3,
      "name": "Trần Văn Minh",
      "email": "<EMAIL>"
    },
    "created_at": "2025-01-15T08:30:00.000000Z",
    "updated_at": "2025-01-16T14:20:00.000000Z"
  }
}
```

### 10. Update Candidate
**PUT** `/candidates/{id}`

Update candidate information with simplified skills and tags structure.

**Permission:** `edit_candidates`

**Path Parameters:**
- `id` (integer, required) - Candidate ID

**Request:**
```json
{
  "name": "Nguyễn Văn Minh",
  "email": "<EMAIL>",
  "phone": "**********",
  "position": "Lập Trình Viên PHP Senior",
  "experience": "5+ years",
  "status": "interview",
  "location": "Thành phố Hồ Chí Minh",
  "salary_expectation_min": 30000000,
  "salary_expectation_max": 40000000,
  "salary_currency": "VND",
  "linkedin_url": "https://linkedin.com/in/minh-nguyen",
  "github_url": "https://github.com/minhnguyen",
  "portfolio_url": "https://minhnguyen.dev",
  "notes": "Ứng viên có kinh nghiệm tốt với Laravel và Vue.js. Đã qua vòng screening.",
  "job_posting_id": 1,
  "skills": ["PHP", "Laravel", "MySQL", "JavaScript", "Vue.js", "Docker"],
  "tags": ["kinh-nghiem-cao", "leader", "technical-expert", "team-player", "docker-ready"]
}
```

**Response (200):**
```json
{
  "status": "success",
  "message": "Thông tin ứng viên đã được cập nhật",
  "data": {
    "id": 1,
    "name": "Nguyễn Văn Minh",
    "email": "<EMAIL>",
    "phone": "**********",
    "position": "Lập Trình Viên PHP Senior",
    "experience": "5+ years",
    "status": "interview",
    "applied_date": "2025-01-15",
    "source": "LinkedIn",
    "location": "Thành phố Hồ Chí Minh",
    "salary_expectation": {
      "min": "30000000.00",
      "max": "40000000.00",
      "currency": "VND",
      "range": "30,000,000 - 40,000,000 VND"
    },
    "rating": 4.5,
    "ai_score": 85,
    "linkedin_url": "https://linkedin.com/in/minh-nguyen",
    "github_url": "https://github.com/minhnguyen",
    "portfolio_url": "https://minhnguyen.dev",
    "avatar": null,
    "resume_url": "resumes/1/resume_1_20250115_abc123.pdf",
    "notes": "Ứng viên có kinh nghiệm tốt với Laravel và Vue.js. Đã qua vòng screening.",
    "skills": ["PHP", "Laravel", "MySQL", "JavaScript", "Vue.js", "Docker"],
    "tags": ["kinh-nghiem-cao", "leader", "technical-expert", "team-player", "docker-ready"],
    "job_posting": {
      "id": 1,
      "title": "Lập Trình Viên PHP Senior",
      "department": "Công Nghệ Thông Tin"
    },
    "created_by": {
      "id": 2,
      "name": "Nguyễn Thị Lan Anh",
      "email": "<EMAIL>"
    },
    "assigned_to": null,
    "created_at": "2025-01-15T08:30:00.000000Z",
    "updated_at": "2025-01-17T11:45:00.000000Z"
  }
}
```

### 11. Delete Candidate
**DELETE** `/candidates/{id}`

Delete a candidate (soft delete).

**Permission:** `delete_candidates`

**Path Parameters:**
- `id` (integer, required) - Candidate ID

**Response (200):**
```json
{
  "status": "success",
  "message": "Ứng viên đã được xóa thành công"
}
```

**Error Response (404):**
```json
{
  "status": "error",
  "message": "Không tìm thấy ứng viên"
}
```

### 12. Update Candidate Status
**PATCH** `/candidates/{id}/status`

Update candidate status with notes.

**Permission:** `manage_candidate_status`

**Path Parameters:**
- `id` (integer, required) - Candidate ID

**Request:**
```json
{
  "status": "interview",
  "notes": "Ứng viên đã qua vòng screening thành công. Chuyển sang phỏng vấn technical."
}
```

**Response (200):**
```json
{
  "status": "success",
  "message": "Trạng thái ứng viên đã được cập nhật",
  "data": {
    "id": 1,
    "status": "interview",
    "previous_status": "screening",
    "notes": "Ứng viên đã qua vòng screening thành công. Chuyển sang phỏng vấn technical.",
    "changed_by": {
      "id": 2,
      "name": "Nguyễn Thị Lan Anh",
      "email": "<EMAIL>"
    },
    "changed_at": "2025-01-17T14:30:00.000000Z"
  }
}
```

### 13. Upload Resume
**POST** `/candidates/{id}/resume`

Upload candidate resume file.

**Permission:** `edit_candidates`

**Path Parameters:**
- `id` (integer, required) - Candidate ID

**Request (multipart/form-data):**
```
resume: [PDF/DOC/DOCX file, max 10MB]
```

**Response (200):**
```json
{
  "status": "success",
  "message": "CV đã được tải lên thành công",
  "data": {
    "resume_url": "resumes/1/resume_1_20250117_xyz789.pdf",
    "file_size": "2.5 MB",
    "uploaded_at": "2025-01-17T15:20:00.000000Z"
  }
}
```

**Error Response (422):**
```json
{
  "status": "error",
  "message": "File không hợp lệ",
  "errors": {
    "resume": ["File phải là định dạng PDF, DOC hoặc DOCX và không vượt quá 10MB"]
  }
}
```

### 14. Trigger AI Analysis
**POST** `/candidates/{id}/ai-analysis`

Trigger AI analysis for candidate profile.

**Permission:** `edit_candidates`

**Path Parameters:**
- `id` (integer, required) - Candidate ID

**Response (200):**
```json
{
  "status": "success",
  "message": "Phân tích AI đã được khởi chạy",
  "data": {
    "analysis_id": "ai_analysis_123456",
    "estimated_completion": "2025-01-17T15:35:00.000000Z",
    "status": "processing"
  }
}
```

---

## Job Postings Management (10 endpoints)

### 15. List Job Postings
**GET** `/jobs`

Get paginated list of job postings with filtering and sorting options.

**Permission:** `view_jobs`

**Query Parameters:**
- `page` (integer) - Page number (default: 1)
- `per_page` (integer) - Items per page (default: 15, max: 100)
- `search` (string) - Search in title, description, department
- `status` (string) - Filter by status: `draft`, `active`, `paused`, `closed`
- `department` (string) - Filter by department
- `location` (string) - Filter by location
- `type` (string) - Filter by job type: `full-time`, `part-time`, `contract`, `internship`
- `work_location` (string) - Filter by work location: `onsite`, `remote`, `hybrid`
- `experience_level` (string) - Filter by experience level: `entry`, `mid`, `senior`, `lead`
- `priority` (string) - Filter by priority: `low`, `medium`, `high`, `urgent`
- `hiring_manager_id` (integer) - Filter by hiring manager
- `recruiter_id` (integer) - Filter by recruiter
- `skills` (array) - Filter by required skills (simplified)
- `sort` (string) - Sort field: `title`, `posted_date`, `closing_date`, `applicant_count`
- `direction` (string) - Sort direction: `asc`, `desc`

**Example Request:**
```
GET /jobs?search=Developer&status=active&department=Công Nghệ Thông Tin&skills[]=PHP&skills[]=Laravel&sort=posted_date&direction=desc&page=1&per_page=10
```

---

## Changelog

### **v2.0.1 (2025-01-17)**
- ✅ **Database Cleanup:** Removed 8 obsolete relational tables
  - Dropped: `job_requirements`, `job_benefits`, `job_responsibilities`, `job_skills`
  - Dropped: `candidate_skills`, `candidate_tags`, `candidate_education`, `candidate_work_history`
- ✅ **Simplified Education & Work History:** Converted to TEXT fields
  - `education`: Now simple text string instead of complex objects
  - `work_history`: Now simple text string instead of complex objects
- ✅ **Optional Job Assignment:** `job_posting_id` is now nullable for candidates
- ✅ **Performance Improvements:** 60-75% reduction in database queries
- ✅ **Code Cleanup:** Removed unused model files and relationships

### **v2.0.0 (2025-01-16)**
- ✅ Simplified candidate skills and tags to string arrays
- ✅ Simplified job posting requirements, benefits, responsibilities, and skills
- ✅ Added comprehensive Postman collection with 51 endpoints
- ✅ Updated API documentation with Vietnamese examples
- ✅ Improved performance with reduced database queries
- ✅ Added automated test scripts for all endpoints

### **v1.0.0 (Previous)**
- Basic API functionality with complex nested objects
- Relational data structure for skills, requirements, etc.
- Limited testing resources

---

**API Documentation v2.0.1 - Updated 2025-01-17**
