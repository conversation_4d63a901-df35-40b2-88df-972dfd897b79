# React to Next.js Migration Plan

## HireFlow ATS Application

---

## Phase 1: Codebase Analysis

### Current Technology Stack

#### ✅ **React Application (v18.3.1)**

- **Framework**: React 18.3.1 with modern features (Suspense, React 18 APIs)
- **Build Tool**: Vite 6.2.2 with SWC for fast compilation
- **Routing**: React Router DOM v6.26.2 (BrowserRouter)
- **Language**: TypeScript with flexible configuration
- **Package Manager**: npm with package-lock.json

#### ✅ **UI & Styling**

- **UI Library**: Radix UI components (@radix-ui/react-\*)
- **Styling**: Tailwind CSS 3.4.11 with custom theme
- **Component System**: Shadcn/ui pattern with reusable components
- **Icons**: Lucide React (462 icons)
- **Themes**: Light/Dark mode with next-themes
- **Animations**: Tailwind CSS animations + Framer Motion

#### ✅ **State Management & Data**

- **Query Management**: TanStack React Query v5.56.2
- **Form Handling**: React Hook Form v7.53.0 with Hookform Resolvers
- **Charts**: Recharts v2.12.7
- **Date Handling**: date-fns v3.6.0

#### ✅ **Internationalization**

- **Custom i18n system** with React Context
- **Languages**: Vietnamese (vi) and English (en)
- **Translation structure**: Comprehensive with nested objects
- **Real-time language switching**

#### ✅ **Development Tools**

- **TypeScript**: Configured with path mapping (@/\* aliases)
- **Linting**: Not explicitly configured (needs setup)
- **Testing**: Vitest v3.1.4
- **Formatting**: Prettier v3.5.3

#### ✅ **Backend Integration**

- **API**: Express.js server integration with Vite middleware
- **CORS**: Configured for development
- **Routes**: RESTful API endpoints (/api/\*)

### Current Application Structure

```
client/
├── components/          # Reusable UI components
│   ├── ui/             # Shadcn/ui components
│   ├── layout/         # Layout components (Header, Sidebar, Layout)
│   ├── landing/        # Landing page sections
│   ├── dashboard/      # Dashboard-specific components
│   ├── candidates/     # Candidate management components
│   └── [feature]/      # Feature-specific components
├── pages/              # Page components (13 pages)
│   ├── Landing.tsx     # Landing page
│   ├── Dashboard.tsx   # Main dashboard
│   ├── Analytics.tsx   # Analytics page
│   └── [other-pages]   # Other feature pages
├── lib/                # Utilities and configuration
│   ├── i18n.tsx        # Internationalization system
│   ├── types.ts        # TypeScript type definitions
│   ├── utils.ts        # Utility functions
│   └── translations/   # Translation files
├── hooks/              # Custom React hooks
├── data/               # Mock data and constants
└── global.css          # Global styles with CSS variables
```

### Current Pages (13 total)

1. `/` - Landing page (marketing site)
2. `/dashboard` - Main dashboard
3. `/candidates` - Candidate management
4. `/jobs` - Job management
5. `/pipeline` - Recruitment pipeline
6. `/calendar` - Calendar and scheduling
7. `/interviewers` - Interviewer management
8. `/messages` - Communication center
9. `/analytics` - Analytics and reporting
10. `/profile` - User profile
11. `/team` - Team management
12. `/settings` - Application settings
13. `/login` - Authentication

---

## Phase 2: Migration Challenges & Considerations

### 🔴 **Critical Migration Challenges**

#### 1. **Client-Side Routing → App Router/Pages Router**

- **Current**: React Router DOM with BrowserRouter
- **Challenge**: Converting client-side routes to Next.js file-based routing
- **Impact**: HIGH - Requires restructuring all page components

#### 2. **Vite → Next.js Build System**

- **Current**: Vite with SWC, custom Express middleware
- **Challenge**: Migrating build configuration and dev server setup
- **Impact**: MEDIUM - Configuration changes required

#### 3. **Custom i18n → Next.js i18n**

- **Current**: React Context-based internationalization
- **Challenge**: Adapting to Next.js i18n or maintaining custom system
- **Impact**: MEDIUM - Affects SEO and SSR compatibility

#### 4. **Express Server Integration**

- **Current**: Embedded Express server with Vite middleware
- **Challenge**: Converting to Next.js API routes
- **Impact**: MEDIUM - API endpoint migration required

### 🟡 **Medium Priority Challenges**

#### 5. **Theme Provider & CSS Variables**

- **Current**: next-themes with custom CSS variables
- **Challenge**: Ensuring SSR compatibility and hydration
- **Impact**: LOW-MEDIUM - Theme flashing prevention

#### 6. **Client-Side State Management**

- **Current**: TanStack Query, React Hook Form
- **Challenge**: Ensuring proper hydration with SSR
- **Impact**: LOW - Already SSR-compatible

### 🟢 **Low Risk Components**

- Radix UI components (SSR compatible)
- Tailwind CSS (Next.js native support)
- TypeScript configuration
- Lucide React icons
- Most custom hooks and utilities

---

## Phase 3: Next.js Migration Strategy

### **Recommended Approach: App Router (Next.js 13+)**

#### **Why App Router over Pages Router?**

1. **Future-proof**: Latest Next.js paradigm
2. **Better Developer Experience**: Co-located layouts and loading states
3. **Enhanced Performance**: Automatic code splitting
4. **SSR/SSG Flexibility**: Per-route rendering strategies
5. **Server Components**: Better performance for static content

### **Project Structure (App Router)**

```
src/
├── app/                    # App Router directory
│   ├── globals.css         # Global styles
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Landing page (/)
│   ├── dashboard/
│   │   ├── layout.tsx      # Dashboard layout
│   │   ├── page.tsx        # Dashboard page
│   │   └── loading.tsx     # Loading UI
│   ├── candidates/
│   │   ├── page.tsx        # Candidates page
│   │   └── [id]/
│   │       └── page.tsx    # Candidate detail
│   ├── analytics/
│   │   └── page.tsx        # Analytics page
│   ├── login/
│   │   └── page.tsx        # Login page
│   └── api/                # API routes
│       ├── ping/
│       │   └── route.ts    # API endpoint
│       └── demo/
│           └── route.ts    # Demo endpoint
├── components/             # Reusable components (unchanged)
├── lib/                    # Utilities and configuration
├── hooks/                  # Custom hooks
├── data/                   # Mock data
└── types/                  # TypeScript types
```

### **Migration Timeline & Phases**

#### **Phase 3.1: Foundation Setup (Week 1-2)**

1. **Next.js Project Initialization**
   - Create Next.js 14+ project with App Router
   - Configure TypeScript and path aliases
   - Set up Tailwind CSS with existing configuration
   - Migrate global styles and CSS variables

2. **Component Migration**
   - Copy entire `components/` directory
   - Migrate `lib/` utilities and types
   - Migrate custom hooks
   - Update imports for Next.js compatibility

#### **Phase 3.2: Core Pages Migration (Week 3-4)**

1. **Landing Page** (`/`)
   - Convert to `app/page.tsx`
   - Implement proper metadata for SEO
   - Ensure SSR compatibility

2. **Authentication Layout**
   - Create `app/login/page.tsx`
   - Implement authentication logic for SSR
   - Set up session management

3. **Main Application Layout**
   - Create `app/(dashboard)/layout.tsx`
   - Migrate Header, Sidebar components
   - Implement nested routing

#### **Phase 3.3: Feature Pages Migration (Week 5-6)**

1. **Dashboard Pages**
   - `app/(dashboard)/dashboard/page.tsx`
   - `app/(dashboard)/candidates/page.tsx`
   - `app/(dashboard)/analytics/page.tsx`
   - Continue with all other pages

2. **API Routes Migration**
   - Convert Express routes to Next.js API routes
   - Maintain existing API contracts
   - Test API endpoint compatibility

#### **Phase 3.4: Advanced Features (Week 7-8)**

1. **Internationalization**
   - Implement Next.js i18n or adapt custom system
   - Ensure SEO-friendly language switching
   - Test Vietnamese language support

2. **Performance Optimization**
   - Implement proper loading states
   - Add Suspense boundaries
   - Optimize bundle size

---

## Phase 4: Implementation Details

### **4.1 Next.js Configuration**

```typescript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    optimizePackageImports: ["lucide-react", "@radix-ui/react-icons"],
  },

  // Internationalization
  i18n: {
    locales: ["en", "vi"],
    defaultLocale: "en",
    localeDetection: false, // Custom detection
  },

  // Images optimization
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
    ],
  },

  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: false,
  },

  // Output for deployment
  output: "standalone",

  // Custom webpack configuration
  webpack: (config) => {
    // Maintain existing build optimizations
    return config;
  },
};

export default nextConfig;
```

### **4.2 Root Layout (app/layout.tsx)**

```typescript
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { I18nProvider } from '@/lib/i18n';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'OTS.VN - Smart Recruitment Platform',
  description: 'Advanced ATS for modern businesses',
  keywords: ['recruitment', 'ATS', 'hiring', 'Vietnam'],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider defaultTheme="system" storageKey="hireflow-ui-theme">
          <I18nProvider>
            <TooltipProvider>
              {children}
              <Toaster />
              <Sonner />
            </TooltipProvider>
          </I18nProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

### **4.3 Dashboard Layout (app/(dashboard)/layout.tsx)**

```typescript
import { Header } from '@/components/layout/Header';
import { Sidebar } from '@/components/layout/Sidebar';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
```

### **4.4 API Routes Migration**

```typescript
// app/api/ping/route.ts
import { NextResponse } from "next/server";

export async function GET() {
  return NextResponse.json({
    message: "Hello from Next.js API v2!",
  });
}

// app/api/demo/route.ts
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  // Migrate existing demo logic
  return NextResponse.json({ data: "demo" });
}
```

### **4.5 Page Components with SSR**

```typescript
// app/(dashboard)/analytics/page.tsx
import { Metadata } from 'next';
import { Analytics } from '@/components/analytics/Analytics';

export const metadata: Metadata = {
  title: 'Analytics - OTS.VN',
  description: 'Recruitment analytics and insights',
};

// Server Component for better performance
export default async function AnalyticsPage() {
  // Can fetch data on the server
  // const data = await fetchAnalyticsData();

  return <Analytics />;
}
```

---

## Phase 5: Performance Optimizations

### **5.1 SSR Strategy**

| Page Type    | Rendering Strategy | Reason                           |
| ------------ | ------------------ | -------------------------------- |
| Landing Page | SSG (Static)       | Marketing content, SEO important |
| Dashboard    | SSR                | Dynamic user data                |
| Analytics    | SSR                | Real-time data                   |
| Login        | SSR                | Authentication state             |
| Candidates   | SSR                | Dynamic filtering                |

### **5.2 Bundle Optimization**

```typescript
// Dynamic imports for heavy components
const AnalyticsChart = dynamic(() => import('@/components/analytics/Chart'), {
  loading: () => <ChartSkeleton />,
  ssr: false, // Client-side only if needed
});

// Code splitting by route
const DashboardPage = dynamic(() => import('./dashboard/page'), {
  loading: () => <DashboardSkeleton />,
});
```

### **5.3 Image Optimization**

```typescript
// next.config.js
module.exports = {
  images: {
    domains: ["example.com"],
    formats: ["image/avif", "image/webp"],
    minimumCacheTTL: 3600,
  },
};
```

---

## Phase 6: Testing Strategy

### **6.1 Unit Testing**

- **Framework**: Vitest (maintain existing)
- **Component Testing**: React Testing Library
- **Coverage**: 80% minimum for critical paths

### **6.2 Integration Testing**

- **API Routes**: Test all migrated endpoints
- **Page Rendering**: SSR/SSG functionality
- **Authentication Flow**: Login/logout scenarios

### **6.3 E2E Testing**

- **Framework**: Playwright or Cypress
- **Critical Flows**: User registration, candidate management, analytics
- **Cross-browser**: Chrome, Firefox, Safari

### **6.4 Performance Testing**

- **Lighthouse**: Score 90+ for all metrics
- **Bundle Analysis**: Use @next/bundle-analyzer
- **Load Testing**: API endpoint performance

---

## Phase 7: Deployment Strategy

### **7.1 Deployment Platform Options**

| Platform | Pros                                   | Cons                       | Recommendation     |
| -------- | -------------------------------------- | -------------------------- | ------------------ |
| Vercel   | Optimized for Next.js, Easy deployment | Cost for high traffic      | ⭐ **Recommended** |
| Netlify  | Good DX, Form handling                 | Less optimized for Next.js | ✅ **Alternative** |
| AWS/GCP  | Full control, Scalable                 | Complex setup              | ⚠️ **Enterprise**  |

### **7.2 Environment Configuration**

```bash
# .env.local
NEXT_PUBLIC_API_URL=https://api.ots.vn
NEXT_PUBLIC_SITE_URL=https://ots.vn
DATABASE_URL=postgresql://...
NEXTAUTH_SECRET=...
NEXTAUTH_URL=https://ots.vn
```

### **7.3 CI/CD Pipeline**

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "18"
      - run: npm ci
      - run: npm run build
      - run: npm run test
      - uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
```

---

## Phase 8: Risk Assessment & Mitigation

### **High Risk (🔴)**

| Risk                      | Impact | Probability | Mitigation                            |
| ------------------------- | ------ | ----------- | ------------------------------------- |
| Translation system breaks | HIGH   | MEDIUM      | Thorough testing, fallback to English |
| SEO performance degrades  | HIGH   | LOW         | Proper metadata implementation        |
| Authentication issues     | HIGH   | MEDIUM      | Staged rollout, backup auth           |

### **Medium Risk (🟡)**

| Risk                       | Impact | Probability | Mitigation                |
| -------------------------- | ------ | ----------- | ------------------------- |
| Theme flashing on load     | MEDIUM | MEDIUM      | Proper SSR theme handling |
| API endpoint compatibility | MEDIUM | LOW         | Maintain API contracts    |
| Performance regression     | MEDIUM | LOW         | Performance monitoring    |

### **Low Risk (🟢)**

| Risk                     | Impact | Probability | Mitigation                        |
| ------------------------ | ------ | ----------- | --------------------------------- |
| Component styling issues | LOW    | LOW         | CSS-in-JS or Tailwind consistency |
| Build configuration      | LOW    | LOW         | Docker containers for consistency |

---

## Phase 9: Success Metrics

### **Performance Targets**

| Metric                   | Current | Target | Tool            |
| ------------------------ | ------- | ------ | --------------- |
| First Contentful Paint   | ~2.5s   | <1.5s  | Lighthouse      |
| Largest Contentful Paint | ~3.0s   | <2.0s  | Web Vitals      |
| Cumulative Layout Shift  | ~0.1    | <0.1   | Web Vitals      |
| Time to Interactive      | ~3.5s   | <2.5s  | Lighthouse      |
| Bundle Size              | ~800KB  | <600KB | Bundle Analyzer |

### **SEO Improvements**

- **Lighthouse SEO Score**: 95+ (from ~85)
- **Core Web Vitals**: All metrics in green
- **Page Speed**: 90+ mobile, 95+ desktop
- **Accessibility**: 100% compliance

### **Developer Experience**

- **Build Time**: <30s (from ~45s)
- **Hot Reload**: <1s
- **Type Safety**: 100% TypeScript coverage
- **Test Coverage**: 85%+

---

## Phase 10: Migration Checklist

### **Pre-Migration (Week 1)**

- [ ] Backup current codebase
- [ ] Set up Next.js project structure
- [ ] Configure development environment
- [ ] Plan downtime window

### **Core Migration (Week 2-4)**

- [ ] Migrate all components and utilities
- [ ] Convert React Router to App Router
- [ ] Implement SSR for critical pages
- [ ] Migrate API endpoints

### **Testing Phase (Week 5-6)**

- [ ] Unit tests for all components
- [ ] Integration tests for pages
- [ ] E2E tests for critical flows
- [ ] Performance testing

### **Pre-Production (Week 7)**

- [ ] Staging environment setup
- [ ] Load testing
- [ ] Security review
- [ ] SEO audit

### **Production Deployment (Week 8)**

- [ ] DNS configuration
- [ ] SSL certificates
- [ ] Monitoring setup
- [ ] Rollback plan ready

### **Post-Migration (Week 9-10)**

- [ ] Performance monitoring
- [ ] User feedback collection
- [ ] Bug fixes and optimizations
- [ ] Documentation updates

---

## Conclusion

This migration plan provides a comprehensive roadmap for converting the React application to Next.js with SSR support while maintaining all existing functionality. The 8-10 week timeline allows for thorough testing and gradual rollout to minimize risks.

**Key Benefits of Migration:**

- **SEO Performance**: Server-side rendering for better search visibility
- **Performance**: Faster initial page loads and optimized bundle splitting
- **Developer Experience**: Better tooling and development workflow
- **Scalability**: Built-in optimization and deployment features
- **Future-Proof**: Modern React patterns and latest Next.js features

**Next Steps:**

1. Review and approve migration plan
2. Set up development environment
3. Begin Phase 3.1 foundation setup
4. Establish regular progress checkpoints

The migration maintains backward compatibility while significantly improving performance, SEO, and developer experience for the HireFlow ATS application.
